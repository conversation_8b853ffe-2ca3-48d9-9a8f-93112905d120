package com.ynhdkc.tenant.client;

import com.ynhdkc.tenant.client.model.TenantPaymentFullListVo;
import com.ynhdkc.tenant.model.RpcSettlementRuleVo;
import com.ynhdkc.tenant.model.RpcWechatPayServiceProviderSecondMerchantVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-11 13:25
 */
@FeignClient(name = "dyt-payment-center", path = "/rpc/v1/payment-center")
public interface PaymentCenterClient {
    @GetMapping(path = "/tenant-payments/all", produces = {"application/json"})
    ResponseEntity<TenantPaymentFullListVo> getAllTenantPayments();

    @GetMapping(value = "/settlement-rule/{id}")
    ResponseEntity<RpcSettlementRuleVo> querySettlementRuleDetail(@PathVariable("id") Long id);

    @GetMapping(value = "/service-provider/wechat-pay/second-merchant/{id}")
    ResponseEntity<RpcWechatPayServiceProviderSecondMerchantVo> querySecondMerchantDetail(@PathVariable("id") Long id);
}
