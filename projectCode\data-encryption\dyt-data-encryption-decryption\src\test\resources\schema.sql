-- 测试环境数据库初始化脚本 (H2数据库)

-- 创建JPA用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    phone VARCHAR(500),  -- 加密字段需要更大的存储空间
    email VARCHAR(500),  -- 加密字段需要更大的存储空间
    id_card VARCHAR(500), -- 加密字段需要更大的存储空间
    real_name VARCHAR(500), -- 加密字段需要更大的存储空间
    age INTEGER,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建MyBatis用户表
CREATE TABLE IF NOT EXISTS mybatis_users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    phone VARCHAR(500),  -- 加密字段需要更大的存储空间
    email VARCHAR(500),  -- 加密字段需要更大的存储空间
    id_card VARCHAR(500), -- 加密字段需要更大的存储空间
    real_name VARCHAR(500), -- 加密字段需要更大的存储空间
    age INTEGER,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建JPA用户表索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- 创建MyBatis用户表索引
CREATE INDEX IF NOT EXISTS idx_mybatis_users_username ON mybatis_users(username);
CREATE INDEX IF NOT EXISTS idx_mybatis_users_status ON mybatis_users(status);
CREATE INDEX IF NOT EXISTS idx_mybatis_users_created_at ON mybatis_users(created_at);
