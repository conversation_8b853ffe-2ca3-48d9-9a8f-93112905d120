package com.ynhdkc.tenant.api.customer;


import backend.security.service.BackendClientUserService;
import com.ynhdkc.tenant.handler.UserDoctorSubscriptionApi;
import com.ynhdkc.tenant.model.InlineResponse200;
import com.ynhdkc.tenant.model.UserDoctorSubscriptionCreateDto;
import com.ynhdkc.tenant.model.UserDoctorSubscriptionVo;
import com.ynhdkc.tenant.service.customer.UserDoctorSubscriptionService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@Api(tags = "CustomUserDoctorSubscription")
@RequiredArgsConstructor
public class CustomUserDoctorSubscriptionController implements UserDoctorSubscriptionApi {

    private final UserDoctorSubscriptionService subscriptionService;
    private final BackendClientUserService backendClientUserService;

    @Override
    public ResponseEntity<UserDoctorSubscriptionVo> addUserDoctorSubscription(UserDoctorSubscriptionCreateDto body) {
        long userId = backendClientUserService.getCurrentUserId();
        body.setUserId(userId);
        return ResponseEntity.ok(subscriptionService.addSubscription(body));
    }


    @Override
    public ResponseEntity<Void> cancelUserDoctorSubscription(Long id) {
        long userId = backendClientUserService.getCurrentUserId();
        subscriptionService.cancelUserDoctorSubscription(id, userId);
        return ResponseEntity.ok().build();
    }

    @Override
    public ResponseEntity<InlineResponse200> checkUserDoctorSubscription(Long doctorId) {
        if (subscriptionService.checkUserDoctorSubscription(backendClientUserService.getCurrentUserId(), doctorId)) {
            return ResponseEntity.ok(new InlineResponse200().isSubscribed(true));
        }
        return ResponseEntity.ok(new InlineResponse200());
    }

    @Override
    public ResponseEntity<List<UserDoctorSubscriptionVo>> getUserDoctorSubscriptions(Integer status) {
        return ResponseEntity.ok(subscriptionService.getSubscriptions(backendClientUserService.getCurrentUserId(), status));
    }


}
