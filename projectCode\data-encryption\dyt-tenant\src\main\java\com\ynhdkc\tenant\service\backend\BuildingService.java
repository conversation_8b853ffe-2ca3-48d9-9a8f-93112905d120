package com.ynhdkc.tenant.service.backend;

import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.entity.Building;
import com.ynhdkc.tenant.model.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-03 16:56
 */
public interface BuildingService {
    BuildingVo create(BuildingCreateReqDto request);

    BuildingVo update(Long buildingId, BuildingUpdateReqDto request);

    BaseOperationResponse delete(Long buildingId);

    BuildingVo getDetail(Long buildingId);

    BuildingPageVo query(BuildingQueryReqDto request);

    static Building toBuilding(BuildingCreateReqDto dto) {
        Building entity = new Building();
        entity.setTenantId(dto.getTenantId());
        entity.setHospitalId(dto.getHospitalId());
        entity.setHospitalAreaId(dto.getHospitalAreaId());
        entity.setHospitalCode(dto.getHospitalCode());
        entity.setName(dto.getName());
        entity.setSort(dto.getSort());
        entity.setStatus(dto.getStatus());
        return entity;
    }

    static BuildingVo toBuildingVo(Building entity) {
        BuildingVo vo = new BuildingVo();
        vo.setTenantId(entity.getTenantId());
        vo.setHospitalId(entity.getHospitalId());
        vo.setHospitalAreaId(entity.getHospitalAreaId());
        vo.setHospitalCode(entity.getHospitalCode());
        vo.setId(entity.getId());
        vo.setName(entity.getName());
        vo.setAddressId(entity.getAddressId());
        vo.setSort(entity.getSort());
        vo.setStatus(entity.getStatus());
        vo.setCreateTime(entity.getCreateTime());
        return vo;
    }
}
