package com.ynhdkc.tenant.dao.impl;

import backend.common.util.MybatisUtil;
import backend.common.util.ObjectsUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ynhdkc.tenant.dao.HospitalAreaQuery;
import com.ynhdkc.tenant.dao.mapper.AppointmentRuleSettingMapper;
import com.ynhdkc.tenant.dao.mapper.HospitalMapper;
import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import lombok.RequiredArgsConstructor;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-27 9:54
 */
@Repository
@RequiredArgsConstructor
public class HospitalAreaQueryImpl implements HospitalAreaQuery {

    private final HospitalMapper hospitalMapper;

    private final AppointmentRuleSettingMapper appointmentRuleSettingMapper;

    @Override
    public Page<Hospital> pageQueryHospitalAreaWithCategory(HospitalAreaQueryOption option) {
        try (final Page<Hospital> page = PageHelper.startPage(option.getCurrentPage(), option.getPageSize())) {
            return page.doSelectPage(() -> hospitalMapper.selectHospitalAreaWithCustomQuery(option));
        }
    }

    @Override
    public Page<Hospital> pageQueryHospitalArea(HospitalAreaQueryOption option) {
        try (final Page<Hospital> page = PageHelper.startPage(option.getCurrentPage(), option.getPageSize())) {
            return page.doSelectPage(() -> queryHospitalArea(option));
        }
    }

    @Override
    public Hospital queryHospitalAreaById(Long hospitalAreaId) {
        List<Hospital> selected = hospitalMapper.selectByExample2(Hospital.class, sql -> sql.andEqualTo(Hospital::getId, hospitalAreaId)
                .andEqualTo(Hospital::getHospitalTypeTag, 1));
        if (selected.isEmpty()) {
            return null;
        }
        return selected.get(0);
    }

    @Override
    public List<Hospital> queryHospitalAreaByParentId(Long parentId) {
        return hospitalMapper.selectByExample2(Hospital.class, sql -> sql.andEqualTo(Hospital::getParentId, parentId)
                .andEqualTo(Hospital::getHospitalTypeTag, 1));
    }

    @Override
    public @Nullable List<Hospital> listHospitalsThatDependOnHis() {
        List<Hospital> hospitalAreaEntities = hospitalMapper.selectByExample2(Hospital.class, sql -> sql.andEqualTo(Hospital::getHospitalTypeTag, 1));
        if (CollectionUtils.isEmpty(hospitalAreaEntities)) {
            return null;
        }

        List<Long> hospitalAreaIds = hospitalAreaEntities.stream().map(Hospital::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hospitalAreaIds)) {
            return null;
        }

        List<AppointmentRuleSetting> appointmentRuleSettings = appointmentRuleSettingMapper.selectByExample2(AppointmentRuleSetting.class, sql -> sql.andIn(AppointmentRuleSetting::getHospitalAreaId, hospitalAreaIds).andEqualTo(AppointmentRuleSetting::getSystemDepends, 0));
        if (CollectionUtils.isEmpty(appointmentRuleSettings)) {
            return null;
        }

        List<Long> hospitalAreaIdsDependOnHis = appointmentRuleSettings.stream().map(AppointmentRuleSetting::getHospitalAreaId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hospitalAreaIdsDependOnHis)) {
            return null;
        }

        return hospitalAreaEntities.stream().filter(hospitalAreaEntity -> hospitalAreaIdsDependOnHis.contains(hospitalAreaEntity.getId())).collect(Collectors.toList());
    }

    @Override
    public @Nullable Hospital queryHospitalAreaBy(@NonNull String hospitalCode) {
        List<Hospital> selected = hospitalMapper.selectByExample2(Hospital.class, sql -> sql.andEqualTo(Hospital::getHospitalCode, hospitalCode)
                .andEqualTo(Hospital::getHospitalTypeTag, 1));
        if (CollectionUtils.isEmpty(selected)) {
            return null;
        }
        return selected.get(0);
    }

    @Override
    public List<Hospital> queryHospitalList(List<String> hospitalCodeList) {
        if (ObjectsUtils.isEmpty(hospitalCodeList)) {
            return Collections.emptyList();
        }
        Example example = new Example(Hospital.class);
        example.createCriteria().andIn("hospitalCode", hospitalCodeList).andEqualTo("hospitalTypeTag", 1);
        return hospitalMapper.selectByExample(example);
    }

    @Override
    public Hospital queryHospitalAreaBy(Long hospitalAreaId) {
        if (hospitalAreaId == null) {
            return null;
        }
        return hospitalMapper.selectByPrimaryKey(hospitalAreaId);
    }

    @Override
    public String getHospitalCodeBy(Long hospitalAreaId) {
        Hospital hospital = hospitalMapper.selectByPrimaryKey(hospitalAreaId);
        if (null == hospital) {
            return null;
        }
        return hospital.getHospitalCode();
    }

    @Override
    public List<Hospital> queryBy(List<String> hospitalAreaIds) {
        if (CollectionUtils.isEmpty(hospitalAreaIds)) {
            return Collections.emptyList();
        }
        return hospitalMapper.selectByExample2(Hospital.class,
                sql -> sql.andIn(Hospital::getId, hospitalAreaIds)
                        .andEqualTo(Hospital::getHospitalTypeTag, 1)
        );
    }

    @Override
    public List<Hospital> queryAll() {
        return hospitalMapper.selectByExample2(Hospital.class, sql -> sql.andEqualTo(Hospital::getStatus, 0));
    }

    @Override
    public List<Hospital> queryParentHospital(List<Long> hospitalAreaIds) {
        return hospitalMapper.selectByExample2(Hospital.class, sql -> sql.andIn(Hospital::getId, hospitalAreaIds));
    }

    @Override
    public List<Hospital> queryHospitalArea(HospitalAreaQueryOption option) {
        return hospitalMapper.selectByExample(Hospital.class, sql -> {
            sql.defGroup(condition -> {
                condition.andEqualTo(Hospital::getHospitalTypeTag, option.getHospitalTypeTag());
                if (null != option.getHospitalAreaCode()) {
                    condition.andLike(Hospital::getHospitalCode, MybatisUtil.likeBoth(option.getHospitalAreaCode()));
                }
                if (null != option.getTenantId()) {
                    condition.andEqualTo(Hospital::getTenantId, option.getTenantId());
                }
                if (null != option.getHospitalId()) {
                    condition.andEqualTo(Hospital::getParentId, option.getHospitalId());
                }
                if (null != option.getHospitalAreaId()) {
                    condition.andEqualTo(Hospital::getId, option.getHospitalAreaId());
                }
                if (null != option.getName()) {
                    condition.andLike(Hospital::getName, MybatisUtil.likeBoth(option.getName()));
                }
                if (null != option.getStatus()) {
                    condition.andEqualTo(Hospital::getStatus, option.getStatus());
                }
                if (null != option.getStartCreateTime() && null != option.getEndCreateTime()) {
                    condition.andBetween(Hospital::getCreateTime, option.getStartCreateTime(), option.getEndCreateTime());
                }
                if (null != option.getDepartmentLayer()) {
                    condition.andEqualTo(Hospital::getDepartmentLayer, option.getDepartmentLayer());
                }
                if (null != option.getTagDictLabel()) {
                    condition.andLike(Hospital::getTagDictLabel, MybatisUtil.likeBoth(option.getTagDictLabel()));
                }

                if (!CollectionUtils.isEmpty(option.getCategory())) {
                    List<Integer> sortedList = option.getCategory().stream().sorted().collect(Collectors.toList());
                    StringBuilder builder = new StringBuilder();
                    sortedList.forEach(category -> builder.append(category).append("%"));
                    String queryPattern = builder.toString();
                    condition.andLike(Hospital::getCategory, MybatisUtil.likeRight(queryPattern));
                }

                if (null != option.getIncludeIds() && !option.getIncludeIds().isEmpty()) {
                    condition.andIn(Hospital::getId, option.getIncludeIds());
                }
                if (null != option.getExcludeIds() && !option.getExcludeIds().isEmpty()) {
                    condition.andNotIn(Hospital::getId, option.getExcludeIds());
                }
                if (null != option.getIncludeTenantIds() && !option.getIncludeTenantIds().isEmpty()) {
                    condition.andIn(Hospital::getTenantId, option.getIncludeTenantIds());
                }
                if (null != option.getExcludeTenantIds() && !option.getExcludeTenantIds().isEmpty()) {
                    condition.andNotIn(Hospital::getTenantId, option.getExcludeTenantIds());
                }
                if (null != option.getIncludeHospitalIds() && !option.getIncludeHospitalIds().isEmpty()) {
                    condition.andIn(Hospital::getParentId, option.getIncludeHospitalIds());
                }
                if (null != option.getExcludeHospitalIds() && !option.getExcludeHospitalIds().isEmpty()) {
                    condition.andNotIn(Hospital::getParentId, option.getExcludeHospitalIds());
                }

                if (null != option.getIncludeActivePaymentIds() && !option.getIncludeActivePaymentIds().isEmpty()) {
                    condition.andIn(Hospital::getId, option.getIncludeActivePaymentIds());
                }
                if (!CollectionUtils.isEmpty(option.getExcludeStatus())) {
                    condition.andNotIn(Hospital::getStatus, option.getExcludeStatus());
                }
            });
            sql.builder(builder ->
                    builder.orderByDesc(Hospital::getDisplaySort)
                            .orderByAsc(Hospital::getId));
        });
    }

}
