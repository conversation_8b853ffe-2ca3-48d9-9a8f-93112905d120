package com.ynhdkc.tenant.api.backend;

import backend.common.response.BaseOperationResponse;
import backend.security.oplog.DytSecurityRequired;
import com.ynhdkc.tenant.handler.HospitalListGroupApi;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.IHospitalListGroupService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
@Api(tags = "HospitalListGroup")
public class HospitalListGroupController implements HospitalListGroupApi {

    private final IHospitalListGroupService hospitalListGroupService;

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:list:group:bind:hospital")
    public ResponseEntity<BaseOperationResponse> bindHospitals(Long id, List<GroupBindHospitalReqDto> dto) {
        return ResponseEntity.ok(hospitalListGroupService.bindHospitals(id, dto));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:list:group:create")
    public ResponseEntity<HospitalListGroupVo> createHospitalListGroup(CreateHospitalListGroupReqDto hospitalListGroup) {
        return ResponseEntity.ok(hospitalListGroupService.createHospitalListGroup(hospitalListGroup));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:list:group:delete")
    public ResponseEntity<BaseOperationResponse> deleteHospitalListGroup(Long id) {
        return ResponseEntity.ok(hospitalListGroupService.deleteHospitalListGroup(id));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:list:group:get")
    public ResponseEntity<HospitalListGroupDetailVo> getHospitalListGroupDetail(Long id) {
        return ResponseEntity.ok(hospitalListGroupService.getHospitalListGroupDetail(id));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:list:group:query")
    public ResponseEntity<HospitalListGroupPageVo> getHospitalListGroupPage(Integer currentPage,
                                                                            Integer pageSize,
                                                                            Integer hospitalCount) {
        return ResponseEntity.ok(hospitalListGroupService.getHospitalListGroupPage(currentPage, pageSize, hospitalCount));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:list:group:update")
    public ResponseEntity<HospitalListGroupVo> updateHospitalListGroup(Long id, UpdateHospitalListGroupReqDto hospitalListGroupDto) {
        return ResponseEntity.ok(hospitalListGroupService.updateHospitalListGroup(id, hospitalListGroupDto));
    }
}
