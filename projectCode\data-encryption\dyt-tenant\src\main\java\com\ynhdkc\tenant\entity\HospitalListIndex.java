package com.ynhdkc.tenant.entity;

import backend.common.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Table;

/**
 * <p>
 * 医院列表序列
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(name = "t_hospital_list_index")
@ApiModel(value = "HospitalListIndex对象", description = "医院列表序列")
public class HospitalListIndex extends BaseEntity {

    @ApiModelProperty("序列排序")
    private Integer indexOrder;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("规则id")
    private Long ruleId;

    @ApiModelProperty("该位置取的医院数量")
    private Integer hospitalCount;
}
