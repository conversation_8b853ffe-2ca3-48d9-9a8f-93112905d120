package com.ynhdkc.tenant.util;

import backend.common.util.ObjectsUtils;
import com.ynhdkc.tenant.entity.Department;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public class CommonUtil {
    private static final double EARTH_RADIUS = 6371229;

    public static Double getDistance(Double longitude1, Double latitude1, BigDecimal longitude2, BigDecimal latitude2) {
        if (latitude2 == null || longitude2 == null || latitude1 == null || longitude1 == null) {
            return null;
        }
        double longitudeDouble = longitude2.doubleValue();
        double latitudeDouble = latitude2.doubleValue();
        if (longitude1 == longitudeDouble && latitude1 == latitudeDouble) {
            return 0D;
        }
        double x = (longitude1 - longitudeDouble) * Math.PI * EARTH_RADIUS * Math.cos(((latitude1 + latitudeDouble) / 2) * Math.PI / 180) / 180;
        double y = (latitude1 - latitudeDouble) * Math.PI * EARTH_RADIUS / 180;
        // 计算距离,单位km，保留1位小数
        return Math.round(Math.hypot(x, y) / 100) / 10.0;
    }

    public static Double getDistance(Double longitude1, Double latitude1, Double longitude2, Double latitude2) {
        if (latitude2 == null || longitude2 == null || latitude1 == null || longitude1 == null) {
            return null;
        }

        if (longitude1 == latitude2 && latitude1 == latitude2) {
            return 0D;
        }
        double x = (longitude1 - longitude2) * Math.PI * EARTH_RADIUS * Math.cos(((latitude1 + latitude2) / 2) * Math.PI / 180) / 180;
        double y = (latitude1 - latitude2) * Math.PI * EARTH_RADIUS / 180;
        // 计算距离,单位km，保留1位小数
        return Math.round(Math.hypot(x, y) / 100) / 10.0;
    }

    public static Department filterDepartment(List<Department> departmentList) {
        if (ObjectsUtils.isEmpty(departmentList)) {
            return null;
        }
        if (departmentList.size() == 1) {
            return departmentList.get(0);
        }
        Department dpt = filter4Enable(departmentList);
        if (dpt == null) {
            return filter4All(departmentList);
        }
        return dpt;
    }

    private static Department filter4Enable(List<Department> rawDepartmentList) {
        List<Department> departmentList = rawDepartmentList.stream().filter(Department::getEnabled).collect(Collectors.toList());
        if (ObjectsUtils.isEmpty(departmentList)) {
            return null;
        }
        if (departmentList.size() == 1) {
            return departmentList.get(0);
        }
        return departmentList.stream().max(Comparator.comparingLong(Department::getId)).orElse(null);
    }

    private static Department filter4All(List<Department> departmentList) {
        if (ObjectsUtils.isEmpty(departmentList)) {
            return null;
        }
        if (departmentList.size() == 1) {
            return departmentList.get(0);
        }
        return departmentList.stream().max(Comparator.comparingLong(Department::getId)).orElse(null);
    }
}
