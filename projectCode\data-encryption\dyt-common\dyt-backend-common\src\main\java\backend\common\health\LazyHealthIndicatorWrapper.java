package backend.common.health;

import com.google.common.collect.Lists;
import org.apache.commons.lang.mutable.MutableBoolean;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.boot.actuate.health.Status;
import org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator;
import org.springframework.boot.actuate.redis.RedisHealthIndicator;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.connection.RedisConnectionFactory;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

public interface LazyHealthIndicatorWrapper {

    HealthResult doHealthCheck(HealthIndicatorType type);

    static LazyHealthIndicatorWrapper createRedisLazyHealthIndicatorWrapper(ApplicationContext context) {
        return new RedisLazyHealthIndicatorWrapper(context);
    }

    static LazyHealthIndicatorWrapper createDataSourceLazyHealthIndicatorWrapper(ApplicationContext context) {
        return new DataSourceLazyHealthIndicatorWrapper(context);
    }

    abstract class AbstractLazyHealthIndicatorWrapper<T extends HealthIndicator> implements LazyHealthIndicatorWrapper {
        private final ApplicationContext context;
        protected final List<T> healthIndicators = new ArrayList<>();
        private final ReentrantLock lock = new ReentrantLock();
        private Boolean retrieveSources;
        private final HealthIndicatorType type;

        public AbstractLazyHealthIndicatorWrapper(ApplicationContext context, HealthIndicatorType type) {
            this.context = context;
            this.retrieveSources = false;
            this.type = type;
        }

        protected void setRetrieveFromContext() {
            if (retrieveSources) {
                return;
            }

            try {
                lock.lock();
                if (retrieveSources) {
                    return;
                }
                healthIndicators.addAll(retrieveIndicators(context));
                retrieveSources = true;
            } finally {
                lock.unlock();
            }
        }

        @Override
        public HealthResult doHealthCheck(HealthIndicatorType type) {
            if (type != this.type) {
                return HealthResult.up("ok");
            }

            setRetrieveFromContext();
            if (healthIndicators.isEmpty()) {
                return HealthResult.up("ok , but not resources");
            }
            return doRealDatasourceHealth();
        }

        protected abstract HealthResult doRealDatasourceHealth();

        protected abstract List<T> retrieveIndicators(ApplicationContext context);

    }

    class DataSourceLazyHealthIndicatorWrapper
            extends AbstractLazyHealthIndicatorWrapper<DataSourceHealthIndicator>
            implements LazyHealthIndicatorWrapper {

        public DataSourceLazyHealthIndicatorWrapper(ApplicationContext context) {
            super(context, HealthIndicatorType.DataSource);
        }

        protected HealthResult doRealDatasourceHealth() {
            MutableBoolean health = new MutableBoolean(false);
            healthIndicators.stream()
                    .filter(d -> d.health().getStatus().equals(Status.UP))
                    .forEach(d -> health.setValue(true));

            if (!health.toBoolean()) {
                return HealthResult.down("error: all datasource aren't health");
            }
            return HealthResult.up("datasource is healthy");
        }


        @Override
        protected List<DataSourceHealthIndicator> retrieveIndicators(ApplicationContext context) {
            return getDatasourceFromContext(context)
                    .stream()
                    .map(d -> {
                        DataSourceHealthIndicator dataSourceHealthIndicator = new DataSourceHealthIndicator(d);
                        dataSourceHealthIndicator.setQuery("SELECT 1");
                        return dataSourceHealthIndicator;
                    })
                    .collect(Collectors.toCollection(ArrayList::new));
        }

        private List<DataSource> getDatasourceFromContext(ApplicationContext context) {
            Map<String, DataSource> beansOfType = context.getBeansOfType(DataSource.class);
            if (!beansOfType.isEmpty()) {
                return Lists.asList(beansOfType.values().iterator().next(),
                        beansOfType.values().toArray(new DataSource[0]));
            }
            return new ArrayList<>();
        }
    }

    class RedisLazyHealthIndicatorWrapper
            extends AbstractLazyHealthIndicatorWrapper<RedisHealthIndicator>
            implements LazyHealthIndicatorWrapper {

        public RedisLazyHealthIndicatorWrapper(ApplicationContext context) {
            super(context, HealthIndicatorType.Redis);
        }

        @Override
        protected HealthResult doRealDatasourceHealth() {
            MutableBoolean health = new MutableBoolean(false);
            healthIndicators.stream()
                    .filter(d -> d.health().getStatus().equals(Status.UP))
                    .forEach(d -> health.setValue(true));

            if (!health.toBoolean()) {
                return HealthResult.down("error: all redis aren't health");
            }
            return HealthResult.up("redis is healthy");
        }

        @Override
        protected List<RedisHealthIndicator> retrieveIndicators(ApplicationContext context) {
            return getDatasourceFromContext(context)
                    .stream()
                    .map(RedisHealthIndicator::new)
                    .collect(Collectors.toCollection(ArrayList::new));
        }

        private List<RedisConnectionFactory> getDatasourceFromContext(ApplicationContext context) {
            Map<String, RedisConnectionFactory> beansOfType = context.getBeansOfType(RedisConnectionFactory.class);
            if (!beansOfType.isEmpty()) {
                return Lists.asList(beansOfType.values().iterator().next(),
                        beansOfType.values().toArray(new RedisConnectionFactory[0]));
            }
            return new ArrayList<>();
        }
    }
}
