package com.ynhdkc.tenant.dao.impl;

import backend.common.domain.tenant.constant.DictFileStatus;
import com.ynhdkc.tenant.dao.SearchManagementRepository;
import com.ynhdkc.tenant.dao.mapper.DictFileMapper;
import com.ynhdkc.tenant.entity.DictFile;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/9/12 16:19
 */
@Repository
@RequiredArgsConstructor
public class SearchManagementRepositoryImpl implements SearchManagementRepository {
    private final DictFileMapper dictFileMapper;

    @Override
    public void createDictFile(DictFile entity) {
        dictFileMapper.insertSelective(entity);
    }

    @Override
    public void updateDictFile(DictFile entity) {
        dictFileMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void deleteDictFile(Long id) {
        DictFile dictFile = dictFileMapper.selectByPrimaryKey(id);
        dictFile.setStatus(DictFileStatus.DELETE.getCode());
        dictFileMapper.updateByPrimaryKeySelective(dictFile);
    }
}
