#!/bin/bash

set -e

pushd $(dirname $0) > /dev/null
SCRIPTPATH=$(pwd -P)
popd > /dev/null

do_package() {
  cd /${PROJECT_NAME}/
  set -x
  mvn package -Dmaven.test.skip=true
  REPOID=`echo ${REPOSITORY1}|awk 'BEGIN{FS="|"}{print $1}'`
  REPOURL=`echo ${REPOSITORY1}|awk 'BEGIN{FS="|"}{print $2}'`
  REPO_SNAPSHOTS_URL=`echo ${REPOURL}|sed -e 's/maven-public/maven-snapshots/'`
  REPO_RELEASES_URL=`echo ${REPOURL}|sed -e 's/maven-public/maven-releases/'`

  mvn deploy -Dmaven.test.skip=true \
	-DaltSnapshotDeploymentRepository=${REPOID}::default::${REPO_SNAPSHOTS_URL} \
	-DaltReleaseDeploymentRepository=${REPOID}::default::${REPO_RELEASES_URL}
#
}

if [[ $? -ne 0 ]]; then
    exit -1
fi
do_package
