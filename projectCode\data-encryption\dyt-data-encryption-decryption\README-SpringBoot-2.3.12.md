# Spring Boot 2.3.12.RELEASE 版本分支

这是数据加解密POC项目的Spring Boot 2.3.12.RELEASE版本分支，专门为需要使用较低版本Spring Boot的环境提供支持。

## 🎯 版本信息

- **Spring Boot**: 2.3.12.RELEASE
- **Spring Framework**: 5.2.15.RELEASE
- **Java**: 8+
- **Hibernate**: 5.4.32.Final
- **MySQL驱动**: 8.0.25
- **MyBatis**: 2.2.0

## 🔄 与主分支的差异

### 主要版本调整
1. **Spring Boot版本**: 从2.7.14降级到2.3.12.RELEASE
2. **MySQL驱动版本**: 从8.0.33调整到8.0.25
3. **MyBatis版本**: 从2.3.1调整到2.2.0

### 配置适配
1. **CORS配置**: 使用`allowedOrigins("*")`替代`allowedOriginPatterns("*")`
2. **数据源配置**: 保持`initialization-mode`配置格式
3. **测试配置**: 修复重复的datasource键问题

## 🚀 快速开始

### 1. 环境要求
```bash
Java 8+
Maven 3.6+
MySQL 8.0+ (可选，默认使用H2)
```

### 2. 编译项目
```bash
mvn clean compile
```

### 3. 运行测试
```bash
mvn test
```

### 4. 启动应用
```bash
mvn spring-boot:run
```

### 5. 访问应用
- 应用主页: http://localhost:8080
- H2控制台: http://localhost:8080/h2-console
- API接口: http://localhost:8080/api/users

## 📋 功能特性

所有主分支的功能特性在此版本中都得到保留：

- ✅ 统一注解驱动加密机制
- ✅ 三种数据迁移策略
- ✅ 透明加密/解密
- ✅ 配置驱动的策略管理
- ✅ 零停机迁移支持
- ✅ Web管理界面
- ✅ JPA和MyBatis双重支持

## 🔧 兼容性说明

### 支持的特性
- 所有核心加密功能正常工作
- JPA AttributeConverter透明加密
- MyBatis TypeHandler加密支持
- 影子字段迁移策略
- Web管理界面

### 已知限制
- 部分Spring Boot 2.7+的新特性不可用
- 某些配置属性名称可能有差异
- 依赖版本相对较旧

## 🛠️ 开发说明

### 分支维护
- 此分支专门维护Spring Boot 2.3.12版本
- 新功能开发优先在主分支进行
- 重要修复会同步到此分支

### 测试状态
- ✅ 编译通过
- ✅ 基础功能测试通过
- ⚠️ 部分业务逻辑测试需要调整（非框架兼容性问题）

## 📞 技术支持

如果在使用Spring Boot 2.3.12版本时遇到问题，请：

1. 首先检查Java版本是否为8+
2. 确认Maven版本是否为3.6+
3. 查看控制台错误日志
4. 参考主分支的文档说明

## 🔄 升级建议

建议在条件允许的情况下升级到主分支的Spring Boot 2.7+版本，以获得：
- 更好的性能
- 更多的功能特性
- 更好的安全性
- 长期技术支持
