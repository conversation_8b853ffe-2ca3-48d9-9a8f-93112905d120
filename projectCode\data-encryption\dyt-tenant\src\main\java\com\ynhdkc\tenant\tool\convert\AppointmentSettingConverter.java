package com.ynhdkc.tenant.tool.convert;

import backend.common.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.ynhdkc.tenant.entity.constant.DepartmentColumn;
import com.ynhdkc.tenant.entity.constant.DoctorColumn;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import com.ynhdkc.tenant.model.AppointmentRuleSettingDto;
import com.ynhdkc.tenant.model.AppointmentRuleSettingVo;
import com.ynhdkc.tenant.model.CustomerAppointmentRuleSettingVo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/2 10:26:49
 */
public class AppointmentSettingConverter {
    private AppointmentSettingConverter() {
    }

    public static AppointmentRuleSetting toAppointmentRuleSetting(AppointmentRuleSettingDto dto) {
        AppointmentRuleSetting entity = new AppointmentRuleSetting();
        entity.setDoctorScheduleCacheExpTime(dto.getDoctorScheduleCacheExpTime());
        entity.setDepartmentLevel(dto.getDepartmentLevel());
        entity.setDepartmentOrderType(dto.getDepartmentOrderType());
        entity.setSystemDepends(dto.getSystemDepends());
        entity.setCurrentSystemType(dto.getCurrentSystemType());
        entity.setAppointToday(dto.isAppointToday());
        entity.setDisplayNo(dto.isDisplayNo());
        entity.setStopAppointTime(dto.getStopAppointTime());
        entity.setForbiddenDay(dto.getForbiddenDay());
        entity.setAdvanceDay(dto.getAdvanceDay());
        entity.setSourceActivateTime(dto.getSourceActivateTime());
        entity.setDisplayTime(dto.getDisplayTime());
        entity.setPaymentCloseDuration(dto.getPaymentCloseDuration());
        entity.setRefundToday(dto.isRefundToday());
        entity.setStopRefundTime(dto.getStopRefundTime());
        entity.setConfirmMedicalInsuranceCard(dto.isConfirmMedicalInsuranceCard());
        entity.setOrderNeedVerify(dto.isOrderNeedVerify());
        entity.setAppointmentNotifyContact(dto.getAppointmentNotifyContact());
        entity.setNoticeTemplateId(dto.getNoticeTemplateId());
        entity.setAppointmentNotice(dto.getAppointmentNotice());
        entity.setAppointmentResultNotice(dto.getAppointmentResultNotice());
        entity.setAppointmentRule(dto.getAppointmentRule());
        entity.setRegistrationReminder(dto.getRegistrationReminder());
        entity.setAppointmentGuide(dto.getAppointmentGuide());
        entity.setAppointmentReminder(dto.getAppointmentReminder());
        entity.setSelectedPayments(JsonUtil.serializeObject(dto.getSelectedPayments()));
        entity.setDisplayDoctorUnderDepartment(dto.getDisplayDoctorUnderDepartment());
        entity.setNeedPayment(dto.isNeedPayment());
        entity.setSupportLockingAppointmentNo(dto.isSupportLockingAppointmentNo());
        entity.setSupportCancelAppointment(dto.isSupportCancelAppointment());
        entity.setNeedPartitionTime(dto.isNeedPartitionTime());
        entity.setDisplayDepartmentAddress(dto.isDisplayDepartmentAddress());
        entity.setDisplayVisitTime(dto.isDisplayVisitTime());
        entity.setNeedSignIn(dto.isNeedSignIn());
        entity.setAllDepartmentBackgroundColor(dto.getAllDepartmentBackgroundColor());
        entity.setUpdateDepartmentExcludeColumns(DepartmentColumn.getMergedColumnsByCodes(dto.getUpdateDepartmentExcludeColumns()));
        entity.setUpdateDoctorExcludeColumns(DoctorColumn.getMergedColumnsByCodes(dto.getUpdateDoctorExcludeColumns()));
        entity.setUpdateDepartmentDependHis(dto.isUpdateDepartmentDependHis());
        entity.setHospitalDependHisEnabledDepartment(dto.isHospitalDependHisEnabledDepartment());
        entity.setCreateDoctorFromHis(dto.isCreateDoctorFromHis());
        entity.setStopGenerateScheduleBeginDate(dto.getStopGenerateScheduleBeginDate());
        entity.setStopGenerateScheduleEndDate(dto.getStopGenerateScheduleEndDate());
        entity.setTimePeriodDisplayType(dto.getTimePeriodDisplayType());
        entity.setDoctorTitleShowType(dto.getDoctorTitleShowType());
        return entity;
    }

    public static AppointmentRuleSettingVo toAppointmentRuleSettingVo(AppointmentRuleSetting entity) {
        AppointmentRuleSettingVo vo = new AppointmentRuleSettingVo();
        vo.setId(entity.getId());
        vo.setFunctionId(entity.getFunctionId());

        vo.setDoctorScheduleCacheExpTime(entity.getDoctorScheduleCacheExpTime());
        vo.setDepartmentLevel(entity.getDepartmentLevel());
        vo.setDepartmentOrderType(entity.getDepartmentOrderType());
        vo.setSystemDepends(entity.getSystemDepends());
        vo.setCurrentSystemType(entity.getCurrentSystemType());
        vo.setAppointToday(entity.getAppointToday());
        vo.setDisplayNo(entity.getDisplayNo());
        vo.setStopAppointTime(entity.getStopAppointTime());
        vo.setForbiddenDay(entity.getForbiddenDay());
        vo.setAdvanceDay(entity.getAdvanceDay());
        vo.setSourceActivateTime(entity.getSourceActivateTime());
        vo.setDisplayTime(entity.getDisplayTime());
        vo.setPaymentCloseDuration(entity.getPaymentCloseDuration());
        vo.setRefundToday(entity.getRefundToday());
        vo.setStopRefundTime(entity.getStopRefundTime());
        vo.setConfirmMedicalInsuranceCard(entity.getConfirmMedicalInsuranceCard());
        vo.setOrderNeedVerify(entity.getOrderNeedVerify());
        vo.setAppointmentResultNotice(entity.getAppointmentResultNotice());
        vo.setAppointmentNotifyContact(entity.getAppointmentNotifyContact());
        vo.setNoticeTemplateId(entity.getNoticeTemplateId());
        vo.setAppointmentNotice(entity.getAppointmentNotice());
        vo.setAppointmentRule(entity.getAppointmentRule());
        vo.setAppointmentGuide(entity.getAppointmentGuide());
        vo.setAppointmentReminder(entity.getAppointmentReminder());
        vo.setRegistrationReminder(entity.getRegistrationReminder());
        vo.setDisplayDoctorUnderDepartment(entity.getDisplayDoctorUnderDepartment());
        vo.setNeedPayment(entity.getNeedPayment());
        vo.setDisplayDepartmentAddress(entity.getDisplayDepartmentAddress());
        vo.setDisplayVisitTime(entity.getDisplayVisitTime());
        if (null != entity.getSelectedPayments()) {
            vo.setSelectedPayments(JsonUtil.deserializeObject(entity.getSelectedPayments(), new TypeReference<List<String>>() {
            }));
        }
        vo.setSupportLockingAppointmentNo(entity.getSupportLockingAppointmentNo());
        vo.setSupportCancelAppointment(entity.getSupportCancelAppointment());
        vo.setNeedPartitionTime(entity.getNeedPartitionTime());
        vo.setNeedSignIn(entity.getNeedSignIn());
        vo.setAllDepartmentBackgroundColor(entity.getAllDepartmentBackgroundColor());
        vo.setUpdateDepartmentExcludeColumns(DepartmentColumn.getCodesByColumns(entity.getUpdateDepartmentExcludeColumns()));
        vo.setUpdateDoctorExcludeColumns(DoctorColumn.getCodesByColumns(entity.getUpdateDoctorExcludeColumns()));
        vo.setUpdateDepartmentDependHis(entity.getUpdateDepartmentDependHis());
        vo.setHospitalDependHisEnabledDepartment(entity.getHospitalDependHisEnabledDepartment());
        vo.setCreateDoctorFromHis(entity.getCreateDoctorFromHis());
        vo.setStopGenerateScheduleBeginDate(entity.getStopGenerateScheduleBeginDate());
        vo.setStopGenerateScheduleEndDate(entity.getStopGenerateScheduleEndDate());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdateTime(entity.getUpdateTime());
        vo.setTimePeriodDisplayType(entity.getTimePeriodDisplayType());
        vo.setDoctorTitleShowType(entity.getDoctorTitleShowType());
        return vo;
    }

    public static CustomerAppointmentRuleSettingVo toCustomerAppointmentRuleSettingVo(AppointmentRuleSetting entity) {
        CustomerAppointmentRuleSettingVo vo = new CustomerAppointmentRuleSettingVo();
        vo.setDepartmentLevel(entity.getDepartmentLevel());
        vo.setSystemDepends(entity.getSystemDepends());
        vo.setAppointToday(entity.getAppointToday());
        vo.setDisplayNo(entity.getDisplayNo());
        vo.setStopAppointTime(entity.getStopAppointTime());
        vo.setAdvanceDay(entity.getAdvanceDay());
        vo.setDisplayTime(entity.getDisplayTime());
        vo.setPaymentCloseDuration(entity.getPaymentCloseDuration());
        vo.setRefundToday(entity.getRefundToday());
        vo.setStopRefundTime(entity.getStopRefundTime());
        vo.setConfirmMedicalInsuranceCard(entity.getConfirmMedicalInsuranceCard());
        vo.setOrderNeedVerify(entity.getOrderNeedVerify());
        vo.setAppointmentNotice(entity.getAppointmentNotice());
        vo.setAppointmentResultNotice(entity.getAppointmentResultNotice());
        vo.setAppointmentNotifyContact(entity.getAppointmentNotifyContact());
        vo.setNoticeTemplateId(entity.getNoticeTemplateId());
        vo.setAppointmentRule(entity.getAppointmentRule());
        vo.setAppointmentGuide(entity.getAppointmentGuide());
        vo.setDisplayDoctorUnderDepartment(entity.getDisplayDoctorUnderDepartment());
        vo.setDisplayDepartmentAddress(entity.getDisplayDepartmentAddress());
        vo.setDisplayTime(entity.getDisplayTime());
        if (null != entity.getSelectedPayments()) {
            vo.setSelectedPayments(JsonUtil.deserializeObject(entity.getSelectedPayments(), new TypeReference<List<String>>() {
            }));
        }
        vo.setSupportLockingAppointmentNo(entity.getSupportLockingAppointmentNo());
        vo.setNeedPartitionTime(entity.getNeedPartitionTime());
        vo.setNeedPayment(entity.getNeedPayment());
        return vo;
    }
}
