package com.ynhdkc.tenant.service.backend.impl;

import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ynhdkc.oldsystem.integration.api.request.GetExtensionAdvertisementReqDto;
import com.ynhdkc.oldsystem.integration.api.response.GetExtensionAdvertisementRespDto;
import com.ynhdkc.oldsystem.integration.client.AdvertisementClient;
import com.ynhdkc.tenant.dao.mapper.HospitalListGroupMapper;
import com.ynhdkc.tenant.dao.mapper.HospitalListIndexMapper;
import com.ynhdkc.tenant.dao.mapper.HospitalListRuleMapper;
import com.ynhdkc.tenant.entity.HospitalListGroup;
import com.ynhdkc.tenant.entity.HospitalListIndex;
import com.ynhdkc.tenant.entity.HospitalListRule;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.IHospitalListRuleService;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 医院列表规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@Service
@RequiredArgsConstructor
public class HospitalListRuleServiceImpl implements IHospitalListRuleService {

    private final HospitalListRuleMapper mapper;
    private final PageVoConvert pageVoConvert;
    private final HospitalListGroupMapper groupMapper;
    private final HospitalListIndexMapper indexMapper;
    private final AdvertisementClient advertisementClient;

    @Override
    public HospitalListRuleVo createHospitalListRule(CreateHospitalListRuleReqDto dto) {
        checkRuleTypeData(dto);
        HospitalListRule rule = IHospitalListRuleService.toEntity(dto);
        int insertEffectCount = mapper.insertSelective(rule);
        if (insertEffectCount == 0) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "创建医院列表规则失败");
        }
        return IHospitalListRuleService.toVo(rule);
    }

    @Override
    public BaseOperationResponse deleteHospitalListRule(Long id) {
        List<HospitalListIndex> hospitalListIndices = indexMapper.selectByExample2(HospitalListIndex.class, sql -> sql.andEqualTo(HospitalListIndex::getRuleId, id));
        if (!CollectionUtils.isEmpty(hospitalListIndices)) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "该规则已被使用，无法删除");
        }
        int deleteEffectCount = mapper.deleteByPrimaryKey(id);
        if (deleteEffectCount == 0) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "删除医院列表规则失败");
        }
        return new BaseOperationResponse();
    }

    @Override
    public HospitalListRuleVo getHospitalListRuleDetail(Long id) {
        HospitalListRule rule = mapper.selectByPrimaryKey(id);
        if (rule == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "医院列表规则不存在");
        }
        return IHospitalListRuleService.toVo(rule);
    }

    @Override
    public HospitalListRulePageVo getHospitalListRulePage(Integer currentPage, Integer pageSize) {
        Page<HospitalListRule> page = PageHelper.startPage(currentPage, pageSize);
        page.doSelectPage(() -> mapper.selectByExample2(HospitalListRule.class, sql -> {
        }));

        HospitalListRulePageVo pageVo = pageVoConvert.toPageVo(page, HospitalListRulePageVo.class, IHospitalListRuleService::toVo);
        List<HospitalListRuleVo> ruleList = pageVo.getList();
        List<Long> groupIds = ruleList.stream().map(HospitalListRuleVo::getHospitalGroupId).collect(Collectors.toList());
        ruleList.stream().filter(rule -> rule.getType() == 5).forEach(rule -> rule.getGroupScale().stream().map(GroupScaleDto::getGroupId).forEach(groupIds::add));

        if (groupIds.isEmpty()) {
            return pageVo;
        }
        Map<Long, HospitalListGroup> groupMap = groupMapper.selectByExample2(HospitalListGroup.class, sql -> sql.andIn("id", groupIds))
                .stream().collect(Collectors.toMap(HospitalListGroup::getId, g -> g));
        ruleList.forEach(rule -> {
            if (rule.getType() == 5) {
                List<GroupScaleDto> groupScale = rule.getGroupScale();
                List<String> groupNames = new ArrayList<>();
                if (!CollectionUtils.isEmpty(groupScale)) {
                    groupScale.forEach(groupScaleDto -> {
                        HospitalListGroup group = groupMap.get(groupScaleDto.getGroupId());
                        if (group != null) {
                            groupNames.add(group.getName());
                        }
                    });
                }
                if (!groupNames.isEmpty()) {
                    rule.setHospitalGroupName(StrUtil.join(",", groupNames));
                }
            } else {
                HospitalListGroup group = groupMap.get(rule.getHospitalGroupId());
                if (group != null) {
                    rule.setHospitalGroupName(group.getName());
                }
            }
        });
        return pageVo;
    }

    @Override
    public HospitalListRuleVo updateHospitalListRule(Long id, UpdateHospitalListRuleReqDto hospitalListRuleDto) {
        checkRuleTypeData(hospitalListRuleDto);
        HospitalListRule rule = mapper.selectByPrimaryKey(id);
        if (rule == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "医院列表规则不存在");
        }
        rule = IHospitalListRuleService.toEntity(hospitalListRuleDto);
        rule.setId(id);
        int updateEffectCount = mapper.updateByPrimaryKey(rule);
        if (updateEffectCount == 0) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "更新医院列表规则失败");
        }
        return IHospitalListRuleService.toVo(rule);
    }

    private <T extends UpdateHospitalListRuleReqDto> void checkRuleTypeData(T dto) {
        if (dto.getType() == null) {
            throw new BizException(HttpStatus.BAD_REQUEST, "规则类型不能为空");
        } else if (dto.getType() == 1 || dto.getType() == 2 || dto.getType() == 3) {
            if (dto.getHospitalGroupId() == null) {
                throw new BizException(HttpStatus.BAD_REQUEST, "医院组id不能为空");
            }
        } else if (dto.getType() == 4) {
            if (dto.getAdvertisementId() == null) {
                throw new BizException(HttpStatus.BAD_REQUEST, "广告ID不能为空");
            }
            if (dto.getAdvertisementType() == null) {
                dto.setAdvertisementType(1);
            }
        } else if (dto.getType() == 5) {
            if (dto.getGroupScale() == null) {
                throw new BizException(HttpStatus.BAD_REQUEST, "医院组规则不能为空");
            }
        } else {
            throw new BizException(HttpStatus.BAD_REQUEST, "规则类型不正确");
        }
    }

    public GetExtensionConditionAdvertisementRespVo getExtensionConditionAdvertisementList(GetExtensionConditionAdvertisementReqDto request) {
        GetExtensionAdvertisementReqDto extensionAdvertisementReqDto = new GetExtensionAdvertisementReqDto();
        extensionAdvertisementReqDto.setIsShow(request.getIsShow())
                .setEnabled(request.isEnable())
                .setBeginTimeStart(LocalDateTimeUtil.of(request.getBeginTimeStart()))
                .setBeginTimeEnd(LocalDateTimeUtil.of(request.getBeginTimeEnd()))
                .setEndTimeStart(LocalDateTimeUtil.of(request.getEndTimeStart()))
                .setEndTimeEnd(LocalDateTimeUtil.of(request.getEndTimeEnd()))
        ;
        List<GetExtensionAdvertisementRespDto> extensionAdvertisementList = advertisementClient.getExtensionAdvertisementList(extensionAdvertisementReqDto);

        List<GetExtensionConditionAdvertisementItemVo> items = extensionAdvertisementList.stream()
                .filter(ex -> ex.getExtensionCondition() != null)
                .map(ex -> {
                    GetExtensionConditionAdvertisementItemVo item = new GetExtensionConditionAdvertisementItemVo();
                    item.setId(ex.getExtensionCondition().getId());
                    item.setExtensionId(ex.getId());
                    item.setExtensionConditionName(ex.getExtensionCondition().getName());
                    item.setAdvertisementResourceName(ex.getRecommendAdvertisementInfo().getName());
                    return item;
                }).collect(Collectors.toList());
        GetExtensionConditionAdvertisementRespVo result = new GetExtensionConditionAdvertisementRespVo();
        result.setList(items);
        return result;
    }
}
