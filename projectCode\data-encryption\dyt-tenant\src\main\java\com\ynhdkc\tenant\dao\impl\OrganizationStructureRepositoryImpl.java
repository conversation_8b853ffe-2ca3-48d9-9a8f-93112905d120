package com.ynhdkc.tenant.dao.impl;

import com.ynhdkc.tenant.dao.OrganizationStructureRepository;
import com.ynhdkc.tenant.dao.mapper.TenantUserStructureMapper;
import com.ynhdkc.tenant.entity.TenantUserStructure;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Collection;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-25 16:22
 */
@Repository
@RequiredArgsConstructor
public class OrganizationStructureRepositoryImpl implements OrganizationStructureRepository {
    private final TenantUserStructureMapper tenantUserStructureMapper;

    @Override
    public void createOrUpdateUserStructure(Long userId, Collection<Long> tenantIds, Collection<TenantUserStructure> privileges) {
        tenantIds.forEach(tenantId -> deleteUserStructure(userId, tenantId));
        privileges.forEach(tenantUserStructureMapper::insert);
    }

    @Override
    public void deleteUserStructure(Long userId) {
        tenantUserStructureMapper.deleteByExample2(TenantUserStructure.class, sql -> sql.andEqualTo(TenantUserStructure::getUserId, userId));
    }

    @Override
    public void deleteUserStructure(Long userId, Long tenantId) {
        tenantUserStructureMapper.deleteByExample2(TenantUserStructure.class, sql ->
                sql.andEqualTo(TenantUserStructure::getUserId, userId).
                        andEqualTo(TenantUserStructure::getTenantId, tenantId));
    }
}
