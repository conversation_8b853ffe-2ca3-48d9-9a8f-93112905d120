package com.ynhdkc.tenant.service;

import backend.common.exception.BizException;
import com.ynhdkc.tenant.dao.mapper.AddressMapper;
import com.ynhdkc.tenant.entity.Address;
import com.ynhdkc.tenant.model.AddressDto;
import com.ynhdkc.tenant.model.AddressVo;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2023/2/15 09:14:36
 */
@Service
@RequiredArgsConstructor
public class AddressService {
    private final AddressMapper addressRepository;

    private final Function<AddressDto, Address> toAddressFunction = addressVo -> {
        final Address address = new Address();
        address.setCounty(addressVo.getCounty());
        address.setCity(addressVo.getCity());
        address.setProvince(addressVo.getProvince());
        address.setDetail(addressVo.getDetail());
        address.setLongitude(addressVo.getLongitude());
        address.setLatitude(addressVo.getLatitude());
        return address;
    };

    private final Function<Address, AddressVo> toAddressVoFunction = address -> {
        final AddressVo addressVo = new AddressVo();
        addressVo.setId(address.getId());
        addressVo.setCounty(address.getCounty());
        addressVo.setCity(address.getCity());
        addressVo.setProvince(address.getProvince());
        addressVo.setDetail(address.getDetail());
        addressVo.setLongitude(address.getLongitude());
        addressVo.setLatitude(address.getLatitude());
        return addressVo;
    };

    @Transactional(rollbackFor = Exception.class)
    public Long create(AddressDto address) {
        Address addressEntity = toAddressFunction.apply(address);
        int effectiveCount = addressRepository.insertSelective(addressEntity);
        if (effectiveCount == 0) {
            throw new BizException(HttpStatus.BAD_REQUEST, "创建地址失败");
        }
        return addressEntity.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long addressId) {
        addressRepository.deleteByPrimaryKey(addressId);
    }

    public List<Address> getAddresses(List<Long> addressIds) {
        Consumer<WeekendSqls<Address>> consumer = weekendSqls -> weekendSqls.andIn(Address::getId, addressIds);
        return addressRepository.selectByExample2(Address.class, consumer);
    }

    @Transactional(rollbackFor = Exception.class)
    public AddressVo update(Long addressId, AddressDto address) {
        Address addressEntity = toAddressFunction.apply(address);
        addressEntity.setId(addressId);
        addressRepository.updateByPrimaryKeySelective(addressEntity);

        return toAddressVoFunction.apply(addressEntity);
    }

    public AddressVo getAddressVoById(Long addressId) {
        if(addressId == null) {
            return null;
        }
        Address address = addressRepository.selectByPrimaryKey(addressId);
        if (address == null) {
//            throw new BizException(HttpStatus.BAD_REQUEST, "地址不存在");
            return null;
        }
        return toAddressVoFunction.apply(address);
    }

    public Address getAddressVoById2(Long addressId) {
        return addressRepository.selectByPrimaryKey(addressId);

    }

    public List<AddressVo> getAddressVosByIds(List<Long> addressIds) {
        List<Address> addresses = getAddresses(addressIds);
        List<AddressVo> addressVos = new ArrayList<>(addresses.size());
        addresses.forEach(address -> addressVos.add(toAddressVoFunction.apply(address)));
        return addressVos;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<Long> addressIds) {
        addressRepository.deleteByExample2(Address.class, weekendSqls -> weekendSqls.andIn(Address::getId, addressIds));
    }
}
