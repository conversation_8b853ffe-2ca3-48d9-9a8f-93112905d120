package backend.encryption.config;

import backend.encryption.annotation.EncryptField;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * 加密配置属性类
 * 用于从application.yml中读取加密相关配置
 * 
 * 配置示例：
 * <pre>
 * backend:
 *   encryption:
 *     enabled: true
 *     secret-key: "your-secret-key-here"
 *     algorithm: AES_GCM
 *     global-strategy: DIRECT_ENCRYPT
 *     strategies:
 *       user-phone:
 *         strategy: SHADOW_PRIORITY
 *         shadow-field: "phone_encrypted"
 *         enabled: true
 *       user-email:
 *         strategy: PLAINTEXT_PRIORITY
 *         shadow-field: "email_encrypted"
 *         enabled: true
 * </pre>
 * 
 * <AUTHOR> Backend Team
 * @version 1.1-SNAPSHOT
 * @since 2024-06-24
 */
@Data
@ConfigurationProperties(prefix = "backend.encryption")
public class EncryptionProperties {
    
    /**
     * 是否启用加密功能
     */
    private boolean enabled = true;
    
    /**
     * 加密密钥
     * 生产环境建议从环境变量或密钥管理系统获取
     */
    private String secretKey;
    
    /**
     * 默认加密算法
     */
    private EncryptField.AlgorithmType algorithm = EncryptField.AlgorithmType.AES_GCM;
    
    /**
     * 全局迁移策略
     * 如果设置，将覆盖注解中的策略配置
     */
    private EncryptField.MigrationStrategy globalStrategy;
    
    /**
     * 具体字段的策略配置
     * key为策略键，value为策略配置
     */
    private Map<String, FieldStrategy> strategies = new HashMap<>();
    
    /**
     * 是否启用性能监控
     */
    private boolean performanceMonitorEnabled = false;
    
    /**
     * 是否启用调试日志
     */
    private boolean debugEnabled = false;
    
    /**
     * 字段策略配置
     */
    @Data
    public static class FieldStrategy {
        
        /**
         * 迁移策略
         */
        private EncryptField.MigrationStrategy strategy = EncryptField.MigrationStrategy.DIRECT_ENCRYPT;
        
        /**
         * 影子字段名称
         */
        private String shadowField;
        
        /**
         * 是否启用
         */
        private boolean enabled = true;
        
        /**
         * 加密算法
         */
        private EncryptField.AlgorithmType algorithm = EncryptField.AlgorithmType.AES_GCM;
        
        /**
         * 字段描述
         */
        private String description;
        
        /**
         * 数据版本号
         */
        private int version = 1;
    }
    
    /**
     * SM2算法配置
     */
    @Data
    public static class SM2Config {
        
        /**
         * 公钥
         */
        private String publicKey;
        
        /**
         * 私钥
         */
        private String privateKey;
        
        /**
         * 是否启用
         */
        private boolean enabled = false;
    }
    
    /**
     * SM4算法配置
     */
    @Data
    public static class SM4Config {
        
        /**
         * 密钥
         */
        private String secretKey;
        
        /**
         * 是否启用
         */
        private boolean enabled = false;
    }
    
    /**
     * 国密算法配置
     */
    private SM2Config sm2 = new SM2Config();
    private SM4Config sm4 = new SM4Config();
}
