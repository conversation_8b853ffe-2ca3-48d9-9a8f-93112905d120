package backend.common.entity.dto.hisgateway.request;

import backend.common.entity.dto.hisgateway.response.DepartmentDoctorItem;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class DepartmentDoctorListRequest {

    // 滇医通自定义唯一标识
    @JsonProperty("hospital_code")
    private String hospitalCode;

    // code 是 his 的唯一标识
    @JsonProperty("department_code")
    private String departmentCode;

    @JsonProperty("sub_department_code")
    private String subDepartmentCode;

    // id 是 数据库id
    @JsonProperty("department_id")
    private Long departmentId;

    @JsonProperty("department_type")
    private Integer departmentType;

    @JsonProperty("start_date")
    private Long startDate;

    @JsonProperty("end_date")
    private Long endDate;

    @JsonProperty("clinic_type")
    private String clinicType;

    @JsonProperty("doctor_list")
    private List<DepartmentDoctorItem> doctorList;

    private String doctors;

}
