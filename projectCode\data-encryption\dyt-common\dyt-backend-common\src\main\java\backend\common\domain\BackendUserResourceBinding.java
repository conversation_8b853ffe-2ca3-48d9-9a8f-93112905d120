package backend.common.domain;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import java.util.Date;

@Data
public class BackendUserResourceBinding {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Long id;
    private Long tenantId;
    private Long userId;
    private Integer kind;
    private Long resourceId;
    private Date createTime;
    private Date updateTime;
}
