package com.ynhdkc.tenant.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

@Data
@Configuration
@ConfigurationProperties(prefix = "hospital")
public class HospitalConfig {

    private List<String> hospitalAreaOutList;

    private List<String> departmentOutList;
    private Map<String, String> hospitalAreaTimeList;
}
