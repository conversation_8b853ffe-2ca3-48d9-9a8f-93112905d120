package com.ynhdkc.tenant.api.backend;

import backend.common.util.JsonUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ynhdkc.tenant.client.HisGatewayClient;
import com.ynhdkc.tenant.client.model.DepartmentDoctorListEnvRequest;
import com.ynhdkc.tenant.client.model.DepartmentDoctorListEnvResponse;
import com.ynhdkc.tenant.dao.DepartmentQuery;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.RecommendConfig;
import com.ynhdkc.tenant.handler.SourceRecommendConfigApi;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.DictLabelService;
import com.ynhdkc.tenant.service.backend.IDoctorService;
import com.ynhdkc.tenant.service.backend.RecommendConfigService;
import com.ynhdkc.tenant.service.customer.impl.HisGatewayServiceImpl;
import com.ynhdkc.tenant.tool.CacheComponent;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@RestController
@Api(tags = "SourceRecommendConfig")
@RequiredArgsConstructor
@Slf4j
public class SourceRecommendConfigController implements SourceRecommendConfigApi {
    public final static String dictType = "source_recommend";
    private final static String RecommendDoctorKey = "dyt-tenant:recommend-doctor:";
    private final static ObjectMapper objectMapper = new ObjectMapper();
    private final DictLabelService dictLabelService;
    private final RecommendConfigService recommendConfigService;
    private final IDoctorService doctorService;
    private final CacheComponent cacheComponent;
    @Resource
    protected DepartmentQuery departmentQuery;
    @Resource
    protected HisGatewayServiceImpl hisGatewayService;
    @Resource
    protected HisGatewayClient hisGatewayClient;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    // 获取分组信息
    @Override
    public ResponseEntity<SourceRecommendConfigPageVo> sourceRecommendConfig(String name, String dictTypeName, Integer status, Integer currentPage, Integer pageSize) {
        DictLabelPageVo list = dictLabelService.getSourceRecommendList(name, dictTypeName, null, status, currentPage, pageSize);
        List<DictLabelVo> vos = list.getList();
        SourceRecommendConfigPageVo resPage = new SourceRecommendConfigPageVo();
        List<DictLabelSourceVo> resList = new ArrayList<>();
        vos.forEach(dictLabelVo -> {
            DictLabelSourceVo sourceVo = new DictLabelSourceVo();
            int num = recommendConfigService.selectCount(dictLabelVo.getId().toString());
            sourceVo.setId(dictLabelVo.getId());
            sourceVo.setDictLabel(dictLabelVo.getDictLabel());
            sourceVo.setDictValue(dictLabelVo.getDictValue());
            sourceVo.setSort(dictLabelVo.getSort());
            sourceVo.setDescription(dictLabelVo.getDescription());
            sourceVo.setStatus(Integer.parseInt(dictLabelVo.getRedirectPath()));
            sourceVo.setDoctorNum(num);
            sourceVo.setUpdateTime(dictLabelVo.getUpdateTime());
            resList.add(sourceVo);
        });
        resPage.setTotalSize(list.getTotalSize());
        resPage.setCurrentPage(list.getCurrentPage());
        resPage.setPageSize(list.getPageSize());
        resPage.setList(resList);
        return ResponseEntity.ok(resPage);
    }

    @Override
    public ResponseEntity<Void> deleteSourceRecommendConfig(String id) {
        int i = recommendConfigService.selectCount(id);
        if (i > 0) {
            throw new RuntimeException("分组医生不为空，无法删除");
        }
        dictLabelService.delete(Long.parseLong(id));
        return ResponseEntity.ok(null);
    }

    @Override
    public ResponseEntity<List<MultipleRecommendConfigDoctorDto>> getMultiDoctorList() {
        return ResponseEntity.ok(recommendConfigService.getMultipleDoctorList());
    }

    private int convertStatus(Integer status) {
        // 当 status 为 null 时，默认返回 1，表示初始状态
        if (status == null) {
            return 1;
        }
        return status == 0 ? 1 : (status == 1 ? 0 : 1);
    }

    // 获取分组医生列表
    @Override
    public ResponseEntity<List<RecommendConfigDoctorDto>> getRecommendDoctorList(String dictLabel) {
        List<RecommendConfig> list = recommendConfigService.selectDoctorRecommondList(dictLabel);
        if (!list.isEmpty()) {
            List<RecommendConfigDoctorDto> res = new ArrayList<>();
            for (RecommendConfig config : list) {
                RecommendConfigDoctorDto doctor = new RecommendConfigDoctorDto();
                String doctorString = config.getDisplayName();
                try {
                    doctor = objectMapper.readValue(doctorString, RecommendConfigDoctorDto.class);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
                DoctorDetailVo docDetail = doctorService.getDetail(Long.parseLong(config.getRedirectUrl()));
                if (Objects.isNull(dictLabel)) {
                    continue;
                }
                doctor.setId(config.getId());
                doctor.setDoctorId(docDetail.getId());
                doctor.setDoctorName(docDetail.getName());
                doctor.setDoctorCode(docDetail.getThrdpartDoctorCode());
                if (!Objects.isNull(docDetail.getRankDictLabel())) {
                    doctor.setDoctorTitle(docDetail.getRankDictLabel().get(0));
                }
                doctor.setDoctorHeadImg(docDetail.getHeadImgUrl());
                doctor.setDepartmentId(docDetail.getDepartmentId());
                doctor.setDepartmentCode(docDetail.getThrdpartDepCode());
                doctor.setHospitalId(docDetail.getHospitalId());
                doctor.setHospitalName(docDetail.getHospitalName());
                doctor.setHospitalAreaId(docDetail.getHospitalAreaId());
                doctor.setHospitalAreaName(docDetail.getHospitalAreaName());
                doctor.setStatus(convertStatus(docDetail.getStatus()));
                doctor.setSort(config.getSort());
                doctor.setDictLabel(config.getDataType());
                doctor.setDictLabelId(Long.parseLong(config.getImgUrl()));
                res.add(doctor);

            }
            return ResponseEntity.ok(res);
        }
        return ResponseEntity.ok(Collections.emptyList());
    }

    // 批量添加分组医生
    @Override
    public ResponseEntity<List<RecommendConfigDoctorDto>> recommendDoctor(UpdateRecommendConfigDoctorDto request) {
        Assert.notEmpty(request, "list is empty");
        return ResponseEntity.ok(recommendConfigService.createRecommendDoctor(request, dictType));
    }

    // 批量更新分组医生
    @Override
    public ResponseEntity<Void> updateRecommendDoctor(UpdateRecommendConfigDoctorDto _list) {
        if (_list.isEmpty()) {
            return ResponseEntity.ok(null);
        }
        recommendConfigService.updateDoctorRecommend(_list);
        return ResponseEntity.ok(null);
    }

    public void updateCache(String message) {
        log.info("更新号满推荐缓存开始:{}", message);
        List<RecommendConfigDoctorDto> collect = new ArrayList<>();
        List<RecommendConfig> list = recommendConfigService.selectDoctorRecommondList(message);
        log.info("查询分组医生结果：{}", JSONUtil.toJsonStr(list));
        for (RecommendConfig config : list) {
            objectMapper.clearProblemHandlers();
            RecommendConfigDoctorDto doctor = null;
            try {
                doctor = objectMapper.readValue(config.getDisplayName(), RecommendConfigDoctorDto.class);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            DoctorDetailVo docDetail = doctorService.getDetail(Long.parseLong(config.getRedirectUrl()));
            if (docDetail == null || docDetail.getStatus() == 1) {
                continue;
            }
            doctor.setId(config.getId());
            doctor.setDoctorId(docDetail.getId());
            doctor.setDoctorName(docDetail.getName());
            doctor.setDoctorCode(docDetail.getThrdpartDoctorCode());
            if (!docDetail.getRankDictLabel().isEmpty()) {
                doctor.setDoctorTitle(docDetail.getRankDictLabel().get(0));
            }
            doctor.setDoctorHeadImg(docDetail.getHeadImgUrl());
            doctor.setDepartmentId(docDetail.getDepartmentId());
            doctor.setDepartmentCode(docDetail.getThrdpartDepCode());
            doctor.setHospitalId(docDetail.getHospitalId());
            doctor.setHospitalName(docDetail.getHospitalName());
            doctor.setHospitalAreaId(docDetail.getHospitalAreaId());
            doctor.setHospitalAreaName(docDetail.getHospitalAreaName());
            doctor.setDepartmentName(docDetail.getDepartmentName());
            doctor.setStatus(convertStatus(docDetail.getStatus()));
            doctor.setSort(config.getSort());
            doctor.setDictLabel(config.getDataType());
            doctor.setDictLabelId(Long.parseLong(config.getImgUrl()));
            Department department = departmentQuery.queryDepartmentById(doctor.getDepartmentId());
            DepartmentDoctorListEnvRequest request = hisGatewayService.buildbuildDepartmentDoctorListEnvRequest(department);
            ResponseEntity<DepartmentDoctorListEnvResponse> temp = hisGatewayClient.getDoctors(department.getHospitalCode(), department.getThrdpartDepCode(), request);
            DepartmentDoctorListEnvResponse response = handleDepartmentDoctorListEnvResponse(temp, request);
            if (response == null) {
                continue;
            }
            boolean b = response.getPayload().getResult().stream().filter(a -> Objects.equals(a.getDoctorCode(), docDetail.getThrdpartDoctorCode()))
                    .collect(Collectors.toList()).stream().anyMatch(it -> it != null && it.getSrcNumber() > 0);
            if (b) {
                collect.add(doctor);
            }
        }
        redisTemplate.delete(RecommendDoctorKey + message);
        // 1. 将整个 collect 列表序列化为 JSON 字符串
        try {
            objectMapper.clearProblemHandlers();
            String json = objectMapper.writeValueAsString(collect);
            log.info("添加缓存{}", json);
            // 2. 使用 set() 方法将 JSON 字符串存入 Redis，并设置过期时间
            redisTemplate.opsForValue().set(RecommendDoctorKey + message, json, 1, TimeUnit.HOURS);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private DepartmentDoctorListEnvResponse handleDepartmentDoctorListEnvResponse(ResponseEntity<DepartmentDoctorListEnvResponse> responseEntity, DepartmentDoctorListEnvRequest request) {
        if (responseEntity.getStatusCode() != HttpStatus.OK) {
            log.error("Failed to get doctors. Request: {}. Response Status: {}", JsonUtil.serializeObject(request), responseEntity.getStatusCode());
            return null;
        }

        DepartmentDoctorListEnvResponse response = responseEntity.getBody();
        if (response == null) {
            log.error("Response body is null. Request: {}", JsonUtil.serializeObject(request));
            return null;
        }

        if (response.getPayload() == null) {
            DepartmentDoctorListEnvResponse.Payload payload = new DepartmentDoctorListEnvResponse.Payload();
            payload.setResult(new ArrayList<>());
            response.setPayload(payload);
        }

        hisGatewayService.filterDoctorScheduleList(request.getPayLoad().getStartDate(), request.getPayLoad().getEndDate(), response.getPayload().getResult());
        log.debug("schedule info response: {}", JsonUtil.serializeObject(response));
        return response;
    }

    @Override
    public ResponseEntity<Void> deleteRecommendDoctor(UpdateRecommendConfigDoctorDto request) {
        Assert.notEmpty(request, "list is empty");
        List<Long> ids = request.stream().map(RecommendConfigDoctorDto::getId).collect(Collectors.toList());
        recommendConfigService.deleteByIds(ids, request.get(0).getDictLabelId());
        return ResponseEntity.ok(null);
    }
}
