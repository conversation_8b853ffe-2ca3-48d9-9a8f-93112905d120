package com.ynhdkc.tenant.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * OssDictFileUploadRequest
 */
@Validated
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2023-09-12T12:06:09.110+08:00")


public class OssDictFileUploadRequest {
    @JsonProperty("dict_list")
    @Valid
    private List<DictFileDto> dictList = null;

    public OssDictFileUploadRequest dictList(List<DictFileDto> dictList) {
        this.dictList = dictList;
        return this;
    }

    public OssDictFileUploadRequest addDictListItem(DictFileDto dictListItem) {
        if (this.dictList == null) {
            this.dictList = new ArrayList<DictFileDto>();
        }
        this.dictList.add(dictListItem);
        return this;
    }

    /**
     * Get dictList
     *
     * @return dictList
     **/
    @ApiModelProperty(value = "")

    @Valid

    public List<DictFileDto> getDictList() {
        return dictList;
    }

    public void setDictList(List<DictFileDto> dictList) {
        this.dictList = dictList;
    }


    @Override
    public boolean equals(java.lang.Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        OssDictFileUploadRequest ossDictFileUploadRequest = (OssDictFileUploadRequest) o;
        return Objects.equals(this.dictList, ossDictFileUploadRequest.dictList);
    }

    @Override
    public int hashCode() {
        return Objects.hash(dictList);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class OssDictFileUploadRequest {\n");

        sb.append("    dictList: ").append(toIndentedString(dictList)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(java.lang.Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}

