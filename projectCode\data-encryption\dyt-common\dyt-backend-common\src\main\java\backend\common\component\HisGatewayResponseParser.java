package backend.common.component;

import backend.common.entity.dto.hisgateway.response.*;
import backend.common.util.MessageUtil;
import org.springframework.stereotype.Component;

@Component
public class HisGatewayResponseParser {

    public DepartmentListResponse parserDepartmentList(String message) {
        String payload = processPayload(message);
        if (payload == null) return null;
        return MessageUtil.json2Obj(payload, DepartmentListResponse.class);
    }

    public DepartmentDoctorListResponse parserDepartmentDoctorList(String message) {
        String payload = processPayload(message);
        if (payload == null) return null;
        return MessageUtil.json2Obj(payload, DepartmentDoctorListResponse.class);
    }

    public DoctorSchedulingInformationRsp parserDoctorSchedulingInfo(String message) {
        String payload = processPayload(message);
        if (payload == null) return null;
        return MessageUtil.json2Obj(payload, DoctorSchedulingInformationRsp.class);
    }

    public DoctorScheduleListResponse parserDoctorSchedulingList(String message) {
        String payload = processPayload(message);
        if (payload == null) return null;
        return MessageUtil.json2Obj(payload, DoctorScheduleListResponse.class);
    }

    public TimeScheduleResponse parserTimeSchedule(String message) {
        String payload = processPayload(message);
        if (payload == null) return null;
        return MessageUtil.json2Obj(payload, TimeScheduleResponse.class);
    }

    public PreRegistrationResponse parserPreRegistration(String message) {
        String payload = processPayload(message);
        if (payload == null) return null;
        return MessageUtil.json2Obj(payload, PreRegistrationResponse.class);
    }

    public CancelPreRegistrationResponse parserCancelPreRegistration(String message) {
        String payload = processPayload(message);
        if (payload == null) return null;
        return MessageUtil.json2Obj(payload, CancelPreRegistrationResponse.class);
    }

    public RegistrationResponse parserRegistration(String message) {
        String payload = processPayload(message);
        if (payload == null) return null;
        return MessageUtil.json2Obj(payload, RegistrationResponse.class);
    }

    public CancelRegistrationResponse parserCancelRegistration(String message) {
        String payload = processPayload(message);
        if (payload == null) return null;
        return MessageUtil.json2Obj(payload, CancelRegistrationResponse.class);
    }

    public QueryPatientArchivesResponse parserQueryPatientArchives(String message) {
        String payload = processPayload(message);
        if (payload == null) return null;
        return MessageUtil.json2Obj(payload, QueryPatientArchivesResponse.class);
    }

    public UpdatePatientArchivesResponse parserUpdatePatientArchives(String message) {
        String payload = processPayload(message);
        if (payload == null) return null;
        return MessageUtil.json2Obj(payload, UpdatePatientArchivesResponse.class);
    }

    public CreateArchivesResponse parserCreateArchives(String message) {
        String payload = processPayload(message);
        if (payload == null) return null;
        return MessageUtil.json2Obj(payload, CreateArchivesResponse.class);
    }

    public DoctorDataSyncResponse parserDoctorDataSync(String message) {
        String payload = processPayload(message);
        if (payload == null) return null;
        return MessageUtil.json2Obj(payload, DoctorDataSyncResponse.class);
    }

    public RegistrationResultCheckResponse parserRegistrationResultCheck(String message) {
        String payload = processPayload(message);
        if (payload == null) return null;
        return MessageUtil.json2Obj(payload, RegistrationResultCheckResponse.class);
    }

    public DoctorOutServiceResponse parserDoctorOutServiceResponse(String message) {
        String payload = processPayload(message);
        if (payload == null) return null;
        return MessageUtil.json2Obj(payload, DoctorOutServiceResponse.class);
    }

    private String processPayload(String message) {
        MessageEnvelope<?> responseEnvelope = MessageUtil.json2Obj(message, MessageEnvelope.class);
        if (responseEnvelope == null || responseEnvelope.getParameterMap() == null) {
            return null;
        }
        return MessageUtil.object2JSONString(responseEnvelope.getParameterMap());
    }

}
