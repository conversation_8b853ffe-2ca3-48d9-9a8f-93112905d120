package com.ynhdkc.tenant.service.backend.impl;

import backend.common.dao.MySqlCommonMapper;
import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import backend.common.util.JsonUtil;
import backend.common.util.MybatisUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ynhdkc.tenant.dao.mapper.*;
import com.ynhdkc.tenant.entity.*;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.VaccineService;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
@RequiredArgsConstructor
public class VaccineServiceImpl implements VaccineService {
    private final VaccineCategoryMapper vaccineCategoryMapper;
    private final VaccineMapper vaccineMapper;
    private final PageVoConvert pageVoConvert;
    private final DepartmentMapper departmentMapper;
    private final DoctorMapper doctorMapper;
    private final TenantMapper tenantMapper;
    private final HospitalMapper hospitalMapper;


    @Override
    public VaccineCategoryVo createVaccineCategory(CreateVaccineCategoryReqDto request) {
        if (ObjectUtils.isEmpty(request.getName())) {
            throw new BizException(HttpStatus.BAD_REQUEST, "添加失败，疫苗分类名称不能为空");
        }
        VaccineCategory selectCate = vaccineCategoryMapper.selectOneByExample2(VaccineCategory.class,
                condition -> condition.andEqualTo(VaccineCategory::getName, request.getName()));
        if (!Objects.isNull(selectCate)) {
            throw new BizException(HttpStatus.BAD_REQUEST, "添加失败，已存在同名疫苗分类");
        }
        VaccineCategory entity = VaccineService.VaccineCategoryToEntity(request);
        int insertEffectCount = vaccineCategoryMapper.insert(entity);
        if (insertEffectCount == 0) {
            log.error("疫苗分类数据入库失败：{}", JsonUtil.serializeObject(request));
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "添加失败");
        }
        return VaccineService.VaccineCategoryToVo(entity);
    }

    @Override
    public VaccineCategoryVo updateVaccineCategory(Long id, CreateVaccineCategoryReqDto request) {
        VaccineCategory vaccineCategory = vaccineCategoryMapper.selectByPrimaryKey(id);
        if (Objects.isNull(vaccineCategory)) {
            throw new BizException(HttpStatus.NOT_FOUND, "更新失败,疫苗分类不存在");
        }
        VaccineCategory entity = VaccineService.VaccineCategoryToEntity(request);
        entity.setId(id);
        int updateEffectCount = vaccineCategoryMapper.updateByPrimaryKey(entity);
        if (updateEffectCount == 0) {
            log.error("疫苗分类数据更新失败：{}", JsonUtil.serializeObject(request));
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "更新失败");
        }

        return VaccineService.VaccineCategoryToVo(entity);
    }

    @Override
    public BaseOperationResponse deleteVaccineCategory(Long id) {
        VaccineCategory vaccineCategory = vaccineCategoryMapper.selectByPrimaryKey(id);
        if (Objects.isNull(vaccineCategory)) {
            throw new BizException(HttpStatus.NOT_FOUND, "删除失败,疫苗分类不存在");
        }
        int deleteEffectCount = vaccineCategoryMapper.deleteByPrimaryKey(id);
        if (deleteEffectCount == 0) {
            log.error("疫苗分类数据删除失败：{}", JsonUtil.serializeObject(id));
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "删除失败");
        }
        return BaseOperationResponse.builder().build();
    }

    @Override
    public VaccineCategoryVo getVaccineCategory(Long id) {
        VaccineCategory vaccineCategory = vaccineCategoryMapper.selectByPrimaryKey(id);
        if (Objects.isNull(vaccineCategory)) {
            throw new BizException(HttpStatus.NOT_FOUND, "查询失败,疫苗分类不存在");
        }
        return VaccineService.VaccineCategoryToVo(vaccineCategory);
    }

    @Override
    public VaccineCategoryPageVo getVaccineCategoryPage(GetVaccineCategoryPageReqDto request) {
        Page<VaccineCategory> page = PageHelper.startPage(request.getCurrent(), request.getSize());
        page.doSelectPage(() -> vaccineCategoryMapper.selectByExample(VaccineCategory.class, helper -> {
            helper.defGroup(sql -> {
                if (!ObjectUtils.isEmpty(request.getName())) {
                    sql.andLike(VaccineCategory::getName, MybatisUtil.likeBoth(request.getName()));
                }
                if (!ObjectUtils.isEmpty(request.getStatus())) {
                    sql.andEqualTo(VaccineCategory::getStatus, request.getStatus());
                }
                if (!ObjectUtils.isEmpty(request.getIsHot())) {
                    sql.andEqualTo(VaccineCategory::getIsHot, request.getIsHot());
                }
            });
            helper.builder(sql -> sql.orderByAsc(VaccineCategory::getSort));
        }));
        return pageVoConvert.toPageVo(page, VaccineCategoryPageVo.class, VaccineService::VaccineCategoryToVo);
    }

    @Override
    public VaccineVo createVaccine(CreateVaccineReqDto request) {
        Vaccine entity = validateVaccineRequest(request);
        int insertEffectCount = vaccineMapper.insert(entity);
        if (insertEffectCount == 0) {
            log.error("疫苗数据入库失败：{}", JsonUtil.serializeObject(request));
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "添加失败");
        }
        return VaccineService.vaccineToVo(entity);
    }

    private Vaccine validateVaccineRequest(CreateVaccineReqDto request) {
        Doctor doctor = doctorMapper.selectByPrimaryKey(request.getDoctorId());
        if (Objects.isNull(doctor)) {
            throw new BizException(HttpStatus.BAD_REQUEST, "医生不存在");
        }
        Department department = departmentMapper.selectByPrimaryKey(doctor.getDepartmentId());
        if (Objects.isNull(department)) {
            throw new BizException(HttpStatus.BAD_REQUEST, "科室不存在");
        }
        VaccineCategory vaccineCategory = vaccineCategoryMapper.selectByPrimaryKey(request.getVaccineCategoryId());
        if (Objects.isNull(vaccineCategory)) {
            throw new BizException(HttpStatus.BAD_REQUEST, "疫苗分类不存在");
        }
        Vaccine vaccine = VaccineService.vaccineToEntity(request);
        vaccine.setTenantId(department.getTenantId())
                .setHospitalId(department.getHospitalId())
                .setHospitalAreaId(department.getHospitalAreaId())
                .setDepartmentId(department.getId())
                .setDoctorId(doctor.getId());

        return vaccine;
    }

    @Override
    public VaccineVo updateVaccine(Long id, UpdateVaccineReqDto request) {
        Vaccine vaccine = vaccineMapper.selectByPrimaryKey(id);
        if (Objects.isNull(vaccine)) {
            throw new BizException(HttpStatus.NOT_FOUND, "更新失败,疫苗不存在");
        }
        updateIfNotNull(vaccine, request);
        int updateEffectCount = vaccineMapper.updateByPrimaryKey(vaccine);
        if (updateEffectCount == 0) {
            log.error("疫苗数据更新失败：{}", JsonUtil.serializeObject(request));
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "更新失败");
        }
        return VaccineService.vaccineToVo(vaccine);
    }

    private void updateIfNotNull(Vaccine entity, UpdateVaccineReqDto dto) {
        if (dto.getDoctorId() != null) {
            Doctor doctor = doctorMapper.selectByPrimaryKey(dto.getDoctorId());
            if (Objects.isNull(doctor)) {
                throw new BizException(HttpStatus.BAD_REQUEST, "医生不存在");
            }
            entity.setDoctorId(dto.getDoctorId());
            Department department = departmentMapper.selectByPrimaryKey(doctor.getDepartmentId());

            if (Objects.isNull(department)) {
                throw new BizException(HttpStatus.BAD_REQUEST, "科室不存在");
            }
            entity.setTenantId(department.getTenantId())
                    .setHospitalId(department.getHospitalId())
                    .setHospitalAreaId(department.getHospitalAreaId())
                    .setDepartmentId(department.getId())
                    .setDoctorId(doctor.getId());
        }
        if (dto.getVaccineCategoryId() != null) {
            VaccineCategory vaccineCategory = vaccineCategoryMapper.selectByPrimaryKey(dto.getVaccineCategoryId());
            if (Objects.isNull(vaccineCategory)) {
                throw new BizException(HttpStatus.BAD_REQUEST, "疫苗分类不存在");
            }
            entity.setVaccineCategoryId(dto.getVaccineCategoryId());
        }
        if (dto.getSort() != null) {
            entity.setSort(dto.getSort());
        }
        if (!ObjectUtils.isEmpty(dto.getRemark())) {
            entity.setRemark(dto.getRemark());
        }
        if (!ObjectUtils.isEmpty(dto.getTips())) {
            entity.setTips(dto.getTips());
        }
    }

    @Override
    public BaseOperationResponse deleteVaccine(Long id) {
        Vaccine vaccine = vaccineMapper.selectByPrimaryKey(id);
        if (Objects.isNull(vaccine)) {
            throw new BizException(HttpStatus.NOT_FOUND, "删除失败,疫苗不存在");
        }
        int deleteEffectCount = vaccineMapper.deleteByPrimaryKey(id);
        if (deleteEffectCount == 0) {
            log.error("疫苗数据删除失败：{}", JsonUtil.serializeObject(id));
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "删除失败");
        }
        return BaseOperationResponse.builder().build();
    }

    @Override
    public VaccineVo getVaccine(Long id) {
        Vaccine vaccine = vaccineMapper.selectByPrimaryKey(id);
        if (Objects.isNull(vaccine)) {
            throw new BizException(HttpStatus.NOT_FOUND, "查询失败,疫苗不存在");
        }
        return VaccineService.vaccineToVo(vaccine);
    }

    @Override
    public VaccinePageVo getVaccinePage(GetVaccinePageReqDto request) {
        Consumer<MySqlCommonMapper.Helper<Vaccine>> helperConsumer = (helper -> {
            helper.defGroup(sql -> {
                if (!ObjectUtils.isEmpty(request.getVaccineCategoryId())) {
                    sql.andEqualTo(Vaccine::getVaccineCategoryId, request.getVaccineCategoryId());
                }
                if (!ObjectUtils.isEmpty(request.getVaccineId())) {
                    sql.andEqualTo(Vaccine::getId, request.getVaccineId());
                }
                if (!ObjectUtils.isEmpty(request.getHospitalId())) {
                    sql.andEqualTo(Vaccine::getHospitalId, request.getHospitalId());
                }
                if (!ObjectUtils.isEmpty(request.getDepartmentId())) {
                    sql.andEqualTo(Vaccine::getDepartmentId, request.getDepartmentId());
                }
                if (!ObjectUtils.isEmpty(request.getDoctorId())) {
                    sql.andEqualTo(Vaccine::getDoctorId, request.getDoctorId());
                }
            });
            helper.builder(sql -> sql.orderByAsc(Vaccine::getSort));
        });
        Consumer<WeekendSqls<Doctor>> doctorSql = where -> {
        };
        if (!ObjectUtils.isEmpty(request.getDoctorName())) {
            doctorSql = doctorSql.andThen(where -> where.andLike(Doctor::getName, MybatisUtil.likeBoth(request.getDoctorName())));
        }
        if (!ObjectUtils.isEmpty(request.getStatus())) {
            doctorSql = doctorSql.andThen(where -> where.andEqualTo(Doctor::getStatus, request.getStatus()));
        }
        List<Doctor> doctors = doctorMapper.selectByExample2(Doctor.class, doctorSql);
        if (CollectionUtils.isEmpty(doctors)) {
            return new VaccinePageVo();
        }
        List<Long> doctorIds = doctors.stream().map(Doctor::getId).collect(Collectors.toList());
        helperConsumer = helperConsumer.andThen(helper -> helper.defGroup(sql -> sql.andIn(Vaccine::getDoctorId, doctorIds)));
        Page<Vaccine> page = PageHelper.startPage(request.getCurrent(), request.getSize());
        Consumer<MySqlCommonMapper.Helper<Vaccine>> finalHelper = helperConsumer;
        page.doSelectPage(() -> vaccineMapper.selectByExample(Vaccine.class, finalHelper));
        VaccinePageVo pageVo = pageVoConvert.toPageVo(page, VaccinePageVo.class, VaccineService::vaccineToVo);
        pageVo.setList(buildVaccineVo(pageVo.getList()));
        return pageVo;
    }

    private List<VaccineVo> buildVaccineVo(List<VaccineVo> vaccineVos) {
        if (CollectionUtils.isEmpty(vaccineVos)) {
            return vaccineVos;
        }
        List<Long> tenantIds = vaccineVos.stream().map(VaccineVo::getTenantId).collect(Collectors.toList());
        List<Long> hospitalIds = vaccineVos.stream().flatMap(vo -> Stream.of(vo.getHospitalId(), vo.getHospitalAreaId())).collect(Collectors.toList());
        List<Long> departmentIds = vaccineVos.stream().map(VaccineVo::getDepartmentId).collect(Collectors.toList());
        List<Long> doctorIds = vaccineVos.stream().map(VaccineVo::getDoctorId).collect(Collectors.toList());
        List<Long> vaccineCategoryIds = vaccineVos.stream().map(VaccineVo::getVaccineCategoryId).collect(Collectors.toList());

        Map<Long, Tenant> tenantMap = new HashMap<>();
        Map<Long, Hospital> hospitalMap = new HashMap<>();
        Map<Long, Department> departmentMap = new HashMap<>();
        Map<Long, Doctor> doctorMap = new HashMap<>();
        Map<Long, VaccineCategory> vaccineCategoryMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(tenantIds)) {
            List<Tenant> tenants = tenantMapper.selectByExample2(Tenant.class, where -> where.andIn(Tenant::getId, tenantIds));
            tenantMap = tenants.stream().collect(Collectors.toMap(Tenant::getId, Function.identity()));
        }
        if (!CollectionUtils.isEmpty(hospitalIds)) {
            List<Hospital> hospitals = hospitalMapper.selectByExample2(Hospital.class, where -> where.andIn(Hospital::getId, hospitalIds));
            hospitalMap = hospitals.stream().collect(Collectors.toMap(Hospital::getId, Function.identity()));
        }
        if (!CollectionUtils.isEmpty(departmentIds)) {
            List<Department> departments = departmentMapper.selectByExample2(Department.class, where -> where.andIn(Department::getId, departmentIds));
            departmentMap = departments.stream().collect(Collectors.toMap(Department::getId, Function.identity()));
        }
        if (!CollectionUtils.isEmpty(doctorIds)) {
            List<Doctor> doctors = doctorMapper.selectByExample2(Doctor.class, where -> where.andIn(Doctor::getId, doctorIds));
            doctorMap = doctors.stream().collect(Collectors.toMap(Doctor::getId, Function.identity()));
        }
        if (!CollectionUtils.isEmpty(vaccineCategoryIds)) {
            List<VaccineCategory> vaccineCategories = vaccineCategoryMapper.selectByExample2(VaccineCategory.class, where -> where.andIn(VaccineCategory::getId, vaccineCategoryIds));
            vaccineCategoryMap = vaccineCategories.stream().collect(Collectors.toMap(VaccineCategory::getId, Function.identity()));
        }

        for (VaccineVo vaccineVo : vaccineVos) {
            vaccineVo.setTenantName(tenantMap.get(vaccineVo.getTenantId()) != null ? tenantMap.get(vaccineVo.getTenantId()).getName() : null);
            vaccineVo.setHospitalName(hospitalMap.get(vaccineVo.getHospitalId()) != null ? hospitalMap.get(vaccineVo.getHospitalId()).getName() : null);
            vaccineVo.setHospitalAreaName(hospitalMap.get(vaccineVo.getHospitalAreaId()) != null ? hospitalMap.get(vaccineVo.getHospitalAreaId()).getName() : null);
            vaccineVo.setDepartmentName(departmentMap.get(vaccineVo.getDepartmentId()) != null ? departmentMap.get(vaccineVo.getDepartmentId()).getName() : null);
            Doctor doctor = doctorMap.get(vaccineVo.getDoctorId());
            if (doctor != null) {
                vaccineVo.setDoctorName(doctor.getName());
                vaccineVo.setStatus(doctor.getStatus());
            }
            vaccineVo.setVaccineCategoryName(vaccineCategoryMap.get(vaccineVo.getVaccineCategoryId()) != null ? vaccineCategoryMap.get(vaccineVo.getVaccineCategoryId()).getName() : null);
        }
        return vaccineVos;
    }
}
