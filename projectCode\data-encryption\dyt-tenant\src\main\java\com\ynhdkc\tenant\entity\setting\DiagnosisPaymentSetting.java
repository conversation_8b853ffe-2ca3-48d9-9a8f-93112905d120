package com.ynhdkc.tenant.entity.setting;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-09 10:03
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(name = "t_hospital_diagnosis_payment_setting")
public class DiagnosisPaymentSetting extends BaseSetting {
    /**
     * 是否支持合并支付
     */
    private Boolean supportMergerPayment;
    /**
     * 是否支持在线退款
     */
    private Boolean supportOnlineRefund;
    /**
     * 是否允许当天退款：0，不允许；1，允许；
     */
    private Boolean refundToday;
    /**
     * 停止退款时间
     */
    private String stopRefundTime;
    /**
     * 是否支持开发票
     */
    private Boolean supportInvoice;
    /**
     * 已选支付方式
     */
    private String selectedPayments;
    /**
     * 缴费信息
     */
    private String paymentInformation;
}
