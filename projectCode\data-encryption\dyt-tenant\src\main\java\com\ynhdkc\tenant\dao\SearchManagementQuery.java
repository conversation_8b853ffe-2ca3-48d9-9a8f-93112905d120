package com.ynhdkc.tenant.dao;

import backend.common.util.BaseQueryOption;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.entity.DictFile;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/9/12 16:17
 */
public interface SearchManagementQuery {
    DictFile queryById(Long id);

    DictFile queryByNameAndType(String name, Integer type);

    Page<DictFile> pageQueryDictFile(DictFileQueryOption option);

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Accessors(chain = true)
    class DictFileQueryOption extends BaseQueryOption {
        private String name;
        private Integer type;
        private Integer status;
        private Integer clusterStatus;

        public DictFileQueryOption(Integer currentPage, Integer pageSize) {
            super(currentPage, pageSize);
        }
    }
}
