package com.example.encryption.mybatis.mapper;

import com.example.encryption.mybatis.entity.MybatisUser;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * MyBatis用户Mapper接口
 * 演示MyBatis环境下的加密字段操作
 */
@Mapper
public interface MybatisUserMapper {
    
    /**
     * 插入用户
     */
    @Insert("INSERT INTO mybatis_users (username, phone, email, id_card, real_name, age, status, created_at, updated_at) " +
            "VALUES (#{username}, #{phone,typeHandler=com.example.encryption.mybatis.handler.EncryptTypeHandler}, " +
            "#{email,typeHandler=com.example.encryption.mybatis.handler.EncryptTypeHandler}, " +
            "#{idCard,typeHandler=com.example.encryption.mybatis.handler.EncryptTypeHandler}, " +
            "#{realName,typeHandler=com.example.encryption.mybatis.handler.EncryptTypeHandler}, " +
            "#{age}, #{status}, #{createdAt}, #{updatedAt})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUser(MybatisUser user);
    
    /**
     * 根据ID查询用户
     */
    @Select("SELECT id, username, phone, email, id_card, real_name, age, status, created_at, updated_at " +
            "FROM mybatis_users WHERE id = #{id}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "username", column = "username"),
        @Result(property = "phone", column = "phone", 
                typeHandler = com.example.encryption.mybatis.handler.EncryptTypeHandler.class),
        @Result(property = "email", column = "email", 
                typeHandler = com.example.encryption.mybatis.handler.EncryptTypeHandler.class),
        @Result(property = "idCard", column = "id_card", 
                typeHandler = com.example.encryption.mybatis.handler.EncryptTypeHandler.class),
        @Result(property = "realName", column = "real_name", 
                typeHandler = com.example.encryption.mybatis.handler.EncryptTypeHandler.class),
        @Result(property = "age", column = "age"),
        @Result(property = "status", column = "status"),
        @Result(property = "createdAt", column = "created_at"),
        @Result(property = "updatedAt", column = "updated_at")
    })
    MybatisUser selectUserById(Long id);
    
    /**
     * 查询所有用户
     */
    @Select("SELECT id, username, phone, email, id_card, real_name, age, status, created_at, updated_at " +
            "FROM mybatis_users ORDER BY id")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "username", column = "username"),
        @Result(property = "phone", column = "phone", 
                typeHandler = com.example.encryption.mybatis.handler.EncryptTypeHandler.class),
        @Result(property = "email", column = "email", 
                typeHandler = com.example.encryption.mybatis.handler.EncryptTypeHandler.class),
        @Result(property = "idCard", column = "id_card", 
                typeHandler = com.example.encryption.mybatis.handler.EncryptTypeHandler.class),
        @Result(property = "realName", column = "real_name", 
                typeHandler = com.example.encryption.mybatis.handler.EncryptTypeHandler.class),
        @Result(property = "age", column = "age"),
        @Result(property = "status", column = "status"),
        @Result(property = "createdAt", column = "created_at"),
        @Result(property = "updatedAt", column = "updated_at")
    })
    List<MybatisUser> selectAllUsers();
    
    /**
     * 根据用户名查询用户
     */
    @Select("SELECT id, username, phone, email, id_card, real_name, age, status, created_at, updated_at " +
            "FROM mybatis_users WHERE username = #{username}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "username", column = "username"),
        @Result(property = "phone", column = "phone", 
                typeHandler = com.example.encryption.mybatis.handler.EncryptTypeHandler.class),
        @Result(property = "email", column = "email", 
                typeHandler = com.example.encryption.mybatis.handler.EncryptTypeHandler.class),
        @Result(property = "idCard", column = "id_card", 
                typeHandler = com.example.encryption.mybatis.handler.EncryptTypeHandler.class),
        @Result(property = "realName", column = "real_name", 
                typeHandler = com.example.encryption.mybatis.handler.EncryptTypeHandler.class),
        @Result(property = "age", column = "age"),
        @Result(property = "status", column = "status"),
        @Result(property = "createdAt", column = "created_at"),
        @Result(property = "updatedAt", column = "updated_at")
    })
    MybatisUser selectUserByUsername(String username);
    
    /**
     * 更新用户
     */
    @Update("UPDATE mybatis_users SET username = #{username}, " +
            "phone = #{phone,typeHandler=com.example.encryption.mybatis.handler.EncryptTypeHandler}, " +
            "email = #{email,typeHandler=com.example.encryption.mybatis.handler.EncryptTypeHandler}, " +
            "id_card = #{idCard,typeHandler=com.example.encryption.mybatis.handler.EncryptTypeHandler}, " +
            "real_name = #{realName,typeHandler=com.example.encryption.mybatis.handler.EncryptTypeHandler}, " +
            "age = #{age}, status = #{status}, updated_at = #{updatedAt} " +
            "WHERE id = #{id}")
    int updateUser(MybatisUser user);
    
    /**
     * 删除用户
     */
    @Delete("DELETE FROM mybatis_users WHERE id = #{id}")
    int deleteUser(Long id);
    
    /**
     * 根据状态查询用户
     */
    @Select("SELECT id, username, phone, email, id_card, real_name, age, status, created_at, updated_at " +
            "FROM mybatis_users WHERE status = #{status}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "username", column = "username"),
        @Result(property = "phone", column = "phone", 
                typeHandler = com.example.encryption.mybatis.handler.EncryptTypeHandler.class),
        @Result(property = "email", column = "email", 
                typeHandler = com.example.encryption.mybatis.handler.EncryptTypeHandler.class),
        @Result(property = "idCard", column = "id_card", 
                typeHandler = com.example.encryption.mybatis.handler.EncryptTypeHandler.class),
        @Result(property = "realName", column = "real_name", 
                typeHandler = com.example.encryption.mybatis.handler.EncryptTypeHandler.class),
        @Result(property = "age", column = "age"),
        @Result(property = "status", column = "status"),
        @Result(property = "createdAt", column = "created_at"),
        @Result(property = "updatedAt", column = "updated_at")
    })
    List<MybatisUser> selectUsersByStatus(MybatisUser.UserStatus status);
    
    /**
     * 统计用户数量
     */
    @Select("SELECT COUNT(*) FROM mybatis_users")
    long countUsers();
    
    /**
     * 检查用户名是否存在
     */
    @Select("SELECT COUNT(*) FROM mybatis_users WHERE username = #{username}")
    boolean existsByUsername(String username);
}
