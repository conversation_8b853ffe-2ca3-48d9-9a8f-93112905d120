package com.ynhdkc.tenant.entity.setting;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-08 17:55
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(name = "t_hospital_patient_card_setting")
public class PatientCardSetting extends BaseSetting {
    /**
     * 就诊卡名称
     */
    private String cardName;
    /**
     * 是否需要患者就诊卡：0，不需要；1，需要
     */
    private Boolean needPatientCard;
    /**
     * 是否需要患者就诊卡：0，不需要；1，需要
     */
    private Boolean needRecharge;
    /**
     * 绑定类型：0，自动绑卡；1，手动填写自动开卡；2，手工录入
     */
    private Integer bindType;
    /**
     * 支持的患者类型：0，全部；1，成人；2，儿童
     */
    private Integer supportPatientType;
    /**
     * 是否需要电子就诊卡：0，不需要；1，需要
     */
    private Boolean needElectronCard;
}
