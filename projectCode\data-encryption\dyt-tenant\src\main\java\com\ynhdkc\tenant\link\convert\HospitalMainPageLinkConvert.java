package com.ynhdkc.tenant.link.convert;

import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.service.customer.CustomerHospitalService;
import com.ynhdkc.tenant.util.UrlUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

import static com.ynhdkc.tenant.link.convert.Constants.DEFAULT_URL;

@Component
@RequiredArgsConstructor
public class HospitalMainPageLinkConvert implements ILinkConvert {


    private final CustomerHospitalService customerHospitalService;

    @Override
    public String getConvertLink(String source) {
        final String hosCode = UrlUtil.getHospitalCodeFromUrl(source);
        if (hosCode == null) {
            return DEFAULT_URL;
        }

        Hospital hospital = customerHospitalService.queryHospitalById(hosCode).orElse(null);
        if (hospital == null) {
            return DEFAULT_URL;
        }

        final String urlPrefix = "https://appv3.ynhdkc.com/PCregistration_hospital_detail_new";
        return urlPrefix + "?id=" + hospital.getId() + "&hospital_id=" + hospital.getParentId() + "&hos_code=" + hosCode;
    }

    @Override
    public boolean isSupport(String source) {
        final List<String> urlPrefixList = Arrays.asList("https://appv2.ynhdkc.com/registration_hospital_detail?hos_code=",
                "https://testapp.ynhdkc.com/registration_hospital_detail?hos_code=",
                "https://ttestapp.ynhdkc.com/registration_hospital_detail?hos_code=");
        return urlPrefixList.stream().anyMatch(source::startsWith);
    }
}
