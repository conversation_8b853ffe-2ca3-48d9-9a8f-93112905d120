package com.ynhdkc.tenant.entity.constant;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-10 9:30
 */
public enum ReportSupportType {
    /**
     * 全部
     */
    ALL(0),
    /**
     * 检验报告
     */
    LAB_REPORT(1),
    /**
     * 检查报告
     */
    CHECK_REPORT(2);


    private final int value;

    ReportSupportType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static ReportSupportType of(Integer input) {
        if (null == input) {
            return null;
        }
        for (ReportSupportType value : ReportSupportType.values()) {
            if (value.getValue() == input) {
                return value;
            }
        }
        return null;
    }

    public static boolean contains(Integer input) {
        if (null == input) {
            return false;
        }
        for (ReportSupportType value : ReportSupportType.values()) {
            if (value.getValue() == input) {
                return true;
            }
        }
        return false;
    }
}
