package com.ynhdkc.tenant.tool;

import com.ynhdkc.tenant.model.DepartmentsTreeSearchVo;

import java.util.Comparator;

public class BackendDepartmentComparator implements Comparator<DepartmentsTreeSearchVo> {

    static int departmentComparator(String firstLetter, String firstLetter2, Integer sort, Integer sort2) {
        int firstLetterCompare = firstLetter.compareTo(firstLetter2);
        if (firstLetterCompare != 0) {
            return firstLetterCompare;
        }

        return sort.compareTo(sort2);
    }

    /**
     * 先按 firstLetter 正序排序，如果 firstLetter 相同，按科室名称排序，最后按 sort 逆序排序
     *
     * @param o1 the first object to be compared.
     * @param o2 the second object to be compared.
     * @return a negative integer, zero, or a positive integer as the first argument is less than, equal to, or greater
     */
    @Override
    public int compare(DepartmentsTreeSearchVo o1, DepartmentsTreeSearchVo o2) {
        return departmentComparator(o1.getFirstLetter(), o2.getFirstLetter(), o2.getSort(), o1.getSort());
    }
}
