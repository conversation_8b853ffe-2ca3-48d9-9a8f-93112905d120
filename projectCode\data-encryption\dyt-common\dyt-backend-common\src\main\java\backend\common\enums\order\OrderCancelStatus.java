package backend.common.enums.order;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum OrderCancelStatus {

    /**
     * 支付订单取消状态成功流转 0->3->2->9
     * 未支付订单取消成功流转 0->1->9
     * <p>
     * 0 初始状态
     * 1 取消锁号 核实
     * -1 取消锁号 失败
     * <p>
     * 2 退费成功 第三方支付机构退还用户的 money
     * -2 支付失败 第三方支付机构
     * <p>
     * 3 取消下单 核实
     * -3 取消下单失败
     * <p>
     * 9 撤销订单最终状态
     */

    HIS_REFUND_FAILED_STATUS("his退号失败", -3),
    REFUND_FAILED_STATUS("支付机构退费失败", -2),
    UNLOCK_FAILED_STATUS("his解锁号源失败结束", -1),
    INI_STATUS("初始态", 0),
    UNLOCK_STATUS("取消锁号核实", 1),
    REFUND_STATUS("支付机构退费完成", 2),
    HIS_REFUND_MIDDLE_STATUS("待退款", 3),
    FINISH_STATUS("订单结束", 9),
    UNKNOWN("未知状态", 999),
    ;

    private final String value;
    private final Integer code;

    OrderCancelStatus(String value, Integer code) {
        this.code = code;
        this.value = value;
    }

    @JsonCreator
    public static OrderCancelStatus getFromCode(Integer code) {
        for (OrderCancelStatus t : OrderCancelStatus.values()) {
            if (t.code.equals(code)) {
                return t;
            }
        }
        return UNKNOWN;
    }

    public String getValue() {
        return value;
    }

    public Integer getCode() {
        return code;
    }

    @JsonValue
    public String getRequestCode() {
        return code.toString();
    }


}
