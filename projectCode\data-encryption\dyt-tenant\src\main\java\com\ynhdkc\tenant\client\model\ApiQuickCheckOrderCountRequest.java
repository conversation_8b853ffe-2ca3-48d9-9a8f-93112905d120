package com.ynhdkc.tenant.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ApiQuickCheckOrderCountRequest
 */
@Validated
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2025-03-31T20:45:41.667+08:00")


public class ApiQuickCheckOrderCountRequest {
  @JsonProperty("hospital_ids")
  @Valid
  private List<Long> hospitalIds = null;

  @JsonProperty("hospital_area_ids")
  @Valid
  private List<Long> hospitalAreaIds = null;

  @JsonProperty("department_ids")
  @Valid
  private List<Long> departmentIds = null;

  @JsonProperty("doctor_ids")
  @Valid
  private List<Long> doctorIds = null;

  public ApiQuickCheckOrderCountRequest hospitalIds(List<Long> hospitalIds) {
    this.hospitalIds = hospitalIds;
    return this;
  }

  public ApiQuickCheckOrderCountRequest addHospitalIdsItem(Long hospitalIdsItem) {
    if (this.hospitalIds == null) {
      this.hospitalIds = new ArrayList<Long>();
    }
    this.hospitalIds.add(hospitalIdsItem);
    return this;
  }

  /**
   * 医院ID列表（空数组表示不限制）
   * @return hospitalIds
  **/
  @ApiModelProperty(example = "[1001,1002]", value = "医院ID列表（空数组表示不限制）")


  public List<Long> getHospitalIds() {
    return hospitalIds;
  }

  public void setHospitalIds(List<Long> hospitalIds) {
    this.hospitalIds = hospitalIds;
  }

  public ApiQuickCheckOrderCountRequest hospitalAreaIds(List<Long> hospitalAreaIds) {
    this.hospitalAreaIds = hospitalAreaIds;
    return this;
  }

  public ApiQuickCheckOrderCountRequest addHospitalAreaIdsItem(Long hospitalAreaIdsItem) {
    if (this.hospitalAreaIds == null) {
      this.hospitalAreaIds = new ArrayList<Long>();
    }
    this.hospitalAreaIds.add(hospitalAreaIdsItem);
    return this;
  }

  /**
   * 医院院区ID列表（空数组表示不限制）
   * @return hospitalAreaIds
  **/
  @ApiModelProperty(example = "[2001,2002]", value = "医院院区ID列表（空数组表示不限制）")


  public List<Long> getHospitalAreaIds() {
    return hospitalAreaIds;
  }

  public void setHospitalAreaIds(List<Long> hospitalAreaIds) {
    this.hospitalAreaIds = hospitalAreaIds;
  }

  public ApiQuickCheckOrderCountRequest departmentIds(List<Long> departmentIds) {
    this.departmentIds = departmentIds;
    return this;
  }

  public ApiQuickCheckOrderCountRequest addDepartmentIdsItem(Long departmentIdsItem) {
    if (this.departmentIds == null) {
      this.departmentIds = new ArrayList<Long>();
    }
    this.departmentIds.add(departmentIdsItem);
    return this;
  }

  /**
   * 科室ID列表（空数组表示不限制）
   * @return departmentIds
  **/
  @ApiModelProperty(example = "[3001,3002]", value = "科室ID列表（空数组表示不限制）")


  public List<Long> getDepartmentIds() {
    return departmentIds;
  }

  public void setDepartmentIds(List<Long> departmentIds) {
    this.departmentIds = departmentIds;
  }

  public ApiQuickCheckOrderCountRequest doctorIds(List<Long> doctorIds) {
    this.doctorIds = doctorIds;
    return this;
  }

  public ApiQuickCheckOrderCountRequest addDoctorIdsItem(Long doctorIdsItem) {
    if (this.doctorIds == null) {
      this.doctorIds = new ArrayList<Long>();
    }
    this.doctorIds.add(doctorIdsItem);
    return this;
  }

  /**
   * 医生ID列表（空数组表示不限制）
   * @return doctorIds
  **/
  @ApiModelProperty(example = "[4001,4002]", value = "医生ID列表（空数组表示不限制）")


  public List<Long> getDoctorIds() {
    return doctorIds;
  }

  public void setDoctorIds(List<Long> doctorIds) {
    this.doctorIds = doctorIds;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ApiQuickCheckOrderCountRequest quickCheckOrderCountRequest = (ApiQuickCheckOrderCountRequest) o;
    return Objects.equals(this.hospitalIds, quickCheckOrderCountRequest.hospitalIds) &&
        Objects.equals(this.hospitalAreaIds, quickCheckOrderCountRequest.hospitalAreaIds) &&
        Objects.equals(this.departmentIds, quickCheckOrderCountRequest.departmentIds) &&
        Objects.equals(this.doctorIds, quickCheckOrderCountRequest.doctorIds);
  }

  @Override
  public int hashCode() {
    return Objects.hash(hospitalIds, hospitalAreaIds, departmentIds, doctorIds);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ApiQuickCheckOrderCountRequest {\n");
    
    sb.append("    hospitalIds: ").append(toIndentedString(hospitalIds)).append("\n");
    sb.append("    hospitalAreaIds: ").append(toIndentedString(hospitalAreaIds)).append("\n");
    sb.append("    departmentIds: ").append(toIndentedString(departmentIds)).append("\n");
    sb.append("    doctorIds: ").append(toIndentedString(doctorIds)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

