package com.ynhdkc.tenant.service.customer.impl;

import com.ynhdkc.tenant.dao.DepartmentQuery;
import com.ynhdkc.tenant.dao.DoctorQuery;
import com.ynhdkc.tenant.dao.HospitalAreaQuery;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.model.GetCodeMappingReqDto;
import com.ynhdkc.tenant.model.GetCodeMappingVo;
import com.ynhdkc.tenant.service.customer.CustomerCodeMappingService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/8/11 14:54
 */
@Service
@RequiredArgsConstructor
public class CustomerCodeMappingServiceImpl implements CustomerCodeMappingService {
    private final HospitalAreaQuery hospitalAreaQuery;
    private final DepartmentQuery departmentQuery;
    private final DoctorQuery doctorQuery;

    @Override
    public GetCodeMappingVo getCodeMapping(GetCodeMappingReqDto request) {
        GetCodeMappingVo vo = new GetCodeMappingVo();
        Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaBy(request.getHospitalAreaCode());
        if (null != hospitalArea) {
            vo.setHospitalId(hospitalArea.getParentId());
            vo.setHospitalAreaId(hospitalArea.getId());
        }
        if (null != request.getDepartmentCode()) {
            Department department = departmentQuery.queryBy(request.getHospitalAreaCode(), request.getDepartmentCode());
            if (null != department) {
                vo.setDepartmentId(department.getId());
            }

            if (null != request.getDoctorCode()) {
                Doctor doctor = doctorQuery.queryByCode(request.getHospitalAreaCode(), request.getDepartmentCode(), request.getDoctorCode());
                if (null != doctor) {
                    vo.setDoctorId(doctor.getId());
                }
            }
        }
        return vo;
    }
}
