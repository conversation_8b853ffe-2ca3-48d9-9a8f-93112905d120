# DYT Backend Encryption Module

DYT后端加解密模块，提供敏感数据的透明加密存储功能。

## 🎯 功能特性

- **透明加密**：使用JPA AttributeConverter实现数据库字段的透明加密存储
- **多算法支持**：支持AES-GCM、SM2等多种加密算法
- **注解驱动**：通过@EncryptField注解标记需要加密的字段
- **渐进式迁移**：支持影子字段迁移策略，实现零停机数据迁移
- **配置灵活**：支持全局和字段级别的策略配置
- **自动装配**：Spring Boot自动配置，开箱即用

## 🚀 快速开始

### 1. 添加依赖

在你的项目pom.xml中添加依赖：

```xml
<dependency>
    <groupId>com.ynhdkc</groupId>
    <artifactId>dyt-backend-encryption</artifactId>
    <version>1.1-SNAPSHOT</version>
</dependency>
```

### 2. 配置文件

在application.yml中添加配置：

```yaml
backend:
  encryption:
    enabled: true
    secret-key: "your-secret-key-here"
    algorithm: AES_GCM
    global-strategy: DIRECT_ENCRYPT
    debug-enabled: false
    performance-monitor-enabled: false
```

### 3. 实体类使用

```java
@Entity
@Table(name = "user")
public class User {
    @Id
    private Long id;
    
    // 使用默认加密
    @EncryptField
    @Convert(converter = EncryptConverter.class)
    @Column(name = "phone")
    private String phone;
    
    // 使用SM2算法加密
    @EncryptField(algorithm = EncryptField.AlgorithmType.SM2)
    @Convert(converter = EncryptConverter.class)
    @Column(name = "id_card")
    private String idCard;
    
    // 使用影子字段迁移策略
    @EncryptField(
        shadowField = "email_encrypted",
        migrationStrategy = EncryptField.MigrationStrategy.SHADOW_PRIORITY
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "email")
    private String email;
    
    // getter和setter...
}
```

## 📖 详细配置

### 加密算法

支持以下加密算法：

- **AES_GCM**：默认算法，提供认证加密，安全性高，性能好
- **SM2**：国密椭圆曲线算法，符合国家密码标准
- **SM4**：国密对称加密算法（待实现）

### 迁移策略

支持以下迁移策略：

- **DIRECT_ENCRYPT**：直接加密，新数据直接加密存储
- **PLAINTEXT_PRIORITY**：明文优先，当前字段存储明文，影子字段存储密文
- **SHADOW_PRIORITY**：影子字段优先，优先从影子字段读取加密数据
- **SHADOW_ONLY**：仅影子字段，只使用影子字段存储加密数据

### 高级配置

```yaml
backend:
  encryption:
    enabled: true
    secret-key: "your-secret-key"
    algorithm: AES_GCM
    global-strategy: DIRECT_ENCRYPT
    debug-enabled: true
    performance-monitor-enabled: true
    
    # 字段级别策略配置
    strategies:
      user-phone:
        strategy: SHADOW_PRIORITY
        shadow-field: "phone_encrypted"
        algorithm: AES_GCM
        enabled: true
        description: "用户手机号加密"
        
      user-idcard:
        strategy: DIRECT_ENCRYPT
        algorithm: SM2
        enabled: true
        description: "用户身份证号加密"
    
    # 国密算法配置
    sm2:
      enabled: true
      public-key: "your-sm2-public-key"
      private-key: "your-sm2-private-key"
```

## 🔧 工具类使用

### 直接使用加密工具

```java
// 使用默认AES-GCM算法
String encrypted = EncryptionUtil.encrypt("敏感数据");
String decrypted = EncryptionUtil.decrypt(encrypted);

// 指定算法类型
String sm2Encrypted = EncryptionUtil.encrypt("敏感数据", EncryptField.AlgorithmType.SM2);
String sm2Decrypted = EncryptionUtil.decrypt(sm2Encrypted, EncryptField.AlgorithmType.SM2);

// 验证加密功能
boolean isValid = EncryptionUtil.verify("测试数据", EncryptField.AlgorithmType.AES_GCM);
```

### SM2密钥对生成

```java
// 生成SM2密钥对
SM2Util.KeyPair keyPair = SM2Util.generateKeyPair();
String publicKey = keyPair.getPublicKey();
String privateKey = keyPair.getPrivateKey();

// 使用指定密钥加密
String encrypted = SM2Util.encrypt("数据", publicKey);
String decrypted = SM2Util.decrypt(encrypted, privateKey);
```

## 🏗️ 架构设计

```
dyt-backend-encryption/
├── annotation/          # 注解定义
│   └── EncryptField.java
├── config/             # 配置类
│   └── EncryptionProperties.java
├── converter/          # JPA转换器
│   └── EncryptConverter.java
├── util/              # 工具类
│   ├── AESGCMUtil.java
│   ├── SM2Util.java
│   └── EncryptionUtil.java
└── BackendEncryptionAutoConfiguration.java
```

## 🔒 安全注意事项

1. **密钥管理**：生产环境必须使用安全的密钥管理方案
2. **密钥轮换**：定期更换加密密钥
3. **权限控制**：限制对加密配置的访问权限
4. **审计日志**：记录加密操作的审计日志
5. **备份策略**：制定加密数据的备份和恢复策略

## 📝 最佳实践

1. **渐进式迁移**：使用影子字段策略实现零停机迁移
2. **性能优化**：合理配置缓存和批处理
3. **监控告警**：启用性能监控和异常告警
4. **测试验证**：充分测试加密解密功能
5. **文档维护**：及时更新配置文档

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个模块。

## 🔄 数据迁移指南

### 影子字段迁移步骤

1. **准备阶段**：添加影子字段到数据库表
```sql
ALTER TABLE user ADD COLUMN email_encrypted VARCHAR(1000) COMMENT '邮箱加密字段';
```

2. **迁移阶段**：配置PLAINTEXT_PRIORITY策略
```yaml
backend:
  encryption:
    strategies:
      user-email:
        strategy: PLAINTEXT_PRIORITY
        shadow-field: "email_encrypted"
```

3. **验证阶段**：切换到SHADOW_PRIORITY策略
```yaml
backend:
  encryption:
    strategies:
      user-email:
        strategy: SHADOW_PRIORITY
        shadow-field: "email_encrypted"
```

4. **完成阶段**：切换到SHADOW_ONLY策略
```yaml
backend:
  encryption:
    strategies:
      user-email:
        strategy: SHADOW_ONLY
        shadow-field: "email_encrypted"
```

## 🧪 测试验证

运行测试验证加密功能：

```bash
mvn test -Dtest=EncryptionUtilTest
```

## 📊 性能监控

启用性能监控后，可以通过日志查看加密操作的性能指标：

```yaml
backend:
  encryption:
    performance-monitor-enabled: true
    debug-enabled: true
```

## 📄 许可证

本项目采用内部许可证，仅供DYT项目内部使用。
