package backend.security.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-24 16:25
 */
@Data
public class TenantUserStructureTree implements Serializable {
    public TenantUserStructureTree() {
        this.superAdmin = false;
        this.tenants = new HashMap<>();
    }

    public TenantUserStructureTree(Long userId) {
        this();
        this.userId = userId;
    }

    private Long userId;
    /**
     * 是否为超管
     */
    private Boolean superAdmin;
    private Map<Long, TenantLayer> tenants;

    /**
     * 判断当前用户是否有权限访问。
     * 参数如果为空，顺序必须由大到小传递，即：tenantId -> hospitalId -> hospitalAreaId -> departmentId。
     * 规则明细：
     * 1.如果tenantId为空，则表示判断是否为超管。
     * 2.如果tenantId不为空，hospitalId为空，则表示判断是否为租户管理员。
     * 3.如果tenantId不为空，hospitalId不为空，hospitalAreaId为空，则表示判断是否为医院管理员。
     * 4.如果tenantId不为空，hospitalId不为空，hospitalAreaId不为空，departmentId为空，则表示判断是否为院区管理员。
     * 5.如果tenantId不为空，hospitalId不为空，hospitalAreaId不为空，departmentId不为空，则表示判断是否为科室成员。
     *
     * @param tenantId       租户ID
     * @param hospitalId     医院ID
     * @param hospitalAreaId 院区ID
     * @param departmentId   科室ID
     * @return 是否有权限
     */
    @JsonIgnore
    public boolean hasPrivilege(Long tenantId, Long hospitalId, Long hospitalAreaId, Long departmentId) {
        if (isSuperAdmin()) {
            return true;
        }
        if (null == tenantId) {
            return false;
        }
        TenantLayer tenantLayer = tenants.get(tenantId);
        if (null == tenantLayer) {
            return false;
        }
        if (tenantLayer.getTenantAdmin()) {
            return true;
        }
        if (null == hospitalId) {
            return false;
        }
        HospitalLayer hospitalLayer = tenantLayer.getHospitals().get(hospitalId);
        if (null == hospitalLayer) {
            return false;
        }
        if (hospitalLayer.getHospitalAdmin()) {
            return true;
        }
        if (null == hospitalAreaId) {
            return false;
        }
        HospitalAreaLayer hospitalAreaLayer = hospitalLayer.getHospitalAreas().get(hospitalAreaId);
        if (null == hospitalAreaLayer) {
            return false;
        }
        if (hospitalAreaLayer.getHospitalAreaAdmin()) {
            return true;
        }
        if (null == departmentId) {
            return false;
        }
        DepartmentLayer departmentLayer = hospitalAreaLayer.getDepartments().get(departmentId);
        if (null == departmentLayer) {
            return false;
        }
        return departmentLayer.getDepartmentAdmin();
    }

    public boolean isSuperAdmin() {
        return superAdmin;
    }

    @JsonIgnore
    public TenantLayer addTenant(Long tenantId, Boolean tenantAdmin) {
        TenantLayer tenantLayer = getOrCreateTenantLayer(tenantId);
        /* true不可被覆盖 */
        if (Boolean.TRUE.equals(tenantLayer.getTenantAdmin())) {
            return tenantLayer;
        }
        tenantLayer.setTenantAdmin(tenantAdmin);
        return tenantLayer;
    }

    @JsonIgnore
    public HospitalLayer addHospital(Long tenantId, Long hospitalId, Boolean hospitalAdmin) {
        getOrCreateTenantLayer(tenantId);

        HospitalLayer hospitalLayer = getOrCreateHospitalLayer(tenantId, hospitalId);
        /* true不可被覆盖 */
        if (Boolean.TRUE.equals(hospitalLayer.getHospitalAdmin())) {
            return hospitalLayer;
        }
        hospitalLayer.setHospitalAdmin(hospitalAdmin);
        return hospitalLayer;
    }

    @JsonIgnore
    public HospitalAreaLayer addHospitalArea(Long tenantId, Long hospitalId, Long hospitalAreaId, Boolean hospitalAreaAdmin) {
        getOrCreateTenantLayer(tenantId);
        getOrCreateHospitalLayer(tenantId, hospitalId);

        HospitalAreaLayer hospitalAreaLayer = getOrCreateHospitalAreaLayer(tenantId, hospitalId, hospitalAreaId);
        /* true不可被覆盖 */
        if (Boolean.TRUE.equals(hospitalAreaLayer.getHospitalAreaAdmin())) {
            return hospitalAreaLayer;
        }
        hospitalAreaLayer.setHospitalAreaAdmin(hospitalAreaAdmin);
        return hospitalAreaLayer;
    }

    @JsonIgnore
    @SuppressWarnings("UnusedReturnValue")
    public DepartmentLayer addDepartment(Long tenantId, Long hospitalId, Long hospitalAreaId, Long departmentId, Boolean departmentAdmin) {
        getOrCreateTenantLayer(tenantId);
        getOrCreateHospitalLayer(tenantId, hospitalId);
        getOrCreateHospitalAreaLayer(tenantId, hospitalId, hospitalAreaId);

        return getOrCreateDepartmentLayer(tenantId, hospitalId, hospitalAreaId, departmentId, departmentAdmin);
    }

    @JsonIgnore
    private TenantLayer getOrCreateTenantLayer(Long tenantId) {
        TenantLayer tenantLayer = tenants.get(tenantId);
        if (null == tenantLayer) {
            tenantLayer = new TenantLayer(tenantId, false);
            this.tenants.put(tenantId, tenantLayer);
        }
        return tenantLayer;
    }

    @JsonIgnore
    private HospitalLayer getOrCreateHospitalLayer(Long tenantId, Long hospitalId) {
        TenantLayer tenantLayer = getOrCreateTenantLayer(tenantId);

        HospitalLayer hospitalLayer = tenantLayer.getHospitals().get(hospitalId);
        if (null == hospitalLayer) {
            hospitalLayer = new HospitalLayer(hospitalId, false);
            tenantLayer.getHospitals().put(hospitalId, hospitalLayer);
        }
        return hospitalLayer;
    }

    @JsonIgnore
    private HospitalAreaLayer getOrCreateHospitalAreaLayer(Long tenantId, Long hospitalId, Long hospitalAreaId) {
        TenantLayer tenantLayer = getOrCreateTenantLayer(tenantId);

        HospitalLayer hospitalLayer = getOrCreateHospitalLayer(tenantId, hospitalId);

        HospitalAreaLayer hospitalAreaLayer = hospitalLayer.getHospitalAreas().get(hospitalAreaId);
        if (null == hospitalAreaLayer) {
            hospitalAreaLayer = new HospitalAreaLayer(hospitalAreaId, false);
            hospitalLayer.getHospitalAreas().put(hospitalAreaId, hospitalAreaLayer);
        }
        return hospitalAreaLayer;
    }

    @JsonIgnore
    private DepartmentLayer getOrCreateDepartmentLayer(Long tenantId, Long hospitalId, Long hospitalAreaId, Long departmentId, Boolean departmentAdmin) {
        getOrCreateTenantLayer(tenantId);
        getOrCreateHospitalLayer(tenantId, hospitalId);

        HospitalAreaLayer hospitalAreaLayer = getOrCreateHospitalAreaLayer(tenantId, hospitalId, hospitalAreaId);

        DepartmentLayer departmentLayer = hospitalAreaLayer.getDepartments().get(departmentId);
        if (null == departmentLayer) {
            departmentLayer = new DepartmentLayer(departmentId, departmentAdmin);
            hospitalAreaLayer.getDepartments().put(departmentId, departmentLayer);
        }
        return departmentLayer;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class TenantLayer extends UserPrivilegeLayer {
        public TenantLayer() {
            this.tenantAdmin = false;
            this.hospitals = new HashMap<>();
        }

        public TenantLayer(Long layerId, Boolean tenantAdmin) {
            this();
            this.layerId = layerId;
            this.tenantAdmin = tenantAdmin;
        }

        /**
         * 是否为租户管理员
         */
        public Boolean tenantAdmin;
        private Map<Long, HospitalLayer> hospitals;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class HospitalLayer extends UserPrivilegeLayer {
        public HospitalLayer() {
            this.hospitalAdmin = false;
            this.hospitalAreas = new HashMap<>();
        }

        public HospitalLayer(Long layerId, Boolean hospitalAdmin) {
            this();
            this.layerId = layerId;
            this.hospitalAdmin = hospitalAdmin;
        }

        /**
         * 是否为医院管理员
         */
        public Boolean hospitalAdmin;
        private Map<Long, HospitalAreaLayer> hospitalAreas;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class HospitalAreaLayer extends UserPrivilegeLayer {
        public HospitalAreaLayer() {
            this.hospitalAreaAdmin = false;
            this.departments = new HashMap<>();
        }

        public HospitalAreaLayer(Long layerId, Boolean hospitalAreaAdmin) {
            this();
            this.layerId = layerId;
            this.hospitalAreaAdmin = hospitalAreaAdmin;
        }

        /**
         * 是否为医院区域管理员
         */
        public Boolean hospitalAreaAdmin;
        private Map<Long, DepartmentLayer> departments;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class DepartmentLayer extends UserPrivilegeLayer {
        public DepartmentLayer() {
            this.departmentAdmin = false;
        }

        public DepartmentLayer(Long layerId, Boolean departmentAdmin) {
            this.layerId = layerId;
            this.departmentAdmin = departmentAdmin;
        }

        /**
         * 是否为科室管理员
         */
        private Boolean departmentAdmin;
    }

    /**
     * 用户权限层级
     */
    @Data
    public static class UserPrivilegeLayer {
        /**
         * 当前层级ID
         */
        protected Long layerId;
    }
}
