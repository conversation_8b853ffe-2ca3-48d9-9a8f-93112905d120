create table t_hospital_custom_business_setting
(
    id                  bigint auto_increment comment '全局唯一标识'
        primary key,
    function_id         bigint                                   null comment '功能 ID',
    tenant_id           bigint                                   not null comment '租户 ID',
    hospital_id         bigint                                   not null comment '医院 ID',
    hospital_area_id    bigint                                   not null comment '院区 ID',
    hospital_code       varchar(20)                              null comment '医院编码',
    name                varchar(50)                              not null comment '名称',
    logo                varchar(250)                             null comment 'logo',
    wechat_open_path    varchar(250)                             null comment '微信公众号路径',
    mini_program_app_id varchar(30)                              null comment '小程序 app id',
    mini_program_path   varchar(250)                             null comment '小程序路径',
    create_time         datetime(3) default CURRENT_TIMESTAMP(3) not null,
    update_time         datetime(3)                              null on update CURRENT_TIMESTAMP(3),
    constraint uidx_limit_custom_business_name
        unique (tenant_id, hospital_id, hospital_area_id, name)
)
    comment '医院自定义业务设置';