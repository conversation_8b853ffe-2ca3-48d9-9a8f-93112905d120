package com.ynhdkc.tenant.service.backend.impl;

import backend.common.domain.tenant.constant.DictFileClusterStatus;
import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.dao.SearchManagementQuery;
import com.ynhdkc.tenant.dao.SearchManagementRepository;
import com.ynhdkc.tenant.entity.DictFile;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.SearchManagementService;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import com.ynhdkc.tenant.tool.convert.SearchManagementConverter;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/9/12 15:56
 */
@RequiredArgsConstructor
public abstract class BaseSearchManagementServiceImpl implements SearchManagementService {
    protected final SearchManagementQuery searchManagementQuery;
    protected final SearchManagementRepository searchManagementRepository;

    protected final PageVoConvert pageVoConvert;

    @Override
    public DictFileVo getDictDetail(Long dictId) {
        DictFile dictFile = searchManagementQuery.queryById(dictId);
        if (null == dictId) {
            throw new BizException(HttpStatus.NOT_FOUND, "字典文件不存在");
        }
        return SearchManagementConverter.toDictFileVo(dictFile);
    }

    @Override
    public DictFileQueryRespDto queryDict(DictFileQueryReqDto request) {
        try (Page<DictFile> page = searchManagementQuery.pageQueryDictFile(new SearchManagementQuery.DictFileQueryOption(request.getCurrentPage(), request.getPageSize())
                .setName(request.getName())
                .setType(request.getType())
                .setStatus(request.getStatus()))) {
            return pageVoConvert.toPageVo(page, DictFileQueryRespDto.class, SearchManagementConverter::toDictFileVo);
        }
    }

    @Override
    public DictFileVo createDict(DictFileCreateReqDto request) {
        DictFile dictFile = searchManagementQuery.queryByNameAndType(request.getName(), request.getType());
        if (null != dictFile) {
            throw new BizException(HttpStatus.BAD_REQUEST, "字典文件已存在");
        }
        dictFile = SearchManagementConverter.toEntity(request);
        searchManagementRepository.createDictFile(dictFile);
        return SearchManagementConverter.toDictFileVo(dictFile);
    }

    @Override
    public DictFileVo updateDict(DictFileUpdateReqDto request) {
        DictFile dictFile = searchManagementQuery.queryById(request.getId());
        if (null == dictFile) {
            throw new BizException(HttpStatus.NOT_FOUND, "字典文件不存在");
        }

        if (null != request.getName() || null != request.getType()) {
            DictFile checkDictFile = searchManagementQuery.queryByNameAndType(request.getName(), request.getType());
            if (null != checkDictFile && !checkDictFile.getId().equals(request.getId())) {
                throw new BizException(HttpStatus.BAD_REQUEST, "修改后字典文件已存在");
            }
        }

        SearchManagementConverter.updateIfNotNull(dictFile, request);
        dictFile.setClusterStatus(DictFileClusterStatus.NOT_SYNC.getCode());
        searchManagementRepository.updateDictFile(dictFile);

        return SearchManagementConverter.toDictFileVo(dictFile);
    }

    @Override
    public BaseOperationResponse deleteDict(Long dictId) {
        DictFile dictFile = searchManagementQuery.queryById(dictId);
        if (null == dictFile) {
            throw new BizException(HttpStatus.NOT_FOUND, "字典文件不存在");
        }
        searchManagementRepository.deleteDictFile(dictId);
        return new BaseOperationResponse("删除成功");
    }
}
