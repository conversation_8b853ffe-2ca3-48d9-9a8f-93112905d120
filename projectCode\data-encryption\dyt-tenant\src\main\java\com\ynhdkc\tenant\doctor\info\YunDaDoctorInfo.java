package com.ynhdkc.tenant.doctor.info;

import backend.common.enums.HospitalCode;
import com.ynhdkc.tenant.client.model.DepartmentDoctorListEnvResponse;
import com.ynhdkc.tenant.config.yunda.YunDaConfig;
import com.ynhdkc.tenant.dao.DoctorQuery;
import com.ynhdkc.tenant.dao.DoctorRepository;
import com.ynhdkc.tenant.entity.DictLabel;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.service.backend.DictLabelService;
import com.ynhdkc.tenant.tool.DoctorHeadImageHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor
public class YunDaDoctorInfo implements IDoctorInfo {

    private final static String DOCTOR_RANK_DICT_TYPE = "doctor_title";
    private final DoctorQuery doctorQuery;
    private final DoctorRepository doctorRepository;
    private final DoctorHeadImageHandler doctorHeadImageHandler;
    private final DictLabelService dictLabelService;
    private final YunDaConfig yunDaConfig;

    @Override
    public List<String> getHospitalCode() {
        return Arrays.asList(
                HospitalCode.KUNMING_MU_FIRST_AFFILIATED_HOSPITAL.getCode(),
                HospitalCode.KUNMING_MU_FIRST_AFFILIATED_CG_CAMPUS_HOSPITAL.getCode()
        );
    }

    @Override
    public void resolve(@NonNull DepartmentDoctorListEnvResponse response) {
        List<DepartmentDoctorListEnvResponse.Payload.Result> result = response.getPayload().getResult();
        if (CollectionUtils.isEmpty(result)) {
            log.warn("resolve doctor info failed, response: {}", response);
            return;
        }

        result.stream().filter(item -> StringUtils.hasText(item.getDoctorProfilePhoto())).forEach(this::updateDoctorProfilePhoto);
    }

    private void updateDoctorProfilePhoto(DepartmentDoctorListEnvResponse.Payload.Result item) {
        if (item.getDoctorProfilePhoto().contains("http://192.168.3.93:88/UploadFiles")) {
            String avatarUrl = item.getDoctorProfilePhoto().replace("http://192.168.3.93:88/UploadFiles", yunDaConfig.getAvatar().getUrl());
            item.setDoctorProfilePhoto(avatarUrl);
        }
    }

    @Override
    public void updateDoctorInfo(String hospitalCode, DepartmentDoctorListEnvResponse body) {
        List<Doctor> doctors = doctorQuery.queryEnableDoctorsBy(hospitalCode);
        if (CollectionUtils.isEmpty(doctors)) {
            return;
        }

        List<DictLabel> dictLabels = dictLabelService.getDictLabelBy(DOCTOR_RANK_DICT_TYPE);
        body.getPayload().getResult().forEach(result -> {
            List<Doctor> filterDoctorsByDoctorCode = doctors.stream().filter(d -> d.getThrdpartDoctorCode().equals(result.getDoctor().getThrdpartDoctorCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterDoctorsByDoctorCode)) {
                return;
            }

            filterDoctorsByDoctorCode = filterDoctorsByDoctorCode.stream().filter(q -> StringUtils.hasText(q.getThrdpartDoctorCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterDoctorsByDoctorCode)) {
                return;
            }

            String doctorImageUrl = doctorHeadImageHandler.getDoctorImageUrl(result.getDoctorProfilePhoto(), hospitalCode, result.getDoctor().getDepartmentCode(), result.getDoctor().getThrdpartDoctorCode());
            for (Doctor doctor : filterDoctorsByDoctorCode) {
                if (StringUtils.hasText(result.getDoctor().getIntroduction())) {
                    doctor.setIntroduction(result.getDoctor().getIntroduction());
                }
                if (StringUtils.hasText(result.getDoctor().getSpeciality())) {
                    doctor.setSpeciality(result.getDoctor().getSpeciality());
                }
                if (result.getRawParameters() != null) {
                    String levelCode = ((LinkedHashMap<?, ?>) result.getRawParameters()).get("levlCode").toString();
                    String title = YunDaLevelEnum.getTitleByCode(levelCode);
                    if (StringUtils.hasText(title)) {
                        Optional<DictLabel> dictLabelOptional = dictLabels.stream().filter(q -> q.getDictLabel().equals(title)).findFirst();
                        if (dictLabelOptional.isPresent()) {
                            DictLabel dictLabel = dictLabelOptional.get();
                            doctor.setRankDictType(dictLabel.getDictType());
                            doctor.setRankDictValue(dictLabel.getDictValue());
                            doctor.setRankDictLabel(dictLabel.getDictLabel());
                        }
                    }
                }
                doctor.setHeadImgUrl(doctorImageUrl);
                doctor.setUpdateTime(new Date());
                doctorRepository.update(doctor);
            }
        });
    }
}
