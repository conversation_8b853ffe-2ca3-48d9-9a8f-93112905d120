package com.ynhdkc.tenant.client;

import com.ynhdkc.tenant.client.model.OssDictFileUploadRequest;
import com.ynhdkc.tenant.client.model.OssDictFileUploadResponse;
import com.ynhdkc.tenant.dto.OssFileUploadResponse;
import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2023/8/3 13:43:31
 */
@FeignClient(name = "${feign.name.data}", path = "${feign.path.data}", configuration = DataClient.DataClientConfiguration.class)
public interface DataClient {

    @PostMapping(value = "/oss/file/upload?server_name={serverName}&bucket_name={bucketName}&path={path}", headers = "Content-Type: multipart/form-data", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ResponseEntity<OssFileUploadResponse> uploadFile(@PathVariable("serverName") String serverName, @PathVariable("bucketName") String bucketName, @PathVariable("path") String path, @RequestPart("file") MultipartFile file);

    @PostMapping(value = "/oss/dic/upload")
    OssDictFileUploadResponse uploadDicFile(@RequestBody OssDictFileUploadRequest request);

    @Configuration
    class DataClientConfiguration {
        @Bean
        public Request.Options feignOptions() {
            return new Request.Options(10, TimeUnit.SECONDS, 60, TimeUnit.SECONDS, true);
        }
    }
}
