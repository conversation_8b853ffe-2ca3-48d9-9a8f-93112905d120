package com.ynhdkc.tenant.util;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.integration.support.locks.ExpirableLockRegistry;
import org.springframework.stereotype.Component;

import java.util.concurrent.locks.Lock;

@Component
@RequiredArgsConstructor
@Slf4j
public class LockUtil {

    private final ExpirableLockRegistry expirableLockRegistry;

    public void withLock(String hospitalCode, String depCode, Runnable callback) {
        Lock lock = getLock(hospitalCode, depCode);
        lock.lock();
        try {
            callback.run();
        } catch (Exception e) {
            log.error("withLock error", e);
        } finally {
            lock.unlock();
        }
    }

    private Lock getLock(String hospitalCode, String departmentCode) {
        return expirableLockRegistry.obtain("CREATE_DOCTOR_LOCK:" + hospitalCode + ":" + departmentCode);
    }

}
