package backend.common.util;

import backend.common.constants.HisOperation;

public class RedisKeyUtils {

    private RedisKeyUtils() {
    }

    public static String getAuthenticateKey(String hospitalCode) {
        return String.format("his:authenticate:hospital-code:%s", hospitalCode);
    }

    public static String getAuthenticateExpirationKey(String hospitalCode) {
        return String.format("his:authenticate:expiration:hospital-code:%s", hospitalCode);
    }

    public static String getServiceDataKey(String hospitalCode, HisOperation hisOperation, String serviceTag) {
        return String.format("his:service:data:hospital-code:%s:his-operation:%s:service-tag:%s", hospitalCode,
                hisOperation.getValue(), serviceTag);
    }

    public static String getServiceDataKey(String hospitalCode, HisOperation hisOperation, String serviceTag, String departmentCode) {
        return String.format("his:service:data:hospital-code:%s:his-operation:%s:service-tag:%s:department-code:%s", hospitalCode,
                hisOperation.getValue(), serviceTag, departmentCode);
    }

    public static String getDoctorScheduleKey(String hospitalCode, String departmentCode, String doctorCode, String startDate) {
        String rawKey = String.format("%s_%s_%s_%s", hospitalCode, departmentCode,
                doctorCode, startDate);

        return getBaseHisOperationKey(rawKey, HisOperation.DOCTOR_SCHEDULE_LIST);
    }

    public static String getBaseHisOperationKey(String rawKey, HisOperation hisOperation) {
        return String.format("his:service:data:his-operation:%s:key:%s", hisOperation.getValue(), rawKey);
    }
}
