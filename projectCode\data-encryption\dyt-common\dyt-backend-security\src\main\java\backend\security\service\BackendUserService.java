package backend.security.service;

import backend.common.exception.BizException;
import backend.security.oauth2.BackendOAuth2User;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/6/30 16:49
 */
public interface BackendUserService {
    /**
     * 获取当前用户ID，获取失败时会抛出异常。
     *
     * @return 用户ID
     * @throws backend.common.exception.BizException 获取不到JWT用户
     */
    default Long getCurrentUserId() {
        Long userId = BackendOAuth2User.getUserIdFromJwt();
        if (null == userId) {
            throw new BizException(HttpStatus.FORBIDDEN, "用户信息读取错误");
        }
        return userId;
    }
}
