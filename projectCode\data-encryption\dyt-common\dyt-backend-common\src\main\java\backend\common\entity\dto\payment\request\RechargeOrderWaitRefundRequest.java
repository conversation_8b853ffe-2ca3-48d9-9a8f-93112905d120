package backend.common.entity.dto.payment.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class RechargeOrderWaitRefundRequest {

    @JsonProperty("order_number")
    private String orderNumber;

    @JsonProperty("refund_order_number")
    private String refundOrderNumber;


    @JsonProperty("total_fee")
    private BigDecimal totalFee;

    /**
     * 退款原因
     */
    @JsonProperty("reason")
    private String reason;
}
