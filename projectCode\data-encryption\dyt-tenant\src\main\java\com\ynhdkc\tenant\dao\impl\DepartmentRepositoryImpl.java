package com.ynhdkc.tenant.dao.impl;

import com.ynhdkc.tenant.dao.DepartmentRepository;
import com.ynhdkc.tenant.dao.mapper.DepartmentMapper;
import com.ynhdkc.tenant.dao.mapper.DoctorMapper;
import com.ynhdkc.tenant.dao.mapper.HospitalAreaPositionMapper;
import com.ynhdkc.tenant.dao.mapper.TenantUserStructureMapper;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.entity.TenantUserStructure;
import lombok.RequiredArgsConstructor;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-29 21:48
 */
@Repository
@RequiredArgsConstructor
public class DepartmentRepositoryImpl implements DepartmentRepository {
    private final DepartmentMapper departmentMapper;
    private final DoctorMapper doctorMapper;
    private final TenantUserStructureMapper tenantUserStructureMapper;
    private final HospitalAreaPositionMapper departmentPositionMapper;

    /**
     * 创建科室
     *
     * @param department 科室
     * @return 科室 id
     */
    @Override
    public Long create(Department department) {
        departmentMapper.insertSelective(department);
        if (!StringUtils.hasText(department.getThrdpartDepCode())) {
            department.setThrdpartDepCode(department.getId().toString());
            departmentMapper.updateByPrimaryKeySelective(department);
        }
        return department.getId();
    }

    @Override
    public void update(Department department) {
        departmentMapper.updateByPrimaryKeySelective(department);
    }

    @Override
    public void delete(Long departmentId) {
        departmentMapper.deleteByPrimaryKey(departmentId);
        /* 级联删除 */
        doctorMapper.deleteByExample2(Doctor.class, sql -> sql.andEqualTo(Doctor::getDepartmentId, departmentId));
        tenantUserStructureMapper.deleteByExample2(TenantUserStructure.class, sql -> sql.andEqualTo(TenantUserStructure::getDepartmentId, departmentId));
    }

    @Override
    public Optional<Department> queryById(Long departmentId) {
        return Optional.ofNullable(departmentMapper.selectByPrimaryKey(departmentId));
    }

    @Override
    public List<Department> listByHospitalCodeAndDepartmentCodeIn(@Nullable List<String> hospitalCodes, @Nullable List<String> departmentCodes) {
        return departmentMapper.selectByExample2(Department.class, consumer -> {
                    if (!CollectionUtils.isEmpty(hospitalCodes)) {
                        consumer.andIn(Department::getHospitalCode, hospitalCodes);
                    }
                    if (!CollectionUtils.isEmpty(departmentCodes)) {
                        consumer.andIn(Department::getThrdpartDepCode, departmentCodes);
                    }
                }
        );
    }

    @Override
    public Department queryByHospitalCodeAndThrdpartDepCode(String hospitalCode, String departmentCode) {
        return departmentMapper.selectOneByExample2(Department.class, sql -> sql.andEqualTo(Department::getHospitalCode, hospitalCode)
                .andEqualTo(Department::getThrdpartDepCode, departmentCode));
    }

    @Override
    public void disableAll(String hospitalCode) {
        List<Department> departments = departmentMapper.selectByExample2(Department.class, sql -> sql.andEqualTo(Department::getHospitalCode, hospitalCode));
        if (!CollectionUtils.isEmpty(departments)) {
            departments.forEach(department -> {
                department.setEnabled(false);
                departmentMapper.updateByPrimaryKeySelective(department);
            });
        }

    }

    @Override
    public void disableBy(Long id) {
        Department department = departmentMapper.selectByPrimaryKey(id);
        if (department != null) {
            department.setEnabled(true);
            departmentMapper.updateByPrimaryKeySelective(department);
        }
    }
}
