package backend.security.oplog.component;

import backend.common.util.MessageUtil;
import backend.security.method.SecurityManifest;
import backend.security.oauth2.BackendOAuth2User;
import backend.security.oplog.DytSecurityRequired;
import backend.security.oplog.dao.repository.OpLogKafkaRepository;
import backend.security.oplog.dto.OpLogModel;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.CodeSignature;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.HandlerMapping;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
@Aspect
public class OpLogComponent {

    private final OpLogKafkaRepository opLogKafkaRepository;

    private final SecurityManifest securityManifest;

    public OpLogComponent(OpLogKafkaRepository opLogKafkaRepository, SecurityManifest securityManifest) {
        this.opLogKafkaRepository = opLogKafkaRepository;
        this.securityManifest = securityManifest;
    }

    @Pointcut("@annotation(backend.security.oplog.DytSecurityRequired)")
    public void needWriteOpLog() {
    }

    @Around("needWriteOpLog()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature sign = (MethodSignature) joinPoint.getSignature();
        Map<String, Object> parameterMap = getNameAndValue(joinPoint);
        DytSecurityRequired annotation = sign.getMethod().getAnnotation(DytSecurityRequired.class);
        if (annotation == null) {
            return joinPoint.proceed();
        }

        boolean needOpLog = annotation.needOpLog();
        String[] opCodeList = annotation.value();
        BackendOAuth2User iotOAuth2User = BackendOAuth2User.tryGetCurrent();
        Object result = null;
        final RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null && iotOAuth2User != null &&
//                        !iotOAuth2User.isRPCAccess() &&
                        needOpLog &&
                        !ObjectUtils.isEmpty(parameterMap)
                        && !ObjectUtils.isEmpty(opCodeList)
        ) {
            Throwable error = null;
            try {
                result = joinPoint.proceed();
            } catch (Throwable throwable) {
                error = throwable;
            }
            try {
                OpLogModel opLogModel = new OpLogModel();
                String name = parseName(opCodeList);
                opLogModel.setOpCode(name);
//                String desc = securityManifest.getAuthorityMap().get(name);
//                opLogModel.setOpCodeCn(desc);
                String moduleName = parseModuleName(name);
                opLogModel.setModuleName(moduleName);
                HttpServletRequest request = (HttpServletRequest) requestAttributes.resolveReference(RequestAttributes.REFERENCE_REQUEST);
                if (request != null) {
                    String path = (String) request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE);
                    final String method = request.getMethod();
                    opLogModel.setUrl(path);
                    opLogModel.setMethod(method);
                }

                opLogModel.setTenantId(iotOAuth2User.getTenantId());
                opLogModel.setUserId(iotOAuth2User.getUserId());
                opLogModel.setUserName(ObjectUtils.isEmpty(iotOAuth2User.getNickName()) ? "" : iotOAuth2User.getNickName());
                if (error != null) {
                    opLogModel.setError(true);
                    opLogModel.setException(error.getClass().getName());
                }
                opLogModel.setRawMessage(MessageUtil.object2JSONString(parameterMap));

                log.info("opLogModel is {}", MessageUtil.object2JSONString(opLogModel));

                opLogKafkaRepository.createOne(opLogModel);

            } catch (Exception e) {
                log.error("OpLog save error", e);
            } finally {
                if (error != null) {
                    throw error;
                }
            }
        } else {
            result = joinPoint.proceed();
        }
        return result;
    }

    private Map<String, Object> getNameAndValue(ProceedingJoinPoint joinPoint) {
        Map<String, Object> param = new HashMap<>();
        Object[] paramValues = joinPoint.getArgs();
        String[] paramNames = ((CodeSignature) joinPoint.getSignature()).getParameterNames();
        for (int i = 0; i < paramNames.length; i++) {
            param.put(paramNames[i], paramValues[i] instanceof MultipartFile ? ((MultipartFile) paramValues[i]).getOriginalFilename() : paramValues[i]);
        }
        return param;
    }

    private String parseName(String[] nameList) {
        return String.join(",", nameList);
    }

    private String parseModuleName(String name) {
        if (ObjectUtils.isEmpty(name) || !name.contains(":")) {
            return "";
        }
        return name.substring(0, name.indexOf(":"));
    }

    private String parseMethodName(String name) {
        if (ObjectUtils.isEmpty(name) || !name.contains(":")) {
            return "";
        }
        return name.substring(name.lastIndexOf(":") + 1).toUpperCase();
    }

    private Long parseTenantId(Map<String, Object> parameterMap, String tenantIdExpr) {
        EvaluationContext evaluationContext = new StandardEvaluationContext();
        for (Map.Entry<String, Object> entry : parameterMap.entrySet()) {
            evaluationContext.setVariable(entry.getKey(), entry.getValue());
        }
        ExpressionParser parser = new SpelExpressionParser();
        parser.parseExpression(tenantIdExpr);

        return parser.parseExpression(tenantIdExpr).getValue(evaluationContext, Long.class);
    }
}
