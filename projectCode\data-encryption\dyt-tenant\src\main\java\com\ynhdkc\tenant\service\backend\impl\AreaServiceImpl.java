package com.ynhdkc.tenant.service.backend.impl;

import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import backend.common.util.JsonUtil;
import backend.security.service.BackendTenantUserService;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.dao.AreaQuery;
import com.ynhdkc.tenant.dao.AreaRepository;
import com.ynhdkc.tenant.entity.Area;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.AreaService;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-04 11:58
 */
@Repository
@RequiredArgsConstructor
public class AreaServiceImpl implements AreaService {
    private final AreaQuery areaQuery;
    private final AreaRepository areaRepository;

    private final BackendTenantUserService backendTenantUserService;

    private final PageVoConvert pageVoConvert;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AreaVo create(AreaCreateReqDto request) {
        Area area = AreaService.toArea(request);
        areaRepository.create(area);
        return AreaService.toAreaVo(area);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AreaVo update(Long areaId, AreaUpdateReqDto request) {
        Area area = areaQuery.queryAreaById(areaId);
        if (area == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "区域不存在");
        }
        updateIfNotNull(area, request);
        areaRepository.update(area);
        return AreaService.toAreaVo(area);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseOperationResponse delete(Long areaId) {
        Area area = areaQuery.queryAreaById(areaId);
        if (area == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "区域不存在");
        }
        areaRepository.delete(areaId);
        return new BaseOperationResponse("删除成功");
    }

    @Override
    public AreaVo getDetail(Long areaId) {
        Area area = areaQuery.queryAreaById(areaId);
        if (area == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "区域不存在");
        }
        return AreaService.toAreaVo(area);
    }

    @Override
    public AreaPageVo query(AreaQueryReqDto request) {
        boolean isSuperAdmin = backendTenantUserService.isSuperAdmin();
        if (null == request.getTenantId() && !isSuperAdmin) {
            throw new BizException(HttpStatus.FORBIDDEN, "必须指定租户ID");
        }
        AreaQuery.AreaQueryOption option = new AreaQuery.AreaQueryOption(request.getCurrentPage(), request.getPageSize())
                .setTenantId(request.getTenantId())
                .setHospitalId(request.getHospitalId())
                .setHospitalAreaId(request.getHospitalAreaId())
                .setBuildingId(request.getBuildingId())
                .setFloorId(request.getFloorId())
                .setName(request.getName())
                .setStatus(request.getStatus());
        if (!isSuperAdmin) {
            option.setIncludeTenantIds(backendTenantUserService.getCurrentUserTenantReadRange(true));
            option.setIncludeHospitalIds(backendTenantUserService.getCurrentUserHospitalReadRange(true));
            option.setIncludeHospitalAreaIds(backendTenantUserService.getCurrentUserHospitalAreaReadRange(true));
        }
        try (Page<Area> buildingPage = areaQuery.pageQuery(option)) {
            return pageVoConvert.toPageVo(buildingPage, AreaPageVo.class, AreaService::toAreaVo);
        }
    }

    private static void updateIfNotNull(Area entity, AreaUpdateReqDto dto) {
        if (null != dto.getBuildingId()) {
            entity.setBuildingId(dto.getBuildingId());
        }
        if (null != dto.getFloorId()) {
            entity.setFloorId(dto.getFloorId());
        }
        if (null != dto.getName()) {
            entity.setName(dto.getName());
        }
        if (null != dto.getStatus()) {
            entity.setStatus(dto.getStatus());
        }
        if (null != dto.getPictureUrls()) {
            entity.setPictureUrls(JsonUtil.serializeObject(dto.getPictureUrls()));
        }
        if (null != dto.getSort()) {
            entity.setSort(dto.getSort());
        }
    }
}
