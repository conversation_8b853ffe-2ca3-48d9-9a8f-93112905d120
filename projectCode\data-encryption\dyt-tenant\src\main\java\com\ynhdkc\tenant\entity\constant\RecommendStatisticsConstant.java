package com.ynhdkc.tenant.entity.constant;

import backend.common.util.RedisKeyBuilder;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/11/2 10:52
 */
public class RecommendStatisticsConstant {
    private RecommendStatisticsConstant() {
    }

    public static final String RECOMMEND_STATISTICS_KEY_PREX = new RedisKeyBuilder("dyt-tenant", "recommend")
            .nextNode("statistics")
            .nextNode("rid")
            .build();

    public static final String LAST_RECOMMEND_STATISTICS_KEY_PREX = new RedisKeyBuilder("dyt-tenant", "recommend")
            .nextNode("statistics")
            .nextNode("rid")
            .nextNode("last")
            .build();
}
