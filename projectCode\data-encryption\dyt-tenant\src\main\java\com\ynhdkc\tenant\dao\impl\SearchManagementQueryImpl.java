package com.ynhdkc.tenant.dao.impl;

import backend.common.util.MybatisUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.ynhdkc.tenant.dao.SearchManagementQuery;
import com.ynhdkc.tenant.dao.mapper.DictFileMapper;
import com.ynhdkc.tenant.entity.DictFile;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/9/12 16:29
 */
@Repository
@RequiredArgsConstructor
public class SearchManagementQueryImpl implements SearchManagementQuery {
    private final DictFileMapper dictFileMapper;

    @Override
    public DictFile queryById(Long id) {
        return dictFileMapper.selectByPrimaryKey(id);
    }

    @Override
    public DictFile queryByNameAndType(String name, Integer type) {
        return dictFileMapper.selectOneByExample2(DictFile.class, condition -> {
            condition.andEqualTo(DictFile::getName, name);
            condition.andEqualTo(DictFile::getType, type);
        });
    }

    @Override
    public Page<DictFile> pageQueryDictFile(DictFileQueryOption option) {
        try (final Page<DictFile> page = PageMethod.startPage(option.getCurrentPage(), option.getPageSize())) {
            return page.doSelectPage(() -> dictFileMapper.selectByExample2(DictFile.class, condition -> {
                if (null != option.getName()) {
                    condition.andLike(DictFile::getName, MybatisUtil.likeBoth(option.getName()));
                }
                if (null != option.getType()) {
                    condition.andEqualTo(DictFile::getType, option.getType());
                }
                if (null != option.getStatus()) {
                    condition.andEqualTo(DictFile::getStatus, option.getStatus());
                }
            }));
        }
    }
}
