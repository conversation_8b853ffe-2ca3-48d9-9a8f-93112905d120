package com.ynhdkc.tenant.tool.convert;

import com.ynhdkc.tenant.entity.HospitalAreaPosition;
import com.ynhdkc.tenant.model.HospitalAreaPositionVo;

public class HospitalAreaPositionConverter {
    private HospitalAreaPositionConverter() {
    }

    public static HospitalAreaPositionVo toVo(HospitalAreaPosition entity) {
        HospitalAreaPositionVo vo = new HospitalAreaPositionVo();
        vo.id(entity.getId());
        vo.tenantId(entity.getTenantId());
        vo.hospitalId(entity.getHospitalId());
        vo.hospitalAreaId(entity.getHospitalAreaId());
        vo.type(entity.getType());
        vo.departmentId(entity.getDepartmentId());
        vo.name(entity.getName());
        vo.buildingId(entity.getBuildingId());
        vo.buildingName(entity.getBuildingName());
        vo.floorId(entity.getFloorId());
        vo.floorName(entity.getFloorName());
        vo.areaId(entity.getAreaId());
        vo.areaName(entity.getAreaName());
        vo.setStatus(entity.getStatus());
        vo.setSort(entity.getSort());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdateTime(entity.getUpdateTime());
        vo.setTriageDeskAddress(entity.getTriageDeskAddress());
        return vo;
    }
}