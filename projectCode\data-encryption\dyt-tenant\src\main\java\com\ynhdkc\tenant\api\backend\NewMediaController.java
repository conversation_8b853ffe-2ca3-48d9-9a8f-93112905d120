package com.ynhdkc.tenant.api.backend;

import backend.common.response.BaseOperationResponse;
import backend.security.oplog.DytSecurityRequired;
import com.ynhdkc.tenant.handler.NewMediaApi;
import com.ynhdkc.tenant.model.SetLiveButtonDto;
import com.ynhdkc.tenant.service.backend.INewMediaService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Api(value = "NewMedia")
@RestController
@RequiredArgsConstructor
public class NewMediaController implements NewMediaApi {
    private final INewMediaService newMediaService;

    @Override
    @DytSecurityRequired(needOpLog = true, value = "newMedia:setLiveButton")
    public ResponseEntity<BaseOperationResponse> setLiveButton(SetLiveButtonDto setLiveButtonDto) {
        return ResponseEntity.ok(newMediaService.setLiveButton(setLiveButtonDto));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "newMedia:setMenu")
    public ResponseEntity<BaseOperationResponse> setMenu(Integer wechatMpChannel, Boolean isDefault, MultipartFile file) {
        return ResponseEntity.ok(newMediaService.setMenu(file, wechatMpChannel, isDefault));
    }
}
