package com.ynhdkc.tenant.api.backend;

import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import backend.security.method.BackendSecurityRequired;
import backend.security.oplog.DytSecurityRequired;
import backend.security.service.BackendTenantUserService;
import cn.hutool.core.lang.RegexPool;
import cn.hutool.core.util.ReUtil;
import com.ynhdkc.tenant.component.TenantUserLoginConfig;
import com.ynhdkc.tenant.handler.TenantUserApi;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.TenantUserService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-25 23:46
 */
@Api(tags = "TenantUser")
@RestController
@RequiredArgsConstructor
public class TenantUserController implements TenantUserApi {
    private final TenantUserService tenantUserService;

    private final BackendTenantUserService backendTenantUserService;

    private final TenantUserLoginConfig tenantUserLoginConfig;

    @Override
    @DytSecurityRequired(needOpLog = true, value = "tenant:user:bind")
    public ResponseEntity<BaseOperationResponse> bindTenantUser(Long id, List<UserTenantPrivilegeConfig> userTenantPrivilegeConfigList) {
        return ResponseEntity.ok(tenantUserService.bindTenant(id, userTenantPrivilegeConfigList));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "tenant:user:checkPassword")
    public ResponseEntity<CheckTenantUserPasswordResultVo> checkPassword(Long id) {
        if (backendTenantUserService.getCurrentUserId().equals(id)) {
            return ResponseEntity.ok(tenantUserService.checkPassword(id));
        }
        throw new BizException(HttpStatus.FORBIDDEN, "租户id与当前登录用户id不一致");
    }


    @Override
    @DytSecurityRequired(needOpLog = true, value = "tenant:user:create")
    public ResponseEntity<TenantUserDetailVo> create(TenantUserCreateReqDto request) {
        if (null == request.getPassword()) {
            throw new BizException(HttpStatus.BAD_REQUEST, "密码不能为空");
        }
        if (!request.getPassword().matches(tenantUserLoginConfig.getPasswordRegex())) {
            throw new BizException(HttpStatus.BAD_REQUEST, "密码强度不符合要求，必须包含大小写字母、数字，密码长度8-16位");
        }
        if (null != request.getEmail() && !ReUtil.isMatch(RegexPool.EMAIL, request.getEmail())) {
            throw new BizException(HttpStatus.BAD_REQUEST, "邮箱格式不正确");
        }
        if (null == request.getUserTenantPrivilegeConfigs() || request.getUserTenantPrivilegeConfigs().isEmpty()) {
            throw new BizException(HttpStatus.BAD_REQUEST, "用户权限配置不能为空");
        }
        TenantUserDetailVo tenantUserVo = tenantUserService.create(request);
        return ResponseEntity.ok(tenantUserVo);
    }

    @BackendSecurityRequired(tenantIdExpr = "#tenantId")
    @Override
    @DytSecurityRequired(needOpLog = true, value = "tenant:user:delete")
    public ResponseEntity<BaseOperationResponse> delete(Long id) {
        return ResponseEntity.ok(tenantUserService.delete(id));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "tenant:user:getCurrentUserDetail")
    public ResponseEntity<TenantUserDetailVo> getCurrentUserDetail() {
        return ResponseEntity.ok(tenantUserService.getUserDetail(backendTenantUserService.getCurrentUserId()));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "tenant:user:getDetail")
    public ResponseEntity<TenantUserDetailVo> getDetail(Long id, Long tenantId) {
        if (!backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.TenantId(tenantId))) {
            throw new BizException(HttpStatus.FORBIDDEN, "用户没有该租户权限");
        }
        return ResponseEntity.ok(tenantUserService.getDetail(id, tenantId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "tenant:user:modify")
    public ResponseEntity<BaseOperationResponse> modify(TenantUserModifyReqDto request) {
        if (StringUtils.hasText(request.getPassword()) && !request.getPassword().matches(tenantUserLoginConfig.getPasswordRegex())) {
            throw new BizException(HttpStatus.BAD_REQUEST, "密码强度不符合要求，必须包含大小写字母、数字，密码长度8-16位");
        }
        return ResponseEntity.ok(tenantUserService.modify(backendTenantUserService.getCurrentUserId(), request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "tenant:user:query")
    public ResponseEntity<TenantUserPageVo> query(TenantUserQueryReqDto request) {
        return ResponseEntity.ok(tenantUserService.query(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "tenant:user:queryUserBoundTenant")
    public ResponseEntity<TenantUserBindingTenantVo> queryUserBoundTenant(Long id) {
        List<TenantVo> tenantVos = tenantUserService.getUserBoundTenants(id, null);
        return ResponseEntity.ok(new TenantUserBindingTenantVo()
                .tenants(tenantVos));
    }

    @Override
    public ResponseEntity<BaseOperationResponse> sendDesensitizationSms() {
        return ResponseEntity.ok(tenantUserService.sendDesensitizationMsg());
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "tenant:user:sendMsg")
    public ResponseEntity<BaseOperationResponse> sendMsg(TenantUserSendMegReqDto tenantUserSendMegReqDto) {
        return ResponseEntity.ok(tenantUserService.sendMsg(tenantUserSendMegReqDto));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "tenant:user:unBindTenantUser")
    public ResponseEntity<BaseOperationResponse> unBindTenantUser(Long id, List<Long> tenantIdList) {
        return ResponseEntity.ok(tenantUserService.unbindTenant(id, tenantIdList));
    }


    @Override
    @DytSecurityRequired(needOpLog = true, value = "tenant:user:queryCurrentUserBoundTenant")
    public ResponseEntity<TenantUserBindingTenantVo> queryCurrentUserBoundTenant(String tenantName) {
        List<TenantVo> tenantVos = tenantUserService.getUserBoundTenants(backendTenantUserService.getCurrentUserId(), tenantName);
        return ResponseEntity.ok(new TenantUserBindingTenantVo()
                .tenants(tenantVos));
    }

    @BackendSecurityRequired(tenantIdExpr = "#tenantId")
    @Override
    @DytSecurityRequired(needOpLog = true, value = "tenant:user:update")
    public ResponseEntity<TenantUserDetailVo> update(Long id, TenantUserUpdateReqDto request) {
        if (StringUtils.hasText(request.getPassword()) && !ReUtil.isMatch(tenantUserLoginConfig.getPasswordRegex(), request.getPassword())) {
            throw new BizException(HttpStatus.BAD_REQUEST, "密码强度不符合要求，必须包含大小写字母、数字，密码长度8-16位");
        }
        return ResponseEntity.ok(tenantUserService.update(id, request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "tenant:user:verify:desensitization:sms")
    public ResponseEntity<BaseOperationResponse> verifyDesensitizationSms(String verificationCode) {
        return ResponseEntity.ok(tenantUserService.desensitizationSmsVerify(verificationCode));
    }
}
