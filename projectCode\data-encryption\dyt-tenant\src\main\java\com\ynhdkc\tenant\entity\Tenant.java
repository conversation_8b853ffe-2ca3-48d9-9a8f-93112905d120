package com.ynhdkc.tenant.entity;

import backend.common.domain.tenant.TenantEntity;
import backend.encryption.annotation.EncryptField;
import backend.encryption.converter.EncryptConverter;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @since 2023/2/14 15:30:47
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "t_tenant")
public final class Tenant extends TenantEntity {

    /**
     * 租户名称（不敏感数据，不加密）
     */
    @Column(name = "name", length = 50)
    private String name;

    /**
     * 租户描述（不敏感数据，不加密）
     */
    @Column(name = "description", length = 300)
    private String description;

    /**
     * 租户地址ID（不敏感数据，不加密）
     */
    @Column(name = "address_id")
    private Long addressId;

    /**
     * 租户logo URL（不敏感数据，不加密）
     */
    @Column(name = "logo_url", length = 200)
    private String logoUrl;

    /**
     * 租户联系人（不敏感数据，不加密）
     */
    @Column(name = "contact", length = 20)
    private String contact;

    /**
     * 联系人手机号（重要数据）
     * 使用影子字段迁移策略
     */
    @EncryptField(
        description = "租户联系人手机号",
        shadowField = "contact_phone_number_encrypted",
        migrationStrategy = EncryptField.MigrationStrategy.SHADOW_PRIORITY,
        strategyKey = "tenant-contact-phone",
        algorithm = EncryptField.AlgorithmType.AES_GCM
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "contact_phone_number", length = 100)
    private String contactPhoneNumber;

    /**
     * 联系人手机号加密字段（影子字段）
     */
    @Column(name = "contact_phone_number_encrypted", length = 1000)
    private String contactPhoneNumberEncrypted;

    /**
     * 联系人邮箱（重要数据）
     * 使用影子字段迁移策略
     */
    @EncryptField(
        description = "租户联系人邮箱",
        shadowField = "contact_email_encrypted",
        migrationStrategy = EncryptField.MigrationStrategy.SHADOW_PRIORITY,
        strategyKey = "tenant-contact-email",
        algorithm = EncryptField.AlgorithmType.AES_GCM
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "contact_email", length = 20)
    private String contactEmail;

    /**
     * 联系人邮箱加密字段（影子字段）
     */
    @Column(name = "contact_email_encrypted", length = 1000)
    private String contactEmailEncrypted;

    /**
     * 租户微信号（不敏感数据，不加密）
     */
    @Column(name = "contact_wechat", length = 30)
    private String contactWechat;

    /**
     * 租户联系人地址ID（不敏感数据，不加密）
     */
    @Column(name = "contact_address_id")
    private Long contactAddressId;
}
