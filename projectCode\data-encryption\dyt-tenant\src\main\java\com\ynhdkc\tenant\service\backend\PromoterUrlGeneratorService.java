package com.ynhdkc.tenant.service.backend;

import backend.common.response.BaseOperationResponse;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ynhdkc.tenant.dao.PromoterUrlGeneratorMapper;
import com.ynhdkc.tenant.entity.PromoterUrlGenerator;
import com.ynhdkc.tenant.model.GeneratePromoterUrlReqDto;
import com.ynhdkc.tenant.model.GetPromoterUrlPageReqDto;
import com.ynhdkc.tenant.model.PromoterUrlPageVo;
import com.ynhdkc.tenant.model.PromoterUrlVo;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;


@Service
@RequiredArgsConstructor
public class PromoterUrlGeneratorService {

    private final PromoterUrlGeneratorMapper promoterUrlGeneratorMapper;
    private final PageVoConvert pageVoConvert;


    public BaseOperationResponse deletePromoterUrl(Long id) {
        int effectiveCount = promoterUrlGeneratorMapper.deleteByPrimaryKey(id);
        return BaseOperationResponse.builder().message(effectiveCount > 0 ? "删除成功" : "删除失败").effectiveCount(effectiveCount).build();
    }

    public BaseOperationResponse generatePromoterUrl(GeneratePromoterUrlReqDto request) {
        if (!StringUtils.hasText(request.getPromoterCode())) {
            throw new IllegalArgumentException("推广码不能为空");
        }
        if (!StringUtils.hasText(request.getPromoterScene())) {
            throw new IllegalArgumentException("推广场景不能为空");
        }
        if (!StringUtils.hasText(request.getPromoterName())) {
            throw new IllegalArgumentException("推广员不能为空");
        }
        if (!StringUtils.hasText(request.getTargetUrl())) {
            throw new IllegalArgumentException("目标链接不能为空");
        }

        final String domain = "https://appv3.ynhdkc.com/redirect/promotion";
        final String encodedPromoterCode;
        final String encodedTargetUrl;
        try {
            encodedPromoterCode = URLEncoder.encode(request.getPromoterCode(), "UTF-8");
            encodedTargetUrl = URLEncoder.encode(request.getTargetUrl(), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new IllegalArgumentException("编码失败");
        }

        final String url = domain + "?id=" + encodedPromoterCode + "&n=" + request.getPromoterName() + "&t=" + request.getPromoterScene() + "&u=" + encodedTargetUrl;

        PromoterUrlGenerator promoterUrlGenerator = new PromoterUrlGenerator();
        promoterUrlGenerator.setPromoterCode(request.getPromoterCode());
        promoterUrlGenerator.setPromoterName(request.getPromoterName());
        promoterUrlGenerator.setPromoterScene(request.getPromoterScene());
        promoterUrlGenerator.setTargetUrl(request.getTargetUrl());
        promoterUrlGenerator.setPromoterUrl(url);
        promoterUrlGeneratorMapper.insertSelective(promoterUrlGenerator);
        return BaseOperationResponse.builder().message("生成成功").build();
    }

    public PromoterUrlVo getBy(Long id) {
        PromoterUrlGenerator promoterUrlGenerator = promoterUrlGeneratorMapper.selectByPrimaryKey(id);
        if (promoterUrlGenerator == null) {
            throw new IllegalArgumentException("推广链接不存在");
        }

        return toVo(promoterUrlGenerator);
    }

    public PromoterUrlPageVo getPromoterUrlPage(GetPromoterUrlPageReqDto request) {
        Page<PromoterUrlGenerator> promoterUrlPage;
        try (final Page<PromoterUrlGenerator> page = PageHelper.startPage(request.getCurrentPage(), request.getPageSize())) {
            promoterUrlPage = page.doSelectPage(() -> promoterUrlGeneratorMapper.selectByExample2(PromoterUrlGenerator.class, q -> {
                if (StringUtils.hasText(request.getPromoterCode())) {
                    q.andLike(PromoterUrlGenerator::getPromoterCode, "%" + request.getPromoterCode() + "%");
                }
                if (StringUtils.hasText(request.getPromoterName())) {
                    q.andLike(PromoterUrlGenerator::getPromoterName, "%" + request.getPromoterName() + "%");
                }
                if (StringUtils.hasText(request.getPromoterScene())) {
                    q.andLike(PromoterUrlGenerator::getPromoterScene, "%" + request.getPromoterScene() + "%");
                }
            }));
            return pageVoConvert.toPageVo(promoterUrlPage, PromoterUrlPageVo.class, this::toVo);
        }
    }

    private PromoterUrlVo toVo(PromoterUrlGenerator promoterUrlGenerator) {
        PromoterUrlVo promoterUrlVo = new PromoterUrlVo();
        promoterUrlVo.setId(promoterUrlGenerator.getId());
        promoterUrlVo.setPromoterCode(promoterUrlGenerator.getPromoterCode());
        promoterUrlVo.setPromoterName(promoterUrlGenerator.getPromoterName());
        promoterUrlVo.setPromoterScene(promoterUrlGenerator.getPromoterScene());
        promoterUrlVo.setPromoterUrl(promoterUrlGenerator.getPromoterUrl());
        promoterUrlVo.setCreateTime(promoterUrlGenerator.getCreateTime());
        promoterUrlVo.setTargetUrl(promoterUrlGenerator.getTargetUrl());
        promoterUrlVo.setUpdateTime(promoterUrlGenerator.getUpdateTime());
        return promoterUrlVo;
    }
}
