package backend.common.entity.dto.hisgateway.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class DoctorSchedulingInfoRequest {

    @JsonProperty("start_date")
    private Long startDate;

    @JsonProperty("end_date")
    private Long endDate;

    @JsonProperty("division_code")
    private String divisionCode;

    @JsonProperty("doctor_code")
    private String doctorCode;

    @JsonProperty("hospital_code")
    private String hospitalCode;

    @JsonProperty("schedule_id")
    private String scheduleId;

}
