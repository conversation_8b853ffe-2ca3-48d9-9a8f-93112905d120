package com.ynhdkc.tenant.dao.impl;

import com.ynhdkc.tenant.dao.TenantUserRepository;
import com.ynhdkc.tenant.dao.mapper.TenantUserMapper;
import com.ynhdkc.tenant.dao.mapper.TenantUserStructureMapper;
import com.ynhdkc.tenant.entity.TenantUser;
import com.ynhdkc.tenant.entity.TenantUserStructure;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-26 10:21
 */
@Repository
@RequiredArgsConstructor
public class TenantUserRepositoryImpl implements TenantUserRepository {
    private final TenantUserMapper tenantUserMapper;
    private final TenantUserStructureMapper tenantUserStructureMapper;

    @Override
    public void save(TenantUser tenantUser) {
        tenantUserMapper.insertSelective(tenantUser);
    }

    @Override
    public void update(TenantUser tenantUser) {
        tenantUserMapper.updateByPrimaryKeySelective(tenantUser);
    }

    @Override
    public void delete(Long userId) {
        tenantUserMapper.deleteByPrimaryKey(userId);
        /* 级联删除 */
        tenantUserStructureMapper.deleteByExample2(TenantUserStructure.class, sql -> sql.andEqualTo(TenantUserStructure::getUserId, userId));
    }

    @Override
    public void delete(Long userId, Long tenantId) {
        tenantUserStructureMapper.deleteByExample2(TenantUserStructure.class, sql ->
                sql.andEqualTo(TenantUserStructure::getUserId, userId)
                        .andEqualTo(TenantUserStructure::getTenantId, tenantId));
    }

}
