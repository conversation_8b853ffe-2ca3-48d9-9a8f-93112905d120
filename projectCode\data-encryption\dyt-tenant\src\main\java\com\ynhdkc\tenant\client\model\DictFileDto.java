package com.ynhdkc.tenant.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * DictFileDto
 */
@Validated
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2023-09-12T12:06:09.110+08:00")


public class DictFileDto {
    @JsonProperty("name")
    private String name = null;

    @JsonProperty("type")
    private Integer type = null;

    @JsonProperty("new_dict")
    private Boolean newDict = null;

    @JsonProperty("words")
    @Valid
    private List<String> words = null;

    public DictFileDto name(String name) {
        this.name = name;
        return this;
    }

    /**
     * 字典名称
     *
     * @return name
     **/
    @ApiModelProperty(value = "字典名称")


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public DictFileDto type(Integer type) {
        this.type = type;
        return this;
    }

    /**
     * 字典类型:0,关键词;1,屏蔽词;
     *
     * @return type
     **/
    @ApiModelProperty(value = "字典类型:0,关键词;1,屏蔽词;")


    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public DictFileDto newDict(Boolean newDict) {
        this.newDict = newDict;
        return this;
    }

    /**
     * 是否新建
     *
     * @return newDict
     **/
    @ApiModelProperty(value = "是否新建")


    public Boolean isNewDict() {
        return newDict;
    }

    public void setNewDict(Boolean newDict) {
        this.newDict = newDict;
    }

    public DictFileDto words(List<String> words) {
        this.words = words;
        return this;
    }

    public DictFileDto addWordsItem(String wordsItem) {
        if (this.words == null) {
            this.words = new ArrayList<String>();
        }
        this.words.add(wordsItem);
        return this;
    }

    /**
     * 关键字集合
     *
     * @return words
     **/
    @ApiModelProperty(value = "关键字集合")


    public List<String> getWords() {
        return words;
    }

    public void setWords(List<String> words) {
        this.words = words;
    }


    @Override
    public boolean equals(java.lang.Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DictFileDto dictFileDto = (DictFileDto) o;
        return Objects.equals(this.name, dictFileDto.name) &&
                Objects.equals(this.type, dictFileDto.type) &&
                Objects.equals(this.newDict, dictFileDto.newDict) &&
                Objects.equals(this.words, dictFileDto.words);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, type, newDict, words);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DictFileDto {\n");

        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    type: ").append(toIndentedString(type)).append("\n");
        sb.append("    newDict: ").append(toIndentedString(newDict)).append("\n");
        sb.append("    words: ").append(toIndentedString(words)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(java.lang.Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}

