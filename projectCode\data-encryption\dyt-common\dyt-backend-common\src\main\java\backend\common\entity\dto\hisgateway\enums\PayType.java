package backend.common.entity.dto.hisgateway.enums;

import com.fasterxml.jackson.annotation.JsonCreator;

/**
 * CV07.10.003
 */
public enum PayType {

	STAFF_MEDICARE("城镇职工基本医疗保险", "01"), RESIDENTS_MEDICARE("城镇居民基本医疗保险", "02"), RURAL_MEDICARE("新型农村合作医疗", "03"),
	POVERTY_RELIEF("贫困救助", "04"), COMMERCIAL_MEDICAL_INSURANCE("商业医疗保险", "05"), PUBLIC_EXPENSE("全公费", "06"),
	OWN_EXPENSE("全自费", "07"), OTHER("其他", "99");

	private final String value;

	private final String code;

	PayType(String value, String code) {
		this.value = value;
		this.code = code;
	}

	@JsonCreator
	public static PayType getFromCode(String code) {
		for (PayType t : PayType.values()) {
			if (t.getCode().equals(code)) {
				return t;
			}
		}
		throw new IllegalArgumentException("支付类型不存在");
	}

	public static PayType getFromValue(String value) {
		for (PayType t : PayType.values()) {
			if (t.getValue().equals(value)) {
				return t;
			}
		}
		throw new IllegalArgumentException("支付类型不存在");
	}

	public String getValue() {
		return value;
	}

	public String getCode() {
		return code;
	}

}
