package com.ynhdkc.tenant.service.backend;

import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.entity.detailpage.HospitalAreaDetailPageSubNaviModule;
import com.ynhdkc.tenant.model.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/24 16:41:37
 */
public interface IHospitalAreaDetailPageSubNaviModuleService {
    HospitalAreaDetailPageSubNaviModuleVo createHospitalAreaDetailPageSubNaviModule(CreateHospitalAreaDetailPageSubNaviModuleReqDto dto);

    BaseOperationResponse deleteHospitalAreaDetailPageSubNaviModule(Long id);

    HospitalAreaDetailPageSubNaviModuleVo getHospitalAreaDetailPageSubNaviModule(Long id);

    HospitalAreaDetailPageSubNaviModulePageVo searchHospitalAreaDetailPageSubNaviModule(SearchHospitalAreaDetailPageSubNaviModuleReqDto searchDto);

    HospitalAreaDetailPageSubNaviModuleVo updateHospitalAreaDetailPageSubNaviModule(Long id, UpdateHospitalAreaDetailPageSubNaviModuleReqDto dto);

    void setSubNaviModule(HospitalAreaLayoutVo vo, Long hospitalAreaId);

    List<HospitalAreaDetailPageSubNaviModuleVo> getHospitalAreaDetailPageSubNaviModuleByHospitalAreaId(Long hospitalAreaId);

    static HospitalAreaDetailPageSubNaviModuleVo toVo(HospitalAreaDetailPageSubNaviModule entity) {
        HospitalAreaDetailPageSubNaviModuleVo vo = new HospitalAreaDetailPageSubNaviModuleVo();
        vo.setId(entity.getId());
        vo.setTenantId(entity.getTenantId());
        vo.setHospitalId(entity.getHospitalId());
        vo.setHospitalAreaId(entity.getHospitalAreaId());
        vo.setSubNaviType(entity.getSubNaviType());
        vo.setSubNaviDisplayLimit(entity.getSubNaviDisplayLimit());
        vo.setSort(entity.getSort());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdateTime(entity.getUpdateTime());
        return vo;
    }

    default HospitalAreaDetailPageSubNaviModule toEntity(CreateHospitalAreaDetailPageSubNaviModuleReqDto dto) {
        HospitalAreaDetailPageSubNaviModule entity = new HospitalAreaDetailPageSubNaviModule();
        entity.setTenantId(dto.getTenantId());
        entity.setHospitalId(dto.getHospitalId());
        entity.setHospitalAreaId(dto.getHospitalAreaId());
        entity.setSubNaviType(dto.getSubNaviType());
        entity.setSubNaviDisplayLimit(dto.getSubNaviDisplayLimit());
        entity.setSort(dto.getSort());
        entity.setChannels("0,1");
        return entity;
    }

    default void toEntity(HospitalAreaDetailPageSubNaviModule entity, UpdateHospitalAreaDetailPageSubNaviModuleReqDto dto) {
        entity.setTenantId(dto.getTenantId());
        entity.setHospitalId(dto.getHospitalId());
        entity.setHospitalAreaId(dto.getHospitalAreaId());
        entity.setSubNaviType(dto.getSubNaviType());
        entity.setSubNaviDisplayLimit(dto.getSubNaviDisplayLimit());
        entity.setSort(dto.getSort());
        entity.setChannels("0,1");
    }
}
