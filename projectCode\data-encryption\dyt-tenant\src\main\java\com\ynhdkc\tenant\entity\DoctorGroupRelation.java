package com.ynhdkc.tenant.entity;

import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/9/6 9:11
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "t_doctor_group_relation")
public class DoctorGroupRelation extends BaseEntity {
    /**
     * 医生id
     */
    private Long doctorId;

    /**
     * 医生姓名
     */
    private String doctorName;
    /**
     * 医生团队来源:0,在线问诊;
     */
    private Integer doctorGroupSource;
    /**
     * 医生团队id
     */
    private Long doctorGroupId;
    /**
     * 医生团队名称
     */
    private String doctorGroupName;
}
