package com.example.encryption.config;

import com.example.encryption.annotation.EncryptField;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 迁移策略配置类
 * 支持通过application.yml统一配置迁移策略，便于快速回滚
 * 
 * 配置示例：
 * <pre>
 * encryption:
 *   migration:
 *     # 全局默认策略
 *     default-strategy: PLAINTEXT_PRIORITY
 *     # 是否启用全局策略覆盖
 *     enable-global-override: true
 *     # 具体字段策略配置
 *     strategies:
 *       email: PLAINTEXT_PRIORITY
 *       idCard: SHADOW_PRIORITY
 *       realName: SHADOW_ONLY
 *       phone: DIRECT_ENCRYPT
 * </pre>
 */
@Component
@ConfigurationProperties(prefix = "encryption.migration")
public class MigrationStrategyConfig {

    /**
     * 动态策略服务（延迟注入避免循环依赖）
     */
    @Autowired
    @Lazy
    private com.example.encryption.service.DynamicStrategyService dynamicStrategyService;

    /**
     * 全局默认策略
     */
    private EncryptField.MigrationStrategy defaultStrategy = EncryptField.MigrationStrategy.DIRECT_ENCRYPT;

    /**
     * 是否启用全局策略覆盖
     * 当为true时，所有字段都使用defaultStrategy，忽略注解中的配置
     * 当为false时，优先使用字段级别的策略配置
     */
    private boolean enableGlobalOverride = false;

    /**
     * 具体字段的策略配置
     * Key: 字段的strategyKey
     * Value: 迁移策略
     */
    private Map<String, EncryptField.MigrationStrategy> strategies = new HashMap<>();
    
    /**
     * 获取字段的迁移策略
     *
     * @param strategyKey 策略配置键
     * @param annotationStrategy 注解中配置的策略
     * @return 最终使用的迁移策略
     */
    public EncryptField.MigrationStrategy getStrategy(String strategyKey, EncryptField.MigrationStrategy annotationStrategy) {
        // 优先从动态策略服务获取
        if (dynamicStrategyService != null) {
            try {
                return dynamicStrategyService.getStrategy(strategyKey, annotationStrategy);
            } catch (Exception e) {
                // 如果动态策略服务出错，回退到静态配置
                System.err.println("动态策略服务异常，回退到静态配置: " + e.getMessage());
            }
        }

        // 回退到静态配置逻辑
        // 如果启用了全局策略覆盖，直接返回全局策略
        if (enableGlobalOverride) {
            return defaultStrategy;
        }

        // 如果有策略键且配置了对应的策略，使用配置的策略
        if (strategyKey != null && !strategyKey.trim().isEmpty() && strategies.containsKey(strategyKey)) {
            return strategies.get(strategyKey);
        }

        // 否则使用注解中的策略
        return annotationStrategy;
    }
    
    /**
     * 检查是否启用了全局策略覆盖
     * 
     * @return true表示启用全局策略覆盖
     */
    public boolean isGlobalOverrideEnabled() {
        return enableGlobalOverride;
    }
    
    /**
     * 获取全局默认策略
     * 
     * @return 全局默认策略
     */
    public EncryptField.MigrationStrategy getGlobalStrategy() {
        return defaultStrategy;
    }
    
    /**
     * 获取所有字段策略配置
     * 
     * @return 字段策略配置映射
     */
    public Map<String, EncryptField.MigrationStrategy> getAllStrategies() {
        return new HashMap<>(strategies);
    }
    
    /**
     * 动态更新策略配置
     * 用于运行时策略切换
     * 
     * @param strategyKey 策略键
     * @param strategy 新的策略
     */
    public void updateStrategy(String strategyKey, EncryptField.MigrationStrategy strategy) {
        strategies.put(strategyKey, strategy);
    }
    
    /**
     * 动态更新全局策略
     * 
     * @param strategy 新的全局策略
     */
    public void updateGlobalStrategy(EncryptField.MigrationStrategy strategy) {
        this.defaultStrategy = strategy;
    }
    
    /**
     * 动态切换全局覆盖模式
     * 
     * @param enabled 是否启用全局覆盖
     */
    public void setGlobalOverrideEnabled(boolean enabled) {
        this.enableGlobalOverride = enabled;
    }
    
    // Getters and Setters for Spring Boot Configuration Properties
    
    public EncryptField.MigrationStrategy getDefaultStrategy() {
        return defaultStrategy;
    }
    
    public void setDefaultStrategy(EncryptField.MigrationStrategy defaultStrategy) {
        this.defaultStrategy = defaultStrategy;
    }
    
    public boolean isEnableGlobalOverride() {
        return enableGlobalOverride;
    }
    
    public void setEnableGlobalOverride(boolean enableGlobalOverride) {
        this.enableGlobalOverride = enableGlobalOverride;
    }
    
    public Map<String, EncryptField.MigrationStrategy> getStrategies() {
        return strategies;
    }
    
    public void setStrategies(Map<String, EncryptField.MigrationStrategy> strategies) {
        this.strategies = strategies;
    }
    
    /**
     * 获取当前配置的摘要信息
     * 用于调试和监控
     * 
     * @return 配置摘要
     */
    public String getConfigSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("MigrationStrategyConfig{");
        summary.append("defaultStrategy=").append(defaultStrategy);
        summary.append(", enableGlobalOverride=").append(enableGlobalOverride);
        summary.append(", strategiesCount=").append(strategies.size());
        if (!strategies.isEmpty()) {
            summary.append(", strategies=").append(strategies);
        }
        summary.append("}");
        return summary.toString();
    }
}
