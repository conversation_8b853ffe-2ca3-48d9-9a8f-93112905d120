package backend.common.entity.dto.hisgateway.enums;

import com.fasterxml.jackson.annotation.JsonGetter;

/**
 * CV04.50.005
 */
public enum BloodType {

	A("A型", 1), B("B型", 2), O("O型", 3);

	private final String value;

	private final Integer code;

	BloodType(String value, Integer code) {
		this.value = value;
		this.code = code;
	}

	public static BloodType getFromCode(int code) {
		for (BloodType t : BloodType.values()) {
			if (t.getCode().equals(code)) {
				return t;
			}
		}
		throw new IllegalArgumentException("血型不存在");
	}

	public static BloodType getFromValue(String value) {
		for (BloodType t : BloodType.values()) {
			if (t.getValue().equals(value)) {
				return t;
			}
		}
		throw new IllegalArgumentException("血型不存在");
	}

	public String getValue() {
		return value;
	}

	public Integer getCode() {
		return code;
	}

	@JsonGetter("code")
	public String getRequestCode() {
		return code.toString();
	}

}
