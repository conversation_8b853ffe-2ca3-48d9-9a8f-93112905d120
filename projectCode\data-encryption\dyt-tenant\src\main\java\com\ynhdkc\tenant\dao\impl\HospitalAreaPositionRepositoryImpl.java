package com.ynhdkc.tenant.dao.impl;

import com.ynhdkc.tenant.dao.HospitalAreaPositionRepository;
import com.ynhdkc.tenant.dao.mapper.HospitalAreaPositionMapper;
import com.ynhdkc.tenant.entity.HospitalAreaPosition;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/9/26 10:06
 */
@Repository
@RequiredArgsConstructor
public class HospitalAreaPositionRepositoryImpl implements HospitalAreaPositionRepository {
    private final HospitalAreaPositionMapper hospitalAreaPositionMapper;

    @Override
    public void create(HospitalAreaPosition departmentPosition) {
        hospitalAreaPositionMapper.insertSelective(departmentPosition);
    }

    @Override
    public void update(HospitalAreaPosition departmentPosition) {
        hospitalAreaPositionMapper.updateByPrimaryKeySelective(departmentPosition);
    }

    @Override
    public void delete(Long positionId) {
        hospitalAreaPositionMapper.deleteByPrimaryKey(positionId);
    }
}
