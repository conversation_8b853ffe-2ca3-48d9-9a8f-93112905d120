package com.ynhdkc.tenant.service.customer;


import com.ynhdkc.tenant.model.UserDoctorSubscriptionCreateDto;
import com.ynhdkc.tenant.model.UserDoctorSubscriptionVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UserDoctorSubscriptionService {
    UserDoctorSubscriptionVo addSubscription(UserDoctorSubscriptionCreateDto subscriptionDto);

    void cancelUserDoctorSubscription(Long id, long userId);

    List<UserDoctorSubscriptionVo> getSubscriptions(Long userId, Integer status);

    Boolean checkUserDoctorSubscription(Long userId, Long doctorId);

}
