package com.ynhdkc.tenant.dto.document;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;

import static com.ynhdkc.tenant.dto.document.HospitalDocument.INDEX_NAME;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/8/10 13:10
 */
@Data
@Document(indexName = INDEX_NAME, createIndex = false)
public class HospitalDocument {
    @Id
    private Long id;

    @Field(type = FieldType.Text, store = true, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String name;

    @Field(type = FieldType.Keyword, store = true)
    private String logo;

    @Field(type = FieldType.Integer, store = true)
    private Integer display;

    @Field(name = "display_sort", type = FieldType.Integer, store = true)
    private Integer displaySort;

    @Field(type = FieldType.Integer, store = true)
    private Integer status;

    @Field(name = "create_time", type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", store = true)
    private Date createTime;

    @Field(name = "update_time", type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", store = true)
    private Date updateTime;

    @JsonIgnore
    public static final String INDEX_NAME = "hospital";
    @JsonIgnore
    public static final String PROPERTY_NAME = "name";
    @JsonIgnore
    public static final String PROPERTY_DISPLAY_SORT = "display_sort";
    @JsonIgnore
    public static final String PROPERTY_STATUS = "status";
}
