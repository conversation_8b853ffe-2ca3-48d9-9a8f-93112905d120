package com.ynhdkc.tenant.service.backend;

import backend.common.response.BaseOperationResponse;
import backend.common.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.entity.constant.HospitalAreaFunctionType;
import com.ynhdkc.tenant.entity.setting.*;
import com.ynhdkc.tenant.model.*;
import org.springframework.data.util.Pair;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-27 10:17
 */
public interface HospitalAreaService {
    static Hospital toHospitalArea(HospitalAreaCreateReqDto dto) {
        Hospital entity = new Hospital();
        entity.setTenantId(dto.getTenantId());
        entity.setParentId(dto.getHospitalId());
        entity.setHospitalCode(dto.getHospitalAreaCode());
        entity.setName(dto.getName());
        entity.setPictures(dto.getPicture());
        entity.setCategories(dto.getCategory());
        entity.setContactPhoneNumber(dto.getContactPhoneNumber());
        entity.setDisplay(dto.isDisplay());
        entity.setDisplaySort(dto.getDisplaySort());
        entity.setAnnouncement(dto.getAnnouncement());
        entity.setIntroduction(dto.getIntroduction());
        entity.setEnvironment(dto.getEnvironment());
        entity.setDisplayGuide(dto.isDisplayGuide());
        entity.setMapKeyword(dto.getMapKeyword());
        entity.setDisplayFloor(dto.isDisplayFloor());
        entity.setStopServiceBeginTime(dto.getStopServiceBeginTime());
        entity.setStopServiceEndTime(dto.getStopServiceEndTime());
        entity.setStatus(dto.getStatus());
        entity.setAppointmentSchedulingTime(dto.getAppointmentSchedulingTime());
        entity.setTagDictLabels(dto.getTagDictLabel());
        return entity;
    }

    static HospitalAreaVo toHospitalAreaVo(Hospital entity) {
        return new HospitalAreaVo()
                .id(entity.getId())
                .tenantId(entity.getTenantId())
                .hospitalAreaCode(entity.getHospitalCode())
                .name(entity.getName())
                .hospitalId(entity.getParentId())
                .status(entity.getStatus())
                .display(entity.getDisplay())
                .category(entity.getCategories())
                .displaySort(entity.getDisplaySort())
                .stopServiceBeginTime(entity.getStopServiceBeginTime())
                .stopServiceEndTime(entity.getStopServiceEndTime())
                .createTime(entity.getCreateTime());
    }

    static HospitalAreaFunctionItem toHospitalAreaFunctionItem(FunctionSetting entity) {
        HospitalAreaFunctionItem vo = new HospitalAreaFunctionItem();
        vo.setId(entity.getId());
        vo.setName(entity.getName());
        vo.setType(entity.getType());
        vo.setLogo(entity.getLogo());
        vo.setTabDisplayStyle(entity.getTabDisplayStyle());
        vo.setStatus(entity.getStatus());
        return vo;
    }

    static DiagnosisPaymentSetting toDiagnosisPaymentSetting(DiagnosisPaymentSettingDto dto) {
        DiagnosisPaymentSetting entity = new DiagnosisPaymentSetting();
        entity.setSupportMergerPayment(dto.isSupportMergerPayment());
        entity.setSupportOnlineRefund(dto.isSupportOnlineRefund());
        entity.setRefundToday(dto.isRefundToday());
        entity.setStopRefundTime(dto.getStopRefundTime());
        entity.setSupportInvoice(dto.isSupportInvoice());
        entity.setSelectedPayments(JsonUtil.serializeObject(dto.getSelectedPayments()));
        entity.setPaymentInformation(JsonUtil.serializeObject(dto.getPaymentInformation()));
        return entity;
    }

    static DiagnosisPaymentSettingVo toDiagnosisPaymentSettingVo(DiagnosisPaymentSetting entity) {
        DiagnosisPaymentSettingVo vo = new DiagnosisPaymentSettingVo();
        vo.setId(entity.getId());
        vo.setFunctionId(entity.getFunctionId());
        vo.setSupportMergerPayment(entity.getSupportMergerPayment());
        vo.setSupportOnlineRefund(entity.getSupportOnlineRefund());
        vo.setRefundToday(entity.getRefundToday());
        vo.setStopRefundTime(entity.getStopRefundTime());
        vo.setSupportInvoice(entity.getSupportInvoice());
        if (null != entity.getSelectedPayments()) {
            vo.setSelectedPayments(JsonUtil.deserializeObject(entity.getSelectedPayments(), new TypeReference<List<String>>() {
            }));
        }
        vo.setPaymentInformation(entity.getPaymentInformation());
        return vo;
    }

    static HospitalizationSetting toHospitalizationSetting(HospitalizationSettingDto dto) {
        HospitalizationSetting entity = new HospitalizationSetting();
        entity.setEnablePayment(dto.isEnablePayment());
        entity.setSupportOnlineRefund(dto.isSupportOnlineRefund());
        entity.setEnableInfoQuery(dto.isEnableInfoQuery());
        entity.setSelectedPayments(JsonUtil.serializeObject(dto.getSelectedPayments()));
        entity.setPaymentInformation(dto.getPaymentInformation());
        return entity;
    }

    static HospitalizationSettingVo toHospitalizationSettingVo(HospitalizationSetting entity) {
        HospitalizationSettingVo vo = new HospitalizationSettingVo();
        vo.setId(entity.getId());
        vo.setFunctionId(entity.getFunctionId());
        vo.setEnablePayment(entity.getEnablePayment());
        vo.setSupportOnlineRefund(entity.getSupportOnlineRefund());
        vo.setEnableInfoQuery(entity.getEnableInfoQuery());
        if (null != entity.getSelectedPayments()) {
            vo.setSelectedPayments(JsonUtil.deserializeObject(entity.getSelectedPayments(), new TypeReference<List<String>>() {
            }));
        }
        vo.setPaymentInformation(entity.getPaymentInformation());
        return vo;
    }

    static PatientReportSetting toPatientReportSetting(PatientReportSettingDto patientReportSetting) {
        PatientReportSetting entity = new PatientReportSetting();
        entity.setSupportReportType(patientReportSetting.getSupportReportType());
        entity.setSupportSearchDateRange(patientReportSetting.getSupportSearchDateRange());
        entity.setSupportSearchTime(JsonUtil.serializeObject(patientReportSetting.getSupportSearchTime()));
        entity.setNotice(patientReportSetting.getNotice());
        return entity;
    }

    static PatientReportSettingVo toPatientReportSettingVo(PatientReportSetting entity) {
        PatientReportSettingVo vo = new PatientReportSettingVo();
        vo.setId(entity.getId());
        vo.setFunctionId(entity.getFunctionId());
        vo.setSupportReportType(entity.getSupportReportType());
        vo.setSupportSearchDateRange(entity.getSupportSearchDateRange());
        vo.setNotice(entity.getNotice());
        if (null != entity.getSupportSearchTime()) {
            vo.setSupportSearchTime(JsonUtil.deserializeObject(entity.getSupportSearchTime(), new TypeReference<List<Integer>>() {
            }));
        }
        return vo;
    }

    static PatientCardSetting toPatientCardSetting(PatientCardSettingDto dto) {
        PatientCardSetting entity = new PatientCardSetting();
        entity.setCardName(dto.getCardName());
        entity.setNeedPatientCard(dto.isNeedPatientCard());
        entity.setNeedRecharge(dto.isNeedRecharge());
        entity.setBindType(dto.getBindType());
        entity.setSupportPatientType(dto.getSupportPatientType());
        entity.setNeedElectronCard(dto.isNeedElectronCard());
        return entity;
    }

    static PatientCardSettingVo toPatientCardSettingVo(PatientCardSetting entity) {
        PatientCardSettingVo vo = new PatientCardSettingVo();
        vo.setId(entity.getId());
        vo.setFunctionId(entity.getFunctionId());
        vo.setCardName(entity.getCardName());
        vo.setNeedPatientCard(entity.getNeedPatientCard());
        vo.setNeedRecharge(entity.getNeedRecharge());
        vo.setBindType(entity.getBindType());
        vo.setSupportPatientType(entity.getSupportPatientType());
        vo.setNeedElectronCard(entity.getNeedElectronCard());
        return vo;
    }

    static CustomBusinessSetting toCustomBusinessSetting(CustomBusinessSettingDto dto) {
        CustomBusinessSetting entity = new CustomBusinessSetting();
        entity.setName(dto.getName());
        entity.setLogo(dto.getLogo());
        entity.setWechatOpenPath(dto.getWechatOpenPath());
        entity.setMiniProgramAppId(dto.getMiniProgramAppId());
        entity.setMiniProgramPath(dto.getMiniProgramPath());
        return entity;
    }

    static CustomBusinessSettingVo toCustomBusinessSettingVo(CustomBusinessSetting entity) {
        CustomBusinessSettingVo vo = new CustomBusinessSettingVo();
        vo.setId(entity.getId());
        vo.setFunctionId(entity.getFunctionId());
        vo.setName(entity.getName());
        vo.setLogo(entity.getLogo());
        vo.setWechatOpenPath(entity.getWechatOpenPath());
        vo.setMiniProgramAppId(entity.getMiniProgramAppId());
        vo.setMiniProgramPath(entity.getMiniProgramPath());
        return vo;
    }

    HospitalAreaDetailVo create(HospitalAreaCreateReqDto request);

    HospitalAreaDetailVo update(Long hospitalAreaId, HospitalAreaUpdateReqDto request);

    Pair<BaseOperationResponse, Long> delete(Long hospitalAreaId);

    HospitalAreaDetailVo getDetail(Long hospitalAreaId);

    HospitalAreaDetailVo rpcGetDetail(Long hospitalAreaId);

    HospitalAreaPageVo query(HospitalAreaQueryReqDto request);

    HospitalAreaDepartmentsInfoPageVo queryHospitalAreaDepartmentInfo(HospitalAreaDepartmentsInfoQueryReqDto request);

    HospitalAreaSettingDetailVo createSetting(Long hospitalAreaId, HospitalAreaSettingCreateReqDto request);

    HospitalAreaSettingDetailVo updateSetting(Long hospitalAreaId, HospitalAreaSettingUpdateReqDto request, HospitalAreaFunctionType type);

    HospitalAreaSettingDetailVo getSettingDetail(Long tenantId, Long hospitalId, Long hospitalAreaId);

    HospitalAreaFunctionDetailVo getFunctionDetail(Long hospitalAreaId);

    HospitalAreaFunctionDetailVo updateFunction(Long hospitalAreaId, HospitalAreaFunctionUpdateReqDto request);

    HospitalAreaLayoutVo getLayout(Long hospitalAreaId);

    Boolean isHospitalAreaDependOnHis(Long hospitalAreaId);

    List<HospitalAreaVo> rpcBatchGetDetail(RpcBatchGetByIdReqDto request);

    HospitalAreaVo rpcGetDetailByCode(String code);

    Hospital getById(Long hospitalAreaId);

    void batchSetStopGenerateSchedule(HospitalAreaBatchSetStopGenerateScheduleReqDto request);

    RpcStopScheduleDataMap rpcStopScheduleDataMap();


    AppointNotifyConfigVo getAppointNotifyConfig(String hospitalCode);

    SetAppointNotifyConfigResponse setAppointNotifyConfig(String hospitalCode, SetAppointNotifyConfigReqDto reqDto);

    Boolean deleteDepartmentScheduleCache(Long hospitalAreaId);

    List<HospitalDependOnHisResponse> rpcGetHospitalDependOnHis(Long id, String hospitalName);
}
