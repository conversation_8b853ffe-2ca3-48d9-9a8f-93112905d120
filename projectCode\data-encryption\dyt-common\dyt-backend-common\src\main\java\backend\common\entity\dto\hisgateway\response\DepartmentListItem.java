package backend.common.entity.dto.hisgateway.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
public class DepartmentListItem {

    @JsonProperty("hospital_code")
    private String hospitalCode;

    @JsonProperty("registration_date")
    private String registrationDate;

    @JsonProperty("registration_level")
    private String registrationLevel;

    @JsonProperty("registration_level_desc")
    private String registrationLevelDesc;

    @JsonProperty("department_name")
    private String departmentName;

    @JsonProperty("department_code")
    private String departmentCode;

    @JsonProperty("parent_department_code")
    private String parentDepartmentCode;

    @JsonProperty("department_type_name")
    private String departmentTypeName;

    @JsonProperty("department_type_code")
    private String departmentTypeCode;

    @JsonProperty("first_department_name")
    private String firstDepartmentName;

    @JsonProperty("first_department_code")
    private String firstDepartmentCode;

    @JsonProperty("second_department_name")
    private String secondDepartmentName;

    @JsonProperty("second_department_code")
    private String secondDepartmentCode;

    @JsonProperty("department_jp")
    private String departmentJP;

    @JsonProperty("first_jp")
    private String firstJP;

    @JsonProperty("department_description")
    private String departmentDescription;

    @JsonProperty("department_address")
    private String departmentAddress;

    private List<DepartmentListItem> children = new ArrayList<>();

    private Object sort;

    // 科室提醒
    private String caution;

    private Map<String, Object> rawParameters;

    private boolean enable;

    public int getIntSortValue() {
        if (sort == null) {
            return 0;
        }
        if (sort instanceof Integer) {
            return (int) sort;
        }
        return 0;
    }

    public boolean getEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }
}
