package com.ynhdkc.tenant.entity;

import backend.common.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Table;

/**
 * <p>
 * 医院组
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(name = "t_hospital_list_group")
@ApiModel(value = "HospitalListGroup对象", description = "医院组")
public class HospitalListGroup extends BaseEntity {

    @ApiModelProperty("医院组名称")
    private String name;

    @ApiModelProperty("描述")
    private String description;

}
