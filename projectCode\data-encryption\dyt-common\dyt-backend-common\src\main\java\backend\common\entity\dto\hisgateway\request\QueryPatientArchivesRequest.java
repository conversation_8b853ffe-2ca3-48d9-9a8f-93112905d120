package backend.common.entity.dto.hisgateway.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class QueryPatientArchivesRequest {

    @JsonProperty("hospital_code")
    private String hospitalCode;

    private String name;

    @JsonProperty("id_card_number")
    private String idCardNumber;

    @JsonProperty("id_card_type")
    private Integer idCardType;

    private String birthday;

    private Integer sex;

    @JsonProperty("medical_record_number")
    private String medicalRecordNumber;

    private String telephone;

}
