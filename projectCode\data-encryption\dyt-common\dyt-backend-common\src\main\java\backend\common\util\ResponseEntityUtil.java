package backend.common.util;

import backend.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-11 14:43
 */
@Slf4j
public class ResponseEntityUtil {
    private ResponseEntityUtil() {
    }


    /**
     * 断言 http 的响应和状态只要响应为 null 或者不是 200
     *
     * @param response         响应
     * @param exceptionMessage 异常信息
     * @param <T>              响应体类型
     */
    public static <T> void assertHttpResponse(ResponseEntity<T> response, String exceptionMessage) {
        if (null == response || !HttpStatus.OK.equals(response.getStatusCode())) {
            log.error("远程调用失败:{}", exceptionMessage);
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, exceptionMessage);
        }
    }

    /**
     * 判断返回结果是否为空、HTTP状态码是否为200、HTTP Body是否为空。如果是则抛出异常，不是直接返回HTTP Body。
     *
     * @param exceptionMessage 校验失败时抛出的异常信息
     */
    public static <T> T assertAndGetBody(ResponseEntity<T> response, String exceptionMessage) {
        if (null == response || !HttpStatus.OK.equals(response.getStatusCode()) || null == response.getBody()) {
            log.error("远程调用失败:{}", exceptionMessage);
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, exceptionMessage);
        }
        return response.getBody();
    }

    /**
     * 判断返回结果是否为空、HTTP状态码是否在可接受清单中、HTTP Body是否为空。如果是则抛出异常，不是直接返回HTTP Body。
     *
     * @param exceptionMessage 校验失败时抛出的异常信息
     * @param acceptStatus     自定义想要接受的状态码，因为会存在非200状态码也有响应数据
     */
    public static <T> T assertAndGetBody(ResponseEntity<T> response, String exceptionMessage, HttpStatus... acceptStatus) {
        Set<HttpStatus> statusSet = new HashSet<>(Arrays.asList(acceptStatus));
        if (null == response || !statusSet.contains(response.getStatusCode()) || null == response.getBody()) {
            log.error("远程调用失败:{}", exceptionMessage);
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, exceptionMessage);
        }
        return response.getBody();
    }
}
