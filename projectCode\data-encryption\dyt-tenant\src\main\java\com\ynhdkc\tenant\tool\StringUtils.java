package com.ynhdkc.tenant.tool;

import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class StringUtils {

    private static final String SEP = "/";//File.separator;

    private StringUtils() {
    }

    public static <T> String join(T[] records, String symbol) {
        if (ObjectUtils.isEmpty(records)) {
            return "";
        }
        StringBuilder buffer = new StringBuilder();
        for (int i = 0; i < records.length; i++) {
            String itemValue = String.valueOf(records[i]);
            if (i == 0) {
                buffer.append(itemValue);
            } else {
                buffer.append(symbol).append(itemValue);
            }
        }
        return buffer.toString();
    }

    public static <T> String join(List<T> records, String symbol) {
        if (ObjectUtils.isEmpty(records)) {
            return "";
        }
        StringBuilder buffer = new StringBuilder();
        for (int i = 0; i < records.size(); i++) {
            String itemValue = String.valueOf(records.get(i));
            if (i == 0) {
                buffer.append(itemValue);
            } else {
                buffer.append(symbol).append(itemValue);
            }
        }
        return buffer.toString();
    }

    public static <T> String join(Set<T> records, String symbol) {
        if (ObjectUtils.isEmpty(records)) {
            return "";
        }
        StringBuilder buffer = new StringBuilder();
        int i = 0;
        for (T value : records) {
            if (i == 0) {
                buffer.append(value);
            } else {
                buffer.append(symbol).append(value);
            }
            i++;
        }
        return buffer.toString();
    }

    public static List<String> spiltString(String rawMessage, String symbol) {
        if (StringUtils.isEmpty(rawMessage) || StringUtils.isEmpty(symbol)) {
            return Collections.emptyList();
        }
        String[] rawMessageArray = rawMessage.split(symbol);
        return Arrays.asList(rawMessageArray);
    }

    public static String getUUID(int length) {
        String s = UUID.randomUUID().toString().replace("-", "");
        return s.substring(0, length);
    }

    public static String getUUID32() {
        return getUUID(32);
    }

    public static String getTimestamp() {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
        return format.format(new Date());
    }

    public static boolean isEmpty(String value) {
        return "".equals(value) || value == null;
    }

    public static String getFileName(String url) {
        if (StringUtils.isEmpty(url)) {
            return "";
        }
        if (!url.contains(SEP) || !url.contains(".")) {
            return "";
        }
        return url.substring(url.lastIndexOf(SEP) + 1, url.lastIndexOf("."));
    }

    public static String getHospitalCodeFolderName(String url, String filePath) {
        if (StringUtils.isEmpty(url) || filePath == null) {
            return "";
        }
        if (!url.contains(SEP)) {
            return "";
        }
        int beginIdx = url.indexOf(filePath);
        int startIdx = beginIdx + (filePath.endsWith(SEP) ? filePath.length() : filePath.length() + 1);
        url = url.substring(startIdx);
        if (!url.contains(SEP)) {
            return "";
        }
        return url.substring(0, url.indexOf(SEP));
    }

    public static void main(String[] arg) {
        System.out.println(getHospitalCodeFolderName(("C:/ASDFASDF/test/test1/871044/v2/test.ftl"), "/test/test1/"));
        System.out.println(getHospitalCodeFolderName(("C:/ASDFASDF/test/test1/871044/test.ftl"), "/test/test1/"));
    }


    public static String getSubStringInclude(String rawMessage, String startTag, String endTag) {
        Pattern startPattern = Pattern.compile(startTag);
        Pattern endPattern = Pattern.compile(endTag);
        Matcher startMatcher = startPattern.matcher(rawMessage);
        Matcher endMatcher = endPattern.matcher(rawMessage);
        if (!startMatcher.find() || !endMatcher.find()) {
            return rawMessage;
        }
        int start = startMatcher.start();
        int end = endMatcher.end();
        return rawMessage.substring(start, end);
    }

    public static String getSubStringExclude(String rawMessage, String startTag, String endTag) {
        Pattern startPattern = Pattern.compile(startTag);
        Pattern endPattern = Pattern.compile(endTag);
        Matcher startMatcher = startPattern.matcher(rawMessage);
        Matcher endMatcher = endPattern.matcher(rawMessage);
        if (!startMatcher.find() || !endMatcher.find()) {
            return rawMessage;
        }
        int start = startMatcher.end();
        int end = endMatcher.start();
        return rawMessage.substring(start, end);
    }

    public static boolean isNumeric(String str) {
        if (str == null) {
            return false;
        } else {
            int sz = str.length();

            for (int i = 0; i < sz; ++i) {
                if (!Character.isDigit(str.charAt(i))) {
                    return false;
                }
            }

            return true;
        }
    }


    public static boolean hasText(@Nullable CharSequence str) {
        return str != null && str.length() > 0 && containsText(str);
    }

    public static boolean hasText(@Nullable String str) {
        return str != null && !str.isEmpty() && containsText(str);
    }

    private static boolean containsText(CharSequence str) {
        int strLen = str.length();

        for (int i = 0; i < strLen; ++i) {
            if (!Character.isWhitespace(str.charAt(i))) {
                return true;
            }
        }
        return false;
    }

    public static String getAmtValue(BigDecimal rawValue) {
        DecimalFormat format = new DecimalFormat("##0.00");
        return format.format(rawValue);
    }

    public static String getAmtValue(double rawValue) {
        DecimalFormat format = new DecimalFormat("##0.00");
        return format.format(rawValue);
    }
}
