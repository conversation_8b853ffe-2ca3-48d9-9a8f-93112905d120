package com.ynhdkc.tenant.service.backend;

import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.entity.HospitalListRule;
import com.ynhdkc.tenant.model.*;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import sdk.common.util.JsonUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 医院列表规则表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
public interface IHospitalListRuleService {

    static HospitalListRuleVo toVo(HospitalListRule entity) {
        if (entity == null) {
            return null;
        }
        List<GroupScaleDto> scales = new ArrayList<>();
        if (!ObjectUtils.isEmpty(entity.getGroupScale())) {
            try {
                scales = JsonUtils.json2list(entity.getGroupScale(), GroupScaleDto.class);
            } catch (Exception ignored) {
            }
        }
        HospitalListRuleVo vo = new HospitalListRuleVo();
        vo.setId(entity.getId());
        vo.setName(entity.getName());
        vo.setDescription(entity.getDescription());
        vo.setType(entity.getType());
        vo.setWeightOrderBy(entity.getWeightOrderBy());
        vo.setDistanceOrderBy(entity.getDistanceOrderBy());
        vo.setHospitalGroupId(entity.getHospitalGroupId());
        vo.setAdvertisementId(entity.getAdvertisementId());
        vo.setAdvertisementType(entity.getAdvertisementType());
        vo.setGroupScale(scales);
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdateTime(entity.getUpdateTime());

        return vo;
    }

    static <T extends UpdateHospitalListRuleReqDto> HospitalListRule toEntity(T dto) {
        if (dto == null) {
            return null;
        }
        String scales = null;
        if (!CollectionUtils.isEmpty(dto.getGroupScale())) {
            try {
                scales = JsonUtils.obj2json(dto.getGroupScale());
            } catch (Exception ignored) {
            }
        }
        HospitalListRule entity = new HospitalListRule();
        entity.setName(dto.getName());
        entity.setDescription(dto.getDescription());
        entity.setType(dto.getType());
        entity.setWeightOrderBy(dto.getWeightOrderBy());
        entity.setDistanceOrderBy(dto.getDistanceOrderBy());
        entity.setHospitalGroupId(dto.getHospitalGroupId());
        entity.setAdvertisementId(dto.getAdvertisementId());
        entity.setAdvertisementType(dto.getAdvertisementType());
        entity.setGroupScale(scales);

        return entity;
    }

    HospitalListRuleVo createHospitalListRule(CreateHospitalListRuleReqDto dto);

    BaseOperationResponse deleteHospitalListRule(Long id);

    HospitalListRuleVo getHospitalListRuleDetail(Long id);

    HospitalListRulePageVo getHospitalListRulePage(Integer currentPage, Integer pageSize);

    HospitalListRuleVo updateHospitalListRule(Long id, UpdateHospitalListRuleReqDto hospitalListRuleDto);

    GetExtensionConditionAdvertisementRespVo getExtensionConditionAdvertisementList(GetExtensionConditionAdvertisementReqDto request);
}
