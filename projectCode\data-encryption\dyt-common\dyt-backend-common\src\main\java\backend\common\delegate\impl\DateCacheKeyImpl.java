package backend.common.delegate.impl;

import backend.common.delegate.CacheKeyDelegate;
import backend.common.entity.dto.hisgateway.response.MessageEnvelope;
import backend.common.util.CacheKeyUtils;
import backend.common.util.ObjectsUtils;

import java.util.Map;

public class DateCacheKeyImpl implements CacheKeyDelegate {

    private final String hisOperationValue;

    public DateCacheKeyImpl(String hisOperationValue) {
        this.hisOperationValue = hisOperationValue;
    }

    @Override
    public String getCacheKey(MessageEnvelope<?> messageEnvelope, Map<String, Object> requestParameters) {
        String hospitalCode = ObjectsUtils.getStringValue(requestParameters, "hospital_code");
        String departmentCode = ObjectsUtils.getStringValue(requestParameters, "department_code");
        String doctorCode = ObjectsUtils.getStringValue(requestParameters, "doctor_code");
        long startDate = ObjectsUtils.getLongValue(requestParameters, "start_date");

        if (ObjectsUtils.isEmpty(hospitalCode) || ObjectsUtils.isEmpty(departmentCode)
                || ObjectsUtils.isEmpty(doctorCode) || startDate == 0) {
            return null;
        }
        return CacheKeyUtils.getCacheKey(hisOperationValue, hospitalCode, departmentCode, doctorCode, startDate);
    }
}
