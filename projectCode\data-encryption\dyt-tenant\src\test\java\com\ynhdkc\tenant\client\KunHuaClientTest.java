package com.ynhdkc.tenant.client;

import com.fasterxml.jackson.core.type.TypeReference;
import com.ynhdkc.tenant.DytTenantApplication;
import com.ynhdkc.tenant.client.model.AuthorizeRequest;
import com.ynhdkc.tenant.client.model.KunHuaAuthorizeResponse;
import com.ynhdkc.tenant.client.model.KunHuaSubDeptRequest;
import com.ynhdkc.tenant.client.model.KunHuaSubDeptResponse;
import com.ynhdkc.tenant.tool.KunHuaTool;
import com.ynhdkc.tenant.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertEquals;

@SpringBootTest(classes = {DytTenantApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("dev")
@Slf4j
class KunHuaClientTest {

    @Autowired
    private KunHuaClient kunHuaClient;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Test
    void getAuthorization() {
        String timestamp = KunHuaTool.getTimeStamp();
        AuthorizeRequest request = new AuthorizeRequest();
        request.setAppId(KunHuaTool.APP_ID);
        request.setAppSecret(KunHuaTool.getAppSecret(timestamp));
        request.setTimestamp(timestamp);
        ResponseEntity<KunHuaAuthorizeResponse> response = kunHuaClient.getAuthorization(request);
        assertEquals(200, response.getStatusCodeValue());
        KunHuaAuthorizeResponse body = response.getBody();

        assert body != null;
        redisTemplate.opsForValue().set(KunHuaTool.CACHE_KEY, body.getAccessToken(), body.getExpiresIn());
    }

    @Test
    void subDept() {
        String deptCode = "10";
        String startDate = "2024-11-26";
        String endDate = "2024-12-06";

        KunHuaSubDeptRequest.Data data = new KunHuaSubDeptRequest.Data();
        data.setPlatFrom(KunHuaTool.APP_ID);
        data.setDeptCode(deptCode);
        data.setStartDate("2021-01-01");
        data.setStartDate(startDate);
        data.setEndDate(endDate);

        KunHuaSubDeptRequest request = new KunHuaSubDeptRequest();
        request.setData(JsonUtil.toJson(data));
        request.setCode("APPOINT012");
        request.setAppId(KunHuaTool.APP_ID);
        request.setToken(redisTemplate.opsForValue().get(KunHuaTool.CACHE_KEY));
        String timestamp = KunHuaTool.getTimeStamp();
        request.setTimestamp(timestamp);
        ResponseEntity<KunHuaSubDeptResponse> response = kunHuaClient.querySubDept(request);
        assertEquals(200, response.getStatusCodeValue());
        String content = Objects.requireNonNull(response.getBody()).getContent();
        List<KunHuaSubDeptResponse.SubDept> subDeptList = JsonUtil.fromJsonToList(content, new TypeReference<List<KunHuaSubDeptResponse.SubDept>>() {
        });
        subDeptList.forEach(System.out::println);
    }

}