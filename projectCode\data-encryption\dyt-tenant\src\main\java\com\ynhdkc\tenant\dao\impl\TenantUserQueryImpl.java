package com.ynhdkc.tenant.dao.impl;

import backend.common.util.MybatisUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.ynhdkc.tenant.dao.TenantUserQuery;
import com.ynhdkc.tenant.dao.mapper.TenantUserMapper;
import com.ynhdkc.tenant.entity.TenantUser;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-25 22:30
 */
@Repository
@RequiredArgsConstructor
public class TenantUserQueryImpl implements TenantUserQuery {
    private final TenantUserMapper tenantUserMapper;

    @Override
    public Page<TenantUser> query(TenantUserQueryOption option) {
        try (final Page<TenantUser> page = PageMethod.startPage(option.getCurrentPage(), option.getPageSize())) {
            Page<TenantUser> tenantUserPage;
            tenantUserPage = page.doSelectPage(() -> tenantUserMapper.selectByExample(TenantUser.class, sql -> {
                sql.defGroup(where -> {
                    if (null != option.getId()) {
                        where.andEqualTo(TenantUser::getId, option.getId());
                    }
                    if (null != option.getNameEqual()) {
                        where.andEqualTo(TenantUser::getName, option.getNameEqual());
                    }
                    if (null != option.getNameLike()) {
                        where.andLike(TenantUser::getName, MybatisUtil.likeBoth(option.getNameLike()));
                    }
                    if (null != option.getNicknameLike()) {
                        where.andLike(TenantUser::getNickname, MybatisUtil.likeBoth(option.getNicknameLike()));
                    }
                    if (null != option.getPhoneNumber()) {
                        where.andLike(TenantUser::getPhoneNumber, MybatisUtil.likeLeft(option.getPhoneNumber()));
                    }
                    if (StringUtils.hasText(option.getIdCardNo())) {
                        where.andLike(TenantUser::getIdCardNo, MybatisUtil.likeBoth(option.getIdCardNo()));
                    }
                    if (null != option.getAdmin()) {
                        where.andEqualTo(TenantUser::getAdmin, option.getAdmin());
                    }
                    if (null != option.getStatus()) {
                        where.andEqualTo(TenantUser::getStatus, option.getStatus());
                    }
                    if (null != option.getStartCreateTime() && null != option.getEndCreateTime()) {
                        where.andBetween(TenantUser::getCreateTime, option.getStartCreateTime(), option.getEndCreateTime());
                    }

                    if (!CollectionUtils.isEmpty(option.getIncludingIds())) {
                        where.andIn(TenantUser::getId, option.getIncludingIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getExcludingIds())) {
                        where.andNotIn(TenantUser::getId, option.getExcludingIds());
                    }

                    if (!CollectionUtils.isEmpty(option.getIncludingRoleUserIds())) {
                        where.andIn(TenantUser::getId, option.getIncludingRoleUserIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getExcludingRoleUserIds())) {
                        where.andNotIn(TenantUser::getId, option.getExcludingRoleUserIds());
                    }
                });
                sql.builder(builder -> builder.orderByDesc(TenantUser::getId));
            }));
            return tenantUserPage;
        }
    }

    @Override
    public TenantUser queryByUserId(Long userId) {
        return tenantUserMapper.selectByPrimaryKey(userId);
    }

    @Override
    public List<TenantUser> queryByUserIds(Collection<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        return tenantUserMapper.selectByExample2(TenantUser.class, sql -> sql.andIn(TenantUser::getId, userIds));
    }

    @Override
    public Integer countByPhoneNumber(String phoneNumber) {
        return tenantUserMapper.selectCountByExample2(TenantUser.class, sql -> sql.andEqualTo(TenantUser::getPhoneNumber, phoneNumber));
    }

    @Override
    public int countByName(String userName) {
        return tenantUserMapper.selectCountByExample2(TenantUser.class, sql -> sql.andEqualTo(TenantUser::getName, userName)
        );
    }


    @Override
    public List<TenantUser> queryByName(String userName) {
        return tenantUserMapper.selectByExample2(TenantUser.class, sql -> sql.andEqualTo(TenantUser::getName, userName));
    }

    @Override
    public List<TenantUser> queryByNameOrPhone(String userName) {
        return tenantUserMapper.selectByExample2(TenantUser.class, sql -> sql.andEqualTo(TenantUser::getName, userName).orEqualTo(TenantUser::getPhoneNumber, userName));
    }

    @Override
    public List<TenantUser> queryPasswordErrorTimesMoreThanOne() {
        return tenantUserMapper.selectByExample2(TenantUser.class, sql -> sql.andGreaterThan(TenantUser::getDailyLoginRetries, 0));
    }

    @Override
    public int countByEmail(String email) {
        return tenantUserMapper.selectCountByExample2(TenantUser.class, sql -> sql.andEqualTo(TenantUser::getEmail, email));
    }
}
