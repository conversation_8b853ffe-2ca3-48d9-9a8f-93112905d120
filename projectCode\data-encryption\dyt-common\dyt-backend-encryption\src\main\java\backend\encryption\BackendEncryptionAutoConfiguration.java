package backend.encryption;

import backend.encryption.config.EncryptionProperties;
import backend.encryption.converter.EncryptConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 后端加密模块自动配置类
 * 
 * 提供以下功能：
 * - 加密配置属性绑定
 * - JPA转换器自动注册
 * - 加密工具类Bean注册
 * - 条件化配置支持
 * 
 * 配置示例：
 * <pre>
 * backend:
 *   encryption:
 *     enabled: true
 *     secret-key: "your-secret-key"
 *     algorithm: AES_GCM
 * </pre>
 * 
 * <AUTHOR> Backend Team
 * @version 1.1-SNAPSHOT
 * @since 2024-06-24
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties(EncryptionProperties.class)
@ConditionalOnProperty(prefix = "backend.encryption", name = "enabled", havingValue = "true", matchIfMissing = true)
public class BackendEncryptionAutoConfiguration {
    
    private final EncryptionProperties encryptionProperties;
    
    /**
     * 注册JPA加密转换器
     * 
     * @return EncryptConverter实例
     */
    @Bean
    @ConditionalOnProperty(prefix = "backend.encryption", name = "enabled", havingValue = "true", matchIfMissing = true)
    public EncryptConverter encryptConverter() {
        log.info("Registering JPA EncryptConverter with algorithm: {}", 
            encryptionProperties.getAlgorithm());
        return new EncryptConverter();
    }
    
    /**
     * 初始化加密模块
     * 在应用启动时执行必要的初始化工作
     */
    @Bean
    public EncryptionInitializer encryptionInitializer() {
        EncryptionInitializer initializer = new EncryptionInitializer(encryptionProperties);
        initializer.initialize();
        return initializer;
    }
    
    /**
     * 加密模块初始化器
     * 负责在应用启动时进行必要的初始化工作
     */
    @RequiredArgsConstructor
    public static class EncryptionInitializer {
        
        private final EncryptionProperties properties;
        
        /**
         * 执行初始化
         */
        public void initialize() {
            log.info("Initializing DYT Backend Encryption Module");
            log.info("Encryption enabled: {}", properties.isEnabled());
            log.info("Default algorithm: {}", properties.getAlgorithm());
            log.info("Global strategy: {}", properties.getGlobalStrategy());
            log.info("Configured strategies: {}", properties.getStrategies().size());
            
            if (properties.isDebugEnabled()) {
                log.debug("Debug mode enabled for encryption module");
            }
            
            if (properties.isPerformanceMonitorEnabled()) {
                log.info("Performance monitoring enabled for encryption operations");
            }
            
            // 验证配置
            validateConfiguration();
            
            log.info("DYT Backend Encryption Module initialized successfully");
        }
        
        /**
         * 验证配置的有效性
         */
        private void validateConfiguration() {
            if (properties.isEnabled()) {
                // 验证密钥配置
                if (properties.getSecretKey() == null || properties.getSecretKey().trim().isEmpty()) {
                    log.warn("No custom secret key configured, using default key. " +
                        "Please configure 'backend.encryption.secret-key' for production use.");
                }
                
                // 验证策略配置
                properties.getStrategies().forEach((key, strategy) -> {
                    if (strategy.getStrategy() == null) {
                        log.warn("Strategy '{}' has no migration strategy configured, using default", key);
                    }
                    if (strategy.getShadowField() != null && strategy.getShadowField().trim().isEmpty()) {
                        log.warn("Strategy '{}' has empty shadow field configured", key);
                    }
                });
                
                log.debug("Configuration validation completed");
            }
        }
    }
}
