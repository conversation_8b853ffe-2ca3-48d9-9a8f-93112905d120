package com.ynhdkc.tenant.tool.convert;

import com.ynhdkc.tenant.entity.DictFile;
import com.ynhdkc.tenant.model.DictFileCreateReqDto;
import com.ynhdkc.tenant.model.DictFileUpdateReqDto;
import com.ynhdkc.tenant.model.DictFileVo;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Collections;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/9/12 16:46
 */
public class SearchManagementConverter {
    private SearchManagementConverter() {
    }

    public static DictFile toEntity(DictFileCreateReqDto dto) {
        DictFile entity = new DictFile();
        entity.setName(dto.getName());
        entity.setType(dto.getType());
        entity.setWords(String.join(",", dto.getWords()));
        entity.setRemark(dto.getRemark());
        return entity;
    }

    public static void updateIfNotNull(DictFile entity, DictFileUpdateReqDto dto) {
        entity.setId(dto.getId());
        if (null != dto.getName()) {
            entity.setName(dto.getName());
        }
        if (null != dto.getType()) {
            entity.setType(dto.getType());
        }
        if (null != dto.getWords()) {
            entity.setWords(String.join(",", dto.getWords()));
        }
        if (null != dto.getRemark()) {
            entity.setRemark(dto.getRemark());
        }
        if (null != dto.getStatus()) {
            entity.setStatus(dto.getStatus());
        }
    }

    public static DictFileVo toDictFileVo(DictFile entity) {
        DictFileVo vo = new DictFileVo();
        vo.setId(entity.getId());
        vo.setName(entity.getName());
        vo.setType(entity.getType());
        if (StringUtils.hasText(entity.getWords())) {
            vo.setWords(Arrays.asList(entity.getWords().split(",")));
        } else {
            vo.setWords(Collections.emptyList());
        }
        vo.setRemark(entity.getRemark());
        vo.setStatus(entity.getStatus());
        vo.setClusterStatus(entity.getClusterStatus());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdateTime(entity.getUpdateTime());
        vo.setSyncTime(entity.getSyncTime());
        return vo;
    }
}
