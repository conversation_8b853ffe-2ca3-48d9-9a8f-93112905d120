create table t_tenant
(
    id                   bigint auto_increment comment '全局唯一标识'
        primary key,
    name                 varchar(50)                              not null comment '租户名称',
    description          varchar(300)                             null comment '租户描述',
    address_id           bigint                                   null comment '租户地址 ID',
    logo_url             varchar(200)                             null comment '租户 logo URL',
    contact              varchar(20)                              null comment '租户联系人',
    contact_phone_number varchar(100)                              null comment '租户联系人手机号',
    contact_email        varchar(20)                              null comment '租户联系人邮箱',
    contact_wechat       varchar(30)                              null comment '租户微信号',
    contact_address_id   bigint                                   null comment '租户联系人地址 ID',
    create_time          datetime(3) default CURRENT_TIMESTAMP(3) not null,
    update_time          datetime(3)                              null on update CURRENT_TIMESTAMP(3)
)
    comment 'Tenant';

create index idx_contact
    on t_tenant (contact);

insert into t_tenant
    (id, name, description)
VALUES (1, '云南省第一人民医院', '云南省第一人民医院租户'),
       (2, '云南省第二人民医院', '云南省第二人民医院租户');