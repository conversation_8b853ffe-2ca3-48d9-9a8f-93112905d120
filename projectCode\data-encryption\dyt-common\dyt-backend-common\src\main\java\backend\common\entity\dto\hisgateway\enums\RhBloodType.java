package backend.common.entity.dto.hisgateway.enums;

import com.fasterxml.jackson.annotation.JsonGetter;

public enum RhBloodType {

	RH_NEG("RH阴性", 1), RH("RH阳性", 2), UNKNOW("不详", 3);

	private final String value;

	private final Integer code;

	RhBloodType(String value, Integer code) {
		this.value = value;
		this.code = code;
	}

	public static RhBloodType getFromCode(int code) {
		for (RhBloodType t : RhBloodType.values()) {
			if (t.getCode().equals(code)) {
				return t;
			}
		}
		throw new IllegalArgumentException("血型不存在");
	}

	public static RhBloodType getFromValue(String value) {
		for (RhBloodType t : RhBloodType.values()) {
			if (t.getValue().equals(value)) {
				return t;
			}
		}
		throw new IllegalArgumentException("血型不存在");
	}

	public String getValue() {
		return value;
	}

	public Integer getCode() {
		return code;
	}

	@JsonGetter("code")
	public String getRequestCode() {
		return code.toString();
	}

}
