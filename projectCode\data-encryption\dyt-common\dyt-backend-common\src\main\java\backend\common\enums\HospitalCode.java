package backend.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;

public enum HospitalCode {

    PROVINCIAL_HEALTH_CARD("871", "省医疗平台健康卡中心"),


    KUNMING_CHILDREN_HOSPITAL_QIAN_XING("871002", "昆明市儿童医院(前兴院区)"),
    KUNMING_CHILDREN_HOSPITAL_SHU_LIN("871003", "昆明市儿童医院(书林院区)"),

    KUNMING_MATERNAL_CHILDREN_HUA_SHANG_CP("871097", "昆明市妇幼保健院（华山院区）"),
    KUNMING_MATERNAL_CHILDREN_CHENG_GONG_CP("871088", "昆明市妇幼保健院（呈贡院区）"),
    KUNMING_MATERNAL_CHILDREN_DAN_XIA_CP("871099", "昆明市妇幼保健院(丹霞院区)"),
    KUNMING_MATERNAL_CHILDREN_HAI_GENG_CP("871890", "昆明市妇幼保健院(海埂院区)"),


    YUNNAN_CANCER_HOSPITAL("871249", "云南肿瘤医院"),

    KUNMING_MU_FIRST_AFFILIATED_HOSPITAL("871044", "昆明医科大学第一附属医院(云大医院)"),
    KUNMING_MU_FIRST_AFFILIATED_CG_CAMPUS_HOSPITAL("871023", "昆明医科大学第一附属医院(云大医院) 呈贡院区"),
    KUNMING_MU_SECOND_AFFILIATED_HOSPITAL("871058", "昆明医科大学第二附属医院(工人医院)"),

    YUNNAN_FIRST_PEOPLE_NEW_KUN_HUA_HOSPITAL("871140", "云南省第一人民医院新昆华医院"),
    YUNNAN_FIRST_PEOPLE_HOSPITAL("871900", "云南省第一人民医院(昆华医院)"),
    YUNNAN_FIRST_PEOPLE_HOSPITAL_V2("871899", "云南省第一人民医院(昆华医院)"),
    YUNNAN_FIRST_PEOPLE_HOSPITAL_V3("871102", "云南省第一人民医院(昆华医院)"),
    YUNNAN_SECOND_PEOPLE_HOSPITAL("871045", "云南省第二人民医院"),
    YUNNAN_THIRD_PEOPLE_HOSPITAL("871030", "云南省第三人民医院"),
    YUNNAN_THIRD_PEOPLE_HOSPITAL_V2("871896", "云南省第三人民医院"),
    YAN_AN_HOSPITAL("871093", "云南省延安医院"),
    DISTRICT_CHENGGONG_PEOPLE_HOSPITAL("871052", "昆明市呈贡区人民医院"),

    YUNNAN_TRADITIONAL_CHINESE_MEDICINE_GUANGHUA("871333", "云南省中医医院(光华院区)"),
    YUNNAN_TRADITIONAL_CHINESE_MEDICINE_DIANCHI("871232", "云南省中医医院(滇池院区)"),
    YUNNAN_TRADITIONAL_CHINESE_MEDICINE_DIANCHI_RESORT("871054", "云南省中医医院滇池度假区医院"),
    KUNMING_FOURTH_PEOPLE_HOSPITAL_JF("871888", "安宁市第一人民医院（金方院区）"),
    KUNMING_FOURTH_PEOPLE_HOSPITAL_LR("871889", "安宁市第一人民医院（连然院区）"),
    KUNMING_ANNING_TCM_HOSPITAL("871892", "安宁市中医医院"),

    KUNMING_FIRST_PEOPLE_HOSPITAL("871001", "昆明市第一人民医院(甘美医院)"),
    KUNMING_FIRST_PEOPLE_HOSPITAL_SOUTH("871053", "昆明市第一人民医院南院"),


    KUNMING_THIRD_PEOPLE_HOSPITAL_WU_JING("871037", "昆明市第三人民医院（吴井院区）"),
    KUNMING_THIRD_PEOPLE_HOSPITAL_CHANG_PO("871010", "昆明市第三人民医院（长坡院区）"),
    KUNMING_JING_KAI_PEOPLE_HOSPITAL("871034", "昆明市经开区人民医院"),
    YUNNAN_PSYCHIATRIC_HOSPITAL("871038", "云南省精神病院"),
    YUNNAN_UTCM_2ND_HOSPITAL("871095", "云南中医药大学第二附属医院"),
    ARMY_920_HOSPITAL("871256", "中国人民解放军联勤保障部队第九二〇医院"),
    FU_WAI_YUNNAN_HOSPITAL("871139", "云南省阜外心血管病医院"),
    FU_WAI_YUNNAN_HOSPITAL_V2("871145", "云南省阜外心血管病医院"),
    KUNMING_AIEYANKE_NEW("871083", "昆明爱尔眼科医院"),


    KUNMING_TCM_HOSPITAL("871039", "昆明市中医医院本部（东风东路）"),
    KUNMING_TCM_HOSPITAL_CG_CAMPUS("871041", "昆明市中医医院(呈贡院区)"),
    KUNMING_TCM_HOSPITAL_GS_CAMPUS("871042", "昆明市中医医院(关上院区)"),


    KUNMING_TEENAGE_MENTAL_CENTER("871092", "昆明市青少年心理健康服务中心"),

    YUNNAN_DERMATOLOGY_HOSPITAL("871961", "云南皮肤病医院"),


    KUNMING_PANG_LONG_DISTRICT_PEOPLE_HOSPITAL("871090", "昆明市盘龙区人民医院"),
    KUNMING_DON_CHUAN_DISTRICT_PEOPLE_HOSPITAL("871011", "东川区人民医院"),


    KUNMING_DERMATOLOGY_HOSPITAL("871031", "昆明皮肤病专科医院"),


    WU_DING_PEOPLE_HOSPITAL("871089", "武定县人民医院"),
    SONG_MING_PEOPLE_HOSPITAL("871028", "嵩明县人民医院"),
    KUNMING_XIN_AN_HOSPITAL("871056", "昆明馨安医院"),


    KUNMING_XI_SHAN_DISTRICT_PEOPLE_HOSPITAL("871032", "昆明市西山区人民医院"),
    KUNMING_XI_SHAN_DISTRICT_PEOPLE_HOSPITAL_V2("871891", "昆明市西山区人民医院"),
    DISTRICT_WUHUA_PEOPLE_HOSPITAL("871027", "昆明市五华区人民医院"),
    DISTRICT_GUANDU_PEOPLE_HOSPITAL("871008", "昆明市官渡区人民医院"),
    DISTRICT_PANLONG_PEOPLE_HOSPITAL("871090", "昆明市盘龙人民医院"),
    YILIANG_TCM_HOSPITAL("871893", "宜良县中医医院"),


    LUFENG_PEOPLE_HOSPITAL("871060", "禄丰市人民医院"),
    KUNMING_GD_MATERNAL_CHILD("871084", "昆明市官渡区妇幼健康服务中心"),

    YUNNAN_GERIATRIC_HOSPITAL("871071", "云南省老年病医院"),

    YUNNAN_DENTAL_BASE("871667", "云南省口腔医院-海源中路院本部"),
    YUNNAN_DENTAL_NORTH("871167", "云南省口腔医院-第一门诊部"),
    YUNNAN_DENTAL_YUANTONG("871567", "云南省口腔医院-第二门诊部"),
    YUNNAN_DENTAL_QIANXING("871367", "云南省口腔医院-前兴路门诊部"),
    YUNNAN_DENTAL_CHENGGONG("871467", "云南省口腔医院-呈贡门诊部"),
    YUNNAN_DENTAL_CHENGGONG_QICAI("871767", "云南省口腔医院-呈贡七彩云南门诊部"),
    YUNNAN_DENTAL_KUNBAIDA("871267", "云南省口腔医院-昆百大门诊部"),

    KUNMING_AN_QI_ER_HOSPITAL("871969", "昆明安琪儿妇产医院"),
    AI_WEI_EYE_HOSPITAL("871085", "昆明艾维眼科医院（原昆明华山眼科医院）"),
    YUNNAN_MATERNAL_CHILD_HEALTH_HOSPITAL("871062", "云南省妇幼保健院"),
    KUNMING_FIRST_PEOPLE_HOSPITAL_XY_CAMPUS("871048", "昆明市第一人民医院星耀医院"),

    KUNMING_TONG_REN_HOSPITAL("871057", "昆明同仁医院"),


    CHUXIONG_MATERNAL_CHILD_HEALTH_HOSPITAL("871068", "楚雄彝族自治州妇幼保健院"),

    YUNNAN_PROVINCE_ZHONGXIYI_HOSPITAL("871094", "云南省中西医结合医院"),

    KUNMING_SECOND_PEOPLE_HOSPITAL("871096", "昆明市第二人民医院"),

    KUNMING_MARIA_HOSPITAL("871009", "昆明玛丽亚医院"),

    DISTRICT_DONGCHUAN_TCM_HOSPITAL("871055", "昆明市东川区中医院"),

    KUNMING_THIRD_PEOPLE_HOSPITAL_WU_JING_V3("871894", "昆明市第三人民医院（吴井院区）"),
    KUNMING_THIRD_PEOPLE_HOSPITAL_CHANG_PO_V3("871895", "昆明市第三人民医院（长坡院区）"),

    DISTRICT_PANLONG_MATERNAL_CHILDREN_HOSPITAL("871098", "昆明市盘龙区妇幼保健院"),

    YUN_NAN_DIAN_DONG_BEI("871101", "滇东北医院"),

    UNKNOWN("", "未知");


    private final String code;

    private final String desc;

    HospitalCode(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static HospitalCode parse(String code) {
        for (HospitalCode hospitalCode : HospitalCode.values()) {
            if (hospitalCode.getCode().equals(code)) {
                return hospitalCode;
            }
        }
        return UNKNOWN;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
