package backend.common.domain.user;


import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "t_dict_label")
public class DictLabelEntity extends BaseEntity {

    private String dictType;

    private String dictLabel;

    private String dictValue;

    private String description;

    private String iconUrl;

    private Integer sort;

    private String tag;

    private String redirectPath;

    private String badge;
}
