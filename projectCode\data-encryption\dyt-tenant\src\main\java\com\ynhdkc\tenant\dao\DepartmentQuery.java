package com.ynhdkc.tenant.dao;

import backend.common.util.BaseQueryOption;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.model.DepartmentsTreeSearchPageReqDto;
import com.ynhdkc.tenant.model.DepartmentsTreeSearchPageVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-25 22:10
 */
public interface DepartmentQuery {
    Department queryDepartmentById(Long departmentId);

    Page<Department> pageQueryDepartment(DepartmentQueryOption option);

    int countByHospitalAreaId(Long hospitalAreaId);

    List<Department> queryChildrenDepartmentById(Long departmentId);

    List<Department> queryDepartments(Long hospitalAreaId, Set<String> departCode);

    List<Department> queryDepartmentCodeList(String hospitalAreaCode);

    List<Department> queryDepartmentsIn(List<String> hospitalCodes, Collection<String> departmentCodes);

    List<Department> queryDepartmentsByHospitalAreaId(Long hospitalAreaId);

    List<Department> queryBy(List<Long> departmentIds);

    List<Department> queryByHospitalIds(List<Long> hospitalIds);

    List<Department> queryList(char firstLetter);

    List<Department> queryList(char firstLetter, List<String> hospitalCodeList);

    @Nullable
    Department queryBy(String hospitalAreaCode, String departmentCode);

    List<Department> queryList(String hospitalAreaCode, String departmentCode);

    Optional<Department> queryDepartmentByCode(String hospitalAreaCode, String departmentCode);

    List<Department> queryBy(String hospitalCode);

    List<Long> queryEnabledDepartmentIds(List<Long> departmentIds);

    Department queryShadowDepartment(String hospitalCode, String departmentCode);

    void save(Department newDepartment);

    DepartmentsTreeSearchPageVo queyrDepartPageByCondition(DepartmentsTreeSearchPageReqDto request);

    List<Department> queryAllQuickCheckDepartment();

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Accessors(chain = true)
    class DepartmentQueryOption extends BaseQueryOption {
        private Long tenantId;
        private Long hospitalId;
        private Long hospitalAreaId;
        private Long parentId;
        private Long id;
        private String name;
        private String shortName;
        private String thrdpartDepCode;
        private String firstLetter;
        private Integer sort;
        private Boolean recommended;
        private List<Integer> category;
        private List<Integer> categoryIn;
        private Boolean enabled;

        private Collection<Long> includingIds;
        private Collection<Long> excludingIds;
        private Collection<Long> includeTenantIds;
        private Collection<Long> excludeTenantIds;
        private Collection<Long> includeHospitalIds;
        private Collection<Long> excludeHospitalIds;
        private Collection<Long> includeHospitalAreaIds;
        private Collection<Long> excludeHospitalAreaIds;

        private Collection<Long> includeAreaIds;

        public DepartmentQueryOption(Integer currentPage, Integer pageSize) {
            super(currentPage, pageSize);
        }
    }
}
