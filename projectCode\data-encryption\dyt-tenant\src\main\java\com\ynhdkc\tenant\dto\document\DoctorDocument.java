package com.ynhdkc.tenant.dto.document;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;

import static com.ynhdkc.tenant.dto.document.DoctorDocument.INDEX_NAME;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/8/10 13:10
 */
@Data
@Document(indexName = INDEX_NAME, createIndex = false)
public class DoctorDocument {
    @Id
    private Long id;

    @Field(type = FieldType.Text, store = true, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String name;

    @Field(name = "doctor_code", type = FieldType.Keyword, store = true)
    private String doctorCode;

    @Field(name = "hospital_id", type = FieldType.Long, store = true)
    private Long hospitalId;

    @Field(name = "hospital_area_id", type = FieldType.Long, store = true)
    private Long hospitalAreaId;

    @Field(name = "hospital_area_name", type = FieldType.Text, store = true, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String hospitalAreaName;

    @Field(name = "hospital_area_code", type = FieldType.Keyword, store = true)
    private String hospitalAreaCode;

    @Field(name = "department_id", type = FieldType.Long, store = true)
    private Long departmentId;

    @Field(name = "department_name", type = FieldType.Text, store = true, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String departmentName;

    @Field(name = "department_code", type = FieldType.Keyword, store = true)
    private String departmentCode;

    @Field(name = "head_img", type = FieldType.Keyword, store = true)
    private String headImg;

    @Field(type = FieldType.Keyword, store = true)
    private String speciality;

    @Field(type = FieldType.Integer, store = true)
    private Integer sort;

    @Field(name = "system_depends", type = FieldType.Integer, store = true)
    private Integer systemDepends;

    @Field(type = FieldType.Boolean, store = true)
    private Boolean display;

//    @Field(name = "create_time", type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", store = true)
//    private Date createTime;
//
//    @Field(name = "update_time", type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", store = true)
//    private Date updateTime;

    @JsonIgnore
    public static final String INDEX_NAME = "doctor";
    @JsonIgnore
    public static final String PROPERTY_HOSPITAL_AREA_ID = "hospital_area_id";
    @JsonIgnore
    public static final String PROPERTY_NAME = "name";
    @JsonIgnore
    public static final String PROPERTY_SOURCE = "source";
    @JsonIgnore
    public static final String PROPERTY_HOSPITAL_AREA_NAME = "hospital_area_name";
    @JsonIgnore
    public static final String PROPERTY_SPECIALITY = "speciality";
    @JsonIgnore
    public static final String PROPERTY_SORT = "sort";
    @JsonIgnore
    public static final String PROPERTY_SYSTEM_DEPENDS = "system_depends";
    @JsonIgnore
    public static final String PROPERTY_DISPLAY = "display";
    @JsonIgnore
    public static final String PROPERTY_STATUS = "status";
}
