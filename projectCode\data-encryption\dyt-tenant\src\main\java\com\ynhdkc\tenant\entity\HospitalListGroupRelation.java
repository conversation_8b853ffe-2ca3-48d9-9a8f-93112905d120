package com.ynhdkc.tenant.entity;

import backend.common.domain.BaseEntity;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Table;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 医院组关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(name = "t_hospital_list_group_relation")
@ApiModel(value = "HospitalListGroupRelation对象", description = "医院组关系表")
public class HospitalListGroupRelation extends BaseEntity {

    @ApiModelProperty("医院组id")
    private Long groupId;

    @ApiModelProperty("医院id")
    private Long hospitalId;

    private String hospitalCode;

    @ApiModelProperty("权重")
    private Integer weight;

    private String platform;

    public List<Integer> getPlatformList() {
        return StrUtil.splitTrim(this.platform, ",").stream().map(Integer::valueOf).collect(Collectors.toList());
    }

    public HospitalListGroupRelation setPlatformWithList(List<Integer> platform) {
        this.platform = platform.stream()
                .sorted()
                .map(String::valueOf)
                .collect(Collectors.joining(","));
        return this;
    }
}
