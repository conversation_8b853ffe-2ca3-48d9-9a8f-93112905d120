package com.ynhdkc.tenant.service.xxl;


import backend.common.dao.respository.HisGatewayMessageRepository;
import backend.common.entity.dto.hisgateway.request.DoctorOutServiceRequest;
import backend.common.entity.dto.hisgateway.request.HospitalOutServiceRequest;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.ObjectId;
import com.ynhdkc.tenant.dao.DepartmentQuery;
import com.ynhdkc.tenant.dao.HospitalAreaQuery;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.Hospital;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class CronTask {

    @Resource
    private DepartmentQuery departmentQuery;

    @Resource
    private HospitalAreaQuery hospitalAreaQuery;

    @Resource
    private HisGatewayMessageRepository hisGatewayMessageRepository;


    public void getDepartmentStopSchedule(String hospitalAreaCode) {
        log.info("按照科室获取停诊排班信息, 医院编码:{}", hospitalAreaCode);
        final Date startDate = new Date();
        final Date endDate = DateUtil.offsetDay(startDate, 7);

        Hospital hospital = hospitalAreaQuery.queryHospitalAreaBy(hospitalAreaCode);
        if (ObjectUtils.isEmpty(hospital)) {
            return;
        }
        List<Department> departmentCodeList = departmentQuery.queryDepartmentCodeList(hospitalAreaCode);
        if (ObjectUtils.isEmpty(departmentCodeList)) {
            return;
        }
        departmentCodeList.forEach(y -> {
            DoctorOutServiceRequest doctorOutServiceRequest = new DoctorOutServiceRequest();
            doctorOutServiceRequest.setHospitalCode(hospitalAreaCode);
            doctorOutServiceRequest.setDepartmentCode(y.getThrdpartDepCode());
            doctorOutServiceRequest.setStartDate(startDate.getTime());
            doctorOutServiceRequest.setEndDate(endDate.getTime());
            hisGatewayMessageRepository.send(doctorOutServiceRequest, hospital.getId(), hospital.getId(), y.getId(), ObjectId.next());
        });
    }

    public void getHospitalAreaStopSchedule(String hospitalAreaCode) {
        log.info("按照医院获取停诊排班信息, 医院编码:{}", hospitalAreaCode);

        final Hospital hospital = hospitalAreaQuery.queryHospitalAreaBy(hospitalAreaCode);
        if (hospital == null) {
            return;
        }

        final HospitalOutServiceRequest hospitalOutServiceRequest = new HospitalOutServiceRequest();
        final Date startDate = new Date();
        final Date endDate = DateUtil.offsetDay(startDate, 7);
        
        hospitalOutServiceRequest.setHospitalCode(hospitalAreaCode);
        hospitalOutServiceRequest.setStartDate(startDate.getTime());
        hospitalOutServiceRequest.setEndDate(endDate.getTime());
        hisGatewayMessageRepository.send(hospitalOutServiceRequest, hospital.getParentId(), hospital.getId(), ObjectId.next());
    }
}
