package com.ynhdkc.tenant.dao;

import com.ynhdkc.tenant.entity.TenantUserStructure;

import java.util.Collection;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-25 16:13
 */
public interface OrganizationStructureRepository {
    /**
     * 存储用户权限
     *
     * @param userId     用户ID
     * @param tenantIds  权限产生变动的租户ID集合
     * @param privileges 新的权限列表
     */
    void createOrUpdateUserStructure(Long userId, Collection<Long> tenantIds, Collection<TenantUserStructure> privileges);

    void deleteUserStructure(Long userId);

    void deleteUserStructure(Long userId, Long tenantId);
}
