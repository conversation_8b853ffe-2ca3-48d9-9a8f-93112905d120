package com.ynhdkc.tenant.client.model;

import lombok.Data;

import java.util.List;

@Data
public class DepartmentList4MultiLevelResponse {

    private List<Result> result;

    @Data
    public static class Result {

        private String caution;

        private List<Result> children;

        private String departmentAddress;

        private String departmentCode;

        private String departmentDescription;

        private String departmentJp;

        private String departmentName;

        private String departmentTypeCode;

        private String departmentTypeName;

        private boolean enable;

        private String firstDepartmentCode;

        private String firstDepartmentName;

        private String firstJp;

        private String hospitalCode;

        private int intSortValue;

        private String parentDepartmentCode;

        private Object rawParameters;

        private String registrationDate;

        private String registrationLevel;

        private String registrationLevelDesc;

        private String secondDepartmentCode;

        private String secondDepartmentName;

        private Object sort;
    }
}
