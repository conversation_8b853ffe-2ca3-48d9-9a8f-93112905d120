CREATE TABLE t_hospital_detail_page_cube
(
    id               BIGINT             NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    tenant_id        BIGINT             NOT NULL COMMENT '租户id',
    hospital_id      BIGINT             NOT NULL COMMENT '医院id',
    hospital_area_id bigint             not null comment '院区id',
    hospital_code    varchar(20)        null comment '医院编码',
    cube_module_id   BIGINT default 0   NULL COMMENT 'cube模块id',
    title            VARCHAR(300)       NOT NULL COMMENT '标题',
    picture          VARCHAR(300)       NOT NULL COMMENT '图片',
    url              VARCHAR(300)       NOT NULL COMMENT '跳转链接',
    sort             INT    default 255 NOT NULL COMMENT '排序',
    channels varchar(100) NOT NULL DEFAULT '' COMMENT '渠道',
    status           INT    default 0   NOT NULL COMMENT '状态：0，启动；1，禁用；',
    create_time      DATETIME           NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time      DATETIME           NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT '医院详情页cube模块表';

CREATE INDEX idx_tenant_id ON t_hospital_detail_page_cube (tenant_id);
CREATE INDEX idx_hospital_id ON t_hospital_detail_page_cube (hospital_id);
CREATE INDEX idx_cube_module_id ON t_hospital_detail_page_cube (cube_module_id);
