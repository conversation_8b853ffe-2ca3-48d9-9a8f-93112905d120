spring:
  application:
    name: dyt-tenant
  liquibase:
    change-log: classpath:db/liquibase/changelog.xml
  jackson:
    default-property-inclusion: non_null
    date-format: yyyy-MM-dd HH:mm:ss
    property-naming-strategy: SNAKE_CASE
    time-zone: Asia/Shanghai
  kafka:
    bootstrap-servers:
      - 192.168.1.227:9092
      - 192.168.1.228:9092
      - 192.168.1.229:9092
    properties:
      max.request.size: 2097152
  profiles:
    active: dev,encrypted

backend:
  swagger:
    base-package: com.ynhdkc
  kafka:
    his:
      change-log:
        enabled: true
        partitions: 3
        replication-factor: 1

backend.security:
  rpc-path-pattern: /rpc/**
  base-path-pattern: /apis/v1/tenant/**
  public-paths:
    - -ant-pattern: /test/**
    - ant-pattern: /rpc/**
    - ant-pattern: /apis/v1/tenant/customer/public/**
    - ant-pattern: /apis/v1/tenant/customer/cron/**
    - ant-pattern: /apis/v1/tenant/customer/bulletin-config/query
    - ant-pattern: /apis/v1/tenant/backend/tenant-user/sendMsg
  authorized-paths:
    - ant-pattern: /apis/v1/tenant/backend/**
      scope:
        - tenant
    - ant-pattern: /apis/v1/tenant/customer/**
      scope:
        - user

mybatis:
  mapper-locations:
    - classpath:db/mapper/*.xml

feign:
  name:
    scheduler: dyt-schedule
    data: dyt-data
    user: dyt-user
  path:
    scheduler: /rpc/v1/schedule
    data: /rpc/apis/v1/data
    user: /rpc/v1/user


hospital:
  # 需要具体到科室的查询停诊
  department-out-list:
  #    - 871044
  #    - 871023
  # 需要具体到院区的查询停诊
  hospital-area-out-list:
    - 871044
    - 871023
    - 871045
    - 871249
    - 871001
    - 871053
    - 871055
  #各个医院放号时间
  hospital-area-time-list:
    871044: '17:30:00'
    871023: '17:30:00'
    871899: '17:00:00'
    871232: '06:30:00'
    871333: '06:30:00'
    871058: '15:00:00'
    871045: '07:30:00'
    871030: '00:00:00'
    871001: '20:00:00'
    871002: '00:00:00'
    871003: '00:00:00'
    871344: '02:00:00'
    871667: '00:00:00'
    871042: '07:30:00'
    871039: '07:30:00'
    871041: '07:30:00'
    871256: '17:00:00'
    871249: '08:00:00'
    871139: '12:00:00'
    871038: '08:00:00'
    871097: '08:00:00'
    871088: '08:00:00'

#spring cache缓存更新间隔时间，单位ms
cache:
  dict-label-cache:
    evict:
      cron: "*/5 * * * * *"
tenant-user-login-config:
  password-policy: DEFAULT
  min-password-length: 8
  max-login-attempts: 5
  login-cooloff-time: 90
  multi-device-login: false
  account-lock-duration: 60
  password-expiry-days: 90
  password-history-count: 5
  password-regex: ^(?=.*[a-z])(?=.*[A-Z])(?=.*\d|.*[@$!%*?&]).{8,16}$
