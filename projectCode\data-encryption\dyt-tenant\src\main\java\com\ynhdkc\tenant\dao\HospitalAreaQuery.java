package com.ynhdkc.tenant.dao;

import backend.common.util.BaseQueryOption;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.entity.Hospital;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-27 9:52
 */
public interface HospitalAreaQuery {
    Page<Hospital> pageQueryHospitalAreaWithCategory(HospitalAreaQueryOption option);

    Page<Hospital> pageQueryHospitalArea(HospitalAreaQueryOption option);

    List<Hospital> queryHospitalArea(HospitalAreaQueryOption option);

    Hospital queryHospitalAreaById(Long hospitalAreaId);

    List<Hospital> queryHospitalAreaByParentId(Long parentId);

    List<Hospital> listHospitalsThatDependOnHis();

    Hospital queryHospitalAreaBy(String hospitalCode);

    List<Hospital> queryHospitalList(List<String> hospitalCodeList);

    @Nullable
    Hospital queryHospitalAreaBy(Long hospitalAreaId);

    @Nullable
    String getHospitalCodeBy(Long hospitalAreaId);

    List<Hospital> queryBy(List<String> hospitalAreaIds);

    List<Hospital> queryAll();

    List<Hospital> queryParentHospital(List<Long> hospitalAreaIds);

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Accessors(chain = true)
    class HospitalAreaQueryOption extends BaseQueryOption {
        @Setter(AccessLevel.NONE)
        private Integer hospitalTypeTag = 1;
        private String hospitalAreaCode;
        private Long tenantId;
        private Long hospitalId;
        private Long hospitalAreaId;
        private String name;
        private List<Integer> category;
        private Integer status;
        private Date startCreateTime;
        private Date endCreateTime;
        private Integer departmentLayer;
        private String tagDictLabel;
        private Collection<Long> includeIds;
        private Collection<Long> excludeIds;
        private Collection<Long> includeTenantIds;
        private Collection<Long> excludeTenantIds;
        private Collection<Long> includeHospitalIds;
        private Collection<Long> excludeHospitalIds;
        private Collection<Long> includeActivePaymentIds;
        private Collection<Integer> excludeCategory;

        private List<Integer> excludeStatus;

        public HospitalAreaQueryOption(Integer currentPage, Integer pageSize) {
            super(currentPage, pageSize);
        }

        public HospitalAreaQueryOption statusNotIn(Integer... status) {
            this.excludeStatus = Arrays.asList(status);
            return this;
        }
    }
}
