package com.ynhdkc.tenant.util;

import cn.hutool.core.util.StrUtil;
import javax.servlet.http.HttpServletRequest;

/**
 * tokenutil
 *
 * <AUTHOR>
 * @date 2024/4/10 16:00
 */
public class TokenUtil {

    public static String getToken(HttpServletRequest httpServletRequest){
        String authorization = httpServletRequest.getHeader("Authorization");
        if(StrUtil.isBlank(authorization)) return authorization;
        return authorization.split(" ")[1];
    }
}
