package com.ynhdkc.tenant.doctor.sort.context;

import com.ynhdkc.tenant.DytTenantApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest(classes = {DytTenantApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("dev")
@Slf4j
class DoctorSortContextTest {

    @Autowired
    private DoctorSortContext doctorSortContext;

    @Test
    void sortDoctors() {
        doctorSortContext.sortDoctors(null, "871900");
    }

}