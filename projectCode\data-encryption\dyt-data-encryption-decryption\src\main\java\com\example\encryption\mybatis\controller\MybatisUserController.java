package com.example.encryption.mybatis.controller;

import com.example.encryption.mybatis.entity.MybatisUser;
import com.example.encryption.mybatis.service.MybatisUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

/**
 * MyBatis用户控制器
 * 提供MyBatis版本的用户相关REST API，演示MyBatis环境下的加密字段使用
 */
@RestController
@RequestMapping("/api/mybatis/users")
@Validated
public class MybatisUserController {
    
    private static final Logger logger = LoggerFactory.getLogger(MybatisUserController.class);
    
    @Autowired
    private MybatisUserService userService;
    
    /**
     * 创建用户
     */
    @PostMapping
    public ResponseEntity<MybatisUser> createUser(@Valid @RequestBody MybatisUser user) {
        try {
            MybatisUser createdUser = userService.createUser(user);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdUser);
        } catch (Exception e) {
            logger.error("Failed to create MyBatis user", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }
    
    /**
     * 根据ID获取用户
     */
    @GetMapping("/{id}")
    public ResponseEntity<MybatisUser> getUserById(@PathVariable Long id) {
        Optional<MybatisUser> user = userService.getUserById(id);
        return user.map(u -> ResponseEntity.ok(u))
                  .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 获取所有用户
     */
    @GetMapping
    public ResponseEntity<List<MybatisUser>> getAllUsers() {
        List<MybatisUser> users = userService.getAllUsers();
        return ResponseEntity.ok(users);
    }
    
    /**
     * 根据用户名获取用户
     */
    @GetMapping("/username/{username}")
    public ResponseEntity<MybatisUser> getUserByUsername(@PathVariable String username) {
        Optional<MybatisUser> user = userService.getUserByUsername(username);
        return user.map(u -> ResponseEntity.ok(u))
                  .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 根据手机号获取用户
     * 注意：这个操作会比较慢，因为需要解密所有用户的手机号进行匹配
     */
    @GetMapping("/phone/{phone}")
    public ResponseEntity<MybatisUser> getUserByPhone(@PathVariable String phone) {
        Optional<MybatisUser> user = userService.getUserByPhone(phone);
        return user.map(u -> ResponseEntity.ok(u))
                  .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 根据邮箱获取用户
     * 注意：这个操作会比较慢，因为需要解密所有用户的邮箱进行匹配
     */
    @GetMapping("/email/{email}")
    public ResponseEntity<MybatisUser> getUserByEmail(@PathVariable String email) {
        Optional<MybatisUser> user = userService.getUserByEmail(email);
        return user.map(u -> ResponseEntity.ok(u))
                  .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 更新用户信息
     */
    @PutMapping("/{id}")
    public ResponseEntity<MybatisUser> updateUser(@PathVariable Long id, @Valid @RequestBody MybatisUser user) {
        try {
            user.setId(id);
            MybatisUser updatedUser = userService.updateUser(user);
            return ResponseEntity.ok(updatedUser);
        } catch (Exception e) {
            logger.error("Failed to update MyBatis user", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }
    
    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteUser(@PathVariable Long id) {
        try {
            userService.deleteUser(id);
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            logger.error("Failed to delete MyBatis user", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }
    
    /**
     * 根据状态获取用户列表
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<List<MybatisUser>> getUsersByStatus(@PathVariable MybatisUser.UserStatus status) {
        List<MybatisUser> users = userService.getUsersByStatus(status);
        return ResponseEntity.ok(users);
    }
    
    /**
     * 获取用户总数
     */
    @GetMapping("/count")
    public ResponseEntity<Long> getUserCount() {
        long count = userService.getUserCount();
        return ResponseEntity.ok(count);
    }
}
