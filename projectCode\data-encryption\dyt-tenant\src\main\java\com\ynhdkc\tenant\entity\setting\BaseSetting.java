package com.ynhdkc.tenant.entity.setting;

import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-12 13:20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class BaseSetting extends BaseEntity {

    private Long functionId;
    /**
     * 租户 ID
     */
    private Long tenantId;
    /**
     * 医院 ID
     */
    private Long hospitalId;
    /**
     * 院区 ID
     */
    private Long hospitalAreaId;

    private String hospitalCode;
}
