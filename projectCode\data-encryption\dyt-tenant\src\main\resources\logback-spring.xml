<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <springProperty scope="context" name="APP_NAME" source="spring.application.name"/>
    <springProperty scope="context" name="KAFKA_SERVERS" source="megaease.output.servers"/>
    <springProperty scope="context" name="KAFKA_TOPIC" source="backend.kafka-appender.topic"
                    defaultValue="application-log"/>
    <springProperty scope="context" name="KAFKA_APPENDER_ENABLED" source="backend.kafka-appender.enabled"
                    defaultValue="true"/>
    <property name="LOG_FILE" value="${LOG_FILE:-${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}}/spring.log}"/>
    <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>
    <include resource="org/springframework/boot/logging/logback/file-appender.xml"/>
    <if condition='property("KAFKA_APPENDER_ENABLED").equals("true")'>
        <then>
            <!-- This is the kafkaAppender -->
            <appender name="kafkaAppender" class="com.github.danielwegener.logback.kafka.KafkaAppender">
                <encoder charset="UTF-8"
                         class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                    <findAndRegisterJacksonModules>false</findAndRegisterJacksonModules>
                    <providers>
                        <timestamp>
                            <fieldName>timestamp</fieldName>
                            <pattern>[UNIX_TIMESTAMP_AS_NUMBER]</pattern>
                            <timeZone>UTC</timeZone>
                        </timestamp>
                        <pattern>
                            <pattern>
                                {
                                "service": "${APP_NAME}",
                                "traceId": "%X{traceId}",
                                "id": "%X{spanId}",
                                "logLevel": "%-5level",
                                "threadId": "%thread",
                                "location": "%logger{36}",
                                "message": "%msg%n%exception",
                                "type": "application-log"
                                }
                            </pattern>
                        </pattern>
                    </providers>
                </encoder>
                <topic>${KAFKA_TOPIC}</topic>
                <keyingStrategy class="com.github.danielwegener.logback.kafka.keying.NoKeyKeyingStrategy"/>
                <deliveryStrategy class="com.github.danielwegener.logback.kafka.delivery.AsynchronousDeliveryStrategy"/>

                <!-- Optional parameter to use a fixed partition -->
                <!-- <partition>0</partition> -->

                <!-- Optional parameter to include log timestamps into the kafka message -->
                <!-- <appendTimestamp>true</appendTimestamp> -->

                <!-- each <producerConfig> translates to regular kafka-client config (format: key=value) -->
                <!-- producer configs are documented here: https://kafka.apache.org/documentation.html#newproducerconfigs -->
                <!-- bootstrap.servers is the only mandatory producerConfig -->
                <producerConfig>bootstrap.servers=${KAFKA_SERVERS}</producerConfig>
                <!--        <producerConfig>bootstrap.servers=localhost:9092</producerConfig>-->

                <!-- this is the fallback appender if kafka is not available. -->
                <appender-ref ref="FILE"/>
            </appender>
            <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
                <!-- if neverBlock is set to true, the async appender discards messages when its internal queue is full -->
                <neverBlock>true</neverBlock>
                <appender-ref ref="kafkaAppender"/>
            </appender>
        </then>
    </if>
    <root level="info">
        <appender-ref ref="CONSOLE"/>
        <if condition='property("KAFKA_APPENDER_ENABLED").equals("true")'>
            <then>
                <appender-ref ref="ASYNC"/>
            </then>
        </if>
    </root>

</configuration>