package backend.security.oplog;

import java.lang.annotation.*;

/**
 * <ul>
 * <li>If tenantIdExpr attribute is not empty, evaluate it and compare with current user's tenantId. Reject when they are not equal.</li>
 * <li>If tenantManager attribute is true and current user is tenantManager, pass.</li>
 * <li>If value(authorities) attribute is not empty and current user have one of the authority, pass.</li>
 * <li>None of the above, reject.</li>
 * </ul>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface DytSecurityRequired {
    /**
     * required authorities
     *
     * @return
     */
    String[] value() default {};

    /**
     * required tenant manager
     *
     * @return
     */
    boolean tenantManager() default false;

    /**
     * an express used to extract tenantId
     *
     * @return
     */
    String tenantIdExpr() default "";

    /**
     * need write op log
     *
     * @return
     */
    boolean needOpLog() default false;

    /**
     *  op log only record
     *
     * @return
     */
    boolean opLogOnly() default false;

}
