package backend.common.health;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.web.servlet.handler.SimpleUrlHandlerMapping;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Configuration
public class HealthCheckConfiguration {

    @Value("${dyt.healthcheck.url:/health/dyt}")
    private String healthCheckUrl;

    @Value("${dyt.healthcheck.redis:false}")
    private boolean checkRedis;

    @Value("${dyt.healthcheck.datasource:true}")
    private boolean checkDataSource;


    @Bean
    @ConditionalOnClass(RedisConnectionFactory.class)
    @ConditionalOnProperty(name = "dyt.healthcheck.enabled", havingValue = "true", matchIfMissing = true)
    public LazyHealthIndicatorWrapper redisLazyHealthIndicatorWrapper(ApplicationContext context) {
        return LazyHealthIndicatorWrapper.createRedisLazyHealthIndicatorWrapper(context);
    }

    @Bean
    @ConditionalOnClass(DataSource.class)
    @ConditionalOnProperty(name = "dyt.healthcheck.enabled", havingValue = "true", matchIfMissing = true)
    public LazyHealthIndicatorWrapper dataSourceLazyHealthIndicatorWrapper(ApplicationContext context) {
        return LazyHealthIndicatorWrapper.createDataSourceLazyHealthIndicatorWrapper(context);
    }


    @Bean
    @ConditionalOnProperty(name = "dyt.healthcheck.enabled", havingValue = "true", matchIfMissing = true)
    public SimpleUrlHandlerMapping healthCheckHandlerMapping(List<LazyHealthIndicatorWrapper> wrappers) {
        HealthCheckController controller = new HealthCheckController(wrappers, checkDataSource, checkRedis);
        SimpleUrlHandlerMapping mapping = new ContextCloseEventListenerSimpleUrlHandlerMapping(controller);
        // Must setOrder otherwise the /** will override this mapping, and the health check will not work
        mapping.setOrder(Integer.MAX_VALUE - 2);

        Map<String, Object> urlMap = new HashMap<>();
        urlMap.put(healthCheckUrl, controller);
        mapping.setUrlMap(urlMap);
        return mapping;
    }

    static class ContextCloseEventListenerSimpleUrlHandlerMapping extends SimpleUrlHandlerMapping  implements ApplicationListener<ContextClosedEvent> {

        private final HealthCheckController controller;

        public ContextCloseEventListenerSimpleUrlHandlerMapping(HealthCheckController controller) {
            this.controller = controller;
        }

        @Override
        public void onApplicationEvent(ContextClosedEvent event) {
            controller.stopped();
        }
    }
}
