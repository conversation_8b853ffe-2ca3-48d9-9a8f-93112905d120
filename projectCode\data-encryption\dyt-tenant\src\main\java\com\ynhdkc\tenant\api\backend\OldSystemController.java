package com.ynhdkc.tenant.api.backend;

import backend.security.oplog.DytSecurityRequired;
import com.ynhdkc.tenant.handler.OldSystemApi;
import com.ynhdkc.tenant.model.DoctorGroupPageRespVo;
import com.ynhdkc.tenant.model.GetDoctorGroupPageReqDto;
import com.ynhdkc.tenant.model.GetExtensionConditionAdvertisementReqDto;
import com.ynhdkc.tenant.model.GetExtensionConditionAdvertisementRespVo;
import com.ynhdkc.tenant.service.backend.IDoctorService;
import com.ynhdkc.tenant.service.backend.IHospitalListRuleService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequiredArgsConstructor
@Api(tags = "OldSystem")
public class OldSystemController implements OldSystemApi {
    private final IDoctorService doctorService;
    private final IHospitalListRuleService hospitalListRuleService;

    @Override
    @DytSecurityRequired(needOpLog = true, value = "old:system:getDoctorGroup")
    public ResponseEntity<DoctorGroupPageRespVo> getDoctorGroupPage(GetDoctorGroupPageReqDto request) {
        return ResponseEntity.ok(doctorService.getDoctorGroupList(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "old:system:getExtensionConditionAdvertisementList")
    public ResponseEntity<GetExtensionConditionAdvertisementRespVo> getExtensionConditionAdvertisementList(GetExtensionConditionAdvertisementReqDto request) {
        return ResponseEntity.ok(hospitalListRuleService.getExtensionConditionAdvertisementList(request));
    }
}
