package backend.common.enums.order;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum OrderStatus {

    /**
     * 需要锁号 0->1->2->3->9
     * <p>
     * 0 初始状态
     * 1 锁号成功，待支付
     * -1 锁号失败
     * 2 支付成功 第三方支付机构获得用户的 money
     * -2 支付失败 第三方支付机构
     * 3 订单核实
     * -3 通知his支付失败
     * 9 订单支付最终状态
     */

    HIS_PAYED_FAILED_STATUS("his下单失败", -3),
    PAYED_FAILED_STATUS("支付机构支付失败", -2),
    LOCK_FAILED_STATUS("his锁号失败", -8),
    NO_PAYED_STATUS("支付超时结束", -1),
    INI_STATUS("初始态", 0),
    WAIT_PAY_STATUS("待支付", 1),
    PAYED_STATUS("支付机构完成支付", 2),
    HIS_PAYED_MIDDLE_STATUS("his订单核实", 3),
    FINISH_STATUS("挂号成功", 9),
    UNKNOWN("未知", 999),
    ;

    private final String value;
    private final Integer code;

    OrderStatus(String value, Integer code) {
        this.code = code;
        this.value = value;
    }

    @JsonCreator
    public static OrderStatus getFromCode(Integer code) {
        for (OrderStatus t : OrderStatus.values()) {
            if (t.code.equals(code)) {
                return t;
            }
        }
        return UNKNOWN;
    }

    public String getValue() {
        return value;
    }

    public Integer getCode() {
        return code;
    }

    @JsonValue
    public String getRequestCode() {
        return code.toString();
    }


}
