package backend.common.entity.dto.notification.request;

import backend.common.constants.ChannelConstant;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class Recipient {
    /**
     * 手机号吗
     */
    @JsonProperty("phoneNumber")
    private String phoneNumber;

    /**
     * 个推：用户cid
     */
    @JsonProperty("cid")
    private String cid;

    /**
     * 微信公众号openId
     */
    @JsonProperty("wxMpOpenId")
    private String wxMpOpenId;

    /**
     * 微信公众号标识，见 backend.common.constants.ChannelConstant
     */
    private ChannelConstant.WechatMpChannel wxMpTag = ChannelConstant.WechatMpChannel.DYT;
}
