package com.ynhdkc.tenant.service.backend.impl;

import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ynhdkc.tenant.dao.mapper.IHospitalDetailPageNavigatorMapper;
import com.ynhdkc.tenant.dao.mapper.IHospitalDetailPageSubNaviModuleMapper;
import com.ynhdkc.tenant.entity.detailpage.HospitalAreaDetailPageNavigator;
import com.ynhdkc.tenant.entity.detailpage.HospitalAreaDetailPageSubNaviModule;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.IHospitalAreaDetailPageSubNaviModuleService;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/24 16:42:12
 */
@Service
@RequiredArgsConstructor
public class HospitalAreaDetailPageSubNaviModuleServiceImpl implements IHospitalAreaDetailPageSubNaviModuleService {
    private final IHospitalDetailPageNavigatorMapper navigatorMapper;
    private final IHospitalDetailPageSubNaviModuleMapper mapper;
    private final PageVoConvert pageVoConvert;

    @Override
    public HospitalAreaDetailPageSubNaviModuleVo createHospitalAreaDetailPageSubNaviModule(CreateHospitalAreaDetailPageSubNaviModuleReqDto dto) {
        HospitalAreaDetailPageSubNaviModule entity = toEntity(dto);
        int effectiveRow = mapper.insertSelective(entity);
        if (effectiveRow == 0) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "创建医院详情页子导航模块失败");
        }
        return IHospitalAreaDetailPageSubNaviModuleService.toVo(entity);
    }

    @Override
    public BaseOperationResponse deleteHospitalAreaDetailPageSubNaviModule(Long id) {
        int effectiveRow = mapper.deleteByPrimaryKey(id);
        if (effectiveRow == 0) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "删除医院详情页子导航模块失败");
        }
        return BaseOperationResponse.builder().effectiveCount(effectiveRow).build();
    }

    @Override
    public HospitalAreaDetailPageSubNaviModuleVo getHospitalAreaDetailPageSubNaviModule(Long id) {
        HospitalAreaDetailPageSubNaviModule entity = mapper.selectByPrimaryKey(id);
        if (entity == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "医院详情页子导航模块不存在");
        }
        HospitalAreaDetailPageSubNaviModuleVo vo = IHospitalAreaDetailPageSubNaviModuleService.toVo(entity);
        importNaviVos(vo);
        return vo;
    }

    @Override
    public HospitalAreaDetailPageSubNaviModulePageVo searchHospitalAreaDetailPageSubNaviModule(SearchHospitalAreaDetailPageSubNaviModuleReqDto searchDto) {
        Page<HospitalAreaDetailPageSubNaviModule> page = PageHelper.startPage(searchDto.getCurrentPage(), searchDto.getPageSize());
        page.doSelectPage(() -> mapper.selectByExample(HospitalAreaDetailPageSubNaviModule.class, helper -> {
            helper.defGroup(consumer -> {
                if (searchDto.getTenantId() != null) {
                    consumer.andEqualTo(HospitalAreaDetailPageSubNaviModule::getTenantId, searchDto.getTenantId());
                }
                if (searchDto.getHospitalId() != null) {
                    consumer.andEqualTo(HospitalAreaDetailPageSubNaviModule::getHospitalId, searchDto.getHospitalId());
                }
                if (searchDto.getHospitalAreaId() != null) {
                    consumer.andEqualTo(HospitalAreaDetailPageSubNaviModule::getHospitalAreaId, searchDto.getHospitalAreaId());
                }
            });
            helper.builder(builder -> builder.orderByDesc(HospitalAreaDetailPageSubNaviModule::getSort));
        }));
        return pageVoConvert.toPageVo(page, HospitalAreaDetailPageSubNaviModulePageVo.class, IHospitalAreaDetailPageSubNaviModuleService::toVo);
    }

    @Override
    public HospitalAreaDetailPageSubNaviModuleVo updateHospitalAreaDetailPageSubNaviModule(Long id, UpdateHospitalAreaDetailPageSubNaviModuleReqDto dto) {
        HospitalAreaDetailPageSubNaviModule entity = mapper.selectByPrimaryKey(id);
        if (entity == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "医院详情页子导航模块不存在");
        }

        toEntity(entity, dto);

        int effectiveRow = mapper.updateByPrimaryKeySelective(entity);
        if (effectiveRow == 0) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "更新医院详情页子导航模块失败");
        }
        return IHospitalAreaDetailPageSubNaviModuleService.toVo(entity);
    }

    @Override
    public void setSubNaviModule(HospitalAreaLayoutVo vo, Long hospitalAreaId) {
        List<HospitalAreaDetailPageSubNaviModule> subNaviModules = mapper.selectByExample(HospitalAreaDetailPageSubNaviModule.class, helper -> {
            helper.defGroup(consumer -> consumer.andEqualTo(HospitalAreaDetailPageSubNaviModule::getHospitalAreaId, hospitalAreaId));
            helper.builder(builder -> builder.orderByDesc(HospitalAreaDetailPageSubNaviModule::getSort));
        });

        List<HospitalAreaDetailPageSubNaviModuleVo> subNaviModuleVos = subNaviModules
                .stream()
                .map(IHospitalAreaDetailPageSubNaviModuleService::toVo)
                .peek(naviModuleVo -> {
                    importNaviVos(naviModuleVo);
                    naviModuleVo.setSubNavigationList(naviModuleVo.getSubNavigationList()
                            .stream()
                            .sorted(Comparator.comparingInt(NaviVo::getSort))
                            .collect(Collectors.toList()));
                })
                .sorted(Comparator.comparingInt(HospitalAreaDetailPageSubNaviModuleVo::getSort))
                .collect(Collectors.toList());
        vo.setSubNavigationVo(subNaviModuleVos);
    }

    @Override
    public List<HospitalAreaDetailPageSubNaviModuleVo> getHospitalAreaDetailPageSubNaviModuleByHospitalAreaId(Long hospitalAreaId) {
        List<HospitalAreaDetailPageSubNaviModule> subNaviModules = mapper.selectByExample(HospitalAreaDetailPageSubNaviModule.class, helper -> {
            helper.defGroup(consumer -> consumer.andEqualTo(HospitalAreaDetailPageSubNaviModule::getHospitalAreaId, hospitalAreaId));
            helper.builder(builder -> builder.orderByDesc(HospitalAreaDetailPageSubNaviModule::getSort));
        });

        return subNaviModules.stream().map(IHospitalAreaDetailPageSubNaviModuleService::toVo).collect(Collectors.toList());
    }

    private void importNaviVos(HospitalAreaDetailPageSubNaviModuleVo vo) {
        List<NaviVo> naviVos = navigatorMapper.selectByExample2(HospitalAreaDetailPageNavigator.class, sql -> sql.andEqualTo(HospitalAreaDetailPageNavigator::getNaviModuleId, vo.getId()))
                .stream()
                .map(naviEntity -> {
                    NaviVo naviVo = new NaviVo();
                    naviVo.setId(naviEntity.getId());
                    naviVo.setTitle(naviEntity.getTitle());
                    naviVo.setSubTitle(naviEntity.getSubTitle());
                    naviVo.setPicture(naviEntity.getPicture());
                    naviVo.setUrl(naviEntity.getUrl());
                    naviVo.setSort(naviEntity.getSort());
                    if (StringUtils.hasText(naviEntity.getChannels())) {
                        naviVo.setChannels(Arrays.stream(naviEntity.getChannels().split(",")).map(Integer::parseInt).collect(Collectors.toList()));
                    }
                    return naviVo;
                })
                .sorted(Comparator.comparingInt(NaviVo::getSort))
                .collect(Collectors.toList());
        vo.setSubNavigationList(naviVos);
    }
}
