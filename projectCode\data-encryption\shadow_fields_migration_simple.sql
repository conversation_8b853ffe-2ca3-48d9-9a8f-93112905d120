-- =====================================================
-- 数据加密影子字段新增SQL脚本（简化版）
-- 根据《数据安全法》《个人信息保护法》整改要求
-- 用于渐进式加密迁移策略
-- 数据库：MySQL
-- 创建时间：2024-06-24
-- =====================================================

-- =====================================================
-- 1. t_tenant_user 表 - 租户用户表
-- =====================================================

-- 新增身份证号加密字段（核心数据）
ALTER TABLE t_tenant_user 
ADD COLUMN id_card_no_encrypted VARCHAR(1000) COMMENT '身份证号加密字段（影子字段）' AFTER id_card_no;

-- 新增手机号加密字段（重要数据）
ALTER TABLE t_tenant_user 
ADD COLUMN phone_number_encrypted VARCHAR(1000) COMMENT '手机号码加密字段（影子字段）' AFTER phone_number;

-- 新增用户名加密字段（重要数据）
ALTER TABLE t_tenant_user 
ADD COLUMN name_encrypted VARCHAR(1000) COMMENT '用户名加密字段（影子字段）' AFTER name;

-- =====================================================
-- 注意：t_recharge_record 表在dyt-tenant项目中不存在
-- 如果后续需要，请根据实际表结构添加相应的DDL语句
-- =====================================================

-- =====================================================
-- 2. t_tenant 表 - 租户表
-- =====================================================

-- 新增联系人手机号加密字段（重要数据）
ALTER TABLE t_tenant 
ADD COLUMN contact_phone_number_encrypted VARCHAR(1000) COMMENT '联系人手机号加密字段（影子字段）' AFTER contact_phone_number;

-- 新增联系人邮箱加密字段（重要数据）
ALTER TABLE t_tenant 
ADD COLUMN contact_email_encrypted VARCHAR(1000) COMMENT '联系人邮箱加密字段（影子字段）' AFTER contact_email;

-- =====================================================
-- 3. 验证SQL语句
-- =====================================================

-- 验证影子字段是否创建成功
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    COLUMN_TYPE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME IN ('t_tenant_user', 't_tenant')
  AND COLUMN_NAME LIKE '%_encrypted'
ORDER BY TABLE_NAME, ORDINAL_POSITION;

-- 检查表结构（可选）
-- DESCRIBE t_tenant_user;
-- DESCRIBE t_recharge_record;
-- DESCRIBE t_tenant;

-- =====================================================
-- 4. 可选索引创建（根据查询需求决定是否需要）
-- =====================================================

-- 如果需要根据加密字段进行查询，可以创建索引
-- 注意：加密字段通常不适合创建索引，因为加密后的数据是随机的
-- 以下索引仅在特殊场景下使用

-- CREATE INDEX idx_tenant_user_phone_encrypted ON t_tenant_user(phone_number_encrypted(255));
-- CREATE INDEX idx_tenant_user_name_encrypted ON t_tenant_user(name_encrypted(255));
-- CREATE INDEX idx_tenant_contact_phone_encrypted ON t_tenant(contact_phone_number_encrypted(255));
-- CREATE INDEX idx_tenant_contact_email_encrypted ON t_tenant(contact_email_encrypted(255));

-- =====================================================
-- 5. 回滚SQL语句（紧急情况使用）
-- =====================================================

/*
-- 如需回滚，请执行以下语句：

-- 删除 t_tenant_user 表的影子字段
ALTER TABLE t_tenant_user DROP COLUMN IF EXISTS phone_number_encrypted;
ALTER TABLE t_tenant_user DROP COLUMN IF EXISTS name_encrypted;

-- 删除 t_tenant 表的影子字段
ALTER TABLE t_tenant DROP COLUMN IF EXISTS contact_phone_number_encrypted;
ALTER TABLE t_tenant DROP COLUMN IF EXISTS contact_email_encrypted;
*/

-- =====================================================
-- 执行说明
-- =====================================================

/*
执行顺序：
1. 先执行影子字段新增语句（ALTER TABLE）
2. 执行验证SQL确认字段创建成功
3. 根据需要决定是否创建索引

注意事项：
1. 影子字段长度设置为1000，足够存储加密后的数据
2. 所有影子字段都允许NULL，避免影响现有业务
3. 建议在业务低峰期执行DDL操作
4. 执行前请备份相关表结构
5. 加密字段通常不需要创建索引

数据分类级别：
- 重要数据：phone_number, name, contact_phone_number, contact_email
- 注意：已移除id_card_no字段的加密（根据要求）

影子字段命名规则：
- 原字段名 + "_encrypted" 后缀
- 例如：id_card_no -> id_card_no_encrypted

字段长度说明：
- VARCHAR(1000)：足够存储AES-GCM加密后的Base64编码数据
- 原始数据越长，加密后的数据也越长，1000字符可以满足大部分场景
*/
