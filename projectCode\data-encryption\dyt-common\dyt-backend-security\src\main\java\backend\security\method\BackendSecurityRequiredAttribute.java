package backend.security.method;

import org.springframework.security.access.ConfigAttribute;

public class BackendSecurityRequiredAttribute implements ConfigAttribute {
    BackendSecurityRequired annotation;

    public BackendSecurityRequiredAttribute(BackendSecurityRequired annotation) {
        this.annotation = annotation;
    }

    public BackendSecurityRequired getAnnotation() {
        return annotation;
    }

    @Override
    public String getAttribute() {
        return null;
    }
}