package backend.common.domain.user;


import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "t_user_wechat_profile")
public class UserWechatProfileEntity extends BaseEntity {
    private Long userId;
    private String openId;
    private String nickName;
    private String headImgUrl;
    private Integer sex;
    private String province;
    private String city;
    private String county;
    private String unionid;
    private String miniOpenid;
    private Boolean subscribe;
    private Date subscribeTime;
    private String subscribeSceneCategory;
    private String scene;
    private Boolean dzjSubscribe;
    private String dzjOpenId;
    private Date dzjSubscribeTime;
}
