package com.ynhdkc.tenant.service.backend.impl;

import backend.common.constants.ChannelConstant;
import backend.common.response.BaseOperationResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.ynhdkc.tenant.model.SetLiveButtonDto;
import com.ynhdkc.tenant.service.backend.INewMediaService;
import com.ynhdkc.tenant.util.WechatMpApiUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

@Service
@Slf4j
@RequiredArgsConstructor
public class NewMediaServiceImpl implements INewMediaService {
    private final WechatMpApiUtil wechatMpApiUtil;
    private final RedisTemplate<String, String> redisTemplate;

    @Override
    public BaseOperationResponse setLiveButton(SetLiveButtonDto setLiveButtonDto) {
        for (ChannelConstant.WechatMpChannel channel : ChannelConstant.WechatMpChannel.values()) {
            JSONObject wechatMpMenu = wechatMpApiUtil.convertQueryMenuToSetMenu(wechatMpApiUtil.getWechatMpMenu(channel.getChannelCode()));
            JSONArray buttonArray = wechatMpMenu.getJSONArray("button");
            JSONObject thirdButton = buttonArray.getJSONObject(2);
            // 移除中间栏
            buttonArray.remove(1);
            JSONObject liveButton = new JSONObject().set("type", "view")
                    .set("url", setLiveButtonDto.getLiveUrl())
                    .set("name", "正在直播\uD83D\uDD34");
            // 插入直播按钮到中间栏
            buttonArray.set(1, liveButton);
            buttonArray.set(2, thirdButton);
            wechatMpApiUtil.publishWechatMpMenu(channel.getChannelCode(), new JSONObject().set("button", buttonArray).toString());
        }
        return new BaseOperationResponse();
    }

    @Override
    public BaseOperationResponse setMenu(MultipartFile file, Integer wechatMpChannel, Boolean isDefault) {
        if (!ObjectUtils.isEmpty(file)) {
            StringBuilder menuJson = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    menuJson.append(line);
                }
            } catch (IOException e) {
                log.error("读取菜单文件失败", e);
            }
            wechatMpApiUtil.publishWechatMpMenu(wechatMpChannel, menuJson.toString());
            if (isDefault) {
                // 若当前提交的菜单是默认菜单，则保存到redis
                redisTemplate.opsForValue().set(WechatMpApiUtil.redisKeyMap.get(wechatMpChannel), menuJson.toString());
            }
            return new BaseOperationResponse();
        }
        wechatMpApiUtil.publishDefaultWechatMpMenu(wechatMpChannel);

        return new BaseOperationResponse();
    }
}
