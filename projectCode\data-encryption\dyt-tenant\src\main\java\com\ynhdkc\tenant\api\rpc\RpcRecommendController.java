package com.ynhdkc.tenant.api.rpc;

import com.ynhdkc.tenant.handler.RpcRecommendConfigApi;
import com.ynhdkc.tenant.model.GetRecommendConfigPageReqDto;
import com.ynhdkc.tenant.model.RecommendConfigPageVo;
import com.ynhdkc.tenant.service.backend.RecommendConfigService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Description:
 * @Author: XieHaiBo
 * @Date 2025/2/26 14:37
 */
@Api(tags = "RpcRecommend")
@RestController
@RequiredArgsConstructor
public class RpcRecommendController implements RpcRecommendConfigApi {

    private final RecommendConfigService recommendConfigService;

    @Override
    public ResponseEntity<RecommendConfigPageVo> getRecommendConfigPage(GetRecommendConfigPageReqDto request) {
        return ResponseEntity.ok(recommendConfigService.getRecommendConfigPage(request));
    }
}
