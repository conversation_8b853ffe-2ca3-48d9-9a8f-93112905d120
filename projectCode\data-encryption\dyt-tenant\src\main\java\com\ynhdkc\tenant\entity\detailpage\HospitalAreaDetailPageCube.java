package com.ynhdkc.tenant.entity.detailpage;

import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @since 2023/5/24 19:16:56
 */
@Data
@Table(name = "t_hospital_detail_page_cube")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HospitalAreaDetailPageCube extends BaseEntity {

    private Long tenantId;

    private Long hospitalId;

    private Long hospitalAreaId;

    private String hospitalCode;

    private Long cubeModuleId;

    private String title;

    private String picture;

    private String url;

    private Integer sort;

    private String channels;

    private Integer status;

}
