package com.ynhdkc.tenant.entity.model;

import com.ynhdkc.tenant.dto.ScheduleResponseDto;
import com.ynhdkc.tenant.entity.Doctor;
import lombok.Data;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Data
public class DoctorScheduleModel {

    private List<ScheduleResponseDto.ScheduleInfo> scheduleInfoList = Collections.emptyList();

    private List<Doctor> doctors = new ArrayList<>();

}
