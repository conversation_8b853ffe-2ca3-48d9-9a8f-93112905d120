package com.ynhdkc.tenant.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
public class UserVo {
    @JsonProperty("id")
    private Long id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("phone_number")
    private String phoneNumber;

    @JsonProperty("gender")
    private Integer gender;

    @JsonProperty("id_card_no")
    private String idCardNo;

    @JsonProperty("status")
    private Integer status;

    @JsonProperty("is_token_valid")
    private Boolean isTokenValid = false;

    @JsonProperty("we_chat_profile")
    private UserWeChatProfileVo weChatProfile;

    @JsonProperty("old_we_chat_profile")
    private UserWeChatProfileVo oldWeChatProfile;

    @JsonProperty("alipay_ma_user_profile")
    private UserAlipayProfileVo alipayMaUserProfile;

    @Data
    public static class UserWeChatProfileVo {
        @JsonProperty("id")
        private Long id;

        @JsonProperty("open_id")
        private String openId;

        @JsonProperty("nick_name")
        private String nickName;

        @JsonProperty("head_img_url")
        private String headImgUrl;

        @JsonProperty("sex")
        private Integer sex;

        @JsonProperty("province")
        private String province;

        @JsonProperty("city")
        private String city;

        @JsonProperty("county")
        private String county;

        @JsonProperty("unionid")
        private String unionid;

        @JsonProperty("mini_openid")
        private String miniOpenid;

        @JsonProperty("subscribe")
        private Boolean subscribe;

        @JsonProperty("subscribe_time")
        private Date subscribeTime;

        @JsonProperty("subscribe_scene_category")
        private String subscribeSceneCategory;

        @JsonProperty("scene")
        private String scene;

        @JsonProperty("dzj_subscribe")
        private Boolean dzjSubscribe;

        @JsonProperty("dzj_open_id")
        private String dzjOpenId;

        @JsonProperty("dzj_subscribe_time")
        private Date dzjSubscribeTime;
    }

    @Data
    public static class UserAlipayProfileVo {
        @JsonProperty("id")
        private Long id;

        @JsonProperty("user_id")
        private Long userId;

        @JsonProperty("alipay_ma_user_id")
        private String alipayMaUserId;

        @JsonProperty("alipay_ma_user_name")
        private String alipayMaUserName;

        @JsonProperty("alipay_ma_user_avatar")
        private String alipayMaUserAvatar;

        @JsonProperty("province")
        private String province;

        @JsonProperty("city")
        private String city;

        @JsonProperty("gender")
        private String gender;

        @JsonProperty("create_time")
        private Date createTime;
    }
}