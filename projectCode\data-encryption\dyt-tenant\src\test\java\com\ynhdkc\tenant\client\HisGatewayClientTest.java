package com.ynhdkc.tenant.client;

import com.fasterxml.jackson.databind.json.JsonMapper;
import com.ynhdkc.tenant.DytTenantApplication;
import com.ynhdkc.tenant.client.model.DepartmentList4MultiLevelResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

@SpringBootTest(classes = {DytTenantApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("dev")
class HisGatewayClientTest {

    @Autowired
    private HisGatewayClient hisGatewayClient;

    @Test
    void getDepartmentList4MultiLevel() {
        ResponseEntity<DepartmentList4MultiLevelResponse> departmentList4MultiLevel = hisGatewayClient.getDepartmentList4MultiLevel("871058");
        if (departmentList4MultiLevel.getBody() == null) {
            return;
        }

        List<DepartmentList4MultiLevelResponse.Result> result = departmentList4MultiLevel.getBody().getResult();
        if (result == null) {
            return;
        }

        JsonMapper jsonMapper = new JsonMapper();
        try {
            System.out.println(jsonMapper.writeValueAsString(result));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}