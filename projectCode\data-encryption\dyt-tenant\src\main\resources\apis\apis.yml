swagger: "2.0"
info:
  title: tenant
  version: "1.0"
  description: 租户中心
host: localhost:9001
basePath: /apis/v1/tenant/backend
schemes:
  - http
  - https

paths:
  /dict/types:
    get:
      summary: 分页查询字典类型
      description: 分页查询字典类型
      operationId: getDictTypeList
      tags:
        - Dict
      parameters:
        - name: name
          in: query
          description: 字典类型名称
          type: string
        - name: description
          in: query
          description: 描述
          type: string
        - name: current_page
          in: query
          description: 当前页
          type: integer
          default: 1
        - name: page_size
          in: query
          description: 每页条数
          type: integer
          default: 10
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/DictTypePageVo"
    post:
      summary: 新增字典类型
      description: 新增字典类型
      operationId: createDictType
      tags:
        - Dict
      parameters:
        - name: createDictTypeDto
          in: body
          description: 新增字典类型
          required: true
          schema:
            $ref: "#/definitions/CreateDictTypeDto"
      responses:
        201:
          description: 成功
          schema:
            $ref: "#/definitions/DictTypeVo"
        400:
          description: Invalid inputs supplied

  /dict/types/{id}:
    get:
      summary: 查询字典类型详情
      description: 查询字典类型详情
      operationId: getDictTypeDetail
      tags:
        - Dict
      parameters:
        - name: id
          in: path
          description: 字典类型ID
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/DictTypeVo"
        404:
          description: Not found
    put:
      summary: 更新字典类型
      description: 更新字典类型
      operationId: updateDictType
      tags:
        - Dict
      parameters:
        - name: id
          in: path
          description: 字典类型ID
          required: true
          type: integer
          format: int64
        - name: updateDictTypeDto
          in: body
          description: 更新字典类型
          required: true
          schema:
            $ref: "#/definitions/UpdateDictTypeDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/DictTypeVo"
        400:
          description: Invalid inputs supplied
        404:
          description: Not found
    delete:
      summary: 删除字典类型
      description: 删除字典类型
      operationId: deleteDictType
      tags:
        - Dict
      parameters:
        - name: id
          in: path
          description: 字典类型ID
          required: true
          type: integer
          format: int64
      responses:
        204:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
        404:
          description: Not found

  /dict/labels:
    get:
      summary: 分页查询字典标签
      description: 分页查询字典标签
      operationId: getDictLabelList
      tags:
        - Dict
      parameters:
        - name: name
          in: query
          description: 字典标签名称
          type: string
        - name: dict_type_name
          in: query
          description: 字典类型名称
          type: string
        - name: sort_by
          in: query
          description: 按 sort 排序:asc,desc
          type: string
          enum:
            - asc
            - desc
        - name: description
          in: query
          description: 描述
          type: string
        - name: current_page
          in: query
          description: 当前页
          type: integer
          default: 1
        - name: page_size
          in: query
          description: 每页条数
          type: integer
          default: 10
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/DictLabelPageVo"
    post:
      summary: 新增字典标签
      description: 新增字典标签
      operationId: createDictLabel
      tags:
        - Dict
      parameters:
        - name: createDictLabelDto
          in: body
          description: 新增字典标签
          required: true
          schema:
            $ref: "#/definitions/CreateDictLabelDto"
      responses:
        201:
          description: 成功
          schema:
            $ref: "#/definitions/DictLabelVo"
        400:
          description: Invalid inputs supplied

  /dict/labels/{id}:
    get:
      summary: 查询字典标签详情
      description: 查询字典标签详情
      operationId: getDictLabelDetail
      tags:
        - Dict
      parameters:
        - name: id
          in: path
          description: 字典标签ID
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/DictLabelVo"
        404:
          description: Not found
    put:
      summary: 更新字典标签
      description: 更新字典标签
      operationId: updateDictLabel
      tags:
        - Dict
      parameters:
        - name: id
          in: path
          description: 字典标签 ID
          required: true
          type: integer
          format: int64
        - name: updateDictLabelDto
          in: body
          description: 更新字典标签
          required: true
          schema:
            $ref: "#/definitions/UpdateDictLabelDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/DictLabelVo"
        400:
          description: Invalid inputs supplied
        404:
          description: Not found
    delete:
      summary: 删除字典标签
      description: 删除字典标签
      operationId: deleteDictLabel
      tags:
        - Dict
      parameters:
        - name: id
          in: path
          description: 字典标签ID
          required: true
          type: integer
          format: int64
      responses:
        204:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
        404:
          description: Not found

  /dict/info/sync:
    get:
      summary: 同步字典信息
      description: 同步字典信息
      operationId: syncDictInfo
      tags:
        - Dict
      responses:
        200:
          description: 成功

  /tenants:
    post:
      summary: 新增租户
      description: 新增租户
      operationId: create
      tags:
        - Tenant
      parameters:
        - name: create
          in: body
          description: 新增租户
          required: true
          schema:
            $ref: "#/definitions/TenantCreateReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/TenantDetailVo"

  /tenants/query:
    post:
      summary: 分页查询租户
      description: 分页查询租户
      operationId: query
      tags:
        - Tenant
      parameters:
        - name: request
          in: body
          schema:
            $ref: "#/definitions/TenantQueryReqDto"
          required: true
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/TenantPageVo"

  /tenants/{tenant-id}:
    get:
      summary: 查询租户详情
      description: 查询租户详情
      operationId: getDetail
      tags:
        - Tenant
      parameters:
        - name: tenant-id
          in: path
          description: 租户ID
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/TenantDetailVo"
        404:
          description: Not found
    put:
      summary: 更新租户
      description: 更新租户
      operationId: update
      tags:
        - Tenant
      parameters:
        - name: tenant-id
          in: path
          description: 租户ID
          required: true
          type: integer
          format: int64
        - name: updateTenantDto
          in: body
          description: 更新租户
          required: true
          schema:
            $ref: "#/definitions/TenantUpdateReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/TenantDetailVo"
        400:
          description: Invalid inputs supplied
        404:
          description: Not found
    delete:
      summary: 删除租户
      description: 删除租户
      operationId: delete
      tags:
        - Tenant
      parameters:
        - name: tenant-id
          in: path
          description: 租户ID
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
        404:
          description: Not found

  /users:
    post:
      summary: 新增租户用户
      description: 新增租户用户
      operationId: create
      tags:
        - TenantUser
      parameters:
        - name: request
          in: body
          description: 新增租户用户
          required: true
          schema:
            $ref: "#/definitions/TenantUserCreateReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/TenantUserDetailVo"
        400:
          description: Invalid inputs supplied
  /users/{id}/check-password:
    get:
      summary: 校验密码(密码强度和密码是否过期)
      description: 校验密码(密码强度和密码是否过期)
      operationId: checkPassword
      tags:
        - TenantUser
      parameters:
        - name: id
          in: path
          description: 租户用户ID
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CheckTenantUserPasswordResultVo"
        400:
          description: Invalid inputs supplied
  /users/detail:
    get:
      summary: 查询当前用户详情
      description: 查询当前用户详情
      operationId: getCurrentUserDetail
      tags:
        - TenantUser
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/TenantUserDetailVo"
        404:
          description: Not found
    put:
      summary: 租户用户更新账户信息
      description: 租户用户更新账户信息
      operationId: modify
      tags:
        - TenantUser
      parameters:
        - name: request
          in: body
          required: true
          schema:
            $ref: "#/definitions/TenantUserModifyReqDto"
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"

  /uesrs/query:
    post:
      summary: 分页查询租户用户
      description: 分页查询租户用户
      operationId: query
      tags:
        - TenantUser
      parameters:
        - name: request
          in: body
          schema:
            $ref: "#/definitions/TenantUserQueryReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/TenantUserPageVo"

  /users/{id}:
    get:
      summary: 查询租户用户详情
      description: 查询租户用户详情
      operationId: getDetail
      tags:
        - TenantUser
      parameters:
        - name: id
          in: path
          description: 租户用户ID
          required: true
          type: integer
          format: int64
        - name: tenant_id
          in: query
          description: 租户ID
          required: false
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/TenantUserDetailVo"
        404:
          description: Not found
    put:
      summary: 更新租户用户
      description: 更新租户用户
      operationId: update
      tags:
        - TenantUser
      parameters:
        - name: id
          in: path
          description: 租户用户 ID
          required: true
          type: integer
          format: int64
        - name: request
          in: body
          description: 更新租户用户
          required: true
          schema:
            $ref: "#/definitions/TenantUserUpdateReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/TenantUserDetailVo"
        400:
          description: Invalid inputs supplied
        404:
          description: Not found
    delete:
      summary: 删除租户用户
      description: 删除租户用户
      operationId: delete
      tags:
        - TenantUser
      parameters:
        - name: id
          in: path
          description: 租户用户ID
          required: true
          type: integer
          format: int64
      responses:
        204:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
        404:
          description: Not found
  /users/{id}/bind-tenant:
    put:
      summary: 绑定租户用户
      description: 绑定租户用户
      operationId: bindTenantUser
      tags:
        - TenantUser
      parameters:
        - name: id
          in: path
          description: 租户用户 ID
          required: true
          type: integer
          format: int64
        - name: userTenantPrivilegeConfigList
          in: body
          description: 绑定租户用户
          required: true
          schema:
            type: array
            items:
              $ref: "#/definitions/UserTenantPrivilegeConfig"
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
        400:
          description: Invalid inputs supplied
        404:
          description: Not found
    post:
      summary: 解绑租户用户
      description: 解绑租户用户
      operationId: unBindTenantUser
      tags:
        - TenantUser
      parameters:
        - name: id
          in: path
          description: 用户 ID
          required: true
          type: integer
          format: int64
        - name: tenantIdList
          in: body
          description: 租户ID集合  # 修正描述
          required: true
          schema:
            type: array
            items:
              type: integer
              format: int64
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
        400:
          description: Invalid inputs supplied
        404:
          description: Not found
  /users/{id}/bound-tenant:
    get:
      summary: 查询用户已绑定租户
      operationId: queryUserBoundTenant
      tags:
        - TenantUser
      parameters:
        - name: id
          in: path
          description: 租户用户ID
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/TenantUserBindingTenantVo"

  /users/bound-tenant:
    get:
      summary: 查询当前用户可读取租户
      operationId: queryCurrentUserBoundTenant
      tags:
        - TenantUser
      parameters:
        - name: tenant_name
          in: query
          type: string
          required: false
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/TenantUserBindingTenantVo"

  /hospitals:
    post:
      summary: 新增医院
      description: 新增医院
      operationId: create
      tags:
        - Hospital
      parameters:
        - name: request
          in: body
          description: 新增医院
          required: true
          schema:
            $ref: "#/definitions/HospitalCreateReqDto"
      responses:
        201:
          description: 成功
          schema:
            $ref: "#/definitions/HospitalDetailVo"
        400:
          description: Invalid inputs supplied

  /hospitals/{hospital-id}:
    get:
      summary: 查询医院详情
      description: 查询医院详情
      operationId: get
      tags:
        - Hospital
      parameters:
        - name: hospital-id
          in: path
          description: 医院id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/HospitalDetailVo"
        404:
          description: Not found
    put:
      summary: 更新医院
      description: 更新医院
      operationId: update
      tags:
        - Hospital
      parameters:
        - name: hospital-id
          in: path
          description: 医院id
          required: true
          type: integer
          format: int64
        - name: request
          in: body
          description: 医院更新传输对象
          required: true
          schema:
            $ref: "#/definitions/HospitalUpdateReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/HospitalDetailVo"
    delete:
      summary: 删除医院
      description: 删除医院
      operationId: delete
      tags:
        - Hospital
      parameters:
        - name: hospital-id
          in: path
          description: 医院id
          required: true
          type: integer
          format: int64
        - name: tenant_id
          in: query
          description: 租户id
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
        404:
          description: Not found

  /hospitals/query:
    post:
      summary: 分页查询医院
      description: 分页查询医院
      operationId: query
      tags:
        - Hospital
      parameters:
        - name: request
          in: body
          schema:
            $ref: "#/definitions/HospitalQueryReqDto"
          required: true
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/HospitalPageVo"

  /hospitals/sync:
    get:
      summary: 同步医院以及科室的所有信息至 Kafka
      tags:
        - Hospital
      description: 同步医院以及科室的所有信息至 Kafka
      operationId: sync
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"

  /hospitals/hospital-ares-query-labels:
    post:
      summary: 查询院区组合名
      tags:
        - Hospital
      description: 查询院区组合名
      operationId: queryHospitalAreaQueryLabels
      parameters:
        - name: request
          in: body
          schema:
            $ref: "#/definitions/HospitalAreaQueryLabelsReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/HospitalAreaQueryLabelsVo"

  /hospital-areas:
    post:
      summary: 新增院区
      tags:
        - HospitalArea
      description: 新增院区
      operationId: create
      parameters:
        - name: request
          in: body
          schema:
            $ref: "#/definitions/HospitalAreaCreateReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/HospitalAreaDetailVo"

  /hospital-areas/query:
    post:
      summary: 分页查询院区
      tags:
        - HospitalArea
      description: 分页查询院区
      operationId: query
      parameters:
        - name: request
          in: body
          schema:
            $ref: "#/definitions/HospitalAreaQueryReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/HospitalAreaPageVo"

  /hospital-areas/{hospital-area-id}:
    get:
      summary: 查询院区详情
      tags:
        - HospitalArea
      operationId: get
      parameters:
        - name: hospital-area-id
          in: path
          description: 院区id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/HospitalAreaDetailVo"
    put:
      summary: 更新院区
      tags:
        - HospitalArea
      operationId: update
      parameters:
        - name: hospital-area-id
          in: path
          description: 院区id
          required: true
          type: integer
          format: int64
        - name: request
          in: body
          schema:
            $ref: "#/definitions/HospitalAreaUpdateReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/HospitalAreaDetailVo"
    delete:
      summary: 删除院区
      tags:
        - HospitalArea
      description: 删除院区
      operationId: delete
      parameters:
        - name: hospital-area-id
          in: path
          description: 院区id
          required: true
          type: integer
          format: int64
        - name: tenant_id
          in: query
          description: 租户id
          type: integer
          format: int64
        - name: hospital_id
          in: query
          description: 医院id
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"

  /hospital-areas/{id}/sync_from_his:
    get:
      summary: 从 HIS 同步科室信息
      description: 同步科室信息
      tags:
        - HospitalArea
      operationId: syncFromHis
      parameters:
        - name: id
          in: path
          description: 院区 ID
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: OK
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"

  /hospital-areas/{hospital-area-id}/layout:
    get:
      summary: 查询院区布局
      tags:
        - HospitalArea
      operationId: getLayout
      parameters:
        - name: hospital-area-id
          in: path
          description: 院区id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/HospitalAreaLayoutVo"

  /hospital-areas/departments/info/query:
    post:
      summary: 分页查询院区科室概况
      tags:
        - HospitalArea
      operationId: queryHospitalAreaDepartmentInfo
      parameters:
        - name: request
          in: body
          schema:
            $ref: "#/definitions/HospitalAreaDepartmentsInfoQueryReqDto"
      responses:
        200:
          description: OK
          schema:
            $ref: "#/definitions/HospitalAreaDepartmentsInfoPageVo"

  /hospital-areas/{hospital-area-id}/settings:
    get:
      summary: 查询院区配置
      tags:
        - HospitalArea
      operationId: getSettingDetail
      parameters:
        - name: hospital-area-id
          in: path
          description: 院区id
          required: true
          type: integer
          format: int64
        - name: tenant_id
          in: query
          description: 租户id
          type: integer
          format: int64
        - name: hospital_id
          in: query
          description: 医院id
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/HospitalAreaSettingDetailVo"
    post:
      summary: 新增院区自定义配置
      description: 新增院区自定义配置，一次只能新增一个自定义配置
      tags:
        - HospitalArea
      operationId: createSetting
      parameters:
        - name: hospital-area-id
          in: path
          description: 院区id
          required: true
          type: integer
          format: int64
        - name: request
          in: body
          schema:
            $ref: "#/definitions/HospitalAreaSettingCreateReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/HospitalAreaSettingDetailVo"
    put:
      summary: 更新院区配置
      tags:
        - HospitalArea
      operationId: updateSetting
      parameters:
        - name: hospital-area-id
          in: path
          description: 院区id
          required: true
          type: integer
          format: int64
        - name: request
          in: body
          schema:
            $ref: "#/definitions/HospitalAreaSettingUpdateReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/HospitalAreaSettingDetailVo"

  /hospital-areas/{hospital-area-id}/function:
    get:
      summary: 查询院区功能清单
      description: 查询院区功能清单，功能取决于是否新增了相应的配置
      tags:
        - HospitalArea
      operationId: getFunctionDetail
      parameters:
        - name: hospital-area-id
          in: path
          description: 院区id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/HospitalAreaFunctionDetailVo"
    put:
      summary: 更新院区功能清单
      tags:
        - HospitalArea
      operationId: updateFunction
      description: 更新院区功能清单，采用增量更新的方式，即只更新传入的功能项，不传的功能项不做处理
      parameters:
        - name: hospital-area-id
          in: path
          description: 院区id
          required: true
          type: integer
          format: int64
        - name: request
          in: body
          required: true
          schema:
            $ref: "#/definitions/HospitalAreaFunctionUpdateReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/HospitalAreaFunctionDetailVo"

  /hospital-areas/batch-set-stop-generate-schedule:
    post:
      summary: 批量设置停止生成排班
      description: 批量设置停止生成排班
      tags:
        - HospitalArea
      operationId: batchSetStopGenerateSchedule
      parameters:
        - name: request
          in: body
          required: true
          schema:
            $ref: "#/definitions/HospitalAreaBatchSetStopGenerateScheduleReqDto"
      responses:
        200:
          description: 成功

  /hospital-areas/appoint-notify-config/{hospital_code}:
    get:
      summary: 获取医院预约成功通知者
      tags:
        - HospitalArea
      operationId: getAppointNotifyConfig
      parameters:
        - name: hospital_code
          in: path
          description: 医院编码
          required: true
          type: string
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/AppointNotifyConfigVo"
    put:
      summary: 设置医院预约成功通知者
      tags:
        - HospitalArea
      operationId: setAppointNotifyConfig
      parameters:
        - name: hospital_code
          in: path
          description: 医院编码
          required: true
          type: string
        - name: admins
          in: body
          required: true
          schema:
            $ref: "#/definitions/SetAppointNotifyConfigReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/SetAppointNotifyConfigResponse"
  /hospital-areas/appoint-notify-config/{hospital_id}/{department_id}:
    get:
      summary: 获取医院科室预约成功通知者
      tags:
        - Department
      operationId: getAppointNotifyConfig
      parameters:
        - name: hospital_id
          in: path
          description: 医院编码
          required: true
          type: string
        - name: department_id
          in: path
          description: 科室编码
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/AppointNotifyConfigVo"
    put:
      summary: 设置医院科室预约成功通知者
      tags:
        - Department
      operationId: setAppointNotifyConfig
      parameters:
        - name: hospital_id
          in: path
          description: 医院编码
          required: true
          type: string
        - name: department_id
          in: path
          description: 科室编码
          required: true
          type: integer
          format: int64
        - name: admins
          in: body
          required: true
          schema:
            $ref: "#/definitions/SetAppointNotifyConfigReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/SetAppointNotifyConfigResponse"



  /departments:
    post:
      summary: 新增科室
      description: 新增科室
      operationId: create
      tags:
        - Department
      parameters:
        - name: request
          in: body
          required: true
          schema:
            $ref: "#/definitions/DepartmentCreateReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/DepartmentDetailVo"
        400:
          description: Invalid inputs supplied

  /departments/query:
    post:
      summary: 分页查询科室
      description: 分页查询科室
      operationId: query
      tags:
        - Department
      parameters:
        - name: request
          in: body
          schema:
            $ref: "#/definitions/DepartmentQueryReqDto"
          required: true
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/DepartmentPageVo"
        400:
          description: Invalid inputs supplied

  /departments/{department-id}:
    get:
      summary: 查询科室详情
      description: 查询科室详情
      operationId: getDetail
      tags:
        - Department
      parameters:
        - name: department-id
          in: path
          description: 科室id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/DepartmentDetailVo"
        404:
          description: Not found
    put:
      summary: 更新科室
      description: 更新科室
      operationId: update
      tags:
        - Department
      parameters:
        - name: department-id
          in: path
          description: 科室id
          required: true
          type: integer
          format: int64
        - name: update
          in: body
          description: 更新科室
          required: true
          schema:
            $ref: "#/definitions/DepartmentUpdateReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/DepartmentDetailVo"
        400:
          description: Invalid inputs supplied
        404:
          description: Not found
    delete:
      summary: 删除科室
      description: 删除科室
      operationId: delete
      tags:
        - Department
      parameters:
        - name: department-id
          in: path
          description: 科室id
          required: true
          type: integer
          format: int64
        - name: tenant_id
          in: query
          type: integer
          format: int64
          description: 租户id
        - name: hospital_id
          in: query
          type: integer
          format: int64
          description: 医院id
        - name: hospital_area_id
          in: query
          type: integer
          format: int64
          description: 院区id
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
        404:
          description: Not found

  /departments/{id}/sync-department-doctors-from-his:
    get:
      summary: 从 HIS 同步科室医生信息
      description: 同步科室医生信息
      tags:
        - Department
      operationId: syncDepartmentDoctorsFromHis
      parameters:
        - name: id
          in: path
          description: 科室 ID
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: OK

  /departments/departments/query/table:
    post:
      summary: 查询院区下所有科室
      tags:
        - Department
      operationId: queryByTable
      parameters:
        - name: request
          in: body
          schema:
            $ref: "#/definitions/TableDepartmentQueryReqDto"
      responses:
        200:
          description: OK
          schema:
            $ref: "#/definitions/TableDepartmentQueryVo"

  /departments/query/tree:
    post:
      summary: 查询院区科室树
      description: 查询院区科室树
      tags:
        - Department
      operationId: queryByTree
      parameters:
        - name: request
          in: body
          schema:
            $ref: "#/definitions/TreeDepartmentQueryReqDto"
      responses:
        200:
          description: OK
          schema:
            $ref: "#/definitions/TreeDepartmentQueryVo"

  /departments/tree/search:
    post:
      summary: 根据院区查询科室树
      description: 根据院区查询科室树
      tags:
        - Department
      operationId: queryTree
      parameters:
        - name: request
          in: body
          schema:
            $ref: "#/definitions/DepartmentsTreeSearchReqDto"
      responses:
        200:
          description: OK
          schema:
            $ref: "#/definitions/DepartmentsTreeSearchListVo"

  /departments/tree/query:
    post:
      summary: 根据院区查询科室树
      description: 根据院区查询科室树
      tags:
        - Department
      operationId: queryTree2
      parameters:
        - name: request
          in: body
          schema:
            $ref: "#/definitions/DepartmentsTreeSearchReqDto"
      responses:
        200:
          description: OK
          schema:
            $ref: "#/definitions/DepartmentsTreeSearchListVo"
  /departments/page:
    post:
      summary: 分页查询科室
      description: 分页查询科室
      tags:
        - Department
      operationId: queryPage
      parameters:
        - name: request
          in: body
          schema:
            $ref: "#/definitions/DepartmentsTreeSearchPageReqDto"
      responses:
        200:
          description: OK
          schema:
            $ref: "#/definitions/DepartmentsTreeSearchPageVo"

  /doctors:
    post:
      summary: 新增医生
      description: 新增医生
      operationId: create
      tags:
        - Doctor
      parameters:
        - name: request
          in: body
          description: 新增医生传输模型
          schema:
            $ref: "#/definitions/DoctorCreateReqDto"
      responses:
        201:
          description: 成功
          schema:
            $ref: "#/definitions/DoctorDetailVo"
        400:
          description: Invalid inputs supplied

  /doctors/sync:
    get:
      summary: 同步所有医生的信息至 Kafka
      description: 同步所有医生的信息至 Kafka
      operationId: sync
      tags:
        - Doctor
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"

  /doctors/sync_info_by_hospital_area_id:
    get:
      summary: 从his同步医师资料
      description: 从his同步医师资料
      operationId: syncInfoByHospitalAreaId
      parameters:
        - name: hospital_area_id
          in: query
          type: integer
          format: int64
          description: 院区id
      tags:
        - Doctor
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
  /doctors/sync_info_by_department_id:
    get:
      summary: 从his同步医师资料
      description: 从his同步医师资料
      operationId: syncInfoByDepartmentId
      parameters:
        - name: department_id
          in: query
          type: integer
          format: int64
          description: 科室id
      tags:
        - Doctor
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
  /doctors/sync_info_by_doctor_id:
    get:
      summary: 从his同步医师资料
      description: 从his同步医师资料
      operationId: syncInfoByDoctorId
      parameters:
        - name: doctor_id
          in: query
          type: integer
          format: int64
          description: 医生id
      tags:
        - Doctor
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
  /doctors/sync_yunda_doctor_info:
    get:
      summary: 从云达同步医师资料
      description: 从云达同步医师资料
      operationId: syncYundaDoctorInfo
      tags:
        - Doctor
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"

  /doctors/query:
    post:
      summary: 分页查询医生
      description: 分页查询医生
      operationId: query
      tags:
        - Doctor
      parameters:
        - name: request
          in: body
          description: 查询医生列表
          required: true
          schema:
            $ref: "#/definitions/DoctorQueryReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/DoctorPageVo"
        400:
          description: Invalid inputs supplied
  /doctors/sort:
    get:
      summary: 对医生重新排序
      description: 对医生重新排序
      operationId: reSortDoctor
      tags:
        - Doctor
      parameters:
        - name: hospital_area_id
          in: query
          type: integer
          description: 院区id
          format: int64
        - name: department_id
          in: query
          type: integer
          format: int64
          description: 科室id

      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
        404:
          description: Not found
  /doctors/{doctor-id}:
    get:
      summary: 查询医生详情
      description: 查询医生详情
      operationId: getDetail
      tags:
        - Doctor
      parameters:
        - name: doctor-id
          in: path
          description: 医生id
          required: true
          type: integer
          format: int64
        - name: tenant_id
          in: query
          type: integer
          format: int64
          description: 租户id
        - name: hospital_id
          in: query
          type: integer
          format: int64
          description: 医院id
        - name: hospital_area_id
          in: query
          type: integer
          format: int64
          description: 院区id
        - name: department_id
          in: query
          type: integer
          format: int64
          description: 科室id
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/DoctorDetailVo"
        404:
          description: Not found
    put:
      summary: 更新医生
      description: 更新医生
      operationId: update
      tags:
        - Doctor
      parameters:
        - name: doctor-id
          in: path
          description: 医生id
          required: true
          type: integer
          format: int64
        - name: request
          in: body
          description: 更新医生传输模型
          schema:
            $ref: "#/definitions/DoctorUpdateReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/DoctorDetailVo"
        400:
          description: Invalid inputs supplied
        404:
          description: Not found
    delete:
      summary: 删除医生
      description: 删除医生
      operationId: delete
      tags:
        - Doctor
      parameters:
        - name: doctor-id
          in: path
          description: 医生id
          required: true
          type: integer
          format: int64
        - name: tenant_id
          in: query
          type: integer
          format: int64
          description: 租户id
        - name: hospital_id
          in: query
          type: integer
          format: int64
          description: 医院id
        - name: hospital_area_id
          in: query
          type: integer
          format: int64
          description: 院区id
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
        404:
          description: Not found

  /doctors/bind-group:
    post:
      summary: 医生绑定医生团队
      description: 医生绑定医生团队
      operationId: bindDoctorGroup
      tags:
        - Doctor
      parameters:
        - name: request
          in: body
          description: 医生绑定医生团队
          required: true
          schema:
            $ref: "#/definitions/DoctorBindGroupReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/DoctorGroupRelationVo"

  /doctors/unbind-group:
    post:
      summary: 医生解绑医生团队
      description: 医生解绑医生团队
      operationId: unbindDoctorGroup
      tags:
        - Doctor
      parameters:
        - name: request
          in: body
          description: 医生解绑医生团队
          required: true
          schema:
            $ref: "#/definitions/DoctorUnbindGroupReqDto"
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"

  /doctors/bind-group/batch:
    post:
      summary: 医生团队批量绑定医生
      description: 医生团队批量绑定医生
      operationId: batchBindDoctorGroup
      tags:
        - Doctor
      parameters:
        - name: request
          in: body
          description: 医生绑定医生团队
          required: true
          schema:
            $ref: "#/definitions/DoctorBatchBindGroupReqDto"
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"

  /doctors/bind-group/query:
    post:
      summary: 查询医生绑定的医生团队
      description: 查询医生绑定的医生团队
      operationId: queryDoctorGroup
      tags:
        - Doctor
      parameters:
        - name: request
          in: body
          required: true
          schema:
            $ref: "#/definitions/DoctorGroupRelationQueryReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/DoctorGroupRelationQueryRespDto"

  /doctors/batch_sync_doctor_info_from_specified_doctor:
    post:
      summary: 从指定医生同步医生信息
      description: 从指定医生同步医生信息
      operationId: batchSyncDoctorInfoFromSpecifiedDoctor
      tags:
        - Doctor
      parameters:
        - name: request
          in: body
          required: true
          schema:
            $ref: "#/definitions/BatchSyncDoctorInfoFromSpecifiedDoctorReqDto"
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"

  /hospital-area-position/buildings:
    post:
      summary: 新增医院大楼
      description: 新增医院大楼
      operationId: createBuilding
      tags:
        - HospitalAreaPosition
      parameters:
        - name: request
          in: body
          description: 新增医院大楼模型
          required: true
          schema:
            $ref: "#/definitions/BuildingCreateReqDto"
      responses:
        201:
          description: 成功
          schema:
            $ref: "#/definitions/BuildingVo"
        400:
          description: Invalid inputs supplied

  /hospital-area-position/buildings/query:
    post:
      summary: 分页查询医院大楼
      description: 分页查询医院大楼
      operationId: queryBuilding
      tags:
        - HospitalAreaPosition
      parameters:
        - name: request
          in: body
          description: 查询医院大楼列表
          required: true
          schema:
            $ref: "#/definitions/BuildingQueryReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/BuildingPageVo"
        400:
          description: Invalid inputs supplied

  /hospital-area-position/buildings/{building-id}:
    get:
      summary: 查询医院大楼详情
      description: 查询医院大楼详情
      operationId: getBuildingDetail
      tags:
        - HospitalAreaPosition
      parameters:
        - name: building-id
          in: path
          description: 医院大楼id
          required: true
          type: integer
          format: int64
        - name: tenant_id
          in: query
          type: integer
          format: int64
          description: 租户id
        - name: hospital_id
          in: query
          type: integer
          format: int64
          description: 医院id
        - name: hospital_area_id
          in: query
          type: integer
          format: int64
          description: 院区id
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/BuildingVo"
        404:
          description: Not found
    put:
      summary: 更新医院大楼
      description: 更新医院大楼
      operationId: updateBuilding
      tags:
        - HospitalAreaPosition
      parameters:
        - name: building-id
          in: path
          description: 医院大楼id
          required: true
          type: integer
          format: int64
        - name: request
          in: body
          description: 更新医院大楼
          required: true
          schema:
            $ref: "#/definitions/BuildingUpdateReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/BuildingVo"
        400:
          description: Invalid inputs supplied
        404:
          description: Not found
    delete:
      summary: 删除医院大楼
      description: 删除医院大楼
      operationId: deleteBuilding
      tags:
        - HospitalAreaPosition
      parameters:
        - name: building-id
          in: path
          description: 医院大楼id
          required: true
          type: integer
          format: int64
        - name: tenant_id
          in: query
          description: 租户id
          required: true
          type: integer
          format: int64
        - name: hospital_id
          in: query
          description: 医院id
          required: true
          type: integer
          format: int64
        - name: hospital_area_id
          in: query
          description: 医院区域id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
        404:
          description: Not found

  /hospital-area-position/floors:
    post:
      summary: 新增楼层
      description: 新增楼层
      operationId: createFloor
      tags:
        - HospitalAreaPosition
      parameters:
        - name: request
          in: body
          required: true
          schema:
            $ref: "#/definitions/FloorCreateReqDto"
      responses:
        201:
          description: 成功
          schema:
            $ref: "#/definitions/FloorVo"
        400:
          description: Invalid inputs supplied

  /hospital-area-position/floors/query:
    post:
      summary: 分页查询楼层
      description: 分页查询楼层
      operationId: queryFloor
      tags:
        - HospitalAreaPosition
      parameters:
        - name: request
          in: body
          description: 楼层查询请求
          required: true
          schema:
            $ref: "#/definitions/FloorQueryReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/FloorPageVo"
        404:
          description: Not found

  /hospital-area-position/floors/{floor-id}:
    get:
      summary: 查询楼层详情
      description: 查询楼层详情
      operationId: getFloorDetail
      tags:
        - HospitalAreaPosition
      parameters:
        - name: floor-id
          in: path
          description: 楼层id
          required: true
          type: integer
          format: int64
        - name: tenant_id
          in: query
          description: 租户id
          required: true
          type: integer
          format: int64
        - name: hospital_id
          in: query
          description: 医院id
          required: true
          type: integer
          format: int64
        - name: hospital_area_id
          in: query
          description: 医院区域id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/FloorVo"
        404:
          description: Not found
    put:
      summary: 更新楼层
      description: 更新楼层
      operationId: updateFloor
      tags:
        - HospitalAreaPosition
      parameters:
        - name: floor-id
          in: path
          description: 楼层id
          required: true
          type: integer
          format: int64
        - name: request
          in: body
          description: 更新楼层传输模型
          required: true
          schema:
            $ref: "#/definitions/FloorUpdateReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/FloorVo"
        400:
          description: Invalid inputs supplied
        404:
          description: Not found
    delete:
      summary: 删除楼层
      description: 删除楼层
      operationId: deleteFloor
      tags:
        - HospitalAreaPosition
      parameters:
        - name: floor-id
          in: path
          description: 楼层id
          required: true
          type: integer
          format: int64
        - name: tenant_id
          in: query
          description: 租户id
          required: true
          type: integer
          format: int64
        - name: hospital_id
          in: query
          description: 医院id
          required: true
          type: integer
          format: int64
        - name: hospital_area_id
          in: query
          description: 医院区域id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
        404:
          description: Not found

  /hospital-area-position/areas:
    post:
      summary: 新增区域
      description: 新增区域
      operationId: createArea
      tags:
        - HospitalAreaPosition
      parameters:
        - name: request
          in: body
          description: 新增区域
          required: true
          schema:
            $ref: "#/definitions/AreaCreateReqDto"
      responses:
        201:
          description: 成功
          schema:
            $ref: "#/definitions/AreaVo"
        400:
          description: Invalid inputs supplied

  /hospital-area-position/areas/query:
    post:
      summary: 分页查询区域
      description: 分页查询区域
      operationId: queryArea
      tags:
        - HospitalAreaPosition
      parameters:
        - name: request
          in: body
          description: 区域查询请求
          required: true
          schema:
            $ref: "#/definitions/AreaQueryReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/AreaPageVo"
        404:
          description: Not found

  /hospital-area-position/areas/{area-id}:
    get:
      summary: 查询区域详情
      description: 查询区域详情
      operationId: getAreaDetail
      tags:
        - HospitalAreaPosition
      parameters:
        - name: area-id
          in: path
          description: 区域id
          required: true
          type: integer
          format: int64
        - name: tenant_id
          in: query
          type: integer
          format: int64
          description: 租户id
        - name: hospital_id
          in: query
          type: integer
          format: int64
          description: 医院id
        - name: hospital_area_id
          in: query
          type: integer
          format: int64
          description: 院区id
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/AreaVo"
        404:
          description: Not found
    put:
      summary: 更新区域
      description: 更新区域
      operationId: updateArea
      tags:
        - HospitalAreaPosition
      parameters:
        - name: area-id
          in: path
          description: 区域id
          required: true
          type: integer
          format: int64
        - name: request
          in: body
          description: 更新区域传输模型
          required: true
          schema:
            $ref: "#/definitions/AreaUpdateReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/AreaVo"
        400:
          description: Invalid inputs supplied
        404:
          description: Not found
    delete:
      summary: 删除区域
      description: 删除区域
      operationId: deleteArea
      tags:
        - HospitalAreaPosition
      parameters:
        - name: area-id
          in: path
          description: 区域id
          required: true
          type: integer
          format: int64
        - name: tenant_id
          in: query
          description: 租户id
          required: true
          type: integer
          format: int64
        - name: hospital_id
          in: query
          description: 医院id
          required: true
          type: integer
          format: int64
        - name: hospital_area_id
          in: query
          description: 医院区域id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
        404:
          description: Not found
  /hospital-areas/{hospital-area-id}/doctor-schedule-cache:
    delete:
      summary: 删除指定院区科室医生排班信息缓存
      tags:
        - HospitalArea
      operationId: deleteDepartmentCache
      parameters:
        - name: hospital-area-id
          in: path
          description: 院区id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/BaseOperationResponse"

  /hospital-area-position/locations:
    post:
      summary: 创建院区位置
      description: 创建院区位置
      operationId: createLocation
      tags:
        - HospitalAreaPosition
      parameters:
        - name: request
          in: body
          required: true
          schema:
            $ref: "#/definitions/LocationCreateReqDto"
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"

  /hospital-area-position/locations/{location-id}:
    put:
      summary: 更新院区位置
      description: 更新院区位置
      operationId: updateLocation
      tags:
        - HospitalAreaPosition
      parameters:
        - name: location-id
          in: path
          description: 位置id
          required: true
          type: integer
          format: int64
        - name: request
          in: body
          required: true
          schema:
            $ref: "#/definitions/LocationUpdateReqDto"
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
    delete:
      summary: 删除院区位置
      description: 删除院区位置
      operationId: deleteLocation
      tags:
        - HospitalAreaPosition
      parameters:
        - name: location-id
          in: path
          required: true
          type: integer
          format: int64
        - name: tenant_id
          in: query
          description: 租户id
          required: false
          type: integer
          format: int64
        - name: hospital_id
          in: query
          description: 医院id
          required: true
          type: integer
          format: int64
        - name: hospital_area_id
          in: query
          description: 院区id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
  /desensitization-white-list:
    post:
      summary: 创建脱敏白名单条目
      description: 创建一个新的脱敏白名单条目。
      operationId: createDesensitizationWhiteList
      tags:
        - DesensitizationWhiteList
      parameters:
        - in: body
          name: request
          required: true
          schema:
            $ref: '#definitions/WhiteListCreateReqDto'
      responses:
        200:
          description: 成功创建
          schema:
            $ref: "#/definitions/DesensitizationWhiteListVo"
    get:
      summary: 分页查询脱敏白名单条目
      description: 按页查询脱敏白名单条目列表。
      operationId: page
      tags:
        - DesensitizationWhiteList
      parameters:
        - in: query
          name: page
          description: 当前页码
          required: false
          type: integer
          format: int32
          default: 1
        - in: query
          name: size
          description: 每页记录数
          required: false
          type: integer
          format: int32
          default: 10
        - in: query
          name: tenant_name
          description: 租户名称
          required: false
          type: string
        - in: query
          name: tenant_user_name
          description: 租户用户名称
          required: false
          type: string
      responses:
        200:
          description: 成功查询
          schema:
            $ref: '#/definitions/WhiteListPageVo'

  /desensitization-white-list/{id}:
    put:
      summary: 更新脱敏白名单条目
      description: 更新现有的脱敏白名单条目。
      operationId: updateDesensitizationWhiteList
      tags:
        - DesensitizationWhiteList
      parameters:
        - in: path
          name: id
          description: 条目id
          required: true
          type: integer
          format: int64
        - in: body
          name: request
          required: true
          schema:
            $ref: '#/definitions/WhiteListUpdateReqDto'
      responses:
        200:
          description: 成功更新
          schema:
            $ref: '#/definitions/DesensitizationWhiteListVo'
    get:
      summary: 查询脱敏白名单条目
      description: 根据提供的条目ID查询一个具体的脱敏白名单条目。
      operationId: getDesensitizationWhiteListById
      tags:
        - DesensitizationWhiteList
      parameters:
        - in: path
          name: id
          description: 条目id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功查询
          schema:
            $ref: '#/definitions/DesensitizationWhiteListVo'
        404:
          description: 条目未找到
    delete:
      summary: 删除脱敏白名单条目
      description: 删除指定的脱敏白名单条目。
      operationId: deleteDesensitizationWhiteList
      tags:
        - DesensitizationWhiteList
      parameters:
        - in: path
          name: id
          description: id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功删除
          schema:
            $ref: '#/definitions/BaseOperationResponse'
  /hospital-area-position/tree:
    post:
      summary: 查询院区位置树
      description: 查询院区位置树
      operationId: queryPositionTree
      tags:
        - HospitalAreaPosition
      parameters:
        - name: request
          in: body
          required: true
          schema:
            $ref: "#/definitions/HospitalAreaPositionTreeQueryReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/HospitalAreaPositionTreeQueryRespDto"

  /hospital-area-position/locations/query:
    post:
      summary: 查询院区位置
      description: 查询院区位置
      operationId: queryLocation
      tags:
        - HospitalAreaPosition
      parameters:
        - name: request
          in: body
          required: true
          schema:
            $ref: "#/definitions/HospitalAreaPositionQueryReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/HospitalAreaPositionQueryRespDto"

  /regions:
    post:
      summary: 新增地区
      tags:
        - Region
      operationId: create
      parameters:
        - name: request
          in: body
          schema:
            $ref: "#/definitions/RegionCreateReqDto"
          required: true
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/RegionVo"

  /regions/{region-id}:
    put:
      summary: 更新地区信息
      tags:
        - Region
      operationId: update
      parameters:
        - name: region-id
          in: path
          type: integer
          format: int64
          required: true
        - name: request
          in: body
          schema:
            $ref: "#/definitions/RegionUpdateReqDto"
          required: true
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/RegionVo"
    delete:
      summary: 删除地区
      tags:
        - Region
      operationId: delete
      parameters:
        - name: region-id
          in: path
          type: integer
          format: int64
          required: true
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"

  /regions/query:
    post:
      summary: 分页查询地区
      tags:
        - Region
      operationId: query
      parameters:
        - name: request
          in: body
          schema:
            $ref: "#/definitions/RegionQueryReqDto"
          required: true
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/RegionPageVo"

  /organization-structure/structure/sync:
    get:
      summary: 同步组织架构
      tags:
        - OrganizationStructure
      operationId: syncOrganizationStructure
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"

  /organization-structure/users/sync:
    get:
      summary: 同步用户组织架构权限
      tags:
        - OrganizationStructure
      operationId: syncUserStructure
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"

  /hospital_list_index:
    post:
      operationId: submitHospitalListIndex
      consumes:
        - application/json
      description: 新增医院序列
      parameters:
        - description: 医院序列
          in: body
          name: hospitalListIndexDtoList
          required: true
          schema:
            type: array
            items:
              $ref: '#/definitions/SubmitHospitalListIndexReqDto'
      produces:
        - application/json
      responses:
        200:
          description: 新增的结果
          schema:
            type: array
            items:
              $ref: '#/definitions/HospitalListIndexVo'
      summary: 新增医院序列
      tags:
        - HospitalListIndex
    get:
      operationId: getHospitalListIndex
      description: 分页查询医院
      summary: 分页查询医院
      tags:
        - HospitalListIndex
      responses:
        200:
          description: 成功
          schema:
            type: array
            items:
              $ref: "#/definitions/HospitalListIndexVo"

  /hospital_list_group:
    post:
      operationId: createHospitalListGroup
      consumes:
        - application/json
      description: 新增医院组
      parameters:
        - description: 新增医院组参数
          in: body
          name: hospitalListGroup
          required: true
          schema:
            $ref: '#/definitions/CreateHospitalListGroupReqDto'
      produces:
        - application/json
      responses:
        200:
          description: 新增的结果
          schema:
            $ref: '#/definitions/HospitalListGroupVo'
      summary: 新增医院组
      tags:
        - HospitalListGroup
    get:
      operationId: getHospitalListGroupPage
      description: 查询医院组分页
      summary: 分页查询医院组
      tags:
        - HospitalListGroup
      parameters:
        - name: current_page
          in: query
          description: 当前页
          default: 1
          type: integer
          minimum: 1
        - name: page_size
          in: query
          description: 每页大小
          default: 10
          type: integer
          minimum: 1
        - name: hospital_count
          in: query
          description: 医院数量
          type: integer
          minimum: 1
          required: false
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/HospitalListGroupPageVo"

  /hospital_list_group/{id}:
    put:
      operationId: updateHospitalListGroup
      consumes:
        - application/json
      produces:
        - application/json
      description: 更新医院组
      summary: 更新医院组
      tags:
        - HospitalListGroup
      parameters:
        - name: id
          in: path
          required: true
          description: 医院组id
          type: integer
          format: int64
        - in: body
          description: 更新医院组
          name: hospitalListGroupDto
          required: true
          schema:
            $ref: '#/definitions/UpdateHospitalListGroupReqDto'
      responses:
        200:
          description: 更新结果
          schema:
            $ref: '#/definitions/HospitalListGroupVo'
    delete:
      operationId: deleteHospitalListGroup
      description: 删除医院组
      summary: 删除医院组
      tags:
        - HospitalListGroup
      parameters:
        - name: id
          in: path
          description: 医院组id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 删除结果
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
    get:
      operationId: getHospitalListGroupDetail
      description: 查询医院组详情
      summary: 查询医院组详情
      tags:
        - HospitalListGroup
      parameters:
        - name: id
          in: path
          description: 医院组id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: '#/definitions/HospitalListGroupDetailVo'
        404:
          description: Not found

  /hospital_list_group/{id}/bind_hospital:
    post:
      operationId: bindHospitals
      consumes:
        - application/json
      description: 医院组绑定医院
      parameters:
        - in: path
          name: id
          description: 医院组id
          type: integer
          format: int64
          required: true
        - description: 医院ids
          in: body
          name: hospitalIds
          required: true
          schema:
            type: array
            items:
              $ref: "#/definitions/GroupBindHospitalReqDto"
      produces:
        - application/json
      responses:
        200:
          description: 绑定的结果
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
      summary: 医院组绑定医院
      tags:
        - HospitalListGroup

  /hospital_list_rule:
    post:
      operationId: createHospitalListRule
      consumes:
        - application/json
      description: 新增医院列表规则
      parameters:
        - description: 新增医院列表规则参数
          in: body
          name: hospitalListRuleDto
          required: true
          schema:
            $ref: '#/definitions/CreateHospitalListRuleReqDto'
      produces:
        - application/json
      responses:
        200:
          description: 新增的结果
          schema:
            $ref: '#/definitions/HospitalListRuleVo'
      summary: 新增医院列表规则
      tags:
        - HospitalListRule
    get:
      operationId: getHospitalListRulePage
      description: 分页查询医院列表规则
      summary: 分页查询医院列表规则
      tags:
        - HospitalListRule
      parameters:
        - name: current_page
          in: query
          description: 当前页
          default: 1
          type: integer
          minimum: 1
        - name: page_size
          in: query
          description: 每页大小
          default: 10
          type: integer
          minimum: 1
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/HospitalListRulePageVo"

  /hospital_list_rule/{id}:
    put:
      operationId: updateHospitalListRule
      consumes:
        - application/json
      produces:
        - application/json
      description: 更新医院列表规则
      summary: 更新医院列表规则
      tags:
        - HospitalListRule
      parameters:
        - name: id
          in: path
          required: true
          description: 医院列表规则id
          type: integer
          format: int64
        - in: body
          description: 更新医院列表规则
          name: hospitalListRuleDto
          required: true
          schema:
            $ref: '#/definitions/UpdateHospitalListRuleReqDto'
      responses:
        200:
          description: 更新结果
          schema:
            $ref: '#/definitions/HospitalListRuleVo'
    delete:
      operationId: deleteHospitalListRule
      description: 删除医院列表规则
      summary: 删除医院列表规则
      tags:
        - HospitalListRule
      parameters:
        - name: id
          in: path
          description: 医院列表规则id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 删除结果
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
    get:
      operationId: getHospitalListRuleDetail
      description: 查询医院列表规则详情
      summary: 查询医院列表规则详情
      tags:
        - HospitalListRule
      parameters:
        - name: id
          in: path
          description: 医院列表规则id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: '#/definitions/HospitalListRuleVo'
        404:
          description: Not found

  /hospital_list_rule/{id}/hospitals:
    get:
      operationId: getHospitalListByRuleId
      description: 通过规则查询医院列表
      summary: 通过规则查询医院列表
      tags:
        - HospitalListRule
      parameters:
        - name: id
          in: path
          description: 医院列表规则id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            type: array
            items:
              $ref: '#/definitions/HospitalVo'
        404:
          description: Not found

  /hospital_list_display:
    post:
      operationId: createHospitalListDisplay
      consumes:
        - application/json
      description: 新增医院显示
      parameters:
        - description: 新增医院显示参数
          in: body
          name: hospitalListDisplay
          required: true
          schema:
            $ref: '#/definitions/CreateHospitalListDisplayReqDto'
      produces:
        - application/json
      responses:
        200:
          description: 新增的结果
          schema:
            $ref: '#/definitions/HospitalListDisplayVo'
      summary: 新增医院显示
      tags:
        - HospitalListDisplay
    get:
      operationId: getHospitalListDisplayPage
      description: 分页查询医院显示
      summary: 分页查询医院显示
      tags:
        - HospitalListDisplay
      parameters:
        - name: current_page
          in: query
          description: 当前页
          default: 1
          type: integer
          minimum: 1
        - name: page_size
          in: query
          description: 每页大小
          default: 10
          type: integer
          minimum: 1
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/HospitalListDisplayPageVo"

  /hospital_list_display/{id}:
    put:
      operationId: updateHospitalListDisplay
      consumes:
        - application/json
      produces:
        - application/json
      description: 更新医院显示
      summary: 更新医院显示
      tags:
        - HospitalListDisplay
      parameters:
        - name: id
          in: path
          required: true
          description: 医院显示id
          type: integer
          format: int64
        - in: body
          description: 更新医院显示
          name: hospitalListDisplayDto
          required: true
          schema:
            $ref: '#/definitions/UpdateHospitalListDisplayReqDto'
      responses:
        200:
          description: 更新结果
          schema:
            $ref: '#/definitions/HospitalListDisplayVo'
    delete:
      operationId: deleteHospitalListDisplay
      description: 删除医院显示
      summary: 删除医院显示
      tags:
        - HospitalListDisplay
      parameters:
        - name: id
          in: path
          description: 医院显示id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 删除结果
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
    get:
      operationId: getHospitalListDisplayDetail
      description: 查询医院显示详情
      summary: 查询医院显示详情
      tags:
        - HospitalListDisplay
      parameters:
        - name: id
          in: path
          description: 医院显示id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: '#/definitions/HospitalListDisplayVo'
        404:
          description: Not found

  /customer-hospitals:
    post:
      tags:
        - Hospital
      summary: 医院列表
      description: 医院列表
      operationId: queryHospitalListPage
      produces:
        - application/json
      parameters:
        - name: queryHospitalPageDto
          in: body
          description: queryHospitalPageDto
          required: true
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: '#/definitions/QueryCustomerHospitalPageReqDto'
      responses:
        200:
          description: successful operation
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: '#/definitions/CustomerHospitalPageVo'
        404:
          description: Not found

  /hospital-area-detail/page-configs:
    post:
      tags:
        - HospitalAreaDetail
      summary: 新增医院详情配置
      description: 新增医院详情配置
      operationId: createHospitalAreaDetailPageConfig
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: createHospitalDetailPageConfigReqDto
          required: true
          schema:
            $ref: '#/definitions/CreateHospitalAreaDetailPageConfigReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/HospitalAreaDetailPageConfigVo'
        404:
          description: Not found

  /hospital-area-detail/page-configs/search:
    post:
      tags:
        - HospitalAreaDetail
      summary: 搜索医院详情配置
      description: 搜索医院详情配置
      operationId: searchHospitalAreaDetailPageConfig
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: searchHospitalDetailPageConfigReqDto
          required: true
          schema:
            $ref: '#/definitions/SearchHospitalAreaDetailPageConfigReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/HospitalAreaDetailPageConfigPageVo'
        404:
          description: Not found

  /hospital-area-detail/page-configs/{config-id}:
    put:
      tags:
        - HospitalAreaDetail
      summary: 更新医院详情配置
      description: 更新医院详情配置
      operationId: updateHospitalAreaDetailPageConfig
      produces:
        - application/json
      parameters:
        - name: config-id
          in: path
          description: config-id
          required: true
          type: integer
          format: int64
        - name: request
          in: body
          description: updateHospitalDetailPageConfigReqDto
          required: true
          schema:
            $ref: '#/definitions/UpdateHospitalAreaDetailPageConfigReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/HospitalAreaDetailPageConfigVo'
        404:
          description: Not found
    get:
      tags:
        - HospitalAreaDetail
      summary: 查询医院详情配置
      description: 查询医院详情配置
      operationId: getHospitalAreaDetailPageConfig
      produces:
        - application/json
      parameters:
        - name: config-id
          in: path
          description: id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/HospitalAreaDetailPageConfigVo'
        404:
          description: Not found
    delete:
      tags:
        - HospitalAreaDetail
      summary: 删除医院详情配置
      description: 删除医院详情配置
      operationId: deleteHospitalAreaDetailPageConfig
      produces:
        - application/json
      parameters:
        - name: config-id
          in: path
          description: id
          required: true
          type: integer
          format: int64
        - name: tenant_id
          in: query
          description: 租户 ID
          required: true
          type: integer
          format: int64
        - name: hospital_id
          in: query
          description: 医院 ID
          required: true
          type: integer
          format: int64
        - name: hospital_area_id
          in: query
          description: 院区 ID
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: successful operation
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: '#/definitions/BaseOperationResponse'
        404:
          description: Not found

  /hospital-area-detail/page-sub-navi-modules:
    post:
      tags:
        - HospitalAreaDetail
      summary: 新增医院详情子导航模块
      description: 新增医院详情子导航模块
      operationId: createHospitalAreaDetailPageSubNaviModule
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: createHospitalDetailPageSubNaviModuleReqDto
          required: true
          schema:
            $ref: '#/definitions/CreateHospitalAreaDetailPageSubNaviModuleReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/HospitalAreaDetailPageSubNaviModuleVo'
        404:
          description: Not found

  /hospital-area-detail/page-sub-navi-modules/search:
    post:
      tags:
        - HospitalAreaDetail
      summary: 搜索医院详情子导航模块
      description: 搜索医院详情子导航模块
      operationId: searchHospitalAreaDetailPageSubNaviModule
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: searchHospitalDetailPageSubNaviModuleReqDto
          required: true
          schema:
            $ref: '#/definitions/SearchHospitalAreaDetailPageSubNaviModuleReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/HospitalAreaDetailPageSubNaviModulePageVo'
        404:
          description: Not found

  /hospital-area-detail/page-sub-navi-modules/{module-id}:
    get:
      tags:
        - HospitalAreaDetail
      summary: 查询医院详情子导航模块
      description: 查询医院详情子导航模块
      operationId: getHospitalAreaDetailPageSubNaviModule
      produces:
        - application/json
      parameters:
        - name: module-id
          in: path
          description: id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/HospitalAreaDetailPageSubNaviModuleVo'
        404:
          description: Not found
    put:
      tags:
        - HospitalAreaDetail
      summary: 更新医院详情子导航模块
      description: 更新医院详情子导航模块
      operationId: updateHospitalAreaDetailPageSubNaviModule
      produces:
        - application/json
      parameters:
        - name: module-id
          in: path
          description: id
          required: true
          type: integer
          format: int64
        - name: request
          in: body
          description: updateHospitalDetailPageSubNaviModuleReqDto
          required: true
          schema:
            $ref: '#/definitions/UpdateHospitalAreaDetailPageSubNaviModuleReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/HospitalAreaDetailPageSubNaviModuleVo'
        404:
          description: Not found
    delete:
      tags:
        - HospitalAreaDetail
      summary: 删除医院详情子导航模块
      description: 删除医院详情子导航模块
      operationId: deleteHospitalAreaDetailPageSubNaviModule
      produces:
        - application/json
      parameters:
        - name: module-id
          in: path
          description: id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: successful operation
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: '#/definitions/BaseOperationResponse'
        404:
          description: Not found

  /hospital-area-detail/page-sub-navi-modules/get-by-hospital-area/{hospital-area-id}:
    get:
      tags:
        - HospitalAreaDetail
      summary: 根据院区查询所有副导航模块
      description: 根据院区查询所有副导航模块
      operationId: getHospitalAreaDetailPageSubNaviModuleByHospitalAreaId
      produces:
        - application/json
      parameters:
        - name: hospital-area-id
          in: path
          description: hospital_area_id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: successful operation
          schema:
            type: array
            items:
              $ref: '#/definitions/HospitalAreaDetailPageSubNaviModuleVo'
        404:
          description: Not found

  /hospital-area-detail/page-navigators:
    post:
      tags:
        - HospitalAreaDetail
      summary: 新增医院详情导航
      description: 新增医院详情导航
      operationId: createHospitalAreaDetailPageNavigator
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: createHospitalDetailPageNavigatorReqDto
          required: true
          schema:
            $ref: '#/definitions/CreateHospitalAreaDetailPageNavigatorReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/HospitalAreaDetailPageNavigatorVo'
        404:
          description: Not found

  /hospital-area-detail/page-navigators/get-by-hospital-area/{hospital-area-id}:
    get:
      tags:
        - HospitalAreaDetail
      summary: 查询医院详情导航
      description: 查询医院详情导航
      operationId: getHospitalAreaDetailPageNavigator
      produces:
        - application/json
      parameters:
        - name: hospital-area-id
          in: path
          description: 院区 id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/HospitalAreaDetailPageNavigatorVo'
        404:
          description: Not found
    put:
      tags:
        - HospitalAreaDetail
      summary: 更新医院详情导航
      description: 更新医院详情导航
      operationId: updateHospitalAreaDetailPageNavigator
      produces:
        - application/json
      parameters:
        - name: hospital-area-id
          in: path
          description: 院区 id
          required: true
          type: integer
          format: int64
        - name: request
          in: body
          description: updateHospitalDetailPageNavigatorReqDto
          required: true
          schema:
            $ref: '#/definitions/UpdateHospitalAreaDetailPageNavigatorReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/HospitalAreaDetailPageNavigatorVo'
        404:
          description: Not found

  /hospital-area-detail/page-navigators/{navigator-id}:
    delete:
      tags:
        - HospitalAreaDetail
      summary: 删除医院详情导航
      description: 删除医院详情导航
      operationId: deleteHospitalAreaDetailPageNavigator
      produces:
        - application/json
      parameters:
        - name: navigator-id
          in: path
          description: id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: successful operation
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: '#/definitions/BaseOperationResponse'
        404:
          description: Not found

  /hospital-area-detail/page-cube-modules:
    post:
      tags:
        - HospitalAreaDetail
      summary: 新增医院详情立方体模块
      description: 新增医院详情立方体模块
      operationId: createHospitalAreaDetailPageCubeModule
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: createHospitalDetailPageCubeModuleReqDto
          required: true
          schema:
            $ref: '#/definitions/CreateHospitalAreaDetailPageCubeModuleReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/HospitalAreaDetailPageCubeModuleVo'
        404:
          description: Not found

  /hospital-area-detail/page-cube-modules/search:
    post:
      tags:
        - HospitalAreaDetail
      summary: 搜索医院详情立方体模块
      description: 搜索医院详情立方体模块
      operationId: searchHospitalAreaDetailPageCubeModule
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: searchHospitalDetailPageCubeModuleReqDto
          required: true
          schema:
            $ref: '#/definitions/SearchHospitalAreaDetailPageCubeModuleReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/HospitalAreaDetailPageCubeModulePageVo'
        404:
          description: Not found

  /hospital-area-detail/page-cube-modules/{cube-module-id}:
    get:
      tags:
        - HospitalAreaDetail
      summary: 查询医院详情立方体模块
      description: 查询医院详情立方体模块
      operationId: getHospitalAreaDetailPageCubeModule
      produces:
        - application/json
      parameters:
        - name: cube-module-id
          in: path
          description: id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/HospitalAreaDetailPageCubeModuleVo'
        404:
          description: Not found
    put:
      tags:
        - HospitalAreaDetail
      summary: 更新医院详情立方体模块
      description: 更新医院详情立方体模块
      operationId: updateHospitalAreaDetailPageCubeModule
      produces:
        - application/json
      parameters:
        - name: cube-module-id
          in: path
          description: id
          required: true
          type: integer
          format: int64
        - name: request
          in: body
          description: updateHospitalDetailPageCubeModuleReqDto
          required: true
          schema:
            $ref: '#/definitions/UpdateHospitalAreaDetailPageCubeModuleReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/HospitalAreaDetailPageCubeModuleVo'
        404:
          description: Not found
    delete:
      tags:
        - HospitalAreaDetail
      summary: 删除医院详情立方体模块
      description: 删除医院详情立方体模块
      operationId: deleteHospitalAreaDetailPageCubeModule
      produces:
        - application/json
      parameters:
        - name: cube-module-id
          in: path
          description: id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: successful operation
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: '#/definitions/BaseOperationResponse'
        404:
          description: Not found

  /hospital-area-detail/page-cube-modules/get-by-hospital_area/{hospital-area-id}:
    get:
      tags:
        - HospitalAreaDetail
      summary: 根据院区查询医院详情立方体模块集合
      description: 根据院区查询医院详情立方体模块集合
      operationId: getHospitalDetailPageCubeModuleByHospitalAreaId
      produces:
        - application/json
      parameters:
        - name: hospital-area-id
          in: path
          description: hospitalAreaId
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: successful operation
          schema:
            type: array
            items:
              $ref: '#/definitions/HospitalAreaDetailPageCubeModuleVo'
        404:
          description: Not found

  /hospital-area-detail/page-cubes:
    post:
      tags:
        - HospitalAreaDetail
      summary: 新增医院详情魔方
      description: 新增医院详情魔方
      operationId: createHospitalAreaDetailPageCube
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: createHospitalDetailPageCubeReqDto
          required: true
          schema:
            $ref: '#/definitions/CreateHospitalAreaDetailPageCubeReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/HospitalAreaDetailPageCubeVo'
        404:
          description: Not found

  /hospital-area-detail/page-cubes/search:
    post:
      tags:
        - HospitalAreaDetail
      summary: 搜索医院详情魔方
      description: 搜索医院详情魔方
      operationId: searchHospitalAreaDetailPageCube
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: searchHospitalDetailPageCubeReqDto
          required: true
          schema:
            $ref: '#/definitions/SearchHospitalAreaDetailPageCubeReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/HospitalAreaDetailPageCubePageVo'
        404:
          description: Not found

  /hospital-area-detail/page-cubes/{cube-id}:
    get:
      tags:
        - HospitalAreaDetail
      summary: 查询医院详情魔方
      description: 查询医院详情魔方
      operationId: getHospitalAreaDetailPageCube
      produces:
        - application/json
      parameters:
        - name: cube-id
          in: path
          description: id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/HospitalAreaDetailPageCubeVo'
        404:
          description: Not found
    put:
      tags:
        - HospitalAreaDetail
      summary: 更新医院详情魔方
      description: 更新医院详情魔方
      operationId: updateHospitalAreaDetailPageCube
      produces:
        - application/json
      parameters:
        - name: cube-id
          in: path
          description: id
          required: true
          type: integer
          format: int64
        - name: request
          in: body
          description: updateHospitalDetailPageCubeReqDto
          required: true
          schema:
            $ref: '#/definitions/UpdateHospitalAreaDetailPageCubeReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/HospitalAreaDetailPageCubeVo'
        404:
          description: Not found
    delete:
      tags:
        - HospitalAreaDetail
      summary: 删除医院详情魔方
      description: 删除医院详情魔方
      operationId: deleteHospitalAreaDetailPageCube
      produces:
        - application/json
      parameters:
        - name: cube-id
          in: path
          description: id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: successful operation
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: '#/definitions/BaseOperationResponse'
        404:
          description: Not found

  /hospital-area-detail/page-tabs:
    post:
      tags:
        - HospitalAreaDetail
      summary: 新增医院详情页tab
      description: 新增医院详情页tab
      operationId: createHospitalAreaDetailPageTab
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: createHospitalDetailPageTabReqDto
          required: true
          schema:
            $ref: '#/definitions/CreateHospitalAreaDetailPageTabReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/HospitalAreaDetailPageTabVo'
        404:
          description: Not found

  /hospital-area-detail/page-tabs/search:
    post:
      tags:
        - HospitalAreaDetail
      summary: 搜索医院详情页tab
      description: 搜索医院详情页tab
      operationId: searchHospitalAreaDetailPageTab
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: searchHospitalDetailPageTabReqDto
          required: true
          schema:
            $ref: '#/definitions/SearchHospitalAreaDetailPageTabReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/HospitalAreaDetailPageTabPageVo'
        404:
          description: Not found

  /hospital-area-detail/page-tabs/{tab-id}:
    get:
      tags:
        - HospitalAreaDetail
      summary: 查询医院详情页tab
      description: 查询医院详情页tab
      operationId: getHospitalDetailPageTab
      produces:
        - application/json
      parameters:
        - name: tab-id
          in: path
          description: id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/HospitalAreaDetailPageTabVo'
        404:
          description: Not found
    put:
      tags:
        - HospitalAreaDetail
      summary: 更新医院详情页tab
      description: 更新医院详情页tab
      operationId: updateHospitalAreaDetailPageTab
      produces:
        - application/json
      parameters:
        - name: tab-id
          in: path
          description: id
          required: true
          type: integer
          format: int64
        - name: updateHospitalDetailPageTabReqDto
          in: body
          description: updateHospitalDetailPageTabReqDto
          required: true
          schema:
            $ref: '#/definitions/UpdateHospitalAreaDetailPageTabReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/HospitalAreaDetailPageTabVo'
        404:
          description: Not found
    delete:
      tags:
        - HospitalAreaDetail
      summary: 删除医院详情页tab
      description: 删除医院详情页tab
      operationId: deleteHospitalAreaDetailPageTab
      produces:
        - application/json
      parameters:
        - name: tab-id
          in: path
          description: id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: successful operation
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: '#/definitions/BaseOperationResponse'
        404:
          description: Not found

  /hospital-area-detail/page-tabs/get-by-hospital-area/{hospital-area-id}:
    get:
      tags:
        - HospitalAreaDetail
      summary: 通过院区查询医院详情页tab集合
      description: 通过院区查询医院详情页tab集合
      operationId: getHospitalAreaDetailPageTabByHospitalAreaId
      produces:
        - application/json
      parameters:
        - name: hospital-area-id
          in: path
          description: hospitalAreaId
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: successful operation
          schema:
            type: array
            items:
              $ref: '#/definitions/HospitalAreaDetailPageTabVo'
        404:
          description: Not found

  /departments/request_from_his:
    get:
      tags:
        - XXLJob
      summary: 定时任务
      description: 发起从 HIS 读取科室的请求
      operationId: requestDepartmentsFromHis
      responses:
        200:
          description: 成功

  /doctors/request_from_his:
    get:
      tags:
        - XXLJob
      summary: 定时任务
      description: 发起从 HIS 读取科室下医生的请求
      operationId: requestDepartmentsDoctorsFromHis
      responses:
        200:
          description: 成功

  /vaccine/cate:
    post:
      tags:
        - Vaccine
      summary: 新增疫苗分类
      description: 新增疫苗分类
      operationId: createVaccineCategory
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: createVaccineCategoryReqDto
          required: true
          schema:
            $ref: '#/definitions/CreateVaccineCategoryReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/VaccineCategoryVo'
        404:
          description: Not found

  /vaccine/cate/page:
    post:
      tags:
        - Vaccine
      summary: 分页查询疫苗分类
      description: 分页查询疫苗分类
      operationId: getVaccineCategoryPage
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: getVaccineCategoryPageReqDto
          required: true
          schema:
            $ref: '#/definitions/GetVaccineCategoryPageReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/VaccineCategoryPageVo'
        404:
          description: Not found

  /vaccine/cate/{id}:
    get:
      tags:
        - Vaccine
      summary: 查询疫苗分类详情
      description: 查询疫苗分类详情
      operationId: getVaccineCategory
      produces:
        - application/json
      parameters:
        - name: id
          in: path
          description: id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/VaccineCategoryVo'
        404:
          description: Not found
    put:
      tags:
        - Vaccine
      summary: 更新疫苗分类
      description: 更新疫苗分类
      operationId: updateVaccineCategory
      produces:
        - application/json
      parameters:
        - name: id
          in: path
          description: id
          required: true
          type: integer
          format: int64
        - name: request
          in: body
          description: updateVaccineCategoryReqDto
          required: true
          schema:
            $ref: '#/definitions/UpdateVaccineCategoryReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/VaccineCategoryVo'
        404:
          description: Not found
    delete:
      tags:
        - Vaccine
      summary: 删除疫苗分类
      description: 删除疫苗分类
      operationId: deleteVaccineCategory
      produces:
        - application/json
      parameters:
        - name: id
          in: path
          description: id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: successful operation
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: '#/definitions/BaseOperationResponse'
        404:
          description: Not found

  /vaccine/page:
    post:
      tags:
        - Vaccine
      summary: 查询疫苗分页列表
      description: 查询疫苗分页列表
      operationId: getVaccinePage
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: getVaccinePageReqDto
          required: true
          schema:
            $ref: '#/definitions/GetVaccinePageReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/VaccinePageVo'
        404:
          description: Not found

  /vaccine:
    post:
      tags:
        - Vaccine
      summary: 新增疫苗
      description: 新增疫苗
      operationId: createVaccine
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: createVaccineReqDto
          required: true
          schema:
            $ref: '#/definitions/CreateVaccineReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/VaccineVo'
        404:
          description: Not found

  /vaccine/{id}:
    get:
      tags:
        - Vaccine
      summary: 查询疫苗详情
      description: 查询疫苗详情
      operationId: getVaccine
      produces:
        - application/json
      parameters:
        - name: id
          in: path
          description: id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/VaccineVo'
        404:
          description: Not found
    put:
      tags:
        - Vaccine
      summary: 更新疫苗
      description: 更新疫苗
      operationId: updateVaccine
      produces:
        - application/json
      parameters:
        - name: id
          in: path
          description: id
          required: true
          type: integer
          format: int64
        - name: request
          in: body
          description: updateVaccineReqDto
          required: true
          schema:
            $ref: '#/definitions/UpdateVaccineReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/VaccineVo'
        404:
          description: Not found
    delete:
      tags:
        - Vaccine
      summary: 删除疫苗
      description: 删除疫苗
      operationId: deleteVaccine
      produces:
        - application/json
      parameters:
        - name: id
          in: path
          description: id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: successful operation
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: '#/definitions/BaseOperationResponse'
        404:
          description: Not found

  /recommend-config:
    post:
      tags:
        - RecommendConfig
      summary: 新增推荐配置
      description: 新增推荐配置
      operationId: createRecommendConfig
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: createRecommendConfigReqDto
          required: true
          schema:
            $ref: '#/definitions/CreateRecommendConfigReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/RecommendConfigVo'
        404:
          description: Not found
  /recommend-config/get-keywords/{id}:
    get:
      tags:
        - RecommendConfig
      summary: 获取指定推荐配置关键词列表
      description: 获取指定推荐配置关键词列表
      operationId: getRecommendConfigKeywords
      produces:
        - application/json
      parameters:
        - name: id
          in: query
          type: integer
          format: int64
          description: 推荐配置id
          required: true
      responses:
        200:
          description: successful operation
          schema:
            type: array
            items:
              type: string
        404:
          description: Not found
  /recommend-config/page:
    post:
      tags:
        - RecommendConfig
      summary: 分页查询推荐配置
      description: 分页查询推荐配置
      operationId: getRecommendConfigPage
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: getRecommendConfigPageReqDto
          required: true
          schema:
            $ref: '#/definitions/GetRecommendConfigPageReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/RecommendConfigPageVo'
        404:
          description: Not found

  /recommend-config/{id}:
    get:
      tags:
        - RecommendConfig
      summary: 查询推荐配置详情
      description: 查询推荐配置详情
      operationId: getRecommendConfig
      produces:
        - application/json
      parameters:
        - name: id
          in: path
          description: id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/RecommendConfigVo'
        404:
          description: Not found
    put:
      tags:
        - RecommendConfig
      summary: 更新推荐配置
      description: 更新推荐配置
      operationId: updateRecommendConfig
      produces:
        - application/json
      parameters:
        - name: id
          in: path
          description: id
          required: true
          type: integer
          format: int64
        - name: request
          in: body
          description: updateRecommendConfigReqDto
          required: true
          schema:
            $ref: '#/definitions/UpdateRecommendConfigReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/RecommendConfigVo'
        404:
          description: Not found
    delete:
      tags:
        - RecommendConfig
      summary: 删除推荐配置
      description: 删除推荐配置
      operationId: deleteRecommendConfig
      produces:
        - application/json
      parameters:
        - name: id
          in: path
          description: id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: successful operation
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: '#/definitions/BaseOperationResponse'
        404:
          description: Not found

  /recommend-config/channel:
    put:
      tags:
        - RecommendConfig
      summary: 批量更新推荐配置渠道
      description: 批量更新推荐配置渠道
      operationId: updateRecommendConfigChannel
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: 推荐配置id集合和渠道
          required: true
          schema:
            $ref: '#/definitions/UpdateRecommendConfigChannelReqDto'
      responses:
        200:
          description: successful operation
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: '#/definitions/BaseOperationResponse'
        404:
          description: Not found

  /old-system/doctor-group/page:
    post:
      tags:
        - OldSystem
      summary: 查询医生分组列表
      description: 查询医生分组列表
      operationId: getDoctorGroupPage
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: getDoctorGroupPageReqDto
          required: true
          schema:
            $ref: '#/definitions/GetDoctorGroupPageReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/DoctorGroupPageRespVo'
        404:
          description: Not found

  /old-system/extension-condition-advertisement/list:
    post:
      tags:
        - OldSystem
      summary: 查询推广条件广告列表
      description: 查询推广条件广告列表
      operationId: getExtensionConditionAdvertisementList
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: getExtensionConditionAdvertisementListReqDto
          required: true
          schema:
            $ref: '#/definitions/GetExtensionConditionAdvertisementReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/GetExtensionConditionAdvertisementRespVo'
        404:
          description: Not found

  /search-management/dict:
    post:
      tags:
        - SearchManagement
      summary: 新增搜索字典
      description: 新增搜索字典
      operationId: createDict
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: createSearchDictReqDto
          required: true
          schema:
            $ref: '#/definitions/DictFileCreateReqDto'
      responses:
        200:
          description: OK
          schema:
            $ref: '#/definitions/DictFileVo'
    put:
      tags:
        - SearchManagement
      summary: 更新搜索字典
      description: 更新搜索字典
      operationId: updateDict
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: updateSearchDictReqDto
          required: true
          schema:
            $ref: '#/definitions/DictFileUpdateReqDto'
      responses:
        200:
          description: OK
          schema:
            $ref: '#/definitions/DictFileVo'

  /search-management/dict/{dict-id}:
    get:
      tags:
        - SearchManagement
      summary: 查询搜索字典
      description: 查询搜索字典
      operationId: getDictDetail
      produces:
        - application/json
      parameters:
        - name: dict-id
          in: path
          description: dict-id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: OK
          schema:
            $ref: '#/definitions/DictFileVo'
    delete:
      tags:
        - SearchManagement
      summary: 删除搜索字典
      description: 删除搜索字典
      operationId: deleteDict
      produces:
        - application/json
      parameters:
        - name: dict-id
          in: path
          description: dict-id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: OK
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: '#/definitions/BaseOperationResponse'

  /search-management/dict/query:
    post:
      tags:
        - SearchManagement
      summary: 分页查询搜索字典
      description: 分页查询搜索字典
      operationId: queryDict
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: getSearchDictPageReqDto
          required: true
          schema:
            $ref: '#/definitions/DictFileQueryReqDto'
      responses:
        200:
          description: OK
          schema:
            $ref: '#/definitions/DictFileQueryRespDto'

  /search-management/dict/sync:
    get:
      tags:
        - SearchManagement
      summary: 同步搜索字典
      description: 同步搜索字典
      operationId: syncDict
      responses:
        200:
          description: OK
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: '#/definitions/BaseOperationResponse'
  /bulletin-config:
    get:
      tags:
        - bulletinConfig
      summary: 获取公告配置详情
      description: 获取公告配置详情
      operationId: getBulletinConfigDetail
      produces:
        - application/json
      parameters:
        - name: id
          in: query
          type: integer
          format: int64
          description: id
          required: true
      responses:
        200:
          description: OK
          schema:
            $ref: "#/definitions/BulletinConfigVo"
    put:
      tags:
        - bulletinConfig
      summary: 更新公告配置
      description: 更新公告配置
      operationId: updateBulletinConfig
      parameters:
        - name: dto
          in: body
          description: dto
          required: true
          schema:
            $ref: "#/definitions/BulletinConfigDto"
      responses:
        200:
          description: OK
          schema:
            $ref: "#/definitions/BulletinConfigVo"
    post:
      tags:
        - bulletinConfig
      summary: 新增公告配置
      description: 新增公告配置
      operationId: addBulletinConfig
      parameters:
        - name: dto
          in: body
          description: dto
          required: true
          schema:
            $ref: "#/definitions/BulletinConfigDto"
      responses:
        200:
          description: OK
          schema:
            $ref: "#/definitions/BulletinConfigVo"
  /bulletin-config/query:
    post:
      tags:
        - bulletinConfig
      description: 分页查询公告配置
      operationId: queryBulletinConfig
      parameters:
        - name: request
          in: body
          description: 查询公告配置
          required: true
          schema:
            $ref: "#/definitions/BulletinConfigReqDto"
      responses:
        200:
          description: OK
          schema:
            $ref: "#/definitions/BulletinConfigPageVo"

  /new-media/wechat-mp/menu/:
    post:
      tags:
        - newMedia
      summary: 设置公众号菜单
      description: 设置公众号菜单
      operationId: setMenu
      consumes:
        - multipart/form-data
      parameters:
        - name: wechat_mp_channel
          in: formData
          required: true
          type: integer
          description: 公众号渠道,不传则新老公众号都修改
        - name: is_default
          in: formData
          default: false
          required: true
          type: boolean
          description: 是否为默认菜单，默认菜单会存到redis中
        - name: file
          in: formData
          required: false
          type: file
          description: 菜单的json文件
      responses:
        200:
          description: OK
          schema:
            $ref: "#/definitions/BaseOperationResponse"

  /new-media/wechat-mp/menu/direct-seeding:
    put:
      tags:
        - newMedia
      summary: 设置直播按钮
      description: 设置直播按钮
      operationId: setLiveButton
      parameters:
        - name: setLiveButtonDto
          in: body
          description: request
          required: true
          schema:
            $ref: "#/definitions/SetLiveButtonDto"
      responses:
        200:
          description: OK
          schema:
            $ref: "#/definitions/BaseOperationResponse"
  /tenant-user/sendMsg:
    post:
      summary: 发送短信验证码
      tags:
        - TenantUser
      operationId: sendMsg
      produces:
        - application/json
      parameters:
        - name: phone_number
          in: body
          description: 用户手机号
          required: false
          schema:
            $ref: '#/definitions/TenantUserSendMegReqDto'
      responses:
        200:
          description: OK
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: '#/definitions/BaseOperationResponse'
  /tenant-user/send/desensitization/sms:
    post:
      summary: 发送数据脱敏二次校验短信验证码
      tags:
        - TenantUser
      operationId: sendDesensitizationSms
      produces:
        - application/json
      responses:
        200:
          description: OK
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: '#/definitions/BaseOperationResponse'
  /tenant-user/verify/desensitization/sms:
    get:
      summary: 校验数据脱敏二次校验短信验证码
      tags:
        - TenantUser
      operationId: verifyDesensitizationSms
      produces:
        - application/json
      parameters:
        - name: verification_code
          in: query
          description: 短信验证码
          type: string
          required: false
      responses:
        200:
          description: OK
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: '#/definitions/BaseOperationResponse'
  /promoter-url-generator:
    post:
      tags:
        - PromoterUrlGenerator
      summary: 生成推广链接
      description: 生成推广链接
      operationId: generatePromoterUrl
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: generatePromoterUrlReqDto
          required: true
          schema:
            $ref: '#/definitions/GeneratePromoterUrlReqDto'

      responses:
        200:
          description: successful operation
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: '#/definitions/BaseOperationResponse'

  /promoter-url-generator/{id}:
    get:
      tags:
        - PromoterUrlGenerator
      summary: 查询推广链接
      description: 查询推广链接
      operationId: getPromoterUrl
      produces:
        - application/json
      parameters:
        - name: id
          in: path
          description: id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/PromoterUrlVo'
    delete:
      tags:
        - PromoterUrlGenerator
      summary: 删除推广链接
      description: 删除推广链接
      operationId: deletePromoterUrl
      produces:
        - application/json
      parameters:
        - name: id
          in: path
          description: id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: successful operation
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: '#/definitions/BaseOperationResponse'
  /promoter-url-generator/list:
    post:
      tags:
        - PromoterUrlGenerator
      summary: 分页查询推广链接
      description: 分页查询推广链接
      operationId: getPromoterUrlPage
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: getPromoterUrlPageReqDto
          required: true
          schema:
            $ref: '#/definitions/GetPromoterUrlPageReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/PromoterUrlPageVo'

  /source-recommend-config:
    get:
      tags:
        - SourceRecommendConfig
      summary: 获取号满推荐分组信息
      description: 获取号满推荐分组信息
      operationId: SourceRecommendConfig
      produces:
        - application/json
      parameters:
        - name: dict_label
          in: query
          description: 分组名称
          type: string
        - name: dict_type
          in: query
          description: 字典类型（默认source_recommend）
          type: string
          default: source_recommend
        - name: status
          in: query
          description: 字典状态
          type: integer
        - name: current_page
          in: query
          description: 当前页
          type: integer
          default: 1
        - name: page_size
          in: query
          description: 每页条数
          type: integer
          default: 10
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/SourceRecommendConfigPageVo"
        404:
          description: Not found
    delete:
      tags:
        - SourceRecommendConfig
      summary: 删除号满推荐分组信息
      description: 删除号满推荐分组信息
      operationId: deleteSourceRecommendConfig
      produces:
        - application/json
      parameters:
        - name: id
          in: query
          type: string
          description: 分组id
          required: true
      responses:
        200:
          description: 成功
  /source-recommend-config/multi-doctor:
    get:
      tags:
        - SourceRecommendConfig
      summary: 获取多分组医生列表
      description: 获取多分组医生列表
      operationId: getMultiDoctorList
      responses:
        200:
          description: 成功
          schema:
            type: array
            items:
              $ref: '#/definitions/MultipleRecommendConfigDoctorDto'
  /source-recommend-config/delete-doctor:
    post:
      tags:
        - SourceRecommendConfig
      summary: 删除分组推荐医生
      description: 删除分组推荐医生
      operationId: deleteRecommendDoctor
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: deleteRecommendConfigDoctorDto
          required: true
          schema:
            $ref: '#/definitions/UpdateRecommendConfigDoctorDto'
      responses:
        200:
          description: 成功
  /source-recommend-config/doctor:
    put:
      tags:
        - SourceRecommendConfig
      summary: 分组添加医生推荐
      description: 分组添加医生推荐
      operationId: recommendDoctor
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: RecommendConfigDoctorDto
          required: true
          schema:
            $ref: '#/definitions/UpdateRecommendConfigDoctorDto'
      responses:
        200:
          description: success
          schema:
            type: array
            items:
              $ref: "#/definitions/RecommendConfigDoctorDto"
    get:
      tags:
        - SourceRecommendConfig
      summary: 获取分组医生列表
      description: 获取分组医生列表
      operationId: getRecommendDoctorList
      produces:
        - application/json
      parameters:
        - name: dict_label
          in: query
          description: 分组名称
          type: string
      responses:
        200:
          description: 成功
          schema:
            type: array
            items:
              $ref: "#/definitions/RecommendConfigDoctorDto"
    post:
      tags:
        - SourceRecommendConfig
      summary: 更新分组推荐医生
      description: 更新分组推荐医生
      operationId: updateRecommendDoctor
      produces:
        - application/json
      parameters:
        - name: list
          in: body
          description: RecommendConfigDoctorDtoList
          required: true
          schema:
            $ref: '#/definitions/UpdateRecommendConfigDoctorDto'
      responses:
        200:
          description: 成功

definitions:
  BasePage:
    type: object
    properties:
      total_size:
        type: integer
        description: 总条数
        format: int64
      current_page:
        type: integer
        description: 当前页码
      page_size:
        type: integer
        description: 每页条数

  HospitalPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalVo"

  DepartmentPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/DepartmentVo"
  QueryBuildingListDto:
    type: object
    properties:
      name:
        type: string
        description: 楼宇名称
      hospitalId:
        type: integer
        description: 医院id
        format: int64
      address:
        $ref: "#/definitions/AddressVo"
      currentPage:
        type: integer
        description: 页码
      pageSize:
        type: integer
        description: 每页数量

  BuildingPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/BuildingVo"

  BuildingVo:
    type: object
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 医院区域id
        format: int64
      hospital_code:
        type: string
      id:
        type: integer
        description: 大楼id
        format: int64
      name:
        type: string
        description: 大楼名称
      address_id:
        type: integer
        description: 地址id
        format: int64
      address:
        $ref: "#/definitions/AddressVo"
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态
      create_time:
        type: string
        description: 新增时间
        format: date-time
  BuildingCreateReqDto:
    type: object
    required:
      - name
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      hospital_code:
        type: string
      name:
        type: string
        description: 楼宇名称
      address:
        $ref: "#/definitions/AddressDto"
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态
  BuildingUpdateReqDto:
    type: object
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      hospital_code:
        type: string
      name:
        type: string
        description: 楼宇名称
      address:
        $ref: "#/definitions/AddressVo"
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态
  FloorCreateReqDto:
    type: object
    required:
      - name
      - building_id
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      hospital_code:
        type: string
      name:
        type: string
        description: 楼层名称
        minLength: 1
        maxLength: 20
      building_id:
        type: integer
        description: 楼宇id
        format: int64
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态
      picture_urls:
        type: array
        items:
          type: string
          description: 图片

  FloorPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/FloorVo"

  FloorVo:
    type: object
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      hospital_code:
        type: string
      id:
        type: integer
        description: 楼层id
        format: int64
      name:
        type: string
        description: 楼层名称
      building_id:
        type: integer
        description: 楼宇id
        format: int64
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态
      picture_urls:
        type: array
        items:
          type: string
          description: 图片
      create_time:
        type: string
        description: 新增时间
        format: date-time

  FloorUpdateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      hospital_code:
        type: string
      name:
        type: string
        description: 楼层名称
        minLength: 1
        maxLength: 20
      building_id:
        type: integer
        description: 楼宇id
        format: int64
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态
      picture_urls:
        type: array
        items:
          type: string
          description: 图片

  AreaCreateReqDto:
    type: object
    required:
      - name
      - tenant_id
      - hospital_id
      - hospital_area_id
      - building_id
      - floor_id
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      hospital_code:
        type: string
      building_id:
        type: integer
        description: 大楼id
        format: int64
      floor_id:
        type: integer
        description: 楼层id
        format: int64
      name:
        type: string
        description: 区域名称
        minLength: 1
        maxLength: 20
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态
      picture_urls:
        type: array
        items:
          type: string
          description: 图片

  AreaUpdateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      hospital_code:
        type: string
      building_id:
        type: integer
        description: 大楼id
        format: int64
      floor_id:
        type: integer
        description: 楼层id
        format: int64
      name:
        type: string
        description: 区域名称
        minLength: 1
        maxLength: 20
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态
      picture_urls:
        type: array
        items:
          type: string
          description: 图片
  AreaVo:
    type: object
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      hospital_code:
        type: string
      building_id:
        type: integer
        description: 大楼id
        format: int64
      floor_id:
        type: integer
        description: 楼层id
        format: int64
      id:
        type: integer
        description: 区域id
        format: int64
      name:
        type: string
        description: 区域名称
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态
      picture_urls:
        type: array
        items:
          type: string
          description: 图片
      create_time:
        type: string
        description: 新增时间
        format: date-time
  AreaPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
    properties:
      list:
        type: array
        items:
          $ref: "#/definitions/AreaVo"

  DictTypePageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/DictTypeVo"
  DictTypeVo:
    type: object
    properties:
      id:
        type: integer
        description: 字典类型id
        format: int64
      type:
        type: string
        description: 字典类型
      description:
        type: string
        description: 字典类型描述
      create_time:
        type: string
        description: 新增时间
        format: date-time
      update_time:
        type: string
        description: 更新时间
        format: date-time
  CreateDictTypeDto:
    type: object
    required:
      - type
    properties:
      type:
        type: string
        description: 字典类型
        minLength: 1
        maxLength: 40
      description:
        type: string
        description: 字典类型描述

  UpdateDictTypeDto:
    type: object
    allOf:
      - $ref: "#/definitions/CreateDictTypeDto"
      - type: object

  DictLabelPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/DictLabelVo"

  DictLabelVo:
    type: object
    properties:
      id:
        type: integer
        description: 字典标签id
        format: int64
      dict_type:
        type: string
        description: 字典类型
      dict_label:
        type: string
        description: 字典标签
      dict_value:
        type: string
        description: 字典值
      sort:
        type: integer
        description: 排序
      description:
        type: string
        description: 字典标签描述
      icon_url:
        type: string
        description: 图标 URL
      badge:
        type: string
        description: 角标 URL
      tag:
        type: string
        description: 标签
      redirect_path:
        type: string
        description: 重定向地址
      create_time:
        type: string
        description: 新增时间
        format: date-time
      update_time:
        type: string
        description: 更新时间
        format: date-time

  CreateDictLabelDto:
    type: object
    required:
      - dict_type
      - dict_label
      - dict_value
    properties:
      dict_type:
        type: string
        description: 字典类型
        minLength: 1
        maxLength: 40
      dict_label:
        type: string
        description: 字典标签
        minLength: 1
        maxLength: 40
      dict_value:
        type: string
        description: 字典值
        minLength: 1
        maxLength: 40
      description:
        type: string
        description: 字典标签描述
      sort:
        type: integer
        description: 排序
      icon_url:
        type: string
        description: 图标 URL
      badge:
        type: string
        description: 角标 URL
      tag:
        type: string
        description: 标签
      redirect_path:
        type: string
        description: 重定向地址


  UpdateDictLabelDto:
    type: object
    allOf:
      - $ref: "#/definitions/CreateDictLabelDto"
      - type: object

  TenantUserCreateReqDto:
    type: object
    required:
      - name
      - status
    properties:
      name:
        type: string
        description: 帐号名
        minLength: 1
        maxLength: 20
      nickname:
        type: string
        description: 用户名
        minLength: 1
        maxLength: 20
      password:
        type: string
        description: 密码
        minLength: 1
        maxLength: 32
      phone_number:
        type: string
        description: 手机号
        minLength: 11
        maxLength: 13
      gender:
        type: integer
        description: 性别,0:未知,1:男,2:女
        format: int32
        default: 0
        minimum: 0
        maximum: 2
      id_card_no:
        type: string
        description: 身份证号
        minLength: 1
        maxLength: 20
      register_platform:
        type: integer
        description: 注册平台 0:未知 1:PC
        format: int32
      status:
        type: integer
        description: 状态 0:正常 1:冻结
        format: int32
        minimum: 0
        maximum: 2
      email:
        type: string
        description: 邮箱
        minLength: 1
        maxLength: 75
      head_image_url:
        type: string
        description: 头像
        minLength: 1
        maxLength: 300
      user_tenant_privilege_configs:
        type: array
        description: 用户权限配置，最基本单位:租户
        items:
          $ref: "#/definitions/UserTenantPrivilegeConfig"
  UserTenantPrivilegeConfig:
    type: object
    properties:
      tenant_id:
        type: integer
        description: 租户id，必填
        format: int64
      tenant_admin:
        type: boolean
        description: 是否租户管理员
      bind_roles:
        type: array
        description: 绑定租户角色id列表
        items:
          type: integer
          format: int64
      unbind_roles:
        type: array
        description: 解绑租户角色id列表
        items:
          type: integer
          format: int64
      hospital_privilege_configs:
        type: array
        description: 当前租户医院配置
        items:
          $ref: "#/definitions/UserHospitalPrivilegeConfig"
  UserHospitalPrivilegeConfig:
    type: object
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_admin:
        type: boolean
        description: 是否医院管理员
      hospital_area_privilege_configs:
        type: array
        description: 当前院区配置(只能是院区)
        items:
          $ref: "#/definitions/UserHospitalAreaPrivilegeConfig"
  UserHospitalAreaPrivilegeConfig:
    type: object
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      hospital_area_admin:
        type: boolean
        description: 是否院区管理员
      departments:
        type: array
        description: 当前租户科室id列表
        items:
          $ref: "#/definitions/UserDepartmentPrivilegeConfig"
  UserDepartmentPrivilegeConfig:
    type: object
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      department_id:
        type: integer
        description: 科室id
        format: int64
      department_admin:
        type: boolean
        description: 是否科室管理员
  TenantUserUpdateReqDto:
    type: object
    allOf:
      - $ref: "#/definitions/TenantUserCreateReqDto"
      - type: object
        properties:
          { }
  TenantUserPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/TenantUserVo"

  RegionQueryReqDto:
    type: object
    required:
      - current_page
      - page_size
    properties:
      current_page:
        type: integer
        description: 当前页码
        default: 1
        minimum: 1
      page_size:
        type: integer
        description: 每页条数
        default: 10
        minimum: 10
        maximum: 200
      id:
        type: integer
        format: int64
        minimum: 1
      pid:
        type: integer
        format: int64
        minimum: 0
      short_name:
        type: string
        maxLength: 100
      name:
        type: string
        maxLength: 100
      merger_name:
        type: string
        maxLength: 255
      level:
        type: integer
        minimum: 1
        maximum: 3
      pinyin:
        type: string
        maxLength: 100
      code:
        type: string
        maxLength: 100
      zip_code:
        type: string
        maxLength: 100
      first:
        type: string
        maxLength: 50
      fid:
        type: integer
      spell_simple:
        type: string
        maxLength: 50
      start_create_time:
        type: string
        format: date-time
      end_create_time:
        type: string
        format: date-time

  RegionCreateReqDto:
    type: object
    properties:
      pid:
        type: integer
        format: int64
        minimum: 0
      short_name:
        type: string
        maxLength: 100
      name:
        type: string
        maxLength: 100
      merger_name:
        type: string
        maxLength: 255
      level:
        type: integer
        minimum: 1
        maximum: 3
      pinyin:
        type: string
        maxLength: 100
      code:
        type: string
        maxLength: 100
      zip_code:
        type: string
        maxLength: 100
      first:
        type: string
        maxLength: 50
      lng:
        type: string
        maxLength: 100
      lat:
        type: string
        maxLength: 100
      fid:
        type: integer
      spell_simple:
        type: string
        maxLength: 50
    required:
      - short_name
      - name
      - merger_name
      - level
      - pid
      - code
      - zip_code
      - first
      - spell_simple

  RegionUpdateReqDto:
    type: object
    properties:
      pid:
        type: integer
        format: int64
        minimum: 0
      short_name:
        type: string
        maxLength: 100
      name:
        type: string
        maxLength: 100
      merger_name:
        type: string
        maxLength: 255
      level:
        type: integer
        minimum: 1
        maximum: 3
      pinyin:
        type: string
        maxLength: 100
      code:
        type: string
        maxLength: 100
      zip_code:
        type: string
        maxLength: 100
      first:
        type: string
        maxLength: 50
      lng:
        type: string
        maxLength: 100
      lat:
        type: string
        maxLength: 100
      fid:
        type: integer
      spell_simple:
        type: string
        maxLength: 50

  RegionVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
      pid:
        type: integer
        format: int64
      short_name:
        type: string
      name:
        type: string
      merger_name:
        type: string
      level:
        type: integer
      pinyin:
        type: string
      code:
        type: string
      zip_code:
        type: string
      first:
        type: string
      lng:
        type: string
      lat:
        type: string
      fid:
        type: integer
      spell_simple:
        type: string
      create_time:
        type: string
        format: date-time

  RegionPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/RegionVo"



  AddressVo:
    type: object
    allOf:
      - $ref: "#/definitions/AddressDto"
      - type: object
        properties:
          id:
            type: integer
            description: 地址id
            format: int64

  AddressDto:
    type: object
    properties:
      province:
        type: string
        description: 省
      city:
        type: string
        description: 市
      county:
        type: string
        description: 区/县
      detail:
        type: string
        description: 详细地址
      longitude:
        type: number
        description: 经度
        format: decimal
      latitude:
        type: number
        description: 纬度
        format: decimal

  VerifyTenantUserPasswordDto:
    type: object
    properties:
      name:
        type: string
        description: 用户名
        minLength: 1
        maxLength: 20
      phone_number:
        type: string
        description: 手机号
        minLength: 11
        maximum: 13
      password:
        type: string
        description: 密码
        minLength: 1
        maxLength: 32
      verification_code:
        type: string
        maxLength: 6
        minimum: 6
        default: 短信验证码

  TenantUserBindingTenantVo:
    type: object
    properties:
      tenants:
        type: array
        items:
          $ref: "#/definitions/TenantVo"

  DepartmentUpdateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
        minimum: 1
      hospital_id:
        type: integer
        description: 医院 id
        format: int64
        minimum: 1
      hospital_area_id:
        type: integer
        description: 院区 id
        format: int64
        minimum: 1
      parent_id:
        type: integer
        description: 父节点科室 id
        format: int64
      thrdpart_dep_code:
        type: string
        description: 第三方科室编码
      name:
        type: string
        description: 科室名称
        minLength: 1
        maxLength: 300
      short_name:
        type: string
        description: 科室简称
      recommended:
        type: boolean
        description: 是否推荐
      category:
        description: 科室类型
        type: array
        items:
          type: integer
          format: int32
      first_letter:
        type: string
        description: 首字母
      introduction:
        type: string
        description: 科室简介
      address_intro:
        type: string
        description: 地址简介
      caution:
        type: string
        description: 科室提醒
      sort:
        type: integer
        description: 排序
      system_depends:
        type: integer
        description: 挂号系统依赖：0,HIS;1,小系统;
      current_system_type:
        type: integer
        description: 当前使用系統类型：1,老系统;2,新系统;3,新老系统共用;
      enabled:
        type: boolean
        description: 是否启用
      display_bg_color:
        type: string
        description: 显示背景色
      enable_department_detail:
        type: boolean
        description: 是否启用科室详情
      uri:
        type: string
        description: 科室跳转地址
      forbidden_day:
        type: integer
        description: 禁止预约天数
        minimum: -1
      advance_day:
        type: integer
        description: 提前预约天数
        minimum: -1
      source_activate_time:
        type: string
        description: 号源激活时间
        default: '00:00:00'
      remark:
        type: string
        description: 备注
      level_tag:
        type: integer
        description: 是否是叶子节点，0:不是叶子节点；1:叶子节点
      doctors:
        description: 医生名称或者编码集合
        type: array
        items:
          type: string
      triage_desk_address:
        type: string
        description: 分诊台地址
      display_fields:
        type: integer
        description: 显示字段（0：name、1:displayDepartmentName)
      display_department_name:
        type: string
        description: 显示科室名称
      appointment_notify_contact:
        type: string
        description: 预约后联系人
      notice_template_id:
        type: string
        description: 通知模板id

  TreeDepartmentVo:
    type: object
    allOf:
      - $ref: "#/definitions/DepartmentVo"
      - type: object
        properties:
          enabled:
            type: boolean
            description: 是否启用
          children:
            type: array
            description: 子科室
            items:
              $ref: "#/definitions/TreeDepartmentVo"
  TableDepartmentQueryReqDto:
    type: object
    required:
      - tenant_id
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户id

  TreeDepartmentQueryReqDto:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户id
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      name:
        type: string
        description: 科室名称

  TreeDepartmentQueryVo:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户id
      hospital_list:
        type: array
        items:
          $ref: "#/definitions/TreeHospitalVo"

  TreeHospitalVo:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户id
      id:
        type: integer
        description: 医院id
        format: int64
      name:
        type: string
        description: 医院名称
      status:
        type: integer
        description: 医院状态
      hospital_area_list:
        type: array
        description: 院区列表
        items:
          $ref: "#/definitions/TreeHospitalAreaVo"

  TreeHospitalAreaVo:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户id
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      id:
        type: integer
        description: 院区id
        format: int64
      name:
        type: string
        description: 院区名称
      status:
        type: integer
        description: 院区状态
      department_list:
        type: array
        description: 科室列表
        items:
          $ref: "#/definitions/TreeDepartmentVo"

  TableDepartmentQueryVo:
    type: object
    properties:
      list:
        type: array
        items:
          $ref: "#/definitions/TableHospitalVo"

  TableHospitalVo:
    type: object
    properties:
      id:
        type: integer
        description: 医院id
        format: int64
      name:
        type: string
        description: 医院名称
      hospital_area_list:
        type: array
        description: 院区列表
        items:
          $ref: "#/definitions/HospitalAreaVo"
      department_list:
        type: array
        description: 科室列表
        items:
          $ref: "#/definitions/DepartmentVo"

  HospitalDepartmentTreeVo:
    type: object
    properties:
      id:
        type: integer
        description: 医院id
        format: int64
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      name:
        type: string
        description: 医院名称
      department_list:
        type: array
        description: 科室列表
        items:
          $ref: "#/definitions/TreeDepartmentVo"

  TenantUserQueryReqDto:
    type: object
    required:
      - current_page
      - page_size
    properties:
      current_page:
        description: 当前页
        type: integer
        default: 1
        minimum: 1
      page_size:
        description: 每页条数
        type: integer
        default: 10
        minimum: 10
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
        minimum: 1
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
        minimum: 1
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
        minimum: 1
      department_id:
        type: integer
        format: int64
        description: 科室ID
        minimum: 1
      name:
        description: 用户名称
        type: string
      nickname:
        description: 用户昵称
        type: string
      phone_number:
        description: 手机号
        type: string
      id_card_no:
        description: 身份证号
        type: string
      status:
        description: 状态
        type: integer
        format: int32
      start_create_time:
        type: string
        format: date-time
      end_create_time:
        type: string
        format: date-time
      role_id:
        type: integer
        format: int64
        description: 角色ID

  TenantCreateReqDto:
    type: object
    required:
      - name
      - contact
      - contact_phone_number
    properties:
      name:
        type: string
        description: 租户名称
        minLength: 1
        maxLength: 50
      contact:
        type: string
        description: 联系人
        minLength: 1
        maxLength: 20
      contact_phone_number:
        type: string
        description: 联系人电话
        minLength: 1
        maxLength: 100
      logo_url:
        type: string
        description: logo地址
      description:
        type: string
        description: 描述
        maximum: 300
      contact_email:
        type: string
        description: 联系人邮箱
        minLength: 1
        maxLength: 20
      contact_wechat:
        type: string
        description: 联系人微信
        minLength: 1
        maxLength: 30
      contact_address:
        $ref: "#/definitions/AddressDto"
      address:
        $ref: "#/definitions/AddressDto"
  TenantUpdateReqDto:
    type: object
    properties:
      name:
        type: string
        description: 租户名称
        minLength: 1
        maxLength: 50
      contact:
        type: string
        description: 联系人
        minLength: 1
        maxLength: 20
      contact_phone_number:
        type: string
        description: 联系人电话
        minLength: 1
        maxLength: 100
      logo_url:
        type: string
        description: logo地址
      description:
        type: string
        description: 描述
        maximum: 300
      contact_email:
        type: string
        description: 联系人邮箱
        minLength: 1
        maxLength: 20
      contact_wechat:
        type: string
        description: 联系人微信
        minLength: 1
        maxLength: 30
      address_id:
        type: integer
        description: 租户地址id
        format: int64
      address:
        $ref: "#/definitions/AddressDto"
      contact_address_id:
        type: integer
        description: 联系人地址id
        format: int64
      contact_address:
        $ref: "#/definitions/AddressDto"
  TenantVo:
    type: object
    properties:
      id:
        type: integer
        description: 租户id
        format: int64
      name:
        type: string
        description: 租户名称
      contact:
        type: string
        description: 联系人
      contact_phone_number:
        type: string
        description: 联系人电话
      address_id:
        type: integer
        description: 租户地址 id
        format: int64
      logo_url:
        type: string
        description: logo地址
      description:
        type: string
        description: 描述
      contact_email:
        type: string
        description: 联系人邮箱
      contact_wechat:
        type: string
        description: 联系人微信
      contact_address_id:
        type: integer
        description: 联系人地址 id
        format: int64
      create_time:
        type: string
        description: 新增时间
        format: date-time
      address:
        $ref: "#/definitions/AddressVo"
  TenantDetailVo:
    allOf:
      - $ref: "#/definitions/TenantVo"
      - type: object
        properties:
          contact_address:
            $ref: "#/definitions/AddressVo"
  TenantQueryReqDto:
    type: object
    properties:
      current_page:
        description: 当前页
        type: integer
        default: 1
      page_size:
        description: 每页条数
        type: integer
        default: 10
      name:
        description: 租户名称
        type: string
      contact_person:
        description: 联系人
        type: string
      contact_phone_number:
        description: 联系人电话
        type: string
  TenantPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/TenantVo"
  TenantUserVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 用户id
      name:
        type: string
        description: 账户名
      nickname:
        type: string
        description: 用户名
      email:
        type: string
        description: 邮箱
      roles:
        type: array
        description: 角色列表
        items:
          $ref: "#/definitions/RoleVo"
      status:
        type: integer
        description: 状态 0:正常 1:冻结
      head_image_url:
        type: string
        description: 头像
        minLength: 1
        maxLength: 300
      create_time:
        type: string
        format: date-time
        description: 新增时间
  TenantUserDetailVo:
    allOf:
      - $ref: "#/definitions/TenantUserVo"
      - type: object
        properties:
          phone_number:
            type: string
            description: 手机号
          admin:
            type: boolean
            description: 是否是超级管理员
          tenants:
            type: array
            description: 租户列表
            items:
              $ref: "#/definitions/TenantVo"
          user_tenant_privilege_configs:
            type: array
            description: 用户权限配置
            items:
              $ref: "#/definitions/UserTenantPrivilegeConfig"
  CheckTenantUserPasswordResultVo:
    type: object
    properties:
      result:
        type: integer
        description: 密码校验结果(0:正常 1:密码强度不符合要求 2:密码过期)
        enum:
          - 0
          - 1
          - 2
        default: 0
      desc:
        type: string
        description: 密码校验结果描述
  TenantUserBindReqDto:
    type: object
    required:
      - name
      - status
    properties:
      tenant_user_id:
        type: integer
        description: 租户用户id
        format: int64
      user_tenant_privilege_configs:
        type: array
        description: 用户权限配置，最基本单位:租户
        items:
          $ref: "#/definitions/UserTenantPrivilegeConfig"
  RoleVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
      tenant_id:
        type: integer
        format: int64
        default: 租户ID
      name:
        type: string
      description:
        type: string
      status:
        type: integer
      user_count:
        type: integer
        format: int64
        description: 用户总数
      create_time:
        type: string
        format: date-time
  HospitalCreateReqDto:
    type: object
    required:
      - tenant_id
      - name
      - logo
      - level_dict_label
      - category
    properties:
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
        minimum: 1
      name:
        type: string
        description: 医院名称
      logo:
        type: string
        description: 医院 logo
      level_dict_type:
        type: string
        description: 医院等级字典类型
        default: hospital_level
      level_dict_label:
        type: array
        description: 医院等级字典标签
        items:
          type: string
      property:
        type: integer
        description: 医院性质：0，公立；1，民营；10，其他；
      category:
        type: array
        items:
          type: integer
        description: 医院分类：0，常规；1，疫苗预约；2，特需门诊；3，多学科；9，其他；
      address:
        $ref: "#/definitions/AddressDto"
      status:
        type: integer
        description: 状态,0:启用,1:开发,2:维护,3:停用
        default: 0
        enum:
          - 0
          - 1
          - 2
          - 3

  HospitalVo:
    type: object
    properties:
      id:
        type: integer
        description: 医院id
        format: int64
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_code:
        type: string
        description: 医院编码
      logo:
        type: string
        description: 医院logo
      name:
        type: string
        description: 医院名称
      status:
        type: integer
        description: 状态,0:启用,1:开发,2:维护,3:停用
      category:
        type: array
        items:
          type: integer
        description: 医院类别：0，常规；1，疫苗预约；2，特需门诊；3，多学科；9，其他；
      property:
        type: integer
        description: 医院性质：0，公立；1，民营；10，其他；
      level_dict_type:
        type: string
        description: 医院等级字典类型
        default: hospital_level
      level_dict_label:
        type: array
        description: 医院等级字典标签
        items:
          type: string
      level_dict_value:
        type: array
        description: 医院级别
        items:
          type: string
      display_sort:
        type: integer
        description: 排序
      create_time:
        type: string
        description: 新增时间
        format: date-time

  HospitalKafkaVo:
    allOf:
      - $ref: "#/definitions/HospitalVo"
      - type: object
        properties:
          tenant:
            $ref: "#/definitions/TenantVo"
          hospital_area_list:
            type: array
            description: 院区列表
            items:
              $ref: "#/definitions/HospitalAreaVo"

  HospitalDetailVo:
    allOf:
      - $ref: "#/definitions/HospitalVo"
      - type: object
        properties:
          address:
            $ref: "#/definitions/AddressVo"

  HospitalQueryReqDto:
    type: object
    required:
      - current_page
      - page_size
    properties:
      current_page:
        description: 当前页
        type: integer
        default: 1
        minimum: 1
      page_size:
        description: 每页条数
        type: integer
        default: 10
        minimum: 10
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
        minimum: 1
      hospital_code:
        type: string
        description: 医院编码
      hospital_name:
        type: string
        description: 医院名称
      level_dict_value:
        description: 医院等级
        type: string
      category:
        type: array
        items:
          type: integer
        description: 院区类型,0，常规；1，疫苗预约；2，特需门诊；3，多学科；9，其他；
      exclude_category:
        type: array
        items:
          type: integer
        description: 院区类型,0，常规；1，疫苗预约；2，特需门诊；3，多学科；9，其他；
      status:
        description: 状态,0:启用,1:开发,2:维护,3:停用
        type: integer
        enum:
          - 0
          - 1
          - 2
          - 3
      start_create_time:
        description: 新增起始时间
        type: string
        format: date-time
      end_create_time:
        description: 新增结束时间
        type: string
        format: date-time
  HospitalUpdateReqDto:
    type: object
    required:
      - tenant_id
    properties:
      tenant_id:
        type: integer
        description: 租户 id，做识别，不会更新
        format: int64
        minimum: 1
      name:
        type: string
        description: 医院名称
      logo:
        type: string
        description: 医院 logo
      level_dict_type:
        type: string
        description: 医院等级字典类型
        default: hospital_level
      level_dict_label:
        type: array
        description: 医院等级字典标签
        items:
          type: string
      category:
        type: array
        items:
          type: integer
        description: 院区类型,0，常规；1，疫苗预约；2，特需门诊；3，多学科；9，其他；
      property:
        type: integer
        description: 医院类型,0:公立,1:民营,20:其他
        enum:
          - 0
          - 1
          - 20
      display_sort:
        type: integer
        description: 排序
        minimum: 0
      address:
        $ref: "#/definitions/AddressDto"
      status:
        type: integer
        description: 状态,0:启用,1:开发,2:维护,3:停用
        default: 0
        enum:
          - 0
          - 1
          - 2
          - 3
  HospitalAreaCreateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - name
      - picture
      - category
      - contact_phone_number
      - stop_service_begin_time
      - stop_service_end_time
    properties:
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
        minimum: 1
      hospital_id:
        type: integer
        description: 医院 id
        format: int64
        minimum: 1
      hospital_area_code:
        type: string
        description: 院区编码
      name:
        type: string
        description: 院区名称
      picture:
        type: string
        description: 院区图片
      category:
        type: array
        items:
          type: integer
        description: 院区类型,0，常规；1，疫苗预约；2，特需门诊；3，多学科；9，其他；
      contact_phone_number:
        type: string
        description: 联系电话
      display:
        type: boolean
        description: 是否显示
      display_sort:
        type: integer
        description: 排序
        default: 255
      appointment_scheduling_time:
        type: string
        description: 放号时间
      announcement:
        type: string
        description: 院区公告
      introduction:
        type: string
        description: 院区介绍
      environment:
        type: string
        description: 院区环境
      display_guide:
        type: boolean
        description: 是否显示去院导航
        default: true
      map_keyword:
        type: string
        description: 地图关键字
      display_floor:
        type: boolean
        description: 是否显示楼层
        default: true
      stop_service_begin_time:
        type: string
        description: 停止服务开始时间
      stop_service_end_time:
        type: string
        description: 停止服务结束时间
      address:
        $ref: "#/definitions/AddressDto"
      tag_dict_label:
        type: array
        description: 院区标签
        items:
          type: string
      status:
        type: integer
        description: 状态,0:启用,1:开发,2:维护,3:停用
        default: 0
        enum:
          - 0
          - 1
          - 2
          - 3

  HospitalAreaVo:
    type: object
    properties:
      id:
        type: integer
        description: 院区id
        format: int64
      hospital_area_code:
        type: string
        description: 院区编码
      name:
        type: string
        description: 院区名称
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 所属医院id
        format: int64
      hospital_name:
        type: string
        description: 所属医院名称
      status:
        type: integer
        description: 状态,0:启用,1:开发,2:维护,3:停用
      display:
        type: boolean
        description: 是否显示,0:显示,1:不显示
      category:
        type: array
        items:
          type: integer
        description: 院区类别：0，常规；1，疫苗预约；2，特需门诊；3，多学科；9，其他；
      display_sort:
        type: integer
        description: 排序
      address:
        $ref: "#/definitions/AddressDto"
      create_time:
        type: string
        description: 新增时间
        format: date-time
      appointment_setting:
        $ref: "#/definitions/AppointmentRuleSettingVo"
      stop_service_begin_time:
        type: string
        description: 停止服务开始时间
      stop_service_end_time:
        type: string
        description: 停止服务结束时间

  HospitalAreaDetailVo:
    allOf:
      - $ref: "#/definitions/HospitalAreaVo"
      - type: object
        properties:
          picture:
            type: string
            description: 院区图片
          contact_phone_number:
            type: string
            description: 联系电话
          appointment_scheduling_time:
            type: string
            description: 放号时间
          announcement:
            type: string
            description: 院区公告
          introduction:
            type: string
            description: 院区介绍
          environment:
            type: string
            description: 院区环境
          display_guide:
            type: boolean
            description: 是否显示去院导航
          map_keyword:
            type: string
            description: 地图关键字
          display_floor:
            type: boolean
            description: 是否显示楼层
          hospital_area_settings:
            $ref: "#/definitions/HospitalAreaSettingDetailVo"

  HospitalAreaQueryReqDto:
    type: object
    required:
      - current_page
      - page_size
    properties:
      current_page:
        type: integer
        description: 页码
        default: 1
        minimum: 1
      page_size:
        type: integer
        description: 每页数量
        default: 10
        minimum: 10
      hospital_area_code:
        type: string
        description: 院区编码
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
        minimum: 1
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
        minimum: 1
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
        minimum: 1
      hospital_name:
        type: string
        description: 医院名称
      hospital_area_name:
        type: string
        description: 院区名称
      category:
        type: array
        items:
          type: integer
        description: 院区类型,0，常规；1，疫苗预约；2，特需门诊；3，多学科；9，其他；
      exclude_category:
        type: array
        items:
          type: integer
        description: 院区类型,0，常规；1，疫苗预约；2，特需门诊；3，多学科；9，其他；
      status:
        type: integer
        description: 状态,0:启用,1:开发,2:维护,3:停用
        enum:
          - 0
          - 1
          - 2
          - 3
      start_create_time:
        type: string
        description: 开始时间
        format: date-time
      end_create_time:
        type: string
        description: 结束时间
        format: date-time
      payment_status:
        type: integer
        description: 支付启动状态，为空不做筛选，0：未配置；1，启动；2，停用；
        enum:
          - 0
          - 1
          - 2
      system_depends:
        type: integer
        description: 挂号系统依赖，为空不做筛选，0：HIS；1：小系统；
        enum:
          - 0
          - 1
  HospitalAreaPageVo:
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalAreaVo"

  HospitalAreaUpdateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
    properties:
      tenant_id:
        type: integer
        description: 租户 id,必填，不做更新
        format: int64
        minimum: 1
      hospital_id:
        type: integer
        format: int64
        description: 医院ID,必填，不做更新
        minimum: 1
      hospital_area_code:
        type: string
        description: 院区编码
      name:
        type: string
        description: 院区名称
      picture:
        type: string
        description: 院区图片
      category:
        type: array
        items:
          type: integer
        description: 院区类型,0，常规；1，疫苗预约；2，特需门诊；3，多学科；9，其他；
      contact_phone_number:
        type: string
        description: 联系电话
      display:
        type: boolean
        description: 是否显示院区列表
      display_sort:
        type: integer
        description: 排序
        default: 255
      announcement:
        type: string
        description: 院区公告
      introduction:
        type: string
        description: 院区介绍
      environment:
        type: string
        description: 院区环境
      display_guide:
        type: boolean
        description: 是否显示去院导航
        default: true
      map_keyword:
        type: string
        description: 地图关键字
      display_floor:
        type: boolean
        description: 是否显示楼层
        default: true
      stop_service_begin_time:
        type: string
        description: 停止服务开始时间
      stop_service_end_time:
        type: string
        description: 停止服务结束时间
      address:
        $ref: "#/definitions/AddressDto"
      status:
        type: integer
        description: 状态,0:启用,1:开发,2:维护,3:停用
        default: 0
        enum:
          - 0
          - 1
          - 2
          - 3
      appointment_scheduling_time:
        type: string
        description: 放号时间
      tag_dict_label:
        type: array
        description: 院区标签
        items:
          type: string

  HospitalAreaDepartmentsInfoQueryReqDto:
    type: object
    properties:
      current_page:
        type: integer
        description: 页码
        default: 1
        minimum: 1
      page_size:
        type: integer
        description: 每页数量
        default: 10
        minimum: 10
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
        minimum: 1
      hospital_area_code:
        type: string
        description: 院区编码
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
        minimum: 1
      hospital_name:
        type: string
        description: 医院名称
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
        minimum: 1
      hospital_area_name:
        type: string
        description: 院区名称
      department_layer:
        type: integer
        description: 展示层级
        minimum: 1
      category:
        type: array
        items:
          type: integer
        description: 科室类型
      exclude_category:
        type: array
        items:
          type: integer
        description: 排除科室类型
  HospitalAreaDepartmentsInfoPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalAreaDepartmentInfoVo"
  HospitalAreaDepartmentInfoVo:
    type: object
    properties:
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      hospital_code:
        type: string
        description: 医院编码
      hospital_name:
        type: string
        description: 医院名称
      hospital_area_code:
        type: string
        description: 院区编码
      hospital_area_name:
        type: string
        description: 院区名称
      display_layer:
        type: integer
        description: 展示层级
      total_count:
        type: integer
        description: 科室数量
      system_depends:
        type: integer
        description: 挂号系统依赖：0，HIS；1，小系统；
      status:
        type: integer
        description: 状态：0,启用；1，开发中；2，维护；3，禁用；

  HospitalAreaSettingCreateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - type
    properties:
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
        minimum: 1
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
        minimum: 1
      custom_business_setting:
        $ref: "#/definitions/CustomBusinessSettingDto"

  HospitalAreaSettingUpdateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - type
    properties:
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
        minimum: 1
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
        minimum: 1
      type:
        type: string
        description: 新增配置类型，
          appointment_registration:预约挂号设置;
          diagnosis_payment:门诊缴费设置;
          patient_report:就诊报告设置;
          hospitalization:住院配置;
          patient_card:就诊卡设置;
          custom_business:自定义业务设置;
      setting_id:
        type: integer
        format: int64
        description: 配置id,仅更新自定义业务时需要
        minimum: 1
      hospitalization_setting:
        $ref: "#/definitions/HospitalizationSettingDto"
      diagnosis_payment_setting:
        $ref: "#/definitions/DiagnosisPaymentSettingDto"
      custom_business_setting:
        $ref: "#/definitions/CustomBusinessSettingDto"
      appointment_rule_setting:
        $ref: "#/definitions/AppointmentRuleSettingDto"
      patient_card_setting:
        $ref: "#/definitions/PatientCardSettingDto"
      patient_report_setting:
        $ref: "#/definitions/PatientReportSettingDto"
  HospitalAreaSettingDetailVo:
    type: object
    properties:
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
        minimum: 1
      hospital_area_code:
        type: string
        description: 院区编码
      hospitalization_setting:
        $ref: "#/definitions/HospitalizationSettingVo"
      diagnosis_payment_setting:
        $ref: "#/definitions/DiagnosisPaymentSettingVo"
      custom_business_settings:
        type: array
        items:
          $ref: "#/definitions/CustomBusinessSettingVo"
      appointment_rule_setting:
        $ref: "#/definitions/AppointmentRuleSettingVo"
      patient_card_setting:
        $ref: "#/definitions/PatientCardSettingVo"
      patient_report_setting:
        $ref: "#/definitions/PatientReportSettingVo"

  HospitalizationSettingDto:
    type: object
    required:
      - enable_payment
      - support_online_refund
      - enable_info_query
    properties:
      enable_payment:
        type: boolean
        description: 是否开启支付
      support_online_refund:
        type: boolean
        description: 是否支持在线退款
      enable_info_query:
        type: boolean
        description: 是否开启住院信息查询
      selected_payments:
        type: array
        description: 支付方式
        items:
          type: string
      payment_information:
        type: string
        description: 缴费提示信息

  HospitalizationSettingUpdateDto:
    allOf:
      - $ref: "#/definitions/HospitalizationSettingDto"
      - type: object
        required:
          - id
        properties:
          id:
            type: integer
            format: int64
            description: 住院规则配置ID
            minimum: 1

  HospitalizationSettingVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 住院规则配置ID
        minimum: 1
      function_id:
        type: integer
        format: int64
        description: 功能ID
        minimum: 1
      enable_payment:
        type: boolean
        description: 是否开启支付
      support_online_refund:
        type: boolean
        description: 是否支持在线退款
      enable_info_query:
        type: boolean
        description: 是否开启住院信息查询
      selected_payments:
        type: array
        description: 支付方式
        items:
          type: string
      payment_information:
        type: string
        description: 缴费提示信息
      create_time:
        type: string
        description: 新增时间
        format: date-time

  DiagnosisPaymentSettingDto:
    type: object
    required:
      - support_merger_payment
      - support_online_refund
      - refund_today
      - stop_refund_time
      - support_invoice
    properties:
      support_merger_payment:
        type: boolean
        description: 是否支持合并支付
      support_online_refund:
        type: boolean
        description: 是否支持在线退款
      refund_today:
        type: boolean
        description: 是否支持当天退款
      stop_refund_time:
        type: string
        description: 停止退款时间
      support_invoice:
        type: boolean
        description: 是否支持开票
      selected_payments:
        type: array
        description: 支付方式
        items:
          type: string
      payment_information:
        type: string
        description: 缴费提示信息
  DiagnosisPaymentSettingUpdateDto:
    allOf:
      - $ref: "#/definitions/DiagnosisPaymentSettingDto"
      - type: object
        required:
          - id
        properties:
          id:
            type: integer
            format: int64
            description: 门诊缴费规则配置ID
            minimum: 1
  DiagnosisPaymentSettingVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 门诊缴费规则配置ID
        minimum: 1
      function_id:
        type: integer
        format: int64
        description: 功能ID
        minimum: 1
      support_merger_payment:
        type: boolean
        description: 是否支持合并支付
      support_online_refund:
        type: boolean
        description: 是否支持在线退款
      refund_today:
        type: boolean
        description: 是否支持当天退款
      stop_refund_time:
        type: string
        description: 停止退款时间
      support_invoice:
        type: boolean
        description: 是否支持开票
      selected_payments:
        type: array
        description: 支付方式
        items:
          type: string
      payment_information:
        type: string
        description: 缴费提示信息
      create_time:
        type: string
        description: 新增时间
        format: date-time
  CustomBusinessSettingDto:
    type: object
    required:
      - name
      - logo
    properties:
      name:
        type: string
        description: 自定义业务名称
        minLength: 1
        maxLength: 50
      logo:
        type: string
        description: 自定义业务logo
        minLength: 1
        maxLength: 250
      wechat_open_path:
        type: string
        description: 微信公众号路径
        minLength: 1
        maxLength: 250
      mini_program_app_id:
        type: string
        description: 小程序appId
        minLength: 1
        maxLength: 30
      mini_program_path:
        type: string
        description: 小程序路径
        minLength: 1
        maxLength: 250
  CustomBusinessSettingVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 自定义业务配置ID
      function_id:
        type: integer
        format: int64
        description: 功能ID
        minimum: 1
      name:
        type: string
        description: 自定义业务名称
      logo:
        type: string
        description: 自定义业务logo
      wechat_open_path:
        type: string
        description: 微信公众号路径
      mini_program_app_id:
        type: string
        description: 小程序appId
      mini_program_path:
        type: string
        description: 小程序路径
      create_time:
        type: string
        description: 新增时间
        format: date-time

  AppointmentRuleSettingDto:
    type: object
    required:
      - department_level
      - system_depends
      - current_system_type
      - appoint_today
      - display_no
      - stop_appoint_time
      - advance_day
      - display_time
      - payment_close_duration
      - refund_today
      - stop_refund_time
      - confirm_medical_insurance_card
      - order_need_verify
      - department_order_type
    properties:
      department_level:
        type: integer
        description: 科室级别，从字典取
      department_order_type:
        type: integer
        description: 科室排序类型：0，倒序；1，正序；
      system_depends:
        type: integer
        description: 挂号系统依赖：0，HIS；1，小系统；
      current_system_type:
        type: integer
        description: 当前使用系統类型：1,老系统;2,新系统;3,新老系统共用;
      doctor_schedule_cache_exp_time:
        type: integer
        description: 医生排班过期时间，单位秒
        format: int64
      appoint_today:
        type: boolean
        description: 是否支持当天预约
      display_no:
        type: boolean
        description: 是否显示序号
      stop_appoint_time:
        type: string
        description: 停止预约时间
      forbidden_day:
        type: integer
        description: 禁止预约天数
        minimum: -1
      advance_day:
        type: integer
        description: 提前预约天数
        minimum: -1
      source_activate_time:
        type: string
        description: 号源激活时间
        default: '00:00:00'
      display_time:
        type: string
        description: 号源显示时间
      payment_close_duration:
        type: integer
        description: 支付关闭时间，单位分钟
        format: int64
        minimum: 0
      refund_today:
        type: boolean
        description: 是否支持当天退款
      stop_refund_time:
        type: string
        description: 停止退款时间
      confirm_medical_insurance_card:
        type: boolean
        description: 是否需要确认医保卡
      order_need_verify:
        type: boolean
        description: 是否需要核销
      appointment_result_notice:
        type: string
        description: 预约结果通知
      appointment_notify_contact:
        type: string
        description: 预约后联系人
      notice_template_id:
        type: string
        description: 通知模板id
      appointment_notice:
        type: string
        description: 预约提示
      appointment_rule:
        type: string
        description: 预约规则
      appointment_guide:
        type: string
        description: 预约指引
      appointment_reminder:
        type: string
        description: 预约提醒
      registration_reminder:
        type: string
        description: 挂号提示
      selected_payments:
        type: array
        description: 支付方式
        items:
          type: string
      display_doctor_under_department:
        type: integer
        description: 科室下医生列表：0，显示科室下所有医生；1，显示有排班的医生；
      need_payment:
        type: boolean
        description: 是否需要支付， 0:不需要，1:需要
      support_locking_appointment_no:
        type: boolean
        description: 是否支持锁号,false:不支持,true:支持
      support_cancel_appointment:
        type: boolean
        description: 是否支持取消预约,false:不支持,true:支持
      need_partition_time:
        type: boolean
        description: 是否需要分时,false:不需要,true:需要
      display_department_address:
        type: boolean
        description: 是否显示科室地址
      display_visit_time:
        type: boolean
        description: 是否显示就诊时间
      need_sign_in:
        type: boolean
        description: 是否需要签到,false:不需要,true:需要
      all_department_background_color:
        type: string
        description: 全科室背景色
      update_department_exclude_columns:
        type: array
        description: 更新科室排班排除字段
        items:
          type: integer
      update_doctor_exclude_columns:
        type: array
        description: 更新医生排班排除字段
        items:
          type: integer
      update_department_depend_his:
        type: boolean
        description: 更新科室排班是否依赖his
      hospital_depend_his_enabled_department:
        type: boolean
        description: 科室是否可用，false：禁用，true：启用
      create_doctor_from_his:
        type: boolean
        description: 是否从his创建医生
      stop_generate_schedule_begin_date:
        type: string
        description: 停止生成排班开始日期
        format: date-time
      stop_generate_schedule_end_date:
        type: string
        description: 停止生成排班结束日期
        format: date-time
      time_period_display_type:
        type: integer
        description: 时间段显示类型：0，不显示；1，显示整个时间段；2，显示有排班的日期；
      doctor_title_show_type:
        type: integer
        description: 医生职称展示类型。1-平台职称，2-医院HIS职称，3-荣誉

  AppointmentRuleSettingUpdateDto:
    allOf:
      - $ref: "#/definitions/AppointmentRuleSettingDto"
      - type: object
        required:
          - id
        properties:
          id:
            type: integer
            format: int64
            description: 预约规则配置ID
            minimum: 1

  AppointmentRuleSettingVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 预约规则配置ID
        minimum: 1
      function_id:
        type: integer
        format: int64
        description: 功能ID
        minimum: 1
      department_level:
        type: integer
        description: 科室级别，从字典取
      department_order_type:
        type: integer
        description: 科室排序类型：0，倒序；1，正序；
      system_depends:
        type: integer
        description: 挂号系统依赖：0，HIS；1，小系统；
      current_system_type:
        type: integer
        description: 当前使用系統类型： 1,老系统;2,新系统;3,新老系统共用
      doctor_schedule_cache_exp_time:
        type: integer
        description: 医生排班过期时间
        format: int64
      appoint_today:
        type: boolean
        description: 是否支持当天预约
      display_no:
        type: boolean
        description: 是否显示序号
      stop_appoint_time:
        type: string
        description: 停止预约时间
      forbidden_day:
        type: integer
        description: 禁止预约天数
        minimum: -1
      advance_day:
        type: integer
        description: 提前预约天数
        minimum: -1
      source_activate_time:
        type: string
        description: 号源激活时间
        default: '00:00:00'
      display_time:
        type: string
        description: 号源显示时间
      payment_close_duration:
        type: integer
        description: 支付关闭时间，单位分钟
        format: int64
        minimum: 0
      refund_today:
        type: boolean
        description: 是否支持当天退款
      stop_refund_time:
        type: string
        description: 停止退款时间
      confirm_medical_insurance_card:
        type: boolean
        description: 是否需要确认医保卡
      order_need_verify:
        type: boolean
        description: 是否需要核销
      appointment_result_notice:
        type: string
        description: 预约结果通知
      appointment_notify_contact:
        type: string
        description: 预约后联系人
      notice_template_id:
        type: string
        description: 通知模板id
      appointment_notice:
        type: string
        description: 预约提示
      appointment_rule:
        type: string
        description: 预约规则
      appointment_guide:
        type: string
        description: 预约指引
      appointment_reminder:
        type: string
        description: 预约提醒
      registration_reminder:
        type: string
        description: 挂号提示
      selected_payments:
        type: array
        description: 支付方式
        items:
          type: string
      display_doctor_under_department:
        type: integer
        description: 科室下医生列表：0，显示科室下所有医生；1，显示有排班的医生；
      all_department_background_color:
        type: string
        description: 全科室背景色
      need_payment:
        type: boolean
        description: 是否需要支付， 0:不需要，1:需要
      support_cancel_appointment:
        type: boolean
        description: 是否支持取消预约,false:不支持,true:支持
      support_locking_appointment_no:
        type: boolean
        description: 是否支持锁号,false:不支持,true:支持
      need_partition_time:
        type: boolean
        description: 是否需要分时,false:不需要,true:需要
      display_department_address:
        type: boolean
        description: 是否显示科室地址
      display_visit_time:
        type: boolean
        description: 是否显示就诊时间
      need_sign_in:
        type: boolean
        description: 是否需要签到,false:不需要,true:需要
      update_department_exclude_columns:
        type: array
        description: 更新科室排班排除字段
        items:
          type: integer
      update_doctor_exclude_columns:
        type: array
        description: 更新医生排班排除字段
        items:
          type: integer
      update_department_depend_his:
        type: boolean
        description: 更新科室排班是否依赖his
      hospital_depend_his_enabled_department:
        type: boolean
        description: 科室是否可用，0：禁用，1：启用
      create_doctor_from_his:
        type: boolean
        description: 是否从his创建医生
      stop_generate_schedule_begin_date:
        type: string
        description: 停止生成排班开始日期
        format: date-time
      stop_generate_schedule_end_date:
        type: string
        description: 停止生成排班结束日期
        format: date-time
      time_period_display_type:
        type: integer
        description: 时间段显示类型：0，不显示；1，显示整个时间段；2，显示有排班的日期；
      create_time:
        type: string
        description: 新增时间
        format: date-time
      update_time:
        type: string
        description: 更新时间
        format: date-time
      doctor_title_show_type:
        type: integer
        description: 医生职称展示类型。1-平台职称，2-医院HIS职称，3-荣誉
  PatientCardSettingDto:
    type: object
    required:
      - need_patient_card
      - bind_type
      - support_patient_type
      - need_electron_card
    properties:
      card_name:
        type: string
        description: 就诊卡名称
      need_patient_card:
        type: boolean
        description: 是否需要就诊卡
      need_recharge:
        type: boolean
        description: 是否需要充值
      bind_type:
        type: integer
        description: 绑定类型：0，自动绑卡；1，手动填写自动开卡；2，手工录入
        enum:
          - 0
          - 1
          - 2
      support_patient_type:
        type: integer
        description: 支持的患者类型：0，全部；1，成人；2，儿童
        enum:
          - 0
          - 1
          - 2
      need_electron_card:
        type: boolean
        description: 是否需要电子就诊卡
  PatientCardSettingUpdateDto:
    allOf:
      - $ref: "#/definitions/PatientCardSettingDto"
      - type: object
        required:
          - id
        properties:
          id:
            type: integer
            format: int64
            description: 就诊卡配置ID
            minimum: 1
  PatientCardSettingVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 就诊卡配置ID
        minimum: 1
      function_id:
        type: integer
        format: int64
        description: 功能ID
        minimum: 1
      card_name:
        type: string
        description: 就诊卡名称
      need_patient_card:
        type: boolean
        description: 是否需要就诊卡
      need_recharge:
        type: boolean
        description: 是否需要充值
      bind_type:
        type: integer
        description: 绑定类型：0，自动绑卡；1，手动填写自动开卡；2，手工录入
        enum:
          - 0
          - 1
          - 2
      support_patient_type:
        type: integer
        description: 支持的患者类型：0，全部；1，成人；2，儿童
      need_electron_card:
        type: boolean
        description: 是否需要电子就诊卡
      create_time:
        type: string
        description: 新增时间
        format: date-time
  PatientReportSettingDto:
    type: object
    properties:
      support_report_type:
        type: integer
        description: 支持的报告类型，0：全部，1：检验报告，2：检查报告
      support_search_date_range:
        type: integer
        description: 支持的按钮刻度，0：无限制，1：30天内，2：90天内，3：180天内，4：一年内
      support_search_time:
        type: array
        description: 支持的查询时间
        items:
          type: integer
          description: 0：全部，1：最近一周，2：最近一月，3：最近三月，4：最近半年，5：最近一年
      notice:
        type: string
        description: 提示消息
  PatientReportSettingVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 报告配置ID
        minimum: 1
      function_id:
        type: integer
        format: int64
        description: 功能ID
        minimum: 1
      support_report_type:
        type: integer
        description: 支持的报告类型，0：全部，1：检验报告，2：检查报告
        enum:
          - 0
          - 1
          - 2
      support_search_date_range:
        type: integer
        description: 支持的按钮刻度，0：无限制，1：30天内，2：90天内，3：180天内，3：一年内
        enum:
          - 0
          - 1
          - 2
          - 3
      support_search_time:
        type: array
        description: 支持的查询时间
        items:
          type: integer
          description: 0：全部，1：最近一周，2：最近一月，3：最近三月，4：最近半年，5：最近一年
          enum:
            - 0
            - 1
            - 2
            - 3
            - 4
            - 5
      notice:
        type: string
        description: 提示消息
      create_time:
        type: string
        description: 新增时间
        format: date-time

  DepartmentCreateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
      - parent_id
      - name
      - first_letter
      - category
    properties:
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
        minimum: 1
      hospital_id:
        type: integer
        description: 医院 id
        format: int64
        minimum: 1
      hospital_area_id:
        type: integer
        description: 院区 id
        format: int64
        minimum: 1
      parent_id:
        type: integer
        description: 父节点科室 id
        format: int64
      thrdpart_dep_code:
        type: string
        description: 第三方科室编码
      name:
        type: string
        description: 科室名称
        minLength: 1
        maxLength: 300
      short_name:
        type: string
        description: 科室简称
      recommended:
        type: boolean
        description: 是否推荐
      category:
        type: array
        items:
          type: integer
          format: int32
          description: 科室类型
      first_letter:
        type: string
        description: 首字母
      introduction:
        type: string
        description: 科室简介
      address_intro:
        type: string
        description: 地址简介
      caution:
        type: string
        description: 科室提醒
      sort:
        type: integer
        description: 排序
      system_depends:
        type: integer
        description: 挂号系统依赖：0,HIS;1,小系统;
      current_system_type:
        type: integer
        description: 当前使用系統类型：1,老系统;2,新系统;3,新老系统共用;99,继承院区配置;
      enabled:
        type: boolean
        description: 是否启用
      display_bg_color:
        type: string
        description: 显示背景色
      enable_department_detail:
        type: boolean
        description: 是否启用科室详情
      uri:
        type: string
        description: 科室跳转地址
      forbidden_day:
        type: integer
        description: 禁止预约天数
        minimum: -1
      advance_day:
        type: integer
        description: 提前预约天数
        minimum: -1
      source_activate_time:
        type: string
        description: 号源激活时间
        default: '00:00:00'
      remark:
        type: string
        description: 备注
      level_tag:
        type: integer
        description: 层级标签，0：不是叶子节点；1:叶子节点
      doctors:
        description: 医生名称或者编码集合
        type: array
        items:
          type: string
      triage_desk_address:
        type: string
        description: 分诊台地址
      display_fields:
        type: integer
        description: 显示字段（0：name、1:displayDepartmentName)
      display_department_name:
        type: string
        description: 显示科室名称

  DepartmentQueryReqDto:
    type: object
    required:
      - current_page
      - page_size
    properties:
      current_page:
        description: 当前页
        type: integer
        default: 1
      page_size:
        description: 每页条数
        type: integer
        default: 10
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
        minimum: 1
      hospital_id:
        description: 医院id
        type: integer
        format: int64
        minimum: 1
      hospital_area_id:
        description: 院区id
        type: integer
        format: int64
        minimum: 1
      id:
        description: 科室id
        type: integer
        format: int64
        minimum: 1
      name:
        description: 科室名称
        type: string
      thrdpart_dep_code:
        description: 第三方科室编码
        type: string
      enabled:
        description: 是否启用
        type: boolean
      category:
        description: 科室类别
        type: array
        items:
          type: integer
          format: int32

  DepartmentVo:
    type: object
    properties:
      id:
        type: integer
        description: 科室 id
        format: int64
      parent_id:
        type: integer
        description: 父节点科室 id
        format: int64
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
      hospital_id:
        type: integer
        description: 医院 id
        format: int64
      hospital_code:
        type: string
        description: 医院编码
      hospital_area_id:
        type: integer
        description: 院区 id
        format: int64
      thrdpart_dep_code:
        type: string
        description: 第三方科室编码
      name:
        type: string
        description: 科室名称
        minLength: 1
        maxLength: 20
      category:
        type: array
        description: 科室类型
        items:
          type: integer
          format: int32
      hospital_name:
        type: string
        description: 医院名称
      hospital_area_name:
        type: string
        description: 院区名称
      system_depends:
        type: integer
        description: 挂号系统依赖：0,HIS;1,小系统;
      current_system_type:
        type: integer
        description: 当前使用系統类型：1,老系统;2,新系统;3,新老系统共用;99,继承院区配置;
      enabled:
        type: boolean
        description: 是否启用
      display_bg_color:
        type: string
        description: 显示背景色
      enable_department_detail:
        type: boolean
        description: 是否启用科室详情
      uri:
        type: string
        description: 科室跳转地址
      first_letter:
        type: string
        description: 首字母
      sort:
        type: integer
        description: 排序
      address_intro:
        type: string
        description: 地址简介
      forbidden_day:
        type: integer
        description: 禁止预约天数
        minimum: -1
      advance_day:
        type: integer
        description: 提前预约天数
        minimum: -1
      source_activate_time:
        type: string
        description: 号源激活时间
        default: '00:00:00'
      remark:
        type: string
        description: 备注
      layer:
        type: integer
        description: 层级
      level_tag:
        type: integer
        description: 层级标签，0：不是叶子节点；1:叶子节点
      create_time:
        type: string
        description: 新增时间
        format: date-time
      doctors:
        description: 医生名称或者编码集合
        type: array
        items:
          type: string
      display_fields:
        type: integer
        description: 显示字段（0：name、1:displayDepartmentName)
      display_department_name:
        type: string
        description: 显示科室名称
      appointment_notify_contact:
        type: string
        description: 预约后联系人
      notice_template_id:
        type: string
        description: 通知模板id

  DepartmentDetailVo:
    allOf:
      - $ref: "#/definitions/DepartmentVo"
      - type: object
        properties:
          short_name:
            type: string
            description: 科室简称
          introduction:
            type: string
            description: 科室简介
          recommended:
            type: boolean
            description: 是否推荐
          caution:
            type: string
            description: 科室提醒
          first_letter:
            type: string
            description: 首字母
          triage_desk_address:
            type: string
            description: 分诊台地址

  FloorQueryReqDto:
    type: object
    required:
      - current_page
      - page_size
    properties:
      current_page:
        description: 当前页
        type: integer
        default: 1
      page_size:
        description: 每页条数
        type: integer
        default: 10
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      building_id:
        description: 医院大楼id
        type: integer
        format: int64
      name:
        description: 楼层名称
        type: string
      status:
        description: 楼层状态,0:正常,1:停用
        type: integer
        enum:
          - 0
          - 1

  DoctorPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/DoctorVo"
  DoctorVo:
    type: object
    properties:
      id:
        type: integer
        description: 医生id
        format: int64
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_code:
        type: string
        description: 院区编码
      hospital_name:
        type: string
        description: 医院名称
      thrdpart_doctor_code:
        type: string
        description: 第三方医生编码
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      hospital_area_name:
        type: string
        description: 院区名称
      department_id:
        type: integer
        description: 科室id
        format: int64
      thrdpart_dep_code:
        type: string
        description: 科室编码
      department_name:
        type: string
        description: 科室名称
      name:
        type: string
        description: 医生姓名
      email:
        type: string
        description: 邮箱
      head_img_url:
        type: string
        description: 头像 URL 地址
      rank_dict_type:
        type: string
        description: 职称字典类型
      rank_dict_label:
        type: array
        description: 职称
        items:
          type: string
      rank_dict_value:
        type: array
        description: 职称字典值
        items:
          type: string
      sort:
        type: integer
        description: 排序
      speciality:
        type: string
        description: 专长
      category:
        description: 医生类别
        type: array
        items:
          type: integer
          format: int32
      system_depends:
        type: integer
        description: 挂号系统依赖：0，HIS；1，小系统；
      status:
        type: integer
        description: 状态：0，正常；1，禁用
      honor:
        type: string
        description: 荣誉
      forbidden_day:
        type: integer
        description: 禁止预约天数
        minimum: -1
      advance_day:
        type: integer
        description: 提前预约天数
        minimum: -1
      source_activate_time:
        type: string
        description: 号源激活时间
        default: '00:00:00'
      multi_department:
        type: boolean
        description: 医生显示多个科室
      need_divide_settlement:
        type: boolean
        description: 是否需要分账
      second_merchant_id:
        type: integer
        description: 分账二级商户id
        format: int64
      settlement_rule_id:
        type: integer
        description: 结算规则id
        format: int64
      display_department_name:
        type: string
        description: 显示科室名称
      create_time:
        type: string
        description: 新增时间
        format: date-time
      tags:
        type: string
        description: 医生标签
      update_from_his:
        type: boolean
        description: 是否从HIS更新医生

  DoctorDetailVo:
    allOf:
      - $ref: "#/definitions/DoctorVo"
      - type: object
        properties:
          introduction:
            type: string
            description: 简介
          appointment_rule_dict_type:
            type: string
            description: 预约规则字典类型
          appointment_rule_dict_label:
            type: array
            description: 预约规则
            items:
              type: string
          statement:
            type: string
            description: 特殊说明
          user_id:
            type: integer
            description: 与在线问诊医生关联的用户 id
            format: int64
            default: 0
          category:
            description: 医生类别
            type: array
            items:
              type: integer
              format: int32
          display:
            type: boolean
            description: C端是否展示该医生
          visiting_address:
            type: string
            description: 出诊地址
          visiting_introduction:
            type: string
            description: 出诊信息
          appointment_notice:
            type: string
            description: 预约提示
          payment_notice:
            type: string
            description: 缴费提示
          judge_appointment_condition:
            type: boolean
            description: 是否判断预约条件
          judge_appointment_rule:
            type: string
            description: 预约条件,|分割
          need_upload_resource:
            type: boolean
            description: 是否需要上传资源
          need_verify_resource:
            type: boolean
            description: 是否需要审核资源
          success_notice_phones:
            type: string
            description: 预约成功通知手机号,分割
          success_template_ids:
            type: string
            description: 预约成功通知模板id,分割

  DoctorCreateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
      - department_id
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      department_id:
        type: integer
        description: 科室id
        format: int64
      department_code:
        type: string
        description: 第三方科室编码
      name:
        type: string
        description: 医生姓名
      email:
        type: string
        description: 邮箱
      thrdpart_doctor_code:
        type: string
        description: 第三方医生编码
      head_img_url:
        type: string
        description: 头像 URL 地址
      speciality:
        type: string
        description: 专长
      rank_dict_type:
        type: string
        description: 职称字典类型
      rank_dict_label:
        type: array
        description: 职称
        items:
          type: string
      introduction:
        type: string
        description: 简介
      appointment_rule_dict_type:
        type: string
        description: 预约规则字典类型
      appointment_rule_dict_label:
        type: array
        description: 预约规则
        items:
          type: string
      statement:
        type: string
        description: 特殊说明
      category:
        description: 医生类别
        type: array
        items:
          type: integer
          format: int32
      display:
        type: boolean
        description: C端是否展示该医生
      system_depends:
        type: integer
        description: 挂号系统依赖：0，HIS；1，小系统；
      status:
        type: integer
        description: 状态：0，正常；1，禁用
      user_id:
        type: integer
        description: 与在线问诊医生关联的用户 id
        format: int64
        default: 0
      sort:
        type: integer
        description: 排序
      honor:
        type: string
        description: 荣誉
      visiting_address:
        type: string
        description: 出诊地址
      visiting_introduction:
        type: string
        description: 出诊信息
      appointment_notice:
        type: string
        description: 预约提示
      payment_notice:
        type: string
        description: 缴费提示
      judge_appointment_condition:
        type: boolean
        description: 是否判断预约条件
      judge_appointment_rule:
        type: string
        description: 预约条件,|分割
      need_upload_resource:
        type: boolean
        description: 是否需要上传资源
      need_verify_resource:
        type: boolean
        description: 是否需要审核资源
      success_notice_phones:
        type: string
        description: 预约成功通知手机号,分割
      success_template_ids:
        type: string
        description: 预约成功通知模板id,分割
      forbidden_day:
        type: integer
        description: 禁止预约天数
        minimum: -1
      advance_day:
        type: integer
        description: 提前预约天数
        minimum: -1
      source_activate_time:
        type: string
        description: 号源激活时间
        default: '00:00:00'
      multi_department:
        type: boolean
        description: 医生显示多个科室
      need_divide_settlement:
        type: boolean
        description: 是否需要分账
      second_merchant_id:
        type: integer
        description: 分账二级商户id
        format: int64
      settlement_rule_id:
        type: integer
        description: 结算规则id
        format: int64
      tags:
        type: string
        description: 医生标签

  DoctorUpdateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
      - department_id
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      department_id:
        type: integer
        description: 科室id
        format: int64
      name:
        type: string
        description: 医生姓名
      email:
        type: string
        description: 邮箱
      thrdpart_doctor_code:
        type: string
        description: 第三方医生编码
      head_img_url:
        type: string
        description: 头像 URL 地址
      speciality:
        type: string
        description: 专长
      rank_dict_type:
        type: string
        description: 职称字典类型
      rank_dict_label:
        type: array
        description: 职称
        items:
          type: string
      introduction:
        type: string
        description: 简介
      appointment_rule_dict_type:
        type: string
        description: 预约规则字典类型
      appointment_rule_dict_label:
        type: array
        description: 预约规则
        items:
          type: string
      statement:
        type: string
        description: 特殊说明
      category:
        description: 医生类别
        type: array
        items:
          type: integer
          format: int32
      display:
        type: boolean
        description: C端是否展示该医生
      system_depends:
        type: integer
        description: 挂号系统依赖：0，HIS；1，小系统；
      status:
        type: integer
        description: 状态：0，正常；1，禁用
      user_id:
        type: integer
        description: 与在线问诊医生关联的用户 id
        format: int64
        default: 0
      sort:
        type: integer
        description: 排序
      honor:
        type: string
        description: 荣誉
      visiting_address:
        type: string
        description: 出诊地址
      visiting_introduction:
        type: string
        description: 出诊信息
      appointment_notice:
        type: string
        description: 预约提示
      payment_notice:
        type: string
        description: 缴费提示
      judge_appointment_condition:
        type: boolean
        description: 是否判断预约条件
      judge_appointment_rule:
        type: string
        description: 预约条件,|分割
      need_upload_resource:
        type: boolean
        description: 是否需要上传资源
      need_verify_resource:
        type: boolean
        description: 是否需要审核资源
      success_notice_phones:
        type: string
        description: 预约成功通知手机号,分割
      success_template_ids:
        type: string
        description: 预约成功通知模板id,分割
      forbidden_day:
        type: integer
        description: 禁止预约天数
        minimum: -1
      advance_day:
        type: integer
        description: 提前预约天数
        minimum: -1
      source_activate_time:
        type: string
        description: 号源激活时间
        default: '00:00:00'
      multi_department:
        type: boolean
        description: 医生显示多个科室
      need_divide_settlement:
        type: boolean
        description: 是否需要分账
      second_merchant_id:
        type: integer
        description: 分账二级商户id
        format: int64
      settlement_rule_id:
        type: integer
        description: 结算规则id
        format: int64
      department_code:
        type: string
        description: 科室编码
      tags:
        type: string
        description: 医生标签
      update_from_his:
        type: boolean
        description: 是否从HIS更新医生

  DoctorKafkaVo:
    type: object
    allOf:
      - $ref: "#/definitions/DoctorVo"
      - type: object
        properties:
          visiting_address:
            type: string
            description: 就诊地址
  DoctorQueryReqDto:
    type: object
    properties:
      current_page:
        description: 当前页
        type: integer
        default: 1
      page_size:
        description: 每页条数
        type: integer
        default: 10
      tenant_id:
        description: 租户id
        type: integer
        format: int64
      hospital_id:
        description: 医院id
        type: integer
        format: int64
      hospital_area_id:
        description: 院区id
        type: integer
        format: int64
      department_id:
        description: 科室id
        type: integer
        format: int64
      id:
        description: 医生id
        type: integer
        format: int64
      name:
        description: 医生姓名
        type: string
      thrdpart_doctor_code:
        description: 第三方医生编码
        type: string
      status:
        type: integer
        description: 状态：0，正常；1，禁用
      start_create_time:
        type: string
        format: date-time
      end_create_time:
        type: string
        format: date-time
      category:
        description: 医生类别：0，预约挂号；1，疫苗；2，在线问诊
        type: array
        items:
          type: integer
          format: int32
      exclude_category:
        description: 医生类别：0，预约挂号；1，疫苗；2，在线问诊
        type: array
        items:
          type: integer
          format: int32
  BuildingQueryReqDto:
    type: object
    required:
      - current_page
      - page_size
    properties:
      current_page:
        description: 当前页
        type: integer
        default: 1
      page_size:
        description: 每页条数
        type: integer
        default: 10
      tenant_id:
        description: 租户id
        type: integer
        format: int64
      hospital_id:
        description: 医院id
        type: integer
        format: int64
      hospital_area_id:
        description: 院区id
        type: integer
        format: int64
      name:
        description: 医院大楼名称
        type: string
      status:
        description: 医院大楼状态,0:正常,1:停用
        type: integer
        enum:
          - 0
          - 1
  AreaQueryReqDto:
    type: object
    properties:
      current_page:
        description: 当前页
        type: integer
        default: 1
      page_size:
        description: 每页条数
        type: integer
        default: 10
      tenant_id:
        description: 租户id
        type: integer
        format: int64
      hospital_id:
        description: 医院id
        type: integer
        format: int64
      hospital_area_id:
        description: 院区id
        type: integer
        format: int64
      building_id:
        description: 大楼id
        type: integer
        format: int64
      floor_id:
        description: 楼层id
        type: integer
        format: int64
      name:
        description: 区域名称
        type: string
      status:
        description: 区域状态,0:正常,1:停用
        type: integer
        enum:
          - 0
          - 1
  HospitalAreaFunctionUpdateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - function
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      id:
        type: integer
        description: 院区功能id
        format: int64
      logo:
        type: string
        description: 院区功能图标icon
      tab_display_style:
        type: string
        description: 院区功能tab展示样式
      status:
        type: integer
        description: 状态,0:开启 1:维护 2:关闭
        enum:
          - 0
          - 1
          - 2
  HospitalAreaFunctionDetailVo:
    type: object
    properties:
      function_list:
        type: array
        description: 院区功能列表
        items:
          $ref: "#/definitions/HospitalAreaFunctionItem"
  HospitalAreaFunctionItem:
    type: object
    required:
      - id
    properties:
      id:
        type: integer
        description: 院区功能id
        format: int64
      name:
        type: string
        description: 院区功能名称
      type:
        type: string
        description: 院区功能类型
      logo:
        type: string
        description: 院区功能图标icon
      tab_display_style:
        type: string
        description: 院区功能tab展示样式
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态,0:开启 1:维护 2:关闭
        enum:
          - 0
          - 1
          - 2
  # HospitalListIndex
  SubmitHospitalListIndexReqDto:
    type: object
    properties:
      index_order:
        type: integer
        description: 医院列表位置
      description:
        type: string
        description: 描述
      rule_id:
        type: integer
        description: 规则id
        format: int64
      hospital_count:
        type: integer
        description: 该位置取的医院数量

  HospitalListIndexVo:
    type: object
    allOf:
      - $ref: "#/definitions/SubmitHospitalListIndexReqDto"
      - type: object
    properties:
      rule_name:
        type: string
        description: 规则名称
      create_time:
        description: 新增时间
        type: string
        format: date-time
      update_time:
        description: 更新时间
        type: string
        format: date-time

  # HospitalListGroup
  CreateHospitalListGroupReqDto:
    type: object
    properties:
      hospitals:
        type: array
        items:
          $ref: "#/definitions/GroupBindHospitalReqDto"
    allOf:
      - $ref: "#/definitions/UpdateHospitalListGroupReqDto"
      - type: object

  HospitalListGroupVo:
    type: object
    allOf:
      - $ref: "#/definitions/UpdateHospitalListGroupReqDto"
      - type: object
    properties:
      id:
        type: integer
        format: int64
      hospital_count:
        type: integer
        description: 绑定医院数量
      create_time:
        description: 新增时间
        type: string
        format: date-time
      update_time:
        description: 更新时间
        type: string
        format: date-time

  HospitalListGroupPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalListGroupVo"

  UpdateHospitalListGroupReqDto:
    type: object
    properties:
      name:
        type: string
        description: 医院组名称
      description:
        type: string
        description: 医院组说明

  GroupBindHospitalReqDto:
    type: object
    properties:
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      weight:
        type: integer
        format: int32
        description: 权重
      platform:
        type: array
        items:
          type: integer
          format: int32
        description: 平台,0微信,1支付宝
  HospitalListGroupDetailVo:
    allOf:
      - $ref: "#/definitions/HospitalListGroupVo"
    properties:
      hospitals:
        type: array
        items:
          type: object
          $ref: "#/definitions/GroupHospitalVo"

  GroupHospitalVo:
    allOf:
      - $ref: "#/definitions/HospitalVo"
    properties:
      weight:
        type: integer
        format: int32
        description: 权重
      display_id:
        type: integer
        format: int64
        description: 医院显示id
      platform:
        type: array
        items:
          type: integer
          format: int32
        description: 平台,0微信,1支付宝

  # HospitalListRule
  CreateHospitalListRuleReqDto:
    type: object
    allOf:
      - $ref: "#/definitions/UpdateHospitalListRuleReqDto"
      - type: object

  HospitalListRuleVo:
    type: object
    allOf:
      - $ref: "#/definitions/UpdateHospitalListRuleReqDto"
      - type: object
    properties:
      id:
        type: integer
        format: int64
      hospital_group_name:
        type: string
        description: 医院组名称
      create_time:
        description: 新增时间
        type: string
        format: date-time
      update_time:
        description: 更新时间
        type: string
        format: date-time

  HospitalListRulePageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalListRuleVo"

  UpdateHospitalListRuleReqDto:
    type: object
    properties:
      name:
        type: string
        description: 规则名称
      description:
        type: string
        description: 规则描述
      type:
        type: integer
        description: 1-固定医院  2-分组距离  3-分组权重  4-广告推荐  5-分组混合距离
      weight_order_by:
        type: integer
        description: 排序方式：1-从低到高 2-从高到低
      distance_order_by:
        type: integer
        description: 距离排序方式：1-从近到远 2-从远到近
      hospital_group_id:
        type: integer
        format: int64
        description: 分组距离模式下对应医院分组ID，当type值为2、3时必填
      advertisement_id:
        type: integer
        format: int64
        description: 广告推荐模式下对应广告id，当type值为4时必填
      advertisement_type:
        type: integer
        format: int32
        description: 广告推荐模式下对应广告类型，当type值为4时必填，1-推荐条件广告
      group_scale:
        type: array
        items:
          $ref: "#/definitions/GroupScaleDto"
        description: 分组混合距离模式下对应混合规则，当type为5时必填，{分组id:取的医院数量}
  GroupScaleDto:
    type: object
    properties:
      group_id:
        type: integer
        format: int64
        description: 分组id
      count:
        type: integer
        description: 该位置取的医院数量

  # HospitalListDisplay
  CreateHospitalListDisplayReqDto:
    type: object
    allOf:
      - $ref: "#/definitions/UpdateHospitalListDisplayReqDto"
      - type: object

  HospitalListDisplayVo:
    type: object
    allOf:
      - $ref: "#/definitions/UpdateHospitalListDisplayReqDto"
      - type: object
    properties:
      id:
        type: integer
        format: int64
      create_time:
        description: 新增时间
        type: string
        format: date-time
      update_time:
        description: 更新时间
        type: string
        format: date-time

  HospitalListDisplayPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalListDisplayVo"

  UpdateHospitalListDisplayReqDto:
    type: object
    properties:
      hospital_id:
        type: integer
        format: int64
        description: 医院 ID
      hospital_name_color_type:
        type: integer
        description: 医院名称颜色，0:默认，1:自定义
      hospital_name_hex_color_code:
        type: string
        description: 设置医院名称颜色，16进制颜色编码
      logo_type:
        type: integer
        description: 医院logo
      display_corner_mark:
        type: integer
        description: 显示角标，0:不显示，1:显示
      corner_mark_style:
        type: string
        description: 角标样式
      display_tag:
        type: integer
        description: 显示标签，0:不显示，1:显示
      tags:
        type: string
        description: 医院标签，通过逗号分隔
      recommended_doctor_id:
        type: integer
        format: int64
        description: 推荐的医生ID

  CreateHospitalAreaDetailPageConfigReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      display_hospital_area:
        type: boolean
        description: 是否显示院区：0，不显示；1，显示；
      display_address:
        type: boolean
        description: 是否显示地址：0，不显示；1，显示；
      display_level:
        type: boolean
        description: 是否显示等级：0，不显示；1，显示；
      display_open_scheduling_time:
        type: boolean
        description: 是否显示医院放号时间：0，不显示；1，显示；
      display_appoint_notice:
        type: boolean
        description: 是否显示预约须知：0，不显示；1，显示；
      display_notice:
        type: boolean
        description: 是否显示医院温馨提示：0，不显示；1，显示；
      display_floor_distribution:
        type: boolean
        description: 是否显示楼层分布：0，不显示；1，显示；
      display_tab:
        type: boolean
        description: 是否显示tab：0，不显示；1，显示；

  HospitalAreaDetailPageConfigVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      display_hospital_area:
        type: boolean
        description: 是否显示院区：0，不显示；1，显示；
      display_address:
        type: boolean
        description: 是否显示地址：0，不显示；1，显示；
      display_level:
        type: boolean
        description: 是否显示等级：0，不显示；1，显示；
      display_open_scheduling_time:
        type: boolean
        description: 是否显示医院放号时间：0，不显示；1，显示；
      display_appoint_notice:
        type: boolean
        description: 是否显示预约须知：0，不显示；1，显示；
      display_notice:
        type: boolean
        description: 是否显示医院温馨提示：0，不显示；1，显示；
      display_floor_distribution:
        type: boolean
        description: 是否显示楼层分布：0，不显示；1，显示；
      display_tab:
        type: boolean
        description: 是否显示tab：0，不显示；1，显示；
      create_time:
        description: 新增时间
        type: string
        format: date-time
      update_time:
        description: 更新时间
        type: string
        format: date-time

  SearchHospitalAreaDetailPageConfigReqDto:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      current_page:
        type: integer
        description: 当前页
        default: 1
      page_size:
        type: integer
        description: 每页条数
        default: 10

  UpdateHospitalAreaDetailPageConfigReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      display_hospital_area:
        type: boolean
        description: 是否显示院区：0，不显示；1，显示；
      display_address:
        type: boolean
        description: 是否显示地址：0，不显示；1，显示；
      display_level:
        type: boolean
        description: 是否显示等级：0，不显示；1，显示；
      display_open_scheduling_time:
        type: boolean
        description: 是否显示医院放号时间：0，不显示；1，显示；
      display_appoint_notice:
        type: boolean
        description: 是否显示预约须知：0，不显示；1，显示；
      display_notice:
        type: boolean
        description: 是否显示医院温馨提示：0，不显示；1，显示；
      display_floor_distribution:
        type: boolean
        description: 是否显示楼层分布：0，不显示；1，显示；
      display_tab:
        type: boolean
        description: 是否显示tab：0，不显示；1，显示；


  HospitalAreaDetailPageConfigPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalAreaDetailPageConfigVo"

  CreateHospitalAreaDetailPageSubNaviModuleReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
      - sub_navi_type
      - sub_navi_display_limit
      - sort
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      sub_navi_type:
        type: integer
        description: 副标题样式：0，固定；1，横向滑动；
        default: 0
      sub_navi_display_limit:
        type: integer
        description: 副标题单行显示上限
        default: 4
      sort:
        type: integer
        description: 排序
        default: 0
      channels:
        type: array
        items:
          type: integer
        description: 渠道

  UpdateHospitalAreaDetailPageSubNaviModuleReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      sub_navi_type:
        type: integer
        description: 副标题样式：0，固定；1，横向滑动；
      sub_navi_display_limit:
        type: integer
        description: 副标题单行显示上限
      sort:
        type: integer
        description: 排序
      channels:
        type: array
        items:
          type: integer
        description: 渠道

  HospitalAreaDetailPageSubNaviModuleVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      sub_navi_type:
        type: integer
        description: 副标题样式：0，固定；1，横向滑动；
      sub_navi_display_limit:
        type: integer
        description: 副标题单行显示上限
      sort:
        type: integer
        description: 排序
      sub_navigation_list:
        type: array
        items:
          $ref: "#/definitions/NaviVo"
      create_time:
        description: 新增时间
        type: string
        format: date-time
      update_time:
        description: 更新时间
        type: string
        format: date-time

  SearchHospitalAreaDetailPageSubNaviModuleReqDto:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      current_page:
        type: integer
        description: 当前页
        default: 1
      page_size:
        type: integer
        description: 每页条数
        default: 10

  HospitalAreaDetailPageSubNaviModulePageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalAreaDetailPageSubNaviModuleVo"

  CreateHospitalAreaDetailPageNavigatorReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      id:
        type: integer
        format: int64
        description: 模块ID
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      type:
        type: integer
        description: 导航栏类型，0:主图文导航，1:副图文导航
        default: 0
      channels:
        type: array
        items:
          type: integer
        description: 渠道
      navi_info:
        type: array
        items:
          type: object
          required:
            - title
            - sub_title
            - picture
            - url
            - sort
          properties:
            title:
              type: string
              description: 主标题
            sub_title:
              type: string
              description: 副标题，副导航栏无效
            picture:
              type: string
              description: 导航图片
            url:
              type: string
              description: 导航链接
            sort:
              type: integer
              description: 排序
              default: 0
            channels:
              type: array
              items:
                type: integer
              description: 渠道

  UpdateHospitalAreaDetailPageNavigatorReqDto:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 模块ID
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      type:
        type: integer
        description: 导航栏类型，0:主图文导航，1:副图文导航
        default: 0
      navi_info:
        type: array
        items:
          $ref: "#/definitions/NaviVo"

  HospitalAreaDetailPageNavigatorVo:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      type:
        type: integer
        description: 导航栏类型，0:主图文导航，1:副图文导航
      channels:
        type: array
        items:
          type: integer
        description: 渠道
      navi_info:
        type: array
        items:
          type: object
          properties:
            id:
              type: integer
              format: int64
            title:
              type: string
              description: 主标题
            sub_title:
              type: string
              description: 副标题，副导航栏无效
            picture:
              type: string
              description: 导航图片
            url:
              type: string
              description: 导航链接
            sort:
              type: integer
              description: 排序
              default: 0
            channels:
              type: array
              items:
                type: integer
              description: 渠道
            create_time:
              description: 新增时间
              type: string
              format: date-time
            update_time:
              description: 更新时间
              type: string
              format: date-time
  NaviVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
      title:
        type: string
        description: 主标题
      sub_title:
        type: string
        description: 副标题，副导航栏无效
      picture:
        type: string
        description: 导航图片
      url:
        type: string
        description: 导航链接
      sort:
        type: integer
        description: 排序
        default: 0
      channels:
        type: array
        items:
          type: integer
        description: 渠道

  SearchHospitalAreaDetailPageNavigatorReqDto:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      navi_module_id:
        type: integer
        format: int64
        description: 副标题模块ID，主标题默认为0
      type:
        type: integer
        description: 导航栏类型，0:主图文导航，1:副图文导航
      current_page:
        type: integer
        description: 当前页
        default: 1
      page_size:
        type: integer
        description: 每页条数
        default: 10

  HospitalAreaDetailPageNavigatorPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalAreaDetailPageNavigatorVo"

  CreateHospitalAreaDetailPageCubeModuleReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID

  UpdateHospitalAreaDetailPageCubeModuleReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      title:
        type: string
        description: 标题
      title_status:
        type: integer
        description: 标题状态：0，展示；1，禁用；
      cube_display_type:
        type: integer
        description: 魔方类型：0，一行两个；1，一行三个；2，二左二右；3，一左二右；4，一左三右
      sort:
        type: integer
        description: 排序
      cube_list:
        type: array
        items:
          type: integer
          format: int64

  HospitalAreaDetailPageCubeModuleVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      title:
        type: string
        description: 标题
      title_status:
        type: integer
        description: 标题状态：0，展示；1，禁用；
      cube_display_type:
        type: integer
        description: 魔方类型：0，一行两个；1，一行三个；2，二左二右；3，一左二右；4，一左三右
      sort:
        type: integer
        description: 排序
      create_time:
        description: 新增时间
        type: string
        format: date-time
      update_time:
        description: 更新时间
        type: string
        format: date-time
      cube_vo:
        type: array
        items:
          $ref: "#/definitions/HospitalAreaDetailPageCubeVo"

  SearchHospitalAreaDetailPageCubeModuleReqDto:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      cube_display_type:
        type: integer
        description: 魔方类型：0，一行两个；1，一行三个；2，二左二右；3，一左二右；4，一左三右
      current_page:
        type: integer
        description: 当前页
        default: 1
      page_size:
        type: integer
        description: 每页条数
        default: 10

  HospitalAreaDetailPageCubeModulePageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalAreaDetailPageCubeModuleVo"

  CreateHospitalAreaDetailPageCubeReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
      - title
      - picture
      - url
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      cube_module_id:
        type: integer
        format: int64
        description: 魔方模块ID
      title:
        type: string
        description: 标题
      picture:
        type: string
        description: 图片
      url:
        type: string
        description: 链接
      sort:
        type: integer
        description: 排序
        default: 0
      channels:
        type: array
        items:
          type: integer
        description: 渠道
      status:
        type: integer
        description: 状态：0，启动；1，禁用；
        default: 0

  UpdateHospitalAreaDetailPageCubeReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      cube_module_id:
        type: integer
        format: int64
        description: 魔方模块ID
      title:
        type: string
        description: 标题
      picture:
        type: string
        description: 图片
      url:
        type: string
        description: 链接
      sort:
        type: integer
        description: 排序
      channels:
        type: array
        items:
          type: integer
        description: 渠道
      status:
        type: integer
        description: 状态：0，启动；1，禁用；
        default: 0

  HospitalAreaDetailPageCubeVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      cube_module_id:
        type: integer
        format: int64
        description: 魔方模块ID
      title:
        type: string
        description: 标题
      picture:
        type: string
        description: 图片
      url:
        type: string
        description: 链接
      sort:
        type: integer
        description: 排序
      channels:
        type: array
        items:
          type: integer
        description: 渠道
      status:
        type: integer
        description: 状态：0，启动；1，禁用；
        default: 0
      create_time:
        description: 新增时间
        type: string
        format: date-time
      update_time:
        description: 更新时间
        type: string
        format: date-time

  SearchHospitalAreaDetailPageCubeReqDto:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      cube_module_id:
        type: integer
        format: int64
        description: 魔方模块ID
      title:
        type: string
        description: 标题
      current_page:
        type: integer
        description: 当前页
        default: 1
      page_size:
        type: integer
        description: 每页条数
        default: 10


  HospitalAreaDetailPageCubePageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalAreaDetailPageCubeVo"

  CreateHospitalAreaDetailPageTabReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
      - title
      - sort
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      title:
        type: string
        description: 标题
      component_type:
        type: integer
        description: 组件类型
      recommend_id:
        type: integer
        format: int64
        description: 推荐ID
      sort:
        type: integer
        description: 排序
        default: 0
      channels:
        type: array
        items:
          type: integer
        description: 渠道
      status:
        type: integer
        description: 状态：0，启动；1，禁用；
        default: 0

  UpdateHospitalAreaDetailPageTabReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      title:
        type: string
        description: 标题
      component_type:
        type: integer
        description: 组件类型
      recommend_id:
        type: integer
        format: int64
        description: 推荐ID
      sort:
        type: integer
        description: 排序
      channels:
        type: array
        items:
          type: integer
        description: 渠道
      status:
        type: integer
        description: 状态：0，启动；1，禁用；
        default: 0

  HospitalAreaDetailPageTabVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      title:
        type: string
        description: 标题
      component_type:
        type: integer
        description: 组件类型
      recommend_id:
        type: integer
        format: int64
        description: 推荐ID
      sort:
        type: integer
        description: 排序
      channels:
        type: array
        items:
          type: integer
        description: 渠道
      status:
        type: integer
        description: 状态：0，启动；1，禁用；
        default: 0
      create_time:
        description: 新增时间
        type: string
        format: date-time
      update_time:
        description: 更新时间
        type: string
        format: date-time

  SearchHospitalAreaDetailPageTabReqDto:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      title:
        type: string
        description: 标题
      component_type:
        type: integer
        description: 组件类型
      current_page:
        type: integer
        description: 当前页
        default: 1
      page_size:
        type: integer
        description: 每页条数
        default: 10

  HospitalAreaDetailPageTabPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalAreaDetailPageTabVo"

  HospitalAreaLayoutVo:
    type: object
    properties:
      map_keyword:
        type: string
        description: 地图关键字
      page_config:
        $ref: "#/definitions/HospitalAreaDetailPageConfigVo"
      navigation_vo:
        $ref: "#/definitions/HospitalAreaDetailPageNavigatorVo"
      sub_navigation_vo:
        type: array
        items:
          description: 医院详情页子导航模块
          type: object
          $ref: "#/definitions/HospitalAreaDetailPageSubNaviModuleVo"
      cube_module_vo:
        type: array
        items:
          description: 医院详情页魔方模块
          $ref: "#/definitions/HospitalAreaDetailPageCubeModuleVo"
      tab_vo:
        type: array
        items:
          description: 医院详情页Tab模块
          $ref: "#/definitions/HospitalAreaDetailPageTabVo"
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      hospital_area_code:
        type: string
        description: 院区编码
      hospital_name:
        type: string
        description: 医院名称
      hospital_area_name:
        type: string
        description: 医院院区名称
      hospital_area_image:
        type: string
        description: 医院院区图片
      hospital_areas:
        type: array
        description: 医院院区集合
        items:
          type: object
          properties:
            hospital_area_id:
              type: integer
              format: int64
              description: 医院院区ID
            hospital_area_name:
              type: string
              description: 医院院区名称
      hospital_area_address:
        $ref: "#/definitions/AddressVo"
      appointment_schedule_time:
        type: string
        description: 放号时间
      appointment_rule:
        type: string
        description: 预约规则
      introduction:
        type: string
        description: 医院介绍
      announcement:
        type: string
        description: 公告，又名：温馨提示
      hospital_area_level:
        type: string
        description: 医院院区等级
      hospital_area_phone:
        type: string
        description: 医院电话

  DepartmentsTreeSearchReqDto:
    type: object
    properties:
      hospital_area_id:
        type: integer
        format: int64
        description: 医院院区ID
      department_name:
        type: string
        description: 科室名称
      thrdpart_dep_code:
        type: string
        description: 科室编码
      category:
        description: 科室类型
        type: array
        items:
          type: integer
          format: int32
      exclude_category:
        description: 科室类型
        type: array
        items:
          type: integer
          format: int32
      enabled:
        type: boolean
        description: 是否启用
      id:
        type: integer
        format: int64
        description: 科室ID

  DepartmentsTreeSearchPageReqDto:
    type: object
    properties:
      current:
        type: integer
        format: int32
        default: 1
        description: 当前页
      size:
        type: integer
        format: int32
        default: 10
        description: 每页显示条数
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_area_id:
        type: integer
        format: int64
        description: 医院院区ID
      department_name:
        type: string
        description: 科室名称
      thrdpart_dep_code:
        type: string
        description: 科室编码
      category:
        description: 科室类型
        type: array
        items:
          type: integer
          format: int32
      exclude_category:
        description: 科室类型
        type: array
        items:
          type: integer
          format: int32
      enabled:
        type: boolean
        description: 是否启用
      id:
        type: integer
        format: int64
        description: 科室ID


  DepartmentsTreeSearchVo:
    type: object
    allOf:
      - $ref: "#/definitions/DepartmentVo"
      - type: object
        properties:
          childrens:
            type: array
            items:
              $ref: "#/definitions/DepartmentsTreeSearchVo"

  DepartmentsTreeSearchListVo:
    type: object
    properties:
      list:
        type: array
        items:
          $ref: "#/definitions/DepartmentsTreeSearchVo"
      level_count:
        type: integer
        description: 科室层级数

  DepartmentsTreeSearchPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/DepartmentVo"


  DictInfoKafkaVo:
    type: object
    properties:
      dict_types:
        type: array
        items:
          $ref: "#/definitions/DictTypeVo"
      dict_labels:
        type: array
        items:
          $ref: "#/definitions/DictLabelVo"

  HospitalAreaQueryLabelsReqDto:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_name:
        type: string
        description: 医院名称
      category:
        type: array
        items:
          type: integer
        description: 院区类型
  HospitalAreaQueryLabelsVo:
    type: object
    properties:
      hospital_list:
        type: array
        items:
          $ref: "#/definitions/HospitalAreaQueryLabelsHospitalVo"
  HospitalAreaQueryLabelsHospitalVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 医院ID
      display_name:
        type: string
        description: 医院名称
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_area_list:
        type: array
        items:
          $ref: "#/definitions/HospitalAreaQueryLabelsHospitalAreaVo"
  HospitalAreaQueryLabelsHospitalAreaVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 院区ID
      name:
        type: string
        description: 院区名称
      display_name:
        type: string
        description: 标签展示名称
      hospital_area_code:
        type: string
        description: 院区编码
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      status:
        type: integer
        format: int32
        description: 状态 0:启用，1:开发中，2:维护，3:禁用
  QueryCustomerHospitalPageReqDto:
    type: object
    properties:
      current:
        type: integer
        format: int32
        default: 1
        description: 当前页
      size:
        type: integer
        format: int32
        default: 10
        description: 每页显示条数
      platform:
        type: integer
        format: int32
        description: 平台 0:微信 1:支付宝
        default: 0
      name:
        type: string
        description: 医院名称
      province:
        type: string
        description: 省
      city:
        type: string
        description: 市
      county:
        type: string
        description: 区
      level:
        type: string
        description: 等级
      sort_type:
        type: integer
        format: int32
        default: 0
        description: 0:综合排序 1:距离排序 2:等级排序
      sort:
        type: integer
        enum:
          - 0
          - 1
        description: 0:升序 1:降序
      longitude:
        type: number
        format: double
        description: 经度
      latitude:
        type: number
        format: double
        description: 纬度
      user_id:
        type: integer
        format: int64
        description: 用户id

  CustomerHospitalPageVo:
    type: object
    allOf:
      - $ref: '#/definitions/BasePage'
      - type: object
        properties:
          records:
            type: array
            items:
              $ref: '#/definitions/CustomerHospitalVo'
  CustomerHospitalVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 医院id
      name:
        type: string
        description: 医院名称
      logo:
        type: string
        description: 医院logo
      distance:
        type: number
        format: double
        description: 距离
      region:
        type: string
        description: 区域
      address_id:
        type: integer
        format: int64
        description: 医院地址id
      status:
        type: integer
        format: int32
        description: 状态 0:启用，1:开发中，2:维护，3:禁用
      is_advertisement:
        type: boolean
        description: 是否是广告
        default: false
      adv_referer:
        type: string
        description: 广告referer
      resource_url:
        type: string
        description: 资源url
      condition_expression:
        type: string
        description: 条件表达式
      redirect_url:
        type: string
        description: 跳转url
      adv_tags:
        type: array
        items:
          type: string
        description: 广告标签
      adv_position:
        type: integer
        description: 广告位置
      adv_remark:
        type: string
        description: 广告备注
      display:
        $ref: '#/definitions/CustomerHospitalListDisplayVo'
      hospital_area_list:
        type: array
        items:
          properties:
            id:
              type: integer
              format: int64
              description: 医院院区id
            code:
              type: string
              description: 医院院区编码
            status:
              type: integer
              format: int32
              description: 状态 0:启用，1:开发中，2:维护，3:禁用
            current_system_type:
              type: integer
              description: 当前使用系統类型：1,老系统;2,新系统;3,新老系统共用;
  CustomerHospitalListDisplayVo:
    type: object
    description: 医院列表展示
    properties:
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_name_color_type:
        type: integer
        description: 医院名称颜色，0:默认，1:自定义
      hospital_name_hex_color_code:
        type: string
        description: 医院名称16进制颜色码
      logo_type:
        type: integer
        enum:
          - 0
          - 1
        description: logo类型 0:默认 1:自定义
      display_corner_mark:
        type: integer
        enum:
          - 0
          - 1
        description: 是否显示角标 0:不显示 1:显示
      corner_mark_style:
        type: string
        description: 角标样式
      display_tag:
        type: integer
        enum:
          - 0
          - 1
        description: 是否显示标签 0:不显示 1:显示
      tags:
        type: string
        description: 标签
      recommended_doctors:
        type: array
        items:
          $ref: '#/definitions/RecommendedDoctorVo'
        description: 推荐医生
      is_last_hospital:
        type: integer
        format: int32
        default: 0
        description: 是否是上次挂号的医院 0:否 1:是
  RecommendedDoctorVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 医生id
      name:
        type: string
        description: 医生姓名
      avatar:
        type: string
        description: 医生头像
      rank:
        type: string
        description: 医生职称
      description:
        type: string
        description: 医生简介
      speciality:
        type: string
        description: 医生擅长
      department_id:
        type: integer
        format: int64
        description: 科室id
      department_name:
        type: string
        description: 科室名称

  # vaccine
  VaccineCategoryVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 苗种id
      name:
        type: string
        description: 苗种名称
      sort:
        type: integer
        format: int32
        description: 排序
      logo:
        type: string
        description: 苗种图标地址
      is_hot:
        type: integer
        format: int32
        description: 是否热门 0 - 否 1 - 是
      hot_sort:
        type: integer
        format: int32
        description: 热门排序
      description:
        type: string
        description: 苗种介绍说明
      tips:
        type: string
        description: 接种须知
      status:
        type: integer
        format: int32
        description: 状态 0 - 正常 1- 禁用
      create_time:
        type: string
        format: date-time
        description: 新增时间
      update_time:
        type: string
        format: date-time
        description: 更新时间
  CreateVaccineCategoryReqDto:
    type: object
    properties:
      name:
        type: string
        description: 苗种名称
      sort:
        type: integer
        format: int32
        description: 排序
      logo:
        type: string
        description: 苗种图标地址
      is_hot:
        type: integer
        format: int32
        description: 是否热门 0 - 否 1 - 是
      hot_sort:
        type: integer
        format: int32
        description: 热门排序
      description:
        type: string
        description: 苗种介绍说明
      tips:
        type: string
        description: 接种须知
      status:
        type: integer
        format: int32
        description: 状态 0 - 正常 1- 禁用
  UpdateVaccineCategoryReqDto:
    type: object
    allOf:
      - $ref: '#/definitions/CreateVaccineCategoryReqDto'
  GetVaccineCategoryPageReqDto:
    type: object
    properties:
      current:
        type: integer
        format: int32
        default: 1
        description: 当前页
      size:
        type: integer
        format: int32
        default: 10
        description: 每页显示条数
      name:
        type: string
        description: 苗种名称
      status:
        type: integer
        format: int32
        description: 状态 0 - 正常 1- 禁用
      is_hot:
        type: integer
        format: int32
        description: 是否热门 0 - 否 1 - 是
  VaccineCategoryPageVo:
    type: object
    allOf:
      - $ref: '#/definitions/BasePage'
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: '#/definitions/VaccineCategoryVo'
  VaccineVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 疫苗id
      vaccine_category_id:
        type: integer
        format: int64
        description: 苗种id
      vaccine_category_name:
        type: string
        description: 苗种名称
      tenant_id:
        type: integer
        format: int64
        description: 租户id
      tenant_name:
        type: string
        description: 租户名称
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_name:
        type: string
        description: 医院名称
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      hospital_area_name:
        type: string
        description: 院区名称
      department_id:
        type: integer
        format: int64
        description: 科室id
      department_name:
        type: string
        description: 科室名称
      doctor_id:
        type: integer
        format: int64
        description: 医生id
      doctor_name:
        type: string
        description: 医生名称
      sort:
        type: integer
        format: int32
        description: 排序
      tips:
        type: string
        description: 预约提示(弹窗等)
      remark:
        type: string
        description: 其他说明(放号时间等)
      status:
        type: integer
        format: int32
        description: 状态 0-正常，1-禁用
      create_time:
        type: string
        format: date-time
        description: 新增时间
      update_time:
        type: string
        format: date-time
        description: 更新时间
  CreateVaccineReqDto:
    type: object
    properties:
      vaccine_category_id:
        type: integer
        format: int64
        description: 苗种id
      doctor_id:
        type: integer
        format: int64
        description: 医生id
      sort:
        type: integer
        format: int32
        description: 排序
      tips:
        type: string
        description: 预约提示(弹窗等)
      remark:
        type: string
        description: 其他说明(放号时间等)
  UpdateVaccineReqDto:
    type: object
    allOf:
      - $ref: '#/definitions/CreateVaccineReqDto'
  GetVaccinePageReqDto:
    type: object
    properties:
      current:
        type: integer
        format: int32
        default: 1
        description: 当前页
      size:
        type: integer
        format: int32
        default: 10
        description: 每页显示条数
      vaccine_id:
        type: integer
        format: int64
        description: 疫苗id
      vaccine_category_id:
        type: integer
        format: int64
        description: 苗种id
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      department_id:
        type: integer
        format: int64
        description: 科室id
      doctor_id:
        type: integer
        format: int64
        description: 医生id
      doctor_name:
        type: string
        description: 医生名称
      status:
        type: integer
        format: int32
        description: 状态 0-正常，1-禁用
  VaccinePageVo:
    type: object
    allOf:
      - $ref: '#/definitions/BasePage'
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: '#/definitions/VaccineVo'

  # recommend-config
  RecommendConfigVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 主键id
      biz_type:
        type: string
        description: 业务类型，例如：专病专诊、特色中医，来自字典配置
      biz_type_desc:
        type: string
        description: 业务类型描述
      data_type:
        type: string
        description: 数据类型，例如：医院、科室、医生，来自字典配置
      data_type_desc:
        type: string
        description: 数据类型描述
      data_tag:
        type: string
        description: 数据标签，当在相同的 biz_type、data_type 下，通过 data_tag，可以同一 data_type 设置多次
      data_id:
        type: string
        description: 数据全局唯一标识
      data_name:
        type: string
        description: 数据名称
      redirect_url:
        type: string
        description: 跳转链接
      sort:
        type: integer
        format: int32
        description: 排序，由大到小
      enabled:
        type: integer
        format: int32
        description: 已启用
      display_tags:
        type: array
        items:
          type: string
        description: 展示标签
      img_url:
        type: string
        description: 图片地址
      display_name:
        type: string
        description: 展示名称
      channels:
        type: array
        items:
          type: integer
        description: 渠道
      reserved_json:
        type: string
        description: 预留字段, json 格式
      create_time:
        type: string
        format: date-time
        description: 新增时间
      update_time:
        type: string
        format: date-time
        description: 更新时间
      key_words:
        type: string
        description: 关键词(用|分隔)
      key_word_list:
        type: array
        items:
          type: string
        description: 关键词列表
      recommend_score:
        type: number
        format: double
        description: 推荐分数
      recommend_reason:
        type: string
        description: 推荐原因
      specially:
        type: string
        description: 擅长
  CreateRecommendConfigReqDto:
    type: object
    properties:
      biz_type:
        type: string
        description: 业务类型，例如：专病专诊、特色中医，来自字典配置
      data_type:
        type: string
        description: 数据类型，例如：医院、科室、医生，来自字典配置
      data_tag:
        type: string
        description: 数据标签，当在相同的 biz_type、data_type 下，通过 data_tag，可以同一 data_type 设置多次
      data_id:
        type: string
        description: 数据全局唯一标识
      redirect_url:
        type: string
        description: 跳转链接
      sort:
        type: integer
        format: int32
        description: 排序，由大到小
      display_tags:
        type: array
        items:
          type: string
        description: 展示标签
      img_url:
        type: string
        description: 图片地址
      display_name:
        type: string
        description: 展示名称
      channels:
        type: array
        items:
          type: integer
        description: 渠道
      enabled:
        type: integer
        format: int32
        description: 已启用
      key_words:
        type: string
        description: 关键词(用|分隔)
      key_word_list:
        type: array
        items:
          type: string
        description: 关键词列表
      recommend_score:
        type: number
        format: double
        description: 推荐分数
      recommend_reason:
        type: string
        description: 推荐原因
      specially:
        type: string
        description: 擅长
  change_key_words:
    type: object
    properties:
      old_key_word:
        type: string
        description: 原关键词
      new_key_word:
        type: string
        description: 新关键词
  UpdateRecommendConfigReqDto:
    type: object
    allOf:
      - $ref: '#/definitions/CreateRecommendConfigReqDto'
    properties:
      change_key_words_list:
        type: array
        description: 修改关键词列表,记录原关键词和新关键词
        items:
          $ref: '#/definitions/change_key_words'
  GetRecommendConfigPageReqDto:
    type: object
    properties:
      current:
        type: integer
        format: int32
        default: 1
        description: 当前页
      size:
        type: integer
        format: int32
        default: 10
        description: 每页显示条数
      biz_type:
        type: string
        description: 业务类型，例如：专病专诊、特色中医，来自字典配置
      data_type:
        type: string
        description: 数据类型，例如：医院、科室、医生，来自字典配置
      data_tag:
        type: string
        description: 数据标签，当在相同的 biz_type、data_type 下，通过 data_tag，可以同一 data_type 设置多次
      data_id:
        type: string
        description: 数据全局唯一标识
      enabled:
        type: integer
        format: int32
        description: 已启用
      channels:
        type: array
        items:
          type: integer
        description: 渠道
      key_words:
        type: string
        description: 关键词(用|分隔)
      recommend_score:
        type: number
        format: double
        description: 推荐分数
  RecommendConfigPageVo:
    type: object
    allOf:
      - $ref: '#/definitions/BasePage'
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: '#/definitions/RecommendConfigVo'
  UpdateRecommendConfigChannelReqDto:
    type: object
    properties:
      recommend_ids:
        type: array
        items:
          type: integer
          format: int64
        description: 推荐配置id
      channels:
        type: array
        items:
          type: integer
        description: 渠道

  GetDoctorGroupPageReqDto:
    type: object
    properties:
      current:
        type: integer
        description: 当前页
        default: 1
      size:
        type: integer
        description: 页大小
        default: 10
      ids:
        type: array
        items:
          type: integer
          format: int64
          description: 医生组id
      name:
        type: string
        description: 医生组名称
      hospitalCodes:
        type: array
        items:
          type: string
          description: 医院编码
      departmentCodes:
        type: array
        items:
          type: string
          description: 科室编码
      doctorCodes:
        type: array
        items:
          type: string
          description: 医生编码
  DoctorGroupPageRespVo:
    type: object
    properties:
      current:
        type: integer
        format: int32
        description: 当前页
      size:
        type: integer
        format: int32
        description: 页大小
      total:
        type: integer
        format: int64
        description: 总条数
      list:
        type: array
        items:
          $ref: "#/definitions/DoctorGroupItemRespVo"
  DoctorGroupItemRespVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 医生id
      name:
        type: string
        description: 医生名称
      title:
        type: string
        description: 职称
      display_name:
        type: string
        description: 展示名称
      hospital_code:
        type: string
        description: 医院编码
      hospital_name:
        type: string
        description: 医院名称
      department_code:
        type: string
        description: 科室编码
      department_name:
        type: string
        description: 科室名称
      doctor_code:
        type: string
        description: 医生编码

  GetExtensionConditionAdvertisementReqDto:
    type: object
    properties:
      is_show:
        type: integer
        format: int32
        description: 是否展示 0-否 1-是
      enable:
        type: boolean
        description: 是否启用
      begin_time_start:
        type: string
        format: date-time
        description: 开始时间起
      begin_time_end:
        type: string
        format: date-time
        description: 开始时间止
      end_time_start:
        type: string
        format: date-time
        description: 结束时间起
      end_time_end:
        type: string
        format: date-time
        description: 结束时间止
  GetExtensionConditionAdvertisementRespVo:
    type: object
    properties:
      list:
        type: array
        items:
          $ref: "#/definitions/GetExtensionConditionAdvertisementItemVo"
  GetExtensionConditionAdvertisementItemVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: extension_condition的id
      extension_id:
        type: integer
        format: int64
        description: 关联表id
      extension_condition_name:
        type: string
        description: 推广条件名称
      advertisement_resource_name:
        type: string
        description: 广告资源名称

  DoctorBindGroupReqDto:
    type: object
    required:
      - doctor_id
      - doctor_group_source
      - doctor_group_id
    properties:
      doctor_id:
        type: integer
        format: int64
        description: 医生id
      doctor_group_source:
        type: integer
        format: int32
        description: 医生团队来源:0,在线问诊;
      doctor_group_id:
        type: integer
        format: int64
        description: 医生团队id
      doctor_group_name:
        type: string
        description: 医生团队名称
  DoctorBatchBindGroupReqDto:
    type: object
    required:
      - doctor_ids
      - doctor_group_source
      - doctor_group_id
    properties:
      doctor_ids:
        type: array
        items:
          type: integer
          format: int64
        description: 医生id
      doctor_group_source:
        type: integer
        format: int32
        description: 医生团队来源:0,在线问诊;
      doctor_group_id:
        type: integer
        format: int64
        description: 医生团队id
      doctor_group_name:
        type: string
        description: 医生团队名称
  DoctorUnbindGroupReqDto:
    type: object
    properties:
      relation_id:
        type: integer
        format: int64
        description: 医生团队关系id
      doctor_id:
        type: integer
        format: int64
        description: 医生id
      doctor_group_source:
        type: integer
        format: int32
        description: 医生团队来源:0,在线问诊;
      doctor_group_id:
        type: integer
        format: int64
        description: 医生团队id
  DoctorGroupRelationQueryReqDto:
    type: object
    required:
      - current_page
      - page_size
    properties:
      current_page:
        type: integer
        description: 当前页
        default: 1
      page_size:
        type: integer
        description: 每页条数
        default: 10
      doctor_id:
        type: integer
        format: int64
        description: 医生id
      doctor_name:
        type: string
        description: 医生名称
      doctor_group_source:
        type: integer
        format: int32
        description: 医生团队来源:0,在线问诊;
      doctor_group_id:
        type: integer
        format: int64
        description: 医生团队id
      doctor_group_name:
        type: string
        description: 医生团队名称
  BatchSyncDoctorInfoFromSpecifiedDoctorReqDto:
    type: object
    required:
      - doctor_id
      - pending_sync_doctor_ids
    properties:
      doctor_id:
        type: integer
        format: int64
        description: 医生id
      pending_sync_doctor_ids:
        type: array
        items:
          type: integer
          format: int64
          description: 待同步的医生id集合
  DoctorGroupRelationQueryRespDto:
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/DoctorGroupRelationVo"
  DoctorGroupRelationVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 医生团队关系id
      doctor_id:
        type: integer
        format: int64
        description: 医生id
      doctor_name:
        type: string
        description: 医生名称
      doctor_code:
        type: string
        description: 医生编码
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_name:
        type: string
        description: 医院名称
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      hospital_area_name:
        type: string
        description: 院区名称
      department_id:
        type: integer
        format: int64
        description: 科室id
      department_name:
        type: string
        description: 科室名称
      status:
        type: integer
        format: int32
        description: 状态 0-正常，1-禁用
      doctor_group_source:
        type: integer
        format: int32
        description: 医生团队来源:0,在线问诊;
      doctor_group_id:
        type: integer
        format: int64
        description: 医生团队id
      doctor_group_name:
        type: string
        description: 医生团队名称

  DictFileCreateReqDto:
    type: object
    required:
      - name
      - type
      - words
    properties:
      name:
        type: string
        description: 字典名称
      type:
        type: integer
        description: 字典类型:0,关键词;1,屏蔽词;
      remark:
        type: string
        description: 备注
      words:
        type: array
        items:
          type: string
          description: 关键字
        description: 关键字集合
  DictFileUpdateReqDto:
    type: object
    required:
      - id
    properties:
      id:
        type: integer
        format: int64
        description: 字典id
      name:
        type: string
        description: 字典名称
      type:
        type: integer
        description: 字典类型:0,关键词;1,屏蔽词;
      words:
        type: array
        items:
          type: string
          description: 关键字
        description: 关键字集合
      remark:
        type: string
        description: 备注
      status:
        type: integer
        description: 状态:0,启用;1,禁用;
  DictFileQueryReqDto:
    type: object
    properties:
      current_page:
        type: integer
        description: 当前页
        default: 1
      page_size:
        type: integer
        description: 每页条数
        default: 10
      name:
        type: string
        description: 字典名称
      type:
        type: integer
        description: 字典类型:0,关键词;1,屏蔽词;
      status:
        type: integer
        description: 状态:0,启用;1,禁用;
  DictFileQueryRespDto:
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/DictFileVo"
  DictFileVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 字典id
      name:
        type: string
        description: 字典名称
      type:
        type: integer
        description: 字典类型:0,关键词;1,屏蔽词;
      words:
        type: array
        items:
          type: string
          description: 关键字
        description: 关键字集合
      remark:
        type: string
        description: 备注
      status:
        type: integer
        description: 状态:0,启用;1,禁用;
      cluster_status:
        type: integer
        description: 集群状态:0,未同步;1,已同步;
      create_time:
        type: string
        format: date-time
        description: 新增时间
      update_time:
        type: string
        format: date-time
        description: 更新时间
      sync_time:
        type: string
        format: date-time
        description: 同步时间

  LocationCreateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
      - type
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户id
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      building_id:
        type: integer
        format: int64
        description: 大楼id
      floor_id:
        type: integer
        format: int64
        description: 楼层id
      area_id:
        type: integer
        format: int64
        description: 区域id
      type:
        type: integer
        description: 位置类型:0,科室;9,自定义;
        minimum: 0
        maximum: 9
      name:
        type: string
        description: 位置名称
      department_id:
        type: integer
        format: int64
        description: 科室id
      sort:
        type: integer
      status:
        type: integer
        format: int32
        description: 状态 0-启用，1-禁用
      triage_desk_address:
        type: string
        description: 分诊台地址
  LocationUpdateReqDto:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户id
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      building_id:
        type: integer
        format: int64
        description: 大楼id
      floor_id:
        type: integer
        format: int64
        description: 楼层id
      area_id:
        type: integer
        format: int64
        description: 区域id
      type:
        type: integer
        description: 位置类型:0,科室;9,自定义;
        minimum: 0
        maximum: 9
      name:
        type: string
        description: 位置名称
      department_id:
        type: integer
        format: int64
        description: 科室id
      sort:
        type: integer
      status:
        type: integer
        format: int32
        description: 状态 0-启用，1-禁用
      triage_desk_address:
        type: string
        description: 分诊台地址
  HospitalAreaPositionTreeQueryReqDto:
    type: object
    required:
      - hospital_area_id
    properties:
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      building_id:
        type: integer
        format: int64
        description: 大楼id
      floor_id:
        type: integer
        format: int64
        description: 楼层id
      area_id:
        type: integer
        format: int64
        description: 区域id
      department_name:
        type: string
        description: 科室名称
      triage_desk_address:
        type: string
        description: 分诊台地址
  HospitalAreaPositionQueryReqDto:
    type: object
    properties:
      current_page:
        type: integer
        description: 当前页
        default: 1
      page_size:
        type: integer
        description: 每页条数
        default: 10
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      building_id:
        type: integer
        format: int64
        description: 大楼id
      floor_id:
        type: integer
        format: int64
        description: 楼层id
      area_id:
        type: integer
        format: int64
        description: 区域id
      type:
        type: integer
        description: 位置类型:0,科室;9,自定义;
        minimum: 0
        maximum: 9
      name:
        type: string
        description: 名称
      triage_desk_address:
        type: string
        description: 分诊台地址
  HospitalAreaPositionQueryRespDto:
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalAreaPositionVo"
  HospitalAreaPositionTreeQueryRespDto:
    type: object
    properties:
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      children:
        type: array
        items:
          $ref: "#/definitions/BuildingPositionVo"
  BuildingPositionVo:
    type: object
    properties:
      building_id:
        type: integer
        format: int64
        description: 大楼id
      building_name:
        type: string
        description: 大楼名称
      status:
        type: integer
        format: int32
        description: 状态 0-启用，1-禁用
      create_time:
        type: string
        format: date-time
        description: 新增时间
      update_time:
        type: string
        format: date-time
        description: 更新时间
      sort:
        type: integer
      location_list:
        type: array
        items:
          $ref: "#/definitions/HospitalAreaPositionVo"
      children:
        type: array
        items:
          $ref: "#/definitions/FloorPositionVo"
  FloorPositionVo:
    type: object
    properties:
      building_id:
        type: integer
        format: int64
        description: 大楼id
      building_name:
        type: string
        description: 大楼名称
      floor_id:
        type: integer
        format: int64
        description: 楼层id
      floor_name:
        type: string
        description: 楼层名称
      status:
        type: integer
        format: int32
        description: 状态 0-启用，1-禁用
      create_time:
        type: string
        format: date-time
        description: 新增时间
      update_time:
        type: string
        format: date-time
        description: 更新时间
      sort:
        type: integer
      location_list:
        type: array
        items:
          $ref: "#/definitions/HospitalAreaPositionVo"
      children:
        type: array
        items:
          $ref: "#/definitions/AreaPositionVo"
  AreaPositionVo:
    type: object
    properties:
      building_id:
        type: integer
        format: int64
        description: 大楼id
      building_name:
        type: string
        description: 大楼名称
      floor_id:
        type: integer
        format: int64
        description: 楼层id
      floor_name:
        type: string
        description: 楼层名称
      area_id:
        type: integer
        format: int64
        description: 区域id
      area_name:
        type: string
        description: 区域名称
      status:
        type: integer
        format: int32
        description: 状态 0-启用，1-禁用
      sort:
        type: integer
      create_time:
        type: string
        format: date-time
        description: 新增时间
      update_time:
        type: string
        format: date-time
        description: 更新时间
      location_list:
        type: array
        items:
          $ref: "#/definitions/HospitalAreaPositionVo"
  HospitalAreaPositionVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 绑定关系id
      tenant_id:
        type: integer
        format: int64
        description: 租户id
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      type:
        type: integer
        description: 位置类型:0,科室;9,自定义;
        minimum: 0
        maximum: 9
      name:
        type: string
        description: 名称
      department_id:
        type: integer
        format: int64
        description: 科室id
      building_id:
        type: integer
        format: int64
        description: 大楼id
      building_name:
        type: string
        description: 大楼名称
      floor_id:
        type: integer
        format: int64
        description: 楼层id
      floor_name:
        type: string
        description: 楼层名称
      area_id:
        type: integer
        format: int64
        description: 区域id
      area_name:
        type: string
        description: 区域名称
      sort:
        type: integer
      status:
        type: integer
        format: int32
        description: 状态 0-启用，1-禁用
      create_time:
        type: string
        format: date-time
        description: 新增时间
      update_time:
        type: string
        format: date-time
        description: 更新时间
      triage_desk_address:
        type: string
        description: 分诊台地址

  TenantUserModifyReqDto:
    type: object
    properties:
      nickname:
        type: string
        description: 昵称
      phone_number:
        type: string
        description: 手机号
      head_image_url:
        type: string
        description: 头像
        minLength: 1
        maxLength: 300
      password:
        type: string
        description: 密码
      old_password:
        type: string
        description: 旧密码
  AppointNotifyConfigVo:
    type: object
    properties:
      admin_list:
        type: array
        items:
          $ref: "#/definitions/HospitalAdminRecipientVo"
  HospitalAdminRecipientVo:
    type: object
    allOf:
      - $ref: "#/definitions/Recipient"
      - type: object
        properties:
          user_id:
            type: integer
            description: 用户id
            format: int64
  SetAppointNotifyConfigReqDto:
    type: object
    properties:
      admin_phone_numbers:
        type: array
        items:
          type: string
          description: 手机号
  SetAppointNotifyConfigResponse:
    type: object
    properties:
      result_map:
        type: object
        description: 配置结果
        additionalProperties:
          type: string

  HospitalAreaBatchSetStopGenerateScheduleReqDto:
    type: object
    properties:
      hospital_codes:
        type: array
        items:
          type: string
        description: 医院编码
      stop_start_date:
        type: string
        format: date-time
        description: 停止生成排班开始时间
      stop_end_date:
        type: string
        format: date-time
        description: 停止生成排班结束时间
  BulletinConfigDto:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 通知公告id
      system_status:
        type: integer
        description: 系统状态：0，正常；1，维护，默认正常
        default: 0
      bulletin_content:
        type: string
        description: 通知公告内容
      status:
        type: integer
        default: 1
        description: 状态：0，正常；1，停用，默认停用
      white_list:
        type: array
        description: 用户白名单列表
        items:
          type: integer
          format: int64
          description: 用户id
  BulletinConfigVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 通知公告id
      system_status:
        type: integer
        description: 系统状态：0，正常；1，维护，默认正常
      bulletin_content:
        type: string
        description: 通知公告内容
      status:
        type: integer
        description: 状态：0，正常；1，停用，默认停用
      white_list:
        type: array
        description: 用户白名单列表
        items:
          type: integer
          format: int64
          description: 用户id
  BulletinConfigReqDto:
    type: object
    properties:
      current_page:
        type: integer
        description: 当前页
        default: 1
      page_size:
        type: integer
        description: 每页条数
        default: 10

  BulletinConfigPageVo:
    type: object
    allOf:
      - $ref: '#/definitions/BasePage'
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: '#/definitions/BulletinConfigVo'
  SetLiveButtonDto:
    type: object
    properties:
      live_url:
        type: string
        description: 直播地址
  TenantUserSendMegReqDto:
    type: object
    required:
      - user_name
    properties:
      user_name:
        type: string
        description: 用户名
      phone_number:
        type: string
        description: 手机号
  GeneratePromoterUrlReqDto:
    type: object
    required:
      - promoter_code
      - target_url
      - promoter_scene
      - promoter_name
    properties:
      promoter_name:
        type: string
        description: 推广员名称
      promoter_code:
        type: string
        description: 推广员编码
      target_url:
        type: string
        description: 目标url
      promoter_scene:
        type: string
        description: 推广场景
  PromoterUrlVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 推广url的id
      promoter_code:
        type: string
        description: 推广员编码
      target_url:
        type: string
        description: 目标url
      promoter_scene:
        type: string
        description: 推广场景
      promoter_name:
        type: string
        description: 推广员名称
      promoter_url:
        type: string
        description: 推广url
      create_time:
        type: string
        format: date-time
        description: 创建时间
      update_time:
        type: string
        format: date-time
        description: 更新时间
  GetPromoterUrlPageReqDto:
    type: object
    allOf:
      - $ref: '#/definitions/BasePage'
      - type: object
        properties:
          current:
            type: integer
            format: int32
            default: 1
            description: 当前页
          size:
            type: integer
            format: int32
            default: 10
            description: 每页显示条数
          promoter_code:
            type: string
            description: 推广员编码
          promoter_name:
            type: string
            description: 推广员名称
          promoter_scene:
            type: string
            description: 推广场景
  PromoterUrlPageVo:
    type: object
    allOf:
      - $ref: '#/definitions/BasePage'
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: '#/definitions/PromoterUrlVo'
  WhiteListCreateReqDto:
    type: object
    required:
      - tenant_id
      - tenant_user_id
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户 ID。
      tenant_user_id:
        type: integer
        format: int64
        description: 租户用户 ID。
      tenant_name:
        type: string
        maxLength: 255
        description: 租户名称，可选字段。
      tenant_user_name:
        type: string
        maxLength: 255
        description: 租户用户名称，可选字段。
  WhiteListUpdateReqDto:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 全局唯一标识，用于确定要更新的记录。
      tenant_id:
        type: integer
        format: int64
        description: 租户 ID，可选字段，用于更新。
      tenant_user_id:
        type: integer
        format: int64
        description: 租户用户 ID，可选字段，用于更新。
      tenant_name:
        type: string
        maxLength: 255
        description: 租户名称，可选字段，用于更新。
      tenant_user_name:
        type: string
        maxLength: 255
        description: 租户用户名称，可选字段，用于更新。
  WhiteListPageReqDto:
    type: object
    allOf:
      - $ref: '#/definitions/BasePage'
      - type: object
        properties:
          tenant_name:
            type: string
            description: 租户名称
          tenant_user_name:
            type: string
            description: 租户用户名称
  DesensitizationWhiteListVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 全局唯一标识
      tenant_id:
        type: integer
        format: int64
        description: 租户 ID
      tenant_name:
        type: string
        description: 租户名称
      tenant_user_id:
        type: integer
        format: int64
        description: 租户用户 ID
      tenant_user_name:
        type: string
        description: 租户用户名称
  WhiteListPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/DesensitizationWhiteListVo"
  HospitalDependOnHisResponse:
    type: object
    properties:
      hospital_id:
        type: Long
        description: 医院id
      hospital_area_id:
        type: Long
        description: 院区id
      hospital_name:
        type: string
        description: 医院名称
      hospital_code:
        type: string
        description: 医院编码

  SourceRecommendConfigPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/DictLabelSourceVo"
  DictLabelSourceVo:
    type: object
    properties:
      id:
        type: integer
        description: 字典标签id
        format: int64
      dict_label:
        type: string
        description: 分组名称
      dict_value:
        type: string
        description: 字典值
      sort:
        type: integer
        description: 序号、排序
      description:
        type: string
        description: 描述
      doctor_num:
        type: integer
        description: 组内医生数量
      create_time:
        type: string
        description: 新增时间
        format: date-time
      update_time:
        type: string
        description: 更新时间
        format: date-time
      status:
        type: integer
        description: 状态
  RecommendConfigDoctorDto:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 主键id
      doctor_id:
        type: integer
        format: int64
        description: 医生id
      doctor_name:
        type: string
        description: 医生名称
      doctor_code:
        type: string
        description: 医生编码
      doctor_title:
        type: string
        description: 医生职称
      doctor_head_img:
        type: string
        description: 医生头像
      department_id:
        type: integer
        format: int64
        description: 科室id
      department_code:
        type: string
        description: 科室编码
      department_name:
        type: string
        description: 科室名称
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      hospital_name:
        type: string
        description: 医院名称
      hospital_area_name:
        type: string
        description: 院区名称
      hospital_code:
        type: string
        description: 医院编码
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态
      dict_label:
        type: string
        description: 分组名称
      data_tag:
        type: string
        description: 推荐分组名称
      dict_label_id:
        type: integer
        format: int64
        description: 分组id
  UpdateRecommendConfigDoctorDto:
    type: array
    items:
      $ref: "#/definitions/RecommendConfigDoctorDto"
  RecommendConfigDoctorListDto:
    type: array
    items:
      $ref: "#/definitions/RecommendConfigDoctorDto"
  MultipleRecommendConfigDoctorDto:
    type: object
    properties:
      list:
        type: array
        items:
          $ref: "#/definitions/RecommendConfigDoctorDto"
      doctor_name:
        type: string
        description: 医生名称
      doctor_code:
        type: string
        description: 医生编码
      doctor_title:
        type: string
        description: 医生职称
      doctor_head_img:
        type: string
        description: 医生头像
      department_id:
        type: integer
        format: int64
        description: 科室id
      department_code:
        type: string
        description: 科室编码
      department_name:
        type: string
        description: 科室名称
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      hospital_name:
        type: string
        description: 医院名称
      hospital_area_name:
        type: string
        description: 院区名称
      hospital_code:
        type: string
        description: 医院编码
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态
      dict_label:
        type: string
        description: 分组名称
      data_tag:
        type: string
        description: 推荐分组名称
      dict_label_id:
        type: integer
        format: int64
        description: 推荐分组id
