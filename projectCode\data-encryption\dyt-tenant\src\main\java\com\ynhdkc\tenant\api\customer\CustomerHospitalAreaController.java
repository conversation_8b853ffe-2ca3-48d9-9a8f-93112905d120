package com.ynhdkc.tenant.api.customer;

import backend.common.exception.BizException;
import com.ynhdkc.tenant.handler.CustomerHospitalAreaApi;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.customer.CustomerHospitalAreaService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/7/19 16:35
 */
@Api(tags = "CustomerHospitalArea")
@RestController
@RequiredArgsConstructor
public class CustomerHospitalAreaController implements CustomerHospitalAreaApi {
    private final CustomerHospitalAreaService customerHospitalAreaService;

    private static void checkHospitalUniqueParams(Long hospitalAreaId, String hospitalAreaCode) {
        if (null == hospitalAreaId && null == hospitalAreaCode) {
            throw new BizException(HttpStatus.BAD_REQUEST, "hospitalAreaId和hospitalAreaCode不能同时为空");
        }
    }

    @Override
    public ResponseEntity<CustomerHospitalAreaDetailVo> getDetail(Long hospitalAreaId, String hospitalAreaCode) {
        checkHospitalUniqueParams(hospitalAreaId, hospitalAreaCode);
        return ResponseEntity.ok(customerHospitalAreaService.getDetail(hospitalAreaId, hospitalAreaCode));
    }

    @Override
    public ResponseEntity<CustomerHospitalAreaDetailVo> getDetailAndHospitalInfo(Long hospitalAreaId, String hospitalAreaCode) {
        checkHospitalUniqueParams(hospitalAreaId, hospitalAreaCode);
        return ResponseEntity.ok(customerHospitalAreaService.getDetailAndHospitalInfo(hospitalAreaId, hospitalAreaCode));
    }

    @Override
    public ResponseEntity<CustomerHospitalAreaPositionVo> getHospitalAreaPosition(CustomerHospitalAreaPositionReqDto request) {
        checkHospitalUniqueParams(request.getHospitalAreaId(), request.getHospitalAreaCode());
        return ResponseEntity.ok(customerHospitalAreaService.getHospitalAreaPosition(request));
    }

    @Override
    public ResponseEntity<CustomerHospitalAreaLayoutVo> getLayout(Integer channel, Long hospitalAreaId, String hospitalAreaCode) {
        checkHospitalUniqueParams(hospitalAreaId, hospitalAreaCode);
        return ResponseEntity.ok(customerHospitalAreaService.getLayout(hospitalAreaId, hospitalAreaCode, channel));
    }

    @Override
    public ResponseEntity<CustomerHospitalAreaSettingDetailVo> getSettingDetail(Long hospitalAreaId, String hospitalAreaCode) {
        checkHospitalUniqueParams(hospitalAreaId, hospitalAreaCode);
        return ResponseEntity.ok(customerHospitalAreaService.getSettingDetail(hospitalAreaId, hospitalAreaCode));
    }

    @Override
    public ResponseEntity<CustomerHospitalAreaPageVo> queryHospitalAreaPage(CustomerQueryHospitalAreaPageReqDto request) {
        return ResponseEntity.ok(customerHospitalAreaService.queryHospitalAreaPage(request));
    }

    @Override
    public ResponseEntity<CustomAppointmentRuleSettingVo> getAppointmentRuleSettingDetail(String hospitalAreaCode) {
        return ResponseEntity.ok(customerHospitalAreaService.getAppointmentRuleSettingDetail(hospitalAreaCode));
    }
}
