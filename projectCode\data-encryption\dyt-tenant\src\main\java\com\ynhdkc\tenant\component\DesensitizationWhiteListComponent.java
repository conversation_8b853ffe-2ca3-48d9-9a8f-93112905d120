package com.ynhdkc.tenant.component;/**
 * <AUTHOR>
 * @date 2024/07/24/14:41
 */

import com.xxl.job.core.handler.annotation.XxlJob;
import com.ynhdkc.tenant.dao.DesensitizationWhiteListQuery;
import com.ynhdkc.tenant.entity.DesensitizationWhiteList;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/7/24 14:41
 **/
@Component
@RequiredArgsConstructor
public class DesensitizationWhiteListComponent {
    private final static String WHITE_LIST_KEY = "dyt-tenant:desensitization:white-list";

    private final DesensitizationWhiteListQuery desensitizationWhiteListRepository;
    private final RedisTemplate<String, Object> redisTemplate;

    @XxlJob("desensitizationWhiteListInit")
    public void init() {
        List<DesensitizationWhiteList> desensitizationWhiteLists = desensitizationWhiteListRepository.queryAll();
        if (!CollectionUtils.isEmpty(desensitizationWhiteLists)) {
            redisTemplate.delete(WHITE_LIST_KEY);
            desensitizationWhiteLists.forEach(desensitizationWhiteList -> {
                redisTemplate.opsForSet().add(WHITE_LIST_KEY, desensitizationWhiteList.getTenantUserId());
            });


        }
    }
}
