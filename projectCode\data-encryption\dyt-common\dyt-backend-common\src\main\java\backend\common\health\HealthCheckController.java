package backend.common.health;

import org.springframework.boot.actuate.health.Status;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.AbstractController;
import org.springframework.web.servlet.view.json.MappingJackson2JsonView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


public class HealthCheckController extends AbstractController {

    final List<LazyHealthIndicatorWrapper> wrappers;
    private final boolean checkDatabase;
    private final boolean checkRedis;
    private final MappingJackson2JsonView jsonViewer;
    private volatile boolean stopped = false;


    public HealthCheckController(List<LazyHealthIndicatorWrapper> wrappers, boolean checkDatabase, boolean checkRedis) {
        this.wrappers = wrappers;
        this.checkDatabase = checkDatabase;
        this.checkRedis = checkRedis;
        this.jsonViewer = new MappingJackson2JsonView();
    }


    @Override
    protected ModelAndView handleRequestInternal(HttpServletRequest request, HttpServletResponse response) throws Exception {
        ModelAndView mav = new ModelAndView(jsonViewer);
        StringBuilder message = new StringBuilder();
        if (stopped) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            mav.addObject("status", "server is stopping");
            return mav;
        }

        for (LazyHealthIndicatorWrapper wrapper : wrappers) {
            if (checkDatabase) {
                HealthResult result = wrapper.doHealthCheck(HealthIndicatorType.DataSource);
                if (result.getStatus() != Status.UP) {
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    mav.addObject("status", "check database result: " + result.getStatus());
                    return mav;
                }
                message.append("check database result: ").append(result).append("|");
            }

            if (checkRedis) {
                HealthResult redis = wrapper.doHealthCheck(HealthIndicatorType.Redis);
                if (redis.getStatus() != Status.UP) {
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    mav.addObject("status", "check redis result: " + redis.getStatus());
                    return mav;
                }
                message.append("check redis result: ").append(redis).append("|");
            }
        }
        mav.addObject("status", "OK " + message);
        return mav;
    }


    public void stopped() {
        this.stopped = true;
    }
}
