package com.example.encryption;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 数据库敏感数据加密存储POC应用启动类
 *
 * 功能特性：
 * 1. 使用JPA AttributeConverter实现透明加密/解密
 * 2. 支持AES-256-GCM算法
 * 3. 自定义注解标记敏感字段
 * 4. 提供完整的CRUD操作示例
 *
 * 访问地址：
 * - 应用主页: http://localhost:8080
 * - H2控制台: http://localhost:8080/h2-console
 * - API文档: http://localhost:8080/api/users
 */
@SpringBootApplication(scanBasePackages = "com.example.encryption")
@EnableTransactionManagement
public class EncryptionDemoApplication {

    public static void main(String[] args) {
        SpringApplication.run(EncryptionDemoApplication.class, args);

        System.out.println("\n" +
            "=================================================================\n" +
            "  数据库敏感数据加密存储POC应用已启动\n" +
            "=================================================================\n" +
            "  应用端口: 8080\n" +
            "  H2控制台: http://localhost:8080/h2-console\n" +
            "  数据库URL: jdbc:h2:mem:testdb\n" +
            "  用户名: sa\n" +
            "  密码: password\n" +
            "=================================================================\n" +
            "  API接口:\n" +
            "  GET    /api/users              - 获取所有用户\n" +
            "  POST   /api/users              - 创建用户\n" +
            "  GET    /api/users/{id}         - 根据ID获取用户\n" +
            "  PUT    /api/users/{id}         - 更新用户\n" +
            "  DELETE /api/users/{id}         - 删除用户\n" +
            "  GET    /api/users/username/{username} - 根据用户名获取用户\n" +
            "  GET    /api/users/phone/{phone}       - 根据手机号获取用户\n" +
            "  GET    /api/users/email/{email}       - 根据邮箱获取用户\n" +
            "=================================================================\n");
    }
}
