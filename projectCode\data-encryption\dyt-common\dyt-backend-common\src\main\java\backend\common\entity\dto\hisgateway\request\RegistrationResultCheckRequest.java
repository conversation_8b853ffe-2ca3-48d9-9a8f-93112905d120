package backend.common.entity.dto.hisgateway.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class RegistrationResultCheckRequest {

    @JsonProperty("medical_record_number")
    private String medicalRecordNumber;

    @JsonProperty("registration_date")
    private String registrationDate;

    // 微信等支付机构订单号
    @JsonProperty("trade_serial_number")
    private String tradeSerialNumber;

    // his 锁号订单号
    @JsonProperty("lock_registration_number")
    private String lockRegistrationNumber;

    @JsonProperty("appointment_serial_number")
    private String appointmentSerialNumber;

    @JsonProperty("order_number")
    private String orderNumber;

    @JsonProperty("registration_level")
    private String registrationLevel;

    @JsonProperty("department_name")
    private String departmentName;

    @JsonProperty("hospital_code")
    private String hospitalCode;

    @JsonProperty("department_code")
    private String departmentCode;

    @JsonProperty("doctor_name")
    private String doctorName;

    @JsonProperty("doctor_code")
    private String doctorCode;

    @JsonProperty("schedule_id")
    private String scheduleId;

    @JsonProperty("visiting_serial_number")
    private String visitingSerialNumber;

}
