package com.ynhdkc.tenant.api.backend;

import backend.common.response.BaseOperationResponse;
import backend.security.method.BackendSecurityRequired;
import backend.security.oplog.DytSecurityRequired;
import com.ynhdkc.tenant.handler.OrganizationStructureApi;
import com.ynhdkc.tenant.service.backend.OrganizationStructureService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-28 17:42
 */
@Api(tags = "OrganizationStructure")
@RestController
@RequiredArgsConstructor
public class OrganizationStructureController implements OrganizationStructureApi {
    private final OrganizationStructureService organizationStructureService;

    @BackendSecurityRequired(tenantManager = true)
    @DytSecurityRequired(needOpLog = true, value = "organization:structure:sync")
    @Override
    public ResponseEntity<BaseOperationResponse> syncOrganizationStructure() {
        organizationStructureService.syncOrganizationStructure();
        return ResponseEntity.ok(new BaseOperationResponse("同步成功"));
    }

    @BackendSecurityRequired(tenantManager = true)
    @DytSecurityRequired(needOpLog = true, value = "organization:user:sync")
    @Override
    public ResponseEntity<BaseOperationResponse> syncUserStructure() {
        organizationStructureService.syncUserOrganizationStructure();
        return ResponseEntity.ok(new BaseOperationResponse("同步成功"));
    }
}
