package com.ynhdkc.tenant.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * RpcRoleBoundUsersQueryVo
 */
@Validated
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2023-04-14T13:35:23.401+08:00")


public class RpcRoleBoundUsersQueryVo {
    @JsonProperty("tenant_id")
    private Long tenantId = null;

    @JsonProperty("bound_list")
    @Valid
    private List<Long> boundList = null;

    public RpcRoleBoundUsersQueryVo tenantId(Long tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    /**
     * Get tenantId
     *
     * @return tenantId
     **/
    @ApiModelProperty(value = "")


    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public RpcRoleBoundUsersQueryVo boundList(List<Long> boundList) {
        this.boundList = boundList;
        return this;
    }

    public RpcRoleBoundUsersQueryVo addBoundListItem(Long boundListItem) {
        if (this.boundList == null) {
            this.boundList = new ArrayList<Long>();
        }
        this.boundList.add(boundListItem);
        return this;
    }

    /**
     * Get boundList
     *
     * @return boundList
     **/
    @ApiModelProperty(value = "")


    public List<Long> getBoundList() {
        return boundList;
    }

    public void setBoundList(List<Long> boundList) {
        this.boundList = boundList;
    }


    @Override
    public boolean equals(java.lang.Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        RpcRoleBoundUsersQueryVo rpcRoleBoundUsersQueryVo = (RpcRoleBoundUsersQueryVo) o;
        return Objects.equals(this.tenantId, rpcRoleBoundUsersQueryVo.tenantId) &&
                Objects.equals(this.boundList, rpcRoleBoundUsersQueryVo.boundList);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tenantId, boundList);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class RpcRoleBoundUsersQueryVo {\n");

        sb.append("    tenantId: ").append(toIndentedString(tenantId)).append("\n");
        sb.append("    boundList: ").append(toIndentedString(boundList)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(java.lang.Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}

