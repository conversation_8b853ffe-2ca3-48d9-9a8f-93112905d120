package com.ynhdkc.tenant.service.customer.impl;

import backend.common.domain.tenant.constant.AppointmentSystemDepends;
import backend.common.entity.dto.hisgateway.response.DepartmentDoctorItem;
import backend.common.util.JsonUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.ynhdkc.oldsystem.integration.api.request.GetDytkfDoctorGroupListReqDto;
import com.ynhdkc.oldsystem.integration.api.response.QueryDoctorGroupRespDto;
import com.ynhdkc.oldsystem.integration.client.DytkfDoctorGroupClient;
import com.ynhdkc.tenant.client.AppointmentOrderClient;
import com.ynhdkc.tenant.client.ScheduleClient;
import com.ynhdkc.tenant.client.model.ApiOrderAppointment;
import com.ynhdkc.tenant.client.model.ApiSchedulePageResponse;
import com.ynhdkc.tenant.client.model.ApiScheduleResponseItem;
import com.ynhdkc.tenant.dao.DepartmentQuery;
import com.ynhdkc.tenant.dao.DoctorQuery;
import com.ynhdkc.tenant.dao.HospitalAreaQuery;
import com.ynhdkc.tenant.dao.mapper.*;
import com.ynhdkc.tenant.entity.*;
import com.ynhdkc.tenant.entity.constant.ChannelSourceType;
import com.ynhdkc.tenant.entity.constant.RecommendConfigConstant;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.AddressService;
import com.ynhdkc.tenant.service.backend.ChannelSourceService;
import com.ynhdkc.tenant.service.customer.CustomerDepartmentService;
import com.ynhdkc.tenant.service.customer.CustomerRecommendService;
import com.ynhdkc.tenant.tool.CacheComponent;
import com.ynhdkc.tenant.tool.ScheduleUtil;
import com.ynhdkc.tenant.util.CommonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ynhdkc.tenant.tool.convert.DoctorConvert.toDoctorAllScheduleInfoVo;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CustomerRecommendServiceImpl implements CustomerRecommendService {
    private static final String HOSPITAL_AREA_RECOMMEND_DATA_TYPE = "5";
    private static final String DEPARTMENT_RECOMMEND_CHANNEL = "2";
    private final RecommendConfigMapper recommendConfigMapper;
    private final HospitalMapper hospitalMapper;
    private final DepartmentQuery departmentQuery;
    private final DoctorQuery doctorQuery;
    private final DepartmentMapper departmentMapper;
    private final AddressMapper addressMapper;
    private final DytkfDoctorGroupClient dytkfDoctorGroupClient;
    private final ScheduleClient scheduleClient;
    private final CacheComponent cacheComponent;
    private final ChannelSourceService channelSourceService;
    private final ScheduleUtil scheduleUtil;
    private final HospitalAreaQuery hospitalAreaQuery;
    private final RecommendDoctorConfigMapper recommendDoctorConfigMapper;
    private final AppointmentOrderClient appointmentOrderClient;
    private final CustomerDepartmentService customerDepartmentService;
    private final AddressService addressService;

    private static <T extends RecommendBaseItemVo> void handleCommonRecommendProperties(RecommendConfig recommendConfig, T vo) {
        vo.setRecommendId(recommendConfig.getId());
        vo.setRedirectUrl(recommendConfig.getRedirectUrl());
        vo.setBizType(recommendConfig.getBizType());
        vo.setDataType(recommendConfig.getDataType());
        vo.setDataTag(recommendConfig.getDataTag());
        vo.setImgUrl(recommendConfig.getImgUrl());
        vo.setRecommendSort(recommendConfig.getSort());
        vo.setDisplayName(recommendConfig.getDisplayName());
        String displayTags = recommendConfig.getDisplayTags();
        if (!ObjectUtils.isEmpty(displayTags)) {
            vo.setDisplayTags(Arrays.asList(displayTags.split(",")));
        }
    }

    private static void fillDoctorInfo(List<Long> doctorIds, List<Doctor> doctors, CustomerDoctorScheduleInfoVo finalVo, List<RecommendDoctorConfig> recommendDoctorConfigs) {
        doctorIds.forEach(doctorId -> {
            Doctor doctor = doctors.stream().filter(doctor1 -> doctor1.getId().equals(doctorId)).findFirst().orElse(null);
            if (doctor != null) {
                CustomerAllScheduleDoctorDetailVo customerAllScheduleDoctorDetailVo = toDoctorAllScheduleInfoVo(doctor);
                customerAllScheduleDoctorDetailVo.setCanOrder(false);
                customerAllScheduleDoctorDetailVo.setShowSchDate(false);
                if (recommendDoctorConfigs != null) {
                    recommendDoctorConfigs.forEach(recommendDoctorConfig -> {
                        if (recommendDoctorConfig.getDoctorId().equals(doctorId)) {
                            customerAllScheduleDoctorDetailVo.setDataTag(recommendDoctorConfig.getDataTag());
                        }
                    });
                }
                finalVo.getAllDoctors().add(customerAllScheduleDoctorDetailVo);
            }
        });
    }

    @Override
    public RecommendRespVo getRecommendList(String bizType, Integer channel, String dataType, String dataTag, Double longitude, Double latitude) {
        List<Long> recommendIds = channelSourceService.selectSourceIdByChannel(ChannelSourceType.RECOMMEND_CONFIG, Collections.singletonList(channel));
        if (CollectionUtils.isEmpty(recommendIds)) {
            return new RecommendRespVo();
        }
        List<RecommendConfig> recommendConfigs = recommendConfigMapper.selectByExample2(RecommendConfig.class,
                sql -> sql.andEqualTo(RecommendConfig::getEnabled, 1)
                        .andEqualTo(RecommendConfig::getBizType, bizType)
                        .andIn(RecommendConfig::getId, recommendIds));
        if (recommendConfigs.isEmpty()) {
            return new RecommendRespVo();
        }
        if (!ObjectUtils.isEmpty(dataType)) {
            recommendConfigs = recommendConfigs.stream()
                    .filter(recommendConfig -> recommendConfig.getDataType().equals(dataType))
                    .collect(Collectors.toList());
        }
        if (!ObjectUtils.isEmpty(dataTag)) {
            recommendConfigs = recommendConfigs.stream()
                    .filter(recommendConfig -> dataTag.equals(recommendConfig.getDataTag()))
                    .collect(Collectors.toList());
        }

        return handleRecommendResp(recommendConfigs, longitude, latitude);
    }

    @Override
    public RecommendRespVo getRecommendById(Long recommendId, Double longitude, Double latitude) {
        RecommendConfig recommendConfig = recommendConfigMapper.selectByPrimaryKey(recommendId);
        if (recommendConfig == null) {
            return new RecommendRespVo();
        }
        return handleRecommendResp(Collections.singletonList(recommendConfig), longitude, latitude);
    }

    @Override
    public List<RecommendConfigDepartmentDto> getRecommendDepartment(String bizType) {
        List<RecommendConfig> recommendConfigs = recommendConfigMapper.selectByExample2(RecommendConfig.class,
                sql -> sql.andEqualTo(RecommendConfig::getEnabled, 1)
                        .andEqualTo(RecommendConfig::getBizType, bizType));
        if (CollectionUtils.isEmpty(recommendConfigs)) {
            return new ArrayList<>();
        }
        return recommendConfigs.stream()
                .map(recommendConfig -> {
                    RecommendConfigDepartmentDto dto = new RecommendConfigDepartmentDto();
                    dto.setSort(recommendConfig.getSort());
                    dto.setDepartmentName(recommendConfig.getDataTag());
                    dto.setBizType(bizType);
                    return dto;
                })
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public CustomRecommendDoctorPageVo getRecommendDepartmentDetail(CustomRecommendDoctorQueryDto request) {
        CustomRecommendDoctorPageVo pageVo = new CustomRecommendDoctorPageVo();
        pageVo.setCurrentPage(request.getPageNum());
        pageVo.setPageSize(request.getPageSize());

        List<RecommendConfig> recommendConfigList = fetchRecommendConfigs(request);

        if (!CollectionUtils.isEmpty(recommendConfigList)) {
            List<CustomRecommendDepartInfoVo> voList = transformRecommendConfigToVoList(recommendConfigList, request);
            if (!ObjectUtils.isEmpty(request.getHospitalAreaId())) {
                voList = voList.stream().filter(vo -> vo.getHospitalAreaId().equals(request.getHospitalAreaId())).collect(Collectors.toList());
            }
            pageVo._list(voList);
            pageVo.setTotalSize((long) recommendConfigList.size());
        } else {
            pageVo.setList(new ArrayList<>());
            pageVo.setTotalSize(0L);
        }

        return pageVo;
    }

    private List<RecommendConfig> fetchRecommendConfigs(CustomRecommendDoctorQueryDto request) {
        Example example = buildExampleFromQueryDto(request);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        return recommendConfigMapper.selectByExample(example);
    }

    private List<CustomRecommendDepartInfoVo> transformRecommendConfigToVoList(List<RecommendConfig> recommendConfigList, CustomRecommendDoctorQueryDto request) {

        List<Long> departmentIds = extractDepartmentIds(recommendConfigList);
        List<Department> departments = departmentQuery.queryBy(departmentIds);
        List<String> hospitalCodes = extractHospitalCodes(departments);
        List<Hospital> hospitals = hospitalAreaQuery.queryAll();
        List<Long> hospitalAreaIds = recommendConfigList.stream().filter(recommendConfig -> recommendConfig.getDataType().equals(HOSPITAL_AREA_RECOMMEND_DATA_TYPE)).map(RecommendConfig::getDataId).map(Long::valueOf).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(hospitalAreaIds)) {
            hospitals.addAll(hospitalAreaQuery.queryParentHospital(hospitalAreaIds));
        }
        List<Long> hospitalIds = hospitals.stream().map(Hospital::getParentId).collect(Collectors.toList());
        List<Hospital> hospitalParentList = hospitalAreaQuery.queryAll();
        List<Long> recommendConfigIdList = extractRecommendConfigIds(recommendConfigList);
        List<RecommendDoctorConfig> recommendDoctorConfigList = fetchRecommendDoctorConfigs(recommendConfigIdList);
        List<Long> doctorIds = extractDoctorIds(recommendDoctorConfigList);
        List<Doctor> doctors = doctorQuery.queryBy(doctorIds);

        List<Long> addressIds = hospitals.stream().map(Hospital::getAddressId).collect(Collectors.toList());
        List<Address> addresses = addressMapper.selectByExample2(Address.class, sql -> sql.andIn(Address::getId, addressIds));
        List<CustomRecommendDepartInfoVo> voList = mapRecommendConfigToVoList(recommendConfigList, departments, hospitals, recommendDoctorConfigList, doctors, hospitalParentList, addresses);
        if (!CollectionUtils.isEmpty(voList) && request.getLatitude() != null && request.getLongitude() != null) {
            voList.forEach(vo -> {
                Address address = addresses.stream().filter(addressItem -> addressItem.getId().equals(vo.getAddressId())).findFirst().orElse(null);
                if (address != null) {
                    vo.setDistance(CommonUtil.getDistance(address.getLongitude().doubleValue(), address.getLatitude().doubleValue(), request.getLongitude(), request.getLatitude()));
                } else {
                    vo.setDistance(0.0);
                }
            });
            voList = voList.stream()
                    .sorted(Comparator.comparing(CustomRecommendDepartInfoVo::getDistance))
                    .collect(Collectors.toList());
        }
        return voList;
    }

    private List<Long> extractDepartmentIds(List<RecommendConfig> recommendConfigList) {
        return recommendConfigList.stream()
                .map(RecommendConfig::getDataId)
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

    private List<String> extractHospitalCodes(List<Department> departments) {
        return departments.stream()
                .map(Department::getHospitalCode)
                .collect(Collectors.toList());
    }

    private List<Long> extractRecommendConfigIds(List<RecommendConfig> recommendConfigList) {
        return recommendConfigList.stream()
                .map(RecommendConfig::getId)
                .collect(Collectors.toList());
    }

    private List<RecommendDoctorConfig> fetchRecommendDoctorConfigs(List<Long> recommendConfigIdList) {
        return recommendDoctorConfigMapper.selectByExample2(RecommendDoctorConfig.class,
                sql -> sql.andIn(RecommendDoctorConfig::getRecommendId, recommendConfigIdList));
    }

    private List<Long> extractDoctorIds(List<RecommendDoctorConfig> recommendDoctorConfigList) {
        return recommendDoctorConfigList.stream()
                .map(RecommendDoctorConfig::getDoctorId)
                .collect(Collectors.toList());
    }

    private List<CustomRecommendDepartInfoVo> mapRecommendConfigToVoList(List<RecommendConfig> recommendConfigList, List<Department> departments, List<Hospital> hospitals, List<RecommendDoctorConfig> recommendDoctorConfigList, List<Doctor> doctors, List<Hospital> hospitalParentList, List<Address> addresses) {
        return recommendConfigList.stream()
                .map(recommendConfig -> createCustomRecommendDepartInfoVo(recommendConfig, departments, hospitals, recommendDoctorConfigList, doctors, hospitalParentList))
                .collect(Collectors.toList());
    }

    private CustomRecommendDepartInfoVo createCustomRecommendDepartInfoVo(RecommendConfig recommendConfig, List<Department> departments, List<Hospital> hospitals, List<RecommendDoctorConfig> recommendDoctorConfigList, List<Doctor> doctors, List<Hospital> hospitalParentList) {
        CustomRecommendDepartInfoVo vo = new CustomRecommendDepartInfoVo();
        if (DEPARTMENT_RECOMMEND_CHANNEL.equals(recommendConfig.getDataType())) {
            Department department = findDepartmentById(Long.parseLong(recommendConfig.getDataId()), departments);
            if (department != null) {
                Hospital hospital = findHospitalByCode(department.getHospitalCode(), hospitals);
                Hospital hospitalParent = findHospitalParentByCode(hospital.getParentId(), hospitalParentList);
                populateHospitalInfo(vo, hospital, hospitalParent);
                populateDepartmentInfo(vo, department);
            }
            vo.setDepartmentName(recommendConfig.getDisplayName());
            populateRecommendConfigInfo(vo, recommendConfig);
            populateRecommendDoctorList(vo, recommendConfig, recommendDoctorConfigList, doctors, hospitalParentList, hospitals);
            return vo;
        }
        if (HOSPITAL_AREA_RECOMMEND_DATA_TYPE.equals(recommendConfig.getDataType())) {
            Hospital hospital = findHospitalById(Long.parseLong(recommendConfig.getDataId()), hospitals);
            Hospital hospitalParent = findHospitalParentByCode(hospital.getParentId(), hospitalParentList);
            vo.setDepartmentName(recommendConfig.getDisplayName());
            populateHospitalInfo(vo, hospital, hospitalParent);
            vo.setHospitalAreaName(recommendConfig.getDisplayName().replaceAll("<p>", "").replaceAll("</p>", ""));
            populateRecommendConfigInfo(vo, recommendConfig);
            return vo;
        }
        return vo;

    }

    private Department findDepartmentById(Long dataId, List<Department> departments) {
        return departments.stream()
                .filter(department -> department.getId().equals(dataId))
                .findFirst()
                .orElse(null);
    }

    private Hospital findHospitalByCode(String hospitalCode, List<Hospital> hospitals) {
        return hospitals.stream()
                .filter(hospital -> StringUtils.hasText(hospital.getHospitalCode()) && hospital.getHospitalCode().equals(hospitalCode))
                .findFirst()
                .orElse(null);
    }

    private Hospital findHospitalById(Long hospitalId, List<Hospital> hospitals) {
        return hospitals.stream()
                .filter(hospital -> hospital.getId().equals(hospitalId))
                .findFirst()
                .orElse(null);
    }

    private Hospital findHospitalParentByCode(Long hospitalId, List<Hospital> hospitalParentList) {
        return hospitalParentList.stream()
                .filter(hospital -> hospital.getId().equals(hospitalId))
                .findFirst()
                .orElse(null);
    }

    private void populateHospitalInfo(CustomRecommendDepartInfoVo vo, Hospital hospital, Hospital hospitalParent) {
        vo.setHospitalAreaName(hospital.getName());
        vo.setHospitalName(hospitalParent.getName());
        vo.setHospitalAreaCode(hospital.getHospitalCode());
        vo.setHospitalAreaId(hospital.getId());
        vo.setHospitalId(hospital.getParentId());
        vo.setAddressId(hospital.getAddressId());
        String hospitalLogoImgUrl = StringUtils.hasText(hospitalParent.getLogo()) ? hospitalParent.getLogo() : "";
        //去掉第一个 /
        if (hospitalLogoImgUrl.startsWith("/")) {
            hospitalLogoImgUrl = hospitalLogoImgUrl.substring(1);
        }
        vo.setHospitalLogoImgUrl(hospitalLogoImgUrl);
    }

    private void populateDepartmentInfo(CustomRecommendDepartInfoVo vo, Department department) {
        vo.setDepartmentCode(department.getThrdpartDepCode());
        vo.setDepartmentName(department.getName());
        vo.setDepartmentId(department.getId());
    }

    private void populateRecommendConfigInfo(CustomRecommendDepartInfoVo vo, RecommendConfig recommendConfig) {
        vo.setRecommendReason(recommendConfig.getRecommendReason());
        vo.setRecommendScore(recommendConfig.getRecommendScore());
        vo.setSpecially(recommendConfig.getSpecially());
        vo.setRecommendId(recommendConfig.getId());
        vo.setDataType(Integer.valueOf(recommendConfig.getDataType()));
        if (StringUtils.hasText(recommendConfig.getKeyWords())) {
            List<CustomRecommendKeyWordVo> keyWordList = new ArrayList<>();


            Arrays.stream(recommendConfig.getKeyWords().split("\\|")).forEach(key -> {
                CustomRecommendKeyWordVo keyWord = new CustomRecommendKeyWordVo();
                keyWord.setKeyWord(key);
                keyWordList.add(keyWord);
            });
            vo.setKeyWordList(keyWordList);
        }
    }

    private void populateRecommendDoctorList(CustomRecommendDepartInfoVo vo, RecommendConfig recommendConfig, List<RecommendDoctorConfig> recommendDoctorConfigList, List<Doctor> doctors, List<Hospital> hospitalParentList, List<Hospital> hospitalList) {
        List<RecommendDoctorConfig> configs = recommendDoctorConfigList.stream()
                .filter(config -> config.getRecommendId().equals(recommendConfig.getId()))
                .collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(configs)) {
            List<Doctor> doctors1 = doctors.stream()
                    .filter(doctor -> configs.stream()
                            .anyMatch(config -> config.getDoctorId().equals(doctor.getId()) && config.getEnabled()))
                    .sorted((d1, d2) -> {
                        RecommendDoctorConfig config1 = configs.stream()
                                .filter(config -> config.getDoctorId().equals(d1.getId()))
                                .findFirst()
                                .orElse(null);
                        RecommendDoctorConfig config2 = configs.stream()
                                .filter(config -> config.getDoctorId().equals(d2.getId()))
                                .findFirst()
                                .orElse(null);
                        return Integer.compare(config2 != null ? config2.getSort() : 0, config1 != null ? config1.getSort() : 0);
                    })
                    .collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(doctors1)) {

                List<CustomRecommendDoctorVo> customRecommendDoctorVoList = doctors1.stream()
                        .map(doctor ->
                                this.createCustomRecommendDoctorVo(doctor, recommendConfig, configs, hospitalParentList, hospitalList))
                        .collect(Collectors.toList());
                if (!ObjectUtils.isEmpty(vo.getKeyWordList())) {
                    vo.getKeyWordList().forEach(keyWord -> {
                        List<CustomRecommendDoctorVo> customRecommendDoctorVoList1 = customRecommendDoctorVoList.stream()
                                .filter(customRecommendDoctorVo -> customRecommendDoctorVo.getDataTag().equals(keyWord.getKeyWord()))
                                .collect(Collectors.toList());
                        if (!ObjectUtils.isEmpty(customRecommendDoctorVoList1)) {
                            customRecommendDoctorVoList1 = customRecommendDoctorVoList1.stream()
                                    .sorted((d1, d2) -> {
                                        RecommendDoctorConfig config1 = configs.stream()
                                                .filter(config -> config.getDoctorId().equals(d1.getDoctorId()))
                                                .findFirst()
                                                .orElse(null);
                                        RecommendDoctorConfig config2 = configs.stream()
                                                .filter(config -> config.getDoctorId().equals(d2.getDoctorId()))
                                                .findFirst()
                                                .orElse(null);
                                        return Integer.compare(config2 != null ? config2.getSort() : 0, config1 != null ? config1.getSort() : 0);
                                    })
                                    .collect(Collectors.toList());
                            keyWord.setRecommendDoctorList(customRecommendDoctorVoList1);
                        }
                    });
                }
            }
        }
    }

    private CustomRecommendDoctorVo createCustomRecommendDoctorVo(Doctor doctor, RecommendConfig config, List<RecommendDoctorConfig> configs, List<Hospital> hospitalParentList, List<Hospital> hospitalList) {
        CustomRecommendDoctorVo customRecommendDoctorVo = new CustomRecommendDoctorVo();
        customRecommendDoctorVo.setDoctorId(doctor.getId());
        customRecommendDoctorVo.setDoctorName(doctor.getName());
        customRecommendDoctorVo.setDoctorCode(doctor.getThrdpartDoctorCode());
        customRecommendDoctorVo.setDoctorImageUrl(doctor.getHeadImgUrl());
        customRecommendDoctorVo.setDataTag(config.getDataTag());
        RecommendDoctorConfig recommendDoctorConfig = configs.stream()
                .filter(config1 -> config1.getDoctorId().equals(doctor.getId()))
                .findFirst()
                .orElse(null);
        customRecommendDoctorVo.setRecommendId(recommendDoctorConfig.getRecommendId());
        customRecommendDoctorVo.setDataTag(recommendDoctorConfig.getDataTag());
        customRecommendDoctorVo.setId(recommendDoctorConfig.getId());
        customRecommendDoctorVo.setHospitalAreaCode(doctor.getHospitalCode());
        customRecommendDoctorVo.setDataName(doctor.getName());
        customRecommendDoctorVo.setDepartmentCode(doctor.getDepartmentCode());
        customRecommendDoctorVo.setHospitalAreaId(doctor.getHospitalAreaId());
        customRecommendDoctorVo.setDepartmentId(doctor.getDepartmentId());
        customRecommendDoctorVo.setHospitalId(doctor.getHospitalId());
        customRecommendDoctorVo.setDoctorTitle(doctor.getHonor());
        customRecommendDoctorVo.setIntroduction(doctor.getIntroduction());
        Hospital hospitalParent = hospitalParentList.stream()
                .filter(hospital -> hospital.getId().equals(doctor.getHospitalId()))
                .findFirst()
                .orElse(null);
        Hospital hospital = hospitalList.stream()
                .filter(hospitalItem -> hospitalItem.getId().equals(doctor.getHospitalId()))
                .findFirst()
                .orElse(null);
        customRecommendDoctorVo.setHospitalName(hospitalParent != null ? hospitalParent.getName() : "");
        customRecommendDoctorVo.setHospitalAreaName(hospital != null ? hospital.getName() : "");
        return customRecommendDoctorVo;
    }

    @Override
    public RecentlyRegistrationDepartmentVo getRecentRegistrationDepartment(Long userId) {
        RecentlyRegistrationDepartmentVo vo = new RecentlyRegistrationDepartmentVo();
        ResponseEntity<ApiOrderAppointment> latestOrder = appointmentOrderClient.getOrderAppointmentByUserId(userId);
        List<RecommendConfig> recommendConfigs = recommendConfigMapper.selectByExample2(RecommendConfig.class,
                sql -> sql.andEqualTo(RecommendConfig::getEnabled, 1)
                        .andEqualTo(RecommendConfig::getBizType, "quick_registration_department"));

        if (latestOrder.getBody() == null) {
            vo.setValue(null);
            return vo;
        }
        if (!CollectionUtils.isEmpty(recommendConfigs)) {
            for (RecommendConfig recommendConfig : recommendConfigs) {
                if (recommendConfig.getKeyWords() != null) {
                    String[] keyWords = recommendConfig.getKeyWords().split("\\|");
                    if (Arrays.stream(keyWords).anyMatch(keyWord -> latestOrder.getBody().getDepartmentName().contains(keyWord))) {
                        vo.setValue(recommendConfig.getDataTag());
                        return vo;
                    }
                }

            }
        }
        vo.setValue("");
        return vo;
    }

    @Override
    public CustomerDoctorScheduleInfoVo queryGroupedDoctorListByDepartmentId(Long departmentId, String hospitalAreaCode, String departmentCode, Integer timeType, Long recommendId, String dataTag) {
        CustomerDoctorScheduleInfoVo vo = new CustomerDoctorScheduleInfoVo();
        if (departmentId != null) {
            vo = customerDepartmentService.queryGroupedDoctorList(departmentId, timeType);
        } else if (StringUtils.hasText(hospitalAreaCode) && StringUtils.hasText(departmentCode)) {
            vo = customerDepartmentService.queryGroupedDoctorListBy(hospitalAreaCode, departmentCode);
        }
        CustomerDoctorScheduleInfoVo finalVo = vo;
        List<RecommendDoctorConfig> recommendDoctorConfigs = new ArrayList<>();
        if (StringUtils.hasText(dataTag)) {
            recommendDoctorConfigs = recommendDoctorConfigMapper.selectByExample2(RecommendDoctorConfig.class,
                    sql -> sql.andEqualTo(RecommendDoctorConfig::getEnabled, 1)
                            .andEqualTo(RecommendDoctorConfig::getRecommendId, recommendId).andEqualTo(RecommendDoctorConfig::getDataTag, dataTag)
            );
        } else {
            recommendDoctorConfigs = recommendDoctorConfigMapper.selectByExample2(RecommendDoctorConfig.class,
                    sql -> sql.andEqualTo(RecommendDoctorConfig::getEnabled, 1)
                            .andEqualTo(RecommendDoctorConfig::getRecommendId, recommendId)
            );
        }
        if (!CollectionUtils.isEmpty(recommendDoctorConfigs)) {
            List<Long> doctorIds = recommendDoctorConfigs.stream().map(RecommendDoctorConfig::getDoctorId).collect(Collectors.toList());
            List<Doctor> doctors = doctorQuery.queryBy(doctorIds);

            if (!CollectionUtils.isEmpty(finalVo.getAllDoctors())) {
                finalVo.setAllDoctors(finalVo.getAllDoctors().stream()
                        .filter(doctor -> doctorIds.contains(doctor.getId()))
                        .collect(Collectors.toList()));
                //如果推荐的医生没有排班，则需要补充推荐医生的信息
                if (CollectionUtils.isEmpty(finalVo.getAllDoctors())) {
                    //检查两个集合医生是否一致
                    List<Long> existsDoctorIds = finalVo.getAllDoctors().stream().map(CustomerAllScheduleDoctorDetailVo::getId).collect(Collectors.toList());
                    List<Long> notExistsDoctorIds = doctorIds.stream().filter(doctorId -> !existsDoctorIds.contains(doctorId)).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(notExistsDoctorIds)) {
                        fillDoctorInfo(notExistsDoctorIds, doctors, finalVo, recommendDoctorConfigs);
                    }
                } else {
                    fillDoctorInfo(doctorIds, doctors, finalVo, recommendDoctorConfigs);
                }

            } else {
                fillDoctorInfo(doctorIds, doctors, finalVo, recommendDoctorConfigs);
            }

            List<RecommendDoctorConfig> finalRecommendDoctorConfigs = recommendDoctorConfigs;
            finalVo.getAllDoctors().stream().sorted((d1, d2) -> {
                RecommendDoctorConfig config1 = finalRecommendDoctorConfigs.stream()
                        .filter(config -> config.getDoctorId().equals(d1.getId()))
                        .findFirst()
                        .orElse(null);
                RecommendDoctorConfig config2 = finalRecommendDoctorConfigs.stream()
                        .filter(config -> config.getDoctorId().equals(d2.getId()))
                        .findFirst()
                        .orElse(null);

                int sortCompare = Integer.compare(
                        (config2 != null ? config2.getSort() : 0),
                        (config1 != null ? config1.getSort() : 0)
                );

                return sortCompare != 0 ? sortCompare :
                        Long.compare(d2.getId(), d1.getId());
            });
            if (!CollectionUtils.isEmpty(finalVo.getGroupedDoctors()) && !CollectionUtils.isEmpty(doctorIds)) {
                finalVo.setGroupedDoctors(finalVo.getGroupedDoctors().stream().filter(groupedDoctor -> !CollectionUtils.isEmpty(groupedDoctor.getDoctors()) && groupedDoctor.getDoctors().stream().anyMatch(doctor -> doctorIds.contains(doctor.getId()))).collect(Collectors.toList()));
            }
        } else {
            finalVo.setAllDoctors(new ArrayList<>());
            finalVo.setGroupedDoctors(new ArrayList<>());
        }
        return finalVo;
    }

    private Example buildExampleFromQueryDto(CustomRecommendDoctorQueryDto queryDto) {
        Example example = new Example(RecommendConfig.class);
        Example.Criteria criteria = example.createCriteria();

        if (queryDto != null) {

            criteria.andEqualTo("enabled", queryDto.isEnabled() == null ? 1 : queryDto.isEnabled());
            if (queryDto.getDataTag() != null) {
                criteria.andEqualTo("dataTag", queryDto.getDataTag());
            }
            criteria.andEqualTo("bizType", "quick_registration_department");
        }

        // 添加排序规则（如果需要）
        example.orderBy("sort").desc();

        return example;
    }

    private RecommendRespVo handleRecommendResp(List<RecommendConfig> recommendConfigs, Double longitude, Double latitude) {
        RecommendRespVo result = new RecommendRespVo();

        List<RecommendConfig> recommendHospitals = recommendConfigs.stream()
                .filter(re -> RecommendConfigConstant.DataType.HOSPITAL.getType().equals(re.getDataType()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(recommendHospitals)) {
            Set<Long> hospitalIds = recommendHospitals.stream()
                    .map(RecommendConfig::getDataId)
                    .map(Long::parseLong)
                    .collect(Collectors.toSet());
            List<Hospital> hospitals = hospitalMapper.selectByExample2(Hospital.class,
                    sql -> sql.andIn(Hospital::getId, hospitalIds)
                            .andEqualTo(Hospital::getHospitalTypeTag, 0)
                            .andNotEqualTo(Hospital::getStatus, 3));
            if (!CollectionUtils.isEmpty(hospitals)) {
                result.setHospitalList(handleRecommendHospital(hospitals, recommendHospitals));
            }
        }

        List<RecommendConfig> recommendHospitalAreas = recommendConfigs.stream()
                .filter(re -> RecommendConfigConstant.DataType.HOSPITAL_AREA.getType().equals(re.getDataType()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(recommendHospitalAreas)) {
            Set<Long> hospitalAreaIds = recommendHospitalAreas.stream()
                    .map(RecommendConfig::getDataId)
                    .map(Long::parseLong)
                    .collect(Collectors.toSet());
            List<Hospital> hospitals = hospitalMapper.selectByExample2(Hospital.class,
                    sql -> sql.andIn(Hospital::getId, hospitalAreaIds)
                            .andEqualTo(Hospital::getHospitalTypeTag, 1)
                            .andNotEqualTo(Hospital::getStatus, 3));
            result.setHospitalAreaList(handleRecommendHospitalArea(hospitals, recommendHospitalAreas, longitude, latitude));
        }

        List<RecommendConfig> recommendDepartments = recommendConfigs.stream()
                .filter(re -> RecommendConfigConstant.DataType.DEPARTMENT.getType().equals(re.getDataType()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(recommendDepartments)) {
            List<Long> departmentIds = recommendDepartments.stream()
                    .map(RecommendConfig::getDataId)
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            List<Department> departments = departmentMapper.selectByExample2(Department.class,
                    sql -> sql.andIn(Department::getId, departmentIds)
                            .andEqualTo(Department::getEnabled, 1));
            if (!CollectionUtils.isEmpty(departments)) {
                result.setDepartmentList(handleRecommendDepartment(departments, recommendDepartments));
            }
        }

        List<RecommendConfig> recommendDoctors = recommendConfigs.stream()
                .filter(re -> RecommendConfigConstant.DataType.DOCTOR.getType().equals(re.getDataType()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(recommendDoctors)) {
            List<Long> doctorIds = recommendDoctors.stream()
                    .map(RecommendConfig::getDataId)
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            List<Doctor> doctors = doctorQuery.queryBy(doctorIds);
            if (!CollectionUtils.isEmpty(doctors)) {
                result.setDoctorList(handleRecommendDoctor(doctors, recommendDoctors));
            }
        }

        List<RecommendConfig> recommendOnlineDoctors = recommendConfigs.stream()
                .filter(re -> RecommendConfigConstant.DataType.ONLINE_DOCTOR.getType().equals(re.getDataType()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(recommendOnlineDoctors)) {
            result.setOnlineDoctorList(handleRecommendOnlineDoctor(recommendOnlineDoctors));
        }

        return result;
    }

    private List<RecommendHospitalListItemVo> handleRecommendHospital(List<Hospital> hospitals, List<RecommendConfig> recommendHospitals) {
        Map<String, List<RecommendConfig>> recommendConfigMap = recommendHospitals.stream()
                .collect(Collectors.groupingBy(RecommendConfig::getDataId));
        List<Long> dataIdSorted = recommendHospitals.stream()
                .sorted(Comparator.comparing(RecommendConfig::getSort))
                .map(RecommendConfig::getDataId)
                .map(Long::parseLong)
                .collect(Collectors.toList());
        List<Long> hospitalIds = hospitals.stream().map(Hospital::getId).collect(Collectors.toList());
        List<Hospital> hospitalAreas = hospitalMapper.selectByExample2(Hospital.class,
                sql -> sql.andIn(Hospital::getParentId, hospitalIds)
                        .andEqualTo(Hospital::getHospitalTypeTag, 1)
                        .andNotEqualTo(Hospital::getStatus, 3));
        Map<Long, List<Hospital>> areaMap = hospitalAreas.stream().collect(Collectors.groupingBy(Hospital::getParentId));
        Map<Long, Address> addressMap = getHospitalAddressMap(hospitals);

        return hospitals.stream()
                .flatMap(hos -> {
                    List<RecommendHospitalListItemVo> vos = new ArrayList<>();
                    List<RecommendConfig> recommendConfigs = recommendConfigMap.get(String.valueOf(hos.getId()));
                    if (!CollectionUtils.isEmpty(recommendConfigs)) {
                        for (RecommendConfig recommendConfig : recommendConfigs) {
                            if (recommendConfig == null) {
                                continue;
                            }
                            RecommendHospitalListItemVo vo = new RecommendHospitalListItemVo();
                            vo.setHospitalId(hos.getId());
                            vo.setHospitalName(hos.getName());
                            vo.setLogo(hos.getLogo());
                            vo.setStatus(hos.getStatus());
                            vo.setLevel(hos.getLevelDictLabel());
                            List<Hospital> areas = areaMap.get(hos.getId());
                            if (!CollectionUtils.isEmpty(areas)) {
                                List<RecommendHospitalAreaItem> areaItemList = areas.stream().flatMap(area -> {
                                    RecommendHospitalAreaItem item = new RecommendHospitalAreaItem();
                                    item.setId(area.getId());
                                    item.setCode(area.getHospitalCode());
                                    item.setCategories(area.getCategories());
                                    item.setStatus(area.getStatus());
                                    return Stream.of(item);
                                }).collect(Collectors.toList());
                                vo.setHospitalAreaList(areaItemList);
                            }

                            Address address = addressMap.get(hos.getAddressId());
                            if (address != null) {
                                vo.setProvince(address.getProvince());
                                vo.setCity(address.getCity());
                                vo.setCounty(address.getCounty());
                            }

                            handleCommonRecommendProperties(recommendConfig, vo);
                            vos.add(vo);
                        }
                    }
                    return vos.stream();
                })
                .sorted(Comparator.comparing(vo -> dataIdSorted.indexOf(vo.getHospitalId())))
                .collect(Collectors.toList());
    }

    private List<RecommendHospitalAreaListItemVo> handleRecommendHospitalArea(List<Hospital> hospitalAreas,
                                                                              List<RecommendConfig> recommendHospitalAreas,
                                                                              Double longitude,
                                                                              Double latitude) {
        Map<String, List<RecommendConfig>> recommendConfigMap = recommendHospitalAreas.stream()
                .collect(Collectors.groupingBy(RecommendConfig::getDataId));

        List<Long> dataIdSorted = recommendHospitalAreas.stream()
                .sorted(Comparator.comparing(RecommendConfig::getSort))
                .map(RecommendConfig::getDataId)
                .map(Long::parseLong)
                .collect(Collectors.toList());
        Map<Long, Address> addressMap = getHospitalAddressMap(hospitalAreas);
        List<Hospital> parents = getParentHospitals(hospitalAreas);
        Map<Long, Hospital> parentMap = parents.stream().collect(Collectors.toMap(Hospital::getId, a -> a));

        return hospitalAreas.stream()
                .flatMap(hos -> {
                    List<RecommendHospitalAreaListItemVo> vos = new ArrayList<>();
                    List<RecommendConfig> recommendConfigs = recommendConfigMap.get(String.valueOf(hos.getId()));
                    if (!CollectionUtils.isEmpty(recommendConfigs)) {
                        for (RecommendConfig recommendConfig : recommendConfigs) {
                            if (recommendConfig == null) {
                                continue;
                            }
                            RecommendHospitalAreaListItemVo vo = new RecommendHospitalAreaListItemVo();
                            vo.setHospitalId(hos.getParentId());
                            vo.setHospitalCode(hos.getHospitalCode());
                            vo.setHospitalAreaId(hos.getId());
                            vo.setHospitalAreaName(hos.getName());
                            vo.setStatus(hos.getStatus());
                            vo.setLevel(hos.getLevelDictLabel());
                            Address add = addressMap.get(hos.getAddressId());
                            if (add != null) {
                                vo.setDistance(CommonUtil.getDistance(longitude, latitude, add.getLongitude(), add.getLatitude()));
                                vo.setAddress(add.getDetail());
                            }
                            Hospital parentHos = parentMap.get(hos.getParentId());
                            if (parentHos != null) {
                                vo.setLogo(parentHos.getLogo());
                            }
                            handleCommonRecommendProperties(recommendConfig, vo);
                            vos.add(vo);
                        }
                    }
                    return vos.stream();
                })
                .sorted(Comparator.comparing(vo -> dataIdSorted.indexOf(vo.getHospitalAreaId())))
                .collect(Collectors.toList());
    }

    private Map<Long, Address> getHospitalAddressMap(List<Hospital> hospitals) {
        List<Long> addIds = hospitals.stream().map(Hospital::getAddressId).collect(Collectors.toList());
        List<Address> addresses = new ArrayList<>();
        if (!CollectionUtils.isEmpty(addIds)) {
            addresses = addressMapper.selectByIds(StrUtil.join(",", addIds));
        }
        return addresses.stream().collect(Collectors.toMap(Address::getId, a -> a));
    }

    private List<RecommendDepartmentListItemVo> handleRecommendDepartment(List<Department> departments, List<RecommendConfig> recommendDepartments) {
        List<Long> hospitalAreaIds = departments.stream().map(Department::getHospitalAreaId).collect(Collectors.toList());
        List<Long> dataIdSorted = recommendDepartments.stream()
                .sorted(Comparator.comparing(RecommendConfig::getSort))
                .map(RecommendConfig::getDataId)
                .map(Long::parseLong)
                .collect(Collectors.toList());
        List<Hospital> hospitalAreas = new ArrayList<>();
        if (!CollectionUtils.isEmpty(hospitalAreaIds)) {
            hospitalAreas = hospitalMapper.selectByIds(StrUtil.join(",", hospitalAreaIds));
        }
        Map<Long, Hospital> hospitalAreaMap = hospitalAreas.stream().collect(Collectors.toMap(Hospital::getId, a -> a));
        List<Hospital> parents = getParentHospitals(hospitalAreas);
        Map<Long, Hospital> parentMap = parents.stream().collect(Collectors.toMap(Hospital::getId, a -> a));
        Map<Long, Address> addressMap = getHospitalAddressMap(hospitalAreas);
        Map<String, List<RecommendConfig>> recommendConfigMap = recommendDepartments.stream()
                .collect(Collectors.groupingBy(RecommendConfig::getDataId));
        return departments.stream()
                .flatMap(dep -> {
                    List<RecommendDepartmentListItemVo> vos = new ArrayList<>();
                    List<RecommendConfig> recommendConfigs = recommendConfigMap.get(String.valueOf(dep.getId()));
                    if (!CollectionUtils.isEmpty(recommendConfigs)) {
                        for (RecommendConfig recommendConfig : recommendConfigs) {
                            if (recommendConfig != null) {
                                RecommendDepartmentListItemVo vo = new RecommendDepartmentListItemVo();
                                vo.setDepartmentId(dep.getId());
                                vo.setDepartmentName(dep.getName());
                                Hospital area = hospitalAreaMap.get(dep.getHospitalAreaId());
                                if (area != null) {
                                    vo.setHospitalAreaId(area.getId());
                                    vo.setHospitalCode(area.getHospitalCode());
                                    vo.setHospitalAreaName(area.getName());
                                    vo.setHospitalId(area.getParentId());
                                    Address add = addressMap.get(area.getAddressId());
                                    if (add != null) {
                                        vo.setAddressIntro(add.getDetail());
                                    }
                                    Hospital parentHos = parentMap.get(area.getParentId());
                                    if (parentHos != null) {
                                        vo.setHospitalId(parentHos.getId());
                                        vo.setHospitalAreaLogo(parentHos.getLogo());
                                        vo.setHospitalName(parentHos.getName());
                                    }
                                }
                                handleCommonRecommendProperties(recommendConfig, vo);
                                vos.add(vo);
                            }
                        }
                    }
                    return vos.stream();
                })
                .sorted(Comparator.comparing(vo -> dataIdSorted.indexOf(vo.getDepartmentId())))
                .collect(Collectors.toList());
    }

    private List<Hospital> getParentHospitals(List<Hospital> hospitalAreas) {
        List<Hospital> parents = new ArrayList<>();
        if (!CollectionUtils.isEmpty(hospitalAreas)) {
            List<Long> parentIds = hospitalAreas.stream().map(Hospital::getParentId).collect(Collectors.toList());
            parents = hospitalMapper.selectByIds(StrUtil.join(",", parentIds));
        }
        return parents;
    }

    private List<RecommendDoctorListItemVo> handleRecommendDoctor(List<Doctor> doctors, List<RecommendConfig> recommendDoctors) {
        Map<String, List<RecommendConfig>> recommendConfigMap = recommendDoctors.stream()
                .collect(Collectors.groupingBy(RecommendConfig::getDataId));
        List<Long> dataIdSorted = recommendDoctors.stream()
                .sorted(Comparator.comparing(RecommendConfig::getSort))
                .map(RecommendConfig::getDataId)
                .map(Long::parseLong)
                .collect(Collectors.toList());
        List<Hospital> hospitalAreas = new ArrayList<>();
        List<Long> hosAreaIds = doctors.stream().map(Doctor::getHospitalAreaId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(hosAreaIds)) {
            hospitalAreas = hospitalMapper.selectByIds(StrUtil.join(",", hosAreaIds));
        }
        Map<Long, Hospital> hospitalAreaMap = hospitalAreas.stream().collect(Collectors.toMap(Hospital::getId, a -> a));
        List<Department> departments = new ArrayList<>();
        List<Long> departmentIds = doctors.stream().map(Doctor::getDepartmentId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(departmentIds)) {
            departments = departmentMapper.selectByIds(StrUtil.join(",", departmentIds));
        }
        Map<Long, Department> departmentMap = departments.stream().collect(Collectors.toMap(Department::getId, a -> a));

        return doctors.stream()
                .flatMap(doctor -> {
                    List<RecommendDoctorListItemVo> vos = new ArrayList<>();
                    List<RecommendConfig> recommendConfigs = recommendConfigMap.get(String.valueOf(doctor.getId()));
                    if (!CollectionUtils.isEmpty(recommendConfigs)) {
                        for (RecommendConfig recommendConfig : recommendConfigs) {
                            if (recommendConfig != null) {
                                RecommendDoctorListItemVo vo = new RecommendDoctorListItemVo();
                                vo.setDoctorId(doctor.getId());
                                vo.setDoctorName(doctor.getName());
                                vo.setDoctorCode(doctor.getThrdpartDoctorCode());
                                vo.setHospitalAreaId(doctor.getHospitalAreaId());
                                vo.setHospitalCode(doctor.getHospitalCode());
                                vo.setDepartmentId(doctor.getDepartmentId());
                                vo.setLevel(doctor.getRankDictLabel());
                                vo.setLogo(doctor.getHeadImgUrl());
                                vo.setSpeciality(doctor.getSpeciality());
                                vo.setIntroduction(doctor.getIntroduction());
                                handleCommonRecommendProperties(recommendConfig, vo);
                                Hospital area = hospitalAreaMap.get(doctor.getHospitalAreaId());
                                if (area != null) {
                                    vo.setHospitalAreaName(area.getName());
                                    vo.setHospitalCode(area.getHospitalCode());
                                    vo.setHospitalAreaId(area.getId());
                                    vo.setHospitalId(area.getParentId());
                                }
                                Department department = departmentMap.get(doctor.getDepartmentId());
                                if (department != null) {
                                    vo.setDepartmentName(department.getName());
                                    vo.setDepartmentCode(department.getThrdpartDepCode());
                                }
                                vos.add(vo);

                                importDoctorCanOrderDays(AppointmentSystemDepends.of(doctor.getSystemDepends()), vo);
                            }
                        }
                    }
                    return vos.stream();
                })
                .sorted(Comparator.comparing(vo -> dataIdSorted.indexOf(vo.getDoctorId())))
                .collect(Collectors.toList());
    }


    private void importDoctorCanOrderDays(AppointmentSystemDepends systemDepends, RecommendDoctorListItemVo vo) {
        vo.setCanOrder(false);
        vo.setShowSchDate(false);

        final List<CustomerDoctorScheduleDateVo> orderDates = new ArrayList<>();
        log.info("推荐医生查询排班，systemDepends:{}", systemDepends);
        if (AppointmentSystemDepends.HIS.equals(systemDepends)) {
            DepartmentDoctorItem doctorItem = cacheComponent.getDoctorItem(vo.getHospitalCode(), vo.getDepartmentCode(), vo.getDoctorCode());
            log.info("推荐医生查询排班，doctorItem:{}", JsonUtil.serializeObject(doctorItem));

            if (null != doctorItem && null != doctorItem.getSrcDateMap()) {
                String now = DateTime.now().toString("MM-dd");
                String nextDay = DateTime.now().offset(DateField.DAY_OF_YEAR, 1).toString("MM-dd");
                doctorItem.getSrcDateMap().forEach((dateStr, number) -> {
                    CustomerDoctorScheduleDateVo scheduleDateVo = new CustomerDoctorScheduleDateVo();
                    if (dateStr.equals(now)) {
                        scheduleDateVo.setDateInWeek("今天");
                    } else if (dateStr.equals(nextDay)) {
                        scheduleDateVo.setDateInWeek("明天");
                    } else {
                        scheduleDateVo.setDateInWeek(dateStr);
                    }
                    scheduleDateVo.setSchDate(dateStr);
                    scheduleDateVo.setCanOrder(null != number && number > 0);
                    scheduleDateVo.setShowSchDate(true);
                    orderDates.add(scheduleDateVo);
                });
            }
        } else {
            Doctor doctor = doctorQuery.queryDoctorById(vo.getDoctorId());
            if (doctor == null) {
                return;
            }
            Map<Long, Pair<Date, Date>> scheduleDate = scheduleUtil.getScheduleDate(Collections.singletonList(vo.getHospitalAreaId()),
                    Collections.singletonList(vo.getDepartmentId()),
                    Collections.singletonList(vo.getDoctorId()));
            Pair<Date, Date> dateTimePair = scheduleDate.get(vo.getDoctorId());
            if (null == dateTimePair) {
                return;
            }
            String startDateStr = DateUtil.format(dateTimePair.getLeft(), "yyyy/MM/dd");
            String endDateStr = DateUtil.format(dateTimePair.getRight(), "yyyy/MM/dd");
            log.info("推荐医生查询排班,医生id:{},开始时间:{},结束时间:{}", vo.getDoctorId(), startDateStr, endDateStr);
            ApiSchedulePageResponse schedules = scheduleClient.getSchedulesBatch(startDateStr, endDateStr, Collections.singletonList(vo.getDoctorId()), 1, 200);
            log.info("推荐医生查询排班，schedules:{}", JsonUtil.serializeObject(schedules));
            if (null != schedules && !CollectionUtils.isEmpty(schedules.getList())) {
                String now = DateTime.now().toString("MM-dd");
                String nextDay = DateTime.now().offset(DateField.DAY_OF_YEAR, 1).toString("MM-dd");
                List<CustomerDoctorScheduleDateVo> scheduleDateVos = schedules.getList().stream()
                        .collect(Collectors.toMap(ApiScheduleResponseItem::getSchDate, schedule -> schedule, (o1, o2) -> o1))
                        .values()
                        .stream()
                        .map(schedule -> {
                            CustomerDoctorScheduleDateVo scheduleDateVo = new CustomerDoctorScheduleDateVo();
                            scheduleDateVo.setShowSchDate(null != schedule.getShowSchDate() && schedule.getShowSchDate() == 1);
                            scheduleDateVo.setSchDate(DateTime.of(schedule.getSchDate()).toString("MM-dd"));
                            scheduleDateVo.setCanOrder(schedule.getStatus() == 1
                                    && null != schedule.getMaxTreatNum()
                                    && null != schedule.getUsedNum()
                                    && schedule.getMaxTreatNum() > schedule.getUsedNum());

                            if (scheduleDateVo.getSchDate().equals(now)) {
                                scheduleDateVo.setDateInWeek("今天");
                            } else if (scheduleDateVo.getSchDate().equals(nextDay)) {
                                scheduleDateVo.setDateInWeek("明天");
                            } else {
                                scheduleDateVo.setDateInWeek(scheduleDateVo.getSchDate());
                            }
                            return scheduleDateVo;
                        })
                        .collect(Collectors.toList());
                orderDates.addAll(scheduleDateVos);
            }
        }

        if (CollectionUtils.isEmpty(orderDates)) {
            return;
        }
        orderDates.sort(Comparator.comparing(dateVo -> {
            String schDate = dateVo.getSchDate();
            // 如果为空，放在最后
            if (null == schDate) {
                return Long.MAX_VALUE;
            }
            int year = DateTime.now().year();
            schDate = year + "-" + schDate;
            DateTime parse = DateUtil.parse(schDate, "yyyy-MM-dd");
            return parse.getTime();
        }));

        vo.setCanOrderDays(orderDates);

        if (orderDates.size() == 1) {
            CustomerDoctorScheduleDateVo scheduleDateVo = orderDates.get(0);
            vo.setShowSchDate(scheduleDateVo.isShowSchDate());
            vo.setCanOrder(scheduleDateVo.isCanOrder() && scheduleDateVo.isShowSchDate());
            return;
        }

        vo.setCanOrder(orderDates.stream().anyMatch(CustomerDoctorScheduleDateVo::isCanOrder));
        vo.setShowSchDate(vo.isCanOrder());
        log.info("推荐医生查询排班，vo:{}", JsonUtil.serializeObject(vo));
    }

    private List<RecommendOnlineDoctorListItemVo> handleRecommendOnlineDoctor(List<RecommendConfig> recommendOnlineDoctors) {
        List<Long> doctorGroupIds = recommendOnlineDoctors.stream().map(RecommendConfig::getDataId).map(Long::parseLong).collect(Collectors.toList());
        Map<String, List<RecommendConfig>> recommendConfigMap = recommendOnlineDoctors.stream()
                .collect(Collectors.groupingBy(RecommendConfig::getDataId));
        GetDytkfDoctorGroupListReqDto request = new GetDytkfDoctorGroupListReqDto();
        request.setIds(doctorGroupIds);
        QueryDoctorGroupRespDto doctorGroupList = dytkfDoctorGroupClient.getDoctorGroupList(request);
        if (CollectionUtils.isEmpty(doctorGroupList.getRecords())) {
            return new ArrayList<>();
        }
        return doctorGroupList.getRecords().stream()
                .flatMap(doctorGroup -> {
                    List<RecommendOnlineDoctorListItemVo> vos = new ArrayList<>();
                    List<RecommendConfig> recommendConfigs = recommendConfigMap.get(String.valueOf(doctorGroup.getId()));
                    if (!CollectionUtils.isEmpty(recommendConfigs)) {
                        for (RecommendConfig recommendConfig : recommendConfigs) {
                            RecommendOnlineDoctorListItemVo vo = new RecommendOnlineDoctorListItemVo();
                            vo.setDoctorId(doctorGroup.getId());
                            vo.setDoctorName(doctorGroup.getName());
                            vo.setHospitalCode(doctorGroup.getHospitalCode());
                            vo.setHospitalName(doctorGroup.getHospitalName());
                            vo.setDepartmentName(doctorGroup.getDepartName());
                            vo.setDepartmentCode(doctorGroup.getDepartCode());
                            vo.setDoctorCode(doctorGroup.getDoctorCode());
                            vo.setLogo(doctorGroup.getGroupImg());
                            vo.setSpeciality(doctorGroup.getGoodAt());
                            vo.setLevel(doctorGroup.getTitle());
                            vo.setWorkingYears(doctorGroup.getWorkYear());
                            int answerCount = doctorGroup.getQuestionBase() + doctorGroup.getQuestionCount();
                            vo.setAnswerCount(answerCount);
                            vo.setIsHot(answerCount >= 100 || doctorGroup.getQCount3() >= 150);
                            vo.setIsRecommend(doctorGroup.getSumScore() >= 4.5 && doctorGroup.getTLock() <= 10);
                            vo.setReplySpeed(getSpeed(doctorGroup.getTLock()));
                            handleCommonRecommendProperties(recommendConfig, vo);
                            vos.add(vo);
                        }
                    }
                    return vos.stream();
                })
                .collect(Collectors.toList());
    }

    private String getSpeed(Double tLock) {
        // <=3:快, 3-10:较快, >10:中等
        if (tLock <= 3) {
            return "快";
        } else if (tLock > 3 && tLock <= 10) {
            return "较快";
        } else {
            return "中等";
        }
    }
}
