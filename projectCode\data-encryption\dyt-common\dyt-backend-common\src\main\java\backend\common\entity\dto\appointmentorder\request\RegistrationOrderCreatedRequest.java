package backend.common.entity.dto.appointmentorder.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class RegistrationOrderCreatedRequest {
    @JsonProperty("hospital_id")
    private Long hospitalId;


    @JsonProperty("hospitalAreaId")
    private Long hospitalAreaId;

    @JsonProperty("order_id")
    private Long orderId;

    @JsonProperty("appointment_id")
    private Long appointmentId;
}
