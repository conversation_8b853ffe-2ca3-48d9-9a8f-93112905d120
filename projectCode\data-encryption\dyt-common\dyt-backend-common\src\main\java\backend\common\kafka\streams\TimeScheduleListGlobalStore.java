package backend.common.kafka.streams;

import backend.common.constants.KafkaTopicConfig;
import backend.common.entity.dto.schedule.TimeScheduleList;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.serialization.Serde;
import org.apache.kafka.common.serialization.Serdes;
import org.apache.kafka.streams.KeyValue;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.support.serializer.JsonSerde;
import org.springframework.lang.Nullable;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

@Slf4j
@Configuration
@ConditionalOnProperty(name = "backend.kafka.global-store.info.time-schedule-list", havingValue = "true")
public class TimeScheduleListGlobalStore extends GlobalStoreKafkaStreamsCallback<String, TimeScheduleList> {

    public static final JsonSerde<TimeScheduleList> VALUE_SERDE = new JsonSerde<>(TimeScheduleList.class).noTypeInfo();
    private final ConcurrentMap<String, TimeScheduleList> key2value = new ConcurrentHashMap<>();

    @Override
    public void processUpdate(String key, TimeScheduleList update, TimeScheduleList old) {
        log.info("inner分时段 global-store 有数据需要更新 key:{}, update:{}, old:{}", key, update, old);
        if (update == null && old == null) {
            return;
        }
        if (update == null) {
            key2value.remove(key);
        } else {
            key2value.put(key, update);
        }
    }

    @Override
    public String storeName() {
        return "time-schedule-list";
    }

    /*@Override
    public String sourceTopic() {
        return KafkaTopicConfig.DOCTOR_SCHEDULE_LIST;
    }*/
    @Override
    public String sourceTopic() {
        return KafkaTopicConfig.TIME_SCHEDULE_LIST;
    }

    @Override
    public Serde<TimeScheduleList> valueSerde() {
        return VALUE_SERDE;
    }

    @Override
    public Serde<String> keySerde() {
        return Serdes.String();
    }

    @Override
    protected void doInitialize(KeyValue<String, TimeScheduleList> next) {
        key2value.put(next.key, next.value);
    }

    /**
     * 获取医生排班列表
     *
     * @param key 组成 hospital_code_hospital_id_doctor_id
     * @return
     */
    public @Nullable
    TimeScheduleList getTimeScheduleList(String key) {
        log.info("inner分时段 global-store 通过key:{},获取分时段数据:{}", key, key2value.get(key));
        return key2value.get(key);
    }

}
