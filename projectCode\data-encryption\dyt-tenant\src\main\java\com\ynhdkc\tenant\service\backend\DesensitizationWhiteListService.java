package com.ynhdkc.tenant.service.backend;

import com.ynhdkc.tenant.model.DesensitizationWhiteListVo;
import com.ynhdkc.tenant.model.WhiteListCreateReqDto;
import com.ynhdkc.tenant.model.WhiteListPageVo;
import com.ynhdkc.tenant.model.WhiteListUpdateReqDto;

/**
 * <AUTHOR>
 */
public interface DesensitizationWhiteListService {

    /**
     * 创建数据脱敏白名单记录.
     *
     * @param dto 白名单创建请求数据传输对象
     * @return 创建后的白名单实体
     */
    DesensitizationWhiteListVo create(WhiteListCreateReqDto dto);

    /**
     * 更新数据脱敏白名单记录.
     *
     * @param id  要更新的白名单记录ID
     * @param dto 白名单更新请求数据传输对象
     * @return 更新后的白名单实体
     */
    DesensitizationWhiteListVo update(Long id, WhiteListUpdateReqDto dto);

    /**
     * 删除数据脱敏白名单记录.
     *
     * @param id 要删除的白名单记录ID
     */
    void delete(Long id);

    /**
     * 获取数据脱敏白名单记录.
     *
     * @param id 要获取的白名单记录ID
     * @return 白名单实体
     */
    DesensitizationWhiteListVo get(Long id);

    /**
     * 分页查询数据脱敏白名单记录.
     *
     * @param page 当前页码
     * @return 分页数据
     */
    WhiteListPageVo findAll(Integer page, Integer size, String tenantName, String tenantUserName);

}