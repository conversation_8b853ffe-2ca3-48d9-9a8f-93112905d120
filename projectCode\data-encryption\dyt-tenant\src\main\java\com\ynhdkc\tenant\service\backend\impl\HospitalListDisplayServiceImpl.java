package com.ynhdkc.tenant.service.backend.impl;

import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ynhdkc.tenant.dao.mapper.HospitalListDisplayMapper;
import com.ynhdkc.tenant.entity.HospitalListDisplay;
import com.ynhdkc.tenant.model.CreateHospitalListDisplayReqDto;
import com.ynhdkc.tenant.model.HospitalListDisplayPageVo;
import com.ynhdkc.tenant.model.HospitalListDisplayVo;
import com.ynhdkc.tenant.model.UpdateHospitalListDisplayReqDto;
import com.ynhdkc.tenant.service.backend.IHospitalListDisplayService;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 医院显示配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@Service
@RequiredArgsConstructor
public class HospitalListDisplayServiceImpl implements IHospitalListDisplayService {

    private final HospitalListDisplayMapper mapper;
    private final PageVoConvert pageVoConvert;

    @Override
    public HospitalListDisplayVo createHospitalListDisplay(CreateHospitalListDisplayReqDto dto) {
        Long hospitalId = dto.getHospitalId();
        HospitalListDisplay display = mapper.selectOneByExample2(HospitalListDisplay.class, sql -> sql.andEqualTo(HospitalListDisplay::getHospitalId, hospitalId));
        if (display != null) {
            throw new BizException(HttpStatus.BAD_REQUEST, "该医院已存在配置");
        }
        HospitalListDisplay entity = IHospitalListDisplayService.toEntity(dto);
        int insertEffectCount = mapper.insertSelective(entity);
        if (insertEffectCount != 1) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "创建失败");
        }
        return IHospitalListDisplayService.toVo(entity);
    }

    @Override
    public BaseOperationResponse deleteHospitalListDisplay(Long id) {
        int deleteEffectCount = mapper.deleteByPrimaryKey(id);
        if (deleteEffectCount != 1) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "删除失败");
        }
        return new BaseOperationResponse();
    }

    @Override
    public HospitalListDisplayVo getHospitalListDisplayDetail(Long id) {
        HospitalListDisplay entity = mapper.selectByPrimaryKey(id);
        if (entity == null) {
            return new HospitalListDisplayVo();
        }
        return IHospitalListDisplayService.toVo(entity);
    }

    @Override
    public HospitalListDisplayPageVo getHospitalListDisplayPage(Integer currentPage, Integer pageSize) {
        Page<HospitalListDisplay> page = PageHelper.startPage(currentPage, pageSize);
        page.doSelectPage(() -> mapper.selectByExample2(HospitalListDisplay.class, sql -> {
        }));
        return pageVoConvert.toPageVo(page, HospitalListDisplayPageVo.class, IHospitalListDisplayService::toVo);
    }

    @Override
    public HospitalListDisplayVo updateHospitalListDisplay(Long id, UpdateHospitalListDisplayReqDto hospitalListDisplayDto) {
        HospitalListDisplay display = mapper.selectByPrimaryKey(id);
        if (display == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "未找到该医院配置");
        }
        HospitalListDisplay entity = IHospitalListDisplayService.toEntity(hospitalListDisplayDto);
        entity.setId(id);
        int updateEffectCount = mapper.updateByPrimaryKey(entity);
        if (updateEffectCount != 1) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "更新失败");
        }
        return IHospitalListDisplayService.toVo(entity);
    }
}
