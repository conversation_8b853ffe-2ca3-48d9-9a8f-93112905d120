package com.ynhdkc.tenant.dto.document;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;

import static com.ynhdkc.tenant.dto.document.HospitalDocument.INDEX_NAME;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/8/10 13:10
 */
@Data
@Document(indexName = INDEX_NAME, createIndex = false)
public class DepartmentDocument {
    @Id
    private Long id;

    @Field(type = FieldType.Text, store = true, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String name;

    @Field(type = FieldType.Text, store = true, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String shortName;

    @Field(name = "hospital_id", type = FieldType.Long, store = true)
    private Long hospitalId;

    @Field(name = "thrdpart_dep_code", type = FieldType.Text, store = true)
    private String thrdpartDepCode;

    @Field(name = "hospital_logo", type = FieldType.Keyword, store = true)
    private String hospitalLogo;

    @Field(name = "hospital_area_id", type = FieldType.Long, store = true)
    private Long hospitalAreaId;

    @Field(name = "hospital_area_name", type = FieldType.Text, store = true, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String hospitalAreaName;

    @Field(name = "hospital_area_code", type = FieldType.Keyword, store = true)
    private String hospitalAreaCode;

    @Field(name = "address_intro", type = FieldType.Keyword, store = true)
    private String addressIntro;

    @Field(type = FieldType.Text, store = true, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private String introduction;

    @Field(type = FieldType.Integer, store = true)
    private Integer sort;

    @Field(type = FieldType.Integer, store = true)
    private Integer recommended;

    @Field(type = FieldType.Keyword, store = true)
    private String uri;

    @Field(type = FieldType.Integer, store = true)
    private Integer enabled;

//    @Field(name = "create_time", type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", store = true)
//    private Date createTime;
//
//    @Field(name = "update_time", type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", store = true)
//    private Date updateTime;

    @JsonIgnore
    public static final String INDEX_NAME = "department";
    @JsonIgnore
    public static final String PROPERTY_NAME = "name";
    @JsonIgnore
    public static final String PROPERTY_HOSPITAL_AREA_ID = "hospital_area_id";
    @JsonIgnore
    public static final String PROPERTY_SHORT_NAME = "short_name";
    @JsonIgnore
    public static final String PROPERTY_HOSPITAL_AREA_NAME = "hospital_area_name";
    @JsonIgnore
    public static final String PROPERTY_INTRODUCTION = "introduction";
    @JsonIgnore
    public static final String PROPERTY_SORT = "sort";
    @JsonIgnore
    public static final String PROPERTY_ENABLED = "enabled";
}
