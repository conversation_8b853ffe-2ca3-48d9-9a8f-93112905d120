package com.ynhdkc.tenant.service.customer;

import backend.common.entity.dto.hisgateway.response.DepartmentDoctorItem;
import backend.common.entity.dto.hisgateway.response.MessageEnvelope;
import backend.common.enums.HospitalCode;
import backend.common.util.MessageUtil;
import backend.common.util.ObjectsUtils;
import backend.common.util.StringUtils;
import com.ynhdkc.tenant.dao.DoctorQuery;
import com.ynhdkc.tenant.dao.HospitalAreaSettingQuery;
import com.ynhdkc.tenant.dao.HospitalQuery;
import com.ynhdkc.tenant.dao.kafka.SyncDoctorKafkaRepository;
import com.ynhdkc.tenant.dao.redis.SyncDoctorRepository;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import com.ynhdkc.tenant.util.DateUtil;
import com.ynhdkc.tenant.util.PathUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class SyncDoctorService {

    private static final int DOCTOR_LEVEL_SIZE = 20;
    private static final Set<String> DOCTOR_LEVEL_HOSPITAL_CODE_SET = new HashSet<>();

    static {
        DOCTOR_LEVEL_HOSPITAL_CODE_SET.add(HospitalCode.YUNNAN_FIRST_PEOPLE_HOSPITAL.getCode());
        DOCTOR_LEVEL_HOSPITAL_CODE_SET.add(HospitalCode.YUNNAN_FIRST_PEOPLE_HOSPITAL_V2.getCode());
        DOCTOR_LEVEL_HOSPITAL_CODE_SET.add(HospitalCode.FU_WAI_YUNNAN_HOSPITAL.getCode());
        DOCTOR_LEVEL_HOSPITAL_CODE_SET.add(HospitalCode.KUNMING_CHILDREN_HOSPITAL_QIAN_XING.getCode());
        DOCTOR_LEVEL_HOSPITAL_CODE_SET.add(HospitalCode.KUNMING_CHILDREN_HOSPITAL_SHU_LIN.getCode());
    }

    @Resource
    private SyncDoctorRepository syncDoctorRepository;

    @Resource
    private DoctorQuery doctorQuery;

    @Resource
    private SyncDoctorKafkaRepository syncDoctorKafkaRepository;

    @Resource
    private HospitalQuery hospitalQuery;

    @Resource
    private HospitalAreaSettingQuery hospitalAreaSettingQuery;

    @Resource
    private IHisGatewayService iHisGatewayService;

    public void syncDoctorData4DoctorLevelTask() {
        int time = DateUtil.convert2Time();
        if (time > 160000 && time < 173000) {
            return;
        }
        for (HospitalCode hospitalCode : HospitalCode.values()) {
            if (!DOCTOR_LEVEL_HOSPITAL_CODE_SET.contains(hospitalCode.getCode())) {
                continue;
            }
            Hospital hospital = hospitalQuery.queryHospitalByCode(hospitalCode.getCode()).orElse(null);
            if (hospital == null) {
                continue;
            }
            syncOneHospitalDoctors(hospitalCode.getCode(), hospital);
        }
    }

    @Cacheable(value = "doctor_setting", key = "#hospitalCode", unless = "#result == null", cacheManager = "cacheManager")
    public AppointmentRuleSetting querySetting(String hospitalCode) {
        Optional<AppointmentRuleSetting> ruleSettingOptional = hospitalAreaSettingQuery.queryAppointmentRuleSettingBy(hospitalCode);
        return ruleSettingOptional.orElse(null);
    }

    private void syncOneHospitalDoctors(String hospitalCode, Hospital hospital) {
        int page = getPageNumber(hospitalCode);
        List<Doctor> doctorList = doctorQuery.queryList(hospitalCode, page, DOCTOR_LEVEL_SIZE);
        if (ObjectsUtils.isEmpty(doctorList)) {
            syncDoctorRepository.deletePageNumber(hospitalCode);
            return;
        }
        try {
            for (Doctor doctor : doctorList) {
                String message = assembleRequestEnvelope(hospitalCode, doctor, hospital);
                syncDoctorKafkaRepository.sendMessage(message);
            }
        } finally {
            syncDoctorRepository.increasePageNumber(hospitalCode);
        }
    }

    public void syncKunHuaDoctorBy(String departmentCode) {
        String hospitalCode = HospitalCode.YUNNAN_FIRST_PEOPLE_HOSPITAL_V2.getCode();
        List<Doctor> doctors = doctorQuery.queryByHospitalCodeAndDepartmentCodes(hospitalCode, Collections.singletonList(departmentCode));
        if (CollectionUtils.isEmpty(doctors)) {
            return;
        }

        Hospital hospital = hospitalQuery.queryHospitalByCode(hospitalCode).orElse(null);
        if (hospital == null) {
            return;
        }

        doctors.forEach(doctor -> {
            String message = assembleRequestEnvelope(hospitalCode, doctor, hospital);
            syncDoctorKafkaRepository.sendMessage(message);
        });
    }

    public void updateDoctor4DoctorLevel(long doctorId, DepartmentDoctorItem departmentDoctorItem, String hospitalCode,
                                         AppointmentRuleSetting setting) {
        Doctor doctorInfoFromHis = assembleHISDoctor(departmentDoctorItem, hospitalCode);

        Doctor databaseDoctor = doctorQuery.queryDoctorById(doctorId);
        if (hospitalCode.equals(HospitalCode.YUNNAN_FIRST_PEOPLE_HOSPITAL.getCode()) || hospitalCode.equals(HospitalCode.YUNNAN_FIRST_PEOPLE_HOSPITAL_V2.getCode())) {
            List<Doctor> kunHuaDoctors = doctorQuery.queryBy(hospitalCode, databaseDoctor.getThrdpartDoctorCode());

            kunHuaDoctors.forEach(kunHuaDoctor -> {
                Department department = assembleDepartment(kunHuaDoctor, hospitalCode);
                iHisGatewayService.updateDoctorInfoFromDepartmentDoctorItem(doctorInfoFromHis, kunHuaDoctor, department, setting);
            });
        } else {
            Department department = assembleDepartment(databaseDoctor, hospitalCode);
            iHisGatewayService.updateDoctorInfoFromDepartmentDoctorItem(doctorInfoFromHis, databaseDoctor, department, setting);
        }
    }

    private Doctor assembleHISDoctor(DepartmentDoctorItem departmentDoctorItem, String hospitalCode) {
        Doctor doctorInfoFromHis = new Doctor();
        doctorInfoFromHis.setHonor(departmentDoctorItem.getHonor());
        doctorInfoFromHis.setIntroduction(departmentDoctorItem.getDoctorDescription());
        doctorInfoFromHis.setSpeciality(departmentDoctorItem.getDoctorAdvantage());
        doctorInfoFromHis.setHeadImgUrl(departmentDoctorItem.getDoctorProfilePhoto());
        doctorInfoFromHis.setRankDictLabel(departmentDoctorItem.getDoctorRankLabel());
        doctorInfoFromHis.setRankDictType(departmentDoctorItem.getDoctorRankType());
        doctorInfoFromHis.setRankDictValue(departmentDoctorItem.getDoctorRankValue());
        doctorInfoFromHis.setName(departmentDoctorItem.getDoctorName());
        doctorInfoFromHis.setSort(departmentDoctorItem.getSort());
        doctorInfoFromHis.setRegistrationLevel(departmentDoctorItem.getRegistrationLevel());

        if (HospitalCode.YUNNAN_FIRST_PEOPLE_HOSPITAL.getCode().equals(hospitalCode) || HospitalCode.YUNNAN_FIRST_PEOPLE_HOSPITAL_V2.getCode().equals(hospitalCode)) {
            process4Yunnan1stHospital(departmentDoctorItem, doctorInfoFromHis);
        }

        return doctorInfoFromHis;
    }

    private void process4Yunnan1stHospital(DepartmentDoctorItem departmentDoctorItem, Doctor doctorInfoFromHis) {
        if (!ObjectsUtils.isEmpty(departmentDoctorItem.getDoctorProfilePhoto())) {
            String value = PathUtil.base64PreProcess(departmentDoctorItem.getDoctorProfilePhoto());
            doctorInfoFromHis.setHeadImgUrl(value);
        }
        if (!ObjectsUtils.isEmpty(departmentDoctorItem.getDoctorRankValue())) {
            doctorInfoFromHis.setHonor(departmentDoctorItem.getDoctorRankValue());
        }
    }

    private Department assembleDepartment(Doctor dbDoctor, String hospitalCode) {
        Department department = new Department();
        department.setId(dbDoctor.getDepartmentId());
        department.setHospitalCode(hospitalCode);

        return department;
    }

    private String assembleRequestEnvelope(String hospitalCode, Doctor doctor, Hospital hospital) {
        MessageEnvelope<Map<String, Object>> requestMessage = new MessageEnvelope<>();
        requestMessage.setTimestamp(System.currentTimeMillis());
        requestMessage.setMessageTraceId(StringUtils.getUUID(16));
        requestMessage.setHospitalCode(hospitalCode);
        requestMessage.setHospitalId(hospital.getId());
        requestMessage.setDoctorId(doctor.getId());
        requestMessage.setDepartmentId(doctor.getDepartmentId());

        Map<String, Object> requestParameters = new HashMap<>();
        requestParameters.put("doctor_id", doctor.getId());
        requestParameters.put("doctor_code", doctor.getThrdpartDoctorCode());
        requestParameters.put("code", doctor.getThrdpartDoctorCode());
        requestParameters.put("department_code", doctor.getDepartmentCode());
        requestParameters.put("hospital_code", hospitalCode);

        requestMessage.setParameterMap(requestParameters);
        return MessageUtil.object2JSONString(requestMessage);
    }


    private int getPageNumber(String hospitalCode) {
        Integer page = syncDoctorRepository.queryPageNumber(hospitalCode);
        if (page == null) {
            syncDoctorRepository.savePageNumber(hospitalCode, 1);
            return 1;
        }
        return page;
    }
}
