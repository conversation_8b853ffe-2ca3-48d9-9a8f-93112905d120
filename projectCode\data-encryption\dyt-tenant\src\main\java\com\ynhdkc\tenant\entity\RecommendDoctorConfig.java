package com.ynhdkc.tenant.entity;

import backend.common.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Table;

/**
 * author: hwc
 */
@Data
@Accessors(chain = true)
@Table(name = "t_recommend_doctor_config")
@ApiModel(description = "推荐医生配置")
public class RecommendDoctorConfig extends BaseEntity {

    @ApiModelProperty(value = "推荐配置ID", required = true, example = "1001")
    private Long recommendId;

    @ApiModelProperty(value = "医生ID", required = true, example = "2001")
    private Long doctorId;

    @ApiModelProperty(value = "所属科室ID", required = true, example = "3001")
    private Long departmentId;

    @ApiModelProperty(value = "所属院区ID", required = true, example = "4001")
    private Long hospitalAreaId;

    @ApiModelProperty(value = "所属院区编码", required = true, example = "HA001")
    private String hospitalAreaCode;

    @ApiModelProperty(value = "所属院区名称", required = true, example = "第一院区")
    private String hospitalAreaName;

    @ApiModelProperty(value = "所属医院编码", required = true, example = "5001")
    private Long hospitalId;

    @ApiModelProperty(value = "排序号，默认为0", example = "0")
    private Integer sort = 0;

    @ApiModelProperty(value = "所属医院名称", example = "市立医院")
    private String hospitalName;

    @ApiModelProperty(value = "数据标签", example = "tag1")
    private String dataTag;

    @ApiModelProperty(value = "是否已启用，默认为true", example = "true")
    private Boolean enabled = true;

}