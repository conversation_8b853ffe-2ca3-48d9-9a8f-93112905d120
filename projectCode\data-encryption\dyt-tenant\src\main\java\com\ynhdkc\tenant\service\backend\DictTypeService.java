package com.ynhdkc.tenant.service.backend;

import backend.common.exception.BizException;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ynhdkc.tenant.dao.mapper.DictTypeMapper;
import com.ynhdkc.tenant.entity.DictType;
import com.ynhdkc.tenant.model.CreateDictTypeDto;
import com.ynhdkc.tenant.model.DictTypePageVo;
import com.ynhdkc.tenant.model.DictTypeVo;
import com.ynhdkc.tenant.model.UpdateDictTypeDto;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/9 14:10:01
 */
@Service
@RequiredArgsConstructor
public class DictTypeService {

    private final DictTypeMapper dictTypeRepository;
    private final PageVoConvert pageVoConvert;
    private final Function<DictType, DictTypeVo> dictTypeVoFunction = q -> {
        DictTypeVo dictTypeVo = new DictTypeVo();
        dictTypeVo.setId(q.getId());
        dictTypeVo.setType(q.getType());
        dictTypeVo.setDescription(q.getDescription());
        dictTypeVo.setCreateTime(q.getCreateTime());
        dictTypeVo.setUpdateTime(q.getUpdateTime());
        return dictTypeVo;
    };

    private final BiFunction<CreateDictTypeDto, DictType, DictType> dictTypeFunction = (dto, dictType) -> {
        dictType.setType(dto.getType());
        dictType.setDescription(dto.getDescription());
        return dictType;
    };


    public DictTypeVo create(CreateDictTypeDto createDictTypeDto) {
        if (dictTypeRepository.countByType(createDictTypeDto.getType()) > 0) {
            throw new BizException(HttpStatus.CONFLICT, "字典类型已存在", createDictTypeDto.getType());
        }
        DictType dictType = dictTypeFunction.apply(createDictTypeDto, new DictType());
        dictTypeRepository.create(dictType);
        return dictTypeVoFunction.apply(dictType);
    }

    public DictTypePageVo getDictTypeList(String name, String description, Integer currentPage, Integer pageSize) {
        Page<DictType> dictTypePage;
        try (final Page<DictType> page = PageHelper.startPage(currentPage, pageSize)) {
            dictTypePage = page.doSelectPage(() -> dictTypeRepository.selectByConditions(name, description));
        }
        return pageVoConvert.toPageVo(dictTypePage, DictTypePageVo.class, dictTypeVoFunction);
    }

    public DictTypeVo update(Long dictTypeId, UpdateDictTypeDto updateDictTypeDto) {
        DictType dictType = dictTypeRepository.selectByPrimaryKey(dictTypeId);
        if (dictType == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "字典类型不存在", dictTypeId);
        }

        if (dictTypeRepository.countByTypeAndIdNot(updateDictTypeDto.getType(), dictTypeId) > 0) {
            throw new BizException(HttpStatus.CONFLICT, "字典类型已存在", updateDictTypeDto.getType());
        }
        dictTypeRepository.updateByPrimaryKeySelective(dictTypeFunction.apply(updateDictTypeDto, dictType));
        return dictTypeVoFunction.apply(dictType);
    }

    public DictTypeVo getDictTypeDetail(Long id) {
        DictType dictType = dictTypeRepository.selectByPrimaryKey(id);
        if (dictType == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "字典类型不存在", id);
        }
        return dictTypeVoFunction.apply(dictType);
    }

    public int delete(Long id) {
        int effectiveCount = dictTypeRepository.deleteByPrimaryKey(id);
        if (effectiveCount == 0) {
            throw new BizException(HttpStatus.NOT_FOUND, "字典类型不存在", id);
        }
        return effectiveCount;
    }

    public void dictTypeExist(String dictType) {
        if (dictTypeRepository.countByType(dictType) == 0) {
            throw new BizException(HttpStatus.BAD_REQUEST, "字典类型不存在", dictType);
        }
    }

    public List<DictTypeVo> getAllDictTypeVos() {
        return dictTypeRepository.selectAll().stream().map(dictTypeVoFunction).collect(Collectors.toList());
    }
}
