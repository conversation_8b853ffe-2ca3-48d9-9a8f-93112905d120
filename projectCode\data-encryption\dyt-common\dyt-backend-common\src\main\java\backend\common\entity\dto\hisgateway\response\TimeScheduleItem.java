package backend.common.entity.dto.hisgateway.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class TimeScheduleItem {

    @JsonProperty("hospital_id")
    private Long hospitalId;

    @JsonProperty("hospital_area_id")
    private Long hospitalAreaId;

    @JsonProperty("department_id")
    private Long departmentId;

    @JsonProperty("doctor_id")
    private Long doctorId;

    @JsonProperty("hospital_code")
    private String hospitalCode;

    @JsonProperty("registration_date")
    private Long registrationDate;

    @JsonProperty("registration_level")
    private Integer registrationLevel;

    @JsonProperty("registration_level_desc")
    private String registrationLevelDesc;

    @JsonProperty("doctor_type")
    private Integer doctorType;

    @JsonProperty("doctor_type_desc")
    private String doctorTypeDesc;

    @JsonProperty("resource_type")
    private Integer resourceType;

    @JsonProperty("resource_type_desc")
    private String resourceTypeDesc;

    private String resource;

    @JsonProperty("department_name")
    private String departmentName;

    @JsonProperty("department_code")
    private String departmentCode;

    @JsonProperty("doctor_name")
    private String doctorName;

    @JsonProperty("doctor_code")
    private String doctorCode;

    @JsonProperty("begin_time")
    private Long beginTime;

    @JsonProperty("end_time")
    private Long endTime;

    // his 格式的开始时间
    @JsonProperty("start_time_text")
    private String startTimeText;

    // his 格式的结束时间
    @JsonProperty("end_time_text")
    private String endTimeText;

    @JsonProperty("schedule_id")
    private String scheduleId;

    @JsonProperty("time_type")
    private Integer timeType;

    @JsonProperty("time_type_desc")
    private String timeTypeDesc;

    @JsonProperty("schedule_status")
    private Integer scheduleStatus;

    @JsonProperty("queue_no")
    private Integer queueNo;

    private Double amt;

    private Map<String, Object> rawParameters = new HashMap<>();

    @JsonProperty("src_sum")
    private Integer srcSum;

    @JsonProperty("src_number")
    private Integer srcNumber;
}
