package com.ynhdkc.tenant.dao.impl;

import com.ynhdkc.tenant.dao.HospitalRepository;
import com.ynhdkc.tenant.dao.mapper.*;
import com.ynhdkc.tenant.entity.*;
import com.ynhdkc.tenant.entity.setting.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-26 14:27
 */
@Repository
@RequiredArgsConstructor
public class HospitalRepositoryImpl implements HospitalRepository {
    private final HospitalMapper hospitalMapper;
    private final AddressMapper addressMapper;
    private final BuildingMapper buildingMapper;
    private final AreaMapper areaMapper;
    private final FloorMapper floorMapper;
    private final DepartmentMapper departmentMapper;
    private final DoctorMapper doctorMapper;
    private final TenantUserStructureMapper tenantUserStructureMapper;

    private final AppointmentRuleSettingMapper appointmentRuleSettingMapper;
    private final DiagnosisPaymentSettingMapper diagnosisPaymentSettingMapper;
    private final HospitalizationSettingMapper hospitalizationSettingMapper;
    private final PatientReportSettingMapper patientReportSettingMapper;
    private final PatientCardSettingMapper patientCardSettingMapper;
    private final CustomBusinessSettingMapper customBusinessSettingMapper;
    private final FunctionSettingMapper functionSettingMapper;

    @Override
    public void createHospital(Hospital hospital) {
        hospital.setHospitalTypeTag(0);
        hospital.setParentId(0L);
        hospitalMapper.insertSelective(hospital);
    }

    @Override
    public void updateHospital(Hospital hospital) {
        hospitalMapper.updateByPrimaryKeySelective(hospital);
    }

    @Override
    public void deleteHospital(Long hospitalId) {
        /* 获取所有需要删除的地址id */
        Set<Long> readyToDeleteAddress = new HashSet<>();
        Hospital hospital = hospitalMapper.selectByPrimaryKey(hospitalId);
        if (null != hospital.getAddressId()) {
            readyToDeleteAddress.add(hospital.getAddressId());
        }
        buildingMapper.selectByExample2(Building.class, sql ->
                        sql.andEqualTo(Building::getHospitalId, hospital.getId()))
                .stream()
                .map(Building::getAddressId).forEach(readyToDeleteAddress::add);


        hospitalMapper.deleteByPrimaryKey(hospitalId);
        /* 级联删除院区 */
        hospitalMapper.deleteByExample2(Hospital.class, sql -> sql.andEqualTo(Hospital::getParentId, hospitalId));
        /* 级联删除大楼 */
        buildingMapper.deleteByExample2(Building.class, sql -> sql.andEqualTo(Building::getHospitalId, hospitalId));
        /* 级联删除楼层 */
        floorMapper.deleteByExample2(Floor.class, sql -> sql.andEqualTo(Floor::getHospitalId, hospitalId));
        /* 级联删除区域 */
        areaMapper.deleteByExample2(Area.class, sql -> sql.andEqualTo(Area::getHospitalId, hospitalId));
        /* 级联删除科室 */
        departmentMapper.deleteByExample2(Department.class, sql -> sql.andEqualTo(Department::getHospitalId, hospitalId));
        /* 级联删除医生 */
        doctorMapper.deleteByExample2(Doctor.class, sql -> sql.andEqualTo(Doctor::getHospitalId, hospitalId));
        /* 级联删除组织架构 */
        tenantUserStructureMapper.selectByExample2(TenantUserStructure.class, sql -> sql.andEqualTo(TenantUserStructure::getHospitalId, hospitalId));
        readyToDeleteAddress.forEach(addressMapper::deleteByPrimaryKey);

        appointmentRuleSettingMapper.deleteByExample2(AppointmentRuleSetting.class, sql -> sql.andEqualTo(AppointmentRuleSetting::getHospitalId, hospitalId));
        diagnosisPaymentSettingMapper.deleteByExample2(DiagnosisPaymentSetting.class, sql -> sql.andEqualTo(DiagnosisPaymentSetting::getHospitalId, hospitalId));
        hospitalizationSettingMapper.deleteByExample2(HospitalizationSetting.class, sql -> sql.andEqualTo(HospitalizationSetting::getHospitalId, hospitalId));
        patientReportSettingMapper.deleteByExample2(PatientReportSetting.class, sql -> sql.andEqualTo(PatientReportSetting::getHospitalId, hospitalId));
        patientCardSettingMapper.deleteByExample2(PatientCardSetting.class, sql -> sql.andEqualTo(PatientCardSetting::getHospitalId, hospitalId));
        customBusinessSettingMapper.deleteByExample2(CustomBusinessSetting.class, sql -> sql.andEqualTo(CustomBusinessSetting::getHospitalId, hospitalId));
        functionSettingMapper.deleteByExample2(FunctionSetting.class, sql -> sql.andEqualTo(FunctionSetting::getHospitalId, hospitalId));
    }
}

