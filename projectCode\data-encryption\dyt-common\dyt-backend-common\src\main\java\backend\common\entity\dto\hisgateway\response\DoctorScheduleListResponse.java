package backend.common.entity.dto.hisgateway.response;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class DoctorScheduleListResponse {

    private List<DoctorScheduleListItem> result = new ArrayList<>();

    public void setResult(List<DoctorScheduleListItem> result) {
        if (result == null) {
            return;
        }
        this.result = result;
    }
}
