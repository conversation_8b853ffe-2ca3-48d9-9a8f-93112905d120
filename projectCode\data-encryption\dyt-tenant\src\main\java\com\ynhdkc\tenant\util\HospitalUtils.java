package com.ynhdkc.tenant.util;

import backend.common.enums.HospitalCode;

public class HospitalUtils {

    public static final String YUN_DA_MDT_DEPARTMENT_CODE = "5268";

    public static final String YUN_DA_SD_DEPARTMENT_CODE = "7025";

    private HospitalUtils() {
    }

    public static boolean isKunmingMU1stHospital(String hospitalCode) {
        return HospitalCode.KUNMING_MU_FIRST_AFFILIATED_HOSPITAL.getCode().equals(hospitalCode) ||
                HospitalCode.KUNMING_MU_FIRST_AFFILIATED_CG_CAMPUS_HOSPITAL.getCode().equals(hospitalCode);
    }

    public static boolean isYunnanSecondPeopleHospital(String hospitalCode) {
        return HospitalCode.YUNNAN_SECOND_PEOPLE_HOSPITAL.getCode().equals(hospitalCode);
    }

    public static boolean isGongRenYiYuan(String hospitalCode) {
        return HospitalCode.KUNMING_MU_SECOND_AFFILIATED_HOSPITAL.getCode().equals(hospitalCode);
    }

    public static boolean isTongRenHospital(String hospitalCode) {
        return HospitalCode.KUNMING_TONG_REN_HOSPITAL.getCode().equals(hospitalCode);
    }

    public static boolean isSpecialDepartment(String departmentCode) {
        if (YUN_DA_MDT_DEPARTMENT_CODE.equals(departmentCode) || YUN_DA_SD_DEPARTMENT_CODE.equals(departmentCode)) {
            return true;
        }
        if (!departmentCode.contains("|")) {
            return true;
        }
        String[] departmentList = departmentCode.split("\\|");
        if (departmentList.length != 2) {
            return false;
        }
        String secondDepartmentCode = departmentList[1];
        return Integer.parseInt(secondDepartmentCode) < 0;
    }

    public static String getRealDoctorCode(String departmentCode) {
        if (!departmentCode.contains("|")) {
            return departmentCode;
        }
        String[] departmentList = departmentCode.split("\\|");
        if (departmentList.length == 1) {
            return departmentCode;
        }
        String secondDepartmentCode = departmentList[departmentList.length - 1];
        if (Integer.parseInt(secondDepartmentCode) < 0) {
            return departmentCode.substring(0, departmentCode.lastIndexOf("|"));
        }
        return departmentCode;
    }
}
