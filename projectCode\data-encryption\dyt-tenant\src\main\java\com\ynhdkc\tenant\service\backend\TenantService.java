package com.ynhdkc.tenant.service.backend;

import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.entity.Tenant;
import com.ynhdkc.tenant.model.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-26 9:55
 */
public interface TenantService {
    TenantDetailVo create(TenantCreateReqDto request);

    TenantDetailVo update(Long tenantId, TenantUpdateReqDto request);

    BaseOperationResponse delete(Long tenantId);

    TenantPageVo query(TenantQueryReqDto request);

    TenantDetailVo getDetail(Long tenantId);

    TenantDetailVo rpcGetDetail(Long tenantId);

    static Tenant toTenant(TenantCreateReqDto dto) {
        Tenant entity = new Tenant();
        entity.setName(dto.getName());
        entity.setDescription(dto.getDescription());
        entity.setLogoUrl(dto.getLogoUrl());
        entity.setContact(dto.getContact());
        entity.setContactPhoneNumber(dto.getContactPhoneNumber());
        entity.setContactEmail(dto.getContactEmail());
        entity.setContactWechat(dto.getContactWechat());
        return entity;
    }

    static TenantVo toTenantVo(Tenant entity) {
        TenantVo vo = new TenantVo();
        vo.setId(entity.getId());
        vo.setName(entity.getName());
        vo.setDescription(entity.getDescription());
        vo.setAddressId(entity.getAddressId());
        vo.setLogoUrl(entity.getLogoUrl());
        vo.setContact(entity.getContact());
        vo.setContactPhoneNumber(entity.getContactPhoneNumber());
        vo.setContactAddressId(entity.getContactAddressId());
        vo.setContactEmail(entity.getContactEmail());
        vo.setContactWechat(entity.getContactWechat());
        vo.setCreateTime(entity.getCreateTime());
        return vo;
    }

    static TenantDetailVo toTenantDetailVo(Tenant entity) {
        TenantDetailVo vo = new TenantDetailVo();
        vo.setId(entity.getId());
        vo.setName(entity.getName());
        vo.setDescription(entity.getDescription());
        vo.setAddressId(entity.getAddressId());
        vo.setLogoUrl(entity.getLogoUrl());
        vo.setContact(entity.getContact());
        vo.setContactPhoneNumber(entity.getContactPhoneNumber());
        vo.setContactAddressId(entity.getContactAddressId());
        vo.setContactEmail(entity.getContactEmail());
        vo.setContactWechat(entity.getContactWechat());
        vo.setCreateTime(entity.getCreateTime());
        return vo;
    }
}
