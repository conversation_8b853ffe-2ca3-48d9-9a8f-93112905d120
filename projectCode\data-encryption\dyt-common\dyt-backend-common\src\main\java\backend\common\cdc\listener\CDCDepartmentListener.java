package backend.common.cdc.listener;

import backend.common.cdc.CDCListener;
import backend.common.cdc.CDCTopics;
import backend.common.cdc.ResourceChangeCapture;
import backend.common.cdc.dto.CDCDepartment;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;

@Slf4j
public class CDCDepartmentListener extends CDCListener<CDCDepartment> {

    public CDCDepartmentListener(ResourceChangeCapture<CDCDepartment> capture) {
        super(capture);
    }

    @KafkaListener(topics = CDCTopics.BACKEND_DEPARTMENT, groupId = "${spring.application.name}")
    public void kafkaListener(ConsumerRecord<String, byte[]> msg) throws Exception {
        this.doWork(msg, CDCDepartment.class);
    }
}
