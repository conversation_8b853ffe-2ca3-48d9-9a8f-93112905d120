package com.ynhdkc.tenant.service.customer.impl;


import backend.common.dao.MySqlCommonMapper;
import backend.common.exception.BizException;
import backend.common.util.MessageUtil;
import backend.common.util.MybatisUtil;
import backend.common.util.ObjectsUtils;
import backend.common.util.ResponseEntityUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.ynhdkc.tenant.client.AppointmentOrderClient;
import com.ynhdkc.tenant.client.ScheduleClient;
import com.ynhdkc.tenant.client.model.ApiQuickCheckOrderCountItem;
import com.ynhdkc.tenant.client.model.ApiQuickCheckOrderCountRequest;
import com.ynhdkc.tenant.client.model.ApiSchedulePageResponse;
import com.ynhdkc.tenant.client.model.ApiScheduleResponseItem;
import com.ynhdkc.tenant.dao.*;
import com.ynhdkc.tenant.dao.mapper.RecommendConfigMapper;
import com.ynhdkc.tenant.doctor.sort.context.DoctorSortContext;
import com.ynhdkc.tenant.dto.ScheduleResponseDto;
import com.ynhdkc.tenant.entity.*;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import com.ynhdkc.tenant.enums.CategoryType;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.AddressService;
import com.ynhdkc.tenant.service.customer.CustomerQuickCheckService;
import com.ynhdkc.tenant.service.customer.IHisGatewayService;
import com.ynhdkc.tenant.tool.DoctorListTool;
import com.ynhdkc.tenant.tool.SetDictInfoHelper;
import com.ynhdkc.tenant.tool.convert.DepartmentConverter;
import com.ynhdkc.tenant.tool.convert.DoctorConvert;
import com.ynhdkc.tenant.util.DoctorUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.ynhdkc.tenant.util.DateUtil.convertDateToMMDD;
import static com.ynhdkc.tenant.util.DateUtil.convertDateToWeekday;
import static java.util.Comparator.comparing;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/7/21 10:26
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerQuickCheckServiceImpl implements CustomerQuickCheckService {

    private static final String TODAY = "今天";
    private static final String TOMORROW = "明天";
    private static final String quickCheckHospitalBizType = "quick_check_hospital";
    private static final String quickCheckExpertDoctorBizType = "120";

    private final HospitalQuery hospitalQuery;
    private final HospitalAreaQuery hospitalAreaQuery;
    private final DepartmentQuery departmentQuery;
    private final DoctorQuery doctorQuery;
    private final HospitalAreaSettingQuery hospitalAreaSettingQuery;

    private final SetDictInfoHelper setDictInfoHelper;
    private final ScheduleClient scheduleClient;
    private final IHisGatewayService hisGatewayService;
    private final DoctorListTool doctorListTool;
    private final DoctorSortContext doctorSortContext;
    private final RecommendConfigMapper recommendConfigMapper;
    private final AddressService addressService;
    private final AppointmentOrderClient appointmentOrderClient;

    private static CustomerQuickCheckDateInWeekScheduleInfo convertToCustomerQuickCheckDateInWeekScheduleInfo(ScheduleResponseDto.ScheduleInfo scheduleInfo) {
        if (scheduleInfo == null) {
            return null;
        }
        CustomerQuickCheckDateInWeekScheduleInfo info = new CustomerQuickCheckDateInWeekScheduleInfo();
        info.setCreateBy(scheduleInfo.getCreateBy());
        info.setCreateTime(scheduleInfo.getCreateTime());
        info.setDepartmentId(scheduleInfo.getDepartmentId());
        info.setDepartmentName(scheduleInfo.getDepartmentName());
        info.setDepartmentCode(scheduleInfo.getDepartmentCode());
        info.setExecDepartmentCode(scheduleInfo.getExecDepartmentCode());
        info.setDoctorId(scheduleInfo.getDoctorId());
        info.setDoctorCode(scheduleInfo.getDoctorCode());
        info.setDoctorName(scheduleInfo.getDoctorName());
        info.setEditBy(scheduleInfo.getEditBy());
        info.setEndTime(scheduleInfo.getEndTime());
        info.setGhf(scheduleInfo.getGhf());
        info.setHospitalAreaId(scheduleInfo.getHospitalAreaId());
        info.setHospitalAreaName(scheduleInfo.getHospitalAreaName());
        info.setHospitalId(scheduleInfo.getHospitalId());
        info.setHospitalName(scheduleInfo.getHospitalName());
        info.setId(scheduleInfo.getId());
        info.setMaxTreatNum(scheduleInfo.getMaxTreatNum());
        info.setSchDate(scheduleInfo.getSchDate());
        info.setScheduleId(scheduleInfo.getScheduleId());
        info.setScheduleInfo(scheduleInfo.getScheduleInfo());
        info.setShowSchDate(scheduleInfo.getShowSchDate());
        info.setSrcNumber(scheduleInfo.getSrcNum());
        info.setStartTime(scheduleInfo.getStartTime());
        info.setStatus(scheduleInfo.getStatus());
        info.setTimePart(scheduleInfo.getTimePart());
        info.setTimeType(scheduleInfo.getTimeType());
        info.setTimeTypeHis(scheduleInfo.getTimeTypeHis());
        info.setTimeTypeText(scheduleInfo.getTimeTypeText());
        info.setTotalFee(scheduleInfo.getTotalFee());
        info.setTotalFeeFormat(scheduleInfo.getTotalFeeFormat());
        info.setUpdateTime(scheduleInfo.getUpdateTime());
        info.setUsedNum(scheduleInfo.getUsedNum());
        info.setZjf(scheduleInfo.getZjf());
        info.setZlf(scheduleInfo.getZlf());
        info.setRegistrationDate(scheduleInfo.getRegistrationDate());
        info.setRegistrationLevelDesc(scheduleInfo.getRegistrationLevelDesc());
        return info;
    }

    private static List<CustomerQuickCheckDoctorVo> filterByPrice(CustomerQuickCheckQueryDoctorsPageReqDto request, List<CustomerQuickCheckDoctorVo> list) {
        if (request.getMaxPrice() != null) {
            list = list.stream().filter(v -> v.getMinPrice() != null && v.getMinPrice().compareTo(BigDecimal.valueOf(request.getMaxPrice())) < 0).collect(Collectors.toList());
        }
        if (request.getMinPrice() != null) {
            list = list.stream().filter(v -> v.getMinPrice() != null && v.getMinPrice().compareTo(BigDecimal.valueOf(request.getMinPrice())) >= 0).collect(Collectors.toList());
        }
        return list;
    }

    private static Comparator<CustomerQuickCheckDoctorVo> getSort(String field, String direction) {
        if (StrUtil.isBlank(field)) {
            field = "distance";
        }

        Comparator<CustomerQuickCheckDoctorVo> comparing;
        switch (field) {
            case "bookedNum":
                comparing = comparing(CustomerQuickCheckDoctorVo::getBookedNum);
                break;
            case "minPrice":
                comparing = comparing(CustomerQuickCheckDoctorVo::getMinPrice);
                break;
            case "distance":
            default:
                comparing = comparing(CustomerQuickCheckDoctorVo::getDistance);
                break;
        }
        if ("desc".equalsIgnoreCase(direction)) {
            comparing = comparing.reversed();
        }
        return comparing;
    }

    @Override
    public List<CustomerQuickCheckDepartmentVo> queryGroupedDepartsAndDoctors(CustomerQuickCheckQueryDepartmentReqDto request) {
        List<Department> departmentList = queryDepartmentList(request.getHospitalAreaId(), null);

        return departmentList.stream()
                .map(d -> {
                    CustomerQuickCheckDepartmentVo customerQuickCheckDepartmentVo = DepartmentConverter.toCustomerQuickCheckDepartmentVo(d);
                    CustomerQuickCheckDoctorScheduleInfoVo customerDoctorScheduleInfoVo = queryGroupedDoctorList(d);
                    customerQuickCheckDepartmentVo.setAllDoctors(customerDoctorScheduleInfoVo.getAllDoctors());
                    return customerQuickCheckDepartmentVo;
                })
                .sorted(comparing(CustomerQuickCheckDepartmentVo::getSort).reversed())
                .collect(Collectors.toList())
                ;
    }

    private List<Doctor> getAllDoctors(Department department) {
        return doctorQuery.queryBy(department.getId());
    }

    private List<DoctorGroupRelation> getDoctorGroupRelations(List<Long> doctorIds) {
        return doctorQuery.listDoctorGroupRelationBy(doctorIds);
    }

    private List<Doctor> getEnabledDoctors(Department department) {
        List<Doctor> doctorList = getAllDoctors(department);
        return doctorList.stream().filter(q -> q.getStatus() == 0).collect(Collectors.toList());
    }

    private List<CustomerQuickCheckAllScheduleDoctorDetailVo> compositeAllDoctor(List<Doctor> doctors, List<ScheduleResponseDto.ScheduleInfo> scheduleInfos, Department department, List<DoctorGroupRelation> doctorGroupRelations, Integer displayDoctorUnderDepartment) {
        return doctors.stream().parallel().map(doctor -> {
            CustomerQuickCheckAllScheduleDoctorDetailVo vo = DoctorConvert.toQuickCheckDoctorAllScheduleInfoVo(doctor);
            setDictInfoHelper.setDictInfo(doctor, vo);

            if (enrichCustomerQuickCheckAllScheduleDoctorDetailVo(scheduleInfos, department.getName(), doctorGroupRelations, displayDoctorUnderDepartment, vo)) {
                return null;
            }
            return vo;
        }).filter(Objects::nonNull)
                .sorted(comparing(CustomerQuickCheckAllScheduleDoctorDetailVo::getSort).reversed())
                .collect(Collectors.toList());
    }

    public boolean enrichCustomerQuickCheckAllScheduleDoctorDetailVo(List<ScheduleResponseDto.ScheduleInfo> scheduleInfos, String departmentName, List<DoctorGroupRelation> doctorGroupRelations, Integer displayDoctorUnderDepartment, CustomerQuickCheckAllScheduleDoctorDetailVo vo) {
        if (displayDoctorUnderDepartment == 1) {
            List<ScheduleResponseDto.ScheduleInfo> scheduleInfoList = scheduleInfos.stream().filter(scheduleInfo -> scheduleInfo.getDoctorCode().equals(vo.getThrdpartDoctorCode())).collect(Collectors.toList());

            log.info("enrichCustomerQuickCheckAllScheduleDoctorDetailVo: {}", MessageUtil.object2JSONString(scheduleInfoList));

            if (CollectionUtils.isEmpty(scheduleInfoList)) {
                return true;
            }
        }
        List<ScheduleResponseDto.ScheduleInfo> doctorScheduleInfos = scheduleInfos.stream().filter(scheduleInfo -> filterScheduleDoctor(scheduleInfo, vo)).collect(Collectors.toList());

        vo.setDateInWeeks(doctorScheduleInfos.stream().map(this::scheduleInfoToDateInWeek).collect(Collectors.toSet()).stream().sorted(comparing(CustomerQuickCheckDateInWeekDto::getLocalDate)).collect(Collectors.toList()));
        vo.setShowSchDate(doctorScheduleInfos.stream().anyMatch(scheduleInfo -> scheduleInfo.getShowSchDate() == 1));
        vo.setCanOrder(vo.getDateInWeeks().stream().anyMatch(CustomerQuickCheckDateInWeekDto::isCanOrder));

        Map<String, String> departmentCodeAndDepartmentName = scheduleInfos.stream().filter(q -> q.getDepartmentCode() != null && q.getDepartmentName() != null).collect(Collectors.toMap(ScheduleResponseDto.ScheduleInfo::getDepartmentCode, ScheduleResponseDto.ScheduleInfo::getDepartmentName, (k1, k2) -> k1));
        vo.setDepartmentName(getDepartmentName(departmentCodeAndDepartmentName, vo.getDepartmentCode(), departmentName));
        vo.setBelongDoctorGroups(doctorGroupRelations.stream().anyMatch(q -> q.getDoctorId().equals(vo.getId())));
        return false;
    }

    private boolean filterScheduleDoctor(ScheduleResponseDto.ScheduleInfo scheduleInfo, CustomerQuickCheckAllScheduleDoctorDetailVo doctor) {
        if (ObjectsUtils.isEmpty(scheduleInfo.getDepartmentCode())) {
            return scheduleInfo.getDoctorCode().equals(doctor.getThrdpartDoctorCode());
        }

        return scheduleInfo.getDepartmentCode().equals(doctor.getDepartmentCode()) && scheduleInfo.getDoctorCode().equals(doctor.getThrdpartDoctorCode());
    }

    private CustomerQuickCheckDateInWeekDto scheduleInfoToDateInWeek(ScheduleResponseDto.ScheduleInfo schedule) {
        CustomerQuickCheckDateInWeekDto customerDateInWeek = new CustomerQuickCheckDateInWeekDto();
        String dateInWeek = convertDateToWeekday(schedule.getSchDate());
        if (TODAY.equals(dateInWeek)) {
            customerDateInWeek.setDateInWeek(dateInWeek);
        } else if (TOMORROW.equals(dateInWeek)) {
            customerDateInWeek.setDateInWeek(dateInWeek);
        } else {
            customerDateInWeek.setDateInWeek(convertDateToMMDD(schedule.getSchDate()));
        }

        customerDateInWeek.setDate(convertDateToMMDD(schedule.getSchDate()));
        customerDateInWeek.setLocalDate(schedule.getSchDate());
        customerDateInWeek.setCanOrder(null != schedule.getSrcNum() && schedule.getSrcNum() > 0);
        customerDateInWeek.setScheduleInfo(convertToCustomerQuickCheckDateInWeekScheduleInfo(schedule));
        return customerDateInWeek;
    }

    private String getDepartmentName(Map<String, String> departmentNameMap, String departmentCode, String departmentNameFromDepartment) {
        if (ObjectsUtils.isEmpty(departmentNameMap)) {
            return departmentNameFromDepartment;
        }

        String departmentName = departmentNameMap.get(departmentCode);
        if (departmentName == null) {
            return departmentNameFromDepartment;
        }
        return departmentName;
    }

    public CustomerQuickCheckDoctorScheduleInfoVo queryGroupedDoctorList(Department department) {
        Optional<AppointmentRuleSetting> ruleSettingOptional = hospitalAreaSettingQuery.queryAppointmentRuleSettingBy(department.getHospitalAreaId());
        if (!ruleSettingOptional.isPresent()) {
            throw new BizException(HttpStatus.NOT_FOUND, "预约规则不存在");
        }

        AppointmentRuleSetting appointmentRuleSetting = ruleSettingOptional.get();
        List<Doctor> doctors = getAllDoctors(department);
        List<ScheduleResponseDto.ScheduleInfo> scheduleInfos = hisGatewayService.getDepartmentDoctorScheduleList(department, doctors, appointmentRuleSetting);
        doctors = getEnabledDoctors(department);
        if (CollectionUtils.isEmpty(doctors)) {
            log.info("doctors is empty, request his, hospital code {}, department code {},department info {}", department.getHospitalCode(), department.getThrdpartDepCode(), department);
            return new CustomerQuickCheckDoctorScheduleInfoVo();
        }

        Integer displayDoctorUnderDepartment = ruleSettingOptional.map(AppointmentRuleSetting::getDisplayDoctorUnderDepartment).orElse(0);

        List<DoctorGroupRelation> doctorGroupRelations = getDoctorGroupRelations(doctors.stream().map(Doctor::getId).collect(Collectors.toList()));

        CustomerQuickCheckDoctorScheduleInfoVo vo = new CustomerQuickCheckDoctorScheduleInfoVo();
//        Set<LocalDate> scheduleDates = getScheduleDate(doctors, department, appointmentRuleSetting);

        doctorSortContext.sortDoctors(doctors, department.getHospitalCode());

        /*List<CustomerDoctorScheduleGroupedByDateVo> customerDoctorScheduleGroupedByDateVos = compositeScheduleDoctorInGroup(scheduleDates, doctors, scheduleInfos, department, doctorGroupRelations, appointmentRuleSetting);
        vo.setGroupedDoctors(customerDoctorScheduleGroupedByDateVos);*/

        List<CustomerQuickCheckAllScheduleDoctorDetailVo> customerAllScheduleDoctorDetailVos = compositeAllDoctor(doctors, scheduleInfos, department, doctorGroupRelations, displayDoctorUnderDepartment);

        if (!ObjectsUtils.isEmpty(customerAllScheduleDoctorDetailVos)) {
            customerAllScheduleDoctorDetailVos.forEach(y -> y.setRequestDepartmentId(department.getId()));
        }
        vo.setAllDoctors(customerAllScheduleDoctorDetailVos);

        vo.setSystemDepends(ruleSettingOptional.map(AppointmentRuleSetting::getSystemDepends).orElse(null));
        DoctorUtils.setDoctorTitle(ruleSettingOptional.get(), vo.getAllDoctors());
        /*vo.getGroupedDoctors().forEach(v -> {
            DoctorUtils.setDoctorTitle(ruleSettingOptional.get(), v.getDoctors());
        });*/
        return vo;
    }

    @Override
    public List<Department> queryDepartmentList(Long hospitalAreaId, String quickCheckType) {
        DepartmentQuery.DepartmentQueryOption option = new DepartmentQuery.DepartmentQueryOption(1, 9999)
                .setCategory(Lists.newArrayList(CategoryType.QUICK_CHECK.getValue()))
                .setEnabled(true)
                ;
        if (hospitalAreaId != null) {
            option.setHospitalAreaId(hospitalAreaId);
        }
        try (Page<Department> departmentPage = departmentQuery.pageQueryDepartment(option)) {
            if (!CollectionUtils.isEmpty(departmentPage.getResult())) {
                List<Department> departmentList = departmentPage.getResult();
                if (StringUtils.hasText(quickCheckType)) {
                    departmentList = departmentList.stream()
                            .filter(d -> d.getThrdpartDepCode().equals(quickCheckType))
                            .collect(Collectors.toList());
                }
                return departmentList;
            }
            return Collections.emptyList();
        }
    }

    @Override
    public List<Doctor> queryDoctorList(Long hospitalAreaId, String quickCheckType, List<Integer> tags) {
        List<Long> departIds = queryDepartmentList(hospitalAreaId, quickCheckType)
                .stream()
                .map(Department::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(departIds)) {
            return Collections.emptyList();
        }
        DoctorQuery.DoctorQueryOption option = new DoctorQuery.DoctorQueryOption(1, 9999)
                .setCategory(Lists.newArrayList(CategoryType.QUICK_CHECK.getValue()))
                .setIncludeDepartmentIds(departIds)
                .setStatus(0)
                ;
        if (hospitalAreaId != null) {
            option.setHospitalAreaId(hospitalAreaId);
        }
        if (!CollectionUtils.isEmpty(tags)) {
            option.setTags(tags);
        }

        try (Page<Doctor> doctorPage = doctorQuery.pageQuery(option)) {
            return doctorPage.getResult();
        }
    }

    @Override
    public List<CustomerQuickCheckDoctorVo> queryDoctors(CustomerQuickCheckQueryDoctorsPageReqDto request) {
        // 1.找到所有快速检查的医生
        List<Doctor> doctorList = queryDoctorList(request.getHospitalAreaId(), request.getQuickCheckType(), request.getDoctorTags());
        // 2.找到所有快速检查的医院
        Map<Long, Hospital> hospitalAreaMap = getHospitalAreaMap(doctorList);
        doctorList = doctorList.stream()
                .filter(d -> {
                    if (d.getHospitalAreaId() == null) {
                        return false;
                    }
                    Hospital hospital = hospitalAreaMap.get(d.getHospitalAreaId());
                    if (hospital == null) {
                        return false;
                    }
                    return hospital.getStatus() == 0;
                }).collect(Collectors.toList());

        List<Long> doctorIds = doctorList.stream()
                .map(Doctor::getId)
                .collect(Collectors.toList());
        Map<Long, Doctor> doctorMap = doctorList.stream()
                .collect(Collectors.toMap(Doctor::getId, v -> v, (k1, k2) -> k1));


        Pair<String, String> startAndEnd = getStartAndEnd(request.getHospitalAreaId());
        Map<Long, List<ApiScheduleResponseItem>> doctorScheduleMap = querySchedule4innerHis(request.getCurrentPage(), request.getPageSize(), doctorIds, startAndEnd);

        List<CustomerQuickCheckDoctorVo> list = new ArrayList<>();
        for (Map.Entry<Long, List<ApiScheduleResponseItem>> entry : doctorScheduleMap.entrySet()) {
            Long k = entry.getKey();
            List<ApiScheduleResponseItem> value = entry.getValue();
            Doctor doctor = doctorMap.get(k);
            CustomerQuickCheckDoctorVo customerQuickCheckDoctorVo = DoctorConvert.toCustomerQuickCheckDoctorVo(doctor);
            customerQuickCheckDoctorVo.setQuickCheckType(request.getQuickCheckType());
            //找出v中的属性totalFee最小的对象
            Optional<ApiScheduleResponseItem> minTotalFee = value.stream().min(comparing(ApiScheduleResponseItem::getTotalFee));
            if (hospitalAreaMap.containsKey(doctor.getHospitalAreaId())) {
                Hospital hospital = hospitalAreaMap.get(doctor.getHospitalAreaId());
                customerQuickCheckDoctorVo.setHospitalAreaName(hospital.getName());
            }
            minTotalFee.ifPresent(s -> customerQuickCheckDoctorVo.setMinPrice(s.getTotalFee()));
            list.add(customerQuickCheckDoctorVo);
        }

        //根据价格过滤
        list = filterByPrice(request, list);

        //获取医院位置距离
        list.forEach(v -> v.setDistance(getDistance(request.getLongitude(), request.getLatitude(), hospitalAreaMap.get(v.getHospitalAreaId()))));
        //排序
        list.sort(getSort(request.getSortBy(), request.getDirection()));
        return list;
    }

    private Double getDistance(Double longitude, Double latitude, Hospital hospital) {
        if(hospital == null || hospital.getAddressId() == null){
            return 999D;
        }
        Address addressVoById = addressService.getAddressVoById2(hospital.getAddressId());
        if (addressVoById == null) {
            return 999D;
        } else {
            return this.getDistance(longitude, latitude, addressVoById.getLongitude(), addressVoById.getLatitude());
        }
    }

    @Override
    public ExpertAppointmentDoctorPageVo queryExpertAppointmentDoctors(List<String> displayTags, Integer current, Integer pageSize) {
        log.info("queryExpertAppointmentDoctors displayTags: {}, current: {}, pageSize: {}", displayTags, current, pageSize);
        Page<RecommendConfig> page = getExpertHospitals(displayTags, current, pageSize);
        List<RecommendConfig> hospitals = page.getResult();
        if (CollectionUtils.isEmpty(hospitals)) {
            return null;
        }
        ExpertAppointmentDoctorPageVo response = new ExpertAppointmentDoctorPageVo();
        List<ExpertAppointmentDoctorPageVoList> hosList = new ArrayList<>();
        hospitals.forEach(hos -> {
            ExpertAppointmentDoctorPageVoList hospitalVo = new ExpertAppointmentDoctorPageVoList();
            Hospital hospital = hospitalQuery.queryHospitalById2(Long.parseLong(hos.getDataId()));
            if (hospital != null) {
                hospitalVo.setHospitalCode(hospital.getHospitalCode());
                hospitalVo.setHospitalLogo(hospital.getLogo());
                hospitalVo.setHospitalAreaId(hospital.getId());
                hospitalVo.setHospitalId(hospital.getParentId());
                if (hospital.getParentId() != 0L) {
                    Hospital parentHospital = hospitalQuery.queryHospitalById2(hospital.getParentId());
                    hospitalVo.setHospitalName(parentHospital.getName());
                    hospitalVo.setHospitalLogo(parentHospital.getLogo());
                } else {
                    hospitalVo.setHospitalName(hospital.getName());
                    hospitalVo.setHospitalLogo(hospital.getLogo());
                }
                List<ExpertAppointmentDoctorPageVoDoctors> doctorList = new ArrayList<>();
                getDoctors(displayTags, doctorList, hos.getDataId());
                hospitalVo.setDoctors(doctorList);
                hosList.add(hospitalVo);
            }
        });
        response.setList(hosList);
        response.setCurrentPage(current);
        response.setPageSize(pageSize);
        response.setTotalSize(page.getTotal());
        return response;
    }

    /**
     * 快速检查页面- 获取全部机构按钮
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return
     */
    @Override
    public List<CustomerQuickCheckAllHospitalVo> getAllHospital(Double longitude, Double latitude, Boolean hot) {
        List<Doctor> doctorList = queryDoctorList(null, null, null);
        // 2.找到所有快速检查的医院
        Map<Long, Hospital> hospitalAreaMap = getHospitalAreaMap(doctorList);
        doctorList = doctorList.stream()
                .filter(d -> {
                    if (d.getHospitalAreaId() == null) {
                        return false;
                    }
                    Hospital hospital = hospitalAreaMap.get(d.getHospitalAreaId());
                    if (hospital == null) {
                        return false;
                    }
                    return hospital.getStatus() == 0;
                }).collect(Collectors.toList());
        // hospitalAreaId : Doctor
        Map<Long, List<Doctor>> hospitalAreaDoctorMap = doctorList.stream()
                .collect(Collectors.groupingBy(Doctor::getHospitalAreaId));
        // hospitalAreaId : Hospital
        // doctorId : 排班信息
        List<CustomerQuickCheckAllHospitalVo> resList = new ArrayList<>();
        hospitalAreaMap.forEach((k, v) -> {
            List<Doctor> doctors = hospitalAreaDoctorMap.get(k);
            Pair<String, String> startAndEnd = getStartAndEnd(v.getId());
            List<Long> doctorIds = doctors.stream()
                    .map(Doctor::getId)
                    .collect(Collectors.toList());
            Map<Long, List<ApiScheduleResponseItem>> doctorScheduleMap = querySchedule4innerHis(1, 9999, doctorIds, startAndEnd);
            List<CustomerQuickCheckAllHospitalVoCheckItems> checkItems = new ArrayList<>();
            for (Doctor doctor : doctors) {
                List<ApiScheduleResponseItem> scheduleList = doctorScheduleMap.get(doctor.getId());
                if (CollectionUtils.isEmpty(scheduleList)) {
                    continue;
                }
                CustomerQuickCheckAllHospitalVoCheckItems checkItem = new CustomerQuickCheckAllHospitalVoCheckItems();
                checkItem.setCheckItemName(doctor.getName());
                // 找出v中的属性totalFee最小的对象
                Optional<ApiScheduleResponseItem> minTotalFee = scheduleList.stream().min(Comparator.comparing(ApiScheduleResponseItem::getTotalFee));
                minTotalFee.ifPresent(s -> checkItem.setMinPrice(s.getTotalFee()));
                checkItem.setQuickCheckType(String.valueOf(doctor.getDepartmentId()));
                checkItems.add(checkItem);
            }
            if (!checkItems.isEmpty()) {
                CustomerQuickCheckAllHospitalVo resVo = new CustomerQuickCheckAllHospitalVo();
                resVo.setCheckItems(checkItems);
                resVo.setHospitalId(v.getParentId());
                resVo.setHospitalAreaId(k);
                resVo.setHospitalCode(v.getHospitalCode());
                resVo.setHospitalLevel(v.getLevelDictLabel());
                getHospitalName(v, resVo);
                resVo.setSort(v.getDisplaySort());
                resVo.setDistance(getDistance(longitude, latitude, v));
                // todo 查询预约单量
                resList.add(resVo);
            }
        });

        Map<String, Integer> orderCountMap = getHospitalOrderCount(resList);
        resList.forEach(v -> v.setBookedNum(orderCountMap.getOrDefault(String.valueOf(v.getHospitalAreaId()), 0)));

        Comparator<CustomerQuickCheckAllHospitalVo> comparator = Boolean.TRUE.equals(hot)
                ? comparing(CustomerQuickCheckAllHospitalVo::getBookedNum).reversed()
                : comparing(CustomerQuickCheckAllHospitalVo::getSort).reversed();
        resList.sort(comparator);
        return resList;
    }

    private Map<String, Integer> getHospitalOrderCount(List<CustomerQuickCheckAllHospitalVo> resList) {
        ApiQuickCheckOrderCountRequest countRequest = new ApiQuickCheckOrderCountRequest();
        countRequest.setHospitalAreaIds(resList.stream().map(CustomerQuickCheckAllHospitalVo::getHospitalAreaId).collect(Collectors.toList()));
        ResponseEntity<List<ApiQuickCheckOrderCountItem>> orderCountList = appointmentOrderClient.getQuickCheckOrderCount(countRequest);
        List<ApiQuickCheckOrderCountItem> checkOrderCountItems = ResponseEntityUtil.assertAndGetBody(orderCountList, "未获取到预约单量");
        return checkOrderCountItems.stream().collect(Collectors.toMap(ApiQuickCheckOrderCountItem::getKey, ApiQuickCheckOrderCountItem::getValue));
    }

    private double getDistance(Double longitude, Double latitude, BigDecimal longitudeHos, BigDecimal latitudeHos) {
        if (longitude == null || latitude == null || longitudeHos == null || latitudeHos == null) {
            return 999d;
        }
        final int R = 6371; // 地球半径，单位：公里
        double latDistance = Math.toRadians(latitudeHos.doubleValue() - latitude);
        double lonDistance = Math.toRadians(longitudeHos.doubleValue() - longitude);
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(latitude)) * Math.cos(Math.toRadians(latitudeHos.doubleValue()))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return Math.round(R * c * 10) / 10.0;// 计算距离，单位：公里
    }

    private void getHospitalName(Hospital hospital, CustomerQuickCheckAllHospitalVo resVo) {
        if (hospital.getParentId() != 0L) {
            Hospital parentHospital = hospitalQuery.queryHospitalById2(hospital.getParentId());
            resVo.setHospitalName(parentHospital.getName());
            resVo.setHospitalLogo(parentHospital.getLogo());
        } else {
            resVo.setHospitalName(hospital.getName());
            resVo.setHospitalLogo(hospital.getLogo());
        }
    }

    private void getDoctors(List<String> displayTags, List<ExpertAppointmentDoctorPageVoDoctors> doctorList, String hospitalId) {
        Consumer<MySqlCommonMapper.Helper<RecommendConfig>> condition = helper -> {
            helper.defGroup(sql -> {
                if (displayTags != null && !displayTags.isEmpty()) {
                    for (String displayTag : displayTags) {
                        sql.orLike(RecommendConfig::getDisplayTags, MybatisUtil.likeBoth(displayTag));
                    }
                }
                sql.andEqualTo(RecommendConfig::getBizType, quickCheckExpertDoctorBizType);
                sql.andEqualTo(RecommendConfig::getDataTag, hospitalId);
                sql.andEqualTo(RecommendConfig::getEnabled, true);
            });
            helper.builder(order -> order.orderByAsc(RecommendConfig::getSort));
        };

        List<RecommendConfig> doctors = recommendConfigMapper.selectByExample(RecommendConfig.class, condition);
        // 获取医生id 转成Long
        List<Long> doctorIds = doctors.stream().map(RecommendConfig::getDataId).map(Long::parseLong).collect(Collectors.toList());
        doctorQuery.queryByIds(doctorIds).forEach(doctor -> {
            ExpertAppointmentDoctorPageVoDoctors doctorVo = new ExpertAppointmentDoctorPageVoDoctors();
            doctorVo.setDoctorId(doctor.getId());
            doctorVo.setDoctorName(doctor.getName());
            doctorVo.setDoctorHeadImgUrl(doctor.getHeadImgUrl());
            doctorVo.setDepartmentId(doctor.getDepartmentId());
            doctorVo.setDepartmentCode(doctor.getDepartmentCode());
            if (StringUtils.hasText(doctor.getThrdpartDoctorCode())) {
                doctorVo.setDoctorCode(doctor.getThrdpartDoctorCode());
            }
            doctorList.add(doctorVo);
        });
    }

    private Page<RecommendConfig> getExpertHospitals(List<String> displayTags, Integer current, Integer pageSize) {
        tk.mybatis.mapper.entity.Example example = new tk.mybatis.mapper.entity.Example(RecommendConfig.class);
        example.orderBy("sort").asc();

        tk.mybatis.mapper.entity.Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("bizType", quickCheckHospitalBizType);
        criteria.andEqualTo("enabled", true);

        if (!CollectionUtils.isEmpty(displayTags)) {
            tk.mybatis.mapper.entity.Example.Criteria orCriteria = example.createCriteria();
            for (String tag : displayTags) {
                orCriteria.orLike("displayTags", "%" + tag + "%");
            }
            // 使用and将orCriteria添加到主条件中
            example.and(orCriteria);
        }

        Page<RecommendConfig> page = PageHelper.startPage(current, pageSize);
        page.doSelectPage(() -> recommendConfigMapper.selectByExample(example));
        return page;
    }

    private Pair<String, String> getStartAndEnd(Long hospitalAreaId) {
        LocalDate now = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        if (Objects.isNull(hospitalAreaId)) {
            return Pair.of(now.format(formatter), now.plusDays(30).format(formatter));
        }
        Optional<AppointmentRuleSetting> ruleSetting = hospitalAreaSettingQuery.queryAppointmentRuleSettingById(null, null, hospitalAreaId);
        if (!ruleSetting.isPresent()) {
            throw new BizException(HttpStatus.BAD_REQUEST, "医院区域预约规则不存在");
        }
        Pair<Integer, Integer> advanceAndForbiddenDays = doctorListTool.defaultAdvanceAndForbiddenDays(null, null, ruleSetting.get());
        Integer advanceDay = advanceAndForbiddenDays.getLeft();
        Integer forbiddenDay = advanceAndForbiddenDays.getRight();
        LocalDate start = now.plusDays(forbiddenDay);
        LocalDate end = now.plusDays(advanceDay);
        return Pair.of(start.format(formatter), end.format(formatter));
    }

    private Map<Long, List<ApiScheduleResponseItem>> querySchedule4innerHis(Integer currentPage, Integer pageSize, List<Long> doctorIds, Pair<String, String> startAndEnd) {
        Map<Long, List<ApiScheduleResponseItem>> doctorScheduleMap = new HashMap<>();
        try {
            ApiSchedulePageResponse doctorScheduleResp = scheduleClient.getSchedulesBatch(startAndEnd.getLeft(), startAndEnd.getRight(), doctorIds, currentPage, pageSize);
            doctorScheduleMap = doctorScheduleResp.getList().stream().collect(Collectors.groupingBy(ApiScheduleResponseItem::getDoctorId));
        } catch (Exception e) {
            log.error("获取医生排班信息失败", e);
        }
        return doctorScheduleMap;
    }

    private Map<Long, Hospital> getHospitalAreaMap(List<Doctor> doctorList) {
        Set<String> hospitalAreaIdSet = doctorList.stream()
                .map(h -> String.valueOf(h.getHospitalAreaId()))
                .collect(Collectors.toSet());

        return hospitalAreaQuery.queryBy(new ArrayList<>(hospitalAreaIdSet))
                .stream()
                .filter(h -> h.getStatus() == 0)
                .collect(Collectors.toMap(Hospital::getId, v -> v, (k1, k2) -> k1));
    }
}
