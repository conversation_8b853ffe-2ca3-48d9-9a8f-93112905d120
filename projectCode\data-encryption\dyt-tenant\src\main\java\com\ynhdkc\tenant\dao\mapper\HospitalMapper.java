package com.ynhdkc.tenant.dao.mapper;

import com.ynhdkc.tenant.dao.HospitalAreaQuery;
import com.ynhdkc.tenant.dao.HospitalQuery;
import com.ynhdkc.tenant.entity.Hospital;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/8 15:00:24
 */
@Mapper
public interface HospitalMapper extends BaseMapper<Hospital> {
    List<Hospital> selectHospitalWithCategory(@Param("option") HospitalQuery.HospitalQueryOption option);

    List<Hospital> selectHospitalAreaWithCustomQuery(@Param("option") HospitalAreaQuery.HospitalAreaQueryOption option);
}
