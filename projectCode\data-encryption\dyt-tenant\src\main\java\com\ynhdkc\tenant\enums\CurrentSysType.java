package com.ynhdkc.tenant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/25 14:06
 **/
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum CurrentSysType {
    /**
     * 当前系统类型
     */
    OLD_SYS("老系统", 1),
    NEW_SYS("新系统", 2),
    BOTH_SYS("新老系统共用", 3);
    private String name;
    private int value;
}
