package com.ynhdkc.tenant.link.convert;

import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.service.customer.CustomerDoctorService;
import com.ynhdkc.tenant.util.UrlUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Component
@RequiredArgsConstructor
public class DoctorDetailPageLinkConvert implements ILinkConvert {

    private final CustomerDoctorService customerDoctorService;

    /**
     * 转换后地址：<a href="https://appv3.ynhdkc.com/registration_doctor_detail_new?hos_code=871044&department_id=226733&doctorId=109866&show_time=&hospital_area_id=56&hospital_id=55">示例</a>
     */
    @Override
    public String getConvertLink(String source) {
        final String hosCode = UrlUtil.getHospitalCodeFromUrl(source);
        if (hosCode == null) {
            return Constants.DEFAULT_URL;
        }

        final String depIdKey = "dep_id=";
        final String departmentCode = UrlUtil.extractValue(source, depIdKey);
        if (departmentCode == null) {
            return Constants.DEFAULT_URL;
        }

        final String doctorCodeKey = "id=";
        final String doctorCode = UrlUtil.extractValue(source, doctorCodeKey);
        if (doctorCode == null) {
            return Constants.DEFAULT_URL;
        }

        Optional<Doctor> doctorOptional = customerDoctorService.queryDoctorByCode(hosCode, departmentCode, doctorCode);
        if (!doctorOptional.isPresent()) {
            return Constants.DEFAULT_URL;
        }

        Doctor doctor = doctorOptional.get();
        final String urlPrefix = "https://appv3.ynhdkc.com/registration_doctor_detail_new";
        return urlPrefix + "?hos_code=" + hosCode + "&department_id=" + doctor.getDepartmentId() + "&doctorId=" + doctor.getId() + "&show_time=&hospital_area_id=" + doctor.getHospitalAreaId() + "&hospital_id=" + doctor.getHospitalId();
    }

    @Override
    public boolean isSupport(String source) {
        final List<String> urlPrefixList = Arrays.asList("https://appv2.ynhdkc.com/registration_doctor_detail",
                "https://testapp.ynhdkc.com/registration_doctor_detail",
                "https://ttestapp.ynhdkc.com/registration_doctor_detail");
        return urlPrefixList.stream().anyMatch(source::startsWith);
    }
}
