package backend.common.entity.dto.hisgateway.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

@Data
public class RegFeeListItem {

	/**
	 * 挂号级别编码
	 */
	@JsonProperty("reglevl_code")
	private String reglevlCode;

	/**
	 * 适用范围 null 全院,其余为特 殊科室代码
	 */
	@JsonProperty("dept_code")
	private String deptCode;

	/**
	 * 挂号费
	 */
	@JsonProperty("reg_fee")
	private BigDecimal regFee;

	/**
	 * 检查费
	 */
	@JsonProperty("check_fee")
	private BigDecimal checkFee;

	/**
	 * 诊查费
	 */
	@JsonProperty("diag_fee")
	private BigDecimal diagFee;

	/**
	 * 专家费
	 */
	@JsonProperty("expert_fee")
	private BigDecimal expertFee;

	/**
	 * 附加费
	 */
	@JsonProperty("oth_fee")
	private BigDecimal othFee;

	/**
	 * 检查费记账金额 (备用)
	 */
	@JsonProperty("diag_pub_fee")
	private BigDecimal diagPubfee;

	/**
	 * ID
	 */
	@JsonProperty("id")
	private String id;

	/**
	 * 总金额
	 */
	@JsonProperty("total_fee")
	private BigDecimal totalFee;

	/**
	 * 医保个帐金额
	 */
	@JsonProperty("insure_account_fee")
	private BigDecimal insureAccountFee;

	/**
	 * 医保统筹金额
	 */
	@JsonProperty("insure_overall_fee")
	private BigDecimal insureOverallFee;

	/**
	 * 自费金额
	 */
	@JsonProperty("self_fee")
	private BigDecimal selfFee;

	private Map<String, Object> rawParameters;

}
