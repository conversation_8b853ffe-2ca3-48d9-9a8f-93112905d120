# Debezium 使用

我们使用 Debezium(配合 Kafka Connect)对 MySQL 的进行监控，获取数据变动历史。

当前用途：

- 数据清理。比如设备管理系统删除了一些设备。其他系统需要接收到这些事件，然后对自身相关的数据进行清理。

## 前提条件

- MySQL 启用`binlog`，且 `binlog_format=ROW` `binlog_row_image=FULL`
- MySQL 启用`GTIDs`，且 `gtid_mode=ON` `enforce_gtid_consistency=ON`。[官方启用说明](https://dev.mysql.com/doc/refman/5.7/en/replication-mode-change-online-enable-gtids.html)
- 提供与示例相同权限的 MySQL 用户。示例`GRANT SELECT, RELOAD, SHOW DATABASES, REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO 'user' IDENTIFIED BY 'password';`

> 详情可参考[此文档](https://debezium.io/documentation/reference/stable/connectors/mysql.html#setting-up-mysql)
> 文档中`GTIDs`是可选，但是为了启用 readonly 模式，我们将`GTIDs`改为必选。

## 部署及启动参数

### 准备connector配置文件

创建所需的connector对应的json文件，示例如下

```json
{
    "connector.class": "io.debezium.connector.mysql.MySqlConnector",
    "database.hostname": "**************",
    "database.port": "3306",
    "database.user": "user",
    "database.password": "password",
    "database.server.id": "223344",
    "database.server.name": "iot",
    "table.include.list": "iot_things.t_things,iot_tenant.t_tenant_user",
    "database.history.kafka.bootstrap.servers": "kafka:9092",
    "database.history.kafka.topic": "dbhistory.iot",
    "include.schema.changes": "false",
    "tombstones.on.delete": "false",
    "transforms": "rename",
    "transforms.rename.type":  "io.debezium.transforms.ByLogicalTableRouter",
    "transforms.rename.topic.regex": "^(.*)\\.(.*)\\.(.*)$",
    "transforms.rename.topic.replacement": "$1-$2-$3",
    "read.only":"true",
    "signal.kafka.topic":"iotcdc-signals",
    "signal.kafka.bootstrap.servers":"kafka:9092"
}
```

参数说明

- `connector.class` 连接器类型，固定值
- `database.hostname` `database.port` `database.user` `database.password` 数据库连接信息
- `database.server.id` 数据库 id，与 mysql 的配置中的`server-id`保持一致即可
- `database.server.name` 数据库的逻辑名，保持`iot`即可
- `table.include.list` 需要监控的数据库表，多个使用逗号分隔。格式为`库名.表名`。
- `database.history.kafka.bootstrap.servers` `database.history.kafka.topic` 系统自用的参数，但由于是必填项，所以我们需要配置。`database.history.kafka.bootstrap.servers`填当前的 kafka 配置即可。`database.history.kafka.topic`保持不变。
- `tombstones.on.delete` 当数据删除时，默认会生成两条记录，第一条是带数据的，第二条是 null。使用此参数来避免生成 null
- `transforms.` 开头的几个参数是用来转换 topic 名字的，使生成的 topic 名字更规范。保持不变即可。
- `read.only` `signal.kafka.topic` `signal.kafka.bootstrap.servers` 是用来启用 debezium 的信号通知功能的。此功能用于对运行期间新添加的表做快照。地址使用当前 kafka 地址即可。此功能的启用需要 MySQL 启用`GTIDs`。

### 使用镜像运行程序

使用 Docker 部署的示例命令如下（假设connector的json文件在/your-connectors目录中）

```shell
docker run -d --name debezium-connect -p 8083:8083 \
-e BOOTSTRAP_SERVERS=**************:9092 \
-v /your-connectors:/opt/kafka-connect/connectors \
 landykingdom/debezium:1.7
```

> 使用的镜像为 [landykingdom/debezium:1.7](https://hub.docker.com/r/landykingdom/debezium)
> 该镜像会自动启用debezium并根据/opt/kafka-connect/connectors下的json文件创建connectors


## 查看运行状态

**查看已创建的 connector 的信息**

使用命令`curl http://host:8083/connectors/iot-connector`
结果示例如下

```json
{
  "name": "iot-connector",
  "config": {
    "connector.class": "io.debezium.connector.mysql.MySqlConnector",
    "database.user": "user",
    "signal.kafka.bootstrap.servers": "kafka:9092",
    "database.server.id": "223344",
    "signal.kafka.topic": "iotcdc-signals",
    "database.history.kafka.bootstrap.servers": "kafka:9092",
    "database.history.kafka.topic": "dbhistory.iot",
    "transforms": "rename",
    "database.server.name": "iot",
    "transforms.rename.type": "io.debezium.transforms.ByLogicalTableRouter",
    "read.only": "true",
    "database.port": "3306",
    "include.schema.changes": "false",
    "transforms.rename.topic.replacement": "$1-$2-$3",
    "tombstones.on.delete": "false",
    "transforms.rename.topic.regex": "^(.*)\\.(.*)\\.(.*)$",
    "database.hostname": "**************",
    "database.password": "password",
    "name": "iot-connector",
    "table.include.list": "iot_control.t_apis,iot_control.t_roles",
    "database.include.list": "iot_control"
  },
  "tasks": [{ "connector": "iot-connector", "task": 0 }],
  "type": "source"
}
```

**查看已创建的 connector 的状态**

使用命令`curl http://host:8083/connectors/iot-connector/status`
结果示例如下

```json
{
  "name": "iot-connector",
  "connector": { "state": "RUNNING", "worker_id": "**********:8083" },
  "tasks": [{ "id": 0, "state": "RUNNING", "worker_id": "**********:8083" }],
  "type": "source"
}
```

> 更多REST API 参见[Kafka Connect的文档](https://docs.confluent.io/platform/current/connect/references/restapi.html)
