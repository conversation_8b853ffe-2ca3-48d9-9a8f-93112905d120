package backend.common.entity.dto.hisgateway.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class NotifyHisRequest {

    @JsonProperty("hospital_id")
    private Long hospitalId;


    @JsonProperty("hospital_area_id")
    private Long hospitalAreaId;

    // 医院code
    @JsonProperty("hospital_code")
    private String hospitalCode;

    // his code
    @JsonProperty("department_code")
    private String departmentCode;

    // 通知类型 1, "支付/挂号成功"   2, "退款/退号成功"
    @JsonProperty("notification_type")
    private Integer notificationType;

    // 病历号
    @JsonProperty("medical_record_number")
    private String medicalRecordNumber;

    // 锁号his返回
    @JsonProperty("lock_registration_number")
    private String lockRegistrationNumber;

    // 医院挂号成功的流水号
    @JsonProperty("appointment_serial_number")
    private String appointmentSerialNumber;

    // 微信流水号
    @JsonProperty("trade_serial_number")
    private String tradeSerialNumber;

    // 微信流水号
    @JsonProperty("bank_order_number")
    private String bankOrderNumber;

    // 金额
    private String amt;

    // 滇医通订单号
    @JsonProperty("order_number")
    private String orderNumber;

    @JsonProperty("from_app")
    @JsonIgnoreProperties(ignoreUnknown = true)
    private String fromApp;

}
