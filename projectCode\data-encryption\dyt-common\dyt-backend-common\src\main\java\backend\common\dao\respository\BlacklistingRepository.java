package backend.common.dao.respository;

import backend.common.entity.dto.patient.BlacklistingPatientRequest;
import backend.common.kafka.constant.KafkaProperties;
import backend.common.util.MessageUtil;
import backend.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
public class BlacklistingRepository {

    private final KafkaTemplate<String, String> kafkaTemplate;

    public BlacklistingRepository(KafkaTemplate<String, String> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }

    public void block(Long userId, Long patientId, String reason) {
        if (userId == null && patientId == null) {
            return;
        }
        BlacklistingPatientRequest blacklistingPatientRequest = new BlacklistingPatientRequest();
        blacklistingPatientRequest.setReason(reason);
        blacklistingPatientRequest.setUserId(userId);
        blacklistingPatientRequest.setPatientId(patientId);
        blacklistingPatientRequest.setTimestamp(new Date().getTime());
        blacklistingPatientRequest.setMessageTraceId(StringUtils.getUUID32());

        String message = MessageUtil.object2JSONString(blacklistingPatientRequest);
        if (ObjectUtils.isEmpty(message)) {
            return;
        }
        try {
            kafkaTemplate.send(KafkaProperties.BLACKLISTING_PATIENT_REQUEST, message);
        } catch (Exception e) {
            log.error("send_item_error:", e);
        }
    }
}
