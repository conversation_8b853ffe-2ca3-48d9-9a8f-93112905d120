package backend.common.domain.tenant.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/11/9 9:54
 */
@Getter
public enum DictFileClusterStatus {
    NOT_SYNC(0, "未同步"),
    SYNC(1, "已同步");

    private final Integer code;
    private final String desc;

    DictFileClusterStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DictFileClusterStatus of(Integer code) {
        if (null == code) {
            return null;
        }
        for (DictFileClusterStatus dictFileClusterStatus : DictFileClusterStatus.values()) {
            if (dictFileClusterStatus.getCode().equals(code)) {
                return dictFileClusterStatus;
            }
        }
        return null;
    }
}