package backend.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum ScheduleTimeType {
    // 0未知 1上午 2下午 3中午 4晚上 5前夜 6后夜 7全天 8白天 9昼夜 10夜间 11傍晚
    ZREO("未知", 0),
    ONE("上午", 1),
    TWO("下午", 2),
    THREE("中午", 3),
    FOUR("晚上", 4),
    FIVE("前夜", 5),
    SIX("后夜", 6),
    SEVEN("全天", 7),
    EIGHT("白天", 8),
    NINE("昼夜", 9),
    TEN("夜间", 10),
    ELEVEN("傍晚", 11),
    TWELVE("早上", 12),
    THIRTEEN("早中班", 13),
    FOURTEEN("中班", 14),
    FIFTEEN("白班", 15),
    SIXTEEN("早班", 16),
    SEVENTEEN("夜班", 17),
    EIGHTEEN("专家上午", 18),
    NINETEEN("凌晨", 19),
    TWENTY("晚班1", 20),
    TWENTY_ONE("晚班2", 21),
    TWENTY_TWO("午夜", 22),
    ;

    private final String value;
    private final Integer code;

    ScheduleTimeType(String value, Integer code) {
        this.code = code;
        this.value = value;
    }

    @JsonCreator
    public static ScheduleTimeType getFromCode(Integer code) {
        for (ScheduleTimeType t : ScheduleTimeType.values()) {
            if (t.code.equals(code)) {
                return t;
            }
        }
        return ZREO;
    }

    @JsonCreator
    public static ScheduleTimeType getFromValue(String value) {
        for (ScheduleTimeType t : ScheduleTimeType.values()) {
            if (t.value.equals(value)) {
                return t;
            }
        }
        return ZREO;
    }

    public String getValue() {
        return value;
    }

    public Integer getCode() {
        return code;
    }

    @JsonValue
    public String getCodeString() {
        return code.toString();
    }


}
