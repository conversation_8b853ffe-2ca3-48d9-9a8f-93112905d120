package com.ynhdkc.tenant.entity;

import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/9/14 11:04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "t_hospital_area_position")
public class HospitalAreaPosition extends BaseEntity {
    private Long tenantId;

    private Long hospitalId;

    private Long hospitalAreaId;

    /**
     * 类型，0：科室，9：自定义
     */
    private Integer type;

    private String name;

    private Long departmentId;

    private Long buildingId;

    private String buildingName;

    private Long floorId;

    private String floorName;

    private Long areaId;

    private String areaName;

    private Integer sort;

    private Integer status;
    private String triageDeskAddress;
}

