package backend.common.entity.dto.hisgateway.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum DivisionCode {

	YFBJK("0100", "0", "预防保健科"), QKYLK("0200", "0", "全科医疗科"), NK("0300", "0", "内科"), HXNKZY("0301", "03", "呼吸内科专业"),
	XHNKZY("0302", "03", "消化内科专业"), SJNKZY("0303", "03", "神经内科专业"), XXGNKZY("0304", "03", "心血管内科专业"),
	XYNKZY("0305", "03", "血液内科专业"), SBXZY("0306", "03", "肾病学专业"), NFBZY("0307", "03", "内分泌专业"),
	MYXZY("0308", "03", "免疫学专业"), BTFYZY("0309", "03", "变态反应专业"), LNBZY("0310", "03", "老年病专业"),
	QTNK("0311", "03", "其他内科"), WK("0400", "0", "外科"), PTWK("0401", "04", "普通外科专业"), SJWKZY("0402", "04", "神经外科专业"),
	GKZY("0403", "04", "骨科专业"), BNWKZY("0404", "04", "泌尿外科专业"), XWKZY("0405", "04", "胸外科专业"),
	XZDXGWKZY("0406", "04", "心脏大血管外科专业"), SSKZY("0407", "04", "烧伤科专业"), ZXWKZY("0408", "04", "整形外科专业"),
	QTWK("0409", "04", "其他外科"), FCK("0500", "0", "妇产科"), FKZY("0501", "05", "妇科专业"), CKZY("0502", "05", "产科专业"),
	JHSYZY("0503", "05", "计划生育专业"), YSXZY("0504", "05", "优生学专业"), SZJKYBYZZY("0505", "05", "生殖健康与不孕症专业"),
	QTFCK("0506", "05", "其他妇产科"), FNBJK("0600", "0", "妇女保健科"), QCQBJZY("0601", "06", "青春期保健专业"),
	WCQBJZY("0602", "06", "围产期保健专业"), GNQBJZY("0603", "06", "更年期保健专业"), FNXLWSZY("0604", "06", "妇女心理卫生专业"),
	FNYYZY("0605", "06", "妇女营养专业"), QTFNBJK("0606", "06", "其他妇女保健科"), EK("0700", "0", "儿科"),
	XSEZY("0701", "07", "新生儿专业"), XECRBZY("0702", "07", "小儿传染病专业"), XEXHZY("0703", "07", "小儿消化专业"),
	XEHXZY("0704", "07", "小儿呼吸专业"), XEXZBZY("0705", "07", "小儿心脏病专业"), XESBZY("0706", "07", "小儿肾病专业"),
	XEXYBZY("0707", "07", "小儿血液病专业"), XESJBXZY("0708", "07", "小儿神经病学专业"), XENFBZY("0709", "07", "小儿内分泌专业"),
	XEYCBZY("0710", "07", "小儿遗传病专业"), XEMYZY("0711", "07", "小儿免疫专业"), QTEK("0712", "07", "其他儿科"),
	XEWK("0800", "0", "小儿外科"), XEPTWKZY("0801", "08", "小儿普通外科专业"), XEGKZY("0802", "08", "小儿骨科专业"),
	XEBNWKZY("0803", "08", "小儿泌尿外科专业"), XEXXWKZY("0804", "08", "小儿胸心外科专业"), XESJWKZY("0805", "08", "小儿神经外科专业"),
	QTXEWK("0806", "08", "其他小儿外科"), ETBJK("0900", "0", "儿童保健科"), ETSZFYZY("0901", "09", "儿童生长发育专业"),
	ETYYZY("0902", "09", "儿童营养专业"), ETXLWSZY("0903", "09", "儿童心理卫生专业"), ETWGBJZY("0904", "09", "儿童五官保健专业"),
	ETKFZY("0905", "09", "儿童康复专业"), QTETBJK("0906", "09", "其他儿童保健科"), YK("1000", "0", "眼科"),
	EBYHK("1100", "0", "耳鼻咽喉科"), EKZY("1101", "11", "耳科专业"), BKZY("1102", "11", "鼻科专业"), YHKZY("1103", "11", "咽喉科专业"),
	QTEBYHK("1104", "11", "其他耳鼻咽喉科"), KQK("1200", "0", "口腔科"), KQKZY("1201", "12", "口腔科专业"),
	KQHMWKZY("1202", "12", "口腔颌面外科专业"), ZJZY("1203", "12", "正畸专业"), KQXFZY("1204", "12", "口腔修复专业"),
	KQYFBJZY("1205", "12", "口腔预防保健专业"), QTKQK("1206", "12", "其他口腔科"), PFK("1300", "0", "皮肤科"),
	PFZY("1301", "13", "皮肤专业"), XCBJBZY("1302", "13", "性传播疾病专业"), QTPFK("1303", "13", "其他皮肤科"),
	YLMRK("1400", "0", "医疗美容科"), JSK("1500", "0", "精神科"), JSBZY("1501", "15", "精神病专业"), JSWSZY("1502", "15", "精神卫生专业"),
	YWYLZY("1503", "15", "药物依赖专业"), JSKFZY("1504", "15", "精神康复专业"), SQFZZY("1505", "15", "社区防治专业"),
	LCXLZY("1506", "15", "临床心理专业"), SFJSZY("1507", "15", "司法精神专业"), QTJSK("1508", "15", "其他精神科"),
	CRK("1600", "0", "传染科"), CDCRBZY("1601", "16", "肠道传染病专业"), HXDCRBZY("1602", "16", "呼吸道传染病专业"),
	GYZY("1603", "16", "肝炎专业"), CMCRBZY("1604", "16", "虫媒传染病专业"), DMWXCRBZY("1605", "16", "动物源性传染病专业"),
	RCBZY("1606", "16", "蠕虫病专业"), QTCRZY("1607", "16", "其他传染专业"), JHBZY("1700", "0", "结核病专业"),
	DFBZY("1800", "0", "地方病专业"), ZLK("1900", "0", "肿瘤科"), JZYXK("2000", "0", "急诊医学科"), KFYXK("2100", "0", "康复医学科"),
	YDYXK("2200", "0", "运动医学科"), ZYBK("2300", "0", "职业病科"), ZYZDZY("2301", "23", "职业中毒专业"), CFZY("2302", "23", "尘肺专业"),
	FSBZY("2303", "23", "放射病专业"), WLYSSSZY("2304", "23", "物理因素损伤专业"), ZYJKJHZY("2305", "23", "职业健康监护专业"),
	QTZYBK("2306", "23", "其他职业病科"), LZGHK("2400", "0", "临终关怀科"), TZYXYJSYXK("2500", "0", "特种医学与军事医学科"),
	MZK("2600", "0", "麻醉科"), YXJYK("3000", "0", "医学检验科"), LCTYXYZY("3001", "30", "临床体液、血液专业"),
	LCWSWXZY("3002", "30", "临床微生物学专业"), LCSHJYZY("3003", "30", "临床生化检验专业"), LCMYXQXZY("3004", "30", "临床免疫、血清学专业"),
	QTYXJYK("3005", "30", "其他医学检验科"), BLK("3100", "0", "病理科"), YXYXK("3200", "0", "医学影像科"),
	XXZDZY("3201", "32", "Ｘ线诊断专业"), CTZDZY("3202", "32", "ＣＴ诊断专业"), CGZCXZDZY("3203", "32", "磁共振成像诊断专业"),
	HYXZY("3204", "32", "核医学专业"), CSZDZY("3205", "32", "超声诊断专业"), XDZDZY("3206", "32", "心电诊断专业"),
	NDJNXLTZDZY("3207", "32", "脑电及脑血流图诊断专业"), SJJRDTZY("3208", "32", "神经肌肉电图专业"), JRFSXZY("3209", "32", "介入放射学专业"),
	FSZLZY("3210", "32", "放射治疗专业"), QTYXYXK("3211", "32", "其他医学影像科"), ZYK("5000", "0", "中医科"), ZYNK("5001", "50", "内科"),
	ZYWK("5002", "50", "外科"), ZYFCK("5003", "50", "妇产科"), ZYEK("5004", "50", "儿科"), ZYPFK("5005", "50", "皮肤科"),
	ZYYK("5006", "50", "眼科"), ZYEBYHK("5007", "50", "耳鼻咽喉科"), ZYNKYF("5405", "54", "内科药房"), ZYZLK("5009", "50", "肿瘤科"),
	ZYGSK("5010", "50", "骨伤科"), ZYGCK("5011", "50", "肛肠科"), ZYLNBK("5012", "50", "老年病科"), ZYZJK("5013", "50", "针灸科"),
	ZYTNK("5014", "50", "推拿科"), ZYKFYX("5015", "50", "康复医学"), ZYJZK("5016", "50", "急诊科"),
	ZYYFBJK("5017", "50", "预防保健科"), QTZYK("5018", "50", "其他中医科"), MZYXK("5100", "0", "民族医学科"),
	WWEYX("5101", "51", "维吾尔医学"), ZYX("5102", "51", "藏医学"), MYX("5103", "51", "蒙医学"), YYX("5104", "51", "彝医学"),
	DYX("5105", "51", "傣医学"), QTMZYXK("5106", "51", "其他民族医学科"), ZXYJHK("5200", "0", "中西医结合科"), YKY("5300", "0", "药库"),
	ZCYK("5301", "53", "中成药库"), CYK("5302", "53", "草药库"), XYK("5303", "53", "西药库"), YF("5400", "0", "药房"),
	ZCYF("5401", "54", "中草药房"), YMYF("5402", "54", "疫苗药房"), XCYF("5403", "54", "西成药房"), GHC("5500", "0", "挂号处"),
	SFC("5600", "0", "收费处"), ZSS("5700", "0", "注射室"), ZYC("5800", "0", "住院处"), HSZ("5900", "0", "护士站"),
	ZYYF("5404", "54", "住院药房"), PWKZYB("5901", "59", "普外科住院部"), GRKZYB("5902", "59", "感染科住院部"),
	ZZJHS("5903", "59", "重症监护室"), XEKZYB("5904", "59", "小儿科住院部"), CKZYB("5905", "59", "产科住院部"), YB("9901", "99", "院办"),
	CW("9902", "99", "财务"), QTKS("9900", "0", "其他科室"), CLK("5304", "53", "材料库"), TJK("0101", "01", "体检科"),
	MZSYS("6000", "0", "门诊输液室"), WSJ("9903", "99", "卫生局"), YSBB("9904", "99", "医生报表"), WKYF("5406", "54", "外科药房"),
	BGSYF("5407", "54", "办公室药房"), ECHSZ("5906", "59", "二层护士站"), SCHSZ("5907", "59", "三层护士站"),
	SCHSZS("5908", "59", "四层护士站"), SSSCHSZ("5909", "59", "手术室层护士站"), ZSSHSZ("5910", "59", "注射室护士站"),
	CLYF("5408", "54", "材料药房"), BGHC("5305", "53", "办公耗材"), JHMY("0102", "01", "计划免疫"),
	UNKNOWN("-9999", "-99", "未知的诊疗科室");

	private final String code;

	private final String level;

	private final String name;

	DivisionCode(String code, String level, String name) {
		this.code = code;
		this.level = level;
		this.name = name;
	}

	@JsonCreator
	public static DivisionCode getFromCode(String code) {
		for (DivisionCode t : DivisionCode.values()) {
			if (t.getCode().equals(code)) {
				return t;
			}
		}
		return UNKNOWN;
	}

	public static DivisionCode getFromName(String name) {
		for (DivisionCode t : DivisionCode.values()) {
			if (t.getName().equals(name)) {
				return t;
			}
		}
		return UNKNOWN;
	}

	@JsonValue
	public String getCode() {
		return code;
	}

	public String getLevel() {
		return level;
	}

	public String getName() {
		return name;
	}

}
