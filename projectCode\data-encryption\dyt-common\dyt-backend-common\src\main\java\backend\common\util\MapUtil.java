package backend.common.util;

import java.util.*;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-30 17:54
 */
public class MapUtil {
    private MapUtil() {
    }

    public static <K, V> Map<K, V> convertToMap(List<V> list, Function<V, K> func) {
        Map<K, V> map = new HashMap<>();
        list.forEach(item -> map.put(func.apply(item), item));
        return map;
    }

    public static <K, V> List<V> convertToList(Map<K, V> map) {
        List<V> list = new ArrayList<>();
        map.forEach((k, v) -> list.add(v));
        return list;
    }

    public static List<Object> convertTiList(Map<String, Object> data) {
        return Collections.emptyList();
    }
}
