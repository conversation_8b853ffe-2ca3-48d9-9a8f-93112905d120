package backend.common.util;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-02-15 13:44
 */
public class RedisKeyBuilder {
    public RedisKeyBuilder(String domain, String model) {
        this.builder = new StringBuilder();
        builder.append(domain);
        nextNode(model);
    }

    private final StringBuilder builder;

    public <T extends CharSequence> RedisKeyBuilder nextNode(T resourceKey) {
        appendDivider();
        builder.append(resourceKey);
        return this;
    }

    public <T extends Number> RedisKeyBuilder nextNode(T resourceKey) {
        appendDivider();
        builder.append(resourceKey);
        return this;
    }

    public String build() {
        return this.builder.toString();
    }

    private void appendDivider() {
        this.builder.append(":");
    }
}