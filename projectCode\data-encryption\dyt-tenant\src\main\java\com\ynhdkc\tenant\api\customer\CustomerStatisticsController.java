package com.ynhdkc.tenant.api.customer;

import backend.security.service.BackendClientUserService;
import com.ynhdkc.tenant.handler.CustomerStatisticsApi;
import com.ynhdkc.tenant.model.RecommendStatisticsReqDto;
import com.ynhdkc.tenant.service.customer.CustomerRecommendStatisticsService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/11/1 17:37
 */
@Api(tags = "CustomerStatistics")
@RestController
@RequiredArgsConstructor
public class CustomerStatisticsController implements CustomerStatisticsApi {

    private final CustomerRecommendStatisticsService customerRecommendStatisticsService;
    private final BackendClientUserService backendClientUserService;

    @Override
    public ResponseEntity<Void> recommendIncrement(RecommendStatisticsReqDto request, String authorization) {
        if (null == authorization) {
            customerRecommendStatisticsService.recommendIncrement(request.getRid());
        } else {
            Long userId = backendClientUserService.getCurrentUserIdFromJwt(authorization.replace("Bearer ", ""));
            customerRecommendStatisticsService.recommendIncrement(request, userId);
        }
        return ResponseEntity.ok().build();
    }

}
