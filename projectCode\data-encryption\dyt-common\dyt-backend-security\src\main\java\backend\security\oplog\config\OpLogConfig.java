package backend.security.oplog.config;

import backend.security.method.BackendMethodSecurityConfiguration;
import backend.security.oauth2.SecurityConfiguration;
import backend.security.oplog.component.OpLogComponent;
import backend.security.oplog.dao.repository.OpLogKafkaRepository;
import backend.security.oplog.dto.OpLogMessageBuilder;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.kafka.core.KafkaTemplate;

//@Import({SecurityConfiguration.class, BackendMethodSecurityConfiguration.class})
@Configuration
@ConditionalOnBean(BackendMethodSecurityConfiguration.class)
public class OpLogConfig {

    @Bean
    OpLogKafkaRepository opLogKafkaRepository(KafkaTemplate<String, String> template) {
        OpLogKafkaRepository opLogKafkaRepository = new OpLogKafkaRepository();
        opLogKafkaRepository.setTemplate(template);
        return opLogKafkaRepository;
    }

    @Bean
    OpLogComponent opLogComponentWith(OpLogKafkaRepository opLogKafkaRepository, BackendMethodSecurityConfiguration securityConfiguration) {

        System.out.println("op_log_component_is_created +++++++++++++++");

        return new OpLogComponent(opLogKafkaRepository, securityConfiguration.getSecurityManifest());
    }

    @Bean
    public OpLogMessageBuilder opLogMessageBuilder() {
        return new OpLogMessageBuilder();
    }

}
