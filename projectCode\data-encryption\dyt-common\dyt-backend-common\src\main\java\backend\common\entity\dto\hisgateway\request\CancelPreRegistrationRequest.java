package backend.common.entity.dto.hisgateway.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class CancelPreRegistrationRequest {

    @JsonProperty("lock_registration_number")
    private String lockRegistrationNumber;

    @JsonProperty("appointment_serial_number")
    private String appointmentSerialNumber;

    @JsonProperty("visiting_serial_number")
    private String visitingSerialNumber;

    @JsonProperty("medical_record_number")
    private String medicalRecordNumber;

    @JsonProperty("medical_card_expansion")
    private String medicalCardExpansion;

    @JsonProperty("start_date")
    private Long startDate;

    @JsonProperty("end_date")
    private Long endDate;

    @JsonProperty("order_number")
    private String orderNumber;

    @JsonProperty("hospital_code")
    private String hospitalCode;

    @JsonProperty("department_code")
    private String departmentCode;

    @JsonProperty("doctor_code")
    private String doctorCode;

    @JsonProperty("cancel_order_time")
    private Long cancelOrderTime;

    @JsonProperty("cancel_reason")
    private String cancelReason;

    private Double amt;

    @JsonProperty("schedule_id")
    private String scheduleId;

    @JsonProperty("visiting_date")
    private Long visitingDate;

    @JsonProperty("start_time")
    private Long startTime;

    @JsonProperty("end_time")
    private Long endTime;

    @JsonProperty("start_time_text")
    private String startTimeText;

    @JsonProperty("end_time_text")
    private String endTimeText;

}
