package backend.security.oauth2;

import backend.common.util.RedisKeyBuilder;
import backend.security.constant.BackendSecurityProperties;
import backend.security.constant.JwtChannel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.core.OAuth2TokenValidator;
import org.springframework.security.oauth2.core.OAuth2TokenValidatorResult;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-20 13:56
 */
public class BackendJwtValidator implements OAuth2TokenValidator<Jwt> {
    private final RedisTemplate<String, Object> redisTemplate;
    public BackendJwtValidator(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public OAuth2TokenValidatorResult validate(Jwt jwt) {
        String jwtChannel = jwt.getClaim(BackendSecurityProperties.JWT_CHANNEL);
        if (null == jwtChannel) {
            return OAuth2TokenValidatorResult.failure(new OAuth2Error("401", "Token不合法", ""));
        }
        Long userId = jwt.getClaim(BackendOAuth2Constants.SCOPE_USER);
        if (null == userId) {
            return OAuth2TokenValidatorResult.failure(new OAuth2Error("401", "Token不合法", ""));
        }
        String jwtCancelledKey = getRedisKey(JwtChannel.TENANT_USER.toString().equals(jwtChannel), userId);
        String jwtStr = (String) redisTemplate.opsForValue().get(jwtCancelledKey);
        if (ObjectUtils.isEmpty(jwtStr)) {
            return OAuth2TokenValidatorResult.failure(new OAuth2Error("401", "Token已失效", ""));
        }
        if(jwtStr.equals(jwt.getTokenValue())){
            return OAuth2TokenValidatorResult.success();
        }
        return OAuth2TokenValidatorResult.failure(new OAuth2Error("401", "Token 已失效", ""));
    }

    private static String getRedisKey(boolean isTenantUser, Long userId) {
        RedisKeyBuilder builder;
        if (isTenantUser) {
            builder = new RedisKeyBuilder(BackendSecurityProperties.DOMAIN_PRIVILEGE, BackendSecurityProperties.MODEL_TENANT_USER);
        } else {
            builder = new RedisKeyBuilder(BackendSecurityProperties.DOMAIN_USER, BackendSecurityProperties.MODEL_CLIENT_USER);
        }
        return builder.nextNode(userId)
                .nextNode(BackendSecurityProperties.RESOURCE_JWT)
                .build();
    }
}
