package backend.common.entity.dto.hisgateway.enums;

//午别标识 1：上午 2：下午 3：中午 4：晚上
public enum TimeType {

    MORNING(1, "上午"),
    AFTERNOON(2, "下午"),
    NOON(3, "中午"),
    NIGHT(4, "晚上"),
    UNKNOWN(-1, "未知");

    private final Integer code;

    private final String desc;

    TimeType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
