package com.ynhdkc.tenant.entity.detailpage;

import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @since 2023/5/24 19:43:47
 */
@Table(name = "t_hospital_detail_page_tab")
@Data
@EqualsAndHashCode(callSuper = true)
public class HospitalAreaDetailPageTab extends BaseEntity {
    private Long tenantId;
    private Long hospitalId;
    private Long hospitalAreaId;
    private String hospitalCode;
    private String title;
    private Integer componentType;
    private Long recommendId;
    private Integer sort;
    private String channels;
    private Integer status;
}
