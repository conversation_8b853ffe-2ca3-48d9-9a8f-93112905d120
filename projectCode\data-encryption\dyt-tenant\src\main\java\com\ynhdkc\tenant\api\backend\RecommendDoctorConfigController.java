package com.ynhdkc.tenant.api.backend;


import com.ynhdkc.tenant.handler.RecommendDoctorConfigApi;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.RecommendDoctorConfigService;
import io.swagger.annotations.ApiModel;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@ApiModel(description = "RecommendDoctorConfig")
@RequestMapping("/apis/v1/tenant/backend")
public class RecommendDoctorConfigController implements RecommendDoctorConfigApi {

    private final RecommendDoctorConfigService recommendDoctorConfigService;

    public RecommendDoctorConfigController(RecommendDoctorConfigService recommendDoctorConfigService) {
        this.recommendDoctorConfigService = recommendDoctorConfigService;
    }

    @Override
    public ResponseEntity<RecommendDoctorConfigVo> addRecommendDoctorConfig(@Valid @RequestBody List<RecommendDoctorConfigCreateDto> body) {
        return ResponseEntity.ok(recommendDoctorConfigService.addRecommendDoctorConfig(body));
    }

    @Override
    public ResponseEntity<Void> deleteRecommendDoctorConfig(@PathVariable("id") Long id) {
        recommendDoctorConfigService.deleteRecommendDoctorConfig(id);
        return ResponseEntity.ok().build();
    }

    @Override
    public ResponseEntity<RecommendDoctorConfigVo> getRecommendDoctorConfigById(@PathVariable("id") Long id) {
        return ResponseEntity.ok(recommendDoctorConfigService.getRecommendDoctorConfigById(id));
    }

    @Override
    public ResponseEntity<RecommendDoctorConfigPageVo> getRecommendDoctorConfigsPage(RecommendDoctorConfigQueryDto request) {
        return ResponseEntity.ok(recommendDoctorConfigService.getRecommendDoctorConfigsPage(request));
    }


    @Override
    public ResponseEntity<Void> updateRecommendDoctorConfig(@PathVariable("id") Long id, @Valid @RequestBody RecommendDoctorConfigUpdateDto body) {
        recommendDoctorConfigService.updateRecommendDoctorConfig(id, body);
        return ResponseEntity.ok().build();
    }
}