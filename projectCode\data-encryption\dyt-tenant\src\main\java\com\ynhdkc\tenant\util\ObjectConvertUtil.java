package com.ynhdkc.tenant.util;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/23 11:59:15
 */
public class ObjectConvertUtil {

    public static List<String> toStringList(Object value) {
        List<?> list = (List<?>) value;
        return list.stream().map(String::valueOf).collect(Collectors.toList());
    }

    public static List<Long> toLongList(Object value) {
        List<?> list = (List<?>) value;
        return list.stream().map(q -> (Long) q).collect(Collectors.toList());
    }
}
