package com.ynhdkc.tenant.service.customer.impl;

import backend.common.exception.BizException;
import com.ynhdkc.tenant.dao.HospitalAreaQuery;
import com.ynhdkc.tenant.dao.HospitalAreaSettingQuery;
import com.ynhdkc.tenant.dao.HospitalQuery;
import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import com.ynhdkc.tenant.model.CustomerHospitalAreaPageVo;
import com.ynhdkc.tenant.model.CustomerHospitalAreaVo;
import com.ynhdkc.tenant.service.AddressService;
import com.ynhdkc.tenant.service.customer.CustomerHospitalService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/7/19 13:06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerHospitalServiceImpl implements CustomerHospitalService {
    private final HospitalQuery hospitalQuery;
    private final HospitalAreaQuery hospitalAreaQuery;
    private final AddressService addressService;
    private final HospitalAreaSettingQuery hospitalAreaSettingQuery;

    @Override
    public CustomerHospitalAreaPageVo queryHospitalAreaListByHospitalId(Long hospitalId) {

        List<AppointmentRuleSetting> appointmentRuleSettingList = hospitalAreaSettingQuery.All();
        Hospital hospital = hospitalQuery.queryHospitalById(hospitalId);
        if (hospital == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "医院不存在");
        }

        List<Hospital> hospitalAreaList = hospitalAreaQuery.pageQueryHospitalArea(
                new HospitalAreaQuery.HospitalAreaQueryOption(1, Integer.MAX_VALUE)
                        .setHospitalId(hospitalId)
                        .statusNotIn(3)
        );
        List<CustomerHospitalAreaVo> vos = hospitalAreaList.stream()
                .filter(Hospital::getDisplay)
                .sorted(Comparator.comparing(Hospital::getDisplaySort).reversed())
                .map(hospitalArea -> {
                    CustomerHospitalAreaVo vo = new CustomerHospitalAreaVo();
                    vo.setId(hospitalArea.getId());
                    vo.setHospitalAreaCode(hospitalArea.getHospitalCode());
                    vo.setName(hospitalArea.getName());
                    vo.setHospitalId(hospital.getId());
                    vo.setHospitalName(hospital.getName());
                    vo.setStatus(hospitalArea.getStatus());
                    vo.setDisplay(hospitalArea.getDisplay());
                    vo.setCategory(hospitalArea.getCategories());
                    vo.setAddress(addressService.getAddressVoById(hospitalArea.getAddressId()));
                    AppointmentRuleSetting appointmentRuleSetting = appointmentRuleSettingList.stream().filter(setting -> setting.getHospitalAreaId().equals(hospitalArea.getId())).findFirst().orElse(null);
                    vo.setCurrentSystemType(appointmentRuleSetting == null ? null : appointmentRuleSetting.getCurrentSystemType());
                    return vo;
                })
                .collect(Collectors.toList());

        return new CustomerHospitalAreaPageVo()
                .name(hospital.getName())
                .logo(hospital.getLogo())
                ._list(vos);
    }

    @Override
    public Optional<Hospital> queryHospitalById(String hospitalCode) {
        return hospitalQuery.queryHospitalByCode(hospitalCode);
    }


}
