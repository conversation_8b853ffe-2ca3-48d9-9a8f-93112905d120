package backend.common.entity.dto.hisgateway.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class RegistrationResultCheckResponse {

    // 如果这个是真，就更新订单到 9 如果不是真不做任何处理
    private Boolean result;

    @JsonProperty("order_number")
    private String orderNumber;

    private String reason;

    @JsonProperty("appointment_serial_number")
    private String appointmentSerialNumber;

    @JsonProperty("lock_registration_number")
    private String lockRegistrationNumber;

    @JsonProperty("visiting_address")
    private String visitingAddress;

    @JsonProperty("visiting_serial_number")
    private String visitingSerialNumber;

    @JsonProperty("original_flag")
    private String originalFlag;

    @JsonProperty("reg_flag")
    private String regFlag;

    @JsonProperty("cancel_flag")
    private String cancelFlag;

    @JsonProperty("stop_diagnosis_flag")
    private String stopDiagnosisFlag;
}
