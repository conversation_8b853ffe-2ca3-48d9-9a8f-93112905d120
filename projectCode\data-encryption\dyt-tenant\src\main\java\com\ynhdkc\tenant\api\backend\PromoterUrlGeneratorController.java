package com.ynhdkc.tenant.api.backend;


import backend.common.response.BaseOperationResponse;
import backend.security.oplog.DytSecurityRequired;
import com.ynhdkc.tenant.handler.PromoterUrlGeneratorApi;
import com.ynhdkc.tenant.model.GeneratePromoterUrlReqDto;
import com.ynhdkc.tenant.model.GetPromoterUrlPageReqDto;
import com.ynhdkc.tenant.model.PromoterUrlPageVo;
import com.ynhdkc.tenant.model.PromoterUrlVo;
import com.ynhdkc.tenant.service.backend.PromoterUrlGeneratorService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(tags = "PromoterUrlGenerator")
@RequiredArgsConstructor
public class PromoterUrlGeneratorController implements PromoterUrlGeneratorApi {

    private final PromoterUrlGeneratorService promoterUrlGeneratorService;

    @Override
    @DytSecurityRequired(needOpLog = true, value = "promoter:url:delete")
    public ResponseEntity<BaseOperationResponse> deletePromoterUrl(Long id) {
        return ResponseEntity.ok(promoterUrlGeneratorService.deletePromoterUrl(id));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "promoter:url:generate")
    public ResponseEntity<BaseOperationResponse> generatePromoterUrl(GeneratePromoterUrlReqDto request) {
        return ResponseEntity.ok(promoterUrlGeneratorService.generatePromoterUrl(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "promoter:url:get")
    public ResponseEntity<PromoterUrlVo> getPromoterUrl(Long id) {
        PromoterUrlVo promoterUrlVo = promoterUrlGeneratorService.getBy(id);
        return ResponseEntity.ok(promoterUrlVo);
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "promoter:url:query:page")
    public ResponseEntity<PromoterUrlPageVo> getPromoterUrlPage(GetPromoterUrlPageReqDto request) {
        return ResponseEntity.ok(promoterUrlGeneratorService.getPromoterUrlPage(request));
    }
}
