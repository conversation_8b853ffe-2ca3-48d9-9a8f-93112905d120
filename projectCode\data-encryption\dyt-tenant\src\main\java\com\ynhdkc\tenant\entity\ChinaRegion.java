package com.ynhdkc.tenant.entity;

import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-07 14:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(name = "t_china_region")
public class ChinaRegion extends BaseEntity {
    private Long pid;
    private String shortName;
    private String name;
    private String mergerName;
    private Integer level;
    private String pinyin;
    private String code;
    private String zipCode;
    private String first;
    private String lng;
    private String lat;
    private Integer fid;
    private String spellSimple;
}
