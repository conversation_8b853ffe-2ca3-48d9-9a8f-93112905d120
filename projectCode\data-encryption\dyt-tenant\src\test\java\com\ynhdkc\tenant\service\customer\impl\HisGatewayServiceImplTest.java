package com.ynhdkc.tenant.service.customer.impl;

import com.ynhdkc.tenant.DytTenantApplication;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest(classes = {DytTenantApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("dev")
class HisGatewayServiceImplTest {

    @Autowired
    private HisGatewayServiceImpl hisGatewayService;

    @Test
    void syncInfoByDepartmentId() {
        hisGatewayService.syncInfoByDepartmentId(226504L);
    }


    @Test
    void syncYunDaHospitalDoctorInfo() {
        hisGatewayService.syncYunDaHospitalDoctorInfo();
    }
}