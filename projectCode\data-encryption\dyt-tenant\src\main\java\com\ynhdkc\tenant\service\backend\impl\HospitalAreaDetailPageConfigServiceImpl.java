package com.ynhdkc.tenant.service.backend.impl;

import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ynhdkc.tenant.dao.mapper.IHospitalDetailPageConfigMapper;
import com.ynhdkc.tenant.entity.detailpage.HospitalAreaDetailPageConfig;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.IHospitalAreaDetailPageConfigService;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/5/24 14:33:10
 */
@Service
@RequiredArgsConstructor
public class HospitalAreaDetailPageConfigServiceImpl implements IHospitalAreaDetailPageConfigService {
    private final IHospitalDetailPageConfigMapper hospitalDetailPageConfigMapper;
    private final PageVoConvert pageVoConvert;

    @Override
    public HospitalAreaDetailPageConfigVo createHospitalAreaDetailPageConfig(CreateHospitalAreaDetailPageConfigReqDto dto) {
        validateHospitalAreaDetailPageConfigExistence(dto.getTenantId(), dto.getHospitalId(), dto.getHospitalAreaId());
        HospitalAreaDetailPageConfig hospitalAreaDetailPageConfig = toHospitalAreaDetailPageConfig(dto);
        int effectiveRow = hospitalDetailPageConfigMapper.insertSelective(hospitalAreaDetailPageConfig);
        if (effectiveRow == 0) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "创建医院详情页配置失败");
        }
        return IHospitalAreaDetailPageConfigService.toHospitalAreaDetailPageConfigVo(hospitalAreaDetailPageConfig);
    }

    private void validateHospitalAreaDetailPageConfigExistence(Long tenantId, Long hospitalId, Long hospitalAreaId) {
        HospitalAreaDetailPageConfig hospitalAreaDetailPageConfig = hospitalDetailPageConfigMapper.selectOneByExample2(HospitalAreaDetailPageConfig.class, sql -> sql.andEqualTo(HospitalAreaDetailPageConfig::getTenantId, tenantId)
                .andEqualTo(HospitalAreaDetailPageConfig::getHospitalId, hospitalId)
                .andEqualTo(HospitalAreaDetailPageConfig::getHospitalAreaId, hospitalAreaId));
        if (hospitalAreaDetailPageConfig != null) {
            throw new BizException(HttpStatus.CONFLICT, "医院详情页配置已存在");
        }
    }

    @Override
    public BaseOperationResponse deleteHospitalAreaDetailPageConfig(Long id) {
        int effectiveRow = hospitalDetailPageConfigMapper.deleteByPrimaryKey(id);
        if (effectiveRow == 0) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "删除医院详情页配置失败");
        }
        return BaseOperationResponse.builder().effectiveCount(effectiveRow).build();
    }

    @Override
    public HospitalAreaDetailPageConfigVo getHospitalAreaDetailPageConfig(Long id) {
        HospitalAreaDetailPageConfig hospitalAreaDetailPageConfig = hospitalDetailPageConfigMapper.selectByPrimaryKey(id);
        if (hospitalAreaDetailPageConfig == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "医院详情页配置不存在");
        }
        return IHospitalAreaDetailPageConfigService.toHospitalAreaDetailPageConfigVo(hospitalAreaDetailPageConfig);
    }

    @Override
    public HospitalAreaDetailPageConfigPageVo searchHospitalAreaDetailPageConfig(SearchHospitalAreaDetailPageConfigReqDto dto) {
        Page<HospitalAreaDetailPageConfig> page = PageHelper.startPage(dto.getCurrentPage(), dto.getPageSize());
        page.doSelectPage(() -> hospitalDetailPageConfigMapper.selectByExample2(HospitalAreaDetailPageConfig.class, sql -> {
            if (dto.getTenantId() != null) {
                sql.andEqualTo(HospitalAreaDetailPageConfig::getTenantId, dto.getTenantId());
            }
            if (dto.getHospitalId() != null) {
                sql.andEqualTo(HospitalAreaDetailPageConfig::getHospitalId, dto.getHospitalId());
            }
            if (dto.getHospitalAreaId() != null) {
                sql.andEqualTo(HospitalAreaDetailPageConfig::getHospitalAreaId, dto.getHospitalAreaId());
            }
        }));
        return pageVoConvert.toPageVo(page, HospitalAreaDetailPageConfigPageVo.class, IHospitalAreaDetailPageConfigService::toHospitalAreaDetailPageConfigVo);
    }

    @Override
    public HospitalAreaDetailPageConfigVo updateHospitalAreaDetailPageConfig(Long id, UpdateHospitalAreaDetailPageConfigReqDto dto) {
        HospitalAreaDetailPageConfig hospitalAreaDetailPageConfig = hospitalDetailPageConfigMapper.selectByPrimaryKey(id);
        if (hospitalAreaDetailPageConfig == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "更新医院详情页配置失败");
        }

        toHospitalAreaDetailPageConfig(dto, hospitalAreaDetailPageConfig);
        int effectiveRow = hospitalDetailPageConfigMapper.updateByPrimaryKeySelective(hospitalAreaDetailPageConfig);
        if (effectiveRow == 0) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "更新医院详情页配置失败");
        }
        return IHospitalAreaDetailPageConfigService.toHospitalAreaDetailPageConfigVo(hospitalAreaDetailPageConfig);
    }

    @Override
    public void setHospitalAreaDetailPageConfig(HospitalAreaLayoutVo vo, Long hospitalAreaId) {
        HospitalAreaDetailPageConfigVo hospitalDetailPageConfigVo = getHospitalDetailPageConfigByHospitalAreaId(hospitalAreaId);
        if (hospitalDetailPageConfigVo == null) {
            return;
        }
        vo.setPageConfig(hospitalDetailPageConfigVo);
    }

    private @Nullable HospitalAreaDetailPageConfigVo getHospitalDetailPageConfigByHospitalAreaId(Long hospitalAreaId) {
        HospitalAreaDetailPageConfig hospitalAreaDetailPageConfig = hospitalDetailPageConfigMapper.selectOneByExample2(HospitalAreaDetailPageConfig.class,
                sql -> sql.andEqualTo(HospitalAreaDetailPageConfig::getHospitalAreaId, hospitalAreaId));
        if (hospitalAreaDetailPageConfig != null) {
            return IHospitalAreaDetailPageConfigService.toHospitalAreaDetailPageConfigVo(hospitalAreaDetailPageConfig);
        }
        return null;
    }
}
