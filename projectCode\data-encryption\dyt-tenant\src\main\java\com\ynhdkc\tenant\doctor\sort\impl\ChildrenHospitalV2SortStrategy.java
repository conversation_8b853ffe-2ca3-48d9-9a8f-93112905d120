package com.ynhdkc.tenant.doctor.sort.impl;

import com.ynhdkc.tenant.doctor.sort.strategy.AbstractDoctorSortStrategy;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.model.CustomerGroupedDoctorDetailVo;
import com.ynhdkc.tenant.tool.DoctorSortUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @Author: zhang<PERSON>ngyun
 * @Date: 2025/5/27 10:45
 * @Description: 
 **/
@Component
public class ChildrenHospitalV2SortStrategy extends AbstractDoctorSortStrategy {

    private static final Set<String> HOSPITAL_CODES = Collections.unmodifiableSet(new HashSet<>(Arrays.asList("871002", "871003")));

    public ChildrenHospitalV2SortStrategy() {
        super(HOSPITAL_CODES);
    }

    @Override
    public void sort(List<Doctor> doctors) {
        DoctorSortUtils.levelDescAndNameAsc(doctors);
    }

    @Override
    public void sortGroupVo(List<CustomerGroupedDoctorDetailVo> doctorGroupVos) {
        DoctorSortUtils.levelDescAndNameAscGroupDoc(doctorGroupVos);
    }
}