package backend.common.entity.dto.hisgateway.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * GB/T 3304-1991 中国各民族名称的罗马字母拼写法和代码
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum Nation {

    HA("汉族", "Han", "Han", "HA", "01"), MG("蒙古族", "Mongol", "Mongol", "MG", "02"), HU("回族", "Hui", "Hui", "HU", "03"),
    ZA("藏族", "<PERSON>ang", "Tibetan", "ZA", "04"), UG("维吾尔族", "Uygur", "Uygur", "UG", "05"),
    MH("苗族", "Miao", "Miao", "MH", "06"), YI("彝族", "<PERSON>", "<PERSON>", "Y<PERSON>", "07"), ZH("壮族", "<PERSON><PERSON>", "<PERSON><PERSON>", "ZH", "08"),
    BY("布依族", "<PERSON>ei", "Buyei", "BY", "09"), CS("朝鲜族", "Chosen", "Korean", "CS", "10"),
    MA("满族", "<PERSON>", "Man", "MA", "11"), <PERSON><PERSON>("侗族", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "12"), Y<PERSON>("瑶族", "<PERSON>", "<PERSON>", "Y<PERSON>", "13"),
    <PERSON>("白族", "<PERSON>", "<PERSON>", "<PERSON>", "14"), <PERSON>J("土家族", "<PERSON><PERSON>a", "Tujia", "TJ", "15"), HN("哈尼族", "Hani", "Hani", "HN", "16"),
    KZ("哈萨克族", "Kazak", "Kazak", "KZ", "17"), DA("傣族", "Dai", "Dai", "DA", "18"), LI("黎族", "Li", "Li", "LI", "19"),
    LS("傈僳族", "Lisu", "Lisu", "LS", "20"), VA("佤族", "Va", "Va", "VA", "21"), SH("畲族", "She", "She", "SH", "22"),
    GS("高山族", "Gaoshan", "Gaoshan", "GS", "23"), LH("拉祜族", "Lahu", "Lahu", "LH", "24"),
    SU("水族", "Sui", "Sui", "SU", "25"), DX("东乡族", "Dongxiang", "Dongxiang", "DX", "26"),
    NX("纳西族", "Naxi", "Naxi", "NX", "27"), JP("景颇族", "Jingpo", "Jingpo", "JP", "28"),
    KG("柯尔克孜族", "Kirgiz", "Kirgiz", "KG", "29"), TU("土族", "Tu", "Tu", "TU", "30"),
    DU("达斡尔族", "Daur", "Daur", "DU", "31"), ML("仫佬族", "Mulao", "Mulao", "ML", "32"),
    QI("羌族", "Qiang", "Qiang", "QI", "33"), BL("布朗族", "Blang", "Blang", "BL", "34"),
    SL("撒拉族", "Salar", "Salar", "SL", "35"), MN("毛南族", "Maonan", "Maonan", "MN", "36"),
    GL("仡佬族", "Gelao", "Gelao", "GL", "37"), XB("锡伯族", "Xibe", "Xibe", "XB", "38"),
    AC("阿昌族", "Achang", "Achang", "AC", "39"), PM("普米族", "Pumi", "Pumi", "PM", "40"),
    TA("塔吉克族", "Tajik", "Tajik", "TA", "41"), NU("怒族", "Nu", "Nu", "NU", "42"),
    UZ("乌孜别克族", "Uzbek", "Uzbek", "UZ", "43"), RS("俄罗斯族", "Russ", "Russ", "RS", "44"),
    EW("鄂温克族", "Ewenki", "Ewenki", "EW", "45"), DE("德昂族", "Deang", "Deang", "DE", "46"),
    BN("保安族", "Bonan", "Bonan", "BN", "47"), YG("裕固族", "Yugur", "Yugur", "YG", "48"),
    GI("京族", "Gin", "Gin", "GI", "49"), TT("塔塔尔族", "Tatar", "Tatar", "TT", "50"),
    DR("独龙族", "Derung", "Derung", "DR", "51"), OR("鄂伦春族", "Oroqen", "Oroqen", "OR", "52"),
    HZ("赫哲族", "Hezhen", "Hezhen", "HZ", "53"), MB("门巴族", "Monba", "Monba", "MB", "54"),
    LB("珞巴族", "Lhoba", "Lhoba", "LB", "55"), JN("基诺族", "Jino", "Jino", "JN", "56"),
    CQR("穿青人", "ChuanQingRen", "ChuanQingRen", "CQR", "57"), UNKNOW("其它民族或外国人", "unknow", "unknow", "UNKNOW", "99");

    /**
     * 名称
     */
    private final String value;

    /**
     * 内部使用罗马拼音
     */
    private final String insideRome;

    /**
     * 外部使用罗马拼音
     */
    private final String outsideRome;

    /**
     * 字母代码
     */
    private final String code;

    /**
     * 数字代码
     */
    private final String number;

    Nation(String value, String insideRome, String outsideRome, String code, String number) {
        this.value = value;
        this.insideRome = insideRome;
        this.outsideRome = outsideRome;
        this.code = code;
        this.number = number;
    }

    @JsonCreator
    public static Nation getFromCode(String code) {
        for (Nation t : Nation.values()) {
            if (t.getCode().equals(code)) {
                return t;
            }
        }
        throw new IllegalArgumentException("民族编码不存在");
    }

    public static Nation getFromNumber(String number) {
        for (Nation t : Nation.values()) {
            if (t.getNumber().equals(number)) {
                return t;
            }
        }
        throw new IllegalArgumentException("民族编码不存在");
    }

    public static Nation getFromValue(String value) {
        for (Nation t : Nation.values()) {
            if (t.getValue().equals(value)) {
                return t;
            }
        }
        throw new IllegalArgumentException("民族编码不存在");
    }

    public String getValue() {
        return value;
    }

    public String getCode() {
        return code;
    }

    public String getInsideRome() {
        return insideRome;
    }

    public String getOutsideRome() {
        return outsideRome;
    }

    public String getNumber() {
        return number;
    }

}
