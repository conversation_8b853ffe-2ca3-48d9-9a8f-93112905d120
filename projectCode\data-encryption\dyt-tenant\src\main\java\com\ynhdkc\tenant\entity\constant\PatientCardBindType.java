package com.ynhdkc.tenant.entity.constant;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-10 10:03
 */
public enum PatientCardBindType {
    /**
     * 自动绑卡
     */
    AUTO(0),
    /**
     * 手动填写自动开卡
     */
    MANUAL_INPUT_AUTO_BIND(1),
    /**
     * 手工录入
     */
    MANUAL_INPUT_MANUAL_BIND(2);
    private final int value;

    PatientCardBindType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static PatientCardBindType of(Integer input) {
        if (null == input) {
            return null;
        }
        for (PatientCardBindType value : PatientCardBindType.values()) {
            if (value.getValue() == input) {
                return value;
            }
        }
        return null;
    }

    public static boolean contains(Integer input) {
        if (null == input) {
            return false;
        }
        for (PatientCardBindType value : PatientCardBindType.values()) {
            if (value.getValue() == input) {
                return true;
            }
        }
        return false;
    }
}
