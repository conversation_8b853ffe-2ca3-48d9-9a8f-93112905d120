package com.ynhdkc.tenant.entity.setting;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-09 10:05
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(name = "t_hospital_patient_report_setting")
public class PatientReportSetting extends BaseSetting {
    /**
     * 支持的报告类型，0：全部，1：检验报告，2：检查报告
     */
    private Integer supportReportType;
    /**
     * 支持的按钮刻度，0：无限制，1：30天内，2：90天内，3：180天内，3：一年内
     */
    private Integer supportSearchDateRange;
    /**
     * 支持的查询时间，0：全部，1：最近一周，2：最近一月，3：最近三月，4：最近半年，5：最近一年
     */
    private String supportSearchTime;
    /**
     * 提示消息
     */
    private String notice;
}
