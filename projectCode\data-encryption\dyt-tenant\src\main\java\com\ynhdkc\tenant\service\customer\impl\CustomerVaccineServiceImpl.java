package com.ynhdkc.tenant.service.customer.impl;

import backend.common.util.MybatisUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ynhdkc.tenant.client.ScheduleClient;
import com.ynhdkc.tenant.client.model.ApiSchedulePageResponse;
import com.ynhdkc.tenant.client.model.ApiScheduleResponseItem;
import com.ynhdkc.tenant.dao.mapper.*;
import com.ynhdkc.tenant.entity.*;
import com.ynhdkc.tenant.entity.constant.VaccineConstant;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.customer.CustomerVaccineService;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import com.ynhdkc.tenant.util.CommonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerVaccineServiceImpl implements CustomerVaccineService {

    private final VaccineCategoryMapper vaccineCategoryMapper;
    private final PageVoConvert pageVoConvert;
    private final VaccineMapper vaccineMapper;
    private final HospitalMapper hospitalMapper;
    private final AddressMapper addressMapper;
    private final DepartmentMapper departmentMapper;
    private final DoctorMapper doctorMapper;
    private final ScheduleClient scheduleClient;

    @Override
    public CustomerVaccineCategoryPageVo getVaccineCategoryPage(CustomerVaccineCategoryPageReqDto request) {
        Page<VaccineCategory> page = PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        page.doSelectPage(() -> vaccineCategoryMapper.selectByExample(VaccineCategory.class, helper -> {
            helper.defGroup(sql -> {
                sql.andEqualTo(VaccineCategory::getStatus, VaccineConstant.Status.NORMAL.getCode());
                if (request.getName() != null) {
                    sql.andLike(VaccineCategory::getName, MybatisUtil.likeBoth(request.getName()));
                }
            });
            helper.builder(sql -> sql.orderByAsc(VaccineCategory::getSort));
        }));
        return pageVoConvert.toPageVo(page, CustomerVaccineCategoryPageVo.class, CustomerVaccineService::toCustomerVaccineCategoryVo);
    }

    @Override
    public List<CustomerVaccineOrganizationVo> getVaccineOrganizationList(CustomerVaccineOrganizationListReqDto request) {
        Consumer<WeekendSqls<Hospital>> condition = sql -> sql.andEqualTo(Hospital::getHospitalTypeTag, 1)
                .andLike(Hospital::getCategory, MybatisUtil.likeBoth("1"))
                .andNotEqualTo(Hospital::getStatus, 3);
        List<Long> hospitalAreaIds = new ArrayList<>();
        if (request.getVaccineCategoryId() != null) {
            List<Vaccine> vaccines = vaccineMapper.selectByExample2(Vaccine.class,
                    sql -> sql.andEqualTo(Vaccine::getVaccineCategoryId, request.getVaccineCategoryId()));
            if (CollectionUtils.isEmpty(vaccines)) {
                return new ArrayList<>();
            } else {
                vaccines.forEach(vaccine -> hospitalAreaIds.add(vaccine.getHospitalAreaId()));
            }
        }
        if (!CollectionUtils.isEmpty(hospitalAreaIds)) {
            condition = condition.andThen(sql -> sql.andIn(Hospital::getId, hospitalAreaIds));
        }
        if (!ObjectUtils.isEmpty(request.getName())) {
            condition = condition.andThen(sql -> sql.andLike(Hospital::getName, MybatisUtil.likeBoth(request.getName())));
        }
        List<Address> addresses = addressMapper.selectByExample2(Address.class,
                sql -> sql.andEqualTo(Address::getProvince, "云南省")
                        .andEqualTo(Address::getCity, "昆明市"));
        if (!ObjectUtils.isEmpty(request.getCounty())) {
            addresses = addresses.stream().filter(address -> address.getCounty().equals(request.getCounty())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(addresses)) {
                return new ArrayList<>();

            }
            List<Long> addressIds = addresses.stream().map(Address::getId).collect(Collectors.toList());
            condition = condition.andThen(sql -> sql.andIn(Hospital::getAddressId, addressIds));
        }
        List<Hospital> hospitals = hospitalMapper.selectByExample2(Hospital.class, condition);
        List<CustomerVaccineOrganizationVo> result = hospitals.stream()
                .map(CustomerVaccineService::hospitalToVaccineOrganizationVo)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(addresses)) {
            Map<Long, Address> addressMap = addresses.stream().collect(Collectors.toMap(Address::getId, add -> add));
            result = result.stream().peek(org -> {
                Address add = addressMap.get(org.getAddressId());
                if (add != null) {
                    org.setAddress(add.getDetail());
                    Double distance = CommonUtil.getDistance(request.getLongitude(), request.getLatitude(), add.getLongitude(), add.getLatitude());
                    if (distance != null) {
                        org.setDistance(distance);
                    }
                }
            }).collect(Collectors.toList());
        }
        if (request.getSort() == VaccineConstant.Sort.DISTANCE.getCode()) {
            Comparator<CustomerVaccineOrganizationVo> distanceComparator = Comparator.nullsLast(Comparator
                    .comparing(o -> o.getDistance() != null ? o.getDistance() : Double.POSITIVE_INFINITY));
            result.sort(distanceComparator);
        }

        return result;
    }

    @Override
    public CustomerVaccineSearchRespVo vaccineSearch(String keyword, Double longitude, Double latitude) {
        CustomerVaccineOrganizationListReqDto orgReq = new CustomerVaccineOrganizationListReqDto();
        orgReq.setName(keyword);
        orgReq.setLongitude(longitude);
        orgReq.setLatitude(latitude);
        List<CustomerVaccineOrganizationVo> organizationList = getVaccineOrganizationList(orgReq);
        CustomerVaccineListReqDto vaccineReq = new CustomerVaccineListReqDto();
        vaccineReq.setName(keyword);
        vaccineReq.setLongitude(longitude);
        vaccineReq.setLatitude(latitude);
        List<CustomerVaccineVo> vaccineList = getVaccineList(vaccineReq);
        CustomerVaccineSearchRespVo orgResp = new CustomerVaccineSearchRespVo();
        orgResp.setVaccineOrganizationList(organizationList);
        List<CustomerVaccineVo> vaccineListStocked = vaccineList.stream()
                .filter(vaccine -> !CollectionUtils.isEmpty(vaccine.getScheduleList()))
                .collect(Collectors.toList());
        orgResp.setVaccineListStocked(vaccineListStocked);
        vaccineList.removeAll(vaccineListStocked);
        orgResp.setVaccineListNoStocked(vaccineList);
        return orgResp;
    }

    @Override
    public List<CustomerVaccineVo> getVaccineList(CustomerVaccineListReqDto request) {
        List<Long> categoryIds = new ArrayList<>();
        Consumer<WeekendSqls<Doctor>> doctorQuery = sql -> sql.andEqualTo(Doctor::getStatus, VaccineConstant.Status.NORMAL.getCode());
        if (!ObjectUtils.isEmpty(request.getName())) {
            doctorQuery = doctorQuery.andThen(sql -> sql.andLike(Doctor::getName, MybatisUtil.likeBoth(request.getName())));
            List<VaccineCategory> vaccineCategories = vaccineCategoryMapper.selectByExample2(VaccineCategory.class,
                    sql -> sql.andLike(VaccineCategory::getName, MybatisUtil.likeBoth(request.getName())));
            if (!CollectionUtils.isEmpty(vaccineCategories)) {
                categoryIds = vaccineCategories.stream().map(VaccineCategory::getId).collect(Collectors.toList());
            }
        }
        List<Hospital> hospitals = hospitalMapper.selectByExample2(Hospital.class,
                sql -> sql.andEqualTo(Hospital::getHospitalTypeTag, 1)
                        .andLike(Hospital::getCategory, MybatisUtil.likeBoth("1"))
                        .andEqualTo(Hospital::getStatus, 1));
        List<Department> departments = departmentMapper.selectByExample2(Department.class, sql ->
                sql.andEqualTo(Department::getEnabled, 1));
        List<Doctor> doctors = doctorMapper.selectByExample2(Doctor.class, doctorQuery);

        if (CollectionUtils.isEmpty(hospitals) || CollectionUtils.isEmpty(departments) || CollectionUtils.isEmpty(doctors)) {
            return new ArrayList<>();
        }
        List<Long> hospitalIds = hospitals.stream().map(Hospital::getId).collect(Collectors.toList());
        List<Long> departmentIds = departments.stream().map(Department::getId).collect(Collectors.toList());
        List<Long> doctorIds = doctors.stream().map(Doctor::getId).collect(Collectors.toList());
        List<Long> finalCategoryIds = categoryIds;
        List<Vaccine> vaccines = vaccineMapper.selectByExample(Vaccine.class, help -> {
            help.defGroup(sql -> {
                if (request.getVaccineCategoryId() != null) {
                    sql.andEqualTo(Vaccine::getVaccineCategoryId, request.getVaccineCategoryId());
                }
                if (request.getHospitalAreaId() != null) {
                    sql.andEqualTo(Vaccine::getHospitalAreaId, request.getHospitalAreaId());
                }
            });
            help.newAndGroup(sql -> {
                if (!CollectionUtils.isEmpty(finalCategoryIds) || !CollectionUtils.isEmpty(doctorIds)) {
                    if (!CollectionUtils.isEmpty(finalCategoryIds)) {
                        sql.orIn(Vaccine::getVaccineCategoryId, finalCategoryIds);
                    }
                    if (!CollectionUtils.isEmpty(doctorIds)) {
                        sql.orIn(Vaccine::getDoctorId, doctorIds);
                    }
                }
            });
        });

        List<CustomerVaccineVo> vos = vaccines
                .stream()
                .filter(vaccine -> hospitalIds.contains(vaccine.getHospitalAreaId())
                        && departmentIds.contains(vaccine.getDepartmentId())
                        && doctorIds.contains(vaccine.getDoctorId()))
                .map(CustomerVaccineService::toCustomerVaccineVo).collect(Collectors.toList());

        return buildCustomerVaccineVo(vos, request, hospitals, departments, doctors);
    }


    private List<CustomerVaccineVo> buildCustomerVaccineVo(List<CustomerVaccineVo> vos,
                                                           CustomerVaccineListReqDto request,
                                                           List<Hospital> hospitals,
                                                           List<Department> departments,
                                                           List<Doctor> doctors) {
        if (CollectionUtils.isEmpty(vos)) {
            return new ArrayList<>();
        }

        Map<Long, Address> addressMap = new HashMap<>();
        Map<Long, Department> departmentMap = departments.stream()
                .collect(Collectors.toMap(Department::getId, department -> department));
        Map<Long, Doctor> doctorMap = doctors.stream()
                .collect(Collectors.toMap(Doctor::getId, doctor -> doctor));
        List<Long> doctorIds = doctors.stream()
                .sorted(Comparator.comparing(Doctor::getSort)).map(Doctor::getId).collect(Collectors.toList());
        List<Long> hospitalIds = hospitals.stream()
                .sorted(Comparator.comparing(Hospital::getDisplaySort))
                .map(Hospital::getId)
                .collect(Collectors.toList());
        Map<Long, Hospital> hospitalMap = hospitals.stream().collect(Collectors.toMap(Hospital::getId, hospital -> hospital));

        List<Long> addressIds = hospitals.stream().map(Hospital::getAddressId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(addressIds)) {
            List<Address> addresses = addressMapper.selectByIds(StrUtil.join(",", addressIds));
            addressMap = addresses.stream().collect(Collectors.toMap(Address::getId, address -> address));
        }

        List<Long> categoryIds = vos.stream().map(CustomerVaccineVo::getCategoryId).collect(Collectors.toList());
        List<VaccineCategory> categories = vaccineCategoryMapper.selectByExample2(VaccineCategory.class,
                sql -> sql.andIn(VaccineCategory::getId, categoryIds)
                        .andEqualTo(VaccineCategory::getStatus, VaccineConstant.Status.NORMAL.getCode()));
        Map<Long, VaccineCategory> categoryMap = categories.stream().collect(Collectors.toMap(VaccineCategory::getId, category -> category));

        Map<Long, List<ApiScheduleResponseItem>> doctorScheduleMap = new HashMap<>();
        try {
            Date today = DateUtil.parse(DateUtil.today(), "yyyy-MM-dd");
            Date after7day = DateUtil.offsetDay(today, 7);
            ApiSchedulePageResponse doctorScheduleResp = scheduleClient.getSchedulesBatch(DateUtil.format(today, "yyyy/MM/dd"), DateUtil.format(after7day, "yyyy/MM/dd"), doctorIds,1,  Integer.MAX_VALUE);
            doctorScheduleMap = doctorScheduleResp.getList().stream().collect(Collectors.groupingBy(ApiScheduleResponseItem::getDoctorId));
        } catch (Exception e) {
            log.error("获取医生排班信息失败", e);
        }

        for (CustomerVaccineVo vo : vos) {
            Hospital hospital = hospitalMap.get(vo.getHospitalAreaId());
            if (hospital != null) {
                vo.setHospitalAreaName(hospital.getName());
                Address address = addressMap.get(hospital.getAddressId());
                if (address != null) {
                    vo.setAddress(address.getDetail());
                    Double distance = CommonUtil.getDistance(request.getLongitude(), request.getLatitude(), address.getLongitude(), address.getLatitude());
                    vo.setDistance(distance);
                }
            }

            Department department = departmentMap.get(vo.getDepartmentId());
            if (department != null) {
                vo.setDepartmentName(department.getName());
            }
            Doctor doctor = doctorMap.get(vo.getDoctorId());
            if (doctor != null) {
                vo.setDoctorName(doctor.getName());
            }
            VaccineCategory category = categoryMap.get(vo.getCategoryId());
            if (category != null) {
                vo.setCategoryName(category.getName());
            }

            List<ApiScheduleResponseItem> scheduleList = doctorScheduleMap.get(vo.getDoctorId());

            if (!CollectionUtils.isEmpty(scheduleList)) {
                List<String> scheduleStrList = scheduleList
                        .stream()
                        .map(ApiScheduleResponseItem::getSchDate)
                        .map(date -> new SimpleDateFormat("MM-dd").format(date))
                        .collect(Collectors.toList());
                vo.setScheduleList(scheduleStrList);
                if (scheduleList.stream().anyMatch(item -> item.getShowSchDate() == 1)) {
                    vo.setAppointmentType(VaccineConstant.AppointmentType.APPOINTMENT.getCode());
                } else if (scheduleList.stream().anyMatch(item -> item.getShowSchDate() == 0)) {
                    vo.setAppointmentType(VaccineConstant.AppointmentType.REGISTER.getCode());
                } else {
                    vo.setAppointmentType(VaccineConstant.AppointmentType.NO_STOCK.getCode());
                }
            } else {
                vo.setAppointmentType(VaccineConstant.AppointmentType.NO_STOCK.getCode());
            }

        }
        vos = vos.stream()
                .sorted(Comparator.comparing(CustomerVaccineVo::getDistance, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(CustomerVaccineVo::getSort, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(vo -> {
                            int index = hospitalIds.indexOf(vo.getHospitalAreaId());
                            return index == -1 ? Integer.MAX_VALUE : index;
                        })
                        .thenComparing(vo -> {
                            int index = doctorIds.indexOf(vo.getDoctorId());
                            return index == -1 ? Integer.MAX_VALUE : index;
                        })
                        .thenComparing(vo -> {
                            if (CollectionUtils.isEmpty(vo.getScheduleList())) {
                                return 1;
                            }
                            return 0;
                        }))
                .collect(Collectors.toList());
        return vos;
    }
}
