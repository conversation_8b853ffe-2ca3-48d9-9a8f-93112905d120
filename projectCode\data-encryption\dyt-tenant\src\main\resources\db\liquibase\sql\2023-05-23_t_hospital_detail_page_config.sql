CREATE TABLE t_hospital_detail_page_config
(
    id                           BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    tenant_id                    BIGINT      NOT NULL COMMENT '租户id',
    hospital_id                  BIGINT      NOT NULL COMMENT '医院id',
    hospital_area_id             bigint      not null comment '院区id',
    hospital_code                varchar(20) null comment '医院编码',
    display_hospital_area        TINYINT(1)  NOT NULL DEFAULT 0 COMMENT '是否展示医院区域',
    display_address              TINYINT(1)  NOT NULL DEFAULT 0 COMMENT '是否展示医院地址',
    display_level                TINYINT(1)  NOT NULL DEFAULT 0 COMMENT '是否展示医院等级',
    display_open_scheduling_time TINYINT(1)  NOT NULL DEFAULT 0 COMMENT '是否展示开放预约时间',
    display_appoint_notice       TINYINT(1)  NOT NULL DEFAULT 0 COMMENT '是否展示预约须知',
    display_notice               TINYINT(1)  NOT NULL DEFAULT 0 COMMENT '是否展示公告',
    display_floor_distribution   TINYINT(1)  NOT NULL DEFAULT 0 COMMENT '是否展示楼层分布',
    display_tab                  TINYINT(1)  NOT NULL DEFAULT 0 COMMENT '是否展示tab',
    create_time                  DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time                  DATETIME    NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uidx_tenant_id_hospital_id_hospital_area_id (tenant_id, hospital_id, hospital_area_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='医院详情页配置表';

insert t_hospital_detail_page_config (id, tenant_id, hospital_id, hospital_area_id)
values (1, 1, 1, 2),
       (2, 1, 1, 3),
       (3, 1, 4, 5),
       (4, 1, 4, 6),
       (5, 1, 4, 7);
