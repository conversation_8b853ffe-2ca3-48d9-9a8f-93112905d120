package com.ynhdkc.tenant.client;

import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.client.model.RoleBoundUsersQueryReqDto;
import com.ynhdkc.tenant.client.model.RpcRoleBoundUsersQueryVo;
import com.ynhdkc.tenant.model.RoleVo;
import com.ynhdkc.tenant.model.RpcUserBatchProcessRolesReqDto;
import com.ynhdkc.tenant.model.RpcUserBoundRolesQueryVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-14 13:37
 */
@FeignClient(name = "dyt-privilege", path = "/rpc/v1/privilege")
public interface PrivilegeClient {
    @GetMapping(value = "/roles/{role-id}")
    ResponseEntity<RoleVo> getRoleById(@PathVariable("role-id") Long roleId);

    @PostMapping(value = "/roles/bound-users/query")
    ResponseEntity<RpcRoleBoundUsersQueryVo> queryRoleBoundUsers(@RequestBody RoleBoundUsersQueryReqDto request);

    @GetMapping(value = "/roles/bound-users/{user-id}/roles")
    ResponseEntity<RpcUserBoundRolesQueryVo> queryUserBoundRoles(@PathVariable("user-id") Long userId, @RequestParam("tenant_id") Long tenantId);

    @PostMapping(value = "/roles/users/{user-id}/process/batch")
    ResponseEntity<BaseOperationResponse> userBatchProcessRoles(@PathVariable("user-id") Long userId, @RequestBody RpcUserBatchProcessRolesReqDto request);
}
