package backend.common.entity.dto.hisgateway.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class UpdatePatientArchivesRequest {

    @JsonProperty("hospital_code")
    private String hospitalCode;

    private String name;

    @JsonProperty("medical_record_number")
    private String medicalRecordNumber;

    private String vuid;

    @JsonProperty("id_card_number")
    private String idCardNumber;

    @JsonProperty("id_card_type")
    private Integer idCardType;

    private String birthday;

    private Integer sex;

    private String telephone;

    private Integer nation;

    private Integer occupation;

    private String address;

}
