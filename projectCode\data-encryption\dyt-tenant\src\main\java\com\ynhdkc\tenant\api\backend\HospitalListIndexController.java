package com.ynhdkc.tenant.api.backend;

import backend.security.oplog.DytSecurityRequired;
import com.ynhdkc.tenant.handler.HospitalListIndexApi;
import com.ynhdkc.tenant.model.HospitalListIndexVo;
import com.ynhdkc.tenant.model.SubmitHospitalListIndexReqDto;
import com.ynhdkc.tenant.service.backend.IHospitalListIndexService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
@Api(tags = "HospitalListIndex")
public class HospitalListIndexController implements HospitalListIndexApi {

    private final IHospitalListIndexService hospitalListIndexService;

    @Override
    @DytSecurityRequired(value = "hospital:list:index:query")
    public ResponseEntity<List<HospitalListIndexVo>> getHospitalListIndex() {
        return ResponseEntity.ok(hospitalListIndexService.getHospitalListIndex());
    }

    @Override
    @DytSecurityRequired(value = "hospital:list:index:submit")
    public ResponseEntity<List<HospitalListIndexVo>> submitHospitalListIndex(List<SubmitHospitalListIndexReqDto> hospitalListIndexDtoList) {
        return ResponseEntity.ok(hospitalListIndexService.submitHospitalListIndex(hospitalListIndexDtoList));
    }
}
