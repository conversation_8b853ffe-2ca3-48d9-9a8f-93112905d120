package backend.common.cdc;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;

@Slf4j
public abstract class CDCListener<T> {
    protected final ResourceChangeCapture<T> capture;

    public CDCListener(ResourceChangeCapture<T> capture) {
        this.capture = capture;
    }

    public void doWork(ConsumerRecord<String, byte[]> msg, Class<T> clazz) throws Exception {
        CDCMessage it = null;
        try {
            it = CDCUtils.buildCDCMessage(msg);
        } catch (Exception e) {
            log.error("CDC Message build error", e);
        }
        if (it == null) {
            return;
        }
        this.capture.onChange(it.getOp(), it.getId(), it.beforeTo(clazz), it.afterTo(clazz));
    }
}
