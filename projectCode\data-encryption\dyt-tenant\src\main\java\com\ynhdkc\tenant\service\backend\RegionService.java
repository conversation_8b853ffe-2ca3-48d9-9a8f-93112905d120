package com.ynhdkc.tenant.service.backend;

import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ynhdkc.tenant.dao.mapper.ChinaRegionMapper;
import com.ynhdkc.tenant.entity.ChinaRegion;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.function.Function;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-07 15:50
 */
@RequiredArgsConstructor
@Service
public class RegionService {
    private final ChinaRegionMapper chinaRegionRepository;
    private final PageVoConvert pageVoConvert;

    @Transactional(rollbackFor = Exception.class)
    public RegionVo create(RegionCreateReqDto request) {
        ChinaRegion entity = toEntity(request);
        chinaRegionRepository.insertSelective(entity);
        return TO_VO_FUNCTION.apply(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    public BaseOperationResponse delete(Long regionId) {
        int effected = chinaRegionRepository.deleteByPrimaryKey(regionId);
        if (effected != 1) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "区域删除失败");
        }
        return new BaseOperationResponse("删除成功");
    }

    public RegionPageVo query(RegionQueryReqDto request) {
        ChinaRegionMapper.RegionQueryOption option = new ChinaRegionMapper.RegionQueryOption(request.getCurrentPage(), request.getPageSize());
        option.setId(request.getId())
                .setPid(request.getPid())
                .setShortName(request.getShortName())
                .setName(request.getName())
                .setMergerName(request.getMergerName())
                .setLevel(request.getLevel())
                .setPinyin(request.getPinyin())
                .setCode(request.getCode())
                .setZipCode(request.getZipCode())
                .setFirst(request.getFirst())
                .setSpellSimple(request.getSpellSimple())
                .setStartCreateTime(request.getStartCreateTime())
                .setEndCreateTime(request.getEndCreateTime());
        Page<ChinaRegion> regionPage;
        try (final Page<ChinaRegion> page = PageHelper.startPage(option.getCurrentPage(), option.getPageSize())) {
            regionPage = page.doSelectPage(() -> chinaRegionRepository.selectByConditions(option));
        }
        return pageVoConvert.toPageVo(regionPage, RegionPageVo.class, TO_VO_FUNCTION);
    }

    @Transactional(rollbackFor = Exception.class)
    public RegionVo update(Long regionId, RegionUpdateReqDto request) {
        ChinaRegion entity = toEntity(regionId, request);
        chinaRegionRepository.insertSelective(entity);
        return TO_VO_FUNCTION.apply(entity);
    }

    private static ChinaRegion toEntity(RegionCreateReqDto dto) {
        ChinaRegion entity = new ChinaRegion();
        entity.setPid(dto.getPid());
        entity.setShortName(dto.getShortName());
        entity.setName(dto.getName());
        entity.setMergerName(dto.getMergerName());
        entity.setLevel(dto.getLevel());
        entity.setPinyin(dto.getPinyin());
        entity.setCode(dto.getCode());
        entity.setZipCode(dto.getZipCode());
        entity.setFirst(dto.getFirst());
        entity.setLng(dto.getLng());
        entity.setLat(dto.getLat());
        entity.setFid(dto.getFid());
        entity.setSpellSimple(dto.getSpellSimple());
        return entity;
    }

    private static ChinaRegion toEntity(Long regionId, RegionUpdateReqDto dto) {
        ChinaRegion entity = new ChinaRegion();
        entity.setId(regionId);
        entity.setPid(dto.getPid());
        entity.setShortName(dto.getShortName());
        entity.setName(dto.getName());
        entity.setMergerName(dto.getMergerName());
        entity.setLevel(dto.getLevel());
        entity.setPinyin(dto.getPinyin());
        entity.setCode(dto.getCode());
        entity.setZipCode(dto.getZipCode());
        entity.setFirst(dto.getFirst());
        entity.setLng(dto.getLng());
        entity.setLat(dto.getLat());
        entity.setFid(dto.getFid());
        entity.setSpellSimple(dto.getSpellSimple());
        return entity;
    }

    private static final Function<ChinaRegion, RegionVo> TO_VO_FUNCTION = entity -> new RegionVo()
            .id(entity.getId())
            .pid(entity.getPid())
            .shortName(entity.getShortName())
            .name(entity.getName())
            .mergerName(entity.getMergerName())
            .level(entity.getLevel())
            .pinyin(entity.getPinyin())
            .code(entity.getCode())
            .zipCode(entity.getZipCode())
            .first(entity.getFirst())
            .lng(entity.getLng())
            .lat(entity.getLat())
            .fid(entity.getFid())
            .spellSimple(entity.getSpellSimple())
            .createTime(entity.getCreateTime());
}
