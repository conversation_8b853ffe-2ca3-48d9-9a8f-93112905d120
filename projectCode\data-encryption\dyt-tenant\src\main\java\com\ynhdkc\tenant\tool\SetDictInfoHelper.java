package com.ynhdkc.tenant.tool;

import com.ynhdkc.tenant.entity.DictLabel;
import com.ynhdkc.tenant.service.backend.DictLabelService;
import com.ynhdkc.tenant.util.ObjectConvertUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/23 09:39:20
 */
@Component
@RequiredArgsConstructor
public class SetDictInfoHelper {

    private final static String DICT_TYPE = "DictType";
    private final static String DICT_LABEL = "DictLabel";
    private final static String DICT_VALUE = "DictValue";
    private final static String SPLIT_CHAR = ",";
    private final DictLabelService dictLabelService;

    private static List<Field> getFields(Object obj) {
        return getAllDeclaredFields(obj.getClass());
    }

    private static List<Field> getAllDeclaredFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>(Arrays.asList(clazz.getDeclaredFields()));
        Class<?> superClass = clazz.getSuperclass();
        if (superClass != null) {
            fields.addAll(getAllDeclaredFields(superClass));
        }

        return fields;
    }

    private static List<String> getDictLabelList(Object source, Field labelFieldInSource) throws IllegalAccessException {
        Object dictLabelObject = labelFieldInSource.get(source);
        if (dictLabelObject instanceof String) {
            return Arrays.asList(((String) dictLabelObject).split(SPLIT_CHAR));
        }
        if (dictLabelObject instanceof List) {
            return ObjectConvertUtil.toStringList(dictLabelObject);
        }
        return null;
    }

    private static String setDictType(Object source, Object destination, List<Field> sourceFields, Field dictTypeField, String dictTypeFieldName) throws IllegalAccessException {
        Field typeFieldInSource = sourceFields.stream().filter(f -> f.getName().equals(dictTypeFieldName)).findFirst().orElse(null);
        if (typeFieldInSource == null) {
            return null;
        }
        dictTypeField.setAccessible(true);
        Object dictTypeObject = typeFieldInSource.get(source);
        if (!(dictTypeObject instanceof String)) {
            return null;
        }
        String dictType = (String) dictTypeObject;
        dictTypeField.set(destination, dictType);
        return dictType;
    }

    private static Field setDictLabel(Object source, Object destination, List<Field> sourceFields, List<Field> destinationFields, String dictTypeFieldName) throws IllegalAccessException {
        String dictLabelFieldName = dictTypeFieldName.replace(DICT_TYPE, DICT_LABEL);
        Field labelFieldInSource = sourceFields.stream().filter(f -> f.getName().equals(dictLabelFieldName)).findFirst().orElse(null);
        if (labelFieldInSource == null) {
            return null;
        }

        Field labelFieldInDestination = destinationFields.stream().filter(f -> f.getName().equals(dictLabelFieldName)).findFirst().orElse(null);
        if (labelFieldInDestination == null) {
            return null;
        }

        if (labelFieldInDestination.getType() == String.class) {
            List<String> dictLabelList = getDictLabelList(source, labelFieldInSource);
            if (dictLabelList == null) {
                return null;
            }
            if (CollectionUtils.isEmpty(dictLabelList)) {
                return null;
            }
            labelFieldInDestination.setAccessible(true);
            labelFieldInDestination.set(destination, String.join(SPLIT_CHAR, dictLabelList));
        }

        if (labelFieldInDestination.getType() == List.class) {
            Object dictLabelObject = labelFieldInSource.get(source);
            if (!(dictLabelObject instanceof String)) {
                return null;
            }
            String dictLabelString = (String) dictLabelObject;
            labelFieldInDestination.setAccessible(true);
            labelFieldInDestination.set(destination, Arrays.asList(dictLabelString.split(SPLIT_CHAR)));
        }
        return labelFieldInSource;
    }

    private static List<String> getDictValuesByDictLabels(Object source, List<DictLabel> dictLabels, String dictType, Field labelFieldInSource) throws IllegalAccessException {
        List<String> dictLabelList = getDictLabelList(source, labelFieldInSource);
        if (CollectionUtils.isEmpty(dictLabelList)) {
            return null;
        }

        List<String> dictValueList = dictLabels.stream().filter(dictLabel -> dictLabel.getDictType().equals(dictType) && dictLabelList.contains(dictLabel.getDictLabel())).map(DictLabel::getDictValue).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dictValueList)) {
            return null;
        }
        return dictValueList;
    }

    public void setDictInfo(Object source, Object destination) {
        setDictInfo(source, destination, null);
    }

    /**
     * 设置字典信息
     * <p>
     * 须知, 该方法只能在 object 中存在以 DictType 以及 DictLabel 结尾命名的属性时才能使用
     * <p>
     * 通过反射拿到 source object 中所有以 DictType 以及 DictLabel 结尾的属性，然后通过字典服务获取字典信息
     *
     * @param source      需要获取字典信息的对象
     * @param destination 需要赋值的对象
     * @param dictLabels  字典信息
     */
    @SneakyThrows
    public void setDictInfo(Object source, Object destination, @Nullable List<DictLabel> dictLabels) {
        DictPropertyInfo dictPropertyInfo = getDictPropertyInfo(source);
        /* 查询出Object中的所有Label */
        if (dictLabels == null) {
            dictLabels = dictLabelService.testDictLabels(dictPropertyInfo.getValueOfDictTypeFields(), dictPropertyInfo.getValueOfDictLabelsFields());
        }

        List<Field> sourceFields = dictPropertyInfo.getFields();
        List<Field> destinationFields = getFields(destination);

        List<Field> dictTypeFieldsInDestination = destinationFields.stream().filter(field -> field.getName().endsWith(DICT_TYPE)).collect(Collectors.toList());
        for (Field dictTypeField : dictTypeFieldsInDestination) {
            String dictTypeFieldName = dictTypeField.getName();

            String dictType = setDictType(source, destination, sourceFields, dictTypeField, dictTypeFieldName);
            Field labelFieldInSource = setDictLabel(source, destination, sourceFields, destinationFields, dictTypeFieldName);
            if (labelFieldInSource == null) {
                continue;
            }

            String dictValueFieldName = dictTypeFieldName.replace(DICT_TYPE, DICT_VALUE);
            Field valueFieldInDestination = destinationFields.stream().filter(f -> f.getName().equals(dictValueFieldName)).findFirst().orElse(null);
            if (valueFieldInDestination == null) {
                continue;
            }

            if (valueFieldInDestination.getType() == String.class) {
                List<String> dictValueList = getDictValuesByDictLabels(source, dictLabels, dictType, labelFieldInSource);
                if (CollectionUtils.isEmpty(dictValueList)) {
                    continue;
                }
                valueFieldInDestination.setAccessible(true);

                valueFieldInDestination.set(destination, String.join(SPLIT_CHAR, dictValueList));
            }

            if (valueFieldInDestination.getType() == List.class) {
                Field dictValueFieldInSource = sourceFields.stream().filter(f -> f.getName().equals(dictValueFieldName)).findFirst().orElse(null);
                if (dictValueFieldInSource == null) {
                    List<String> dictValueList = getDictValuesByDictLabels(source, dictLabels, dictType, labelFieldInSource);
                    if (CollectionUtils.isEmpty(dictValueList)) {
                        continue;
                    }
                    valueFieldInDestination.setAccessible(true);
                    valueFieldInDestination.set(destination, dictValueList);
                } else {
                    dictValueFieldInSource.setAccessible(true);
                    Object dictValueObject = dictValueFieldInSource.get(source);
                    if (!(dictValueObject instanceof String)) {
                        continue;
                    }
                    String dictValueString = (String) dictValueObject;
                    valueFieldInDestination.setAccessible(true);
                    valueFieldInDestination.set(destination, Arrays.asList(dictValueString.split(SPLIT_CHAR)));
                }
            }
        }
    }

    public <T> List<DictLabel> getDictLabels(List<T> sources) {
        List<String> dictTypes = new ArrayList<>();
        List<String> dictLabels = new ArrayList<>();
        for (Object source : sources) {
            DictPropertyInfo dictPropertyInfo = getDictPropertyInfo(source);
            dictTypes.addAll(dictPropertyInfo.getValueOfDictTypeFields());
            dictLabels.addAll(dictPropertyInfo.getValueOfDictLabelsFields());
        }
        return dictLabelService.testDictLabels(dictTypes, dictLabels);
    }

    @SneakyThrows
    private DictPropertyInfo getDictPropertyInfo(Object source) {
        List<String> valueOfDictTypeFields = new ArrayList<>();
        List<String> valueOfDictLabelsFields = new ArrayList<>();
        List<Field> fields = getFields(source);
        for (Field field : fields) {
            String fieldName = field.getName();
            if (fieldName.endsWith(DICT_TYPE) || fieldName.endsWith(DICT_LABEL)) {
                field.setAccessible(true);
            }

            if (fieldName.endsWith(DICT_TYPE)) {
                Object value = field.get(source);
                if (value instanceof String) {
                    String dictType = (String) value;
                    valueOfDictTypeFields.add(dictType);
                }
            }
            if (fieldName.endsWith(DICT_LABEL)) {
                Object value = field.get(source);
                if (value instanceof List) {
                    List<String> dictLabel = ObjectConvertUtil.toStringList(value);
                    valueOfDictLabelsFields.addAll(dictLabel);
                }
                if (value instanceof String) {
                    String dictLabel = (String) value;
                    valueOfDictLabelsFields.addAll(Arrays.asList(dictLabel.split(SPLIT_CHAR)));
                }
            }
        }
        return new DictPropertyInfo(valueOfDictTypeFields, valueOfDictLabelsFields, fields);
    }


    @Data
    @AllArgsConstructor
    static class DictPropertyInfo {
        private List<String> valueOfDictTypeFields;
        private List<String> valueOfDictLabelsFields;
        private List<Field> fields;
    }

}
