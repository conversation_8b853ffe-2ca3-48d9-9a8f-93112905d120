package com.ynhdkc.tenant.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class UrlUtil {

    public static String extractValue(String source, String key) {
        int startIndex = source.indexOf(key);
        if (startIndex != -1) {
            startIndex += key.length();
            int endIndex = source.indexOf("&", startIndex);
            if (endIndex != -1) {
                return source.substring(startIndex, endIndex);
            } else {
                return source.substring(startIndex);
            }
        }
        return null;
    }


    public static String getHospitalCodeFromUrl(String source) {
        Pattern pattern = Pattern.compile("hos_code=([^&?]*)");
        Matcher matcher = pattern.matcher(source);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }


    public static void main(String[] args) {
        String source = "https://appv2.ynhdkc.com/registration_doctor_list?hos_code=871030&dep_id=0502";
        String hosCodeKey = "hos_code=";
        String hosCode = extractValue(source, hosCodeKey);
        System.out.println(hosCode);

        String depIdKey = "dep_id=";
        String depId = extractValue(source, depIdKey);
        System.out.println(depId);
    }
}
