package com.ynhdkc.tenant.entity;

import backend.common.domain.user.BaseUserEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @since 2023/2/16 14:31:01
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "t_tenant_user")
public class TenantUser extends BaseUserEntity {
    private String nickname;
    private String email;
    /**
     * 是否是超级管理员
     */
    private Boolean admin;

    private String headImageUrl;
}
