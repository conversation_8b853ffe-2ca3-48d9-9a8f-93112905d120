package backend.common.kafka.streams;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.kafka.StreamsBuilderFactoryBeanCustomizer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.kafka.annotation.EnableKafkaStreams;

import java.util.List;

@Data
@Configuration
@Slf4j
@EnableKafkaStreams
@ConfigurationProperties(prefix = "backend.kafka.global-store.info")
@Import({
        HospitalInfoModelGlobalStore.class,
        DoctorInfoModelGlobalStore.class,
        DoctorScheduleListGlobalStore.class,
        DoctorScheduleListHisGlobalStore.class,
        TimeScheduleListGlobalStore.class,
        TimeScheduleListHisGlobalStore.class,
        HisDepartmentDoctorGlobalStore.class,
        DictInfoGlobalStore.class
})
@RequiredArgsConstructor
@ConditionalOnProperty(name = "backend.kafka.global-store.info.hospital-info", havingValue = "true")
public class GlobalStoreAutoConfiguration {
    @Autowired(required = false)
    private HospitalInfoModelGlobalStore hospitalInfoModelGlobalStore;

    @Autowired(required = false)
    private DoctorInfoModelGlobalStore doctorInfoModelGlobalStore;

    @Autowired(required = false)
    private DoctorScheduleListGlobalStore doctorScheduleListGlobalStore;

    @Autowired(required = false)
    private DoctorScheduleListHisGlobalStore doctorScheduleListHisGlobalStore;

    @Autowired(required = false)
    private TimeScheduleListGlobalStore timeScheduleListGlobalStore;

    @Autowired(required = false)
    private TimeScheduleListHisGlobalStore timeScheduleListHisGlobalStore;

    @Autowired(required = false)
    private HisDepartmentDoctorGlobalStore hisDepartmentDoctorGlobalStore;

    @Autowired(required = false)
    private DictInfoGlobalStore dictInfoGlobalStore;

    private boolean hospitalInfo;
    private boolean doctorInfo;
    private boolean doctorScheduleList;
    private boolean doctorScheduleListHis;
    private boolean timeScheduleList;
    private boolean timeScheduleListHis;
    private boolean hisDepartmentDoctor;
    private boolean dictInfo;

    @Bean
    public StreamsBuilderFactoryBeanCustomizer streamsBuilderFactoryBean() {
        List<KafkaStreamsCallback> callbacks = Lists.newArrayList();

        for (StoreType storeType : StoreType.values()) {
            if (storeType.isActive(this) && storeType.getStore(this) != null) {
                callbacks.add(storeType.getStore(this));
            }
        }

        return new CallbackStreamsBuilderFactoryBeanCustomizer(callbacks);
    }

    private enum StoreType {
        HOSPITAL_INFO {
            @Override
            public KafkaStreamsCallback getStore(GlobalStoreAutoConfiguration configuration) {
                return configuration.hospitalInfoModelGlobalStore;
            }

            @Override
            public boolean isActive(GlobalStoreAutoConfiguration configuration) {
                return configuration.isHospitalInfo();
            }
        },
        DOCTOR_INFO {
            @Override
            public KafkaStreamsCallback getStore(GlobalStoreAutoConfiguration configuration) {
                return configuration.doctorInfoModelGlobalStore;
            }

            @Override
            public boolean isActive(GlobalStoreAutoConfiguration configuration) {
                return configuration.isDoctorInfo();
            }
        },
        DOCTOR_SCHEDULE_LIST {
            @Override
            public KafkaStreamsCallback getStore(GlobalStoreAutoConfiguration configuration) {
                return configuration.doctorScheduleListGlobalStore;
            }

            @Override
            public boolean isActive(GlobalStoreAutoConfiguration configuration) {
                return configuration.isDoctorScheduleList();
            }
        },
        DOCTOR_SCHEDULE_LIST_HIS {
            @Override
            public KafkaStreamsCallback getStore(GlobalStoreAutoConfiguration configuration) {
                return configuration.doctorScheduleListHisGlobalStore;
            }

            @Override
            public boolean isActive(GlobalStoreAutoConfiguration configuration) {
                return configuration.isDoctorScheduleListHis();
            }
        },
        TIME_SCHEDULE_LIST {
            @Override
            public KafkaStreamsCallback getStore(GlobalStoreAutoConfiguration configuration) {
                return configuration.timeScheduleListGlobalStore;
            }

            @Override
            public boolean isActive(GlobalStoreAutoConfiguration configuration) {
                return configuration.isTimeScheduleList();
            }
        },
        TIME_SCHEDULE_LIST_HIS {
            @Override
            public KafkaStreamsCallback getStore(GlobalStoreAutoConfiguration configuration) {
                return configuration.timeScheduleListHisGlobalStore;
            }

            @Override
            public boolean isActive(GlobalStoreAutoConfiguration configuration) {
                return configuration.isTimeScheduleListHis();
            }
        },
        HIS_DEPARTMENT_DOCTOR {
            @Override
            public KafkaStreamsCallback getStore(GlobalStoreAutoConfiguration configuration) {
                return configuration.hisDepartmentDoctorGlobalStore;
            }

            @Override
            public boolean isActive(GlobalStoreAutoConfiguration configuration) {
                return configuration.isHisDepartmentDoctor();
            }
        },
        DICT_INFO {
            @Override
            public KafkaStreamsCallback getStore(GlobalStoreAutoConfiguration configuration) {
                return configuration.dictInfoGlobalStore;
            }

            @Override
            public boolean isActive(GlobalStoreAutoConfiguration configuration) {
                return configuration.isDictInfo();
            }
        };

        public abstract KafkaStreamsCallback getStore(GlobalStoreAutoConfiguration configuration);

        public abstract boolean isActive(GlobalStoreAutoConfiguration configuration);
    }
}
