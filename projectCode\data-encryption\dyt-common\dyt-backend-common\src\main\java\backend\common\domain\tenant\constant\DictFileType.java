package backend.common.domain.tenant.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/11/9 9:42
 */
@Getter
public enum DictFileType {
    KEYWORD(0, "关键词"),
    SHIELD(1, "屏蔽词");

    private final Integer code;
    private final String desc;

    DictFileType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DictFileType of(Integer code) {
        if (null == code) {
            return null;
        }
        for (DictFileType dictFileType : DictFileType.values()) {
            if (dictFileType.getCode().equals(code)) {
                return dictFileType;
            }
        }
        return null;
    }
}
