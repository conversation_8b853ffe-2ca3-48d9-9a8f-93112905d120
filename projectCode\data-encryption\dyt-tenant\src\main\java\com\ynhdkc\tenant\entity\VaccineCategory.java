package com.ynhdkc.tenant.entity;

import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;

@EqualsAndHashCode(callSuper = true)
@Table(name = "t_vaccine_category")
@Data
public class VaccineCategory extends BaseEntity {
    private String name;
    private Integer sort;
    private String logo;
    private Integer isHot;
    private Integer hotSort;
    private String description;
    private String tips;
    private Integer status;
}
