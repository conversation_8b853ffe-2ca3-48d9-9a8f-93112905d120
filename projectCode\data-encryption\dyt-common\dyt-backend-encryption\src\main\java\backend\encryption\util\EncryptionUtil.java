package backend.encryption.util;

import backend.encryption.annotation.EncryptField;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * 统一加密工具类
 * 根据算法类型选择相应的加密实现
 * 
 * 支持的算法：
 * - AES-GCM：默认对称加密算法
 * - SM2：国密椭圆曲线算法
 * - SM4：国密对称加密算法（待实现）
 * 
 * <AUTHOR> Backend Team
 * @version 1.1-SNAPSHOT
 * @since 2024-06-24
 */
@Slf4j
public class EncryptionUtil {
    
    /**
     * 根据算法类型加密数据
     * 
     * @param plainText 待加密的明文
     * @param algorithm 加密算法类型
     * @return 加密后的字符串
     */
    public static String encrypt(String plainText, EncryptField.AlgorithmType algorithm) {
        if (!StringUtils.hasText(plainText)) {
            return plainText;
        }
        
        try {
            switch (algorithm) {
                case AES_GCM:
                    return AESGCMUtil.encrypt(plainText);
                    
                case SM2:
                    return SM2Util.encrypt(plainText, null); // 使用默认公钥
                    
                case SM4:
                    // TODO: 实现SM4加密
                    log.warn("SM4 algorithm not implemented yet, falling back to AES-GCM");
                    return AESGCMUtil.encrypt(plainText);
                    
                default:
                    log.warn("Unknown algorithm type: {}, using AES-GCM", algorithm);
                    return AESGCMUtil.encrypt(plainText);
            }
        } catch (Exception e) {
            log.error("Failed to encrypt data with algorithm: {}", algorithm, e);
            throw new RuntimeException("加密失败: " + algorithm, e);
        }
    }
    
    /**
     * 根据算法类型解密数据
     * 
     * @param encryptedText 加密的字符串
     * @param algorithm 加密算法类型
     * @return 解密后的明文
     */
    public static String decrypt(String encryptedText, EncryptField.AlgorithmType algorithm) {
        if (!StringUtils.hasText(encryptedText)) {
            return encryptedText;
        }
        
        try {
            switch (algorithm) {
                case AES_GCM:
                    return AESGCMUtil.decrypt(encryptedText);
                    
                case SM2:
                    return SM2Util.decrypt(encryptedText, null); // 使用默认私钥
                    
                case SM4:
                    // TODO: 实现SM4解密
                    log.warn("SM4 algorithm not implemented yet, falling back to AES-GCM");
                    return AESGCMUtil.decrypt(encryptedText);
                    
                default:
                    log.warn("Unknown algorithm type: {}, using AES-GCM", algorithm);
                    return AESGCMUtil.decrypt(encryptedText);
            }
        } catch (Exception e) {
            log.error("Failed to decrypt data with algorithm: {}", algorithm, e);
            throw new RuntimeException("解密失败: " + algorithm, e);
        }
    }
    
    /**
     * 使用默认算法（AES-GCM）加密
     * 
     * @param plainText 待加密的明文
     * @return 加密后的字符串
     */
    public static String encrypt(String plainText) {
        return encrypt(plainText, EncryptField.AlgorithmType.AES_GCM);
    }
    
    /**
     * 使用默认算法（AES-GCM）解密
     * 
     * @param encryptedText 加密的字符串
     * @return 解密后的明文
     */
    public static String decrypt(String encryptedText) {
        return decrypt(encryptedText, EncryptField.AlgorithmType.AES_GCM);
    }
    
    /**
     * 验证指定算法的加密解密功能
     * 
     * @param plainText 测试明文
     * @param algorithm 算法类型
     * @return 是否验证成功
     */
    public static boolean verify(String plainText, EncryptField.AlgorithmType algorithm) {
        try {
            String encrypted = encrypt(plainText, algorithm);
            String decrypted = decrypt(encrypted, algorithm);
            return plainText.equals(decrypted);
        } catch (Exception e) {
            log.error("Verification failed for algorithm: {}", algorithm, e);
            return false;
        }
    }
    
    /**
     * 验证所有支持的算法
     * 
     * @param plainText 测试明文
     * @return 验证结果，key为算法类型，value为验证结果
     */
    public static java.util.Map<EncryptField.AlgorithmType, Boolean> verifyAll(String plainText) {
        java.util.Map<EncryptField.AlgorithmType, Boolean> results = new java.util.HashMap<>();
        
        for (EncryptField.AlgorithmType algorithm : EncryptField.AlgorithmType.values()) {
            try {
                boolean result = verify(plainText, algorithm);
                results.put(algorithm, result);
                log.debug("Algorithm {} verification: {}", algorithm, result ? "PASSED" : "FAILED");
            } catch (Exception e) {
                results.put(algorithm, false);
                log.error("Algorithm {} verification failed with exception", algorithm, e);
            }
        }
        
        return results;
    }
}
