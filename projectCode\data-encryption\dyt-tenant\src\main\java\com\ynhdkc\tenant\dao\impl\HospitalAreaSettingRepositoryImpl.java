package com.ynhdkc.tenant.dao.impl;

import com.ynhdkc.tenant.dao.HospitalAreaSettingRepository;
import com.ynhdkc.tenant.dao.mapper.*;
import com.ynhdkc.tenant.entity.setting.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-09 16:21
 */
@Repository
@RequiredArgsConstructor
public class HospitalAreaSettingRepositoryImpl implements HospitalAreaSettingRepository {
    private final AppointmentRuleSettingMapper appointmentRuleSettingMapper;
    private final DiagnosisPaymentSettingMapper diagnosisPaymentSettingMapper;
    private final HospitalizationSettingMapper hospitalizationSettingMapper;
    private final PatientReportSettingMapper patientReportSettingMapper;
    private final PatientCardSettingMapper patientCardSettingMapper;
    private final CustomBusinessSettingMapper customBusinessSettingMapper;

    @Override
    public void createAppointmentRuleSetting(AppointmentRuleSetting entity) {
        appointmentRuleSettingMapper.insertSelective(entity);
    }

    @Override
    public void updateAppointmentRuleSetting(AppointmentRuleSetting entity) {
        appointmentRuleSettingMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void createDiagnosisPaymentSetting(DiagnosisPaymentSetting entity) {
        diagnosisPaymentSettingMapper.insertSelective(entity);
    }

    @Override
    public void updateDiagnosisPaymentSetting(DiagnosisPaymentSetting entity) {
        diagnosisPaymentSettingMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void createHospitalizationSetting(HospitalizationSetting entity) {
        hospitalizationSettingMapper.insertSelective(entity);
    }

    @Override
    public void updateHospitalizationSetting(HospitalizationSetting entity) {
        hospitalizationSettingMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void createPatientReportSetting(PatientReportSetting entity) {
        patientReportSettingMapper.insertSelective(entity);
    }

    @Override
    public void updatePatientReportSetting(PatientReportSetting entity) {
        patientReportSettingMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void createPatientCardSetting(PatientCardSetting entity) {
        patientCardSettingMapper.insertSelective(entity);
    }

    @Override
    public void updatePatientCardSetting(PatientCardSetting entity) {
        patientCardSettingMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void createCustomBusinessSetting(CustomBusinessSetting entity) {
        customBusinessSettingMapper.insertSelective(entity);
    }

    @Override
    public void updateCustomBusinessSetting(CustomBusinessSetting entity) {
        customBusinessSettingMapper.updateByPrimaryKeySelective(entity);
    }
}
