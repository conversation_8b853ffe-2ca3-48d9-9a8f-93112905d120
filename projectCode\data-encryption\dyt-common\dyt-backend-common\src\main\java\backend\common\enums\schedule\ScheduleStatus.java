package backend.common.enums.schedule;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum ScheduleStatus {
    DISABLE("禁用", 0),
    ENABLE("启用", 1),
    NOT_YET("时间未到", 2),
    CLOSE("已关闭", 3),
    FULL("约满", 4),
    NOT_OPEN("约满", 5),
    STOP_DIAGNOSIS("停诊", 6),
    UN_KNOW("未知", -1);

    private final String value;

    private final Integer code;

    ScheduleStatus(String value, Integer code) {
        this.value = value;
        this.code = code;
    }

    @JsonCreator
    public static ScheduleStatus getFromCode(int code) {
        for (ScheduleStatus t : ScheduleStatus.values()) {
            if (t.getCode().equals(code)) {
                return t;
            }
        }
        return UN_KNOW;
    }

    public static ScheduleStatus getFromValue(String value) {
        for (ScheduleStatus t : ScheduleStatus.values()) {
            if (t.getValue().equals(value)) {
                return t;
            }
        }
        return UN_KNOW;
    }

    public String getValue() {
        return value;
    }

    public Integer getCode() {
        return code;
    }

    @JsonValue
    public String getRequestCode() {
        return code.toString();
    }
}
