package com.ynhdkc.tenant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/25 14:06
 **/
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum CategoryType {
    NORMAL("常规", 0),
    VACCINE("疫苗预约", 1),
    SPECIAL_NEED_CLINIC("特需门诊", 2),
    MULTIDISCIPLINARY("多学科", 3),
    TRADITIONAL_CHINESE_MEDICINE("中医", 4),
    OTHERS("其他", 9),
    QUICK_CHECK("快速检查", 11);
    private String name;
    private int value;
}
