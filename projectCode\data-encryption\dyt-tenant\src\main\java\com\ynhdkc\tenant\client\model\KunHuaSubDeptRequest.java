package com.ynhdkc.tenant.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class KunHuaSubDeptRequest {

    @JsonProperty("as_data")
    private String data;

    @JsonProperty("as_code")
    private String code;

    @JsonProperty("as_appid")
    private String appId;

    @JsonProperty("as_token")
    private String token;

    @JsonProperty("as_secret")
    private String secret;

    @JsonProperty("as_timestamp")
    private String timestamp;

    @lombok.Data
    public static class Data {

        @JsonProperty("BOOKING_PLATFORM")
        private String platFrom;

        @JsonProperty("DEPT_CODE")
        private String deptCode;

        @JsonProperty("START_DATE")
        private String startDate;

        @JsonProperty("END_DATE")
        private String endDate;
    }
}
