package backend.common.domain.privilege;


import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RoleEntity extends BaseEntity {
    private Long tenantId;
    private Long parentId;
    private String name;
    private String description;
    private Boolean canAssigned;
    private Integer status;
}
