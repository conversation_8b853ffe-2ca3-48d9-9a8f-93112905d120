# =====================================================
# 数据加密配置文件
# 根据《数据安全法》《个人信息保护法》整改要求
# 配合影子字段实现渐进式加密迁移
# =====================================================

backend:
  encryption:
    # 启用加密功能
    enabled: true
    
    # 生产环境密钥（请使用环境变量或密钥管理系统）
    secret-key: ${ENCRYPTION_SECRET_KEY:DYT-Backend-Encryption-Key-32Byte}
    
    # 默认加密算法
    algorithm: AES_GCM
    
    # 启用调试和性能监控（生产环境建议关闭debug）
    debug-enabled: false
    performance-monitor-enabled: true
    
    # 字段级别的加密策略配置
    strategies:
      
      # =====================================================
      # t_tenant_user 表字段配置
      # =====================================================
      
      # 身份证号（核心数据 - 最高安全级别）
      tenant-user-id-card:
        strategy: SHADOW_PRIORITY  # 影子字段优先策略
        shadow-field: "id_card_no_encrypted"
        algorithm: AES_GCM  # 使用AES-GCM算法
        enabled: true
        description: "租户用户身份证号加密"
        version: 1
      
      # 手机号码（重要数据）
      tenant-user-phone:
        strategy: SHADOW_PRIORITY
        shadow-field: "phone_number_encrypted"
        algorithm: AES_GCM
        enabled: true
        description: "租户用户手机号码加密"
        version: 1
      
      # 用户名（重要数据）
      tenant-user-name:
        strategy: SHADOW_PRIORITY
        shadow-field: "name_encrypted"
        algorithm: AES_GCM
        enabled: true
        description: "租户用户名加密"
        version: 1
      
      # =====================================================
      # t_recharge_record 表字段配置
      # =====================================================
      
      # 就诊人姓名（重要数据）
      recharge-patient-name:
        strategy: SHADOW_PRIORITY
        shadow-field: "patient_name_encrypted"
        algorithm: AES_GCM
        enabled: true
        description: "充值记录就诊人姓名加密"
        version: 1
      
      # 就诊卡号（核心数据 - 最高安全级别）
      recharge-jz-card:
        strategy: SHADOW_PRIORITY
        shadow-field: "jz_card_encrypted"
        algorithm: AES_GCM  # 核心数据可考虑使用SM2国密算法
        enabled: true
        description: "充值记录就诊卡号加密"
        version: 1
      
      # =====================================================
      # t_tenant 表字段配置
      # =====================================================
      
      # 联系人手机号（重要数据）
      tenant-contact-phone:
        strategy: SHADOW_PRIORITY
        shadow-field: "contact_phone_number_encrypted"
        algorithm: AES_GCM
        enabled: true
        description: "租户联系人手机号加密"
        version: 1
      
      # 联系人邮箱（重要数据）
      tenant-contact-email:
        strategy: SHADOW_PRIORITY
        shadow-field: "contact_email_encrypted"
        algorithm: AES_GCM
        enabled: true
        description: "租户联系人邮箱加密"
        version: 1
    
    # =====================================================
    # 国密算法配置（可选，用于核心数据）
    # =====================================================
    sm2:
      enabled: false  # 如需使用国密算法，设置为true
      public-key: ${SM2_PUBLIC_KEY:}
      private-key: ${SM2_PRIVATE_KEY:}
    
    sm4:
      enabled: false
      secret-key: ${SM4_SECRET_KEY:}

# =====================================================
# 数据库配置示例
# =====================================================
spring:
  datasource:
    url: **************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    
    # 连接池配置
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  jpa:
    hibernate:
      ddl-auto: none  # 生产环境建议设置为none
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        # 批量操作配置
        jdbc:
          batch_size: 50
        order_inserts: true
        order_updates: true

# =====================================================
# 日志配置
# =====================================================
logging:
  level:
    backend.encryption: INFO  # 生产环境建议设置为INFO
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
  
  # 日志文件配置
  file:
    name: logs/encryption.log
  
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# =====================================================
# 监控和健康检查配置
# =====================================================
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

# =====================================================
# 迁移策略说明
# =====================================================

# 迁移阶段配置说明：
# 
# 1. PLAINTEXT_PRIORITY（迁移初期）
#    - 读取：优先读取原字段明文数据
#    - 写入：同时写入原字段和影子字段
#    - 用途：确保系统稳定性，开始数据迁移
#
# 2. SHADOW_PRIORITY（迁移中期）
#    - 读取：优先读取影子字段加密数据，原字段作为备份
#    - 写入：同时写入原字段和影子字段
#    - 用途：逐步切换到加密存储
#
# 3. SHADOW_ONLY（迁移完成）
#    - 读取：只读取影子字段加密数据
#    - 写入：只写入影子字段
#    - 用途：完全使用加密存储
#
# 4. DIRECT_ENCRYPT（新系统）
#    - 直接在原字段进行加密存储
#    - 适用于新系统或可接受停服的场景

# =====================================================
# 环境变量配置示例
# =====================================================

# 在生产环境中，建议通过环境变量配置敏感信息：
# export ENCRYPTION_SECRET_KEY="your-32-byte-secret-key-here"
# export DB_USERNAME="your_db_username"
# export DB_PASSWORD="your_db_password"
# export SM2_PUBLIC_KEY="your_sm2_public_key"
# export SM2_PRIVATE_KEY="your_sm2_private_key"
