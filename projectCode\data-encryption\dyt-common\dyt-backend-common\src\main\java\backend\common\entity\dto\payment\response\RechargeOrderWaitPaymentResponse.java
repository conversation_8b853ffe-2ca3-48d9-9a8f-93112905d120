package backend.common.entity.dto.payment.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class RechargeOrderWaitPaymentResponse {
    @JsonProperty("order_number")
    private String orderNumber;

    @JsonProperty("total_fee")
    private BigDecimal totalFee;

    @JsonProperty("result")
    private Boolean result;

    @JsonProperty("pay_type")
    private String payType;

    @JsonProperty("trade_serial_number")
    private String tradeSerialNumber;

    @JsonProperty("bank_order_number")
    private String bankOrderNumber;

    @JsonProperty("bank_card")
    private String bankCard;

    @JsonProperty("pay_time")
    private Date payTime;
}
