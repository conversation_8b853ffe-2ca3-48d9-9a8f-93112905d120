package com.ynhdkc.tenant.dao.impl;

import backend.common.util.MybatisUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.ynhdkc.tenant.dao.HospitalAreaPositionQuery;
import com.ynhdkc.tenant.dao.mapper.HospitalAreaPositionMapper;
import com.ynhdkc.tenant.entity.HospitalAreaPosition;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/9/25 17:53
 */
@Repository
@RequiredArgsConstructor
public class HospitalAreaPositionQueryImpl implements HospitalAreaPositionQuery {
    private final HospitalAreaPositionMapper hospitalAreaPositionMapper;

    @Override
    public HospitalAreaPosition queryByDepartmentId(Long departmentId) {
        return hospitalAreaPositionMapper.selectOneByExample2(HospitalAreaPosition.class, sql ->
                sql.andEqualTo(HospitalAreaPosition::getDepartmentId, departmentId));
    }

    @Override
    public HospitalAreaPosition queryByUniqueKey(Long buildingId, Long floorId, Long areaId, Integer type, String name) {
        return hospitalAreaPositionMapper.selectOneByExample2(HospitalAreaPosition.class, sql -> {
                    sql.andEqualTo(HospitalAreaPosition::getBuildingId, buildingId);
                    if (null == floorId) {
                        sql.andIsNull(HospitalAreaPosition::getFloorId);
                    } else {
                        sql.andEqualTo(HospitalAreaPosition::getFloorId, floorId);
                    }
                    if (null == areaId) {
                        sql.andIsNull(HospitalAreaPosition::getAreaId);
                    } else {
                        sql.andEqualTo(HospitalAreaPosition::getAreaId, areaId);
                    }
                    sql.andEqualTo(HospitalAreaPosition::getType, type);
                    sql.andEqualTo(HospitalAreaPosition::getName, name);
                }
        );
    }

    @Override
    public Page<HospitalAreaPosition> pageQueryPositions(PositionQueryOption option) {
        try (Page<HospitalAreaPosition> page = PageMethod.startPage(option.getCurrentPage(), option.getPageSize())) {
            return page.doSelectPage(() -> hospitalAreaPositionMapper.selectByExample(HospitalAreaPosition.class, builder -> {
                builder.defGroup(sql -> {
                    sql.andEqualTo(HospitalAreaPosition::getHospitalAreaId, option.getHospitalAreaId());
                    if (null != option.getBuildingId()) {
                        sql.andEqualTo(HospitalAreaPosition::getBuildingId, option.getBuildingId());
                    }
                    if (null != option.getFloorId()) {
                        sql.andEqualTo(HospitalAreaPosition::getFloorId, option.getFloorId());
                    }
                    if (null != option.getAreaId()) {
                        sql.andEqualTo(HospitalAreaPosition::getAreaId, option.getAreaId());
                    }
                    if (null != option.getType()) {
                        sql.andEqualTo(HospitalAreaPosition::getType, option.getType());
                    }
                    if (StringUtils.hasText(option.getName())) {
                        sql.andLike(HospitalAreaPosition::getName, MybatisUtil.likeBoth(option.getName()));
                    }
                });
                builder.builder(sql -> sql.orderByDesc(HospitalAreaPosition::getSort));
            }));
        }
    }

    @Override
    public HospitalAreaPosition queryById(Long positionId) {
        return hospitalAreaPositionMapper.selectByPrimaryKey(positionId);
    }
}
