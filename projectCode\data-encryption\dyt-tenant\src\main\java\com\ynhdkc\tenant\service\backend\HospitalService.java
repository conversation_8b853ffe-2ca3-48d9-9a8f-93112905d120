package com.ynhdkc.tenant.service.backend;

import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.model.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-26 14:24
 */
public interface HospitalService {
    static HospitalVo toHospitalVo(Hospital entity) {
        HospitalVo hospitalVo = new HospitalVo();
        hospitalVo.setId(entity.getId());
        hospitalVo.setTenantId(entity.getTenantId());
        hospitalVo.setHospitalCode(entity.getHospitalCode());
        hospitalVo.setLogo(entity.getLogo());
        hospitalVo.setName(entity.getName());
        hospitalVo.setStatus(entity.getStatus());
        hospitalVo.setCategory(entity.getCategories());
        hospitalVo.setProperty(entity.getProperty());
        hospitalVo.setDisplaySort(entity.getDisplaySort());
        hospitalVo.setCreateTime(entity.getCreateTime());
        return hospitalVo;
    }

    static HospitalDetailVo toHospitalDetailVo(Hospital entity) {
        HospitalDetailVo hospitalVo = new HospitalDetailVo();
        hospitalVo.setId(entity.getId());
        hospitalVo.setTenantId(entity.getTenantId());
        hospitalVo.setHospitalCode(entity.getHospitalCode());
        hospitalVo.setLogo(entity.getLogo());
        hospitalVo.setName(entity.getName());
        hospitalVo.setStatus(entity.getStatus());
        hospitalVo.setCategory(entity.getCategories());
        hospitalVo.setProperty(entity.getProperty());
        hospitalVo.setDisplaySort(entity.getDisplaySort());
        hospitalVo.setCreateTime(entity.getCreateTime());
        return hospitalVo;
    }

    static HospitalKafkaVo toHospitalKafkaVo(Hospital entity) {
        HospitalKafkaVo hospitalKafkaVo = new HospitalKafkaVo();
        hospitalKafkaVo.setId(entity.getId());
        hospitalKafkaVo.setTenantId(entity.getTenantId());
        hospitalKafkaVo.setHospitalCode(entity.getHospitalCode());
        hospitalKafkaVo.setLogo(entity.getLogo());
        hospitalKafkaVo.setName(entity.getName());
        hospitalKafkaVo.setStatus(entity.getStatus());
        hospitalKafkaVo.setCategory(entity.getCategories());
        hospitalKafkaVo.setProperty(entity.getProperty());
        hospitalKafkaVo.setDisplaySort(entity.getDisplaySort());
        hospitalKafkaVo.setCreateTime(entity.getCreateTime());
        return hospitalKafkaVo;
    }

    HospitalDetailVo create(HospitalCreateReqDto request);

    HospitalDetailVo update(Long hospitalId, HospitalUpdateReqDto request);

    BaseOperationResponse delete(Long hospitalId);

    HospitalDetailVo getDetail(Long hospitalId);

    HospitalDetailVo rpcGetDetail(Long hospitalId);

    HospitalKafkaVo getDetailForKafka(Long hospitalId);

    HospitalPageVo query(HospitalQueryReqDto request);

    List<HospitalKafkaVo> queryAllForKafka();

    CustomerHospitalPageVo customerQueryHospitalList(Long userId, QueryCustomerHospitalPageReqDto request);

    default Hospital toHospitalEntity(HospitalCreateReqDto dto) {
        Hospital hospital = new Hospital();
        hospital.setTenantId(dto.getTenantId());
        hospital.setName(dto.getName());
        hospital.setLogo(dto.getLogo());
        hospital.setLevelDictType(dto.getLevelDictType());
        hospital.setCategories(dto.getCategory());
        hospital.setStatus(dto.getStatus());
        /* 院区默认值，医院无效 */
        hospital.setDisplay(false);
        hospital.setDisplayGuide(false);
        hospital.setDisplayFloor(false);
        return hospital;
    }

    List<HospitalVo> selectHospitalListByRuleId(Long id);

    Hospital getByHospitalAreaId(Long hospitalAreaId);

    HospitalAreaQueryLabelsVo queryHospitalAreaQueryLabels(HospitalAreaQueryLabelsReqDto request);

    List<HospitalVo> rpcBatchGetDetail(RpcBatchGetByIdReqDto request);
}
