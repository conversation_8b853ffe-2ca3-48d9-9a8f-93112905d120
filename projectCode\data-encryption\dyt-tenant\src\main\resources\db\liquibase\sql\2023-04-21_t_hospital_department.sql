create table t_hospital_department
(
    id                       bigint auto_increment comment '全局唯一标识'
        primary key,
    tenant_id                bigint                                     not null comment '租户 ID',
    hospital_id              bigint                                     not null comment '医院 ID',
    hospital_area_id         bigint                                     not null comment '院区 ID',
    hospital_code            varchar(20)                                null comment '医院编码',
    parent_id                bigint        default 0                    not null comment '父节点 ID',
    thrdpart_dep_code        varchar(300)  default ''                   null comment '第三方科室编码，通过逗号分隔',
    name                     varchar(300)                               not null comment '科室名称',
    first_letter             varchar(20)                                not null comment '科室名称首字母',
    short_name               varchar(100)                               null comment '简称',
    introduction             varchar(1000)                              null comment '科室介绍',
    address_id               bigint                                     null comment '科室地址',
    sort                     int           default 255                  null comment '科室排序',
    recommended              tinyint       default 0                    null comment '是否推荐：0，不推荐；1，推荐；',
    caution                  varchar(300)                               null comment '科室提醒',
    category                 varchar(100)  default ''                   not null comment '科室分类',
    address_intro            varchar(1000) default ''                   not null comment '科室地址',
    system_depends int            not null comment '挂号系统依赖：0,HIS;1,小系统;',
    enabled                  tinyint       default 1                    not null comment '是否启用：0，禁用；1，启用',
    display_bg_color         varchar(200)  default '#FFFFFF'            not null comment '科室展示背景色',
    enable_department_detail tinyint       default 0                    not null comment '是否启用科室详情：0，禁用；1，启用',
    uri                      varchar(1000)                              null comment '科室 URI',
    forbidden_day  int default -1 not null comment '禁止预约天数',
    advance_day    int default -1 not null comment '提前预约天数',
    remark                   varchar(500)                               null comment '备注',
    create_time              datetime(3)   default CURRENT_TIMESTAMP(3) not null,
    update_time              datetime(3)                                null on update CURRENT_TIMESTAMP(3)
)
    comment 'HospitalDepartment';