package com.ynhdkc.tenant.entity;

import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Table;

@EqualsAndHashCode(callSuper = true)
@Table(name = "t_vaccine")
@Data
@Accessors(chain = true)
public class Vaccine extends BaseEntity {
    private Long vaccineCategoryId;
    private Long tenantId;
    private Long hospitalId;
    private Long hospitalAreaId;
    private Long departmentId;
    private Long doctorId;
    private Integer sort;
    private String tips;
    private String remark;
}
