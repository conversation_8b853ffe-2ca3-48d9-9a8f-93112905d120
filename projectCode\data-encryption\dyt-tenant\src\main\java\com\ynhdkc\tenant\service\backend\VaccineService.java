package com.ynhdkc.tenant.service.backend;


import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.entity.Vaccine;
import com.ynhdkc.tenant.entity.VaccineCategory;
import com.ynhdkc.tenant.model.*;

public interface VaccineService {

    VaccineCategoryVo createVaccineCategory(CreateVaccineCategoryReqDto request);

    VaccineCategoryVo updateVaccineCategory(Long id, CreateVaccineCategoryReqDto request);

    BaseOperationResponse deleteVaccineCategory(Long id);

    VaccineCategoryVo getVaccineCategory(Long id);

    VaccineCategoryPageVo getVaccineCategoryPage(GetVaccineCategoryPageReqDto request);

    VaccineVo createVaccine(CreateVaccineReqDto request);

    VaccineVo updateVaccine(Long id, UpdateVaccineReqDto request);

    BaseOperationResponse deleteVaccine(Long id);

    VaccineVo getVaccine(Long id);

    VaccinePageVo getVaccinePage(GetVaccinePageReqDto request);

    static VaccineCategoryVo VaccineCategoryToVo(VaccineCategory cate) {
        VaccineCategoryVo vo = new VaccineCategoryVo();
        vo.setId(cate.getId());
        vo.setName(cate.getName());
        vo.setSort(cate.getSort());
        vo.setLogo(cate.getLogo());
        vo.setIsHot(cate.getIsHot());
        vo.setHotSort(cate.getHotSort());
        vo.setDescription(cate.getDescription());
        vo.setTips(cate.getTips());
        vo.setStatus(cate.getStatus());
        vo.setUpdateTime(cate.getUpdateTime());
        vo.setCreateTime(cate.getCreateTime());
        return vo;
    }

    static <T extends CreateVaccineCategoryReqDto> VaccineCategory VaccineCategoryToEntity(T dto) {
        VaccineCategory entity = new VaccineCategory();
        entity.setName(dto.getName());
        entity.setSort(dto.getSort());
        entity.setLogo(dto.getLogo());
        entity.setIsHot(dto.getIsHot());
        entity.setHotSort(dto.getHotSort());
        entity.setDescription(dto.getDescription());
        entity.setTips(dto.getTips());
        entity.setStatus(dto.getStatus());
        return entity;
    }

    static VaccineVo vaccineToVo(Vaccine vaccine) {
        VaccineVo vo = new VaccineVo();
        vo.setId(vaccine.getId());
        vo.setVaccineCategoryId(vaccine.getVaccineCategoryId());
        vo.setHospitalId(vaccine.getHospitalId());
        vo.setDepartmentId(vaccine.getDepartmentId());
        vo.setDoctorId(vaccine.getDoctorId());
        vo.setSort(vaccine.getSort());
        vo.setTips(vaccine.getTips());
        vo.setRemark(vaccine.getRemark());
        vo.setUpdateTime(vaccine.getUpdateTime());
        vo.setCreateTime(vaccine.getCreateTime());
        return vo;
    }

    static <T extends CreateVaccineReqDto> Vaccine vaccineToEntity(T dto) {
        Vaccine entity = new Vaccine();
        entity.setVaccineCategoryId(dto.getVaccineCategoryId());
        entity.setDoctorId(dto.getDoctorId());
        entity.setSort(dto.getSort());
        entity.setTips(dto.getTips());
        entity.setRemark(dto.getRemark());
        return entity;
    }

}
