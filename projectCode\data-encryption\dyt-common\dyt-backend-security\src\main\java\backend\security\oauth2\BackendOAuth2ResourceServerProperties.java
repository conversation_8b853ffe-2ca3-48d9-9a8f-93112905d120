package backend.security.oauth2;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.http.HttpMethod;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@SuppressWarnings({"AlibabaClassMustHaveAuthor", "AlibabaClassNamingShouldBeCamel"})
@Getter
@Setter
@ConfigurationProperties(prefix = "backend.security")
@Validated
public class BackendOAuth2ResourceServerProperties {
    private String rpcPathPattern = "/rpc/**";
    @NotEmpty
    private String basePathPattern;
    private List<PathMethod> publicPaths = new ArrayList<>();
    private List<AuthenticationPathMethod> authorizedPaths = new ArrayList<>();

    @Data
    @Validated
    public static class PathMethod {
        @NotEmpty
        private String antPattern;
        private Set<HttpMethod> method = new HashSet<>();
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Validated
    public static class AuthenticationPathMethod extends PathMethod {
        @NotEmpty
        private Set<String> scope;
    }
}
