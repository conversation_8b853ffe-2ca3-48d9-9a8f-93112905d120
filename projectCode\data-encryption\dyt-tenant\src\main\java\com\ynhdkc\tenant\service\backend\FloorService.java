package com.ynhdkc.tenant.service.backend;

import backend.common.response.BaseOperationResponse;
import backend.common.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.ynhdkc.tenant.entity.Floor;
import com.ynhdkc.tenant.model.*;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-03 10:08
 */
public interface FloorService {
    FloorVo create(FloorCreateReqDto request);

    BaseOperationResponse delete(Long floorId);

    FloorVo update(Long floorId, FloorUpdateReqDto request);

    FloorVo getDetail(Long floorId);

    FloorPageVo query(FloorQueryReqDto request);

    static Floor toFloor(FloorCreateReqDto request) {
        Floor floor = new Floor();
        floor.setTenantId(request.getTenantId());
        floor.setHospitalId(request.getHospitalId());
        floor.setHospitalAreaId(request.getHospitalAreaId());
        floor.setHospitalCode(request.getHospitalCode());
        floor.setName(request.getName());
        floor.setBuildingId(request.getBuildingId());
        floor.setSort(request.getSort());
        floor.setStatus(request.getStatus());
        if (!CollectionUtils.isEmpty(request.getPictureUrls())) {
            floor.setPictureUrls(JsonUtil.serializeObject(request.getPictureUrls()));
        }
        return floor;
    }

    static FloorVo toFloorVo(Floor floor) {
        FloorVo vo = new FloorVo();
        vo.setTenantId(floor.getTenantId());
        vo.setHospitalId(floor.getHospitalId());
        vo.setHospitalAreaId(floor.getHospitalAreaId());
        vo.setHospitalCode(floor.getHospitalCode());
        vo.setId(floor.getId());
        vo.setBuildingId(floor.getBuildingId());
        vo.setName(floor.getName());
        if (null != floor.getPictureUrls()) {
            vo.setPictureUrls(JsonUtil.deserializeObject(floor.getPictureUrls(), new TypeReference<List<String>>() {
            }));
        }
        vo.setSort(floor.getSort());
        vo.setStatus(floor.getStatus());
        vo.setCreateTime(floor.getCreateTime());
        return vo;
    }
}
