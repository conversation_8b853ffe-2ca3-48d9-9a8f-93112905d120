package com.ynhdkc.tenant.service.backend.impl;

import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import backend.common.util.JsonUtil;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.dao.FloorQuery;
import com.ynhdkc.tenant.dao.FloorRepository;
import com.ynhdkc.tenant.entity.Floor;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.FloorService;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2023/2/24 10:09:10
 */
@Service
@RequiredArgsConstructor
public class FloorServiceImpl implements FloorService {
    private final FloorQuery floorQuery;
    private final FloorRepository floorRepository;

    private final PageVoConvert pageVoConvert;

    @Override
    public FloorVo create(FloorCreateReqDto request) {
        Floor floor = FloorService.toFloor(request);
        floorRepository.create(floor);
        return FloorService.toFloorVo(floor);
    }

    @Override
    public BaseOperationResponse delete(Long floorId) {
        Floor floor = floorQuery.queryFloorById(floorId);
        if (floor == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "楼层不存在");
        }
        floorRepository.delete(floorId);
        return new BaseOperationResponse("删除成功");
    }

    @Override
    public FloorVo getDetail(Long floorId) {
        Floor floor = floorQuery.queryFloorById(floorId);
        if (floor == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "楼层不存在");
        }
        return FloorService.toFloorVo(floor);
    }

    @Override
    public FloorPageVo query(FloorQueryReqDto request) {
        Page<Floor> floorPage = floorQuery.pageQuery(new FloorQuery.FloorQueryOption(request.getCurrentPage(), request.getPageSize())
                .setTenantId(request.getTenantId())
                .setHospitalId(request.getHospitalId())
                .setHospitalAreaId(request.getHospitalAreaId())
                .setBuildingId(request.getBuildingId())
                .setName(request.getName())
                .setStatus(request.getStatus()));
        return pageVoConvert.toPageVo(floorPage, FloorPageVo.class, FloorService::toFloorVo);
    }

    @Override
    public FloorVo update(Long floorId, FloorUpdateReqDto request) {
        Floor floor = floorQuery.queryFloorById(floorId);
        if (floor == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "楼层不存在");
        }

        updateIfNotNull(floor, request);
        floorRepository.update(floor);

        return FloorService.toFloorVo(floor);
    }

    private static void updateIfNotNull(Floor entity, FloorUpdateReqDto dto) {
        if (null != dto.getBuildingId()) {
            entity.setBuildingId(dto.getBuildingId());
        }
        if (null != dto.getName()) {
            entity.setName(dto.getName());
        }
        if (!CollectionUtils.isEmpty(dto.getPictureUrls())) {
            entity.setPictureUrls(JsonUtil.serializeObject(dto.getPictureUrls()));
        }
        if (null != dto.getStatus()) {
            entity.setStatus(dto.getStatus());
        }
        if (null != dto.getSort()) {
            entity.setSort(dto.getSort());
        }
    }
}
