package com.ynhdkc.tenant.client;

import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.client.model.UserInfoDtoRespFeign;
import com.ynhdkc.tenant.client.model.UserVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2023/8/3 13:43:31
 */
@FeignClient(name = "${feign.name.user}")
public interface UserClient {
    @GetMapping(value = "/rpc/v1/user/users/user-phone-number/{phone-number}")
    ResponseEntity<UserVo> queryUserByPhoneNumber(@PathVariable("phone-number") String phoneNumber);

    @GetMapping(value = "/apis/v1/user/customer-api/users/user-phone-number/{phone-number}/send-message")
    ResponseEntity<BaseOperationResponse> sendMessage(@PathVariable("phone-number") String phoneNumber, @RequestParam Integer action, @RequestParam Integer from_app);

    @GetMapping(value = "/apis/v1/user/customer-api/users/user-phone-number/{phone-number}/verify-message")
    ResponseEntity<BaseOperationResponse> verifyMessage(@PathVariable("phone-number") String phoneNumber, @RequestParam Integer action, @RequestParam Integer from_app, @RequestParam String verify_code);

    @GetMapping(value = "/rpc/v1/user/users/{userId}")
    ResponseEntity<UserInfoDtoRespFeign> getUserInfo(@PathVariable Long userId);
}
