package com.ynhdkc.tenant.service.backend;

import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.model.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/9/25 17:17
 */
public interface HospitalAreaPositionService {
    BaseOperationResponse createLocation(LocationCreateReqDto request);

    BaseOperationResponse deleteLocation(Long locationId);

    HospitalAreaPositionQueryRespDto queryLocation(HospitalAreaPositionQueryReqDto request);

    HospitalAreaPositionTreeQueryRespDto queryPositionTree(HospitalAreaPositionTreeQueryReqDto request);

    BaseOperationResponse updateLocation(Long locationId, LocationUpdateReqDto request);
}
