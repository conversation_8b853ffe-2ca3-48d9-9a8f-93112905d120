package com.ynhdkc.tenant.doctor.sort.impl;

import com.ynhdkc.tenant.doctor.sort.strategy.AbstractDoctorSortStrategy;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.model.CustomerGroupedDoctorDetailVo;
import com.ynhdkc.tenant.tool.StringUtils;
import com.ynhdkc.tenant.util.DoctorUtils;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.ynhdkc.tenant.tool.DoctorSortUtils.getPinyin;

@Component
public class ShengZhongSortStrategy extends AbstractDoctorSortStrategy {

    private static final Set<String> HOSPITAL_CODES = Collections.unmodifiableSet(new HashSet<>(Arrays.asList("871333", "871232")));

    public ShengZhongSortStrategy() {
        super(HOSPITAL_CODES);
    }

    @Override
    public void sort(List<Doctor> doctors) {
        doctors.forEach(doctor -> {
            if (doctor != null) {
                String honor = doctor.getHonor();
                if (honor != null && honor.contains("名医") || doctor.getName().equals("袁卓珺")) {
                    doctor.setSort(0);
                } else {
                    Integer titleValue = DoctorUtils.getTitleValue(honor);
                    doctor.setSort(titleValue);
                }

                if (doctor.getName().equals("普通门诊")) {
                    doctor.setSort(Integer.MAX_VALUE);
                }
            }
        });

        doctors.sort(Comparator.comparing(Doctor::getSort).thenComparing(x -> {
            String name = x.getName();
            String pinyin = StringUtils.hasText(name) ? getPinyin(name) : "";
            return StringUtils.hasText(pinyin) ? pinyin : "";
        }));
    }

    @Override
    public void sortGroupVo(List<CustomerGroupedDoctorDetailVo> doctorGroupVos) {
        doctorGroupVos.sort(Comparator.comparing((CustomerGroupedDoctorDetailVo vo) -> vo.getSort() == null ? 0 : vo.getSort()));
    }
}
