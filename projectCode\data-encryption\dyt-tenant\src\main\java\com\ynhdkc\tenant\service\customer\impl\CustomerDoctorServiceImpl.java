package com.ynhdkc.tenant.service.customer.impl;


import backend.common.exception.BizException;
import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.Page;
import com.ynhdkc.oldsystem.integration.api.response.DoctorGroupServiceRespDto;
import com.ynhdkc.oldsystem.integration.api.response.QueryBaseDoctorDepartmentRespDto;
import com.ynhdkc.oldsystem.integration.client.DytkfDoctorClient;
import com.ynhdkc.oldsystem.integration.client.DytkfDoctorGroupClient;
import com.ynhdkc.tenant.dao.*;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.entity.DoctorGroupRelation;
import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import com.ynhdkc.tenant.enums.CurrentSysType;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.customer.CustomerDoctorService;
import com.ynhdkc.tenant.tool.SetDictInfoHelper;
import com.ynhdkc.tenant.tool.convert.DoctorConvert;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import com.ynhdkc.tenant.util.DoctorUtils;
import com.ynhdkc.tenant.util.HospitalUtils;
import com.ynhdkc.tenant.util.TestDomainWhiteListUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/7/21 10:26
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerDoctorServiceImpl implements CustomerDoctorService {

    private final HospitalQuery hospitalQuery;
    private final HospitalAreaQuery hospitalAreaQuery;
    private final DepartmentQuery departmentQuery;
    private final DoctorQuery doctorQuery;
    private final HospitalAreaSettingQuery hospitalAreaSettingQuery;

    private final SetDictInfoHelper setDictInfoHelper;
    private final PageVoConvert pageVoConvert;

    private final DytkfDoctorGroupClient doctorGroupClient;
    private final DytkfDoctorClient doctorClient;

    @Resource
    private TestDomainWhiteListUtil testDomainWhiteListUtil;

    @Override
    public CustomerDoctorDetailVo getDetail(Long doctorId) {
        Doctor doctor = doctorQuery.queryDoctorById(doctorId);
        if (null == doctor || !Boolean.TRUE.equals(doctor.getDisplay())) {
            return null;
        }
        Optional<AppointmentRuleSetting> appointmentRuleSettingOptional = hospitalAreaSettingQuery.queryAppointmentRuleSettingById(null, null, doctor.getHospitalAreaId());
        if (!appointmentRuleSettingOptional.isPresent()) {
            throw new BizException(HttpStatus.NOT_FOUND, "医院院区预约规则不存在");
        }

        appointmentRuleSettingOptional.ifPresent(appointmentRuleSetting -> {
            if (testDomainWhiteListUtil.isTestDomain()) {
                if (!testDomainWhiteListUtil.isWhiteList()) {
                    throw new BizException(HttpStatus.NOT_FOUND, "该用户没有预生产环境访问权限！");
                }
            } else {
                if (!appointmentRuleSetting.getCurrentSystemType().equals(CurrentSysType.NEW_SYS.getValue())) {
                    throw new BizException(HttpStatus.NOT_FOUND, "该医院暂未切换到新系统，禁止预约！");
                }
            }
        });
        CustomerDoctorDetailVo vo = DoctorConvert.toCustomerDoctorDetailVo(doctor);
        importDoctorInfo(vo);
        setDictInfoHelper.setDictInfo(doctor, vo);

        vo.setBelongDoctorGroups(doctorQuery.hasDoctorGroup(doctor.getId()));

        Optional<AppointmentRuleSetting> ruleSetting = hospitalAreaSettingQuery.queryAppointmentRuleSettingById(null, null, vo.getHospitalAreaId());
        ruleSetting.ifPresent(appointmentRuleSetting -> vo.setAppointmentRule(appointmentRuleSetting.getAppointmentRule()));
        DoctorUtils.setDoctorTitle(appointmentRuleSettingOptional.get(), CollUtil.toList(vo));
        return vo;
    }

    @Override
    public CustomerDoctorPageVo query(CustomerDoctorQueryReqDto request) {
        DoctorQuery.DoctorQueryOption option = new DoctorQuery.DoctorQueryOption(request.getCurrentPage(), request.getPageSize())
                .setHospitalId(request.getHospitalId())
                .setHospitalAreaId(request.getHospitalAreaId())
                .setDepartmentId(request.getDepartmentId())
                .setName(request.getName())
                .setDisplay(true)
                .sortBy(request.getSortBy());
        try (Page<Doctor> doctorPage = doctorQuery.pageQuery(option)) {
            CustomerDoctorPageVo pageVo = pageVoConvert.toPageVo(doctorPage, CustomerDoctorPageVo.class, DoctorConvert::toCustomerDoctorVo);
            if (CollectionUtils.isEmpty(pageVo.getList())) {
                return pageVo;
            }

            List<Long> hospitalIds = doctorPage.getResult().stream().map(Doctor::getHospitalId).collect(Collectors.toList());
            List<Long> hospitalAreaIds = doctorPage.getResult().stream().map(Doctor::getHospitalAreaId).collect(Collectors.toList());
            List<Hospital> hospitals = hospitalQuery.queryBy(hospitalIds, hospitalAreaIds);
            List<Department> departments = departmentQuery.queryByHospitalIds(hospitalIds);

            if (!CollectionUtils.isEmpty(hospitals)) {
                pageVo.getList().forEach(q -> importHospitalAndDepartmentInfo(q, hospitals, departments));
            }

            Map<Long, Doctor> doctorMap = doctorPage.stream()
                    .collect(Collectors.toMap(Doctor::getId, entity -> entity));
            pageVo.getList().forEach(vo -> {
                setDictInfoHelper.setDictInfo(doctorMap.get(vo.getId()), vo);
                vo.setBelongDoctorGroups(doctorQuery.hasDoctorGroup(vo.getId()));
            });
            return pageVo;
        }
    }

    @Override
    public CustomerDoctorDetailVo getDetailByCode(String hospitalAreaCode, String departmentCode, String doctorCode) {
        Doctor doctor = doctorQuery.queryByCode(hospitalAreaCode, departmentCode, doctorCode);
        if (null == doctor || !Boolean.TRUE.equals(doctor.getDisplay())) {
            throw new BizException(HttpStatus.NOT_FOUND, "医生不存在");
        }

        CustomerDoctorDetailVo vo = DoctorConvert.toCustomerDoctorDetailVo(doctor);
        importDoctorInfo(vo);
        setDictInfoHelper.setDictInfo(doctor, vo);

        Optional<AppointmentRuleSetting> ruleSetting = hospitalAreaSettingQuery.queryAppointmentRuleSettingById(null, null, vo.getHospitalAreaId());
        ruleSetting.ifPresent(appointmentRuleSetting -> vo.setAppointmentRule(appointmentRuleSetting.getAppointmentRule()));
        return vo;
    }

    @Override
    public CustomerDoctorAskServiceVo getAskServiceList(Long doctorId) {
        Doctor doctor = doctorQuery.queryDoctorById(doctorId);
        if (null == doctor || !Boolean.TRUE.equals(doctor.getDisplay())) {
            throw new BizException(HttpStatus.NOT_FOUND, "医生不存在");
        }
        Page<DoctorGroupRelation> doctorGroupRelations = doctorQuery.pageQueryDoctorGroupRelation(new DoctorQuery.DoctorGroupRelationQueryOption(1, Integer.MAX_VALUE)
                .setDoctorId(doctorId)
        );
        List<CustomerDoctorGroupVo> doctorGroupVos = doctorGroupRelations.stream()
                .map(relation -> {
                    DoctorGroupServiceRespDto groupService = doctorGroupClient.getDoctorGroupService(relation.getDoctorGroupId());

                    CustomerDoctorGroupVo groupVo = new CustomerDoctorGroupVo();
                    groupVo.setId(groupService.getId());
                    groupVo.setName(groupService.getName());
                    groupVo.setFree(groupService.getFree());
                    groupVo.setFreeStartTime(groupService.getFreeStartTime());
                    groupVo.setFreeEndTime(groupService.getFreeEndTime());
                    groupVo.setCount(groupService.getCount());
                    groupVo.setServices(groupService.getServices().stream()
                            .map(service -> {
                                CustomerDoctorGroupServiceVo serviceVo = new CustomerDoctorGroupServiceVo();
                                serviceVo.setId(service.getId());
                                serviceVo.setName(service.getName());
                                serviceVo.setPrice(service.getPrice().doubleValue());
                                serviceVo.setDescription(service.getDescription());
                                serviceVo.setType(service.getType());
                                serviceVo.setDuration(service.getDuration());
                                serviceVo.setTimes(service.getTimes());
                                serviceVo.setStatus(service.getStatus());
                                return serviceVo;
                            })
                            .collect(Collectors.toList())
                    );
                    return groupVo;
                })
                .collect(Collectors.toList());
        return new CustomerDoctorAskServiceVo()
                .doctorId(doctorId)
                .doctorCode(doctor.getThrdpartDoctorCode())
                .doctorGroups(doctorGroupVos);
    }


    @Override
    public CustomerDoctorScheduledDepartmentVo queryDoctorScheduledDepartment(Long baseDoctorId) {
        QueryBaseDoctorDepartmentRespDto queryBaseDoctorDepartmentRespDto = doctorClient.queryBaseDoctorDepartment(baseDoctorId);
        if (null == queryBaseDoctorDepartmentRespDto) {
            throw new BizException(HttpStatus.NOT_FOUND, "医生主页不存在");
        }

        Map<String, Pair<Doctor, List<Department>>> doctorCodeDepartmentMap = new HashMap<>();
        queryDoctorAndDepartment(queryBaseDoctorDepartmentRespDto.getDepartmentInfoList(), doctorCodeDepartmentMap);

        Set<String> hospitalCodeSet = queryBaseDoctorDepartmentRespDto.getDepartmentInfoList().stream().map(QueryBaseDoctorDepartmentRespDto.BaseDepartmentInfoDto::getHospitalAreaCode).collect(Collectors.toSet());
        List<AppointmentRuleSetting> appointmentRuleSettings = hospitalAreaSettingQuery.queryAppointmentRuleSettingBy(hospitalCodeSet);

        List<CustomerScheduledDepartmentVo> departmentVos = new ArrayList<>();
        doctorCodeDepartmentMap.values().forEach(pair -> pair.getSecond().forEach(department -> {
            Doctor doctor = pair.getFirst();

            CustomerScheduledDepartmentVo scheduledDepartmentVo = new CustomerScheduledDepartmentVo();
            scheduledDepartmentVo.setDepartmentId(department.getId());
            scheduledDepartmentVo.setDepartmentName(department.getName());
            scheduledDepartmentVo.setDepartmentCode(department.getThrdpartDepCode());

            AppointmentRuleSetting departmentAppointmentRuleSetting = appointmentRuleSettings.stream().filter(q -> q.getHospitalCode().equals(department.getHospitalCode())).findFirst().orElse(null);
            if (departmentAppointmentRuleSetting == null) {
                throw new BizException(HttpStatus.NOT_FOUND, "医院院区预约规则不存在, hospitalCode: " + department.getHospitalCode());
            }


            Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaById(department.getHospitalAreaId());
            if (null == hospitalArea) {
                return;
            }

            scheduledDepartmentVo.setDoctorAppointmentNotice(pair.getFirst().getAppointmentNotice());
            scheduledDepartmentVo.setDoctorCategory(pair.getFirst().getCategory());
            if (StringUtils.hasText(pair.getFirst().getRankDictLabel())) {
                scheduledDepartmentVo.setRankDictLabel(Arrays.asList(pair.getFirst().getRankDictLabel().split(",")));
            }
            if (StringUtils.hasText(pair.getFirst().getRankDictValue())) {
                scheduledDepartmentVo.setRankDictValue(Arrays.asList(pair.getFirst().getRankDictValue().split(",")));
            }
            scheduledDepartmentVo.setHospitalId(hospitalArea.getParentId());
            scheduledDepartmentVo.setHospitalAreaId(hospitalArea.getId());
            scheduledDepartmentVo.setHospitalAreaName(hospitalArea.getName());
            scheduledDepartmentVo.setHospitalAreaCode(hospitalArea.getHospitalCode());
            scheduledDepartmentVo.setDoctorId(doctor.getId());
            scheduledDepartmentVo.setDoctorCode(doctor.getThrdpartDoctorCode());
            scheduledDepartmentVo.setSort(department.getSort() == null ? 0 : department.getSort());

            hospitalAreaSettingQuery.queryAppointmentRuleSettingBy(hospitalArea.getId()).ifPresent(appointmentRuleSetting -> {
                scheduledDepartmentVo.setHospitalAreaAppointmentNotice(appointmentRuleSetting.getAppointmentRule());
            });

            departmentVos.add(scheduledDepartmentVo);
        }));

        List<CustomerScheduledDepartmentVo> sortedDepartmentVos = departmentVos.stream().sorted(Comparator.comparing(CustomerScheduledDepartmentVo::getSort).reversed()).collect(Collectors.toList());
        CustomerScheduledDepartmentVo first = null;
        if (!CollectionUtils.isEmpty(sortedDepartmentVos)) {
            first = sortedDepartmentVos.get(0);
        }

        return new CustomerDoctorScheduledDepartmentVo()
                .id(queryBaseDoctorDepartmentRespDto.getDoctorInfo().getId())
                .name(queryBaseDoctorDepartmentRespDto.getDoctorInfo().getName())
                .headImgUrl(queryBaseDoctorDepartmentRespDto.getDoctorInfo().getAvatar())
                .level(queryBaseDoctorDepartmentRespDto.getDoctorInfo().getLevel())
                .speciality(queryBaseDoctorDepartmentRespDto.getDoctorInfo().getSpeciality())
                .summary(queryBaseDoctorDepartmentRespDto.getDoctorInfo().getSummary())
                .hospitalAreaName(null != first ? first.getHospitalAreaName() : "")
                .departmentName(null != first ? first.getDepartmentName() : "")
                .departmentScheduleList(sortedDepartmentVos);
    }

    @Override
    public Optional<Doctor> queryDoctorByCode(String hospitalAreaCode, String departmentCode, String doctorCode) {
        return Optional.ofNullable(doctorQuery.queryByCode(hospitalAreaCode, departmentCode, doctorCode));
    }

    private void queryDoctorAndDepartment(List<QueryBaseDoctorDepartmentRespDto.BaseDepartmentInfoDto> baseDepartmentInfoDtos, Map<String, Pair<Doctor, List<Department>>> doctorCodeDepartmentMap) {
        baseDepartmentInfoDtos
                .stream()
                .filter(QueryBaseDoctorDepartmentRespDto.BaseDepartmentInfoDto::getEnabled)
                .forEach(departmentInfo -> {
                    Doctor doctor = doctorQuery.queryByCode(departmentInfo.getHospitalAreaCode(), departmentInfo.getDepartmentCode(), departmentInfo.getDoctorCode());
                    if (null == doctor) {
                        return;
                    }

                    Department department = departmentQuery.queryBy(departmentInfo.getHospitalAreaCode(), departmentInfo.getDepartmentCode());
                    if (null == department) {
                        return;
                    }
                    if (doctorCodeDepartmentMap.containsKey(departmentInfo.getDoctorCode())) {
                        doctorCodeDepartmentMap.get(departmentInfo.getDoctorCode()).getSecond().add(department);
                        return;
                    }

                    List<Department> departments = new ArrayList<>();
                    departments.add(department);
                    doctorCodeDepartmentMap.put(departmentInfo.getDoctorCode(), Pair.of(doctor, departments));
                });
    }

    private <T extends CustomerDoctorVo> void importDoctorInfo(T vo) {
        if (null != vo.getHospitalId()) {
            Hospital hospital = hospitalQuery.queryHospitalById(vo.getHospitalId());
            if (null == hospital) {
                throw new BizException(HttpStatus.NOT_FOUND, "医生所属医院不存在");
            }
            vo.setHospitalName(hospital.getName());
        }
        if (null != vo.getHospitalAreaId()) {
            Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaById(vo.getHospitalAreaId());
            if (null == hospitalArea) {
                throw new BizException(HttpStatus.NOT_FOUND, "医生所属院区不存在");
            }
            vo.setHospitalAreaName(hospitalArea.getName());
            vo.setHospitalAreaCode(hospitalArea.getHospitalCode());
        }
        if (null != vo.getDepartmentId()) {
            Department department = departmentQuery.queryDepartmentById(vo.getDepartmentId());
            if (null == department) {
                throw new BizException(HttpStatus.NOT_FOUND, "医生所属科室不存在");
            }
            if (HospitalUtils.isKunmingMU1stHospital(department.getHospitalCode())) {
                vo.setDisplayDepartmentName(null);
                vo.setDepartmentName(getDptName4KunmingMU1st(department));
            } else {
                vo.setDepartmentName(department.getName());
            }
        }
    }

    private String getDptName4KunmingMU1st(Department department) {
        Department shadowDepartment = departmentQuery.queryShadowDepartment(department.getHospitalCode(), department.getThrdpartDepCode());
        if (shadowDepartment == null) {
            return department.getName();
        }
        return shadowDepartment.getName();
    }


    private void importHospitalAndDepartmentInfo(CustomerDoctorVo doctorVo, List<Hospital> hospitals, List<Department> departments) {
        hospitals.stream().filter(h -> h.getId().equals(doctorVo.getHospitalId())).findFirst().ifPresent(hospital -> doctorVo.setHospitalName(hospital.getName()));
        hospitals.stream().filter(h -> h.getId().equals(doctorVo.getHospitalAreaId())).findFirst().ifPresent(hospital -> doctorVo.setHospitalAreaName(hospital.getName()));
        departments.stream().filter(d -> d.getId().equals(doctorVo.getDepartmentId())).findFirst().ifPresent(department -> doctorVo.setDepartmentName(department.getName()));
    }

}
