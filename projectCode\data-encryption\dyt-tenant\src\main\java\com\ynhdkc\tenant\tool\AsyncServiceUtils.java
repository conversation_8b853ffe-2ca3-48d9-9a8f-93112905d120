package com.ynhdkc.tenant.tool;

import com.ynhdkc.tenant.dao.DoctorRepository;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import com.ynhdkc.tenant.service.customer.IHisGatewayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AsyncServiceUtils {
    @Resource
    @Lazy
    private IHisGatewayService hisGatewayService;

    @Resource
    @Lazy
    private DoctorRepository doctorRepository;

    @Async
    public void updateDoctorInfoFromDepartmentDoctorItemAsync(Doctor item, Doctor doctor, Department department, AppointmentRuleSetting appointmentRuleSetting) {
        try {
            hisGatewayService.updateDoctorInfoFromDepartmentDoctorItem(item, doctor, department, appointmentRuleSetting);
        } catch (Exception e) {
            log.error("Error updating doctor info asynchronously: doctorId={}", doctor.getId(), e);
        }
    }

    @Async
    public void disabledDoctors(List<Doctor> dbDoctors, List<Doctor> doctorsFromHis) {
        Set<String> thrdpartDoctorCodeSet = doctorsFromHis.stream()
                .map(Doctor::getThrdpartDoctorCode)
                .collect(Collectors.toSet());
        Date now = new Date();
        dbDoctors.stream()
                .filter(dbDoctor -> !thrdpartDoctorCodeSet.contains(dbDoctor.getThrdpartDoctorCode()))
                .forEach(doctor -> {
                    doctor.setStatus(1);
                    doctor.setUpdateTime(now);
                    doctorRepository.update(doctor);
                });
    }
}
