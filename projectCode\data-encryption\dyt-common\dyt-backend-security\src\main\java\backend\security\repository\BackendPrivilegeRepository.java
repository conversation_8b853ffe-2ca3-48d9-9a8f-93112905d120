package backend.security.repository;

import backend.security.entity.OrganizationStructureTree;
import backend.security.entity.Role;
import backend.security.entity.TenantUserAscription;
import backend.security.entity.TenantUserStructureTree;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/9/7 15:38
 */
public interface BackendPrivilegeRepository {
    void saveRole(Role role);

    void deleteRole(Long roleId);

    Role getRoleById(Long roleId);

    void saveOrganizationStructureTree(OrganizationStructureTree organizationStructureTree);

    OrganizationStructureTree getOrganizationStructureTree();

    void saveTenantUserStructureTree(TenantUserStructureTree tenantUserStructureTree);

    void deleteTenantUserStructureTree(Long userId);

    TenantUserStructureTree getTenantUserStructureTree(Long userId);

    void saveTenantUserAscription(TenantUserAscription userAscription);

    void deleteTenantUserAscription(Long userId);

    TenantUserAscription getTenantUserAscription(Long userId);
}
