package com.ynhdkc.tenant.config.yunda;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "yun-da-config")
public class YunDaConfig {

    private Avatar avatar;

    @Data
    public static class Avatar {

        private String url;
        
    }
}
