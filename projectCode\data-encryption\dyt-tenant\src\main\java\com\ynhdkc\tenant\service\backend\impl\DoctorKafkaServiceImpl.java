package com.ynhdkc.tenant.service.backend.impl;

import backend.common.kafka.KafkaPublisher;
import backend.common.kafka.constant.KafkaProperties;
import com.ynhdkc.tenant.model.DoctorKafkaVo;
import com.ynhdkc.tenant.service.backend.DoctorKafkaService;
import com.ynhdkc.tenant.service.backend.IDoctorService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-03 15:51
 */
@Service
@RequiredArgsConstructor
public class DoctorKafkaServiceImpl implements DoctorKafkaService {
    private final IDoctorService doctorService;

    private final KafkaPublisher kafkaPublisher;

    @Override
    public void syncAllDoctor() {
        List<DoctorKafkaVo> kafkaVos = doctorService.queryAllForKafka();
        if (CollectionUtils.isEmpty(kafkaVos)) {
            return;
        }

        syncDoctorInfoTask(kafkaVos);
    }

    private void syncDoctorInfoTask(List<DoctorKafkaVo> kafkaVos) {
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(4, 8, 60, TimeUnit.SECONDS, new ArrayBlockingQueue<>(kafkaVos.size()));
        kafkaVos.forEach(kafkaVo -> threadPoolExecutor.execute(() -> sendMessage(kafkaVo.getId(), kafkaVo)));

        while (true) {
            if (threadPoolExecutor.getCompletedTaskCount() == kafkaVos.size()) {
                threadPoolExecutor.shutdown();
                break;
            }
        }
    }

    private void sendMessage(Long doctorId, DoctorKafkaVo kafkaVo) {
        kafkaPublisher.publishMsg(KafkaProperties.DOCTOR_TOPIC_NAME, doctorId.toString(), kafkaVo);
    }

    @Override
    public void syncDoctor(Long doctorId) {
        sendMessage(doctorId, doctorService.getDetailForKafka(doctorId));
    }

    @Override
    public void syncDoctorBy(Long departmentId) {
        List<DoctorKafkaVo> kafkaVos = doctorService.queryByDepartmentIdForKafkaVo(departmentId);
        if (CollectionUtils.isEmpty(kafkaVos)) {
            return;
        }
        syncDoctorInfoTask(kafkaVos);
    }

    @Override
    public void deleteDoctor(Long doctorId) {
        sendMessage(doctorId, null);
    }

    @Override
    public void requestDepartmentDoctors(String thirdPartDepartmentCode) {

    }
}
