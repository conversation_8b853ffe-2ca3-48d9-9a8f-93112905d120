package com.ynhdkc.tenant.api.customer;

import backend.common.exception.BizException;
import com.ynhdkc.tenant.handler.CustomerSearchApi;
import com.ynhdkc.tenant.model.SearchDepartmentAndDoctorReqDto;
import com.ynhdkc.tenant.model.SearchDepartmentAndDoctorVo;
import com.ynhdkc.tenant.model.SearchHospitalReqDto;
import com.ynhdkc.tenant.model.SearchHospitalVo;
import com.ynhdkc.tenant.service.customer.CustomerSearchService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/8/10 15:49
 */
@RestController
@RequiredArgsConstructor
public class CustomerSearchController implements CustomerSearchApi {
    private final CustomerSearchService customerSearchService;

    @Override
    public ResponseEntity<SearchDepartmentAndDoctorVo> searchDepartmentAndDoctor(SearchDepartmentAndDoctorReqDto request) {
        switch (request.getType()) {
            case 0: {
                if (null == request.isDepartmentEnabled()) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "科室是否启用不能为空");
                }
                if (null == request.getDoctorStatus()) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "医生状态不能为空");
                }
                break;
            }
            case 1: {
                if (null == request.isDepartmentEnabled()) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "科室是否启用不能为空");
                }
                break;
            }
            case 2: {
                if (null == request.getDoctorStatus()) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "医生状态不能为空");
                }
                break;
            }
            default:
                throw new BizException(HttpStatus.BAD_REQUEST, "搜索类型错误");
        }
        return ResponseEntity.ok(customerSearchService.searchDepartmentAndDoctor(request));
    }

    @Override
    public ResponseEntity<SearchHospitalVo> searchHospital(SearchHospitalReqDto request) {
        return ResponseEntity.ok(customerSearchService.searchHospital(request));
    }
}
