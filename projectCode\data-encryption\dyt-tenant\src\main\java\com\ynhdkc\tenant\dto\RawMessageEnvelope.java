package com.ynhdkc.tenant.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/7/18 15:39:36
 */
@Data
public class RawMessageEnvelope {

    @JsonProperty("hospital_id")
    private Long hospitalId;

    @JsonProperty("hospital_area_id")
    private Long hospitalAreaId;

    @JsonProperty("department_id")
    private Long departmentId;

    @JsonProperty("pay_load")
    private PayLoad payLoad;

    @Data
    public static class PayLoad {
        @JsonProperty("hospital_code")
        public String hospitalCode;

        @JsonProperty("department_code")
        public String departmentCode;

        @JsonProperty("department_id")
        public Long departmentId;
    }
}
