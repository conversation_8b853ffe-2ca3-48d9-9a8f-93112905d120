create table t_building_floor
(
    id               bigint auto_increment comment '全局唯一标识'
        primary key,
    tenant_id        bigint                                   not null comment '租户 ID',
    hospital_id      bigint                                   not null comment '医院 ID',
    hospital_code    varchar(20)                              null comment '医院编码',
    hospital_area_id bigint                                   not null comment '院区 ID',
    building_id      bigint                                   not null comment '大楼 ID',
    name             varchar(20)                              not null comment '楼层名称',
    picture_urls     varchar(1000)                            null comment '楼层图片，通过逗号分隔',
    sort int default 0 null comment '排序',
    status           int         default 0                    not null comment '状态，0:正常，1:禁用',
    create_time      datetime(3) default CURRENT_TIMESTAMP(3) not null,
    update_time      datetime(3)                              null on update CURRENT_TIMESTAMP(3)
)
    comment '楼层表';