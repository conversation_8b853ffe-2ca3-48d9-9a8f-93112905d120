package com.ynhdkc.tenant.service.backend;

import com.ynhdkc.tenant.entity.Department;

/**
 * <AUTHOR>
 * @since 2023/7/11 16:55:06
 */
public interface IHisRequestService {

    void requestDepartmentDoctorsFromHis(Department department);

    void requestDepartmentsFromHis();

    void syncDoctorSchedule();

    void syncDoctorSchedule(String hospitalCode);

    void syncHospitalDepartmentDoctorInMorning();

    void requestDepartmentsFromHisBy(Long hospitalAreaId);

    void requestOneDepartment(String hospitalCode);

    void requestGongRenDepartmentsFromHis();
}
