package com.ynhdkc.tenant.entity;

import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @since 2023/2/24 11:06:52
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "t_floor_area")
public class Area extends BaseEntity {

    private Long tenantId;

    private Long hospitalId;

    private Long hospitalAreaId;

    private String hospitalCode;

    private Long buildingId;

    private Long floorId;

    private String name;

    private String pictureUrls;

    private Integer sort;

    private Integer status;
}
