package com.ynhdkc.tenant.api.rpc;

import com.ynhdkc.tenant.handler.RpcHospitalApi;
import com.ynhdkc.tenant.model.HospitalVo;
import com.ynhdkc.tenant.model.RpcBatchGetByIdReqDto;
import com.ynhdkc.tenant.service.backend.HospitalService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-23 10:48
 */
@Api(tags = "RpcHospital")
@RestController
@RequiredArgsConstructor
public class RpcHospitalController implements RpcHospitalApi {
    private final HospitalService hospitalService;

    @Override
    public ResponseEntity<List<HospitalVo>> batchGetHospitalDetail(RpcBatchGetByIdReqDto request) {
        return ResponseEntity.ok(hospitalService.rpcBatchGetDetail(request));
    }

    @Override
    public ResponseEntity<HospitalVo> getHospitalDetail(Long id) {
        return ResponseEntity.ok(hospitalService.rpcGetDetail(id));
    }
}
