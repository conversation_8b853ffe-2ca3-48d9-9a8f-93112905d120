package com.ynhdkc.tenant.service.backend.impl;

import backend.common.densensitized.enums.PrivacyType;
import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import backend.security.service.BackendTenantUserService;
import backend.security.service.impl.DesensitizationWhiteListUtils;
import backend.security.until.DesensitizedUtils;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.dao.TenantQuery;
import com.ynhdkc.tenant.dao.TenantRepository;
import com.ynhdkc.tenant.entity.Tenant;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.AddressService;
import com.ynhdkc.tenant.service.backend.TenantService;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/2/14 16:20:52
 */
@Service
@RequiredArgsConstructor
public class TenantServiceImpl implements TenantService {
    private final TenantQuery tenantQuery;
    private final TenantRepository tenantRepository;

    private final AddressService addressService;

    private final BackendTenantUserService backendTenantUserService;
    private final DesensitizationWhiteListUtils desensitizationWhiteListUtils;
    private final PageVoConvert pageVoConvert;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TenantDetailVo create(TenantCreateReqDto request) {
        if (tenantQuery.countByName(request.getName()) > 0) {
            throw new BizException(HttpStatus.CONFLICT, "租户名称已存在", request.getName());
        }
        Tenant tenant = TenantService.toTenant(request);

        if (request.getAddress() != null) {
            Long addressId = addressService.create(request.getAddress());
            tenant.setAddressId(addressId);
        }

        if (request.getContactAddress() != null) {
            Long contactAddressId = addressService.create(request.getContactAddress());
            tenant.setContactAddressId(contactAddressId);
        }
        tenantRepository.create(tenant);

        TenantDetailVo vo = TenantService.toTenantDetailVo(tenant);
        importAddresses(vo);
        return vo;
    }

    @Override
    public TenantDetailVo getDetail(Long tenantId) {
        Tenant tenant = tenantQuery.queryTenantById(tenantId);
        if (tenant == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "租户不存在", tenantId);
        }

        if (!backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.TenantId(tenantId))) {
            throw new BizException(HttpStatus.FORBIDDEN, "用户无访问该租户权限");
        }

        TenantDetailVo vo = TenantService.toTenantDetailVo(tenant);
        importAddresses(vo);
        return vo;
    }

    @Override
    public TenantDetailVo rpcGetDetail(Long tenantId) {
        Tenant tenant = tenantQuery.queryTenantById(tenantId);
        if (tenant == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "租户不存在", tenantId);
        }
        TenantDetailVo vo = TenantService.toTenantDetailVo(tenant);
        importAddresses(vo);
        return vo;
    }

    @Override
    public TenantPageVo query(TenantQueryReqDto request) {
        Set<Long> tenantIds = backendTenantUserService.getCurrentUserTenantReadRange(true);
        TenantQuery.TenantQueryOption option = new TenantQuery.TenantQueryOption(request.getCurrentPage(), request.getPageSize())
                .setName(request.getName())
                .setContactPerson(request.getContactPerson())
                .setContactPhoneNumber(request.getContactPhoneNumber())
                .setIncludeIds(tenantIds);

        try (Page<Tenant> tenantPage = tenantQuery.pageQueryTenant(option)) {
            TenantPageVo pageVo = pageVoConvert.toPageVo(tenantPage, TenantPageVo.class, TenantService::toTenantVo);
            pageVo.getList().forEach(item -> {
                importAddresses(item);
                if (desensitizationWhiteListUtils.needDesensitization(backendTenantUserService.getCurrentUserId())) {
                    item.setContactPhoneNumber(DesensitizedUtils.desensitize(PrivacyType.MOBILE_PHONE, item.getContactPhoneNumber()));
                    item.setContactEmail(DesensitizedUtils.desensitize(PrivacyType.EMAIL, item.getContactEmail()));
                    item.setContact(DesensitizedUtils.desensitize(PrivacyType.CHINESE_NAME, item.getContact()));
                    item.getAddress().detail(DesensitizedUtils.desensitize(PrivacyType.ADDRESS, item.getAddress().getDetail()));
                }
            });
            return pageVo;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TenantDetailVo update(Long tenantId, TenantUpdateReqDto request) {
        Tenant tenant = tenantQuery.queryTenantById(tenantId);
        if (tenant == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "租户不存在");
        }

        if (request.getAddress() != null) {
            if (tenant.getAddressId() != null) {
                addressService.update(tenant.getAddressId(), request.getAddress());
            } else {
                Long addressId = addressService.create(request.getAddress());
                tenant.setAddressId(addressId);
            }
        }
        if (request.getContactAddress() != null) {
            if (tenant.getContactAddressId() != null) {
                addressService.update(tenant.getContactAddressId(), request.getContactAddress());
            } else {
                Long contactAddressId = addressService.create(request.getContactAddress());
                tenant.setContactAddressId(contactAddressId);
            }
        }
        updateIfNotNull(tenant, request);

        tenantRepository.update(tenant);

        TenantDetailVo vo = TenantService.toTenantDetailVo(tenant);
        importAddresses(vo);
        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseOperationResponse delete(Long tenantId) {
        Tenant tenant = tenantQuery.queryTenantById(tenantId);
        if (tenant == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "租户不存在");
        }
        tenantRepository.delete(tenantId);

        return new BaseOperationResponse("删除成功");
    }

    private void updateIfNotNull(Tenant entity, TenantUpdateReqDto dto) {
        if (null != dto.getName()) {
            entity.setName(dto.getName());
        }
        if (null != dto.getDescription()) {
            entity.setDescription(dto.getDescription());
        }
        if (null != dto.getContact()) {
            entity.setContact(dto.getContact());
        }
        if (null != dto.getContactPhoneNumber()) {
            entity.setContactPhoneNumber(dto.getContactPhoneNumber());
        }
        if (null != dto.getLogoUrl()) {
            entity.setLogoUrl(dto.getLogoUrl());
        }
        if (null != dto.getContactEmail()) {
            entity.setContactEmail(dto.getContactEmail());
        }
        if (null != dto.getContactWechat()) {
            entity.setContactWechat(dto.getContactWechat());
        }
    }

    private void importAddresses(TenantVo vo) {
        if (null != vo.getAddressId()) {
            vo.setAddress(addressService.getAddressVoById(vo.getAddressId()));
        }
        if (vo instanceof TenantDetailVo && null != vo.getContactAddressId()) {
            ((TenantDetailVo) vo).setContactAddress(addressService.getAddressVoById(vo.getContactAddressId()));
        }
    }
}

