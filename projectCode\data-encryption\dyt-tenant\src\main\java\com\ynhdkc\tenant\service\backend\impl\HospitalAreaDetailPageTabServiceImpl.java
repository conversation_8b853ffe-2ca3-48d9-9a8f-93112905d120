package com.ynhdkc.tenant.service.backend.impl;

import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ynhdkc.tenant.dao.mapper.IHospitalDetailPageTabMapper;
import com.ynhdkc.tenant.entity.detailpage.HospitalAreaDetailPageTab;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.IHospitalAreaDetailPageTabService;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/24 19:53:21
 */
@Service
@RequiredArgsConstructor
public class HospitalAreaDetailPageTabServiceImpl implements IHospitalAreaDetailPageTabService {

    private final IHospitalDetailPageTabMapper mapper;
    private final PageVoConvert pageVoConvert;

    @Override
    public HospitalAreaDetailPageTabVo createHospitalAreaDetailPageTab(CreateHospitalAreaDetailPageTabReqDto dto) {
        HospitalAreaDetailPageTab hospitalAreaDetailPageTab = toEntity(dto);
        int effectiveRows = mapper.insertSelective(hospitalAreaDetailPageTab);
        if (effectiveRows == 0) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "创建失败");
        }
        return IHospitalAreaDetailPageTabService.toVo(hospitalAreaDetailPageTab);
    }

    @Override
    public BaseOperationResponse deleteHospitalAreaDetailPageTab(Long id) {
        int effectiveRows = mapper.deleteByPrimaryKey(id);
        if (effectiveRows == 0) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "删除失败");
        }
        return BaseOperationResponse.builder().effectiveCount(effectiveRows).build();
    }

    @Override
    public HospitalAreaDetailPageTabVo updateHospitalAreaDetailPageTab(Long tabId, UpdateHospitalAreaDetailPageTabReqDto dto) {
        HospitalAreaDetailPageTab entity = mapper.selectByPrimaryKey(tabId);
        updateEntity(entity, dto);
        mapper.updateByPrimaryKeySelective(entity);
        return IHospitalAreaDetailPageTabService.toVo(entity);
    }

    @Override
    public HospitalAreaDetailPageTabVo getHospitalAreaDetailPageTab(Long id) {
        HospitalAreaDetailPageTab hospitalAreaDetailPageTab = mapper.selectByPrimaryKey(id);
        if (hospitalAreaDetailPageTab == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "查询失败");
        }
        return IHospitalAreaDetailPageTabService.toVo(hospitalAreaDetailPageTab);
    }

    @Override
    public HospitalAreaDetailPageTabPageVo searchHospitalAreaDetailPageTab(SearchHospitalAreaDetailPageTabReqDto dto) {
        Page<HospitalAreaDetailPageTab> page = PageHelper.startPage(dto.getCurrentPage(), dto.getPageSize());
        page.doSelectPage(() -> mapper.selectByExample(HospitalAreaDetailPageTab.class, helper -> {
            helper.defGroup(consumer -> {
                if (dto.getTenantId() != null) {
                    consumer.andEqualTo(HospitalAreaDetailPageTab::getTenantId, dto.getTenantId());
                }
                if (dto.getHospitalId() != null) {
                    consumer.andEqualTo(HospitalAreaDetailPageTab::getHospitalId, dto.getHospitalId());
                }
                if (dto.getHospitalAreaId() != null) {
                    consumer.andEqualTo(HospitalAreaDetailPageTab::getHospitalAreaId, dto.getHospitalAreaId());
                }
                if (!StringUtils.isEmpty(dto.getTitle())) {
                    consumer.andLike(HospitalAreaDetailPageTab::getTitle, "%" + dto.getTitle() + "%");
                }
                if (dto.getComponentType() != null) {
                    consumer.andEqualTo(HospitalAreaDetailPageTab::getComponentType, dto.getComponentType());
                }
            });
            helper.builder(builder -> builder.orderByDesc(HospitalAreaDetailPageTab::getSort));
        }));
        return pageVoConvert.toPageVo(page, HospitalAreaDetailPageTabPageVo.class, IHospitalAreaDetailPageTabService::toVo);
    }

    @Override
    public List<HospitalAreaDetailPageTabVo> getHospitalAreaDetailPageTabByHospitalAreaId(Long hospitalAreaId) {
        List<HospitalAreaDetailPageTab> hospitalAreaDetailPageTabs = mapper.selectByExample(HospitalAreaDetailPageTab.class, helper -> {
            helper.defGroup(consumer -> consumer.andEqualTo(HospitalAreaDetailPageTab::getHospitalAreaId, hospitalAreaId));
            helper.builder(builder -> builder.orderByDesc(HospitalAreaDetailPageTab::getSort));
        });

        return hospitalAreaDetailPageTabs.stream().map(IHospitalAreaDetailPageTabService::toVo).collect(Collectors.toList());
    }
}
