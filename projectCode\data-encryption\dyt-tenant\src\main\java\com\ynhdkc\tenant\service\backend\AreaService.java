package com.ynhdkc.tenant.service.backend;

import backend.common.response.BaseOperationResponse;
import backend.common.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.ynhdkc.tenant.entity.Area;
import com.ynhdkc.tenant.model.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-04 9:59
 */
public interface AreaService {
    AreaVo create(AreaCreateReqDto request);

    AreaVo update(Long areaId, AreaUpdateReqDto request);

    BaseOperationResponse delete(Long areaId);

    AreaVo getDetail(Long areaId);

    AreaPageVo query(AreaQueryReqDto request);

    static Area toArea(AreaCreateReqDto dto) {
        Area entity = new Area();
        entity.setTenantId(dto.getTenantId());
        entity.setHospitalId(dto.getHospitalId());
        entity.setHospitalAreaId(dto.getHospitalAreaId());
        entity.setHospitalCode(dto.getHospitalCode());
        entity.setBuildingId(dto.getBuildingId());
        entity.setFloorId(dto.getFloorId());
        entity.setName(dto.getName());
        entity.setSort(dto.getSort());
        entity.setStatus(dto.getStatus());

        if (null != dto.getPictureUrls()) {
            entity.setPictureUrls(JsonUtil.serializeObject(dto.getPictureUrls()));
        }
        return entity;
    }

    static AreaVo toAreaVo(Area entity) {
        AreaVo vo = new AreaVo();
        vo.setId(entity.getId());
        vo.setTenantId(entity.getTenantId());
        vo.setHospitalId(entity.getHospitalId());
        vo.setHospitalAreaId(entity.getHospitalAreaId());
        vo.setHospitalCode(entity.getHospitalCode());
        vo.setBuildingId(entity.getBuildingId());
        vo.setFloorId(entity.getFloorId());
        vo.setName(entity.getName());
        vo.setSort(entity.getSort());
        vo.setStatus(entity.getStatus());
        vo.setCreateTime(entity.getCreateTime());
        if (null != entity.getPictureUrls()) {
            vo.setPictureUrls(JsonUtil.deserializeObject(entity.getPictureUrls(), new TypeReference<List<String>>() {
            }));
        }
        return vo;
    }
}
