package com.ynhdkc.tenant.service.backend.impl;

import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ynhdkc.tenant.dao.mapper.HospitalDetailPageCubeMapper;
import com.ynhdkc.tenant.dao.mapper.HospitalDetailPageCubeModuleMapper;
import com.ynhdkc.tenant.entity.detailpage.HospitalAreaDetailPageCubeModule;
import com.ynhdkc.tenant.entity.detailpage.HospitalAreaDetailPageCube;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.IHospitalAreaDetailPageCubeService;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/24 19:15:59
 */
@Service
@RequiredArgsConstructor
public class HospitalAreaDetailPageCubeServiceImpl implements IHospitalAreaDetailPageCubeService {

    private final HospitalDetailPageCubeMapper mapper;
    private final PageVoConvert pageVoConvert;
    private final HospitalDetailPageCubeModuleMapper cubeModuleMapper;

    @Override
    public HospitalAreaDetailPageCubeVo createHospitalAreaDetailPageCube(CreateHospitalAreaDetailPageCubeReqDto dto) {
        validateCubeModuleIdExistence(dto.getCubeModuleId());

        HospitalAreaDetailPageCube entity = toEntity(dto);
        int effectiveCount = mapper.insertSelective(entity);
        if (effectiveCount == 0) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "创建失败");
        }
        return IHospitalAreaDetailPageCubeService.toVo(entity);
    }

    private void validateCubeModuleIdExistence(Long cubeModuleId) {
        if (cubeModuleId == null) {
            throw new BizException(HttpStatus.BAD_REQUEST, "魔方模块id不能为空");
        }
        int count = cubeModuleMapper.selectCountByExample(HospitalAreaDetailPageCubeModule.class, q -> q.defGroup(consumer -> consumer.andEqualTo(HospitalAreaDetailPageCubeModule::getId, cubeModuleId)));
        if (count == 0) {
            throw new BizException(HttpStatus.BAD_REQUEST, "魔方模块不存在");
        }
    }

    @Override
    public BaseOperationResponse deleteHospitalAreaDetailPageCube(Long id) {
        mapper.deleteByPrimaryKey(id);
        return new BaseOperationResponse();
    }

    @Override
    public HospitalAreaDetailPageCubeVo getHospitalAreaDetailPageCube(Long id) {
        HospitalAreaDetailPageCube entity = mapper.selectByPrimaryKey(id);
        if (entity == null) {
            throw new BizException(HttpStatus.BAD_REQUEST, "魔方数据不存在");
        }
        return IHospitalAreaDetailPageCubeService.toVo(entity);
    }

    @Override
    public HospitalAreaDetailPageCubePageVo searchHospitalAreaDetailPageCube(SearchHospitalAreaDetailPageCubeReqDto dto) {
        Page<HospitalAreaDetailPageCube> page = PageHelper.startPage(dto.getCurrentPage(), dto.getPageSize());
        page.doSelectPage(() -> mapper.selectByExample2(HospitalAreaDetailPageCube.class, sql -> {
            if (dto.getTenantId() != null) {
                sql.andEqualTo(HospitalAreaDetailPageCube::getTenantId, dto.getTenantId());
            }
            if (dto.getHospitalId() != null) {
                sql.andEqualTo(HospitalAreaDetailPageCube::getHospitalId, dto.getHospitalId());
            }
            if (dto.getHospitalAreaId() != null) {
                sql.andEqualTo(HospitalAreaDetailPageCube::getHospitalAreaId, dto.getHospitalAreaId());
            }
            if (dto.getCubeModuleId() != null) {
                sql.andEqualTo(HospitalAreaDetailPageCube::getCubeModuleId, dto.getCubeModuleId());
            }
            if (!StringUtils.isEmpty(dto.getTitle())) {
                sql.andLike(HospitalAreaDetailPageCube::getTitle, "%" + dto.getTitle() + "%");
            }
        }));
        return pageVoConvert.toPageVo(page, HospitalAreaDetailPageCubePageVo.class, IHospitalAreaDetailPageCubeService::toVo);
    }

    @Override
    public HospitalAreaDetailPageCubeVo updateHospitalAreaDetailPageCube(Long id, UpdateHospitalAreaDetailPageCubeReqDto dto) {
        validateCubeModuleIdExistence(dto.getCubeModuleId());

        HospitalAreaDetailPageCube entity = mapper.selectByPrimaryKey(id);
        if (entity == null) {
            throw new BizException(HttpStatus.BAD_REQUEST, "魔方数据不存在");
        }

        toEntity(dto, entity);
        int effectiveCount = mapper.updateByPrimaryKeySelective(entity);
        if (effectiveCount == 0) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "更新失败");
        }
        return IHospitalAreaDetailPageCubeService.toVo(entity);
    }

    @Override
    public List<HospitalAreaDetailPageCubeVo> getCubeVosByIds(List<Long> cubeModuleIds) {
        return mapper.selectByExample2(HospitalAreaDetailPageCube.class, sql -> sql.andIn(HospitalAreaDetailPageCube::getCubeModuleId, cubeModuleIds)).stream().map(IHospitalAreaDetailPageCubeService::toVo).collect(Collectors.toList());
    }

    @Override
    public List<HospitalAreaDetailPageCubeVo> getCubeVosByCubeModuleId(Long cubeModuleId) {
        return mapper.selectByExample2(HospitalAreaDetailPageCube.class, sql -> sql.andEqualTo(HospitalAreaDetailPageCube::getCubeModuleId, cubeModuleId)).stream().map(IHospitalAreaDetailPageCubeService::toVo).collect(Collectors.toList());
    }
}
