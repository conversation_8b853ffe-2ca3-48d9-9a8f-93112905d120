package com.ynhdkc.tenant.entity;

import backend.common.domain.tenant.DepartmentEntity;
import cn.hutool.core.text.CharSequenceUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/22 15:09:15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "t_hospital_department")
public class Department extends DepartmentEntity {
    public static final Long NO_PARENT = 0L;

    private Long parentId;
    private String name;
    private String thrdpartDepCode;
    private String firstLetter;
    private String shortName;
    private String introduction;
    private Integer sort;
    private Boolean recommended;
    private String caution;
    /**
     * 科室地址
     */
    private String addressIntro;
    /**
     * 科室类别
     */
    private String category;
    private Integer systemDepends;
    private Boolean enabled;

    /**
     * 显示背景颜色
     */
    private String displayBgColor;

    private Boolean enableDepartmentDetail;

    private String uri;

    private Integer forbiddenDay;

    private Integer advanceDay;

    private String sourceActivateTime;

    private String remark;

    private String doctors;

    /**
     * 科室来源,0:默认，1:HIS
     */
    private Integer source;

    private Integer levelTag;
    private String triageDeskAddress;
    private Integer displayFields;
    private String displayDepartmentName;
    private String appointmentNotifyContact;
    private String noticeTemplateId;

    public List<Integer> getCategoryStr() {
        return CharSequenceUtil.splitTrim(this.category, ',').stream().map(Integer::valueOf).collect(Collectors.toList());
    }

    public void setCategoryStr(List<Integer> category) {
        this.category = CharSequenceUtil.join(",", category.stream().sorted().collect(Collectors.toList()));
    }
}
