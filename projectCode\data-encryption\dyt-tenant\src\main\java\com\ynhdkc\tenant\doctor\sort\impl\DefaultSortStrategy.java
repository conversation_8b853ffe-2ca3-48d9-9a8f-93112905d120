package com.ynhdkc.tenant.doctor.sort.impl;

import com.ynhdkc.tenant.doctor.sort.strategy.AbstractDoctorSortStrategy;
import com.ynhdkc.tenant.entity.Doctor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

@Component
public class DefaultSortStrategy extends AbstractDoctorSortStrategy {
    
    public DefaultSortStrategy() {
        super(Collections.emptySet());
    }

    @Override
    public void sort(List<Doctor> doctors) {
        doctors.sort(Comparator.comparing(Doctor::getSort).reversed());
    }
}
