package com.ynhdkc.tenant.dao.impl;

import com.ynhdkc.tenant.dao.HospitalAreaFunctionRepository;
import com.ynhdkc.tenant.dao.mapper.FunctionSettingMapper;
import com.ynhdkc.tenant.entity.setting.FunctionSetting;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-09 10:29
 */
@Repository
@RequiredArgsConstructor
public class HospitalAreaFunctionRepositoryImpl implements HospitalAreaFunctionRepository {
    private final FunctionSettingMapper functionSettingMapper;

    @Override
    public void create(FunctionSetting entity) {
        functionSettingMapper.insertSelective(entity);
    }

    @Override
    public void update(FunctionSetting entity) {
        functionSettingMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void delete(Long functionId) {
        functionSettingMapper.deleteByPrimaryKey(functionId);
    }

    @Override
    public List<FunctionSetting> queryByHospitalAreaId(Long hospitalAreaId) {
        return functionSettingMapper.selectByExample(FunctionSetting.class, helper -> helper.defGroup(q -> q.andEqualTo(FunctionSetting::getHospitalAreaId, hospitalAreaId)));
    }
}
