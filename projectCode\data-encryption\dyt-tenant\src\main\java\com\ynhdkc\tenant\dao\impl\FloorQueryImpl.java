package com.ynhdkc.tenant.dao.impl;

import backend.common.util.MybatisUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.ynhdkc.tenant.dao.FloorQuery;
import com.ynhdkc.tenant.dao.mapper.FloorMapper;
import com.ynhdkc.tenant.entity.Floor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-03 10:49
 */
@Repository
@RequiredArgsConstructor
public class FloorQueryImpl implements FloorQuery {
    private final FloorMapper floorMapper;

    @Override
    public Floor queryFloorById(Long floorId) {
        return floorMapper.selectByPrimaryKey(floorId);
    }

    @Override
    public Page<Floor> pageQuery(FloorQueryOption option) {
        try (final Page<Floor> page = PageMethod.startPage(option.getCurrentPage(), option.getPageSize())) {
            return page.doSelectPage(() -> floorMapper.selectByExample(Floor.class, sql -> {
                sql.defGroup(condition -> {
                    if (null != option.getTenantId()) {
                        condition.andEqualTo(Floor::getTenantId, option.getTenantId());
                    }
                    if (null != option.getHospitalId()) {
                        condition.andEqualTo(Floor::getHospitalId, option.getHospitalId());
                    }
                    if (null != option.getHospitalAreaId()) {
                        condition.andEqualTo(Floor::getHospitalAreaId, option.getHospitalAreaId());
                    }
                    if (null != option.getBuildingId()) {
                        condition.andEqualTo(Floor::getBuildingId, option.getBuildingId());
                    }
                    if (null != option.getName()) {
                        condition.andLike(Floor::getName, MybatisUtil.likeBoth(option.getName()));
                    }
                    if (null != option.getStatus()) {
                        condition.andEqualTo(Floor::getStatus, option.getStatus());
                    }

                    if (!CollectionUtils.isEmpty(option.getIncludeTenantIds())) {
                        condition.andIn(Floor::getTenantId, option.getIncludeTenantIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getExcludeTenantIds())) {
                        condition.andNotIn(Floor::getTenantId, option.getExcludeTenantIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getIncludeHospitalIds())) {
                        condition.andIn(Floor::getHospitalId, option.getIncludeHospitalIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getExcludeHospitalIds())) {
                        condition.andNotIn(Floor::getHospitalId, option.getExcludeHospitalIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getIncludeHospitalAreaIds())) {
                        condition.andIn(Floor::getHospitalAreaId, option.getIncludeHospitalAreaIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getExcludeHospitalAreaIds())) {
                        condition.andNotIn(Floor::getHospitalAreaId, option.getExcludeHospitalAreaIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getIncludeBuildingIds())) {
                        condition.andIn(Floor::getBuildingId, option.getIncludeBuildingIds());
                    }
                });
                sql.builder(builder -> builder
                        .orderByDesc(Floor::getSort)
                        .orderByDesc(Floor::getId));
            }));
        }
    }

    @Override
    public List<Floor> queryFloorsByHospitalAreaId(Long hospitalAreaId) {
        return floorMapper.selectByExample(Floor.class, sql -> {
            sql.defGroup(condition -> condition.andEqualTo(Floor::getHospitalAreaId, hospitalAreaId));
            sql.builder(builder -> builder
                    .orderByDesc(Floor::getSort)
                    .orderByDesc(Floor::getId));
        });
    }
}
