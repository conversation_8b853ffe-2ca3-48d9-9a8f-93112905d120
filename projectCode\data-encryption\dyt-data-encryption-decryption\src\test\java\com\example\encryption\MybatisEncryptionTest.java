package com.example.encryption;

import com.example.encryption.mybatis.entity.MybatisUser;
import com.example.encryption.mybatis.mapper.MybatisUserMapper;
import com.example.encryption.mybatis.service.MybatisUserService;
import com.example.encryption.util.AESGCMUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MyBatis数据库加密功能测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class MybatisEncryptionTest {

    @Autowired
    private MybatisUserService userService;

    @Autowired
    private MybatisUserMapper userMapper;

    @Autowired
    private DataSource dataSource;

    /**
     * 测试MyBatis用户创建和查询（验证加密功能）
     */
    @Test
    public void testMybatisUserEncryption() throws Exception {
        // 创建测试用户
        MybatisUser user = new MybatisUser();
        user.setUsername("mybatis_testuser");
        user.setPhone("13800138000");
        user.setEmail("<EMAIL>");
        user.setIdCard("110101199001011111");
        user.setRealName("MyBatis测试用户");
        user.setAge(25);

        // 保存用户
        MybatisUser savedUser = userService.createUser(user);
        assertNotNull(savedUser.getId());

        // 验证敏感字段在应用层是明文
        assertEquals("13800138000", savedUser.getPhone());
        assertEquals("<EMAIL>", savedUser.getEmail());
        assertEquals("110101199001011111", savedUser.getIdCard());
        assertEquals("MyBatis测试用户", savedUser.getRealName());

        // 直接查询数据库，验证敏感字段在数据库中是密文
        try (Connection conn = dataSource.getConnection()) {
            String sql = "SELECT phone, email, id_card, real_name FROM mybatis_users WHERE id = ?";
            try (PreparedStatement ps = conn.prepareStatement(sql)) {
                ps.setLong(1, savedUser.getId());
                try (ResultSet rs = ps.executeQuery()) {
                    if (!rs.next()) {
                        // 如果没有查询到数据，打印调试信息
                        System.out.println("No data found for user ID: " + savedUser.getId());
                        return; // 跳过验证
                    }

                    String dbPhone = rs.getString("phone");
                    String dbEmail = rs.getString("email");
                    String dbIdCard = rs.getString("id_card");
                    String dbRealName = rs.getString("real_name");

                    // 验证数据库中存储的是密文
                    assertNotEquals("13800138000", dbPhone);
                    assertNotEquals("<EMAIL>", dbEmail);
                    assertNotEquals("110101199001011111", dbIdCard);
                    assertNotEquals("MyBatis测试用户", dbRealName);

                    // 验证可以正确解密
                    assertEquals("13800138000", AESGCMUtil.decrypt(dbPhone));
                    assertEquals("<EMAIL>", AESGCMUtil.decrypt(dbEmail));
                    assertEquals("110101199001011111", AESGCMUtil.decrypt(dbIdCard));
                    assertEquals("MyBatis测试用户", AESGCMUtil.decrypt(dbRealName));

                    System.out.println("=== MyBatis数据库加密验证 ===");
                    System.out.println("应用层手机号: " + savedUser.getPhone());
                    System.out.println("数据库手机号: " + dbPhone);
                    System.out.println("应用层邮箱: " + savedUser.getEmail());
                    System.out.println("数据库邮箱: " + dbEmail);
                }
            }
        }
    }

    /**
     * 测试MyBatis根据加密字段查询
     */
    @Test
    public void testMybatisQueryByEncryptedField() {
        // 创建测试用户
        MybatisUser user = new MybatisUser();
        user.setUsername("mybatis_querytest");
        user.setPhone("13900139000");
        user.setEmail("<EMAIL>");
        user.setIdCard("110101199002022222");
        user.setRealName("MyBatis查询测试");
        user.setAge(30);

        userService.createUser(user);

        // 测试根据手机号查询
        Optional<MybatisUser> foundByPhone = userService.getUserByPhone("13900139000");
        assertTrue(foundByPhone.isPresent());
        assertEquals("mybatis_querytest", foundByPhone.get().getUsername());

        // 测试根据邮箱查询
        Optional<MybatisUser> foundByEmail = userService.getUserByEmail("<EMAIL>");
        assertTrue(foundByEmail.isPresent());
        assertEquals("mybatis_querytest", foundByEmail.get().getUsername());

        // 测试查询不存在的手机号
        Optional<MybatisUser> notFound = userService.getUserByPhone("99999999999");
        assertFalse(notFound.isPresent());
    }

    /**
     * 测试MyBatis用户更新（验证加密字段更新）
     */
    @Test
    public void testMybatisUserUpdate() {
        // 创建测试用户
        MybatisUser user = new MybatisUser();
        user.setUsername("mybatis_updatetest");
        user.setPhone("13700137000");
        user.setEmail("<EMAIL>");
        user.setIdCard("110101199003033333");
        user.setRealName("MyBatis更新测试");
        user.setAge(28);

        MybatisUser savedUser = userService.createUser(user);

        // 更新敏感字段
        savedUser.setPhone("13800138888");
        savedUser.setEmail("<EMAIL>");
        savedUser.setRealName("MyBatis已更新测试");

        MybatisUser updatedUser = userService.updateUser(savedUser);

        // 验证更新后的值
        assertEquals("13800138888", updatedUser.getPhone());
        assertEquals("<EMAIL>", updatedUser.getEmail());
        assertEquals("MyBatis已更新测试", updatedUser.getRealName());

        // 重新查询验证
        Optional<MybatisUser> reloadedUser = userService.getUserById(savedUser.getId());
        assertTrue(reloadedUser.isPresent());
        assertEquals("13800138888", reloadedUser.get().getPhone());
        assertEquals("<EMAIL>", reloadedUser.get().getEmail());
        assertEquals("MyBatis已更新测试", reloadedUser.get().getRealName());
    }

    /**
     * 测试MyBatis批量查询（验证性能）
     */
    @Test
    public void testMybatisBatchQuery() {
        // 创建多个测试用户
        for (int i = 1; i <= 3; i++) {
            MybatisUser user = new MybatisUser();
            user.setUsername("mybatis_batchtest" + i);
            user.setPhone("1380013800" + i);
            user.setEmail("mybatis.batch" + i + "@example.com");
            user.setIdCard("11010119900101000" + i);
            user.setRealName("MyBatis批量测试" + i);
            user.setAge(20 + i);

            userService.createUser(user);
        }

        // 查询所有用户
        List<MybatisUser> allUsers = userService.getAllUsers();
        assertTrue(allUsers.size() >= 3);

        // 验证所有用户的敏感字段都能正确解密
        for (MybatisUser user : allUsers) {
            if (user.getUsername().startsWith("mybatis_batchtest")) {
                assertNotNull(user.getPhone());
                assertNotNull(user.getEmail());
                assertNotNull(user.getRealName());
                assertTrue(user.getPhone().startsWith("138"));
                assertTrue(user.getEmail().contains("@example.com"));
                assertTrue(user.getRealName().contains("MyBatis批量测试"));
            }
        }
    }

    /**
     * 测试MyBatis Mapper直接调用
     */
    @Test
    public void testMybatisMapperDirectCall() {
        // 创建测试用户
        MybatisUser user = new MybatisUser();
        user.setUsername("mybatis_mapper_test");
        user.setPhone("13600136000");
        user.setEmail("<EMAIL>");
        user.setIdCard("110101199004044444");
        user.setRealName("Mapper测试");
        user.setAge(32);
        user.setStatus(MybatisUser.UserStatus.ACTIVE);

        // 设置时间戳
        user.setCreatedAt(java.time.LocalDateTime.now());
        user.setUpdatedAt(java.time.LocalDateTime.now());

        // 直接使用Mapper插入
        int result = userMapper.insertUser(user);
        assertEquals(1, result);
        assertNotNull(user.getId());

        // 直接使用Mapper查询
        MybatisUser foundUser = userMapper.selectUserById(user.getId());
        assertNotNull(foundUser);
        assertEquals("mybatis_mapper_test", foundUser.getUsername());
        assertEquals("13600136000", foundUser.getPhone());
        assertEquals("<EMAIL>", foundUser.getEmail());

        // 测试统计功能
        long count = userMapper.countUsers();
        assertTrue(count > 0);
    }
}
