CREATE TABLE `t_bulletin_config`  (
  `id` bigint(20) UNSIGNED ZEROFILL NOT NULL AUTO_INCREMENT COMMENT '全局唯一标识',
  `system_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '系统状态：0，正常；1，维护，默认正常',
  `bulletin_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公告内容',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '启用状态：0，启用；1，禁用，默认禁用',
  `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `update_time` datetime(3) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB  COMMENT = '系统公告配置' ROW_FORMAT = Dynamic;
