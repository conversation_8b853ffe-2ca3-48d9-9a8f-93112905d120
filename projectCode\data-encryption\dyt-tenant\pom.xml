<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ynhdkc</groupId>
        <artifactId>dyt-common-parent</artifactId>
        <version>1.1-SNAPSHOT</version>
    </parent>

    <groupId>com.ynhdkc.tenant</groupId>
    <artifactId>dyt-tenant</artifactId>
    <version>1.0-SNAPSHOT</version>
    <name>dyt-tenant</name>
    <description>dyt-tenant</description>
    <properties>
        <java.version>1.8</java.version>
        <springdoc.version>1.6.8</springdoc.version>
        <backend.version>1.1-SNAPSHOT</backend.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
            <version>5.13.0</version>
        </dependency>
        <dependency>
            <groupId>com.ynhdkc</groupId>
            <artifactId>dyt-backend-common</artifactId>
            <version>${backend.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ynhdkc</groupId>
            <artifactId>dyt-backend-security</artifactId>
            <version>${backend.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ynhdkc</groupId>
            <artifactId>dyt-backend-encryption</artifactId>
            <version>${backend.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ynhdkc</groupId>
            <artifactId>dyt-old-system-integration-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>tk.mybatis</groupId>
            <artifactId>mapper-spring-boot-starter</artifactId>
            <version>4.2.2</version>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.kafka</groupId>
                    <artifactId>kafka-clients</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>5.8.26</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-json</artifactId>
            <version>5.8.26</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-jaxb-annotations</artifactId>
            <version>2.11.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.10.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.4.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-redis</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.8.RELEASE</version>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-configuration-processor</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-codegen-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>generate-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/apis/apis.yml
                            </inputSpec>
                            <language>spring</language>
                            <apiPackage>com.ynhdkc.tenant.handler</apiPackage>
                            <modelPackage>com.ynhdkc.tenant.model</modelPackage>
                            <importMappings>
                                <importMapping>BaseOperationResponse=backend.common.response.BaseOperationResponse
                                </importMapping>
                                <importMapping>Recipient=backend.common.entity.dto.notification.request.Recipient
                                </importMapping>
                            </importMappings>
                            <templateDirectory>${project.basedir}/src/main/resources/apis/template</templateDirectory>
                            <configOptions>
                                <interfaceOnly>true</interfaceOnly>
                                <useTags>true</useTags>
                                <dateLibrary>legacy</dateLibrary>
                            </configOptions>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-api-2025</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/apis/apis-2025.yml
                            </inputSpec>
                            <language>spring</language>
                            <apiPackage>com.ynhdkc.tenant.handler</apiPackage>
                            <modelPackage>com.ynhdkc.tenant.model</modelPackage>
                            <importMappings>
                                <importMapping>BaseOperationResponse=backend.common.response.BaseOperationResponse
                                </importMapping>
                                <importMapping>Recipient=backend.common.entity.dto.notification.request.Recipient
                                </importMapping>
                                <importMapping>BasePage=com.ynhdkc.tenant.model.BasePage</importMapping>
                            </importMappings>
                            <templateDirectory>${project.basedir}/src/main/resources/apis/template</templateDirectory>
                            <configOptions>
                                <interfaceOnly>true</interfaceOnly>
                                <useTags>true</useTags>
                                <dateLibrary>legacy</dateLibrary>
                            </configOptions>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-rpc-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/apis/rpc-apis.yml
                            </inputSpec>
                            <language>spring</language>
                            <apiPackage>com.ynhdkc.tenant.handler</apiPackage>
                            <modelPackage>com.ynhdkc.tenant.model</modelPackage>
                            <importMappings>
                                <importMapping>BaseOperationResponse=backend.common.response.BaseOperationResponse
                                </importMapping>
                            </importMappings>
                            <templateDirectory>${project.basedir}/src/main/resources/apis/template</templateDirectory>
                            <configOptions>
                                <interfaceOnly>true</interfaceOnly>
                                <useTags>true</useTags>
                                <dateLibrary>legacy</dateLibrary>
                            </configOptions>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-customer-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/src/main/resources/apis/customer-apis.yml
                            </inputSpec>
                            <language>spring</language>
                            <apiPackage>com.ynhdkc.tenant.handler</apiPackage>
                            <modelPackage>com.ynhdkc.tenant.model</modelPackage>
                            <importMappings>
                                <importMapping>BaseOperationResponse=backend.common.response.BaseOperationResponse
                                </importMapping>
                                <importMapping>BasePage=com.ynhdkc.tenant.model.BasePage</importMapping>
                                <importMapping>
                                    CustomerQueryHospitalPageReqDto=com.ynhdkc.tenant.model.CustomerQueryHospitalPageReqDto
                                </importMapping>
                                <importMapping>CustomerHospitalPageVo=com.ynhdkc.tenant.model.CustomerHospitalPageVo
                                </importMapping>
                            </importMappings>
                            <templateDirectory>${project.basedir}/src/main/resources/apis/template</templateDirectory>
                            <configOptions>
                                <interfaceOnly>true</interfaceOnly>
                                <useTags>true</useTags>
                                <dateLibrary>legacy</dateLibrary>
                            </configOptions>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
