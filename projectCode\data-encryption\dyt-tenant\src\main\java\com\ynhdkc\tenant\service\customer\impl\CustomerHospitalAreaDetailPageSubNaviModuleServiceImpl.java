package com.ynhdkc.tenant.service.customer.impl;

import com.ynhdkc.tenant.dao.HospitalAreaQuery;
import com.ynhdkc.tenant.dao.mapper.IHospitalDetailPageSubNaviModuleMapper;
import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.entity.detailpage.HospitalAreaDetailPageSubNaviModule;
import com.ynhdkc.tenant.model.CustomerHospitalAreaDetailPageSubNaviModuleVo;
import com.ynhdkc.tenant.service.customer.CustomerHospitalAreaDetailPageSubNaviModuleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/7/19 16:12
 */
@Service
@RequiredArgsConstructor
public class CustomerHospitalAreaDetailPageSubNaviModuleServiceImpl implements CustomerHospitalAreaDetailPageSubNaviModuleService {
    private final HospitalAreaQuery hospitalAreaQuery;
    private final IHospitalDetailPageSubNaviModuleMapper mapper;

    @Override
    public List<CustomerHospitalAreaDetailPageSubNaviModuleVo> getHospitalAreaDetailPageSubNaviModuleByHospitalAreaId(Long hospitalAreaId, String hospitalAreaCode) {
        if (null == hospitalAreaId) {
            Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaBy(hospitalAreaCode);
            hospitalAreaId = hospitalArea.getId();
        }

        Long finalHospitalAreaId = hospitalAreaId;
        List<HospitalAreaDetailPageSubNaviModule> subNaviModules = mapper.selectByExample(HospitalAreaDetailPageSubNaviModule.class, helper -> {
            helper.defGroup(consumer -> consumer.andEqualTo(HospitalAreaDetailPageSubNaviModule::getHospitalAreaId, finalHospitalAreaId));
            helper.builder(builder -> builder.orderByDesc(HospitalAreaDetailPageSubNaviModule::getSort));
        });

        return subNaviModules.stream().
                map(CustomerHospitalAreaDetailPageSubNaviModuleService::toVo).
                collect(Collectors.toList());
    }
}
