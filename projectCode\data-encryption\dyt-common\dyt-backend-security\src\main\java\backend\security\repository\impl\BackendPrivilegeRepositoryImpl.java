package backend.security.repository.impl;

import backend.common.exception.BizException;
import backend.security.entity.OrganizationStructureTree;
import backend.security.entity.Role;
import backend.security.entity.TenantUserAscription;
import backend.security.entity.TenantUserStructureTree;
import backend.security.repository.BackendPrivilegeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/9/7 16:08
 */
@RequiredArgsConstructor
public class BackendPrivilegeRepositoryImpl implements BackendPrivilegeRepository {
    private static final String ROLE_KEY = "backend:security:role";
    private static final String ORGANIZATION_STRUCTURE_TREE_KEY = "backend:security:organization-structure:tree";
    private static final String TENANT_USER_STRUCTURE_TREE_KEY = "backend:security:tenant-user-structure:tree";
    private static final String TENANT_USER_ASCRIPTION_KEY = "backend:security:tenant-user-ascription";

    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    public void saveRole(Role role) {
        redisTemplate.opsForValue().set(getRoleKey(role.getId()), role);
    }

    @Override
    public void deleteRole(Long roleId) {
        redisTemplate.delete(getRoleKey(roleId));
    }

    @Override
    public Role getRoleById(Long roleId) {
        Object value = redisTemplate.opsForValue().get(getRoleKey(roleId));
        if (value instanceof Role) {
            return (Role) value;
        }
        throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "获取角色失败");
    }

    @Override
    public void saveOrganizationStructureTree(OrganizationStructureTree organizationStructureTree) {
        redisTemplate.opsForValue().set(ORGANIZATION_STRUCTURE_TREE_KEY, organizationStructureTree);
    }

    @Override
    public OrganizationStructureTree getOrganizationStructureTree() {
        Object value = redisTemplate.opsForValue().get(ORGANIZATION_STRUCTURE_TREE_KEY);
        if (value instanceof OrganizationStructureTree) {
            return (OrganizationStructureTree) value;
        }
        throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "获取组织架构失败");
    }

    @Override
    public void saveTenantUserStructureTree(TenantUserStructureTree tenantUserStructureTree) {
        redisTemplate.opsForValue().set(getTentUserOrganizationStructureTreeKey(tenantUserStructureTree.getUserId()), tenantUserStructureTree);
    }

    @Override
    public void deleteTenantUserStructureTree(Long userId) {
        redisTemplate.delete(getTentUserOrganizationStructureTreeKey(userId));
    }

    @Override
    public TenantUserStructureTree getTenantUserStructureTree(Long userId) {
        Object value = redisTemplate.opsForValue().get(getTentUserOrganizationStructureTreeKey(userId));
        if (value instanceof TenantUserStructureTree) {
            return (TenantUserStructureTree) value;
        }
        throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "获取用户架构失败");
    }

    @Override
    public void saveTenantUserAscription(TenantUserAscription tenantUserAscription) {
        redisTemplate.opsForValue().set(getTenantUserAscriptionKey(tenantUserAscription.getUserId()), tenantUserAscription);
    }

    @Override
    public void deleteTenantUserAscription(Long userId) {
        redisTemplate.delete(getTenantUserAscriptionKey(userId));
    }

    @Override
    public TenantUserAscription getTenantUserAscription(Long userId) {
        Object value = redisTemplate.opsForValue().get(getTenantUserAscriptionKey(userId));
        if (value instanceof TenantUserAscription) {
            return (TenantUserAscription) value;
        }
        throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "获取用户架构失败");
    }

    private static String getRoleKey(Long roleId) {
        return ROLE_KEY + ":" + roleId;
    }

    private static String getTentUserOrganizationStructureTreeKey(Long userId) {
        return TENANT_USER_STRUCTURE_TREE_KEY + ":" + userId;
    }

    private static String getTenantUserAscriptionKey(Long userId) {
        return TENANT_USER_ASCRIPTION_KEY + ":" + userId;
    }
}
