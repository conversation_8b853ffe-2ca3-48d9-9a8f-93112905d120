package com.ynhdkc.tenant.service.backend.impl;

import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ynhdkc.tenant.dao.mapper.*;
import com.ynhdkc.tenant.entity.*;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.HospitalService;
import com.ynhdkc.tenant.service.backend.IHospitalListGroupService;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <p>
 * 医院组 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@Service
@RequiredArgsConstructor
public class HospitalListGroupServiceImpl implements IHospitalListGroupService {

    private final HospitalListGroupMapper mapper;
    private final HospitalListGroupRelationMapper relationMapper;
    private final PageVoConvert pageVoConvert;
    private final HospitalMapper hospitalMapper;
    private final HospitalListDisplayMapper hospitalListDisplayMapper;
    private final HospitalListRuleMapper hospitalListRuleMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseOperationResponse bindHospitals(Long id, List<GroupBindHospitalReqDto> dto) {
        HospitalListGroup group = mapper.selectByPrimaryKey(id);
        if (group == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "医院组不存在");
        }

        List<HospitalListGroupRelation> oldRelationList = relationMapper.selectByExample2(HospitalListGroupRelation.class, sql -> sql.andEqualTo(HospitalListGroupRelation::getGroupId, id));
        if (!CollectionUtils.isEmpty(oldRelationList)) {
            List<Long> deleteIds = oldRelationList.stream().map(HospitalListGroupRelation::getId).collect(Collectors.toList());
            if (!deleteIds.isEmpty()) {
                int deleteEffectCount = relationMapper.deleteByIds(StrUtil.join(",", deleteIds));
                if (deleteEffectCount == 0) {
                    throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "绑定医院失败");
                }
            }
        }

        groupBindHospitals(id, dto);
        return new BaseOperationResponse();
    }

    private void groupBindHospitals(Long id, List<GroupBindHospitalReqDto> dto) {
        if (!CollectionUtils.isEmpty(dto)) {
            List<HospitalListGroupRelation> listForInsert = new ArrayList<>();
            dto.forEach(h -> {
                HospitalListGroupRelation relation = new HospitalListGroupRelation();
                relation.setGroupId(id)
                        .setHospitalId(h.getHospitalId())
                        .setPlatformWithList(h.getPlatform())
                        .setWeight(h.getWeight());
                listForInsert.add(relation);
            });
            int insertEffectCount = relationMapper.insertList(listForInsert);
            if (insertEffectCount == 0) {
                throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "绑定医院失败");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HospitalListGroupVo createHospitalListGroup(CreateHospitalListGroupReqDto hospitalListGroup) {
        mapper.selectByExample2(HospitalListGroup.class,
                        sql -> sql.andEqualTo(HospitalListGroup::getName, hospitalListGroup.getName()))
                .stream()
                .findFirst()
                .ifPresent(v -> {
                    throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "医院组名称已存在");
                });
        HospitalListGroup group = IHospitalListGroupService.toEntity(hospitalListGroup);
        int insertEffectCount = mapper.insertSelective(group);
        if (insertEffectCount == 0) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "新增医院组失败");
        }

        groupBindHospitals(group.getId(), hospitalListGroup.getHospitals());

        return IHospitalListGroupService.toVo(group);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseOperationResponse deleteHospitalListGroup(Long id) {
        List<HospitalListRule> hospitalListRules = hospitalListRuleMapper.selectByExample2(HospitalListRule.class,
                sql -> sql.andEqualTo(HospitalListRule::getHospitalGroupId, id));
        if (!CollectionUtils.isEmpty(hospitalListRules)) {
            String ruleNames = hospitalListRules.stream().map(HospitalListRule::getName).collect(Collectors.joining(","));
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "该医院组已被规则：【" + ruleNames + "】使用，无法删除");
        }
        HospitalListGroup group = mapper.selectByPrimaryKey(id);
        if (group == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "医院组不存在");
        }
        int deleteEffectCount = mapper.deleteByPrimaryKey(id);
        if (deleteEffectCount == 0) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "删除医院组失败");
        }
        relationMapper.deleteByExample2(HospitalListGroupRelation.class, sql -> sql.andEqualTo(HospitalListGroupRelation::getGroupId, id));
        return new BaseOperationResponse();
    }

    @Override
    public HospitalListGroupDetailVo getHospitalListGroupDetail(Long id) {
        HospitalListGroup group = mapper.selectByPrimaryKey(id);
        if (group == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "医院组不存在");
        }

        HospitalListGroupDetailVo vo = IHospitalListGroupService.toDetailVo(group);
        List<HospitalListGroupRelation> relationList = relationMapper.selectByExample2(HospitalListGroupRelation.class,
                sql -> sql.andEqualTo(HospitalListGroupRelation::getGroupId, id));
        List<Long> hospitalIds = relationList.stream().map(HospitalListGroupRelation::getHospitalId).collect(Collectors.toList());
        if (!hospitalIds.isEmpty()) {
            List<Hospital> hospitals = hospitalMapper.selectByIds(StrUtil.join(",", hospitalIds));
            List<HospitalVo> hospitalVos = hospitals.stream().map(HospitalService::toHospitalVo).collect(Collectors.toList());
            List<GroupHospitalVo> groupHospitalVos = BeanUtil.copyToList(hospitalVos, GroupHospitalVo.class);

            Map<Long, Long> displayIdMap = hospitalListDisplayMapper.selectByExample2(HospitalListDisplay.class, sql -> sql.andIn(HospitalListDisplay::getHospitalId, hospitalIds))
                    .stream().collect(Collectors.toMap(HospitalListDisplay::getHospitalId, HospitalListDisplay::getId));
            Map<Long, HospitalListGroupRelation> relationMap = relationList.stream().collect(Collectors.toMap(HospitalListGroupRelation::getHospitalId, r -> r));
            groupHospitalVos = groupHospitalVos.stream().peek(v -> {
                Long displayId = displayIdMap.get(v.getId());
                HospitalListGroupRelation relation = relationMap.get(v.getId());
                if (relation != null) {
                    v.setPlatform(relation.getPlatformList());
                    v.setWeight(relation.getWeight());
                    v.setDisplayId(displayId);
                }
            }).sorted(Comparator.comparing(GroupHospitalVo::getWeight)).collect(Collectors.toList());
            vo.setHospitals(groupHospitalVos);
        }

        return vo;
    }

    @Override
    public HospitalListGroupPageVo getHospitalListGroupPage(Integer currentPage, Integer pageSize, Integer hospitalCount) {

        Consumer<WeekendSqls<HospitalListGroup>> pageSql = sql -> {
            if (hospitalCount != null) {
                List<Long> groupIds = new ArrayList<>();
                relationMapper.selectAll().stream().collect(Collectors.groupingBy(HospitalListGroupRelation::getGroupId)).forEach((k, v) -> {
                    if (v.size() == hospitalCount) {
                        groupIds.add(k);
                    }
                });
                if (!groupIds.isEmpty()) {
                    sql.andIn(HospitalListGroup::getId, groupIds);
                }
            }
        };

        Page<HospitalListGroup> page = PageHelper.startPage(currentPage, pageSize);
        page.doSelectPage(() -> mapper.selectByExample2(HospitalListGroup.class, pageSql));

        HospitalListGroupPageVo pageVo = pageVoConvert.toPageVo(page, HospitalListGroupPageVo.class, IHospitalListGroupService::toVo);

        List<Long> groupIds = page.stream().map(HospitalListGroup::getId).collect(Collectors.toList());
        if (!groupIds.isEmpty()) {
            List<HospitalListGroupRelation> relationList = relationMapper.selectByExample2(HospitalListGroupRelation.class,
                    sql -> sql.andIn(HospitalListGroupRelation::getGroupId, groupIds));
            Map<Long, List<HospitalListGroupRelation>> relationMap = relationList.stream().collect(Collectors.groupingBy(HospitalListGroupRelation::getGroupId));
            pageVo.getList().forEach(v -> {
                List<HospitalListGroupRelation> relations = relationMap.get(v.getId());
                if (relations != null) {
                    v.setHospitalCount(relations.size());
                }
            });
        }

        return pageVo;
    }

    @Override
    public HospitalListGroupVo updateHospitalListGroup(Long id, UpdateHospitalListGroupReqDto hospitalListGroupDto) {
        HospitalListGroup group = mapper.selectByPrimaryKey(id);
        if (group == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "医院组不存在");
        }
        group.setName(hospitalListGroupDto.getName()).setDescription(hospitalListGroupDto.getDescription()).setUpdateTime(new Date());

        int updateEffectCount = mapper.updateByPrimaryKey(group);
        if (updateEffectCount == 0) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "更新医院组信息失败");
        }
        return IHospitalListGroupService.toVo(group);
    }
}
