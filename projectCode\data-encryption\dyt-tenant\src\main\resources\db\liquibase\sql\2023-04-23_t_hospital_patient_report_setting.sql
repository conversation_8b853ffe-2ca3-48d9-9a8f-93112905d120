create table t_hospital_patient_report_setting
(
    id                        bigint auto_increment comment '全局唯一标识'
        primary key,
    function_id               bigint                                    null comment '功能 ID',
    tenant_id                 bigint                                    not null comment '租户 ID',
    hospital_id               bigint                                    not null comment '医院 ID',
    hospital_area_id          bigint                                    not null comment '院区 ID',
    hospital_code             varchar(20)                               null comment '医院编码',
    support_report_type       int          default 0                    not null comment '支持的报告类型，0：全部，1：检验报告，2：检查报告',
    support_search_date_range int          default 3                    not null comment '支持的按钮刻度，0：无限制，1：30天内，2：90天内，3：180天内，3：一年内',
    support_search_time       varchar(250) default '[]'                 not null comment '支持的查询时间，0：全部，1：最近一周，2：最近一月，3：最近三月，4：最近半年，5：最近一年',
    notice                    text                                      null comment '提示消息',
    create_time               datetime(3)  default CURRENT_TIMESTAMP(3) not null,
    update_time               datetime(3)                               null on update CURRENT_TIMESTAMP(3),
    constraint uidx_limit_single_setting
        unique (tenant_id, hospital_id, hospital_area_id)
)
    comment '医院报告查询设置';

insert into t_hospital_patient_report_setting
    (id, tenant_id, hospital_id, hospital_area_id)
values (1, 1, 1, 2),
       (2, 1, 1, 3),
       (3, 2, 4, 5),
       (4, 2, 4, 6),
       (5, 2, 4, 7);