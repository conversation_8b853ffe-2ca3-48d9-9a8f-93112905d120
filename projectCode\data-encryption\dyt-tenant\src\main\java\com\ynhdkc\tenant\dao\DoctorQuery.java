package com.ynhdkc.tenant.dao;

import backend.common.util.BaseQueryOption;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.entity.DoctorGroupRelation;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.lang.NonNull;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-03 16:00
 */
public interface DoctorQuery {
    Page<Doctor> pageQueryDoctorWithCategory(DoctorQueryOption option);

    Page<Doctor> pageQuery(DoctorQueryOption option);

    Doctor queryDoctorById(Long doctorId);

    List<Doctor> queryAll();


    List<Doctor> queryByIds(List<Long> doctorIds);

    List<Doctor> queryBy(Long departmentId);

    List<Doctor> queryByDepartmentCodeAndHospitalCode(@NonNull String departmentCode, @NonNull String hospitalCode);

    List<Doctor> queryBy(Long hospitalAreaId, Long departmentId);

    List<Doctor> queryLikeName(String name);

    List<Doctor> queryList(String hospitalAreaCode, List<String> doctorCodeList);

    Doctor queryByCode(String hospitalAreaCode, String departmentCode, String doctorCode);

    List<Doctor> queryDoctorsByCode(String hospitalAreaCode, String departmentCode, String doctorCode);

    List<Doctor> queryDoctorsByHospitalAreaIdAndDepartmentCode(Long hospitalAreaId, String departmentCode);

    List<Doctor> queryBy(List<Long> doctorIds);

    List<Doctor> findByDepartmentId(Long departmentId);

    DoctorGroupRelation queryDoctorGroupRelation(Long doctorId, Integer doctorGroupSource, Long doctorGroupId);

    DoctorGroupRelation queryDoctorGroupRelationById(Long relationId);

    Page<DoctorGroupRelation> pageQueryDoctorGroupRelation(DoctorGroupRelationQueryOption option);

    Boolean hasDoctorGroup(Long id);

    List<Doctor> queryList(String hospitalCode, int page, int size);

    List<DoctorGroupRelation> listDoctorGroupRelationBy(List<Long> doctorIds);

    List<Doctor> queryByHospitalCodeAndDepartmentCodes(String hospitalCode, Collection<String> departmentCodes);

    List<Doctor> queryByDepartmentIds(Collection<Long> departmentIds);

    List<Long> queryEnableDoctorIds(List<Long> departmentIds);

    List<Doctor> queryEnableDoctorsBy(String hospitalCode);

    List<Doctor> queryBy(Set<Long> doctorIds);

    List<Doctor> queryBy(String hospitalCode, String thrdpartDoctorCode);

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Accessors(chain = true)
    class DoctorQueryOption extends BaseQueryOption {
        private Long tenantId;
        private Long hospitalId;
        private Long hospitalAreaId;
        private Long departmentId;
        private Long id;
        private String name;
        private Integer status;
        private String thrdpartDoctorCode;
        private Date startCreateTime;
        private Date endCreateTime;
        private Long userId;
        private List<Integer> category;
        private List<Integer> tags;
        private Boolean display;

        private Collection<Long> includeIds;
        private Collection<Long> includeTenantIds;
        private Collection<Long> excludeTenantIds;
        private Collection<Long> includeHospitalIds;
        private Collection<Long> excludeHospitalIds;
        private Collection<Long> includeHospitalAreaIds;
        private Collection<Long> excludeHospitalAreaIds;
        private Collection<Long> includeDepartmentIds;
        private Collection<Long> excludeDepartmentIds;
        private Collection<Integer> excludeCategory;

        @Setter(AccessLevel.NONE)
        private String sortBy;

        public DoctorQueryOption(Integer currentPage, Integer pageSize) {
            super(currentPage, pageSize);
        }

        public DoctorQueryOption sortBy(String sortBy) {
            this.sortBy = sortBy;
            return this;
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Accessors(chain = true)
    class DoctorGroupRelationQueryOption extends BaseQueryOption {
        private Long doctorId;
        private String doctorName;
        private Integer doctorGroupSource;
        private Long doctorGroupId;
        private String doctorGroupName;

        public DoctorGroupRelationQueryOption(Integer currentPage, Integer pageSize) {
            super(currentPage, pageSize);
        }
    }
}
