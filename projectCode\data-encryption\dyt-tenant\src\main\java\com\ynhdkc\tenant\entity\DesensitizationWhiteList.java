package com.ynhdkc.tenant.entity;/**
 * <AUTHOR>
 * @date 2024/07/23/10:54
 */

import backend.common.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @description
 * @date 2024/7/23 10:54
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "t_desensitization_white_list")
public class DesensitizationWhiteList extends BaseEntity {
    private Long tenantId;
    private Long tenantUserId;
    private String tenantName;
    private String tenantUserName;
}
