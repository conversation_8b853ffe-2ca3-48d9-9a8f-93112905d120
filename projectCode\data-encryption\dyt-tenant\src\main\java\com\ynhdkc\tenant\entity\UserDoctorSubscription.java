package com.ynhdkc.tenant.entity;

import backend.common.domain.BaseEntity;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "t_user_doctor_subscription")
public class UserDoctorSubscription extends BaseEntity {

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "doctor_id")
    private Long doctorId;

    @Column(name = "doctor_code")
    private String doctorCode;

    @Column(name = "doctor_name")
    private String doctorName;

    @Column(name = "department_id")
    private Long departmentId;

    @Column(name = "department_code")
    private String departmentCode;

    @Column(name = "department_name")
    private String departmentName;

    @Column(name = "hospital_area_id")
    private Long hospitalAreaId;

    @Column(name = "hospital_area_code")
    private String hospitalAreaCode;

    @Column(name = "hospital_area_name")
    private String hospitalAreaName;

    @Column(name = "hospital_id")
    private Long hospitalId;

    @Column(name = "hospital_name")
    private String hospitalName;

    @Column(name = "channel_tag")
    private String channelTag;
    
    @Column(name = "status")
    private Integer status;

    @Column(name = "notice_count")
    private Integer noticeCount;

    private Date updateTime;
    @Column(name = "cancel_time")
    private Date cancelTime;
    @Column(name = "last_notified_time")
    private Date lastNotifiedTime;
}
