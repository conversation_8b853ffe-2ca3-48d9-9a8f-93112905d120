<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ynhdkc.tenant.dao.mapper.AppointmentRuleSettingMapper">

    <select id="selectDependOnHis" resultType="com.ynhdkc.tenant.model.HospitalDependOnHisResponse">
        select a.hospital_id as hospitalId,
        a.hospital_area_id as hospitalAreaId,
        h.hospital_code as hospitalCode,
        h.name as hospitalName
        from t_hospital_appointment_rule_setting a
        left join t_hospital h on a.hospital_area_id = h.id
        where h.status = 0
        and a.system_depends = 0

        <if test="id != null">
            and a.hospital_area_id = #{a.hospital_area_id}
        </if>
        <if test="hospitalName != null">
            and h.name like concat('%', #{hospitalName}, '%')
        </if>
    </select>
</mapper>