package com.ynhdkc.tenant.dao;

import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.entity.DoctorGroupRelation;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-03 15:59
 */
public interface DoctorRepository {
    void create(Doctor doctor);

    void update(Doctor doctor);

    void delete(Long doctorId);

    void createDoctorGroupRelation(DoctorGroupRelation doctorGroupRelation);

    void deleteDoctorGroupRelation(Long doctorId, Integer doctorGroupSource, Long doctorGroupId);

    List<Doctor> queryEnabledDoctors(String code);
}
