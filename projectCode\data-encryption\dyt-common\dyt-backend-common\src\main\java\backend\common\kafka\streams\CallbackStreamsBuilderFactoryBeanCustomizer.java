package backend.common.kafka.streams;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.streams.KafkaStreams;
import org.apache.kafka.streams.StreamsBuilder;
import org.apache.kafka.streams.StreamsConfig;
import org.apache.kafka.streams.errors.LogAndContinueExceptionHandler;
import org.apache.logging.log4j.core.util.Throwables;
import org.springframework.boot.autoconfigure.kafka.StreamsBuilderFactoryBeanCustomizer;
import org.springframework.kafka.config.StreamsBuilderFactoryBean;

import java.util.List;
import java.util.Properties;

@Slf4j
public class CallbackStreamsBuilderFactoryBeanCustomizer implements StreamsBuilderFactoryBeanCustomizer {


    private final List<KafkaStreamsCallback> kafkaStreamsCallbacks;

    public CallbackStreamsBuilderFactoryBeanCustomizer(List<KafkaStreamsCallback> kafkaStreamsCallbacks) {
        this.kafkaStreamsCallbacks = kafkaStreamsCallbacks;
    }

    @Override
    public void customize(StreamsBuilderFactoryBean fb) {
        try {
            final StreamsBuilder streamsBuilder = fb.getObject();
            if (fb.getStreamsConfiguration() != null) {
                fb.getStreamsConfiguration().put(StreamsConfig.DEFAULT_DESERIALIZATION_EXCEPTION_HANDLER_CLASS_CONFIG,
                        LogAndContinueExceptionHandler.class.getName());
            } else {
                final Properties props = new Properties();
                props.put(StreamsConfig.DEFAULT_DESERIALIZATION_EXCEPTION_HANDLER_CLASS_CONFIG,
                        LogAndContinueExceptionHandler.class.getName());
                fb.setStreamsConfiguration(props);
            }

            fb.setUncaughtExceptionHandler((t, e) -> log.error("uncaught exception. thread: {}", t.getName(), e));
            fb.addListener(new StreamsBuilderFactoryBean.Listener() {
                @Override
                public void streamsAdded(String id, KafkaStreams streams) {
                    for (KafkaStreamsCallback callback : kafkaStreamsCallbacks) {
                        callback.onStreamsAdded(id, streams);
                    }
                }

                @Override
                public void streamsRemoved(String id, KafkaStreams streams) {
                    for (KafkaStreamsCallback callback : kafkaStreamsCallbacks) {
                        callback.onStreamsRemoved(id, streams);
                    }
                }
            });
            for (KafkaStreamsCallback callback : kafkaStreamsCallbacks) {
                callback.build(streamsBuilder);
            }
        } catch (Exception e) {
            log.error("customize StreamsBuilderFactoryBean failure: {}", e.getMessage());
            Throwables.rethrow(e);
        }
    }
}
