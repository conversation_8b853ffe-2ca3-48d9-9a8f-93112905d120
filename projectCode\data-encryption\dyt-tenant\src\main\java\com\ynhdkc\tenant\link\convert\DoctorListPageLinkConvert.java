package com.ynhdkc.tenant.link.convert;

import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.service.customer.CustomerDepartmentService;
import com.ynhdkc.tenant.util.UrlUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Component
@RequiredArgsConstructor
public class DoctorListPageLinkConvert implements ILinkConvert {

    private final CustomerDepartmentService customerDepartmentService;

    /**
     * 转换后地址：<a href="https://appv3.ynhdkc.com/registration_doctor_list_new?hos_code=871030&deparmentId=1762&hospital_id=1&hospital_area_id=2">示例</a>
     */
    @Override
    public String getConvertLink(String source) {
        final String hosCodeKey = "hos_code=";
        final String hosCode = UrlUtil.extractValue(source, hosCodeKey);
        if (hosCode == null) {
            return Constants.DEFAULT_URL;
        }

        final String depIdKey = "dep_id=";
        final String depId = UrlUtil.extractValue(source, depIdKey);
        if (depId == null) {
            return Constants.DEFAULT_URL;
        }

        Optional<Department> departmentOptional = customerDepartmentService.queryDepartmentByCode(hosCode, depId);
        if (!departmentOptional.isPresent()) {
            return Constants.DEFAULT_URL;
        }

        Department department = departmentOptional.get();
        final String urlPrefix = "https://appv3.ynhdkc.com/registration_doctor_list_new";
        return urlPrefix + "?hos_code=" + hosCode + "&departmentId=" + department.getId() + "&hospital_id=" + department.getHospitalId() + "&hospital_area_id=" + department.getHospitalAreaId();
    }

    @Override
    public boolean isSupport(String source) {
        final List<String> urlPrefixList = Arrays.asList("https://appv2.ynhdkc.com/registration_doctor_list",
                "https://testapp.ynhdkc.com/registration_doctor_list",
                "https://ttestapp.ynhdkc.com/registration_doctor_list");
        return urlPrefixList.stream().anyMatch(source::startsWith);
    }
}
