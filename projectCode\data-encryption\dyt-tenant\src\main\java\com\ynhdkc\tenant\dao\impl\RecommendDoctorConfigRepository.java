package com.ynhdkc.tenant.dao.impl;

import backend.common.exception.BizException;
import backend.common.util.MybatisUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ynhdkc.tenant.dao.mapper.RecommendDoctorConfigMapper;
import com.ynhdkc.tenant.entity.RecommendDoctorConfig;
import com.ynhdkc.tenant.model.RecommendDoctorConfigCreateDto;
import com.ynhdkc.tenant.model.RecommendDoctorConfigQueryDto;
import com.ynhdkc.tenant.model.RecommendDoctorConfigUpdateDto;
import com.ynhdkc.tenant.model.RecommendDoctorConfigVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2025/01/06/15:47
 */
@Repository
public class RecommendDoctorConfigRepository {
    @Autowired
    private RecommendDoctorConfigMapper mapper;

    /**
     * 插入一个新的推荐医生配置
     */
    public void insert(RecommendDoctorConfigCreateDto createDto) {
        if (createDto != null && StringUtils.hasText(createDto.getDataTag())) {
            Stream.of(createDto.getDataTag().split("\\|")).forEach(dataTag -> {
                RecommendDoctorConfig entity = convertToEntity(createDto);
                entity.setDataTag(dataTag);
                mapper.insert(entity);
            });
        }
    }


    public List<RecommendDoctorConfig> queryListByRecommendId(Long id) {
        return mapper.selectByExample2(RecommendDoctorConfig.class,
                sql -> sql.andEqualTo(RecommendDoctorConfig::getEnabled, 1)
                        .andEqualTo(RecommendDoctorConfig::getRecommendId, id)

        );
    }

    public List<RecommendDoctorConfig> queryListByRecommendIdAndDataTag(Long id, String dataTag) {
        return mapper.selectByExample2(RecommendDoctorConfig.class,
                sql -> sql.andEqualTo(RecommendDoctorConfig::getEnabled, 1)
                        .andEqualTo(RecommendDoctorConfig::getRecommendId, id).andEqualTo(RecommendDoctorConfig::getDataTag, dataTag)

        );
    }

    /**
     * 删除指定ID的推荐医生配置
     */
    public void deleteById(Long id) {
        mapper.deleteByPrimaryKey(id);
    }

    /**
     * 根据ID获取推荐医生配置详情
     */
    public RecommendDoctorConfig getById(Long id) {
        return mapper.selectByPrimaryKey(id);
    }

    /**
     * 分页查询推荐医生配置列表
     */
    public PageInfo<RecommendDoctorConfig> getPage(RecommendDoctorConfigQueryDto queryDto) {
        Example example = buildExampleFromQueryDto(queryDto);
        PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
        List<RecommendDoctorConfig> list = mapper.selectByExample(example);
        return new PageInfo<>(list);
    }

    /**
     * 更新指定ID的推荐医生配置信息
     */
    public void updateById(Long id, RecommendDoctorConfigUpdateDto updateDto) {
        // 首先获取数据库中现有的实体对象
        RecommendDoctorConfig existingEntity = mapper.selectByPrimaryKey(id);
        if (existingEntity == null) {
            throw new BizException("找不到推荐医生配置");
        }

        // 将DTO中的非空属性复制到现有实体中
        copyNonNullProperties(updateDto, existingEntity);

        // 使用selective方法更新，这样可以保证只有非空字段被更新
        mapper.updateByPrimaryKeySelective(existingEntity);
    }

    public void update(RecommendDoctorConfig updateEntity) {
        mapper.updateByPrimaryKeySelective(updateEntity);
    }

    private RecommendDoctorConfig convertToEntity(RecommendDoctorConfigCreateDto createDto) {
        // DTO 转换为 Entity 的逻辑
        RecommendDoctorConfig entity = new RecommendDoctorConfig();
        entity.setRecommendId(createDto.getRecommendId());
        entity.setDoctorId(createDto.getDoctorId());
        entity.setDepartmentId(createDto.getDepartmentId());
        entity.setHospitalAreaId(createDto.getHospitalAreaId());
        entity.setHospitalAreaCode(createDto.getHospitalAreaCode());
        entity.setHospitalAreaName(createDto.getHospitalAreaName());
        entity.setHospitalId(createDto.getHospitalId());
        entity.setSort(createDto.getSort());
        entity.setHospitalName(createDto.getHospitalName());
        entity.setDataTag(createDto.getDataTag());
        entity.setEnabled(createDto.isEnabled());
        return entity;
    }

    /**
     * 从源对象拷贝非空属性到目标对象
     */
    private void copyNonNullProperties(RecommendDoctorConfigUpdateDto source, RecommendDoctorConfig target) {
        if (source.getRecommendId() != null) {
            target.setRecommendId(source.getRecommendId());
        }
        if (source.getDoctorId() != null) {
            target.setDoctorId(source.getDoctorId());
        }
        if (source.getDepartmentId() != null) {
            target.setDepartmentId(source.getDepartmentId());
        }
        if (source.getHospitalAreaId() != null) {
            target.setHospitalAreaId(source.getHospitalAreaId());
        }
        if (source.getHospitalAreaCode() != null) {
            target.setHospitalAreaCode(source.getHospitalAreaCode());
        }
        if (source.getHospitalAreaName() != null) {
            target.setHospitalAreaName(source.getHospitalAreaName());
        }
        if (source.getHospitalId() != null) {
            target.setHospitalId(source.getHospitalId());
        }
        if (source.getSort() != null) {
            target.setSort(source.getSort());
        }
        if (source.getHospitalName() != null) {
            target.setHospitalName(source.getHospitalName());
        }
        if (source.getDataTag() != null) {
            target.setDataTag(source.getDataTag());
        }
        if (source.isEnabled() != null) {
            target.setEnabled(source.isEnabled());
        }
        if (source.getDataTag() != null) {
            target.setDataTag(source.getDataTag());
        }
    }

    private void updateEntityFromDto(RecommendDoctorConfig entity, RecommendDoctorConfigUpdateDto updateDto) {
        // DTO 更新 Entity 的逻辑
        entity.setRecommendId(updateDto.getRecommendId());
        entity.setDoctorId(updateDto.getDoctorId());
        entity.setDepartmentId(updateDto.getDepartmentId());
        entity.setHospitalAreaId(updateDto.getHospitalAreaId());
        entity.setHospitalAreaCode(updateDto.getHospitalAreaCode());
        entity.setHospitalAreaName(updateDto.getHospitalAreaName());
        entity.setHospitalId(updateDto.getHospitalId());
        entity.setSort(updateDto.getSort());
        entity.setHospitalName(updateDto.getHospitalName());
        entity.setDataTag(updateDto.getDataTag());
        entity.setEnabled(updateDto.isEnabled());
    }

    /**
     * 根据查询DTO构建Example对象
     */
    /**
     * 根据查询DTO构建Example对象。
     *
     * @param queryDto 查询条件DTO
     * @return 构建好的Example对象
     */
    private Example buildExampleFromQueryDto(RecommendDoctorConfigQueryDto queryDto) {
        Example example = new Example(RecommendDoctorConfig.class);
        Example.Criteria criteria = example.createCriteria();

        if (queryDto != null) {
            if (queryDto.getRecommendId() != null) {
                criteria.andEqualTo("recommendId", queryDto.getRecommendId());
            }
            if (queryDto.getDoctorId() != null) {
                criteria.andEqualTo("doctorId", queryDto.getDoctorId());
            }
            if (queryDto.getHospitalId() != null) {
                criteria.andEqualTo("hospitalId", queryDto.getHospitalId());
            }
            if (queryDto.getDepartmentId() != null) {
                criteria.andEqualTo("departmentId", queryDto.getDepartmentId());
            }
            if (queryDto.getHospitalAreaId() != null) {
                criteria.andEqualTo("hospitalAreaId", queryDto.getHospitalAreaId());
            }
            if (queryDto.getHospitalAreaCode() != null) {
                criteria.andLike("hospitalAreaCode", MybatisUtil.likeBoth(queryDto.getHospitalAreaCode()));
            }
            if (queryDto.getHospitalAreaName() != null) {
                criteria.andLike("hospitalAreaName", MybatisUtil.likeBoth(queryDto.getHospitalAreaName()));
            }
            if (queryDto.getHospitalName() != null) {
                criteria.andLike("hospitalName", MybatisUtil.likeBoth(queryDto.getHospitalName()));
            }
            if (queryDto.getDataTag() != null) {
                criteria.andLike("dataTag", MybatisUtil.likeBoth(queryDto.getDataTag()));
            }
            if (queryDto.isEnabled() != null) {
                criteria.andEqualTo("enabled", queryDto.isEnabled());
            }

        }

        // 添加排序规则（如果需要）
        example.orderBy("sort").desc().orderBy("id").desc();

        return example;
    }

    private RecommendDoctorConfigVo convertToVo(RecommendDoctorConfig entity) {
        // Entity 转换为 VO 的逻辑
        RecommendDoctorConfigVo vo = new RecommendDoctorConfigVo();
        // 设置VO属性...
        vo.setId(entity.getId());
        vo.setRecommendId(entity.getRecommendId());
        vo.setDoctorId(entity.getDoctorId());
        vo.setDepartmentId(entity.getDepartmentId());
        vo.setHospitalAreaId(entity.getHospitalAreaId());
        vo.setHospitalAreaCode(entity.getHospitalAreaCode());
        vo.setHospitalAreaName(entity.getHospitalAreaName());
        vo.setHospitalId(entity.getHospitalId());
        vo.setSort(entity.getSort());
        vo.setHospitalName(entity.getHospitalName());
        vo.setDataTag(entity.getDataTag());
        vo.setEnabled(entity.getEnabled());
        return vo;
    }
}
