package com.ynhdkc.tenant.tool;

import cn.hutool.core.date.DateUtil;
import com.ynhdkc.tenant.dao.DepartmentQuery;
import com.ynhdkc.tenant.dao.DoctorQuery;
import com.ynhdkc.tenant.dao.HospitalAreaSettingQuery;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class ScheduleUtil {

    private final HospitalAreaSettingQuery hospitalAreaSettingQuery;
    private final DepartmentQuery departmentQuery;
    private final DoctorQuery doctorQuery;
    private final DoctorListTool doctorListTool;

    public Map<Long, Pair<Date, Date>> getScheduleDate(List<Long> hospitalAreaIds, List<Long> departmentIds, List<Long> doctorIds) {
        List<AppointmentRuleSetting> appointmentRuleSettings = hospitalAreaSettingQuery.queryAppointmentRuleSettingInId(hospitalAreaIds);
        Map<Long, AppointmentRuleSetting> settingMap = appointmentRuleSettings.stream().collect(Collectors.toMap(AppointmentRuleSetting::getHospitalAreaId, a -> a, (o1, o2) -> o1));
        List<Department> departments = departmentQuery.queryBy(departmentIds);
        Map<Long, Department> departmentMap = departments.stream().collect(Collectors.toMap(Department::getId, a -> a, (o1, o2) -> o1));
        List<Doctor> doctors = doctorQuery.queryBy(doctorIds);
        List<HospitalDepartmentDoctor> hddList = doctors.stream().map(d -> {
            Department department = departmentMap.get(d.getDepartmentId());
            AppointmentRuleSetting setting = settingMap.get(d.getHospitalAreaId());
            return new HospitalDepartmentDoctor()
                    .setDoctorId(d.getId())
                    .setAppointmentRuleSetting(setting)
                    .setDepartment(department)
                    .setDoctor(d);
        }).collect(Collectors.toList());

        return hddList.stream().collect(Collectors.toMap(HospitalDepartmentDoctor::getDoctorId,
                hdd -> {
                    Department department = hdd.getDepartment();
                    AppointmentRuleSetting setting = hdd.getAppointmentRuleSetting();
                    Pair<Integer, Integer> pair = doctorListTool.defaultAdvanceAndForbiddenDays(hdd.getDoctor(), department, setting);
                    return Pair.of(getDate(pair, true), getDate(pair, false));
                },
                (o1, o2) -> o1));
    }

    private Date getDate(Pair<Integer, Integer> pair, boolean isStart) {
        Date today = DateUtil.parse(DateUtil.today(), "yyyy-MM-dd");
        if (pair == null) {
            return null;
        }
        Integer offset = isStart ? pair.getRight() : pair.getLeft();
        if (offset == null) {
            return null;
        }
        if (isStart) {
            offset = offset == -1 ? 0 : offset;
        } else {
            offset = offset == -1 ? 7 : offset;
        }
        return DateUtil.offsetDay(today, offset).toJdkDate();
    }

    @Setter
    @Getter
    @Accessors(chain = true)
    private static class HospitalDepartmentDoctor {
        private Long doctorId;

        private AppointmentRuleSetting appointmentRuleSetting;

        private Department department;

        private Doctor doctor;
    }
}
