package backend.security.oplog.dto;

import backend.common.util.MessageUtil;
import backend.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class OpLogMessageBuilder {

    private static final List<SchemaField> SCHEMA_FIELDS = new ArrayList<>();

    private static final SchemaField KEY_FIELD = SchemaField.builder().type("string").optional(false).build();

    static {
        SCHEMA_FIELDS.add(SchemaField.builder().field("op_code").type("string").optional(false).build());
        SCHEMA_FIELDS.add(SchemaField.builder().field("op_code_cn").type("string").optional(false).build());
        SCHEMA_FIELDS.add(SchemaField.builder().field("url").type("string").optional(false).build());
        SCHEMA_FIELDS.add(SchemaField.builder().field("method").type("string").optional(false).build());
        SCHEMA_FIELDS.add(SchemaField.builder().field("tenant_id").type("int64").optional(false).build());
        SCHEMA_FIELDS.add(SchemaField.builder().field("user_id").type("int64").optional(false).build());
        SCHEMA_FIELDS.add(SchemaField.builder().field("user_name").type("string").optional(false).build());
        SCHEMA_FIELDS.add(SchemaField.builder().field("error").type("boolean").optional(false).build());
        SCHEMA_FIELDS.add(SchemaField.builder().field("exception").type("string").optional(false).build());
        SCHEMA_FIELDS.add(SchemaField.builder().field("module_name").type("string").optional(false).build());
        SCHEMA_FIELDS.add(SchemaField.builder().field("raw_message").type("string").optional(false).build());
        SCHEMA_FIELDS.add(SchemaField.builder().field("type").type("string").optional(false).build());
        SCHEMA_FIELDS.add(SchemaField.builder().field("timestamp").type("int64").optional(false).build());
    }

    public String getOpLogMessage(OpLogModel opLogModel) {
        OpLogMessage opLogMessage = new OpLogMessage();
        opLogMessage.setPayload(opLogModel);

        opLogMessage.setSchema(Schema.builder().fields(SCHEMA_FIELDS).version(1).optional(false).type("struct").build());
        return MessageUtil.object2JSONString(opLogMessage);
    }

    public String getOpLogKey() {
        Map<String, Object> keyMap = new HashMap<>();
        keyMap.put("schema", KEY_FIELD);
        keyMap.put("payload", StringUtils.getUUID32());

        return MessageUtil.object2JSONString(keyMap);
    }

}
