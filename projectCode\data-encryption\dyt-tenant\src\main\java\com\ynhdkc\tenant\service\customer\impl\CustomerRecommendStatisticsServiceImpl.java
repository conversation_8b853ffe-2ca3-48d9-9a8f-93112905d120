package com.ynhdkc.tenant.service.customer.impl;

import com.ynhdkc.tenant.dto.document.UserBehaviorTraceDocument;
import com.ynhdkc.tenant.entity.constant.RecommendStatisticsConstant;
import com.ynhdkc.tenant.entity.constant.UserAction;
import com.ynhdkc.tenant.model.RecommendStatisticsReqDto;
import com.ynhdkc.tenant.model.UserActionDto;
import com.ynhdkc.tenant.service.customer.CustomerRecommendStatisticsService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/11/1 17:39
 */
@Service
@RequiredArgsConstructor
public class CustomerRecommendStatisticsServiceImpl implements CustomerRecommendStatisticsService {
    private final RedisTemplate<String, Object> redisTemplate;

    private final ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Override
    public void recommendIncrement(Long rid) {
        String key = RecommendStatisticsConstant.RECOMMEND_STATISTICS_KEY_PREX + ":" + rid;
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            redisTemplate.opsForValue().increment(key);
        } else {
            redisTemplate.opsForValue().set(key, 1);
        }
    }

    @Override
    public void recommendIncrement(RecommendStatisticsReqDto request, Long userId) {
        recommendIncrement(request.getRid());
        asyncSaveUserBehaviorTraceDocument(userId, request.getUserAction());
    }

    private void asyncSaveUserBehaviorTraceDocument(Long userId, UserActionDto userActionDto) {
        new Thread(() -> {
            UserBehaviorTraceDocument document = new UserBehaviorTraceDocument();
            document.setUserId(userId);
            document.setIp(userActionDto.getIp());
            document.setChannel(userActionDto.getChannel());
            document.setAction(UserAction.CLICK_RECOMMEND_RESOURCE.getCode());
            document.setActionResource(userActionDto.getActionResource());
            document.setActionTime(new Date());
            elasticsearchRestTemplate.save(document, IndexCoordinates.of(UserBehaviorTraceDocument.INDEX_NAME));
        }).start();
    }
}
