package backend.encryption;

import backend.encryption.annotation.EncryptField;
import backend.encryption.util.AESGCMUtil;
import backend.encryption.util.EncryptionUtil;
import backend.encryption.util.SM2Util;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 加密工具类测试
 *
 * <AUTHOR> Backend Team
 * @version 1.1-SNAPSHOT
 * @since 2024-06-24
 */
class EncryptionUtilTest {
    
    private static final String TEST_PLAIN_TEXT = "这是一个测试字符串，包含中文和English123!@#";
    
    @Test
    void testAESGCMEncryption() {
        // 测试AES-GCM加密解密
        String encrypted = AESGCMUtil.encrypt(TEST_PLAIN_TEXT);
        assertNotNull(encrypted);
        assertNotEquals(TEST_PLAIN_TEXT, encrypted);
        
        String decrypted = AESGCMUtil.decrypt(encrypted);
        assertEquals(TEST_PLAIN_TEXT, decrypted);
        
        // 测试验证功能
        assertTrue(AESGCMUtil.verify(TEST_PLAIN_TEXT));
    }
    
    @Test
    void testSM2Encryption() {
        // 生成密钥对
        SM2Util.KeyPair keyPair = SM2Util.generateKeyPair();
        assertNotNull(keyPair.getPublicKey());
        assertNotNull(keyPair.getPrivateKey());

        // 测试SM2加密解密（使用默认密钥，因为我们的简化实现使用固定密钥）
        String encrypted = SM2Util.encrypt(TEST_PLAIN_TEXT, null);
        assertNotNull(encrypted);
        assertNotEquals(TEST_PLAIN_TEXT, encrypted);

        String decrypted = SM2Util.decrypt(encrypted, null);
        assertEquals(TEST_PLAIN_TEXT, decrypted);

        // 测试验证功能
        assertTrue(SM2Util.verify(TEST_PLAIN_TEXT));
    }
    
    @Test
    void testUnifiedEncryptionUtil() {
        // 测试AES-GCM
        String aesEncrypted = EncryptionUtil.encrypt(TEST_PLAIN_TEXT, EncryptField.AlgorithmType.AES_GCM);
        assertNotNull(aesEncrypted);
        String aesDecrypted = EncryptionUtil.decrypt(aesEncrypted, EncryptField.AlgorithmType.AES_GCM);
        assertEquals(TEST_PLAIN_TEXT, aesDecrypted);
        
        // 测试SM2
        String sm2Encrypted = EncryptionUtil.encrypt(TEST_PLAIN_TEXT, EncryptField.AlgorithmType.SM2);
        assertNotNull(sm2Encrypted);
        String sm2Decrypted = EncryptionUtil.decrypt(sm2Encrypted, EncryptField.AlgorithmType.SM2);
        assertEquals(TEST_PLAIN_TEXT, sm2Decrypted);
        
        // 测试默认算法
        String defaultEncrypted = EncryptionUtil.encrypt(TEST_PLAIN_TEXT);
        assertNotNull(defaultEncrypted);
        String defaultDecrypted = EncryptionUtil.decrypt(defaultEncrypted);
        assertEquals(TEST_PLAIN_TEXT, defaultDecrypted);
    }
    
    @Test
    void testVerifyAll() {
        Map<EncryptField.AlgorithmType, Boolean> results = EncryptionUtil.verifyAll(TEST_PLAIN_TEXT);
        
        // 验证AES-GCM
        assertTrue(results.get(EncryptField.AlgorithmType.AES_GCM));
        
        // 验证SM2
        assertTrue(results.get(EncryptField.AlgorithmType.SM2));
        
        // SM4暂未实现，应该回退到AES-GCM
        assertTrue(results.get(EncryptField.AlgorithmType.SM4));
    }
    
    @Test
    void testEmptyAndNullValues() {
        // 测试空字符串
        assertEquals("", EncryptionUtil.encrypt(""));
        assertEquals("", EncryptionUtil.decrypt(""));
        
        // 测试null值
        assertNull(EncryptionUtil.encrypt(null));
        assertNull(EncryptionUtil.decrypt(null));
        
        // 测试空白字符串
        assertEquals("   ", EncryptionUtil.encrypt("   "));
        assertEquals("   ", EncryptionUtil.decrypt("   "));
    }
    
    @Test
    void testDifferentDataTypes() {
        // 测试不同类型的数据
        String[] testData = {
            "简单文本",
            "123456789",
            "special@#$%^&*()characters",
            "混合内容123ABC!@#",
            "很长的文本内容，包含各种字符：中文、English、数字123、特殊符号!@#$%^&*()_+-=[]{}|;':\",./<>?",
            "单字符a",
            "🎉🎊🎈emoji测试"
        };
        
        for (String data : testData) {
            // 测试AES-GCM
            String aesEncrypted = EncryptionUtil.encrypt(data, EncryptField.AlgorithmType.AES_GCM);
            String aesDecrypted = EncryptionUtil.decrypt(aesEncrypted, EncryptField.AlgorithmType.AES_GCM);
            assertEquals(data, aesDecrypted, "AES-GCM failed for: " + data);
            
            // 测试SM2
            String sm2Encrypted = EncryptionUtil.encrypt(data, EncryptField.AlgorithmType.SM2);
            String sm2Decrypted = EncryptionUtil.decrypt(sm2Encrypted, EncryptField.AlgorithmType.SM2);
            assertEquals(data, sm2Decrypted, "SM2 failed for: " + data);
        }
    }
}
