package com.ynhdkc.tenant.entity;

import backend.common.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Table;

/**
 * <p>
 * 医院列表规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(name = "t_hospital_list_rule")
@ApiModel(value = "HospitalListRule对象", description = "医院列表规则表")
public class HospitalListRule extends BaseEntity {

    private String name;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("规则类型：1-固定医院 2-分组距离 3-分组优先 4-广告推荐 5-分组混合距离")
    private Integer type;

    @ApiModelProperty("权重排序规则：1-升序 2-降序")
    private Integer weightOrderBy;

    @ApiModelProperty("距离排序规则：1-升序 2-降序")
    private Integer distanceOrderBy;

    @ApiModelProperty("对应医院分组ID，当type值为1、2、3时必填")
    private Long hospitalGroupId;

    @ApiModelProperty("广告推荐模式下对应广告id，当type值为4时必填")
    private Long advertisementId;

    @ApiModelProperty("广告推荐模式下对应广告类型，当type值为4时必填，1-推荐条件广告")
    private Integer advertisementType;

    @ApiModelProperty("分组混合距离模式下对应混合规则，当type为5时必填，[{group_id:2, count: 10},{ group_id:3, count: 20},...,]")
    private String groupScale;

    @Data
    public static class GroupScale {
        @JsonProperty("group_id")
        private Long groupId;
        private Integer count;
    }
}
