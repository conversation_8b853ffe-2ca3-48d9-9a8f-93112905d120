package com.ynhdkc.tenant.tool;

import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.util.UploadFileUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
@RequiredArgsConstructor
public class DoctorHeadImageHandler {

    private final UploadFileUtil uploadFileUtil;

    public void setDoctorImageUrl(Doctor doctor, String headImgUrl) {
        if (!StringUtils.hasText(headImgUrl)) {
            return;
        }

        String preGeneratedUrl = preGenerateImageUrl(doctor.getHospitalCode(), doctor.getDepartmentCode(), doctor.getThrdpartDoctorCode(), headImgUrl);

        if (!preGeneratedUrl.equals(doctor.getHeadImgUrl())) {
            String path = doctor.getHospitalCode() + "/" + doctor.getDepartmentCode().replace("#", "_");
            doctor.setHeadImgUrl(uploadFileUtil.upload(path, headImgUrl, doctor.getThrdpartDoctorCode()));
        }
    }

    public String getDoctorImageUrl(String headImgUrl, String hospitalCode, String departmentCode, String thrdpartDoctorCode) {
        if (!StringUtils.hasText(headImgUrl)) {
            return null;
        }

        String preGeneratedUrl = preGenerateImageUrl(hospitalCode, departmentCode, thrdpartDoctorCode, headImgUrl);

        if (!preGeneratedUrl.equals(headImgUrl)) {
            String path = hospitalCode + "/" + departmentCode.replace("#", "_");
            return uploadFileUtil.upload(path, headImgUrl, thrdpartDoctorCode);
        }

        return headImgUrl;
    }

    private String preGenerateImageUrl(String hospitalCode, String departmentCode, String thrdpartDoctorCode, String headImgUrl) {
        String path = hospitalCode + "/" + departmentCode.replace("#", "_");
        return uploadFileUtil.preGenerateUrl(path, headImgUrl, thrdpartDoctorCode);
    }
}
