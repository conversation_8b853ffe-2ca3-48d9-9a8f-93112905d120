package com.ynhdkc.tenant.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;

import java.util.Objects;

/**
 * ApiQuickCheckOrderCountItem
 */
@Validated
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2025-03-31T20:45:41.667+08:00")


public class ApiQuickCheckOrderCountItem {
  @JsonProperty("key")
  private String key = null;

  @JsonProperty("value")
  private Integer value = null;

  public ApiQuickCheckOrderCountItem key(String key) {
    this.key = key;
    return this;
  }

  /**
   * code
   * @return key
  **/
  @ApiModelProperty(value = "code")


  public String getKey() {
    return key;
  }

  public void setKey(String key) {
    this.key = key;
  }

  public ApiQuickCheckOrderCountItem value(Integer value) {
    this.value = value;
    return this;
  }

  /**
   * 预约状态数量
   * @return value
  **/
  @ApiModelProperty(value = "预约状态数量")


  public Integer getValue() {
    return value;
  }

  public void setValue(Integer value) {
    this.value = value;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ApiQuickCheckOrderCountItem quickCheckOrderCountItem = (ApiQuickCheckOrderCountItem) o;
    return Objects.equals(this.key, quickCheckOrderCountItem.key) &&
        Objects.equals(this.value, quickCheckOrderCountItem.value);
  }

  @Override
  public int hashCode() {
    return Objects.hash(key, value);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ApiQuickCheckOrderCountItem {\n");
    
    sb.append("    key: ").append(toIndentedString(key)).append("\n");
    sb.append("    value: ").append(toIndentedString(value)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

