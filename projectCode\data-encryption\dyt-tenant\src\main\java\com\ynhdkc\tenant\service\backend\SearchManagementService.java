package com.ynhdkc.tenant.service.backend;

import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.model.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/9/12 15:53
 */
public interface SearchManagementService {
    DictFileVo getDictDetail(Long dictId);

    DictFileQueryRespDto queryDict(DictFileQueryReqDto request);

    DictFileVo createDict(DictFileCreateReqDto request);

    DictFileVo updateDict(DictFileUpdateReqDto request);

    BaseOperationResponse deleteDict(Long dictId);

    BaseOperationResponse syncDict();
}
