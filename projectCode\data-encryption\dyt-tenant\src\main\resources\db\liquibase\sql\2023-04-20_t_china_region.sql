create table t_china_region
(
    id           bigint auto_increment not null comment 'id'
        primary key,
    pid          bigint                null default 0 comment '父id',
    short_name   varchar(100)          null comment '简称',
    name         varchar(100)          null comment '名称',
    merger_name  varchar(255)          null comment '全称',
    level        int               null comment '层级 0 1 2 省市区县',
    pinyin       varchar(100)          null comment '拼音',
    code         varchar(100)          null comment '长途区号',
    zip_code     varchar(100)          null comment '邮编',
    first        varchar(50)           null comment '首字母',
    lng          varchar(100)          null comment '经度',
    lat          varchar(100)          null comment '纬度',
    fid          int                   null comment '墨迹天气查询ID',
    spell_simple varchar(50)           null comment '拼音首字符缩写',
    create_time  datetime(3)                default CURRENT_TIMESTAMP(3) not null,
    update_time  datetime(3)           null on update CURRENT_TIMESTAMP(3)
);