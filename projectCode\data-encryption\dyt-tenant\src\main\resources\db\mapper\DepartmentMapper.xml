<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ynhdkc.tenant.dao.mapper.DepartmentMapper">

    <!-- 定义 resultMap -->
    <resultMap id="DepartmentResultMap" type="com.ynhdkc.tenant.entity.Department">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="hospital_id" property="hospitalId" />
        <result column="hospital_area_id" property="hospitalAreaId" />
        <result column="hospital_code" property="hospitalCode" />
        <result column="parent_id" property="parentId" />
        <result column="thrdpart_dep_code" property="thrdpartDepCode" />
        <result column="name" property="name" />
        <result column="display_department_name" property="displayDepartmentName" />
        <result column="first_letter" property="firstLetter" />
        <result column="short_name" property="shortName" />
        <result column="introduction" property="introduction" />
        <result column="sort" property="sort" />
        <result column="recommended" property="recommended" />
        <result column="caution" property="caution" />
        <result column="category" property="category" />
        <result column="address_intro" property="addressIntro" />
        <result column="triage_desk_address" property="triageDeskAddress" />
        <result column="system_depends" property="systemDepends" />
        <result column="enabled" property="enabled" />
        <result column="display_bg_color" property="displayBgColor" />
        <result column="enable_department_detail" property="enableDepartmentDetail" />
        <result column="uri" property="uri" />
        <result column="forbidden_day" property="forbiddenDay" />
        <result column="advance_day" property="advanceDay" />
        <result column="source_activate_time" property="sourceActivateTime" />
        <result column="remark" property="remark" />
        <result column="source" property="source" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="doctors" property="doctors" />
        <result column="level_tag" property="levelTag" />
        <result column="display_fields" property="displayFields" />
    </resultMap>

    <!-- 分页查询 -->
    <select id="queryDepartWithCategory" resultMap="DepartmentResultMap">
        SELECT *
        FROM t_hospital_department
        <where>
            <if test="option.hospitalId != null">
                AND hospital_id = #{option.hospitalId}
            </if>
            <if test="option.hospitalAreaId != null">
                AND hospital_area_id = #{option.hospitalAreaId}
            </if>
            <if test="option.departmentName != null and option.departmentName != ''">
                AND name LIKE CONCAT('%', #{option.departmentName}, '%')
            </if>
            <if test="option.thrdpartDepCode != null and option.thrdpartDepCode != ''">
                AND thrdpart_dep_code = #{option.thrdpartDepCode}
            </if>
            <if test="option.id != null">
                AND id = #{option.id}
            </if>
            <if test="option.enabled != null">
                AND enabled = #{option.enabled}
            </if>
            <if test="option.category != null and option.category.size > 0">
                AND (
                <foreach collection="option.category" item="cat" separator=" OR ">
                    FIND_IN_SET(#{cat}, category) > 0
                </foreach>
                )
            </if>
            <if test="option.excludeCategory != null and option.excludeCategory.size > 0">
                AND (
                <foreach collection="option.excludeCategory" item="cat" separator=" AND ">
                    FIND_IN_SET(#{cat}, category) = 0
                </foreach>
                )
            </if>
        </where>
        ORDER BY sort DESC, id ASC
    </select>

</mapper>