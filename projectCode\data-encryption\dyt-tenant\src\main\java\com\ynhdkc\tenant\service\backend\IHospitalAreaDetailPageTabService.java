package com.ynhdkc.tenant.service.backend;

import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.entity.detailpage.HospitalAreaDetailPageTab;
import com.ynhdkc.tenant.model.*;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/24 19:53:07
 */
public interface IHospitalAreaDetailPageTabService {
    HospitalAreaDetailPageTabVo createHospitalAreaDetailPageTab(CreateHospitalAreaDetailPageTabReqDto dto);

    BaseOperationResponse deleteHospitalAreaDetailPageTab(Long id);

    HospitalAreaDetailPageTabVo updateHospitalAreaDetailPageTab(Long tabId, UpdateHospitalAreaDetailPageTabReqDto dto);

    HospitalAreaDetailPageTabVo getHospitalAreaDetailPageTab(Long id);

    HospitalAreaDetailPageTabPageVo searchHospitalAreaDetailPageTab(SearchHospitalAreaDetailPageTabReqDto dto);

    List<HospitalAreaDetailPageTabVo> getHospitalAreaDetailPageTabByHospitalAreaId(Long hospitalAreaId);

    static HospitalAreaDetailPageTabVo toVo(HospitalAreaDetailPageTab entity) {
        HospitalAreaDetailPageTabVo vo = new HospitalAreaDetailPageTabVo();
        vo.setId(entity.getId());
        vo.setTenantId(entity.getTenantId());
        vo.setHospitalId(entity.getHospitalId());
        vo.setHospitalAreaId(entity.getHospitalAreaId());
        vo.setTitle(entity.getTitle());
        vo.setComponentType(entity.getComponentType());
        vo.setRecommendId(entity.getRecommendId());
        vo.setSort(entity.getSort());
        vo.setStatus(entity.getStatus());
        if (StringUtils.hasText(entity.getChannels())) {
            vo.setChannels((Arrays.stream(entity.getChannels().split(",")).
                    map(Integer::parseInt)
                    .collect(Collectors.toList()))
            );
        }
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdateTime(entity.getUpdateTime());
        return vo;
    }

    default void updateEntity(HospitalAreaDetailPageTab entity, UpdateHospitalAreaDetailPageTabReqDto dto) {
        entity.setTitle(dto.getTitle());
        entity.setComponentType(dto.getComponentType());
        entity.setRecommendId(dto.getRecommendId());
        entity.setSort(dto.getSort());
        if (!CollectionUtils.isEmpty(dto.getChannels())) {
            entity.setChannels(dto.getChannels().stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        entity.setStatus(dto.getStatus());
    }

    default HospitalAreaDetailPageTab toEntity(CreateHospitalAreaDetailPageTabReqDto dto) {
        HospitalAreaDetailPageTab entity = new HospitalAreaDetailPageTab();
        entity.setTenantId(dto.getTenantId());
        entity.setHospitalId(dto.getHospitalId());
        entity.setHospitalAreaId(dto.getHospitalAreaId());
        entity.setTitle(dto.getTitle());
        entity.setComponentType(dto.getComponentType());
        entity.setRecommendId(dto.getRecommendId());
        entity.setSort(dto.getSort());
        if (!CollectionUtils.isEmpty(dto.getChannels())) {
            entity.setChannels(dto.getChannels().stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        entity.setStatus(dto.getStatus());
        return entity;
    }

}
