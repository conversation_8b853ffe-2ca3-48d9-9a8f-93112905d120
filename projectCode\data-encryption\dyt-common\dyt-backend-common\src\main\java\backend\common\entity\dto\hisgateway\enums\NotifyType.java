package backend.common.entity.dto.hisgateway.enums;


public enum NotifyType {
    NOTIFY_PAY_SUCCESS(1, "支付/挂号成功"),
    NOTIFY_REFUND_SUCCESS(2, "退款/退号成功");

    private final Integer value;

    private final String desc;

    NotifyType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static Occupation getFromValue(String value) {
        for (Occupation t : Occupation.values()) {
            if (t.getValue().equals(value)) {
                return t;
            }
        }
        throw new IllegalArgumentException("职业分类不存在");
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
