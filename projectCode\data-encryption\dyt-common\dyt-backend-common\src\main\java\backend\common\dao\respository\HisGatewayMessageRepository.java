package backend.common.dao.respository;

import backend.common.constants.HisOperation;
import backend.common.entity.dto.hisgateway.request.*;
import backend.common.entity.dto.hisgateway.response.MessageEnvelope;
import backend.common.enums.HospitalCode;
import backend.common.util.MessageUtil;
import backend.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Repository
public class HisGatewayMessageRepository {

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;

    public void send(DepartmentListRequest departmentListRequest, Long hospitalId, Long hospitalAreaId) {
        send(departmentListRequest, hospitalId, hospitalAreaId, null);
    }

    public void send(DepartmentListRequest departmentListRequest, Long hospitalId, Long hospitalAreaId, String messageTraceId) {
        MessageEnvelope<DepartmentListRequest> messageEnvelope = new MessageEnvelope<>();
        assembleEnvelope(messageEnvelope, hospitalId, messageTraceId);
        messageEnvelope.setParameterMap(departmentListRequest);
        messageEnvelope.setHospitalAreaId(hospitalAreaId);
        messageEnvelope.setHospitalCode(departmentListRequest.getHospitalCode());

        String message = MessageUtil.object2JSONString(messageEnvelope);
        send(message, HisOperation.DEPARTMENT_LIST);
    }

    public void send(DepartmentList4AdminRequest departmentList4AdminRequest, Long hospitalId, Long hospitalAreaId) {
        send(departmentList4AdminRequest, hospitalId, hospitalAreaId, null);
    }

    public void send(DepartmentList4AdminRequest departmentList4AdminRequest, Long hospitalId, Long hospitalAreaId, String messageTraceId) {
        MessageEnvelope<DepartmentList4AdminRequest> messageEnvelope = new MessageEnvelope<>();
        assembleEnvelope(messageEnvelope, hospitalId, messageTraceId);
        messageEnvelope.setParameterMap(departmentList4AdminRequest);
        messageEnvelope.setHospitalAreaId(hospitalAreaId);
        messageEnvelope.setHospitalCode(departmentList4AdminRequest.getHospitalCode());

        String message = MessageUtil.object2JSONString(messageEnvelope);
        send(message, HisOperation.DEPARTMENT_LIST_4_ADMIN);
    }

    public void send(DepartmentDoctorListRequest departmentDoctorListRequest, Long hospitalId, Long hospitalAreaId, Long departmentId) {
        send(departmentDoctorListRequest, hospitalId, hospitalAreaId, departmentId, null);
    }

    public void send(DepartmentDoctorListRequest departmentDoctorListRequest, Long hospitalId, Long hospitalAreaId,
                     Long departmentId, String messageTraceId) {
        MessageEnvelope<DepartmentDoctorListRequest> messageEnvelope = new MessageEnvelope<>();
        assembleEnvelope(messageEnvelope, hospitalId, messageTraceId);
        messageEnvelope.setParameterMap(departmentDoctorListRequest);
        messageEnvelope.setHospitalAreaId(hospitalAreaId);
        messageEnvelope.setDepartmentId(departmentId);
        messageEnvelope.setHospitalCode(departmentDoctorListRequest.getHospitalCode());

        String message = MessageUtil.object2JSONString(messageEnvelope);
        send(message, HisOperation.DEPARTMENT_DOCTOR_LIST);
    }

    public void send(TimeScheduleRequest timeScheduleRequest, Long hospitalId, Long hospitalAreaId, Long departmentId,
                     Long doctorId) {
        send(timeScheduleRequest, hospitalId, hospitalAreaId, departmentId, doctorId, null);
    }

    public void send(TimeScheduleRequest timeScheduleRequest, Long hospitalId, Long hospitalAreaId, Long departmentId,
                     Long doctorId, String messageTraceId) {
        MessageEnvelope<TimeScheduleRequest> messageEnvelope = new MessageEnvelope<>();
        assembleEnvelope(messageEnvelope, hospitalId, messageTraceId);
        messageEnvelope.setParameterMap(timeScheduleRequest);
        messageEnvelope.setHospitalAreaId(hospitalAreaId);
        messageEnvelope.setDepartmentId(departmentId);
        messageEnvelope.setDoctorId(doctorId);
        messageEnvelope.setHospitalCode(timeScheduleRequest.getHospitalCode());

        String message = MessageUtil.object2JSONString(messageEnvelope);
        send(message, HisOperation.TIME_SCHEDULE);
    }

    public void send(DoctorScheduleListRequest doctorScheduleListRequest, Long hospitalId, Long hospitalAreaId,
                     Long departmentId, Long doctorId) {
        send(doctorScheduleListRequest, hospitalId, hospitalAreaId, departmentId, doctorId, null);
    }

    public void send(DoctorScheduleListRequest doctorScheduleListRequest, Long hospitalId, Long hospitalAreaId,
                     Long departmentId, Long doctorId, String messageTraceId) {
        MessageEnvelope<DoctorScheduleListRequest> messageEnvelope = new MessageEnvelope<>();
        assembleEnvelope(messageEnvelope, hospitalId, messageTraceId);
        messageEnvelope.setParameterMap(doctorScheduleListRequest);
        messageEnvelope.setHospitalAreaId(hospitalAreaId);
        messageEnvelope.setDepartmentId(departmentId);
        messageEnvelope.setDoctorId(doctorId);
        messageEnvelope.setHospitalCode(doctorScheduleListRequest.getHospitalCode());

        String message = MessageUtil.object2JSONString(messageEnvelope);
        send(message, HisOperation.DOCTOR_SCHEDULE_LIST);
    }

    public void send(PreRegistrationRequest preRegistrationRequest, Long hospitalId, Long hospitalAreaId,
                     Long departmentId, Long doctorId) {
        send(preRegistrationRequest, hospitalId, hospitalAreaId, departmentId, doctorId, null);
    }

    public void send(PreRegistrationRequest preRegistrationRequest, Long hospitalId, Long hospitalAreaId,
                     Long departmentId, Long doctorId, String messageTraceId) {
        MessageEnvelope<PreRegistrationRequest> messageEnvelope = new MessageEnvelope<>();
        assembleEnvelope(messageEnvelope, hospitalId, messageTraceId);
        messageEnvelope.setParameterMap(preRegistrationRequest);
        messageEnvelope.setHospitalAreaId(hospitalAreaId);
        messageEnvelope.setDepartmentId(departmentId);
        messageEnvelope.setDoctorId(doctorId);
        messageEnvelope.setHospitalCode(preRegistrationRequest.getHospitalCode());

        String message = MessageUtil.object2JSONString(messageEnvelope);
        send(message, HisOperation.PRE_REGISTRATION);
    }

    public void send(CancelPreRegistrationRequest cancelPreRegistrationRequest, Long hospitalId, Long hospitalAreaId,
                     Long departmentId, Long doctorId) {
        send(cancelPreRegistrationRequest, hospitalId, hospitalAreaId, departmentId, doctorId, null);
    }

    public void send(CancelPreRegistrationRequest cancelPreRegistrationRequest, Long hospitalId, Long hospitalAreaId,
                     Long departmentId, Long doctorId, String messageTraceId) {
        MessageEnvelope<CancelPreRegistrationRequest> messageEnvelope = new MessageEnvelope<>();
        assembleEnvelope(messageEnvelope, hospitalId, messageTraceId);
        messageEnvelope.setParameterMap(cancelPreRegistrationRequest);
        messageEnvelope.setHospitalAreaId(hospitalAreaId);
        messageEnvelope.setDepartmentId(departmentId);
        messageEnvelope.setDoctorId(doctorId);
        messageEnvelope.setHospitalCode(cancelPreRegistrationRequest.getHospitalCode());

        String message = MessageUtil.object2JSONString(messageEnvelope);
        send(message, HisOperation.CANCEL_PRE_REGISTRATION);
    }

    public void send(RegistrationRequest registrationRequest, Long hospitalId, Long hospitalAreaId,
                     Long departmentId, Long doctorId) {
        send(registrationRequest, hospitalId, hospitalAreaId, departmentId, doctorId, null);
    }

    public void send(RegistrationRequest registrationRequest, Long hospitalId, Long hospitalAreaId,
                     Long departmentId, Long doctorId, String messageTraceId) {
        MessageEnvelope<RegistrationRequest> messageEnvelope = new MessageEnvelope<>();
        assembleEnvelope(messageEnvelope, hospitalId, messageTraceId);
        messageEnvelope.setParameterMap(registrationRequest);
        messageEnvelope.setHospitalAreaId(hospitalAreaId);
        messageEnvelope.setDepartmentId(departmentId);
        messageEnvelope.setDoctorId(doctorId);
        messageEnvelope.setHospitalCode(registrationRequest.getHospitalCode());

        String message = MessageUtil.object2JSONString(messageEnvelope);
        send(message, HisOperation.REGISTERED);
    }

    public void send(CancelRegistrationRequest cancelRegistrationRequest, Long hospitalId, Long hospitalAreaId,
                     Long departmentId, Long doctorId) {
        send(cancelRegistrationRequest, hospitalId, hospitalAreaId, departmentId, doctorId, null);
    }

    public void send(CancelRegistrationRequest cancelRegistrationRequest, Long hospitalId, Long hospitalAreaId,
                     Long departmentId, Long doctorId, String messageTraceId) {
        MessageEnvelope<CancelRegistrationRequest> messageEnvelope = new MessageEnvelope<>();
        assembleEnvelope(messageEnvelope, hospitalId, messageTraceId);
        messageEnvelope.setParameterMap(cancelRegistrationRequest);
        messageEnvelope.setHospitalAreaId(hospitalAreaId);
        messageEnvelope.setDepartmentId(departmentId);
        messageEnvelope.setDoctorId(doctorId);
        messageEnvelope.setHospitalCode(cancelRegistrationRequest.getHospitalCode());

        String message = MessageUtil.object2JSONString(messageEnvelope);
        send(message, HisOperation.CANCEL_REGISTERED);
    }

    public void send(QueryPatientArchivesRequest queryPatientArchivesRequest, Long hospitalId, Long hospitalAreaId) {
        send(queryPatientArchivesRequest, hospitalId, hospitalAreaId, null);
    }

    public void send(QueryPatientArchivesRequest queryPatientArchivesRequest, Long hospitalId, Long hospitalAreaId, String messageTraceId) {
        MessageEnvelope<QueryPatientArchivesRequest> messageEnvelope = new MessageEnvelope<>();
        assembleEnvelope(messageEnvelope, hospitalId, messageTraceId);
        messageEnvelope.setParameterMap(queryPatientArchivesRequest);
        messageEnvelope.setHospitalAreaId(hospitalAreaId);
        messageEnvelope.setHospitalCode(queryPatientArchivesRequest.getHospitalCode());

        String message = MessageUtil.object2JSONString(messageEnvelope);
        send(message, HisOperation.QUERY_PATIENT_ARCHIVES);
    }

    public void send(CreateArchivesRequest createArchivesRequest, Long hospitalId, Long hospitalAreaId) {
        MessageEnvelope<CreateArchivesRequest> messageEnvelope = new MessageEnvelope<>();
        assembleEnvelope(messageEnvelope, hospitalId, null);
        messageEnvelope.setParameterMap(createArchivesRequest);
        messageEnvelope.setHospitalAreaId(hospitalAreaId);

        String message = MessageUtil.object2JSONString(messageEnvelope);
        send(message, HisOperation.CREATE_ARCHIVES);
    }

    public void send(UpdatePatientArchivesRequest updatePatientArchivesRequest, Long hospitalId, Long hospitalAreaId) {
        MessageEnvelope<UpdatePatientArchivesRequest> messageEnvelope = new MessageEnvelope<>();
        assembleEnvelope(messageEnvelope, hospitalId, null);
        messageEnvelope.setParameterMap(updatePatientArchivesRequest);
        messageEnvelope.setHospitalAreaId(hospitalAreaId);

        String message = MessageUtil.object2JSONString(messageEnvelope);
        send(message, HisOperation.UPDATE_PATIENT_ARCHIVES);
    }

    public void send(RegistrationResultCheckRequest registrationResultCheckRequest, Long hospitalId, Long hospitalAreaId) {
        MessageEnvelope<RegistrationResultCheckRequest> messageEnvelope = new MessageEnvelope<>();
        assembleEnvelope(messageEnvelope, hospitalId, null);
        messageEnvelope.setParameterMap(registrationResultCheckRequest);
        messageEnvelope.setHospitalAreaId(hospitalAreaId);
        messageEnvelope.setHospitalCode(registrationResultCheckRequest.getHospitalCode());

        String message = MessageUtil.object2JSONString(messageEnvelope);
        send(message, HisOperation.REGISTRATION_RESULT_CHECK);
    }

    public void send(DoctorDataSyncRequest doctorDataSyncRequest, Long hospitalId) {
        MessageEnvelope<DoctorDataSyncRequest> messageEnvelope = new MessageEnvelope<>();
        assembleEnvelope(messageEnvelope, hospitalId, null);
        messageEnvelope.setParameterMap(doctorDataSyncRequest);

        String message = MessageUtil.object2JSONString(messageEnvelope);
        send(message, HisOperation.DOCTOR_DATA_SYNC);
    }

    public void send(NotifyHisRequest notifyHisRequest, Long hospitalId, Long hospitalAreaId,
                     Long departmentId, Long doctorId) {
        send(notifyHisRequest, hospitalId, hospitalAreaId, departmentId, doctorId, null);
    }

    public void send(NotifyHisRequest notifyHisRequest, Long hospitalId, Long hospitalAreaId,
                     Long departmentId, Long doctorId, String messageTraceId) {
        MessageEnvelope<NotifyHisRequest> messageEnvelope = new MessageEnvelope<>();
        assembleEnvelope(messageEnvelope, hospitalId, messageTraceId);
        messageEnvelope.setParameterMap(notifyHisRequest);
        messageEnvelope.setHospitalAreaId(hospitalAreaId);
        messageEnvelope.setDepartmentId(departmentId);
        messageEnvelope.setDoctorId(doctorId);
        messageEnvelope.setHospitalCode(notifyHisRequest.getHospitalCode());

        String message = MessageUtil.object2JSONString(messageEnvelope);
        send(message, HisOperation.NOTIFY_HIS);
    }

    //发送医生停诊查询请求
    public void send(DoctorOutServiceRequest doctorOutServiceRequest, Long hospitalId, Long hospitalAreaId, Long departmentId,
                     String messageTraceId) {
        MessageEnvelope<DoctorOutServiceRequest> messageEnvelope = new MessageEnvelope<>();
        assembleEnvelope(messageEnvelope, hospitalId, messageTraceId);
        messageEnvelope.setParameterMap(doctorOutServiceRequest);
        messageEnvelope.setHospitalAreaId(hospitalAreaId);
        messageEnvelope.setDepartmentId(departmentId);
        messageEnvelope.setHospitalCode(doctorOutServiceRequest.getHospitalCode());

        String message = MessageUtil.object2JSONString(messageEnvelope);
        send(message, HisOperation.DOCTOR_OUT_SERVICE);
    }

    //发送医院停诊查询请求
    public void send(HospitalOutServiceRequest hospitalOutServiceRequest, Long hospitalId, Long hospitalAreaId, String messageTraceId) {
        MessageEnvelope<HospitalOutServiceRequest> messageEnvelope = new MessageEnvelope<>();
        assembleEnvelope(messageEnvelope, hospitalId, messageTraceId);
        messageEnvelope.setParameterMap(hospitalOutServiceRequest);
        messageEnvelope.setHospitalAreaId(hospitalAreaId);
        messageEnvelope.setHospitalCode(hospitalOutServiceRequest.getHospitalCode());

        String message = MessageUtil.object2JSONString(messageEnvelope);
        send(message, HisOperation.HOSPITAL_OUT_SERVICE);
    }

    public void send(String message, HisOperation hisOperation) {
        if (ObjectUtils.isEmpty(message) || HisOperation.UNKNOWN.equals(hisOperation)) {
            return;
        }
        log.info("send message {}", message);
        kafkaTemplate.send(hisOperation.getRequestTopicName(), message);
    }

    private void assembleEnvelope(MessageEnvelope<?> messageEnvelope, Long hospitalId, String messageTraceId) {
        if (messageTraceId == null || messageTraceId.equals("")) {
            messageEnvelope.setMessageTraceId(StringUtils.getUUID32());
        } else {
            messageEnvelope.setMessageTraceId(messageTraceId);
        }
        messageEnvelope.setTimestamp(System.currentTimeMillis());
        messageEnvelope.setHospitalId(hospitalId);
    }
}
