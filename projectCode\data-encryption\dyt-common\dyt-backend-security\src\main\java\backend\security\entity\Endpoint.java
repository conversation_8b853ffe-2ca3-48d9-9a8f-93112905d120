package backend.security.entity;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.AntPathMatcher;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-18 18:20
 */
@Data
@Accessors(chain = true)
public class Endpoint {
    private static final AntPathMatcher PATH_MATCHER = new AntPathMatcher();
    private Long id;
    private String path;
    private String method;

    public boolean isMatch(String path, String method) {
        return this.method.equals(method) && PATH_MATCHER.match(this.path, path);
    }
}
