package com.ynhdkc.tenant.service.backend.impl;

import backend.common.domain.tenant.constant.DictFileClusterStatus;
import backend.common.domain.tenant.constant.DictFileStatus;
import backend.common.domain.tenant.constant.DictFileType;
import backend.common.response.BaseOperationResponse;
import backend.common.util.RedisKeyBuilder;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.dao.SearchManagementQuery;
import com.ynhdkc.tenant.dao.SearchManagementRepository;
import com.ynhdkc.tenant.entity.DictFile;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/11/8 17:17
 */
@Service
public class LocalSearchManagementServiceImpl extends BaseSearchManagementServiceImpl {
    public LocalSearchManagementServiceImpl(SearchManagementQuery searchManagementQuery,
                                            SearchManagementRepository searchManagementRepository,
                                            PageVoConvert pageVoConvert,
                                            RedisTemplate<String, Object> redisTemplate) {
        super(searchManagementQuery, searchManagementRepository, pageVoConvert);
        this.redisTemplate = redisTemplate;
    }

    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    public BaseOperationResponse syncDict() {
        try (Page<DictFile> page = searchManagementQuery.pageQueryDictFile(new SearchManagementQuery.DictFileQueryOption(1, Integer.MAX_VALUE)
                .setStatus(DictFileStatus.ENABLE.getCode())
        )) {
            Map<DictFileType, List<DictFile>> groupedDictFiles = page.stream()
                    .filter(dict -> null != dict.getType())
                    .collect(Collectors.groupingBy(dict -> DictFileType.of(dict.getType())));

            groupedDictFiles.forEach((type, dictFiles) -> {
                String key = new RedisKeyBuilder("dyt-tenant", "search-management")
                        .nextNode("dict-type")
                        .nextNode(type.getCode())
                        .build();
                String md5Key = new RedisKeyBuilder("dyt-tenant", "search-management")
                        .nextNode("dict-type")
                        .nextNode(type.getCode())
                        .nextNode("md5")
                        .build();

                String words = mergerDictFile(dictFiles);
                String digest = DigestUtils.md5DigestAsHex(words.getBytes());

                redisTemplate.opsForValue().set(key, words);
                redisTemplate.opsForValue().set(md5Key, digest);
            });

            /* 更新同步状态 */
            Date date = new Date();
            page.forEach(dict -> {
                dict.setClusterStatus(DictFileClusterStatus.SYNC.getCode());
                dict.setSyncTime(date);
                searchManagementRepository.updateDictFile(dict);
            });

            return new BaseOperationResponse("同步成功");
        } catch (Exception e) {
            return new BaseOperationResponse("同步失败");
        }
    }


    private static String mergerDictFile(List<DictFile> dictFiles) {
        if (dictFiles.isEmpty()) {
            return "";
        }
        Set<String> keywords = dictFiles.stream()
                .flatMap(dict -> Arrays.stream(dict.getWords().split(",")))
                .collect(Collectors.toSet());

        StringBuilder builder = new StringBuilder();
        for (String keyword : keywords) {
            builder.append(keyword).append("\n");
        }
        return builder.toString();
    }
}
