package com.ynhdkc.tenant.dao.mapper;

import backend.common.util.MybatisUtil;
import com.ynhdkc.tenant.entity.DictType;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/2/9 14:11:10
 */
@Mapper
public interface DictTypeMapper extends BaseMapper<DictType> {

    default long countByType(String type) {
        return selectCountByExample2(DictType.class, sql -> sql.andEqualTo(DictType::getType, type));
    }

    default Long create(DictType dictType) {
        insertSelective(dictType);
        return dictType.getId();
    }

    default void selectByConditions(String type, String description) {
        selectByExample(DictType.class, helper -> {
                    helper.defGroup(condition -> {
                        if (StringUtils.hasText(type)) {
                            condition.andLike(DictType::getType, MybatisUtil.likeBoth(type));
                        }
                        if (StringUtils.hasText(description)) {
                            condition.andLike(DictType::getDescription, MybatisUtil.likeBoth(description));
                        }
                    });
                    helper.builder(q -> q.orderByDesc(DictType::getCreateTime));
                }
        );
    }

    default long countByTypeAndIdNot(String type, Long id) {
        return selectCountByExample2(DictType.class, sql -> {
            sql.andEqualTo(DictType::getType, type);
            sql.andNotEqualTo(DictType::getId, id);
        });
    }
}
