package backend.common.entity.dto.patient;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class BlacklistingPatientRequest {

    @JsonProperty("patient_id")
    private Long patientId;

    @JsonProperty("user_id")
    private Long userId;

    private String reason;

    @JsonProperty("message_trace_id")
    private String messageTraceId;

    private Long timestamp;

}
