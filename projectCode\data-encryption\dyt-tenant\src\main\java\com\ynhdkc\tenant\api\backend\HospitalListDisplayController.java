package com.ynhdkc.tenant.api.backend;

import backend.common.response.BaseOperationResponse;
import backend.security.oplog.DytSecurityRequired;
import com.ynhdkc.tenant.handler.HospitalListDisplayApi;
import com.ynhdkc.tenant.model.CreateHospitalListDisplayReqDto;
import com.ynhdkc.tenant.model.HospitalListDisplayPageVo;
import com.ynhdkc.tenant.model.HospitalListDisplayVo;
import com.ynhdkc.tenant.model.UpdateHospitalListDisplayReqDto;
import com.ynhdkc.tenant.service.backend.IHospitalListDisplayService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Api(tags = "HospitalListDisplay")
public class HospitalListDisplayController implements HospitalListDisplayApi {

    private final IHospitalListDisplayService hospitalListDisplayService;

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:list:display:create")
    public ResponseEntity<HospitalListDisplayVo> createHospitalListDisplay(CreateHospitalListDisplayReqDto hospitalListDisplay) {
        return ResponseEntity.ok(hospitalListDisplayService.createHospitalListDisplay(hospitalListDisplay));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:list:display:delete")
    public ResponseEntity<BaseOperationResponse> deleteHospitalListDisplay(Long id) {
        return ResponseEntity.ok(hospitalListDisplayService.deleteHospitalListDisplay(id));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:list:display:get")
    public ResponseEntity<HospitalListDisplayVo> getHospitalListDisplayDetail(Long id) {
        return ResponseEntity.ok(hospitalListDisplayService.getHospitalListDisplayDetail(id));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:list:display:query:page")
    public ResponseEntity<HospitalListDisplayPageVo> getHospitalListDisplayPage(Integer currentPage, Integer pageSize) {
        return ResponseEntity.ok(hospitalListDisplayService.getHospitalListDisplayPage(currentPage, pageSize));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:list:display:update")
    public ResponseEntity<HospitalListDisplayVo> updateHospitalListDisplay(Long id, UpdateHospitalListDisplayReqDto hospitalListDisplayDto) {
        return ResponseEntity.ok(hospitalListDisplayService.updateHospitalListDisplay(id, hospitalListDisplayDto));
    }
}
