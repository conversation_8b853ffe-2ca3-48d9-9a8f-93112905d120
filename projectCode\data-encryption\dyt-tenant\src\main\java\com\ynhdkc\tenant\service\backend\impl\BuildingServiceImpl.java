package com.ynhdkc.tenant.service.backend.impl;

import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import backend.security.service.BackendTenantUserService;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.dao.BuildingQuery;
import com.ynhdkc.tenant.dao.BuildingRepository;
import com.ynhdkc.tenant.entity.Building;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.AddressService;
import com.ynhdkc.tenant.service.backend.BuildingService;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-04 10:25
 */
@Service
@RequiredArgsConstructor
public class BuildingServiceImpl implements BuildingService {
    private final BuildingQuery buildingQuery;
    private final BuildingRepository buildingRepository;

    private final AddressService addressService;

    private final BackendTenantUserService backendTenantUserService;

    private final PageVoConvert pageVoConvert;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BuildingVo create(BuildingCreateReqDto request) {
        Building building = BuildingService.toBuilding(request);
        if (null != request.getAddress()) {
            building.setAddressId(addressService.create(request.getAddress()));
        }

        buildingRepository.create(building);

        BuildingVo vo = BuildingService.toBuildingVo(building);
        importAddress(vo);
        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BuildingVo update(Long buildingId, BuildingUpdateReqDto request) {
        Building building = buildingQuery.queryBuildingById(buildingId);
        if (building == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "大楼不存在");
        }
        updateIfNotNull(building, request);
        buildingRepository.update(building);
        if (request.getAddress() != null) {
            addressService.update(request.getAddress().getId(), request.getAddress());
        }
        BuildingVo vo = BuildingService.toBuildingVo(building);
        importAddress(vo);
        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseOperationResponse delete(Long buildingId) {
        Building building = buildingQuery.queryBuildingById(buildingId);
        if (building == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "大楼不存在");
        }
        buildingRepository.delete(buildingId);
        return new BaseOperationResponse("删除成功");
    }

    @Override
    public BuildingVo getDetail(Long buildingId) {
        Building building = buildingQuery.queryBuildingById(buildingId);
        if (building == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "大楼不存在");
        }
        BuildingVo vo = BuildingService.toBuildingVo(building);
        importAddress(vo);
        return vo;
    }

    @Override
    public BuildingPageVo query(BuildingQueryReqDto request) {
        boolean isSuperAdmin = backendTenantUserService.isSuperAdmin();
        if (null == request.getTenantId() && !isSuperAdmin) {
            throw new BizException(HttpStatus.FORBIDDEN, "必须指定租户ID");
        }
        BuildingQuery.BuildingQueryOption option = new BuildingQuery.BuildingQueryOption(request.getCurrentPage(), request.getPageSize())
                .setTenantId(request.getTenantId())
                .setHospitalId(request.getHospitalId())
                .setHospitalAreaId(request.getHospitalAreaId())
                .setName(request.getName())
                .setStatus(request.getStatus());
        if (!isSuperAdmin) {
            option.setIncludeTenantIds(backendTenantUserService.getCurrentUserTenantReadRange(true));
            option.setIncludeHospitalIds(backendTenantUserService.getCurrentUserHospitalReadRange(true));
            option.setIncludeHospitalAreaIds(backendTenantUserService.getCurrentUserHospitalAreaReadRange(true));
        }
        try (Page<Building> buildingPage = buildingQuery.pageQuery(option)) {
            return pageVoConvert.toPageVo(buildingPage, BuildingPageVo.class, BuildingService::toBuildingVo);
        }
    }

    private void importAddress(BuildingVo vo) {
        if (null != vo.getAddressId()) {
            vo.setAddress(addressService.getAddressVoById(vo.getAddressId()));
        }
    }

    private static void updateIfNotNull(Building entity, BuildingUpdateReqDto dto) {
        if (null != dto.getTenantId()) {
            entity.setTenantId(dto.getTenantId());
        }
        if (null != dto.getHospitalId()) {
            entity.setHospitalId(dto.getHospitalId());
        }
        if (null != dto.getHospitalAreaId()) {
            entity.setHospitalAreaId(dto.getHospitalAreaId());
        }
        if (null != dto.getName()) {
            entity.setName(dto.getName());
        }
        if (null != dto.getStatus()) {
            entity.setStatus(dto.getStatus());
        }
        if (null != dto.getSort()) {
            entity.setSort(dto.getSort());
        }
    }
}
