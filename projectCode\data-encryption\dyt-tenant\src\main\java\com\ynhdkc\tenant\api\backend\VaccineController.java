package com.ynhdkc.tenant.api.backend;

import backend.common.response.BaseOperationResponse;
import backend.security.oplog.DytSecurityRequired;
import com.ynhdkc.tenant.handler.VaccineApi;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.VaccineService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "Vaccine")
@RestController
@RequiredArgsConstructor
public class VaccineController implements VaccineApi {
    private final VaccineService vaccineService;

    @Override
    @DytSecurityRequired(needOpLog = true, value = "vaccine:create")
    public ResponseEntity<VaccineVo> createVaccine(CreateVaccineReqDto request) {
        return ResponseEntity.ok(vaccineService.createVaccine(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "vaccine:create:category")
    public ResponseEntity<VaccineCategoryVo> createVaccineCategory(CreateVaccineCategoryReqDto request) {
        return ResponseEntity.ok(vaccineService.createVaccineCategory(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "vaccine:delete")
    public ResponseEntity<BaseOperationResponse> deleteVaccine(Long id) {
        return ResponseEntity.ok(vaccineService.deleteVaccine(id));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "vaccine:delete:category")
    public ResponseEntity<BaseOperationResponse> deleteVaccineCategory(Long id) {
        return ResponseEntity.ok(vaccineService.deleteVaccineCategory(id));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "vaccine:get")
    public ResponseEntity<VaccineVo> getVaccine(Long id) {
        return ResponseEntity.ok(vaccineService.getVaccine(id));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "vaccine:get:category")
    public ResponseEntity<VaccineCategoryVo> getVaccineCategory(Long id) {
        return ResponseEntity.ok(vaccineService.getVaccineCategory(id));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "vaccine:query:category:page")
    public ResponseEntity<VaccineCategoryPageVo> getVaccineCategoryPage(GetVaccineCategoryPageReqDto request) {
        return ResponseEntity.ok(vaccineService.getVaccineCategoryPage(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "vaccine:query:page")
    public ResponseEntity<VaccinePageVo> getVaccinePage(GetVaccinePageReqDto request) {
        return ResponseEntity.ok(vaccineService.getVaccinePage(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "vaccine:update")
    public ResponseEntity<VaccineVo> updateVaccine(Long id, UpdateVaccineReqDto request) {
        return ResponseEntity.ok(vaccineService.updateVaccine(id, request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "vaccine:update:category")
    public ResponseEntity<VaccineCategoryVo> updateVaccineCategory(Long id, UpdateVaccineCategoryReqDto request) {
        return ResponseEntity.ok(vaccineService.updateVaccineCategory(id, request));
    }
}
