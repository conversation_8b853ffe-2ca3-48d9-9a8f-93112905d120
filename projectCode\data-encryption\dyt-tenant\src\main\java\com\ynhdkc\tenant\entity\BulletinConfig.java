package com.ynhdkc.tenant.entity;

import backend.common.domain.BaseEntity;
import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;

/**
 * 系统公告配置
 *
 * @TableName t_bulletin_config
 */
@Table(name = "t_bulletin_config")
@Data
public class BulletinConfig extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 系统状态：0，正常；1，维护，默认正常
     */
    private Integer systemStatus;
    /**
     * 公告内容
     */
    private String bulletinContent;
    /**
     * 启用状态：0，启用；1，禁用，默认禁用
     */
    private Integer status;
    /**
     * 用户白名单列表，多个用逗号分隔
     */
    private String whiteList;


}