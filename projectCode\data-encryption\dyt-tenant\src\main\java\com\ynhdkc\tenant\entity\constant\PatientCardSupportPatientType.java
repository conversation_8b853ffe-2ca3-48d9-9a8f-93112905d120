package com.ynhdkc.tenant.entity.constant;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-10 10:03
 */
public enum PatientCardSupportPatientType {
    /**
     * 全部
     */
    ALL(0),
    /**
     * 成人
     */
    ADULT(1),
    /**
     * 儿童
     */
    CHILD(2);
    private final int value;

    PatientCardSupportPatientType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static PatientCardSupportPatientType of(Integer input) {
        if (null == input) {
            return null;
        }
        for (PatientCardSupportPatientType value : PatientCardSupportPatientType.values()) {
            if (value.getValue() == input) {
                return value;
            }
        }
        return null;
    }

    public static boolean contains(Integer input) {
        if (null == input) {
            return false;
        }
        for (PatientCardSupportPatientType value : PatientCardSupportPatientType.values()) {
            if (value.getValue() == input) {
                return true;
            }
        }
        return false;
    }
}
