package backend.common.entity.dto.hisgateway.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum SyncLevel {
    LEVEL_ONE(1, "第一级，刷新到医生列表"),
    LEVEL_TWO(2, "第二级，刷新到排班列表"),
    LEVEL_THREE(3, "第三级，刷新到分时段列表"),
    UNKNOWN(-1, "未知");

    private final Integer code;

    private final String desc;

    SyncLevel(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static SyncLevel parse(Integer code) {
        for (SyncLevel syncLevel : SyncLevel.values()) {
            if (syncLevel.code.equals(code)) {
                return syncLevel;
            }
        }
        return UNKNOWN;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
