package com.ynhdkc.tenant.api.customer;

import backend.security.service.BackendClientUserService;
import com.ynhdkc.tenant.handler.CustomerHospitalApi;
import com.ynhdkc.tenant.model.CustomerHospitalAreaPageVo;
import com.ynhdkc.tenant.model.CustomerHospitalPageVo;
import com.ynhdkc.tenant.model.CustomerQueryHospitalPageReqDto;
import com.ynhdkc.tenant.service.backend.HospitalService;
import com.ynhdkc.tenant.service.customer.CustomerHospitalService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "CustomerHospital")
@RestController
@RequiredArgsConstructor
@Slf4j
public class CustomerHospitalController implements CustomerHospitalApi {
    private final HospitalService hospitalService;
    private final CustomerHospitalService customerHospitalService;
    private final BackendClientUserService backendClientUserService;

    @Override
    public ResponseEntity<CustomerHospitalAreaPageVo> queryHospitalAreaListByHospitalId(Long hospitalId) {
        return ResponseEntity.ok(customerHospitalService.queryHospitalAreaListByHospitalId(hospitalId));
    }

    @Override
    public ResponseEntity<CustomerHospitalPageVo> queryHospitalPage(CustomerQueryHospitalPageReqDto queryHospitalPageDto, String authorization) {
        Long userId = null;
        if (!StringUtils.isEmpty(authorization)) {
            try {
                userId = backendClientUserService.getCurrentUserIdFromJwt(authorization.replace("Bearer ", ""));
            } catch (Exception e) {
                log.error("获取当前登录用户失败。", e);
            }
        }
        return ResponseEntity.ok(hospitalService.customerQueryHospitalList(userId, queryHospitalPageDto));
    }
}
