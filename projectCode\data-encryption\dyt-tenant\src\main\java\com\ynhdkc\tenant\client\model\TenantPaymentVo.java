package com.ynhdkc.tenant.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.Date;
import java.util.Objects;

/**
 * TenantPaymentVo
 */
@Validated
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2023-04-10T22:27:16.318+08:00")


public class TenantPaymentVo extends UpdateTenantPaymentDto {
    @JsonProperty("id")
    private Long id = null;

    @JsonProperty("create_time")
    private Date createTime = null;

    @JsonProperty("update_time")
    private Date updateTime = null;

    public TenantPaymentVo id(Long id) {
        this.id = id;
        return this;
    }

    /**
     * 支付配置ID
     *
     * @return id
     **/
    @ApiModelProperty(value = "支付配置ID")


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public TenantPaymentVo createTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    /**
     * 创建时间
     *
     * @return createTime
     **/
    @ApiModelProperty(value = "创建时间")

    @Valid

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public TenantPaymentVo updateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    /**
     * 更新时间
     *
     * @return updateTime
     **/
    @ApiModelProperty(value = "更新时间")

    @Valid

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }


    @Override
    public boolean equals(java.lang.Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TenantPaymentVo tenantPaymentVo = (TenantPaymentVo) o;
        return Objects.equals(this.id, tenantPaymentVo.id) &&
                Objects.equals(this.createTime, tenantPaymentVo.createTime) &&
                Objects.equals(this.updateTime, tenantPaymentVo.updateTime) &&
                super.equals(o);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, createTime, updateTime, super.hashCode());
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class TenantPaymentVo {\n");
        sb.append("    ").append(toIndentedString(super.toString())).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
        sb.append("    updateTime: ").append(toIndentedString(updateTime)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(java.lang.Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}

