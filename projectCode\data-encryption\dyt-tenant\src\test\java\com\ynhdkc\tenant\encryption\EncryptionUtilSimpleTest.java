package com.ynhdkc.tenant.encryption;

import backend.encryption.util.EncryptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 简单的加密工具类测试
 * 不依赖Spring容器，直接测试加密解密功能
 * 
 * <AUTHOR> Backend Team
 * @version 1.1-SNAPSHOT
 * @since 2024-06-24
 */
@Slf4j
class EncryptionUtilSimpleTest {

    @Test
    void testBasicEncryptionDecryption() {
        log.info("=== 测试基本加密解密功能 ===");
        
        String[] testData = {
            "13800138000",
            "<EMAIL>", 
            "张三",
            "Test User Name",
            "复杂的中英文混合数据123!@#"
        };
        
        for (String original : testData) {
            try {
                // 测试加密
                String encrypted = EncryptionUtil.encrypt(original);
                assertNotNull(encrypted, "加密结果不应为空");
                assertNotEquals(original, encrypted, "加密后数据应与原文不同");
                assertTrue(encrypted.length() > original.length(), "加密后数据长度应大于原文");
                
                // 测试解密
                String decrypted = EncryptionUtil.decrypt(encrypted);
                assertEquals(original, decrypted, "解密后数据应与原文相同");
                
                log.info("✓ 原文: {} -> 密文长度: {} -> 解密: {}", original, encrypted.length(), decrypted);
            } catch (Exception e) {
                log.error("加密解密测试失败，原文: {}", original, e);
                fail("加密解密测试失败: " + e.getMessage());
            }
        }
        
        log.info("✓ 基本加密解密功能测试通过");
    }

    @Test
    void testNullAndEmptyValues() {
        log.info("=== 测试空值处理 ===");
        
        // 测试null值
        assertNull(EncryptionUtil.encrypt(null), "null值加密应返回null");
        assertNull(EncryptionUtil.decrypt(null), "null值解密应返回null");
        
        // 测试空字符串
        assertEquals("", EncryptionUtil.encrypt(""), "空字符串加密应返回空字符串");
        assertEquals("", EncryptionUtil.decrypt(""), "空字符串解密应返回空字符串");
        
        log.info("✓ 空值处理测试通过");
    }

    @Test
    void testLargeData() {
        log.info("=== 测试大数据加密 ===");
        
        // 创建较大的测试数据
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            sb.append("这是测试数据").append(i).append("，包含中文和数字。");
        }
        String largeData = sb.toString();
        
        try {
            String encrypted = EncryptionUtil.encrypt(largeData);
            assertNotNull(encrypted, "大数据加密结果不应为空");
            assertNotEquals(largeData, encrypted, "加密后数据应与原文不同");
            
            String decrypted = EncryptionUtil.decrypt(encrypted);
            assertEquals(largeData, decrypted, "大数据解密后应与原文相同");
            
            log.info("✓ 大数据加密测试通过，原文长度: {}, 密文长度: {}", largeData.length(), encrypted.length());
        } catch (Exception e) {
            log.error("大数据加密测试失败", e);
            fail("大数据加密测试失败: " + e.getMessage());
        }
    }

    @Test
    void testSpecialCharacters() {
        log.info("=== 测试特殊字符加密 ===");
        
        String[] specialData = {
            "!@#$%^&*()_+-=[]{}|;':\",./<>?",
            "中文测试数据",
            "English Test Data",
            "混合数据Mixed123!@#",
            "换行符\n制表符\t回车符\r",
            "Unicode字符: 😀🎉🔒🛡️"
        };
        
        for (String data : specialData) {
            try {
                String encrypted = EncryptionUtil.encrypt(data);
                String decrypted = EncryptionUtil.decrypt(encrypted);
                assertEquals(data, decrypted, "特殊字符数据解密后应与原文相同");
                log.debug("✓ 特殊字符测试通过: {}", data.substring(0, Math.min(20, data.length())));
            } catch (Exception e) {
                log.error("特殊字符加密测试失败，数据: {}", data, e);
                fail("特殊字符加密测试失败: " + e.getMessage());
            }
        }
        
        log.info("✓ 特殊字符加密测试通过");
    }

    @Test
    void testPerformance() {
        log.info("=== 测试加密性能 ===");
        
        String testData = "这是性能测试数据，包含中文和English123!@#";
        int iterations = 100;
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < iterations; i++) {
            String encrypted = EncryptionUtil.encrypt(testData);
            String decrypted = EncryptionUtil.decrypt(encrypted);
            assertEquals(testData, decrypted, "性能测试中解密数据应正确");
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        double avgTime = (double) totalTime / iterations;
        
        log.info("✓ 性能测试完成: {}次加密解密操作耗时{}ms, 平均{:.2f}ms/次", 
                iterations, totalTime, avgTime);
        
        // 性能断言（根据实际情况调整）
        assertTrue(avgTime < 50, "平均加密解密时间应小于50ms");
    }

    @Test
    void testConsistency() {
        log.info("=== 测试加密一致性 ===");
        
        String testData = "一致性测试数据";
        
        // 多次加密同一数据，验证每次结果都不同（因为使用了随机IV）
        String encrypted1 = EncryptionUtil.encrypt(testData);
        String encrypted2 = EncryptionUtil.encrypt(testData);
        String encrypted3 = EncryptionUtil.encrypt(testData);
        
        // 加密结果应该不同（因为使用了随机IV）
        assertNotEquals(encrypted1, encrypted2, "相同数据的加密结果应该不同（随机IV）");
        assertNotEquals(encrypted2, encrypted3, "相同数据的加密结果应该不同（随机IV）");
        
        // 但解密结果应该相同
        assertEquals(testData, EncryptionUtil.decrypt(encrypted1), "解密结果应该正确");
        assertEquals(testData, EncryptionUtil.decrypt(encrypted2), "解密结果应该正确");
        assertEquals(testData, EncryptionUtil.decrypt(encrypted3), "解密结果应该正确");
        
        log.info("✓ 加密一致性测试通过");
    }
}
