package com.ynhdkc.tenant.tool;

import com.ynhdkc.tenant.dao.mapper.AppointmentRuleSettingMapper;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

@Component
@RequiredArgsConstructor
public class DoctorListTool {
    public static final String DEFAULT_SOURCE_ACTIVATE_TIME="00:00:00";
    private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final ZoneId zoneId = ZoneId.of("Asia/Shanghai");
    private static final Logger log = LoggerFactory.getLogger(DoctorListTool.class);
    private static final Integer DEFAULT_ADVANCE_DAY = -1;
    private static final Integer DEFAULT_FORBIDDEN_DAY = -1;


    private final AppointmentRuleSettingMapper appointmentRuleSettingMapper;

    /**
     * 医生的优先级最高，其次是科室，最后是院区的设置
     * 如果值为 -1，则表示不生效
     *
     * @return 优先级最高的预约提前天数和禁约天数
     */
    public Pair<Integer, Integer> defaultAdvanceAndForbiddenDays(Doctor doctor, Department department, AppointmentRuleSetting ruleSetting) {
        Pair<Integer, Integer> advanceAndForbiddenPairs = getAdvanceAndForbiddenDays(doctor, department, ruleSetting);
        Integer advanceDay = advanceAndForbiddenPairs.getLeft();
        Integer forbiddenDay = advanceAndForbiddenPairs.getRight();
        String sourceActivateTime = getSourceActivateTime(doctor, department, ruleSetting);
        advanceDay = resetAdvanceDayBySourceActivateTime(doctor, department, sourceActivateTime, advanceDay);
        //log.info("最终defaultAdvanceAndForbiddenDays，院区编码：{},department:{},doctor:{},sourceActivateTime:{},advanceDay:{},forbiddenDay:{}", department.getHospitalCode(),department.getName(), (Objects.nonNull(doctor) ? doctor.getName() : "无"), sourceActivateTime, advanceDay, forbiddenDay);
        return Pair.of(advanceDay, forbiddenDay);
    }

    private static Integer resetAdvanceDayBySourceActivateTime(Doctor doctor, Department department, String sourceActivateTime, Integer advanceDay) {
        if(!StringUtils.isEmpty(sourceActivateTime) && !DEFAULT_SOURCE_ACTIVATE_TIME.equals(sourceActivateTime)){
            String[] openTimeArray =  sourceActivateTime.split(":");
            ZonedDateTime nowInShanghai = ZonedDateTime.now(zoneId);
            ZonedDateTime openTimeInShanghai = ZonedDateTime.of(nowInShanghai.getYear(), nowInShanghai.getMonthValue(), nowInShanghai.getDayOfMonth(), Integer.parseInt(openTimeArray[0]), Integer.parseInt(openTimeArray[1]), Integer.parseInt(openTimeArray[2]), 0, zoneId);
            //如果还没到放号时间，则可提前预约天数-1
            if(nowInShanghai.isBefore(openTimeInShanghai)){
                //log.info("需要根据放号时间重置AdvanceDay，院区编码：{}，科室：{}，医生：{}，当前时间：{}，放号时间：{}，可提前预约天数：{}", department.getHospitalCode(), department.getName(), (Objects.nonNull(doctor) ? doctor.getName() : "无"), nowInShanghai.format(dateTimeFormatter), openTimeInShanghai.format(dateTimeFormatter), advanceDay);
                advanceDay -= 1;
            }
        }
        return advanceDay;
    }

    private static Pair<Integer, Integer> getAdvanceAndForbiddenDays(Doctor doctor, Department department, AppointmentRuleSetting ruleSetting) {
        Integer advanceDay = DEFAULT_ADVANCE_DAY;
        Integer forbiddenDay = DEFAULT_FORBIDDEN_DAY;
        Integer doctorAdvanceDay = DEFAULT_ADVANCE_DAY;
        Integer doctorForbiddenDay = DEFAULT_FORBIDDEN_DAY;
        Integer departmentAdvanceDay = DEFAULT_ADVANCE_DAY;
        Integer departmentForbiddenDay = DEFAULT_FORBIDDEN_DAY;
        Integer ruleSettingAdvanceDay = DEFAULT_ADVANCE_DAY;
        Integer ruleSettingForbiddenDay = DEFAULT_FORBIDDEN_DAY;

        if (Objects.nonNull(doctor)) {
            doctorAdvanceDay = doctor.getAdvanceDay();
            doctorForbiddenDay = doctor.getForbiddenDay();
        }
        if (Objects.nonNull(department)) {
            departmentAdvanceDay = department.getAdvanceDay();
            departmentForbiddenDay = department.getForbiddenDay();
        }
        if (Objects.nonNull(ruleSetting)) {
            ruleSettingAdvanceDay = ruleSetting.getAdvanceDay();
            ruleSettingForbiddenDay = ruleSetting.getForbiddenDay();
        }

        // 优先级：医生 > 科室 > 院区
        if (DEFAULT_ADVANCE_DAY.equals(doctorAdvanceDay)) {
            if (DEFAULT_ADVANCE_DAY.equals(departmentAdvanceDay)) {
                if (!DEFAULT_ADVANCE_DAY.equals(ruleSettingAdvanceDay)) {
                    advanceDay = ruleSettingAdvanceDay;
                }
            }else{
                advanceDay = departmentAdvanceDay;
            }
        } else {
            advanceDay = doctorAdvanceDay;
        }
        if (DEFAULT_FORBIDDEN_DAY.equals(doctorForbiddenDay)) {
            if (DEFAULT_FORBIDDEN_DAY.equals(departmentForbiddenDay)) {
                if (!DEFAULT_FORBIDDEN_DAY.equals(ruleSettingForbiddenDay)) {
                    forbiddenDay = ruleSettingForbiddenDay;
                }
            }else{
                forbiddenDay = departmentForbiddenDay;
            }
        } else {
            forbiddenDay = doctorForbiddenDay;
        }
        return Pair.of(advanceDay, forbiddenDay);
    }

    private static String getSourceActivateTime(Doctor doctor, Department department, AppointmentRuleSetting ruleSetting) {
        String activateTime = DEFAULT_SOURCE_ACTIVATE_TIME;
        String doctorSourceActivateTime = DEFAULT_SOURCE_ACTIVATE_TIME;
        String departmentSourceActivateTime = DEFAULT_SOURCE_ACTIVATE_TIME;
        String ruleSettingSourceActivateTime = DEFAULT_SOURCE_ACTIVATE_TIME;

        if(Objects.nonNull(doctor)){
            doctorSourceActivateTime = doctor.getSourceActivateTime();
        }
        if(Objects.nonNull(department)){
            departmentSourceActivateTime = department.getSourceActivateTime();
        }
        if(Objects.nonNull(ruleSetting)){
            ruleSettingSourceActivateTime = ruleSetting.getSourceActivateTime();
        }

        //优先级: 医生 > 科室 > 院区
        if(DEFAULT_SOURCE_ACTIVATE_TIME.equals(doctorSourceActivateTime)){
            if(DEFAULT_SOURCE_ACTIVATE_TIME.equals(departmentSourceActivateTime)){
                if(!DEFAULT_SOURCE_ACTIVATE_TIME.equals(ruleSettingSourceActivateTime)){
                    activateTime = ruleSettingSourceActivateTime;
                }
            }else{
                activateTime = departmentSourceActivateTime;
            }
        }else{
            activateTime = doctorSourceActivateTime;
        }
        return activateTime;
    }

    public Pair<Integer, Integer> defaultAdvanceAndForbiddenDays(Long hospitalAreaId) {
        List<AppointmentRuleSetting> appointmentRuleSettings = appointmentRuleSettingMapper.selectByExample2(AppointmentRuleSetting.class, consumer -> consumer.andEqualTo(AppointmentRuleSetting::getHospitalAreaId, hospitalAreaId));
        if (appointmentRuleSettings.isEmpty()) {
            return Pair.of(DEFAULT_ADVANCE_DAY, DEFAULT_FORBIDDEN_DAY);
        }
        return Pair.of(appointmentRuleSettings.get(0).getAdvanceDay(), appointmentRuleSettings.get(0).getForbiddenDay());
    }
}
