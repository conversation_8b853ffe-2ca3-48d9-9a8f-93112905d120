package com.ynhdkc.tenant.service.customer;

import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.model.*;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/7/21 10:25
 */
public interface CustomerDoctorService {
    CustomerDoctorDetailVo getDetail(Long doctorId);

    CustomerDoctorPageVo query(CustomerDoctorQueryReqDto request);

    CustomerDoctorDetailVo getDetailByCode(String hospitalAreaCode, String departmentCode, String doctorCode);

    CustomerDoctorAskServiceVo getAskServiceList(Long doctorId);

    CustomerDoctorScheduledDepartmentVo queryDoctorScheduledDepartment(Long baseDoctorId);

    Optional<Doctor> queryDoctorByCode(String hospitalAreaCode, String departmentCode, String doctorCode);
}
