package backend.common.domain;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import java.io.Serializable;
import java.util.Date;

@Data
public abstract class BaseEntity implements Serializable {
    @Id
    @KeySql(useGeneratedKeys = true)
    private Long id;
    private Date createTime;
    private Date updateTime;

    public BaseEntity() {
        final Date now = new Date();
        this.createTime = now;
        this.updateTime = now;
    }
}
