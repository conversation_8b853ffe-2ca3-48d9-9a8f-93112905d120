create table t_recommend_config
(
    `id`           bigint auto_increment,
    `biz_type`     varchar(50)                          not null comment '业务类型，例如：专病专诊、特色中医，来自字典配置',
    `data_type`    varchar(40)                          not null comment '数据类型，例如：医院、科室、医生，来自字典配置',
    `data_tag`     varchar(60)                          null comment '数据标签，当在相同的 biz_type、data_type 下，通过 data_tag，可以同一 data_type 设置多次',
    `data_id`      varchar(30)                          not null comment '数据全局唯一标识',
    `redirect_url` varchar(255)                         null comment '跳转链接',
    `display_tags` varchar(255)                         null comment '展示标签，多个标签用逗号分隔',
    `sort`         int(10)    default 0                 not null comment '排序，由大到小',
    `enabled`      tinyint(1) default 1                 not null comment '已启用',
    `create_time`  datetime   default CURRENT_TIMESTAMP not null,
    `update_time`  datetime                             null on update CURRENT_TIMESTAMP,
    constraint t_recommend_config_pk
        primary key (`id`)
)
    comment '推荐配置';

create unique index t_biz_type_tag_index
    on t_recommend_config (`biz_type`, `data_type`, `data_tag`, `data_id`);