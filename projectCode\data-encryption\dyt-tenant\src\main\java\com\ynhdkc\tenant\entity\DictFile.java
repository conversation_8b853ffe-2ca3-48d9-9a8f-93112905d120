package com.ynhdkc.tenant.entity;

import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/9/12 16:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "t_dict_file")
public class DictFile extends BaseEntity {
    /**
     * 字典名称
     */
    private String name;
    /**
     * 字典类型:0,关键词;1,屏蔽词;
     */
    private Integer type;
    /**
     * 关键字集合
     */
    private String words;
    /**
     * 字典状态:0,启用;1,禁用;2,删除;
     */
    private Integer status;
    /**
     * 集群状态:0,未同步;1,已同步;
     */
    private Integer clusterStatus;
    /**
     * 备注
     */
    private String remark;
    /**
     * 同步时间
     */
    private Date syncTime;
}
