package backend.common.dao;

import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.Fn;
import tk.mybatis.mapper.weekend.reflection.Reflections;

public class WeekendExampleBuilder<T> extends Example.Builder {
    public WeekendExampleBuilder(Class<T> entityClass) {
        super(entityClass);
    }

    @SafeVarargs
    public final WeekendExampleBuilder<T> notSelect(Fn<T, Object>... fns) {
        String[] properties = fns2properties(fns);
        this.notSelect(properties);
        return this;
    }

    private String[] fns2properties(Fn<T, Object>[] fns) {
        String[] properties = new String[fns.length];
        for (int i = 0; i < fns.length; i++) {
            properties[i] = Reflections.fnToFieldName(fns[i]);
        }
        return properties;
    }

    @SafeVarargs
    public final WeekendExampleBuilder<T> orderByAsc(Fn<T, Object>... fns) {
        this.orderByAsc(fns2properties(fns));
        return this;
    }

    @SafeVarargs
    public final WeekendExampleBuilder<T> orderByDesc(Fn<T, Object>... fns) {
        this.orderByDesc(fns2properties(fns));
        return this;
    }
}
