<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ynhdkc.tenant.dao.mapper.HospitalMapper">

    <resultMap id="HospitalResultMap" type="com.ynhdkc.tenant.entity.Hospital">
        <id column="id" property="id" />
        <result column="parent_id" property="parentId" />
        <result column="tenant_id" property="tenantId" />
        <result column="hospital_type_tag" property="hospitalTypeTag" />
        <result column="hospital_code" property="hospitalCode" />
        <result column="name" property="name" />
        <result column="level_dict_type" property="levelDictType" />
        <result column="level_dict_value" property="levelDictValue" />
        <result column="level_dict_label" property="levelDictLabel" />
        <result column="category" property="category" />
        <result column="property" property="property" />
        <result column="introduction" property="introduction" />
        <result column="announcement" property="announcement" />
        <result column="environment" property="environment" />
        <result column="address_id" property="addressId" />
        <result column="appointment_caution_dict_type" property="appointmentCautionDictType" />
        <result column="appointment_caution_dict_label" property="appointmentCautionDictLabel" />
        <result column="contact_person" property="contactPerson" />
        <result column="contact_phone_number" property="contactPhoneNumber" />
        <result column="status" property="status" />
        <result column="tag_dict_type" property="tagDictType" />
        <result column="tag_dict_label" property="tagDictLabel" />
        <result column="tag_dict_value" property="tagDictValue" />
        <result column="display" property="display" />
        <result column="display_guide" property="displayGuide" />
        <result column="display_floor" property="displayFloor" />
        <result column="display_sort" property="displaySort" />
        <result column="map_keyword" property="mapKeyword" />
        <result column="stop_service_begin_time" property="stopServiceBeginTime" />
        <result column="stop_service_end_time" property="stopServiceEndTime" />
        <result column="department_layer" property="departmentLayer" />
        <result column="logo" property="logo" />
        <result column="pictures" property="pictures" />
        <result column="appointment_scheduling_time" property="appointmentSchedulingTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="selectHospitalWithCategory" resultMap="HospitalResultMap">
        SELECT *
        FROM t_hospital
        <where>
            <if test="option.hospitalTypeTag != null">
                AND hospital_type_tag = #{option.hospitalTypeTag}
            </if>
            <if test="option.id != null">
                AND id = #{option.id}
            </if>
            <if test="option.tenantId != null">
                AND tenant_id = #{option.tenantId}
            </if>
            <if test="option.hospitalCode != null and option.hospitalCode != ''">
                AND hospital_code LIKE CONCAT('%', #{option.hospitalCode}, '%')
            </if>
            <if test="option.name != null and option.name != ''">
                AND name LIKE CONCAT('%', #{option.name}, '%')
            </if>
            <if test="option.levelDictValue != null and option.levelDictValue != ''">
                AND level_dict_value LIKE CONCAT('%', #{option.levelDictValue}, '%')
            </if>
            <if test="option.status != null">
                AND status = #{option.status}
            </if>
            <if test="option.startCreateTime != null and option.endCreateTime != null">
                AND create_time BETWEEN #{option.startCreateTime} AND #{option.endCreateTime}
            </if>
            <if test="option.includeIds != null and option.includeIds.size > 0">
                AND id IN
                <foreach collection="option.includeIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="option.excludeIds != null and option.excludeIds.size > 0">
                AND id NOT IN
                <foreach collection="option.excludeIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="option.includeTenantIds != null and option.includeTenantIds.size > 0">
                AND tenant_id IN
                <foreach collection="option.includeTenantIds" item="tid" open="(" separator="," close=")">
                    #{tid}
                </foreach>
            </if>
            <if test="option.excludeTenantIds != null and option.excludeTenantIds.size > 0">
                AND tenant_id NOT IN
                <foreach collection="option.excludeTenantIds" item="tid" open="(" separator="," close=")">
                    #{tid}
                </foreach>
            </if>
            <if test="option.category != null and option.category.size > 0">
                AND (
                <foreach collection="option.category" item="cat" separator=" OR ">
                    FIND_IN_SET(#{cat}, category) > 0
                </foreach>
                )
            </if>
            <if test="option.excludeCategory != null and option.excludeCategory.size > 0">
                AND (
                <foreach collection="option.excludeCategory" item="cat" separator=" AND ">
                    FIND_IN_SET(#{cat}, category) = 0
                </foreach>
                )
            </if>
        </where>
        ORDER BY display_sort DESC, id ASC
    </select>

    <select id="selectHospitalAreaWithCustomQuery"  resultMap="HospitalResultMap">
        SELECT *
        FROM t_hospital
        <where>
            <if test="option.hospitalTypeTag != null">
                AND hospital_type_tag = #{option.hospitalTypeTag}
            </if>
            <if test="option.hospitalAreaCode != null and option.hospitalAreaCode != ''">
                AND hospital_code LIKE CONCAT('%', #{option.hospitalAreaCode}, '%')
            </if>
            <if test="option.tenantId != null">
                AND tenant_id = #{option.tenantId}
            </if>
            <if test="option.hospitalId != null">
                AND parent_id = #{option.hospitalId}
            </if>
            <if test="option.hospitalAreaId != null">
                AND id = #{option.hospitalAreaId}
            </if>
            <if test="option.name != null and option.name != ''">
                AND name LIKE CONCAT('%', #{option.name}, '%')
            </if>
            <if test="option.status != null">
                AND status = #{option.status}
            </if>
            <if test="option.startCreateTime != null and option.endCreateTime != null">
                AND create_time BETWEEN #{option.startCreateTime} AND #{option.endCreateTime}
            </if>
            <if test="option.departmentLayer != null">
                AND department_layer = #{option.departmentLayer}
            </if>
            <if test="option.tagDictLabel != null and option.tagDictLabel != ''">
                AND tag_dict_label LIKE CONCAT('%', #{option.tagDictLabel}, '%')
            </if>
            <if test="option.includeIds != null and option.includeIds.size > 0">
                AND id IN
                <foreach collection="option.includeIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="option.excludeIds != null and option.excludeIds.size > 0">
                AND id NOT IN
                <foreach collection="option.excludeIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="option.includeTenantIds != null and option.includeTenantIds.size > 0">
                AND tenant_id IN
                <foreach collection="option.includeTenantIds" item="tid" open="(" separator="," close=")">
                    #{tid}
                </foreach>
            </if>
            <if test="option.excludeTenantIds != null and option.excludeTenantIds.size > 0">
                AND tenant_id NOT IN
                <foreach collection="option.excludeTenantIds" item="tid" open="(" separator="," close=")">
                    #{tid}
                </foreach>
            </if>
            <if test="option.includeHospitalIds != null and option.includeHospitalIds.size > 0">
                AND parent_id IN
                <foreach collection="option.includeHospitalIds" item="hid" open="(" separator="," close=")">
                    #{hid}
                </foreach>
            </if>
            <if test="option.excludeHospitalIds != null and option.excludeHospitalIds.size > 0">
                AND parent_id NOT IN
                <foreach collection="option.excludeHospitalIds" item="hid" open="(" separator="," close=")">
                    #{hid}
                </foreach>
            </if>
            <if test="option.includeActivePaymentIds != null and option.includeActivePaymentIds.size > 0">
                AND id IN
                <foreach collection="option.includeActivePaymentIds" item="pid" open="(" separator="," close=")">
                    #{pid}
                </foreach>
            </if>
            <if test="option.excludeStatus != null and option.excludeStatus.size > 0">
                AND status NOT IN
                <foreach collection="option.excludeStatus" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="option.category != null and option.category.size > 0">
                AND (
                <foreach collection="option.category" item="cat" separator=" OR ">
                    FIND_IN_SET(#{cat}, category) > 0
                </foreach>
                )
            </if>
            <if test="option.excludeCategory != null and option.excludeCategory.size > 0">
                AND (
                <foreach collection="option.excludeCategory" item="cat" separator=" AND ">
                    FIND_IN_SET(#{cat}, category) = 0
                </foreach>
                )
            </if>
        </where>
        ORDER BY display_sort DESC, id ASC
    </select>
</mapper>
