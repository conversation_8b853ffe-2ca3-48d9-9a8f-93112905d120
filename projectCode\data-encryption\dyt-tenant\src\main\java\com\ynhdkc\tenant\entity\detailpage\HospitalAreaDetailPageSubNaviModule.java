package com.ynhdkc.tenant.entity.detailpage;

import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @since 2023/5/24 16:44:48
 */
@Table(name = "t_hospital_detail_page_sub_navi_module")
@Data
@EqualsAndHashCode(callSuper = true)
public class HospitalAreaDetailPageSubNaviModule extends BaseEntity {

    private Long tenantId;

    private Long hospitalId;

    private Long hospitalAreaId;

    private String hospitalCode;

    private Integer subNaviType;

    private Integer subNaviDisplayLimit;

    private Integer sort;

    private String channels;
}
