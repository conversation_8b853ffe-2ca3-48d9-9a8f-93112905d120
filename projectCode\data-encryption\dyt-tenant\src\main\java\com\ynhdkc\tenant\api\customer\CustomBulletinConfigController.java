package com.ynhdkc.tenant.api.customer;

import com.ynhdkc.tenant.handler.CustomBulletinConfigApi;
import com.ynhdkc.tenant.model.CustomBulletinConfigVo;
import com.ynhdkc.tenant.service.backend.BulletinConfigService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Api(tags = "CustomBulletinConfig")
@RestController
@RequiredArgsConstructor
public class CustomBulletinConfigController implements CustomBulletinConfigApi {
    private final BulletinConfigService bulletinConfigService;

    @Override
    public ResponseEntity<CustomBulletinConfigVo> getCurrentBulletinConfig(Long userId) {
        return ResponseEntity.ok(bulletinConfigService.queryCurrentBulletinConfig(userId));
    }

}
