# =====================================================
# 测试环境数据加密配置文件
# 用于验证数据加密功能的集成测试
# =====================================================

spring:
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb-encrypted;MODE=MYSQL;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    initialization-mode: always
    
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.H2Dialect

  h2:
    console:
      enabled: true
      path: /h2-console

# 测试环境的加密配置
backend:
  encryption:
    # 启用加密功能
    enabled: true

    # 测试环境密钥
    secret-key: "DYT-Test-Encryption-Key-2024-Testing-Environment"

    # 默认加密算法
    algorithm: AES_GCM

    # 启用调试和性能监控
    debug-enabled: true
    performance-monitor-enabled: true

    # 字段级别的加密策略配置
    strategies:

      # =====================================================
      # t_tenant_user 表字段配置
      # =====================================================

      # 手机号码（重要数据）
      tenant-user-phone:
        strategy: SHADOW_PRIORITY
        shadow-field: "phone_number_encrypted"
        algorithm: AES_GCM
        enabled: true
        description: "租户用户手机号码加密"
        version: 1

      # 用户名（重要数据）
      tenant-user-name:
        strategy: SHADOW_PRIORITY
        shadow-field: "name_encrypted"
        algorithm: AES_GCM
        enabled: true
        description: "租户用户名加密"
        version: 1

      # =====================================================
      # t_tenant 表字段配置
      # =====================================================

      # 联系人手机号（重要数据）
      tenant-contact-phone:
        strategy: SHADOW_PRIORITY
        shadow-field: "contact_phone_number_encrypted"
        algorithm: AES_GCM
        enabled: true
        description: "租户联系人手机号加密"
        version: 1

      # 联系人邮箱（重要数据）
      tenant-contact-email:
        strategy: SHADOW_PRIORITY
        shadow-field: "contact_email_encrypted"
        algorithm: AES_GCM
        enabled: true
        description: "租户联系人邮箱加密"
        version: 1

    # =====================================================
    # 国密算法配置（测试环境）
    # =====================================================
    sm2:
      enabled: false  # 测试环境暂时关闭国密算法
      public-key: "04F6E0C3345AE42B51E06BF50B98834988DCFD30022722A88CDA3F104F8F54C83AB29E83606E4D52B70104DCBCBCE2C770F2F8A2B16971D2A8B8C6B47FA2C8A4"
      private-key: "59276E27D506861A16680F3AD9C02DCCEF3CC1FA3CDBE4CE6D54B80DEAC1BC21"

    sm4:
      enabled: false
      secret-key: "0123456789ABCDEFFEDCBA9876543210"

# 日志配置
logging:
  level:
    com.ynhdkc.tenant: DEBUG
    backend.encryption: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    root: INFO

# MyBatis配置
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true

# 分页配置
pagehelper:
  helper-dialect: h2
  reasonable: true
  support-methods-arguments: true
  params: count=countSql
