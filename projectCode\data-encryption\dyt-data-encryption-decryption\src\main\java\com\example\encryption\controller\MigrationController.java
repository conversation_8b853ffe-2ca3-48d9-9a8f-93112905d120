package com.example.encryption.controller;

import com.example.encryption.entity.MigrationUser;
import com.example.encryption.service.MigrationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

/**
 * 影子字段迁移控制器
 * 提供渐进式数据迁移的REST API接口
 */
@RestController
@RequestMapping("/api/migration")
@Validated
public class MigrationController {
    
    private static final Logger logger = LoggerFactory.getLogger(MigrationController.class);
    
    @Autowired
    private MigrationService migrationService;
    
    /**
     * 创建迁移用户
     */
    @PostMapping("/users")
    public ResponseEntity<MigrationUser> createUser(@Valid @RequestBody MigrationUser user) {
        try {
            MigrationUser createdUser = migrationService.createUser(user);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdUser);
        } catch (Exception e) {
            logger.error("Failed to create migration user", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }
    
    /**
     * 根据ID获取用户
     */
    @GetMapping("/users/{id}")
    public ResponseEntity<MigrationUser> getUserById(@PathVariable Long id) {
        Optional<MigrationUser> user = migrationService.getUserById(id);
        return user.map(u -> ResponseEntity.ok(u))
                  .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 根据用户名获取用户
     */
    @GetMapping("/users/username/{username}")
    public ResponseEntity<MigrationUser> getUserByUsername(@PathVariable String username) {
        Optional<MigrationUser> user = migrationService.getUserByUsername(username);
        return user.map(u -> ResponseEntity.ok(u))
                  .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 获取所有用户
     */
    @GetMapping("/users")
    public ResponseEntity<List<MigrationUser>> getAllUsers() {
        List<MigrationUser> users = migrationService.getAllUsers();
        return ResponseEntity.ok(users);
    }
    
    /**
     * 更新用户信息
     */
    @PutMapping("/users/{id}")
    public ResponseEntity<MigrationUser> updateUser(@PathVariable Long id, @Valid @RequestBody MigrationUser user) {
        try {
            user.setId(id);
            MigrationUser updatedUser = migrationService.updateUser(user);
            return ResponseEntity.ok(updatedUser);
        } catch (Exception e) {
            logger.error("Failed to update migration user", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }
    
    /**
     * 批量迁移用户数据到指定版本
     */
    @PostMapping("/migrate/{targetVersion}")
    public ResponseEntity<MigrationService.MigrationResult> migrateToVersion(@PathVariable Integer targetVersion) {
        try {
            logger.info("Starting migration to version: {}", targetVersion);
            MigrationService.MigrationResult result = migrationService.migrateUsersToVersion(targetVersion);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("Failed to migrate users to version: {}", targetVersion, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 批量回滚用户数据到指定版本
     */
    @PostMapping("/rollback/{targetVersion}")
    public ResponseEntity<MigrationService.MigrationResult> rollbackToVersion(@PathVariable Integer targetVersion) {
        try {
            logger.info("Starting rollback to version: {}", targetVersion);
            MigrationService.MigrationResult result = migrationService.rollbackUsersToVersion(targetVersion);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("Failed to rollback users to version: {}", targetVersion, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 获取迁移统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<MigrationService.MigrationStatistics> getMigrationStatistics() {
        try {
            MigrationService.MigrationStatistics stats = migrationService.getMigrationStatistics();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            logger.error("Failed to get migration statistics", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 获取迁移策略说明
     */
    @GetMapping("/strategies")
    public ResponseEntity<MigrationStrategyInfo> getMigrationStrategies() {
        MigrationStrategyInfo info = new MigrationStrategyInfo();
        return ResponseEntity.ok(info);
    }
    
    /**
     * 迁移策略信息
     */
    public static class MigrationStrategyInfo {
        private final String directEncrypt = "直接加密模式：直接在当前字段上进行加密存储，适用于新字段或无历史数据的场景";
        private final String plaintextPriority = "明文字段优先模式：读取时优先从明文字段读取，写入时同时写入明文和影子字段，适用于迁移初期";
        private final String shadowPriority = "影子字段优先模式：读取时优先从影子字段读取，写入时只写入影子字段，适用于迁移中期";
        private final String shadowOnly = "仅影子字段模式：只使用影子字段进行读写，完全忽略明文字段，适用于迁移完成后";
        
        public String getDirectEncrypt() { return directEncrypt; }
        public String getPlaintextPriority() { return plaintextPriority; }
        public String getShadowPriority() { return shadowPriority; }
        public String getShadowOnly() { return shadowOnly; }
    }
}
