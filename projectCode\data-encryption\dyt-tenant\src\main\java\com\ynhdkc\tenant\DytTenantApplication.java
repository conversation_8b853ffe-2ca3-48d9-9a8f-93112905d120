package com.ynhdkc.tenant;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableFeignClients(basePackages = {"com.ynhdkc.tenant", "com.ynhdkc.oldsystem.integration.client"})
@SpringBootApplication
@ConfigurationPropertiesScan
@EnableCaching
@MapperScan(basePackages = "com.ynhdkc.tenant.dao.mapper")
@EnableAsync(proxyTargetClass = true)
public class DytTenantApplication {

    public static void main(String[] args) {
        SpringApplication.run(DytTenantApplication.class, args);
    }

}
