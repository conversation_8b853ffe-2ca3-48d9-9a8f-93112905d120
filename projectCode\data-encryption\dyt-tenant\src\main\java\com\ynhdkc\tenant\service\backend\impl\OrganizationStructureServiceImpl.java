package com.ynhdkc.tenant.service.backend.impl;

import backend.common.domain.tenant.TenantUserStructureEntity;
import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import backend.common.util.ResponseEntityUtil;
import backend.security.entity.OrganizationStructureTree;
import backend.security.entity.TenantUserAscription;
import backend.security.entity.TenantUserStructureTree;
import backend.security.repository.BackendPrivilegeRepository;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.client.PrivilegeClient;
import com.ynhdkc.tenant.dao.*;
import com.ynhdkc.tenant.entity.*;
import com.ynhdkc.tenant.model.RpcUserBatchProcessRolesItem;
import com.ynhdkc.tenant.model.RpcUserBatchProcessRolesReqDto;
import com.ynhdkc.tenant.model.UserDepartmentPrivilegeConfig;
import com.ynhdkc.tenant.model.UserTenantPrivilegeConfig;
import com.ynhdkc.tenant.service.backend.OrganizationStructureService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-25 14:07
 */
@Service
@RequiredArgsConstructor
public class OrganizationStructureServiceImpl implements OrganizationStructureService {
    private final TenantUserQuery tenantUserQuery;
    private final OrganizationStructureQuery organizationStructureQuery;
    private final OrganizationStructureRepository organizationStructureRepository;
    private final TenantQuery tenantQuery;
    private final HospitalQuery hospitalQuery;
    private final HospitalAreaQuery hospitalAreaQuery;
    private final DepartmentQuery departmentQuery;

    private final BackendPrivilegeRepository backendPrivilegeRepository;

    private final PrivilegeClient privilegeClient;

    @Override
    public void syncOrganizationStructure() {
        /* 查询所有租户 */
        List<Tenant> tenants;
        try (Page<Tenant> tenantPage = tenantQuery.pageQueryTenant(new TenantQuery.TenantQueryOption(1, Integer.MAX_VALUE))) {
            tenants = tenantPage.getResult();
        }
        /* 查询所有医院 */
        List<Hospital> hospitals;
        try (Page<Hospital> hospitalPage = hospitalQuery.pageQueryHospital(new HospitalQuery.HospitalQueryOption(1, Integer.MAX_VALUE))) {
            hospitals = hospitalPage.getResult();
        }
        /* 查询所有院区 */
        List<Hospital> hospitalAreas;
        try (Page<Hospital> hospitalAreaPage = hospitalAreaQuery.pageQueryHospitalArea(new HospitalAreaQuery.HospitalAreaQueryOption(1, Integer.MAX_VALUE))) {
            hospitalAreas = hospitalAreaPage.getResult();
        }
        /* 查询所有科室 */
        List<Department> departments;
        try (Page<Department> departmentPage = departmentQuery.pageQueryDepartment(new DepartmentQuery.DepartmentQueryOption(1, Integer.MAX_VALUE))) {
            departments = departmentPage.getResult();
        }
        /* 初始化树 */
        OrganizationStructureTree organizationStructureTree = new OrganizationStructureTree(tenants, hospitals, hospitalAreas, departments);
        organizationStructureTree.setAdminUsers(tenantUserQuery.query(new TenantUserQuery.TenantUserQueryOption(1, Integer.MAX_VALUE)
                        .setAdmin(true)).stream()
                .map(TenantUser::getId)
                .collect(Collectors.toSet())
        );
        backendPrivilegeRepository.saveOrganizationStructureTree(organizationStructureTree);
    }

    @Override
    public void syncUserOrganizationStructure() {
        List<TenantUserStructure> structures = organizationStructureQuery.queryUserStructure(new OrganizationStructureQuery.OrganizationStructureQueryOption(1, Integer.MAX_VALUE));
        OrganizationStructureTree organizationStructureTree = backendPrivilegeRepository.getOrganizationStructureTree();

        /* 填充用户到组织架构中 */
        organizationStructureTree.initializeUsers(structures);

        /* 获取用户读写权限 */
        Set<Long> userIds = structures.stream().map(TenantUserStructure::getUserId).collect(Collectors.toSet());
        List<TenantUserAscription> tenantUserAscriptions = organizationStructureTree.getAllUserAscription(userIds);
        List<TenantUserStructureTree> tenantUserStructureTrees = organizationStructureTree.getAllUserPrivilegeTree(userIds, true);

        tenantUserAscriptions.forEach(backendPrivilegeRepository::saveTenantUserAscription);
        tenantUserStructureTrees.forEach(backendPrivilegeRepository::saveTenantUserStructureTree);
    }

    @Override
    public void syncUserOrganizationStructure(Long userId) {
        List<TenantUserStructure> structures = organizationStructureQuery.queryUserStructure(new OrganizationStructureQuery.OrganizationStructureQueryOption(1, Integer.MAX_VALUE)
                .setUserId(userId));
        OrganizationStructureTree organizationStructureTree = backendPrivilegeRepository.getOrganizationStructureTree();

        /* 填充用户到组织架构中 */
        organizationStructureTree.initializeUsers(structures);

        /* 获取用户读写权限 */
        Set<Long> userIds = structures.stream().map(TenantUserStructure::getUserId).collect(Collectors.toSet());
        List<TenantUserAscription> tenantUserAscriptions = organizationStructureTree.getAllUserAscription(userIds);
        List<TenantUserStructureTree> tenantUserStructureTrees = organizationStructureTree.getAllUserPrivilegeTree(userIds, true);

        tenantUserAscriptions.forEach(backendPrivilegeRepository::saveTenantUserAscription);
        tenantUserStructureTrees.forEach(backendPrivilegeRepository::saveTenantUserStructureTree);
    }

    @Override
    public void syncDeleteUserOrganizationStructure(Long userId) {
        backendPrivilegeRepository.deleteTenantUserStructureTree(userId);
        backendPrivilegeRepository.deleteTenantUserAscription(userId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createOrUpdateUserStructure(final Long userId, List<UserTenantPrivilegeConfig> tenantConfigs) {
        if (null == userId) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "绑定组织架构的用户ID不能为空");
        }
        if (null == tenantConfigs || tenantConfigs.isEmpty()) {
            return;
        }
        /* 设置用户组织架构 */
        List<TenantUserStructure> privileges = createUserPrivileges(userId, tenantConfigs);
        Set<Long> tenantIds = privileges.stream().map(TenantUserStructure::getTenantId).collect(Collectors.toSet());
        organizationStructureRepository.createOrUpdateUserStructure(userId, tenantIds, privileges);
        /* 批量绑定角色，最后执行，避免不同服务无法回滚 */
        processUserRoles(userId, tenantConfigs);
    }

    @Override
    public void asyncUserOrganizationStructureAndUser() {
        asyncExecute(() -> {
            syncOrganizationStructure();
            syncUserOrganizationStructure();
        });
    }

    @Override
    public void asyncUserOrganizationStructureAndUser(Long userId) {
        asyncExecute(() -> {
            syncOrganizationStructure();
            syncUserOrganizationStructure(userId);
        });
    }

    @Override
    public void asyncDeleteUserOrganizationStructure(Long userId) {
        asyncExecute(() -> syncDeleteUserOrganizationStructure(userId));
    }

    private static void asyncExecute(Runnable runnable) {
        new Thread(runnable).start();
    }

    private void processUserRoles(Long userId, List<UserTenantPrivilegeConfig> tenantConfigs) {
        if (null == tenantConfigs || tenantConfigs.isEmpty()) {
            return;
        }
        RpcUserBatchProcessRolesReqDto reqDto = new RpcUserBatchProcessRolesReqDto()
                ._list(tenantConfigs.stream().map(config -> new RpcUserBatchProcessRolesItem()
                        .tenantId(config.getTenantId())
                        .bindRoles(config.getBindRoles())
                        .unbindRoles(config.getUnbindRoles())).collect(Collectors.toList()));
        ResponseEntity<BaseOperationResponse> rpcResp = privilegeClient.userBatchProcessRoles(userId, reqDto);
        ResponseEntityUtil.assertAndGetBody(rpcResp, "用户绑定角色失败");
    }

    private List<TenantUserStructure> createUserPrivileges(Long userId, List<UserTenantPrivilegeConfig> tenantConfigs) {
        List<TenantUserStructure> privileges = new ArrayList<>();
        tenantConfigs.forEach(tenantConfig -> {
            /* 是否为租户管理员 */
            if (Boolean.TRUE.equals(tenantConfig.isTenantAdmin())) {
                TenantUserStructure privilege = new TenantUserStructure();
                privilege.setUserId(userId)
                        .setTenantId(tenantConfig.getTenantId())
                        .setHospitalId(TenantUserStructureEntity.ADMIN_VALUE)
                        .setHospitalAreaId(TenantUserStructureEntity.ADMIN_VALUE)
                        .setDepartmentId(TenantUserStructureEntity.ADMIN_VALUE)
                        .setDepartmentAdmin(true);
                privileges.add(privilege);
                return;
            }
            tenantConfig.getHospitalPrivilegeConfigs().forEach(hospitalConfig -> {
                /* 是否为医院管理员 */
                if (Boolean.TRUE.equals(hospitalConfig.isHospitalAdmin())) {
                    TenantUserStructure privilege = new TenantUserStructure();
                    privilege.setUserId(userId)
                            .setTenantId(tenantConfig.getTenantId())
                            .setHospitalId(hospitalConfig.getHospitalId())
                            .setHospitalAreaId(TenantUserStructureEntity.ADMIN_VALUE)
                            .setDepartmentId(TenantUserStructureEntity.ADMIN_VALUE)
                            .setDepartmentAdmin(true);
                    privileges.add(privilege);
                    return;
                }
                hospitalConfig.getHospitalAreaPrivilegeConfigs().forEach(hospitalAreaConfig -> {
                    /* 是否为院区管理员 */
                    if (Boolean.TRUE.equals(hospitalAreaConfig.isHospitalAreaAdmin())) {
                        TenantUserStructure privilege = new TenantUserStructure();
                        privilege.setUserId(userId)
                                .setTenantId(tenantConfig.getTenantId())
                                .setHospitalId(hospitalConfig.getHospitalId())
                                .setHospitalAreaId(hospitalAreaConfig.getHospitalAreaId())
                                .setDepartmentId(TenantUserStructureEntity.ADMIN_VALUE)
                                .setDepartmentAdmin(true);
                        privileges.add(privilege);
                        return;
                    }
                    if (CollectionUtils.isEmpty(hospitalAreaConfig.getDepartments())) {
                        throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "必须配置科室");
                    }
                    /* 遍历出所有管理员节点和成员节点 */
                    Set<Long> adminSet = new HashSet<>();
                    Set<Long> memberSet = new HashSet<>();
                    for (UserDepartmentPrivilegeConfig userDepartmentPrivilegeConfig : hospitalAreaConfig.getDepartments()) {
                        if (Boolean.TRUE.equals(userDepartmentPrivilegeConfig.isDepartmentAdmin())) {
                            adminSet.add(userDepartmentPrivilegeConfig.getDepartmentId());
                            continue;
                        }
                        memberSet.add(userDepartmentPrivilegeConfig.getDepartmentId());
                    }

                    /* 查询所有为管理员的节点以及子节点 */
                    HashMap<Long, Department> adminMap = adminSet
                            .stream()
                            .map(departmentQuery::queryDepartmentById)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toMap(Department::getId, department -> department, (k1, k2) -> k1, HashMap::new));
                    adminSet.forEach(departmentId -> extraChildrenDepartment(adminMap, departmentId));
                    adminSet = adminMap.keySet();

                    /* 查询所有成员的节点以及子节点 */
                    HashMap<Long, Department> memberMap = memberSet
                            .stream()
                            .map(departmentQuery::queryDepartmentById)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toMap(Department::getId, department -> department, (k1, k2) -> k1, HashMap::new));
                    memberSet.forEach(departmentId -> extraChildrenDepartment(memberMap, departmentId));
                    memberSet = memberMap.keySet();
                    /* 移除所有在admin中的member节点 */
                    memberSet.removeAll(adminSet);

                    adminSet.forEach(departmentId -> {
                        TenantUserStructure privilege = new TenantUserStructure();
                        privilege.setUserId(userId)
                                .setTenantId(tenantConfig.getTenantId())
                                .setHospitalId(hospitalConfig.getHospitalId())
                                .setHospitalAreaId(hospitalAreaConfig.getHospitalAreaId())
                                .setDepartmentId(departmentId)
                                .setDepartmentAdmin(true);
                        privileges.add(privilege);
                    });
                    memberSet.forEach(departmentId -> {
                        TenantUserStructure privilege = new TenantUserStructure();
                        privilege.setUserId(userId)
                                .setTenantId(tenantConfig.getTenantId())
                                .setHospitalId(hospitalConfig.getHospitalId())
                                .setHospitalAreaId(hospitalAreaConfig.getHospitalAreaId())
                                .setDepartmentId(departmentId)
                                .setDepartmentAdmin(false);
                        privileges.add(privilege);
                    });
                });
            });
        });
        return privileges;
    }

    private void extraChildrenDepartment(HashMap<Long, Department> map, Long departmentId) {
        List<Department> children = departmentQuery.queryChildrenDepartmentById(departmentId);
        if (null == children || children.isEmpty()) {
            return;
        }
        for (Department department : children) {
            map.putIfAbsent(department.getId(), department);
            extraChildrenDepartment(map, department.getId());
        }
    }
}
