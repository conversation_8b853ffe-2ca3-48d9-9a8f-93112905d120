package backend.security.oauth2;

import backend.security.constant.BackendSecurityProperties;
import backend.security.constant.JwtChannel;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;

import javax.annotation.Nullable;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static org.springframework.web.context.request.RequestAttributes.SCOPE_REQUEST;

public class BackendOAuth2User {
    public static final String REQ_ATTR = BackendOAuth2User.class.getName() + ".attr";
    public static final String CLAIM_NAME = "nickname";
    public static final String CLAIM_NICK_NAME = "nickname";
    public static final String CLAIM_SID = "sid";
    public static final String CLAIM_AUTHORITY = "auth";
    public static final String CLAIM_SCOPE = "scope";
    public static final String CLAIM_TENANT = "tenant";
    private final JwtAuthenticationToken authentication;
    private final long userId;
    private final long tenantId;
    private final String name;
    private final String nickName;
    private final String sid;
    private final Set<String> authorities;

    public BackendOAuth2User(JwtAuthenticationToken authentication) {
        this.authentication = authentication;
        final Jwt token = authentication.getToken();
        this.userId = Long.parseLong(token.getClaimAsString(BackendOAuth2Constants.SCOPE_USER));
        this.name = token.getClaimAsString(CLAIM_NAME);
        this.nickName = token.getClaimAsString(CLAIM_NICK_NAME);
        this.sid = token.getClaimAsString(CLAIM_SID);
        this.authorities = authentication.getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toSet());
        final String tokenClaimAsString = token.getClaimAsString(CLAIM_TENANT);
        this.tenantId = StringUtils.hasText(tokenClaimAsString) ? Long.parseLong(tokenClaimAsString) : -1;
    }

    public Set<String> getAuthorities() {
        return this.authorities;
    }


    public boolean hasScope(String scope) {
        return this.authorities.contains(BackendOAuth2Constants.PREFIX_SCOPE + scope);
    }

    public boolean hasAuthority(String authority) {
        return this.authorities.contains(BackendOAuth2Constants.PREFIX_AUTH + authority);
    }

    public JwtAuthenticationToken getAuthentication() {
        return authentication;
    }

    public long getUserId() {
        return userId;
    }

    public String getName() {
        return name;
    }

    public String getNickName() {
        return nickName;
    }

    public String getSid() {
        return sid;
    }

    public long getTenantId() {
        return tenantId;
    }

    @Nullable
    public static BackendOAuth2User tryGetCurrent() {
        final BackendOAuth2User previous = Optional.ofNullable(RequestContextHolder.getRequestAttributes())
                .map(attr -> (BackendOAuth2User) attr.getAttribute(REQ_ATTR, SCOPE_REQUEST)).orElse(null);
        if (previous != null) {
            return previous;
        }
        final Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication instanceof JwtAuthenticationToken) {
            final BackendOAuth2User user = new BackendOAuth2User((JwtAuthenticationToken) authentication);
            Optional.ofNullable(RequestContextHolder.getRequestAttributes()).ifPresent(attr -> {
                attr.setAttribute(REQ_ATTR, user, SCOPE_REQUEST);
            });
            return user;
        }
        return null;
    }

    public static boolean isTenantUser() {
        final Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication instanceof JwtAuthenticationToken) {
            JwtAuthenticationToken token = (JwtAuthenticationToken) authentication;
            String channel = token.getToken().getClaim(BackendSecurityProperties.JWT_CHANNEL);
            return null != channel && channel.equals(JwtChannel.TENANT_USER.name());
        }
        return false;
    }

    public static boolean isClientUser() {
        final Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication instanceof JwtAuthenticationToken) {
            JwtAuthenticationToken token = (JwtAuthenticationToken) authentication;
            String channel = token.getToken().getClaim(BackendSecurityProperties.JWT_CHANNEL);
            return null != channel && channel.equals(JwtChannel.CLIENT_USER.name());
        }
        return false;
    }

    public static Long getUserIdFromJwt() {
        return getProertyFromJwt(BackendOAuth2Constants.SCOPE_USER);
    }

    public static <T> T getProertyFromJwt(String propertyName) {
        final Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication instanceof JwtAuthenticationToken) {
            JwtAuthenticationToken token = (JwtAuthenticationToken) authentication;
            return token.getToken().getClaim(propertyName);
        }
        return null;
    }

    public static BackendOAuth2User requireCurrent() {
        final BackendOAuth2User user = tryGetCurrent();
        if (user == null) {
            throw new IllegalStateException("Can't get user");
        }
        return user;
    }

    public boolean isSystemManager() {
        return this.hasScope(BackendOAuth2Constants.SCOPE_GLOBAL);
    }

    public boolean isTenantManager() {
        return this.hasScope(BackendOAuth2Constants.SCOPE_TENANT);
    }

    public boolean isRPCAccess() {
        return this.hasScope(BackendOAuth2Constants.SCOPE_RPC);
    }
}
