package com.ynhdkc.tenant.api.rpc;

import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.handler.RpcTenantUserApi;
import com.ynhdkc.tenant.model.TenantUserVo;
import com.ynhdkc.tenant.model.VerifyTenantUserPasswordDto;
import com.ynhdkc.tenant.service.backend.RpcTenantUserService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-08 16:17
 */
@Api(tags = "RpcTenantUser")
@RestController
@RequiredArgsConstructor
public class RpcTenantUserController implements RpcTenantUserApi {
    private final RpcTenantUserService rpcTenantUserService;

    @Override
    public ResponseEntity<TenantUserVo> getTenantUserDetail(Long userId) {
        return ResponseEntity.ok(rpcTenantUserService.getDetail(userId));
    }

    @Override
    public ResponseEntity<TenantUserVo> getTenantUserDetailByNameOrPhone(String userName, String userPhone) {
        return ResponseEntity.ok(rpcTenantUserService.getDetail(userName, userPhone));
    }

    @Override
    public ResponseEntity<BaseOperationResponse> passwordAndCodeVerify(VerifyTenantUserPasswordDto request) {
        return ResponseEntity.ok(rpcTenantUserService.passwordAndCodeVerify(request));
    }

    @Override
    public ResponseEntity<BaseOperationResponse> passwordVerify(VerifyTenantUserPasswordDto request) {
        return ResponseEntity.ok(rpcTenantUserService.passwordVerify(request));
    }
}
