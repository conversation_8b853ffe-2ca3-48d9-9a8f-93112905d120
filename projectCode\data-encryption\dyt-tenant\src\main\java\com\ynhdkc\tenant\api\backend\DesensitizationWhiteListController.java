package com.ynhdkc.tenant.api.backend;/**
 * <AUTHOR>
 * @date 2024/07/23/14:59
 */

import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.handler.DesensitizationWhiteListApi;
import com.ynhdkc.tenant.model.DesensitizationWhiteListVo;
import com.ynhdkc.tenant.model.WhiteListCreateReqDto;
import com.ynhdkc.tenant.model.WhiteListPageVo;
import com.ynhdkc.tenant.model.WhiteListUpdateReqDto;
import com.ynhdkc.tenant.service.backend.DesensitizationWhiteListService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description
 * @date 2024/7/23 14:59
 **/
@Api(tags = "DesensitizationWhiteList")
@RestController
@RequiredArgsConstructor
public class DesensitizationWhiteListController implements DesensitizationWhiteListApi {
    private final DesensitizationWhiteListService desensitizationWhiteListService;

    @Override
    public ResponseEntity<DesensitizationWhiteListVo> createDesensitizationWhiteList(WhiteListCreateReqDto request) {
        return ResponseEntity.ok(desensitizationWhiteListService.create(request));
    }

    @Override
    public ResponseEntity<BaseOperationResponse> deleteDesensitizationWhiteList(Long id) {
        desensitizationWhiteListService.delete(id);
        return ResponseEntity.ok(new BaseOperationResponse());
    }

    @Override
    public ResponseEntity<DesensitizationWhiteListVo> getDesensitizationWhiteListById(Long id) {
        return ResponseEntity.ok(desensitizationWhiteListService.get(id));
    }

    @Override
    public ResponseEntity<WhiteListPageVo> page(Integer page, Integer size, String tenantName, String tenantUserName) {
        return ResponseEntity.ok(desensitizationWhiteListService.findAll(page, size, tenantName, tenantUserName));
    }

    @Override
    public ResponseEntity<DesensitizationWhiteListVo> updateDesensitizationWhiteList(Long entryId, WhiteListUpdateReqDto request) {

        return ResponseEntity.ok(desensitizationWhiteListService.update(entryId, request));
    }
}
