package com.ynhdkc.tenant.api.rpc;

import com.ynhdkc.tenant.handler.RpcTenantApi;
import com.ynhdkc.tenant.model.GetTenantAllUserIdsVo;
import com.ynhdkc.tenant.model.TenantVo;
import com.ynhdkc.tenant.service.backend.TenantService;
import com.ynhdkc.tenant.service.backend.TenantUserService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-08 16:17
 */
@Api(tags = "RpcTenant")
@RestController
@RequiredArgsConstructor
public class RpcTenantController implements RpcTenantApi {
    private final TenantService tenantService;
    private final TenantUserService tenantUserService;

    @Override
    public ResponseEntity<GetTenantAllUserIdsVo> getTenantAllUserIds(Long tenantId) {
        return ResponseEntity.ok(new GetTenantAllUserIdsVo()._list(tenantUserService.getTenantBoundUser(tenantId)));
    }

    @Override
    public ResponseEntity<TenantVo> getTenantDetail(Long id) {
        return ResponseEntity.ok(tenantService.rpcGetDetail(id));
    }
}
