package com.ynhdkc.tenant.util;

import backend.common.util.ObjectsUtils;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.util.Base64;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PathUtil {

    private static final String BASE64_PRE = "data:image/jpeg;base64,";

    private static final int BASE64_PRE_LENGTH = BASE64_PRE.length();


    // 判断字符串是否为Base64编码
    public static boolean isBase64(String str) {
        if (ObjectsUtils.isEmpty(str)) {
            return false;
        }
        try {
            Base64.getDecoder().decode(str);
            return str.matches("^[A-Za-z0-9+/=]+$");
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    public static String base64PreProcess(String rawValue) {
        if (!rawValue.startsWith(BASE64_PRE)) {
            return rawValue;
        }
        return rawValue.substring(BASE64_PRE_LENGTH);
    }

    // 判断字符串是否为URL（包括相对路径）
    public static boolean isUrl(String url) {
        // 正则表达式用于匹配绝对和相对URL
        String urlRegex = "^(https?|ftp|file)://.*|^/.*";
        Pattern pattern = Pattern.compile(urlRegex);
        Matcher matcher = pattern.matcher(url);
        return matcher.matches();
    }
}