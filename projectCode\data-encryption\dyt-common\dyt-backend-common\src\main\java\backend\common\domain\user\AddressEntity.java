package backend.common.domain.user;


import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AddressEntity extends BaseEntity {
    private String country;
    private String province;
    private String city;
    private String county;
    private String detail;
    private BigDecimal longitude;
    private BigDecimal latitude;
}
