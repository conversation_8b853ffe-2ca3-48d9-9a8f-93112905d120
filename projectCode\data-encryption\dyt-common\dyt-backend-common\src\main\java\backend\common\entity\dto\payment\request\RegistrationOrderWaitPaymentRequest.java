package backend.common.entity.dto.payment.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class RegistrationOrderWaitPaymentRequest {

    @JsonProperty("order_number")
    private String orderNumber;

    @JsonProperty("total_fee")
    private BigDecimal totalFee;

    @JsonProperty("expiration_time")
    private Long expirationTime;
}
