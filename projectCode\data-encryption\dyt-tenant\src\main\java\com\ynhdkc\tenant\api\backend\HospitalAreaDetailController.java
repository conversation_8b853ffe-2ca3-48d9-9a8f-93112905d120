package com.ynhdkc.tenant.api.backend;

import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import backend.security.oplog.DytSecurityRequired;
import com.ynhdkc.tenant.entity.constant.NavigatorTypeEnum;
import com.ynhdkc.tenant.handler.HospitalAreaDetailApi;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.*;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-06-01 10:57
 */
@Api(tags = "HospitalAreaDetail")
@RestController
@RequiredArgsConstructor
public class HospitalAreaDetailController implements HospitalAreaDetailApi {
    private final IHospitalAreaDetailPageConfigService hospitalAreaDetailPageConfigService;
    private final IHospitalAreaDetailPageTabService hospitalAreaDetailPageTabService;
    private final IHospitalAreaDetailPageCubeService hospitalAreaDetailPageCubeService;
    private final IHospitalAreaDetailPageCubeModuleService hospitalAreaDetailPageCubeModuleService;
    private final IHospitalAreaDetailPageNavigatorService hospitalAreaDetailPageNavigatorService;
    private final IHospitalAreaDetailPageSubNaviModuleService hospitalAreaDetailPageSubNaviModuleService;

    @Override
    public ResponseEntity<HospitalAreaDetailPageConfigVo> createHospitalAreaDetailPageConfig(CreateHospitalAreaDetailPageConfigReqDto request) {
        return ResponseEntity.ok(hospitalAreaDetailPageConfigService.createHospitalAreaDetailPageConfig(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:createHospitalAreaDetailPageCube")
    public ResponseEntity<HospitalAreaDetailPageCubeVo> createHospitalAreaDetailPageCube(CreateHospitalAreaDetailPageCubeReqDto request) {
        return ResponseEntity.ok(hospitalAreaDetailPageCubeService.createHospitalAreaDetailPageCube(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:createHospitalAreaDetailPageCubeModule")
    public ResponseEntity<HospitalAreaDetailPageCubeModuleVo> createHospitalAreaDetailPageCubeModule(CreateHospitalAreaDetailPageCubeModuleReqDto request) {
        return ResponseEntity.ok(hospitalAreaDetailPageCubeModuleService.createHospitalAreaDetailPageCubeModule(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:createHospitalAreaDetailPageNavigator")
    public ResponseEntity<HospitalAreaDetailPageNavigatorVo> createHospitalAreaDetailPageNavigator(CreateHospitalAreaDetailPageNavigatorReqDto request) {
        if (NavigatorTypeEnum.SUB_NAVIGATOR.getCode().equals(request.getType()) && null == request.getId()) {
            throw new BizException(HttpStatus.BAD_REQUEST, "模块id不能为空");
        }
        return ResponseEntity.ok(hospitalAreaDetailPageNavigatorService.createHospitalAreaDetailPageNavigator(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:createHospitalAreaDetailPageSubNaviModule")
    public ResponseEntity<HospitalAreaDetailPageSubNaviModuleVo> createHospitalAreaDetailPageSubNaviModule(CreateHospitalAreaDetailPageSubNaviModuleReqDto request) {
        return ResponseEntity.ok(hospitalAreaDetailPageSubNaviModuleService.createHospitalAreaDetailPageSubNaviModule(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:createHospitalAreaDetailPageTab")
    public ResponseEntity<HospitalAreaDetailPageTabVo> createHospitalAreaDetailPageTab(CreateHospitalAreaDetailPageTabReqDto request) {
        return ResponseEntity.ok(hospitalAreaDetailPageTabService.createHospitalAreaDetailPageTab(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:deleteHospitalAreaDetailPageConfig")
    public ResponseEntity<BaseOperationResponse> deleteHospitalAreaDetailPageConfig(Long configId, Long tenantId, Long hospitalId, Long hospitalAreaId) {
        return ResponseEntity.ok(hospitalAreaDetailPageConfigService.deleteHospitalAreaDetailPageConfig(configId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:deleteHospitalAreaDetailPageCube")
    public ResponseEntity<BaseOperationResponse> deleteHospitalAreaDetailPageCube(Long cubeId) {
        return ResponseEntity.ok(hospitalAreaDetailPageCubeService.deleteHospitalAreaDetailPageCube(cubeId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:deleteHospitalAreaDetailPageCubeModule")
    public ResponseEntity<BaseOperationResponse> deleteHospitalAreaDetailPageCubeModule(Long cubeModuleId) {
        return ResponseEntity.ok(hospitalAreaDetailPageCubeModuleService.deleteHospitalAreaDetailPageCubeModule(cubeModuleId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:deleteHospitalAreaDetailPageNavigator")
    public ResponseEntity<BaseOperationResponse> deleteHospitalAreaDetailPageNavigator(Long navigatorId) {
        return ResponseEntity.ok(hospitalAreaDetailPageNavigatorService.deleteHospitalAreaDetailPageNavigator(navigatorId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:deleteHospitalAreaDetailPageSubNaviModule")
    public ResponseEntity<BaseOperationResponse> deleteHospitalAreaDetailPageSubNaviModule(Long moduleId) {
        return ResponseEntity.ok(hospitalAreaDetailPageSubNaviModuleService.deleteHospitalAreaDetailPageSubNaviModule(moduleId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:deleteHospitalAreaDetailPageTab")
    public ResponseEntity<BaseOperationResponse> deleteHospitalAreaDetailPageTab(Long tabId) {
        return ResponseEntity.ok(hospitalAreaDetailPageTabService.deleteHospitalAreaDetailPageTab(tabId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:getHospitalAreaDetailPageConfig")
    public ResponseEntity<HospitalAreaDetailPageConfigVo> getHospitalAreaDetailPageConfig(Long configId) {
        return ResponseEntity.ok(hospitalAreaDetailPageConfigService.getHospitalAreaDetailPageConfig(configId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:getHospitalAreaDetailPageCube")
    public ResponseEntity<HospitalAreaDetailPageCubeVo> getHospitalAreaDetailPageCube(Long cubeId) {
        return ResponseEntity.ok(hospitalAreaDetailPageCubeService.getHospitalAreaDetailPageCube(cubeId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:getHospitalAreaDetailPageCubeModule")
    public ResponseEntity<HospitalAreaDetailPageCubeModuleVo> getHospitalAreaDetailPageCubeModule(Long cubeModuleId) {
        return ResponseEntity.ok(hospitalAreaDetailPageCubeModuleService.getHospitalAreaDetailPageCubeModule(cubeModuleId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:getHospitalAreaDetailPageNavigator")
    public ResponseEntity<HospitalAreaDetailPageNavigatorVo> getHospitalAreaDetailPageNavigator(Long hospitalAreaId) {
        return ResponseEntity.ok(hospitalAreaDetailPageNavigatorService.getHospitalAreaDetailPageNavigator(hospitalAreaId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:getHospitalAreaDetailPageSubNaviModule")
    public ResponseEntity<HospitalAreaDetailPageSubNaviModuleVo> getHospitalAreaDetailPageSubNaviModule(Long moduleId) {
        return ResponseEntity.ok(hospitalAreaDetailPageSubNaviModuleService.getHospitalAreaDetailPageSubNaviModule(moduleId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:getHospitalAreaDetailPageSubNaviModuleByHospitalAreaId")
    public ResponseEntity<List<HospitalAreaDetailPageSubNaviModuleVo>> getHospitalAreaDetailPageSubNaviModuleByHospitalAreaId(Long hospitalAreaId) {
        return ResponseEntity.ok(hospitalAreaDetailPageSubNaviModuleService.getHospitalAreaDetailPageSubNaviModuleByHospitalAreaId(hospitalAreaId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:getHospitalAreaDetailPageTabByHospitalAreaId")
    public ResponseEntity<List<HospitalAreaDetailPageTabVo>> getHospitalAreaDetailPageTabByHospitalAreaId(Long hospitalAreaId) {
        return ResponseEntity.ok(hospitalAreaDetailPageTabService.getHospitalAreaDetailPageTabByHospitalAreaId(hospitalAreaId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:getHospitalDetailPageCubeModuleByHospitalAreaId")
    public ResponseEntity<List<HospitalAreaDetailPageCubeModuleVo>> getHospitalDetailPageCubeModuleByHospitalAreaId(Long hospitalAreaId) {
        return ResponseEntity.ok(hospitalAreaDetailPageCubeModuleService.getHospitalAreaDetailPageCubeModuleByHospitalAreaId(hospitalAreaId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:getHospitalDetailPageTab")
    public ResponseEntity<HospitalAreaDetailPageTabVo> getHospitalDetailPageTab(Long tabId) {
        return ResponseEntity.ok(hospitalAreaDetailPageTabService.getHospitalAreaDetailPageTab(tabId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:searchHospitalAreaDetailPageConfig")
    public ResponseEntity<HospitalAreaDetailPageConfigPageVo> searchHospitalAreaDetailPageConfig(SearchHospitalAreaDetailPageConfigReqDto request) {
        return ResponseEntity.ok(hospitalAreaDetailPageConfigService.searchHospitalAreaDetailPageConfig(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:searchHospitalAreaDetailPageCube")
    public ResponseEntity<HospitalAreaDetailPageCubePageVo> searchHospitalAreaDetailPageCube(SearchHospitalAreaDetailPageCubeReqDto request) {
        return ResponseEntity.ok(hospitalAreaDetailPageCubeService.searchHospitalAreaDetailPageCube(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:searchHospitalAreaDetailPageCubeModule")
    public ResponseEntity<HospitalAreaDetailPageCubeModulePageVo> searchHospitalAreaDetailPageCubeModule(SearchHospitalAreaDetailPageCubeModuleReqDto request) {
        return ResponseEntity.ok(hospitalAreaDetailPageCubeModuleService.searchHospitalAreaDetailPageCubeModule(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:searchHospitalAreaDetailPageSubNaviModule")
    public ResponseEntity<HospitalAreaDetailPageSubNaviModulePageVo> searchHospitalAreaDetailPageSubNaviModule(SearchHospitalAreaDetailPageSubNaviModuleReqDto request) {
        return ResponseEntity.ok(hospitalAreaDetailPageSubNaviModuleService.searchHospitalAreaDetailPageSubNaviModule(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:searchHospitalAreaDetailPageTab")
    public ResponseEntity<HospitalAreaDetailPageTabPageVo> searchHospitalAreaDetailPageTab(SearchHospitalAreaDetailPageTabReqDto request) {
        return ResponseEntity.ok(hospitalAreaDetailPageTabService.searchHospitalAreaDetailPageTab(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:updateHospitalAreaDetailPageConfig")
    public ResponseEntity<HospitalAreaDetailPageConfigVo> updateHospitalAreaDetailPageConfig(Long configId, UpdateHospitalAreaDetailPageConfigReqDto request) {
        return ResponseEntity.ok(hospitalAreaDetailPageConfigService.updateHospitalAreaDetailPageConfig(configId, request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:updateHospitalAreaDetailPageCube")
    public ResponseEntity<HospitalAreaDetailPageCubeVo> updateHospitalAreaDetailPageCube(Long cubeId, UpdateHospitalAreaDetailPageCubeReqDto request) {
        return ResponseEntity.ok(hospitalAreaDetailPageCubeService.updateHospitalAreaDetailPageCube(cubeId, request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:updateHospitalAreaDetailPageCubeModule")
    public ResponseEntity<HospitalAreaDetailPageCubeModuleVo> updateHospitalAreaDetailPageCubeModule(Long cubeModuleId, UpdateHospitalAreaDetailPageCubeModuleReqDto request) {
        return ResponseEntity.ok(hospitalAreaDetailPageCubeModuleService.updateHospitalAreaDetailPageCubeModule(cubeModuleId, request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:updateHospitalAreaDetailPageNavigator")
    public ResponseEntity<HospitalAreaDetailPageNavigatorVo> updateHospitalAreaDetailPageNavigator(Long hospitalAreaId, UpdateHospitalAreaDetailPageNavigatorReqDto request) {
        if (CollectionUtils.isEmpty(request.getNaviInfo())) {
            throw new BizException(HttpStatus.BAD_REQUEST, "导航信息不能为空");
        }
        return ResponseEntity.ok(hospitalAreaDetailPageNavigatorService.updateHospitalAreaDetailPageNavigator(hospitalAreaId, request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:updateHospitalAreaDetailPageSubNaviModule")
    public ResponseEntity<HospitalAreaDetailPageSubNaviModuleVo> updateHospitalAreaDetailPageSubNaviModule(Long moduleId, UpdateHospitalAreaDetailPageSubNaviModuleReqDto request) {
        return ResponseEntity.ok(hospitalAreaDetailPageSubNaviModuleService.updateHospitalAreaDetailPageSubNaviModule(moduleId, request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:detail:updateHospitalAreaDetailPageTab")
    public ResponseEntity<HospitalAreaDetailPageTabVo> updateHospitalAreaDetailPageTab(Long tabId, UpdateHospitalAreaDetailPageTabReqDto updateHospitalDetailPageTabReqDto) {
        return ResponseEntity.ok(hospitalAreaDetailPageTabService.updateHospitalAreaDetailPageTab(tabId, updateHospitalDetailPageTabReqDto));
    }
}
