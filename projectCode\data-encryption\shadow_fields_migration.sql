-- =====================================================
-- 数据加密影子字段新增SQL脚本
-- 根据《数据安全法》《个人信息保护法》整改要求
-- 用于渐进式加密迁移策略
-- 数据库：MySQL
-- 创建时间：2024-06-24
-- =====================================================

-- =====================================================
-- 1. t_tenant_user 表 - 租户用户表
-- =====================================================

-- 新增身份证号加密字段（核心数据）
ALTER TABLE t_tenant_user 
ADD COLUMN id_card_no_encrypted VARCHAR(1000) COMMENT '身份证号加密字段（影子字段）' AFTER id_card_no;

-- 新增手机号加密字段（重要数据）
ALTER TABLE t_tenant_user 
ADD COLUMN phone_number_encrypted VARCHAR(1000) COMMENT '手机号码加密字段（影子字段）' AFTER phone_number;

-- 新增用户名加密字段（重要数据）
ALTER TABLE t_tenant_user 
ADD COLUMN name_encrypted VARCHAR(1000) COMMENT '用户名加密字段（影子字段）' AFTER name;

-- 为影子字段添加索引（可选，根据查询需求）
-- CREATE INDEX idx_tenant_user_id_card_encrypted ON t_tenant_user(id_card_no_encrypted(255));
-- CREATE INDEX idx_tenant_user_phone_encrypted ON t_tenant_user(phone_number_encrypted(255));

-- =====================================================
-- 2. t_recharge_record 表 - 充值记录表
-- =====================================================

-- 新增就诊人姓名加密字段（重要数据）
ALTER TABLE t_recharge_record 
ADD COLUMN patient_name_encrypted VARCHAR(1000) COMMENT '就诊人姓名加密字段（影子字段）' AFTER patient_name;

-- 新增就诊卡号加密字段（核心数据）
ALTER TABLE t_recharge_record 
ADD COLUMN jz_card_encrypted VARCHAR(1000) COMMENT '就诊卡号加密字段（影子字段）' AFTER jz_card;

-- 为影子字段添加索引（可选，根据查询需求）
-- CREATE INDEX idx_recharge_patient_name_encrypted ON t_recharge_record(patient_name_encrypted(255));
-- CREATE INDEX idx_recharge_jz_card_encrypted ON t_recharge_record(jz_card_encrypted(255));

-- =====================================================
-- 3. t_tenant 表 - 租户表
-- =====================================================

-- 新增联系人手机号加密字段（重要数据）
ALTER TABLE t_tenant 
ADD COLUMN contact_phone_number_encrypted VARCHAR(1000) COMMENT '联系人手机号加密字段（影子字段）' AFTER contact_phone_number;

-- 新增联系人邮箱加密字段（重要数据）
ALTER TABLE t_tenant 
ADD COLUMN contact_email_encrypted VARCHAR(1000) COMMENT '联系人邮箱加密字段（影子字段）' AFTER contact_email;

-- 为影子字段添加索引（可选，根据查询需求）
-- CREATE INDEX idx_tenant_contact_phone_encrypted ON t_tenant(contact_phone_number_encrypted(255));
-- CREATE INDEX idx_tenant_contact_email_encrypted ON t_tenant(contact_email_encrypted(255));

-- =====================================================
-- 4. 数据迁移状态跟踪表（可选）
-- =====================================================

-- 创建加密迁移状态跟踪表
CREATE TABLE IF NOT EXISTS t_encryption_migration_status (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    table_name VARCHAR(100) NOT NULL COMMENT '表名',
    field_name VARCHAR(100) NOT NULL COMMENT '字段名',
    migration_status ENUM('NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'FAILED') DEFAULT 'NOT_STARTED' COMMENT '迁移状态',
    total_records BIGINT DEFAULT 0 COMMENT '总记录数',
    migrated_records BIGINT DEFAULT 0 COMMENT '已迁移记录数',
    failed_records BIGINT DEFAULT 0 COMMENT '失败记录数',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    error_message TEXT COMMENT '错误信息',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_table_field (table_name, field_name)
) COMMENT='加密迁移状态跟踪表';

-- 初始化迁移状态记录
INSERT INTO t_encryption_migration_status (table_name, field_name) VALUES
('t_tenant_user', 'id_card_no'),
('t_tenant_user', 'phone_number'),
('t_tenant_user', 'name'),
('t_recharge_record', 'patient_name'),
('t_recharge_record', 'jz_card'),
('t_tenant', 'contact_phone_number'),
('t_tenant', 'contact_email')
ON DUPLICATE KEY UPDATE updated_time = CURRENT_TIMESTAMP;

-- =====================================================
-- 5. 验证SQL语句
-- =====================================================

-- 验证影子字段是否创建成功
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    COLUMN_TYPE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME IN ('t_tenant_user', 't_recharge_record', 't_tenant')
  AND COLUMN_NAME LIKE '%_encrypted'
ORDER BY TABLE_NAME, ORDINAL_POSITION;

-- 检查表结构
-- DESCRIBE t_tenant_user;
-- DESCRIBE t_recharge_record;
-- DESCRIBE t_tenant;

-- =====================================================
-- 6. 回滚SQL语句（紧急情况使用）
-- =====================================================

/*
-- 如需回滚，请执行以下语句：

-- 删除 t_tenant_user 表的影子字段
ALTER TABLE t_tenant_user DROP COLUMN IF EXISTS id_card_no_encrypted;
ALTER TABLE t_tenant_user DROP COLUMN IF EXISTS phone_number_encrypted;
ALTER TABLE t_tenant_user DROP COLUMN IF EXISTS name_encrypted;

-- 删除 t_recharge_record 表的影子字段
ALTER TABLE t_recharge_record DROP COLUMN IF EXISTS patient_name_encrypted;
ALTER TABLE t_recharge_record DROP COLUMN IF EXISTS jz_card_encrypted;

-- 删除 t_tenant 表的影子字段
ALTER TABLE t_tenant DROP COLUMN IF EXISTS contact_phone_number_encrypted;
ALTER TABLE t_tenant DROP COLUMN IF EXISTS contact_email_encrypted;

-- 删除迁移状态跟踪表
DROP TABLE IF EXISTS t_encryption_migration_status;
*/

-- =====================================================
-- 执行说明
-- =====================================================

/*
执行顺序：
1. 先执行影子字段新增语句（ALTER TABLE）
2. 根据需要创建索引（可选）
3. 创建迁移状态跟踪表
4. 执行验证SQL确认字段创建成功

注意事项：
1. 影子字段长度设置为1000，足够存储加密后的数据
2. 所有影子字段都允许NULL，避免影响现有业务
3. 建议在业务低峰期执行DDL操作
4. 执行前请备份相关表结构
5. 可根据实际查询需求决定是否创建索引

数据分类级别：
- 核心数据：id_card_no, jz_card（最高安全级别）
- 重要数据：phone_number, name, patient_name, contact_phone_number, contact_email
*/
