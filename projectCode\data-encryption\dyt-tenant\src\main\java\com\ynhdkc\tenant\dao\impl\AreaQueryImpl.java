package com.ynhdkc.tenant.dao.impl;

import backend.common.util.MybatisUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.ynhdkc.tenant.dao.AreaQuery;
import com.ynhdkc.tenant.dao.mapper.AreaMapper;
import com.ynhdkc.tenant.entity.Area;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-04 13:12
 */
@Repository
@RequiredArgsConstructor
public class AreaQueryImpl implements AreaQuery {
    private final AreaMapper areaMapper;

    @Override
    public Page<Area> pageQuery(AreaQueryOption option) {
        try (final Page<Area> page = PageMethod.startPage(option.getCurrentPage(), option.getPageSize())) {
            return page.doSelectPage(() -> areaMapper.selectByExample(Area.class, sql -> {
                sql.defGroup(condition -> {
                    if (null != option.getTenantId()) {
                        condition.andEqualTo(Area::getTenantId, option.getTenantId());
                    }
                    if (null != option.getHospitalId()) {
                        condition.andEqualTo(Area::getHospitalId, option.getHospitalId());
                    }
                    if (null != option.getHospitalAreaId()) {
                        condition.andEqualTo(Area::getHospitalAreaId, option.getHospitalAreaId());
                    }
                    if (null != option.getBuildingId()) {
                        condition.andEqualTo(Area::getBuildingId, option.getBuildingId());
                    }
                    if (null != option.getFloorId()) {
                        condition.andEqualTo(Area::getFloorId, option.getFloorId());
                    }
                    if (null != option.getName()) {
                        condition.andLike(Area::getName, MybatisUtil.likeBoth(option.getName()));
                    }
                    if (null != option.getStatus()) {
                        condition.andEqualTo(Area::getStatus, option.getStatus());
                    }

                    if (!CollectionUtils.isEmpty(option.getIncludeTenantIds())) {
                        condition.andIn(Area::getTenantId, option.getIncludeTenantIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getExcludeTenantIds())) {
                        condition.andNotIn(Area::getTenantId, option.getExcludeTenantIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getIncludeHospitalIds())) {
                        condition.andIn(Area::getHospitalId, option.getIncludeHospitalIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getExcludeHospitalIds())) {
                        condition.andNotIn(Area::getHospitalId, option.getExcludeHospitalIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getIncludeHospitalAreaIds())) {
                        condition.andIn(Area::getHospitalAreaId, option.getIncludeHospitalAreaIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getExcludeHospitalAreaIds())) {
                        condition.andNotIn(Area::getHospitalAreaId, option.getExcludeHospitalAreaIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getIncludeFloorIds())) {
                        condition.andIn(Area::getFloorId, option.getIncludeFloorIds());
                    }
                });
                sql.builder(builder -> builder
                        .orderByDesc(Area::getSort)
                        .orderByDesc(Area::getId));
            }));
        }
    }

    @Override
    public Area queryAreaById(Long areaId) {
        return areaMapper.selectByPrimaryKey(areaId);
    }

    @Override
    public List<Area> queryAreasByHospitalAreaId(Long hospitalAreaId) {
        return areaMapper.selectByExample(Area.class, sql -> {
            sql.defGroup(condition -> condition.andEqualTo(Area::getHospitalAreaId, hospitalAreaId));
            sql.builder(builder -> builder
                    .orderByDesc(Area::getSort)
                    .orderByDesc(Area::getId));
        });
    }
}
