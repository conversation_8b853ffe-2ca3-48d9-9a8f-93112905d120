package backend.common.densensitized.service;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DefaultDesensitizedPermissionService extends AbstractDesensitizedPermissionService {
    public DefaultDesensitizedPermissionService() {
        super();
    }

    @Override
    public boolean evaluate(String payload, String description) {
        log.info("Evaluate Desensitized Permission payload: {}, desc: {}", payload, description);
        return  true;
    }
}
