package com.ynhdkc.tenant.link.convert;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class Constants {

    public static final String DEFAULT_URL = "https://appv3.ynhdkc.com/home";

    /**
     * 上线 app v3 的医院编码
     * <p>
     * 第一批上线的医院编码
     * 872021 10011 10052 871010 871037
     * <p>
     * 4月17号上线非 HIS 医院编码
     * 852   872964  871935 872061
     * <p>
     * 4 月 18 号上线接 HIS 医院编码
     * 871008、871089、871667、871167、871567、871467、871367、871267、871092
     * <p>
     * 4 月 28 号上线接 HIS 医院编码
     * 871001
     * 871011
     * 871031
     * 871032
     * 871052
     * 871053
     * 871140
     * 871055
     * <p>
     * 5 月 9 号上线接 HIS 医院编码
     * 871045
     * 871096
     * <p>
     * 5 月 21 号上线接 HIS 医院编码
     * 871027  871028  871034  871090 871002  871003 871030 871038  871039 871041 871042 871249
     * <p>
     * 6 月 25 号上线接 HIS 医院编码
     * 871048
     * 871054
     * 871062
     * 871071
     * 871083
     * 871084
     * 871085
     * 871888
     * <p>
     * 7 月 9 号上线接 HIS 医院编码
     * 871093
     * 871057
     * 871058
     * 871094
     * 871095
     * 871139
     * 871256
     */
    public static final Set<String> APP_V3_HOSPITAL_CODE = new HashSet<>(Arrays.asList("872021", "10011", "10052", "871010", "871037", "852", "872964", "871935", "872061", "871008", "871089", "871667", "871167", "871567", "871467", "871367", "871267",
            "871092", "871001", "871011", "871031", "871032", "871052", "871053",
            "871140", "871055", "871045", "871096", "871027", "871028", "871034",
            "871090", "871002", "871003", "871030", "871038", "871039", "871041",
            "871042", "871249", "871048", "871054", "871062", "871071", "871083", "871084", "871085", "871888",
            "871093", "871057", "871058", "871094", "871095", "871139", "871256", "871044", "871023", "871900", "871333", "871232"));
}
