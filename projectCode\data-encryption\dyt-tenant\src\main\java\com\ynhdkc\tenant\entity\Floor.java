package com.ynhdkc.tenant.entity;

import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @since 2023/2/24 10:10:09
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "t_building_floor")
public class Floor extends BaseEntity {
    private Long tenantId;

    private Long hospitalId;

    private Long hospitalAreaId;

    private String hospitalCode;

    private Long buildingId;

    private String name;

    private String pictureUrls;

    private Integer sort;

    private Integer status;
}
