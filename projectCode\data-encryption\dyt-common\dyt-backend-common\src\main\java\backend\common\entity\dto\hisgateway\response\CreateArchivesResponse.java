package backend.common.entity.dto.hisgateway.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

@Data
public class CreateArchivesResponse {

    @JsonProperty("hospital_code")
    private String hospitalCode;

    private Boolean result;

    private Boolean success;

    private String name;

    @JsonProperty("id_card_number")
    private String idCardNumber;

    @JsonProperty("id_card_type")
    private Integer idCardType;

    private Long birthday;

    private Integer sex;

    private String telephone;

    private String nation;

    private Integer occupation;

    @JsonProperty("insure_card_type")
    private Integer insureCardType;

    @JsonProperty("insure_card_number")
    private String insureCardNumber;

    private String address;

    @JsonProperty("medical_record_number")
    private String medicalRecordNumber;

    @JsonProperty("medical_card_expansion") // 兼容老系统，替换老的 （患者编号 患者索引 省中真实患者就诊卡号）字段
    private String medicalCardExpansion;

    private String reason;

    private Map<String, Object> rawParameters;

    public void setResult(Boolean result) {
        this.result = result;
        this.success = result;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
        this.result = success;
    }
}
