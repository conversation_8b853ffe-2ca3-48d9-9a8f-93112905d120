package com.ynhdkc.tenant.client;

import com.ynhdkc.tenant.client.model.ApiOrderAppointment;
import com.ynhdkc.tenant.client.model.ApiQuickCheckOrderCountItem;
import com.ynhdkc.tenant.client.model.ApiQuickCheckOrderCountRequest;
import com.ynhdkc.tenant.client.model.RecentApiOrderAppointmentRespDto;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@FeignClient(value = "dyt-appointment-order", path = "/rpc/v1/appointment-order")
//@FeignClient(value = "dyt-appointment-order",url = "https://megacloud.ynhdkc.com", path = "/rpc/v1/appointment-order")
public interface AppointmentOrderClient {
    @GetMapping(path = "/appointment-orders/detail/by-user-id/{user_id}", produces = "application/json")
    ResponseEntity<ApiOrderAppointment> getOrderAppointmentByUserId(@PathVariable("user_id") Long userId);

    @GetMapping(path = "/appointment-orders/detail/user-id/{user_id}/hospital-area-code/{hospital_area_code}", produces = "application/json")
    RecentApiOrderAppointmentRespDto getOrderAppointmentByUserIdAndHospitalAreaId(@PathVariable("user_id") Long userId, @PathVariable("hospital_area_code") String hospitalAreaCode);

    @PostMapping(value = "/quick-check-order/select-order-count/")
    ResponseEntity<List<ApiQuickCheckOrderCountItem>> getQuickCheckOrderCount(@ApiParam(value = "", required = true) @Valid @RequestBody ApiQuickCheckOrderCountRequest body);

}
