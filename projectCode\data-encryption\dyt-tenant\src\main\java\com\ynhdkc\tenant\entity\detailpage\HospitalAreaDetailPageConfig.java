package com.ynhdkc.tenant.entity.detailpage;

import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @since 2023/5/24 14:49:25
 */
@Table(name = "t_hospital_detail_page_config")
@Data
@EqualsAndHashCode(callSuper = true)
public class HospitalAreaDetailPageConfig extends BaseEntity {
    private Long tenantId;
    private Long hospitalId;
    private Long hospitalAreaId;
    private String hospitalCode;
    private Boolean displayHospitalArea;
    private Boolean displayAddress;
    private Boolean displayLevel;
    private Boolean displayOpenSchedulingTime;
    private Boolean displayAppointNotice;
    private Boolean displayNotice;
    private Boolean displayFloorDistribution;
    private Boolean displayTab;
}
