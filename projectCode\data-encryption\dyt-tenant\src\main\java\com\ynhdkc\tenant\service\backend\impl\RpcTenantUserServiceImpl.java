package com.ynhdkc.tenant.service.backend.impl;

import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import backend.common.util.PasswordUtil;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.client.UserClient;
import com.ynhdkc.tenant.component.TenantUserLoginConfig;
import com.ynhdkc.tenant.dao.TenantUserQuery;
import com.ynhdkc.tenant.dao.TenantUserRepository;
import com.ynhdkc.tenant.entity.TenantUser;
import com.ynhdkc.tenant.enums.TenantUserStatus;
import com.ynhdkc.tenant.model.TenantUserDetailVo;
import com.ynhdkc.tenant.model.VerifyTenantUserPasswordDto;
import com.ynhdkc.tenant.service.backend.RpcTenantUserService;
import com.ynhdkc.tenant.service.backend.TenantUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-04 14:39
 */
@Service
@RequiredArgsConstructor
public class RpcTenantUserServiceImpl implements RpcTenantUserService {
    private final TenantUserQuery tenantUserQuery;
    private final TenantUserLoginConfig tenantUserLoginConfig;
    private final UserClient userClient;
    private final TenantUserRepository tenantUserRepository;
    private final RedisTemplate<String, Object> redisTemplate;
    private final String TENANT_USER_LOCK_KEY = "tenant:user:lock:%s";

    @Override
    public TenantUserDetailVo getDetail(Long userId) {
        TenantUser tenantUser = tenantUserQuery.queryByUserId(userId);
        if (tenantUser == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "用户不存在");
        }
        return TenantUserService.toTenantUserDetailVo(tenantUser);
    }

    @Override
    public TenantUserDetailVo getDetail(String userName, String userPhone) {
        if (null == userName && null == userPhone) {
            throw new BizException(HttpStatus.NOT_FOUND, "用户名或手机号为空");
        }
        TenantUserQuery.TenantUserQueryOption option = new TenantUserQuery.TenantUserQueryOption(1, Integer.MAX_VALUE)
                .setNameEqual(userName)
                .setPhoneNumber(userPhone);
        try (Page<TenantUser> tenantUserPage = tenantUserQuery.query(option)) {
            if (tenantUserPage.isEmpty()) {
                throw new BizException(HttpStatus.NOT_FOUND, "用户不存在");
            }
            if (tenantUserPage.size() > 1) {
                throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "用户查询异常");
            }
            /* 只有一个用户 */
            return TenantUserService.toTenantUserDetailVo(tenantUserPage.get(0));
        }
    }

    @Override
    public BaseOperationResponse passwordVerify(VerifyTenantUserPasswordDto request) {
        if (null == request.getName() && null == request.getPhoneNumber()) {
            throw new BizException(HttpStatus.NOT_FOUND, "用户名或手机号为空");
        }

        boolean passed;
        TenantUser tenantUser = getTenantUser(request);
        if (Boolean.TRUE.equals(redisTemplate.hasKey(String.format(TENANT_USER_LOCK_KEY, tenantUser.getId())))) {
            throw new BizException(HttpStatus.BAD_REQUEST, "密码错误次数过多，账户已被锁定，请稍后再试");
        }

        passed = PasswordUtil.isMatch(PasswordUtil.PasswordEncryptType.BCrypt, request.getPassword(), tenantUser.getPassword());
        if (!passed) {
            tenantUser.setDailyLoginRetries(tenantUser.getDailyLoginRetries() + 1);
            if (tenantUser.getDailyLoginRetries() % tenantUserLoginConfig.getMaxLoginAttempts() == 0) {
                tenantUser.setStatus(TenantUserStatus.LOCK.getValue());
                /* 锁定账户 ，防止撞库，每次锁定账户时间在上一次的基础上加倍*/
                Long lockDuration = tenantUserLoginConfig.getAccountLockDuration() * (tenantUser.getDailyLoginRetries() / tenantUserLoginConfig.getMaxLoginAttempts());
                redisTemplate.opsForValue().set(String.format(TENANT_USER_LOCK_KEY, tenantUser.getId()), "lock", lockDuration, java.util.concurrent.TimeUnit.MINUTES);
                tenantUserRepository.update(tenantUser);
                throw new BizException(HttpStatus.BAD_REQUEST, "密码错误次数过多，账户已被锁定，请稍后再试");
            }
            tenantUserRepository.update(tenantUser);
            throw new BizException(HttpStatus.BAD_REQUEST, "密码错误");
        }
        return new BaseOperationResponse("密码正确");
    }

    @Override
    public BaseOperationResponse passwordAndCodeVerify(VerifyTenantUserPasswordDto request) {
        TenantUser tenantUser = getTenantUser(request);
        userClient.verifyMessage(tenantUser.getPhoneNumber(), 7, 4, request.getVerificationCode());
        passwordVerify(request);
        return new BaseOperationResponse("密码正确");
    }

    public TenantUser getTenantUser(VerifyTenantUserPasswordDto request) {
        TenantUserQuery.TenantUserQueryOption option = new TenantUserQuery.TenantUserQueryOption(1, Integer.MAX_VALUE)
                .setNameEqual(request.getName())
                .setPhoneNumber(request.getPhoneNumber());
        try (Page<TenantUser> tenantUserPage = tenantUserQuery.query(option)) {
            if (tenantUserPage.isEmpty()) {
                throw new BizException(HttpStatus.NOT_FOUND, "用户不存在");
            }
            if (tenantUserPage.size() > 1) {
                throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "用户查询异常");
            }
            /* 只有一个用户 */
            return tenantUserPage.get(0);
        }
    }
}
