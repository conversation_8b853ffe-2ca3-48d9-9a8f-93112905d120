CREATE TABLE `t_user_doctor_subscription` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
  `doctor_id` BIGINT(20) NOT NULL COMMENT '医生ID',
  `doctor_code` VARCHAR(10) NOT NULL COMMENT '医生编码',
  `doctor_name` VARCHAR(50) NOT NULL COMMENT '医生名称',
  `department_id` BIGINT(20) NOT NULL COMMENT '科室ID',
  `department_code` VARCHAR(10) NOT NULL COMMENT '科室编码',
  `hospital_area_id` BIGINT(20) NOT NULL COMMENT '院区ID',
  `hospital_area_code` VARCHAR(10) NOT NULL COMMENT '院区编码',
  `hospital_area_name` VARCHAR(50) NOT NULL COMMENT '院区名称',
  `hospital_id` BIGINT(20) NOT NULL COMMENT '医院ID',
  `hospital_name` VARCHAR(255) NULL DEFAULT NULL COMMENT '医院名称',
  `notice_count` INT(11) NOT NULL DEFAULT 0 COMMENT '已通知次数',
  `status` TINYINT(2) NOT NULL DEFAULT 1 COMMENT '订阅状态（0：待通知，1：开始通知，2：通知结束，3：通知失败，-1：已取消）',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `cancel_time` DATETIME NULL DEFAULT NULL COMMENT '取消时间',
    `last_notified_time` DATETIME NULL DEFAULT NULL COMMENT '最近一次通知时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB
  AUTO_INCREMENT=1
  COMMENT='用户订阅医生记录'
  ROW_FORMAT=DYNAMIC;
