package com.ynhdkc.tenant.dao.impl;

import com.ynhdkc.tenant.dao.HospitalAreaRepository;
import com.ynhdkc.tenant.dao.mapper.*;
import com.ynhdkc.tenant.entity.*;
import com.ynhdkc.tenant.entity.setting.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-27 16:30
 */
@Repository
@RequiredArgsConstructor
public class HospitalAreaRepositoryImpl implements HospitalAreaRepository {
    private final HospitalMapper hospitalMapper;
    private final AddressMapper addressMapper;
    private final BuildingMapper buildingMapper;
    private final AreaMapper areaMapper;
    private final FloorMapper floorMapper;
    private final DepartmentMapper departmentMapper;
    private final DoctorMapper doctorMapper;
    private final TenantUserStructureMapper tenantUserStructureMapper;

    private final AppointmentRuleSettingMapper appointmentRuleSettingMapper;
    private final DiagnosisPaymentSettingMapper diagnosisPaymentSettingMapper;
    private final HospitalizationSettingMapper hospitalizationSettingMapper;
    private final PatientReportSettingMapper patientReportSettingMapper;
    private final PatientCardSettingMapper patientCardSettingMapper;
    private final CustomBusinessSettingMapper customBusinessSettingMapper;
    private final FunctionSettingMapper functionSettingMapper;

    @Override
    public void create(Hospital hospital) {
        hospital.setHospitalTypeTag(1);
        hospitalMapper.insertSelective(hospital);
        if (!StringUtils.hasText(hospital.getHospitalCode())) {
            hospital.setHospitalCode(hospital.getId().toString());
            hospitalMapper.updateByPrimaryKeySelective(hospital);
        }
    }

    @Override
    public void update(Hospital hospitalArea) {
        hospitalMapper.updateByPrimaryKeySelective(hospitalArea);
    }

    @Override
    public void delete(Long hospitalAreaId) {
        /* 获取所有需要删除的地址id */
        Set<Long> readyToDeleteAddress = new HashSet<>();
        Hospital hospital = hospitalMapper.selectByPrimaryKey(hospitalAreaId);
        if (null != hospital.getAddressId()) {
            readyToDeleteAddress.add(hospital.getAddressId());
        }
        buildingMapper.selectByExample2(Building.class, sql ->
                        sql.andEqualTo(Building::getHospitalId, hospital.getId()))
                .stream()
                .map(Building::getAddressId).forEach(readyToDeleteAddress::add);

        hospitalMapper.deleteByPrimaryKey(hospitalAreaId);
        buildingMapper.deleteByExample2(Building.class, sql -> sql.andEqualTo(Building::getHospitalId, hospitalAreaId));
        floorMapper.deleteByExample2(Floor.class, sql -> sql.andEqualTo(Floor::getHospitalAreaId, hospitalAreaId));
        areaMapper.deleteByExample2(Area.class, sql -> sql.andEqualTo(Area::getHospitalAreaId, hospitalAreaId));
        /* 级联删除 */
        departmentMapper.deleteByExample2(Department.class, sql -> sql.andEqualTo(Department::getHospitalAreaId, hospitalAreaId));
        doctorMapper.deleteByExample2(Doctor.class, sql -> sql.andEqualTo(Doctor::getHospitalAreaId, hospitalAreaId));
        tenantUserStructureMapper.deleteByExample2(TenantUserStructure.class, sql -> sql.andEqualTo(TenantUserStructure::getHospitalAreaId, hospitalAreaId));
        readyToDeleteAddress.forEach(addressMapper::deleteByPrimaryKey);

        appointmentRuleSettingMapper.deleteByExample2(AppointmentRuleSetting.class, sql -> sql.andEqualTo(AppointmentRuleSetting::getHospitalAreaId, hospitalAreaId));
        diagnosisPaymentSettingMapper.deleteByExample2(DiagnosisPaymentSetting.class, sql -> sql.andEqualTo(DiagnosisPaymentSetting::getHospitalAreaId, hospitalAreaId));
        hospitalizationSettingMapper.deleteByExample2(HospitalizationSetting.class, sql -> sql.andEqualTo(HospitalizationSetting::getHospitalAreaId, hospitalAreaId));
        patientReportSettingMapper.deleteByExample2(PatientReportSetting.class, sql -> sql.andEqualTo(PatientReportSetting::getHospitalAreaId, hospitalAreaId));
        patientCardSettingMapper.deleteByExample2(PatientCardSetting.class, sql -> sql.andEqualTo(PatientCardSetting::getHospitalAreaId, hospitalAreaId));
        customBusinessSettingMapper.deleteByExample2(CustomBusinessSetting.class, sql -> sql.andEqualTo(CustomBusinessSetting::getHospitalAreaId, hospitalAreaId));
        functionSettingMapper.deleteByExample2(FunctionSetting.class, sql -> sql.andEqualTo(FunctionSetting::getHospitalAreaId, hospitalAreaId));
    }
}
