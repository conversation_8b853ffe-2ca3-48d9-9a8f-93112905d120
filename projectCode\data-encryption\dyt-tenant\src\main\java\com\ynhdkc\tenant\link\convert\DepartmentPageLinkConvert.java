package com.ynhdkc.tenant.link.convert;

import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.service.customer.CustomerHospitalService;
import com.ynhdkc.tenant.util.UrlUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Component
@RequiredArgsConstructor
public class DepartmentPageLinkConvert implements ILinkConvert {

    private final CustomerHospitalService customerHospitalService;

    /**
     * 转换后的地址：<a href="https://appv3.ynhdkc.com/registration_depart_list_new?hospital_area_id=56&hospital_id=55&hos_code=871044">示例</a>
     *
     * @param source 源地址
     * @return 转换后的地址
     */
    @Override
    public String getConvertLink(String source) {
        final String hosCodeKey = "hos_code=";
        final String hosCode = UrlUtil.extractValue(source, hosCodeKey);
        if (hosCode == null) {
            return Constants.DEFAULT_URL;
        }

        Hospital hospital = customerHospitalService.queryHospitalById(hosCode).orElse(null);
        if (hospital == null) {
            return Constants.DEFAULT_URL;
        }

        final String urlPrefix = "https://appv3.ynhdkc.com/registration_depart_list_new";
        return urlPrefix + "?hospital_area_id=" + hospital.getId() + "&hospital_id=" + hospital.getParentId() + "&hos_code=" + hosCode;
    }

    @Override
    public boolean isSupport(String source) {
        List<String> urlPrefix = Arrays.asList("https://appv2.ynhdkc.com/registration_depart_list?hos_code=",
                "https://appv2.ynhdkc.com/registration_yunda_new_dep?hos_code=",
                "https://appv2.ynhdkc.com/register_kunhua_depart_list?hos_code=",
                "https://appv2.ynhdkc.com/register_gongren_depart_list?hos_code=",
                "https://appv2.ynhdkc.com/register_yanan_dep_list?hos_code=",
                "https://appv2.ynhdkc.com/register_shenfy_depart_list?hos_code=",
                "https://appv2.ynhdkc.com/register_shenfy_depart_list?hos_code=",
                "https://appv2.ynhdkc.com/register_fuwai_depart_list?hos_code=",
                "https://testapp.ynhdkc.com/registration_depart_list?hos_code=",
                "https://testapp.ynhdkc.com/registration_yunda_new_dep?hos_code=",
                "https://testapp.ynhdkc.com/register_kunhua_depart_list?hos_code=",
                "https://testapp.ynhdkc.com/register_gongren_depart_list?hos_code=",
                "https://testapp.ynhdkc.com/register_yanan_dep_list?hos_code=",
                "https://testapp.ynhdkc.com/register_shenfy_depart_list?hos_code=",
                "https://testapp.ynhdkc.com/register_shenfy_depart_list?hos_code=",
                "https://testapp.ynhdkc.com/register_fuwai_depart_list?hos_code=",
                "https://ttestapp.ynhdkc.com/registration_depart_list?hos_code=",
                "https://ttestapp.ynhdkc.com/registration_yunda_new_dep?hos_code=",
                "https://ttestapp.ynhdkc.com/register_kunhua_depart_list?hos_code=",
                "https://ttestapp.ynhdkc.com/register_gongren_depart_list?hos_code=",
                "https://ttestapp.ynhdkc.com/register_yanan_dep_list?hos_code=",
                "https://ttestapp.ynhdkc.com/register_shenfy_depart_list?hos_code=",
                "https://ttestapp.ynhdkc.com/register_shenfy_depart_list?hos_code=",
                "https://ttestapp.ynhdkc.com/register_fuwai_depart_list?hos_code=");
        return urlPrefix.stream().anyMatch(source::startsWith);
    }
}
