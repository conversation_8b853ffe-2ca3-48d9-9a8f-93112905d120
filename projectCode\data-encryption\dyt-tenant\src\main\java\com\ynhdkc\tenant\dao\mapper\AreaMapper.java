package com.ynhdkc.tenant.dao.mapper;

import backend.common.util.MybatisUtil;
import com.ynhdkc.tenant.entity.Area;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/2/24 11:06:23
 */
@Mapper
public interface AreaMapper extends BaseMapper<Area> {
    default void selectByCondition(Long floorId, String name, Integer status) {
        selectByExample(Area.class, helper -> {
            helper.defGroup(condition -> {
                if (floorId != null && floorId > 0) {
                    condition.andEqualTo(Area::getFloorId, floorId);
                }
                if (StringUtils.hasText(name)) {
                    condition.andLike(Area::getName, MybatisUtil.likeBoth(name));
                }
                if (status != null && status >= 0) {
                    condition.andEqualTo(Area::getStatus, status);
                }
                helper.builder(q -> q.orderByDesc(Area::getCreateTime));
            });
        });
    }
}
