package backend.security;

import backend.security.service.impl.DesensitizationWhiteListUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

/**
 * <AUTHOR>
 */
@Configuration
public class DesensitizationConfig {

    @Bean
    public DesensitizationWhiteListUtils desensitizationWhiteListUtils(
            RedisTemplate<String, Object> redisTemplate) {
        return new DesensitizationWhiteListUtils(redisTemplate);
    }
}