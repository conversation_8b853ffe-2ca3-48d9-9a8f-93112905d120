package backend.common.entity.dto.hisgateway.enums;

public enum DepartmentType {

    NORMAL(1, "普通科室"),
    VIP(2, "VIP科室"),
    MULTI_LEVEL(3, "多级科室列表"),
    MULTI_LIST(4, "多级科室多个列表"),
    SUB_DEPARTMENT(5, "子科室"),
    UNKNOWN(-1, "未知");

    private final String value;

    private final Integer code;

    DepartmentType(Integer code, String value) {
        this.value = value;
        this.code = code;
    }

    public static DepartmentType getFromCode(int code) {
        for (DepartmentType t : DepartmentType.values()) {
            if (t.getCode().equals(code)) {
                return t;
            }
        }
        return UNKNOWN;
    }

    public String getValue() {
        return value;
    }

    public Integer getCode() {
        return code;
    }
}
