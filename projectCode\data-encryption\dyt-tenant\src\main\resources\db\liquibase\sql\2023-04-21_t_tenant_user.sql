create table t_tenant_user
(
    id                  bigint auto_increment comment '全局唯一标识'
        primary key,
    name     varchar(30) not null comment '租户帐号名',
    nickname varchar(20) null comment '用户名',
    phone_number        varchar(13)                              null comment '用户手机号码',
    email               varchar(75)                              null comment '邮箱',
    gender              int         default 0                    not null comment '0:未知；1:男；2:女；',
    id_card_no          varchar(20)                              null comment '用户身份证号',
    password            varchar(60)                              not null comment '登录密码',
    register_platform   int         default 0                    not null comment '注册平台 0:未知 1:PC',
    login_ip            int         default 0                    null comment '登录 IP',
    daily_login_retries bigint      default 0                    null comment '当天登录重试次数',
    status              int         default 0                    not null comment '状态：0:正常，1:冻结',
    admin               tinyint     default 0                    null comment '是否是超级管理员，0:不是，1：是',
    head_image_url      varchar(300)                             null comment '头像地址',
    create_time         datetime(3) default CURRENT_TIMESTAMP(3) not null,
    update_time         datetime(3)                              null on update CURRENT_TIMESTAMP(3),
    constraint uidx_id_card_no
        unique (id_card_no)
)
    comment '租户用户表，B 端用户';

create unique index idx_name
    on t_tenant_user (name);

create unique index idx_phone_number
    on t_tenant_user (phone_number);

create unique index idx_email
    on t_tenant_user (email);

insert into t_tenant_user
    (id, name, phone_number, email, password, admin)
VALUES (1, 'super_admin', '13333333333', '<EMAIL>', '$2a$10$1BPqCovBDFFcLUkii1ElrOn1.Fmb2n/0Js27Vi25M3RyPpA7FbW3W', 1),
       (2, '云南省第一人民医院管理员', '13333333332', '<EMAIL>',
        '$2a$10$1BPqCovBDFFcLUkii1ElrOn1.Fmb2n/0Js27Vi25M3RyPpA7FbW3W', 0),
       (3, '云南省第二人民医院管理员', '13333333331', '<EMAIL>',
        '$2a$10$1BPqCovBDFFcLUkii1ElrOn1.Fmb2n/0Js27Vi25M3RyPpA7FbW3W', 0);

