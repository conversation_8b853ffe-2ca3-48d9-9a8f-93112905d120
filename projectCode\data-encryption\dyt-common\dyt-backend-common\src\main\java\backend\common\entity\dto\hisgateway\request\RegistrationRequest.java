package backend.common.entity.dto.hisgateway.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class RegistrationRequest {

    @JsonProperty("hospital_code")
    private String hospitalCode;

    @JsonProperty("department_code")
    private String departmentCode;

    @JsonProperty("doctor_code")
    private String doctorCode;

    @JsonProperty("doctor_name")
    private String doctorName;

    private String name;

    @JsonProperty("medical_record_number")
    private String medicalRecordNumber;

    @JsonProperty("medical_card_expansion")
    private String medicalCardExpansion;

    @JsonProperty("id_card_number")
    private String idCardNumber;

    private String telephone;

    @JsonProperty("schedule_id")
    private String scheduleId;

    @JsonProperty("visiting_serial_number")
    private String visitingSerialNumber;

    @JsonProperty("visiting_room")
    private String visitingRoom;

    private Long time;

    @JsonProperty("visiting_date")
    private Long visitingDate;

    @JsonProperty("time_type")
    private Integer timeType;

    @JsonProperty("order_number")
    private String orderNumber;

    @JsonProperty("pay_type")
    private String payType;

    @JsonProperty("pay_time")
    private Long payTime;

    // 总费用
    private String amt;

    // his 锁号订单号
    @JsonProperty("lock_registration_number")
    private String lockRegistrationNumber;

    // 微信等支付机构订单号
    @JsonProperty("trade_serial_number")
    private String tradeSerialNumber;

    @JsonProperty("bank_order_number")
    private String bankOrderNumber;

    @JsonProperty("bank_card")
    private String bankCard;

    // 挂号费
    @JsonProperty("registration_amt")
    private double registrationAmt;

    // 诊疗费
    @JsonProperty("treat_amt")
    private double treatAmt;

    // 专家费
    @JsonProperty("expert_amt")
    private double expertAmt;

    // 开始时间
    @JsonProperty("start_time")
    private Long startTime;

    // 结束时间
    @JsonProperty("end_time")
    private Long endTime;

    // his 格式的开始时间
    @JsonProperty("start_time_text")
    private String startTimeText;

    // his格式的结束时间
    @JsonProperty("end_time_text")
    private String endTimeText;

    // 患者姓名
    @JsonProperty("patient_name")
    private String patientName;

    // 患者手机号
    @JsonProperty("patient_phone")
    private String patientPhone;

    // 患者出生年月日
    @JsonProperty("patient_birthday")
    private String patientBirthday;

    // 患者性别
    @JsonProperty("patient_sex")
    private Integer patientSex;

    // 证件类型
    @JsonProperty("id_card_type")
    private Integer idCardType;

    @JsonProperty("open_id")
    private String openId;

    // 患者地址
    @JsonProperty("patient_address")
    private String patientAddress;
}
