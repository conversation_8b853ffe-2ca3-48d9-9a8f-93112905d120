package com.ynhdkc.tenant.entity.constant;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-10 9:30
 */
public enum ReportSupportSearchDateRange {
    NO_LIMIT(0),
    DAY_OF_30(1),
    DAY_OF_90(2),
    DAY_OF_180(3),
    DAY_OF_365(4);

    private final int value;

    ReportSupportSearchDateRange(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static ReportSupportSearchDateRange of(Integer input) {
        if (null == input) {
            return null;
        }
        for (ReportSupportSearchDateRange value : ReportSupportSearchDateRange.values()) {
            if (value.getValue() == input) {
                return value;
            }
        }
        return null;
    }

    public static boolean contains(Integer input) {
        if (null == input) {
            return false;
        }
        for (ReportSupportSearchDateRange value : ReportSupportSearchDateRange.values()) {
            if (value.getValue() == input) {
                return true;
            }
        }
        return false;
    }
}
