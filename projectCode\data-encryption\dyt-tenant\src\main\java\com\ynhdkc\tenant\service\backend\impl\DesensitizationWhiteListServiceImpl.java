package com.ynhdkc.tenant.service.backend.impl;

import backend.common.exception.BizException;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.component.DesensitizationWhiteListComponent;
import com.ynhdkc.tenant.dao.DesensitizationWhiteListQuery;
import com.ynhdkc.tenant.dao.DesensitizationWhiteListRepository;
import com.ynhdkc.tenant.entity.DesensitizationWhiteList;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.DesensitizationWhiteListService;
import com.ynhdkc.tenant.service.backend.TenantService;
import com.ynhdkc.tenant.service.backend.TenantUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class DesensitizationWhiteListServiceImpl implements DesensitizationWhiteListService {

    private final DesensitizationWhiteListRepository desensitizationWhiteListRepository;
    private final DesensitizationWhiteListQuery desensitizationWhiteListQuery;
    private final TenantUserService tenantUserService;
    private final TenantService tenantService;
    private final DesensitizationWhiteListComponent desensitizationWhiteListComponent;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DesensitizationWhiteListVo create(WhiteListCreateReqDto dto) {
        TenantDetailVo tenant = tenantService.getDetail(dto.getTenantId());
        TenantUserDetailVo tenantUser = tenantUserService.getUserDetail(dto.getTenantUserId());
        DesensitizationWhiteList entity = new DesensitizationWhiteList();
        entity.setTenantId(dto.getTenantId());
        entity.setTenantUserId(dto.getTenantUserId());
        entity.setTenantName(tenant.getName());
        entity.setTenantUserName(tenantUser.getNickname());
        desensitizationWhiteListRepository.create(entity);
        desensitizationWhiteListComponent.init();
        return entityToVo(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DesensitizationWhiteListVo update(Long id, WhiteListUpdateReqDto dto) {
        DesensitizationWhiteList entity = desensitizationWhiteListQuery.queryDesensitizationWhiteListById(id);
        TenantDetailVo tenant = tenantService.getDetail(dto.getTenantId());
        TenantUserDetailVo tenantUser = tenantUserService.getUserDetail(dto.getTenantUserId());
        if (null == entity) {
            throw new BizException(HttpStatus.NOT_FOUND, "未查询到记录");
        }
        if (dto.getTenantId() != null) {
            entity.setTenantId(dto.getTenantId());
            entity.setTenantName(tenant.getName());
        }
        if (dto.getTenantUserId() != null) {
            entity.setTenantUserId(dto.getTenantUserId());
            entity.setTenantUserName(tenantUser.getNickname());
        }

        desensitizationWhiteListRepository.update(entity);
        desensitizationWhiteListComponent.init();
        return entityToVo(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        desensitizationWhiteListRepository.delete(id);
        desensitizationWhiteListComponent.init();
    }

    @Override
    public DesensitizationWhiteListVo get(Long id) {
        return entityToVo(desensitizationWhiteListQuery.queryDesensitizationWhiteListById(id));
    }

    @Override
    public WhiteListPageVo findAll(Integer page, Integer size, String tenantName, String tenantUserName) {
        WhiteListPageReqDto whiteListPageReqDto = new WhiteListPageReqDto();
        whiteListPageReqDto.setCurrentPage(page);
        whiteListPageReqDto.setPageSize(size);
        whiteListPageReqDto.setTenantName(tenantName);
        whiteListPageReqDto.setTenantUserName(tenantUserName);
        Page<DesensitizationWhiteList> result = desensitizationWhiteListQuery.pageQueryDesensitizationWhiteList(whiteListPageReqDto);
        WhiteListPageVo pageResult = new WhiteListPageVo();
        pageResult.setCurrentPage(result.getPageNum());
        pageResult.setTotalSize(result.getTotal());
        pageResult.setPageSize(size);
        pageResult.setList(entityToVo(result.getResult()));
        return pageResult;

    }


    public DesensitizationWhiteListVo entityToVo(DesensitizationWhiteList entity) {
        DesensitizationWhiteListVo vo = new DesensitizationWhiteListVo();
        vo.setId(entity.getId());
        vo.setTenantId(entity.getTenantId());
        vo.setTenantName(entity.getTenantName());
        vo.setTenantUserId(entity.getTenantUserId());
        vo.setTenantUserName(entity.getTenantUserName());
        return vo;
    }

    public List<DesensitizationWhiteListVo> entityToVo(List<DesensitizationWhiteList> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return new ArrayList<>();
        }
        List<DesensitizationWhiteListVo> voList = new ArrayList<>();
        entityList.forEach(entity -> {
            voList.add(entityToVo(entity));
        });
        return voList;
    }
}