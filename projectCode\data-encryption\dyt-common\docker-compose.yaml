version: '3'

services:
  eureka:
    image: landykingdom/eureka-server:0.0.1
    ports:
      - "8761:8761"

  db:
    image: mysql:5.7
    ports:
      - "3306:3306"
    environment:
      TZ: Asia/Shanghai
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: backend_example
      MYSQL_USER: backend_platform
      MYSQL_PASSWORD: 8a4b041e

  zk:
    image: zookeeper
    ports:
      - "2181:2181"

  kafka:
    image: wurstmeister/kafka:2.13-2.7.1
    ports:
      - "9092:9092"
    environment:
      KAFKA_ZOOKEEPER_CONNECT: host.docker.internal:2181
      KAFKA_ADVERTISED_HOST_NAME: localhost
      KAFKA_ADVERTISED_PORT: 9092
    depends_on:
      - zk