<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ynhdkc.tenant.dao.mapper.ChannelSourceMapper">

    <update id="updateChannelInIds">
        update `t_channel_source` set `channel_code` = #{channels} where `id` in
        <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
    </update>
</mapper>