package com.ynhdkc.tenant.service.backend.impl;

import backend.common.domain.tenant.constant.AppointmentSystemDepends;
import backend.common.domain.user.constant.UserStatus;
import backend.common.entity.dto.hisgateway.response.DepartmentListItem;
import backend.common.entity.dto.notification.constant.ServiceConstant;
import backend.common.enums.HospitalCode;
import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import backend.common.util.MapUtil;
import backend.security.service.BackendTenantUserService;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.client.UserClient;
import com.ynhdkc.tenant.client.model.DepartmentList4MultiLevelResponse;
import com.ynhdkc.tenant.client.model.GongRenFirstLevelDepartment;
import com.ynhdkc.tenant.client.model.UserVo;
import com.ynhdkc.tenant.dao.*;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.entity.constant.DepartmentColumn;
import com.ynhdkc.tenant.entity.constant.DepartmentSourceEnum;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.DepartmentService;
import com.ynhdkc.tenant.service.backend.HospitalAreaService;
import com.ynhdkc.tenant.service.backend.HospitalService;
import com.ynhdkc.tenant.service.backend.TenantService;
import com.ynhdkc.tenant.tool.BackendDepartmentComparator;
import com.ynhdkc.tenant.tool.ColumnUpdateTool;
import com.ynhdkc.tenant.tool.convert.DepartmentConverter;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import com.ynhdkc.tenant.util.ChineseToEnglishUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.util.Pair;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-29 21:35
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DepartmentServiceImpl implements DepartmentService {
    private final DepartmentQuery departmentQuery;
    private final DepartmentRepository departmentRepository;
    private final HospitalQuery hospitalQuery;
    private final HospitalAreaQuery hospitalAreaQuery;

    private final TenantService tenantService;
    private final HospitalService hospitalService;
    private final HospitalAreaService hospitalAreaService;
    private final HospitalAreaSettingQuery hospitalAreaSettingQuery;

    private final BackendTenantUserService backendTenantUserService;

    private final PageVoConvert pageVoConvert;
    private final UserClient userClient;
    private final RedisTemplate<String, Object> redisTemplate;
    private final List<String> shengzhongHospitalCodeList = Arrays.asList("871333", "871232");

    private static TreeHospitalVo toTreeHospitalVo(Hospital entity) {
        return new TreeHospitalVo()
                .tenantId(entity.getTenantId())
                .id(entity.getId())
                .name(entity.getName())
                .status(entity.getStatus())
                .hospitalAreaList(new ArrayList<>());
    }

    private static TreeHospitalAreaVo toTreeHospitalAreaVo(Hospital hospitalArea) {
        return new TreeHospitalAreaVo()
                .tenantId(hospitalArea.getTenantId())
                .hospitalId(hospitalArea.getParentId())
                .id(hospitalArea.getId())
                .name(hospitalArea.getName())
                .status(hospitalArea.getStatus())
                .departmentList(new ArrayList<>());
    }

    private static TreeDepartmentVo toTreeDepartmentVo(Department department) {
        TreeDepartmentVo vo = new TreeDepartmentVo()
                .children(new ArrayList<>());
        vo.tenantId(department.getTenantId())
                .hospitalId(department.getHospitalId())
                .hospitalAreaId(department.getHospitalAreaId());
        vo.id(department.getId())
                .name(department.getName())
                .parentId(department.getParentId());
        vo.setEnabled(department.getEnabled());
        return vo;
    }

    private static String getGongRenThirdLevelDepartmentCode(Department parentDepartment, String departmentName) {
        String departmentCode;
        switch (departmentName) {
            case "普通门诊":
                departmentCode = parentDepartment.getThrdpartDepCode() + "-1";
                break;
            case "专家门诊":
                departmentCode = parentDepartment.getThrdpartDepCode() + "-2";
                break;
            case "特需门诊":
                departmentCode = parentDepartment.getThrdpartDepCode() + "-3";
                break;
            case "专病门诊":
                departmentCode = parentDepartment.getThrdpartDepCode() + "-4";
                break;
            case "名医门诊":
                departmentCode = parentDepartment.getThrdpartDepCode() + "-5";
                break;
            default:
                departmentCode = "";
                break;
        }
        return departmentCode;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DepartmentDetailVo create(DepartmentCreateReqDto request) {
        tenantService.getDetail(request.getTenantId());
        hospitalService.getDetail(request.getHospitalId());
        HospitalAreaDetailVo hospitalArea = hospitalAreaService.getDetail(request.getHospitalAreaId());
        if (null == hospitalArea) {
            throw new BizException(HttpStatus.NOT_FOUND, "院区不存在");
        }
//        checkYunDaDepartmentUnique(hospitalArea.getHospitalAreaCode(), request.getThrdpartDepCode(), null);

        Department department = DepartmentConverter.toDepartment(request);
        department.setHospitalCode(hospitalArea.getHospitalAreaCode());
        Optional<AppointmentRuleSetting> appointmentRuleSetting = hospitalAreaSettingQuery.queryAppointmentRuleSettingById(hospitalArea.getTenantId(), hospitalArea.getHospitalId(), hospitalArea.getId());
        if (null == department.getSystemDepends()) {
            appointmentRuleSetting.ifPresent(ruleSetting -> department.setSystemDepends(ruleSetting.getSystemDepends()));
        }
        departmentRepository.create(department);

        return DepartmentConverter.toDepartmentDetailVo(department);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DepartmentDetailVo update(Long departmentId, DepartmentUpdateReqDto request) {
        Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaById(request.getHospitalAreaId());

        if (null == hospitalArea) {
            throw new BizException(HttpStatus.NOT_FOUND, "院区不存在");
        }
        Department department = departmentQuery.queryDepartmentById(departmentId);
        if (null == department) {
            throw new BizException(HttpStatus.NOT_FOUND, "科室不存在");
        }
//        if (null != request.getThrdpartDepCode() && !request.getThrdpartDepCode().equals(department.getThrdpartDepCode())) {
//            checkYunDaDepartmentUnique(hospitalArea.getHospitalCode(), department.getThrdpartDepCode(), departmentId);
//        }


        DepartmentConverter.updateDepartment(department, request);
        departmentRepository.update(department);
        return DepartmentConverter.toDepartmentDetailVo(department);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Pair<BaseOperationResponse, Long> delete(Long departmentId) {
        Department department = departmentQuery.queryDepartmentById(departmentId);
        if (null == department) {
            throw new BizException(HttpStatus.NOT_FOUND, "科室不存在");
        }
        departmentRepository.delete(departmentId);
        return Pair.of(new BaseOperationResponse("删除成功"), departmentId);
    }

    @Override
    public DepartmentDetailVo getDetail(Long departmentId) {
        Department department = departmentQuery.queryDepartmentById(departmentId);
        if (null == department) {
            throw new BizException(HttpStatus.NOT_FOUND, "科室不存在");
        }

        /* 校验权限 */
        if (!backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.DepartmentId(departmentId))) {
            throw new BizException(HttpStatus.FORBIDDEN, "用户无访问该科室权限");
        }

        return DepartmentConverter.toDepartmentDetailVo(department);
    }

    @Override
    public DepartmentDetailVo rpcGetDetail(Long departmentId) {
        Department department = departmentQuery.queryDepartmentById(departmentId);
        if (null == department) {
            return null;
        }
        DepartmentDetailVo vo = DepartmentConverter.toDepartmentDetailVo(department);
        if (vo.getForbiddenDay().equals(-1) || vo.getAdvanceDay().equals(-1)) {
            Optional<AppointmentRuleSetting> ruleSetting = hospitalAreaSettingQuery.queryAppointmentRuleSettingById(department.getTenantId(), department.getHospitalId(), department.getHospitalAreaId());

            int initForbiddenDay = vo.getForbiddenDay();
            int initAdvanceDay = vo.getAdvanceDay();

            if (initForbiddenDay == -1 && ruleSetting.isPresent()) {
                initForbiddenDay = ruleSetting.get().getForbiddenDay();
            }
            if (initAdvanceDay == -1 && ruleSetting.isPresent()) {
                initAdvanceDay = ruleSetting.get().getAdvanceDay();
            }
            vo.setForbiddenDay(initForbiddenDay);
            vo.setAdvanceDay(initAdvanceDay);
        }
        return vo;
    }

    @Override
    public DepartmentPageVo query(DepartmentQueryReqDto request) {
        /* 未指定租户ID，需要判断是否为管理员 */
        boolean isSuperAdmin = backendTenantUserService.isSuperAdmin();
        if (null == request.getTenantId() && !isSuperAdmin) {
            throw new BizException(HttpStatus.FORBIDDEN, "必须指定租户ID");
        }
        DepartmentQuery.DepartmentQueryOption option = new DepartmentQuery.DepartmentQueryOption(request.getCurrentPage(), request.getPageSize());
        option.setTenantId(request.getTenantId());
        option.setHospitalId(request.getHospitalId());
        option.setHospitalAreaId(request.getHospitalAreaId());
        option.setId(request.getId());
        option.setName(request.getName());
        option.setThrdpartDepCode(request.getThrdpartDepCode());
        option.setCategory(request.getCategory());
        option.setEnabled(request.isEnabled());
        /* 约束科室范围 */
        if (!isSuperAdmin) {
            option.setIncludingIds(backendTenantUserService.getCurrentUserDepartmentReadRange(true));
        }
        try (Page<Department> departmentPage = departmentQuery.pageQueryDepartment(option)) {
            return pageVoConvert.toPageVo(departmentPage, DepartmentPageVo.class, department -> {
                DepartmentVo vo = DepartmentConverter.toDepartmentVo(department);
                Hospital hospital = hospitalQuery.queryHospitalById(department.getHospitalId());
                if (null != hospital) {
                    vo.setHospitalName(hospital.getName());
                }
                Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaById(department.getHospitalAreaId());
                if (null != hospitalArea) {
                    vo.setHospitalAreaName(hospitalArea.getName());
                }
                return vo;
            });
        }
    }

    @Override
    public TableDepartmentQueryVo queryByTable(TableDepartmentQueryReqDto request) {
        if (!backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.TenantId(request.getTenantId()))) {
            throw new BizException(HttpStatus.FORBIDDEN, "用户权限不足");
        }
        List<TableHospitalVo> tableHospitalVos = new ArrayList<>();
        try (Page<Hospital> hospitalPage = hospitalQuery.pageQueryHospital(new HospitalQuery.HospitalQueryOption(1, Integer.MAX_VALUE)
                .setTenantId(request.getTenantId()))) {
            hospitalPage.forEach(hospital -> {
                List<HospitalAreaVo> hospitalAreaList;
                List<DepartmentVo> departmentList;
                try (Page<Hospital> hospitalAreaPage = hospitalAreaQuery.pageQueryHospitalArea(new HospitalAreaQuery.HospitalAreaQueryOption(1, Integer.MAX_VALUE)
                        .setHospitalId(hospital.getId()))) {
                    hospitalAreaList = hospitalAreaPage.stream().map(HospitalAreaService::toHospitalAreaVo).collect(Collectors.toList());
                }
                try (Page<Department> departmentPage = departmentQuery.pageQueryDepartment(new DepartmentQuery.DepartmentQueryOption(1, Integer.MAX_VALUE)
                        .setHospitalId(hospital.getId()))) {
                    departmentList = departmentPage.stream().map(DepartmentConverter::toDepartmentVo).collect(Collectors.toList());
                }
                tableHospitalVos.add(new TableHospitalVo()
                        .id(hospital.getId())
                        .name(hospital.getName())
                        .hospitalAreaList(hospitalAreaList)
                        .departmentList(departmentList));
            });
        }

        return new TableDepartmentQueryVo()
                ._list(tableHospitalVos);
    }

    @Override
    public TreeDepartmentQueryVo queryByTree(TreeDepartmentQueryReqDto request) {
        /* 未指定租户ID，需要判断是否为管理员 */
        try (Page<Department> departmentPage = departmentQuery.pageQueryDepartment(new DepartmentQuery.DepartmentQueryOption(1, Integer.MAX_VALUE)
                .setTenantId(request.getTenantId())
                .setHospitalId(request.getHospitalId())
                .setHospitalAreaId(request.getHospitalAreaId())
                .setName(request.getName()))
        ) {
            /* 查询出所有医院、院区 */
            Map<Long, TreeHospitalVo> hospitals = new HashMap<>();
            try (Page<Hospital> hospitalPage = hospitalQuery.pageQueryHospital(new HospitalQuery.HospitalQueryOption(1, Integer.MAX_VALUE)
                    .setTenantId(request.getTenantId())
                    .setId(request.getHospitalId()))) {
                hospitalPage.forEach(hospital -> hospitals.put(hospital.getId(), toTreeHospitalVo(hospital)));
            }
            Map<Long, TreeHospitalAreaVo> hospitalAreas = new HashMap<>();
            try (Page<Hospital> hospitalAreaPage = hospitalAreaQuery.pageQueryHospitalArea(new HospitalAreaQuery.HospitalAreaQueryOption(1, Integer.MAX_VALUE)
                    .setTenantId(request.getTenantId())
                    .setHospitalId(request.getHospitalId())
                    .setHospitalAreaId(request.getHospitalAreaId()))) {
                hospitalAreaPage.forEach(hospitalArea -> hospitalAreas.put(hospitalArea.getId(), toTreeHospitalAreaVo(hospitalArea)));
            }
            /* 转化为VO */
            Map<Long, TreeDepartmentVo> treeDepartmentVos = new HashMap<>();
            departmentPage.forEach(department -> {
                if (!treeDepartmentVos.containsKey(department.getId())) {
                    treeDepartmentVos.put(department.getId(), toTreeDepartmentVo(department));
                }
            });
            /* 找到根节点 */
            List<TreeDepartmentVo> rootDepartments = new ArrayList<>();
            treeDepartmentVos.forEach((id, vo) -> {
                if (vo.getParentId().equals(Department.NO_PARENT)) {
                    rootDepartments.add(vo);
                    return;
                }
                /* 科室匹配科室 */
                TreeDepartmentVo parent = treeDepartmentVos.get(vo.getParentId());
                if (null != parent) {
                    parent.addChildrenItem(vo);
                }
            });
            /* 科室匹配院区 */
            for (TreeDepartmentVo rootDepartment : rootDepartments) {
                TreeHospitalAreaVo hospitalArea = hospitalAreas.get(rootDepartment.getHospitalAreaId());
                if (null != hospitalArea) {
                    hospitalArea.addDepartmentListItem(rootDepartment);
                }
            }
            /* 院区匹配医院 */
            for (TreeHospitalAreaVo hospitalArea : hospitalAreas.values()) {
                TreeHospitalVo hospital = hospitals.get(hospitalArea.getHospitalId());
                if (null != hospital) {
                    hospital.addHospitalAreaListItem(hospitalArea);
                }
            }
            return new TreeDepartmentQueryVo()
                    .tenantId(request.getTenantId())
                    .hospitalList(new ArrayList<>(hospitals.values()));
        }
    }

    @Override
    public DepartmentsTreeSearchListVo queryTree(DepartmentsTreeSearchReqDto request) {
        List<Department> departments = departmentQuery.queryDepartmentsByHospitalAreaId(request.getHospitalAreaId());
        if (CollectionUtils.isEmpty(departments)) {
            return new DepartmentsTreeSearchListVo()
                    ._list(Collections.emptyList());
        }

        List<Department> departmentsFiltered = departments
                .stream()
                .filter(q -> null == request.getDepartmentName() || q.getName().contains(request.getDepartmentName()))
                .filter(q -> null == request.getThrdpartDepCode() || q.getThrdpartDepCode().contains(request.getThrdpartDepCode()))
                .filter(q -> CollectionUtils.isEmpty(request.getCategory()) || !Collections.disjoint(q.getCategoryStr(), request.getCategory()))
                .filter(q -> null == request.isEnabled() || q.getEnabled().equals(request.isEnabled()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(departmentsFiltered)) {
            return new DepartmentsTreeSearchListVo()
                    ._list(Collections.emptyList());
        }

        List<Department> departmentsAndParents = new ArrayList<>();
        for (Department department : departmentsFiltered) {
            List<Department> temp = getDepartmentAndParents(department, departments);
            addDepartmentsNotContains(departmentsAndParents, temp);
        }
        if (CollectionUtils.isEmpty(departmentsAndParents)) {
            return new DepartmentsTreeSearchListVo();
        }

        List<DepartmentsTreeSearchVo> vos = getDepartmentsTreeFromDepartments(departmentsAndParents, 0L);
        DepartmentsTreeSearchListVo vo = new DepartmentsTreeSearchListVo();
        vos.sort(new BackendDepartmentComparator());
        vo.setList(vos);
        vo.setLevelCount(calculateMaxDepth(vos));
        setCurrentDepthForEach(vo.getList(), 1);
        return vo;
    }

    @Override
    public DepartmentsTreeSearchListVo queryTree2(DepartmentsTreeSearchReqDto request) {
        boolean superAdmin = true;
        List<Department> departments = departmentQuery.queryDepartmentsByHospitalAreaId(request.getHospitalAreaId());
        if (CollectionUtils.isEmpty(departments)) {
            return new DepartmentsTreeSearchListVo()._list(Collections.emptyList());
        }

        List<Department> departmentsFiltered = departments
                .stream()
                .filter(q -> null == request.getDepartmentName() || q.getName().contains(request.getDepartmentName()))
                .filter(q -> null == request.getThrdpartDepCode() || q.getThrdpartDepCode().contains(request.getThrdpartDepCode()))
                .filter(q -> CollectionUtils.isEmpty(request.getCategory()) || !Collections.disjoint(q.getCategoryStr(), request.getCategory()))
                .filter(q -> null == request.isEnabled() || q.getEnabled().equals(request.isEnabled()))
                .filter(q -> null == request.getId() || q.getId().equals(request.getId()))
                .filter(q -> superAdmin)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(departmentsFiltered)) {
            return new DepartmentsTreeSearchListVo()._list(Collections.emptyList());
        }

        Map<Long, List<DepartmentsTreeSearchVo>> parentChildrenMap = departments.stream()
                .map(DepartmentConverter::toDepartmentsTreeSearchVo)
                .collect(Collectors.groupingBy(DepartmentsTreeSearchVo::getParentId));

        List<DepartmentsTreeSearchVo> treeSearchVos = departmentsFiltered.stream()
                .map(DepartmentConverter::toDepartmentsTreeSearchVo)
                .collect(Collectors.toList());
        fillChildren(treeSearchVos, parentChildrenMap);

        Map<Long, DepartmentsTreeSearchVo> treeSearchVoMap = MapUtil.convertToMap(treeSearchVos, DepartmentsTreeSearchVo::getId);
        Map<Long, DepartmentsTreeSearchVo> departmentMap = departments.stream()
                .map(DepartmentConverter::toDepartmentsTreeSearchVo)
                .collect(Collectors.toMap(DepartmentsTreeSearchVo::getId, department -> department, (a, b) -> b));
        treeSearchVos.forEach(vo -> fillParentVoUtilRoot(vo, departmentMap, treeSearchVoMap));
        buildTree(treeSearchVoMap);
        List<DepartmentsTreeSearchVo> vos = treeSearchVoMap.values().stream()
                .filter(vo -> vo.getParentId() == 0)
                .sorted(new BackendDepartmentComparator())
                .collect(Collectors.toList());
        DepartmentsTreeSearchListVo vo = new DepartmentsTreeSearchListVo();
        vo.setList(vos);
        vo.setLevelCount(calculateMaxDepth(vos));
        setCurrentDepthForEach(vo.getList(), 1);
        return vo;
    }

    @Override
    public DepartmentsTreeSearchPageVo queryPage(DepartmentsTreeSearchPageReqDto request) {
        return departmentQuery.queyrDepartPageByCondition(request);
    }

    private void fillChildren(List<DepartmentsTreeSearchVo> treeSearchVos, Map<Long, List<DepartmentsTreeSearchVo>> parentChildrenMap) {
        treeSearchVos.forEach(vo -> {
            List<DepartmentsTreeSearchVo> children = parentChildrenMap.get(vo.getId());
            if (CollectionUtils.isEmpty(children)) {
                vo.setChildrens(Collections.emptyList());
            } else {
                vo.setChildrens(children);
            }
            fillChildren(vo.getChildrens(), parentChildrenMap);
        });
    }

    private void fillParentVoUtilRoot(DepartmentsTreeSearchVo treeSearchVo, Map<Long, DepartmentsTreeSearchVo> departmentMap, Map<Long, DepartmentsTreeSearchVo> treeSearchVoMap) {
        if (treeSearchVo.getParentId() == null || treeSearchVo.getParentId() == 0) {
            return;
        }
        DepartmentsTreeSearchVo parentVo = departmentMap.get(treeSearchVo.getParentId());
        if (parentVo == null) {
            return;
        }
        treeSearchVoMap.put(parentVo.getId(), parentVo);
        fillParentVoUtilRoot(parentVo, departmentMap, treeSearchVoMap);
    }

    private void buildTree(Map<Long, DepartmentsTreeSearchVo> treeSearchVoMap) {
        Map<Long, List<DepartmentsTreeSearchVo>> parentChildrenMap = treeSearchVoMap.values().stream()
                .collect(Collectors.groupingBy(DepartmentsTreeSearchVo::getParentId));
        parentChildrenMap.forEach((parentId, children) -> {
            DepartmentsTreeSearchVo parent = treeSearchVoMap.get(parentId);
            if (parent == null) {
                return;
            }
            parent.setChildrens(children);
        });
    }

    private void setCurrentDepthForEach(List<DepartmentsTreeSearchVo> list, int depth) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.forEach(item -> {
            item.setLayer(depth);
            setCurrentDepthForEach(item.getChildrens(), depth + 1);
        });
    }

    @Override
    public Department getById(Long id) {
        return departmentQuery.queryDepartmentById(id);
    }

    @Override
    public List<DepartmentVo> rpcBatchGetDetail(RpcBatchGetByIdReqDto request) {
        List<Department> departments = departmentQuery.queryBy(request.getIds());
        return departments.stream().map(DepartmentConverter::toDepartmentVo).collect(Collectors.toList());
    }

    @Override
    public void saveDepartment(String hospitalCode, List<DepartmentListItem> departmentListItems) {
        if (CollectionUtils.isEmpty(departmentListItems)) {
            return;
        }

        Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaBy(hospitalCode);
        if (hospitalArea == null) {
            log.info("hospital is null when save department from his, hospitalCode: {}", hospitalCode);
            return;
        }

        AppointmentRuleSetting appointmentRuleSetting = hospitalAreaSettingQuery.queryAppointmentRuleSettingBy(hospitalArea.getId()).orElse(null);
        if (appointmentRuleSetting == null) {
            log.info("appointmentRuleSetting is null when save department from his, hospitalCode: {}", hospitalCode);
            return;
        }

        List<String> hospitalCodeList = Collections.singletonList(hospitalCode);
        Set<String> departmentCodeSet = extractDepartmentCode(departmentListItems);

        List<Department> existingDepartments = departmentQuery.queryDepartmentsIn(hospitalCodeList, departmentCodeSet);

//        log.info("save_dpt_1: {}", MessageUtil.object2JSONString(departmentListItems));
//        log.info("save_dpt_2: {}", MessageUtil.object2JSONString(hospitalArea));
//        log.info("save_dpt_3: {}", MessageUtil.object2JSONString(appointmentRuleSetting));
//        log.info("save_dpt_4: {}", MessageUtil.object2JSONString(existingDepartments));


        departmentListItems.forEach(departmentListItem -> recursionAddDepartmentFromDepartmentListItem(departmentListItem,
                hospitalArea,
                appointmentRuleSetting,
                0L,
                existingDepartments)
        );
    }

    @Override
    public DepartmentVo rpcGetDetailByCode(String hospitalAreaCode, String departmentCode) {
        Department department = departmentQuery.queryBy(hospitalAreaCode, departmentCode);
        if (department == null) {
            return null;
        }
        return DepartmentConverter.toDepartmentVo(department);
    }

    @Override
    public void createOrUpdateGongRenHospitalDepartmentInfo(List<DepartmentList4MultiLevelResponse.Result> result) {
        List<Department> departments = departmentQuery.queryBy(HospitalCode.KUNMING_MU_SECOND_AFFILIATED_HOSPITAL.getCode()).stream().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(departments)) {
            log.info("工人医院所有科室数据为空，请及时维护科室数据");
            return;
        }

        List<GongRenFirstLevelDepartment> firstLevelDepartments = result.stream().map(item -> {
            GongRenFirstLevelDepartment firstLevelDepartment = new GongRenFirstLevelDepartment();
            firstLevelDepartment.setDepartmentCode(item.getFirstDepartmentCode());
            firstLevelDepartment.setDepartmentName(item.getFirstDepartmentName());
            return firstLevelDepartment;
        }).distinct().collect(Collectors.toList());


        createOrUpdateSecondLevelDepartmentInfo(firstLevelDepartments, departments);
        createOrUpdateThirdLevelDepartmentInfo(firstLevelDepartments);
        createOrUpdateForthLevelDepartmentInfo(result);
    }

    @Override
    public Map<Long, Integer> queryGongRenDepartmentTreeLevel() {
        List<Department> departments = departmentQuery.queryBy(HospitalCode.KUNMING_MU_SECOND_AFFILIATED_HOSPITAL.getCode());
        if (CollectionUtils.isEmpty(departments)) {
            return Collections.emptyMap();
        }

        Map<Long, Integer> departmentTreeLevelMap = new HashMap<>();
        for (Department department : departments) {
            int level = calculateLevel(department, departments);
            departmentTreeLevelMap.put(department.getId(), level);
        }
        return departmentTreeLevelMap;
    }

    @Override
    public AppointNotifyConfigVo getAppointNotifyConfig(String hospitalCode, Long departmentId) {
        Object recipientObj = redisTemplate.opsForHash().get(ServiceConstant.HOSPITAL_DEPARTMENT_ADMIN_REDIS_KEY, hospitalCode + "_" + departmentId);
        if (recipientObj != null) {
            List<HospitalAdminRecipientVo> list = JSONUtil.toList(recipientObj.toString(), HospitalAdminRecipientVo.class);
            return new AppointNotifyConfigVo().adminList(list);
        }
        return null;
    }

    @Override
    public SetAppointNotifyConfigResponse setAppointNotifyConfig(String hospitalCode, Long departmentId, SetAppointNotifyConfigReqDto reqDto) {
        List<String> adminPhoneNumbers = reqDto.getAdminPhoneNumbers();
        Map<String, String> setResultMap = new HashMap<>();
        List<UserVo> userVos = adminPhoneNumbers.stream().map(phoneNumber -> {
            UserVo body = userClient.queryUserByPhoneNumber(phoneNumber).getBody();
            if (body != null && body.getStatus() == UserStatus.NORMAL.val()) {
                setResultMap.put(phoneNumber, "设置成功");
            } else {
                setResultMap.put(phoneNumber, "未找到该用户");
            }
            return body;
        }).collect(Collectors.toList());

        if (ObjectUtils.isEmpty(userVos)) {
            throw new BizException(HttpStatus.NOT_FOUND, "未找到该手机号码的用户");
        }
        List<HospitalAdminRecipientVo> recipientVos = userVos.stream().map(user -> {
                    HospitalAdminRecipientVo recipient = new HospitalAdminRecipientVo();
                    recipient.setUserId(user.getId());
                    recipient.setPhoneNumber(user.getPhoneNumber());
                    recipient.setWxMpOpenId(user.getWeChatProfile().getOpenId());
                    return recipient;
                })
                .collect(Collectors.toList());
        redisTemplate.opsForHash().put(ServiceConstant.HOSPITAL_DEPARTMENT_ADMIN_REDIS_KEY, hospitalCode + "_" + departmentId, JSONUtil.toJsonStr(recipientVos));
        return new SetAppointNotifyConfigResponse().resultMap(setResultMap);
    }

    private int calculateLevel(Department department, List<Department> departments) {
        if (department.getParentId().equals(Department.NO_PARENT)) {
            return 1;
        }
        Department parent = departments.stream().filter(q -> q.getId().equals(department.getParentId())).findFirst().orElse(null);
        if (parent == null) {
            return 1;
        }
        return calculateLevel(parent, departments) + 1;
    }

    private void createOrUpdateForthLevelDepartmentInfo(List<DepartmentList4MultiLevelResponse.Result> result) {
        List<Department> departments = departmentQuery.queryBy(HospitalCode.KUNMING_MU_SECOND_AFFILIATED_HOSPITAL.getCode());
        if (CollectionUtils.isEmpty(departments)) {
            return;
        }

        for (DepartmentList4MultiLevelResponse.Result item : result) {
            List<Department> department = departments.stream().filter(q -> q.getName().equals(item.getDepartmentName())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(department)) {
                Department secondLevelDepartment = departments.stream().filter(q -> q.getName().equals(item.getFirstDepartmentName())).findFirst().orElse(null);
                if (secondLevelDepartment == null) {
                    continue;
                }
                Department thirdLevelDepartment = departments.stream().filter(q -> q.getParentId().equals(secondLevelDepartment.getId())).filter(q -> q.getName().equals("专病门诊")).findFirst().orElse(null);
                if (thirdLevelDepartment == null) {
                    continue;
                }
                createGongRenDepartment(thirdLevelDepartment.getTenantId(), thirdLevelDepartment.getHospitalId(), thirdLevelDepartment.getHospitalAreaId(), thirdLevelDepartment.getHospitalCode(), thirdLevelDepartment.getId(), item.getDepartmentCode(), item.getDepartmentName());
            } else {
                for (Department temp : department) {
                    int level = calculateLevel(temp, departments);
                    if (level != 4) {
                        continue;
                    }
                    temp.setThrdpartDepCode(item.getDepartmentCode());
                    departmentRepository.update(temp);
                }
            }
        }
    }

    /**
     * 创建获取更新工人第二层科室数据，没有则新增，有则更新
     *
     * @param firstLevelDepartments 医院返回的第一级科室，但是在滇医通是是第二级科室
     * @param departments           滇医通工人医院的所有科室
     */
    private void createOrUpdateSecondLevelDepartmentInfo(List<GongRenFirstLevelDepartment> firstLevelDepartments, List<Department> departments) {
        for (GongRenFirstLevelDepartment firstLevelDepartment : firstLevelDepartments) {
            List<Department> department = departments.stream().filter(q -> q.getName().equals(firstLevelDepartment.getDepartmentName())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(department)) {
                Department temp = departments.get(0);
                createGongRenDepartment(temp.getTenantId(), temp.getHospitalId(), temp.getHospitalAreaId(), temp.getHospitalCode(), 0L, firstLevelDepartment.getDepartmentCode(), firstLevelDepartment.getDepartmentName());
            } else {
                for (Department temp : department) {
                    int level = calculateLevel(temp, departments);
                    if (level != 2) {
                        continue;
                    }
                    temp.setThrdpartDepCode(firstLevelDepartment.getDepartmentCode());
                    departmentRepository.update(temp);
                }
            }
        }
    }

    private void createGongRenDepartment(Long tenantId, Long hospitalId, Long hospitalAreaId, String hospitalCode, Long parentId, String departmentCode, String departmentName) {
        Department department = new Department();
        department.setTenantId(tenantId);
        department.setHospitalId(hospitalId);
        department.setHospitalAreaId(hospitalAreaId);
        department.setHospitalCode(hospitalCode);
        department.setThrdpartDepCode(departmentCode);
        department.setName(departmentName);
        department.setFirstLetter(ChineseToEnglishUtil.getFirstLetter(departmentName));
        department.setParentId(parentId);
        department.setSystemDepends(AppointmentSystemDepends.HIS.getCode());
        department.setEnabled(Boolean.FALSE);
        departmentRepository.create(department);
    }

    /**
     * 维护工人医院在滇医通的第三层数据，没有则新增，有则更新
     *
     * @param firstLevelDepartments 医院返回的第一级科室，但是在滇医通是是第二级科室
     */
    private void createOrUpdateThirdLevelDepartmentInfo(List<GongRenFirstLevelDepartment> firstLevelDepartments) {
        List<Department> departments = departmentQuery.queryBy(HospitalCode.KUNMING_MU_SECOND_AFFILIATED_HOSPITAL.getCode()).stream().filter(q -> q.getEnabled().equals(Boolean.TRUE)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(departments)) {
            return;
        }

        for (GongRenFirstLevelDepartment firstLevelDepartment : firstLevelDepartments) {
            List<Department> secondLevelDepartments = departments.stream().filter(q -> q.getName().equals(firstLevelDepartment.getDepartmentName())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(secondLevelDepartments)) {
                continue;
            }

            for (Department secondLevelDepartment : secondLevelDepartments) {
                int level = calculateLevel(secondLevelDepartment, departments);
                if (level != 2) {
                    continue;
                }

                List<Department> thirdLevelDepartments = departments.stream().filter(q -> q.getParentId().equals(secondLevelDepartment.getId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(thirdLevelDepartments)) {
                    createGongRenThirdLevelDepartment(secondLevelDepartment, null);
                } else {
                    List<String> thirdLevelDepartmentNames = Arrays.asList("普通门诊", "专家门诊", "特需门诊", "专病门诊", "名医门诊");
                    for (String thirdLevelDepartmentName : thirdLevelDepartmentNames) {
                        Department department = thirdLevelDepartments.stream().filter(q -> q.getName().equals(thirdLevelDepartmentName)).findFirst().orElse(null);
                        if (department == null) {
                            createGongRenThirdLevelDepartment(secondLevelDepartment, thirdLevelDepartmentName);
                        } else {
                            department.setThrdpartDepCode(getGongRenThirdLevelDepartmentCode(secondLevelDepartment, thirdLevelDepartmentName));
                            departmentRepository.update(department);
                        }
                    }
                }
            }
        }
    }

    private void createGongRenThirdLevelDepartment(Department parentDepartment, String departmentName) {
        if (!StringUtils.hasText(departmentName)) {
            createGongRenDepartment(parentDepartment.getTenantId(), parentDepartment.getHospitalId(), parentDepartment.getHospitalAreaId(), parentDepartment.getHospitalCode(), parentDepartment.getId(), getGongRenThirdLevelDepartmentCode(parentDepartment, "普通门诊"), "普通门诊");
            createGongRenDepartment(parentDepartment.getTenantId(), parentDepartment.getHospitalId(), parentDepartment.getHospitalAreaId(), parentDepartment.getHospitalCode(), parentDepartment.getId(), getGongRenThirdLevelDepartmentCode(parentDepartment, "专家门诊"), "专家门诊");
            createGongRenDepartment(parentDepartment.getTenantId(), parentDepartment.getHospitalId(), parentDepartment.getHospitalAreaId(), parentDepartment.getHospitalCode(), parentDepartment.getId(), getGongRenThirdLevelDepartmentCode(parentDepartment, "特需门诊"), "特需门诊");
            createGongRenDepartment(parentDepartment.getTenantId(), parentDepartment.getHospitalId(), parentDepartment.getHospitalAreaId(), parentDepartment.getHospitalCode(), parentDepartment.getId(), getGongRenThirdLevelDepartmentCode(parentDepartment, "专病门诊"), "专病门诊");
            createGongRenDepartment(parentDepartment.getTenantId(), parentDepartment.getHospitalId(), parentDepartment.getHospitalAreaId(), parentDepartment.getHospitalCode(), parentDepartment.getId(), getGongRenThirdLevelDepartmentCode(parentDepartment, "名医门诊"), "名医门诊");
        } else {
            createGongRenDepartment(parentDepartment.getTenantId(), parentDepartment.getHospitalId(), parentDepartment.getHospitalAreaId(), parentDepartment.getHospitalCode(), parentDepartment.getId(), getGongRenThirdLevelDepartmentCode(parentDepartment, departmentName), departmentName);
        }
    }

    private Set<String> extractDepartmentCode(List<DepartmentListItem> children) {
        Set<String> departmentCodes = children.stream().map(DepartmentListItem::getDepartmentCode).collect(Collectors.toSet());
        children.stream()
                .filter(child -> !CollectionUtils.isEmpty(child.getChildren()))
                .map(child -> extractDepartmentCode(child.getChildren()))
                .forEach(departmentCodes::addAll);
        return departmentCodes;
    }

    private void addDepartmentsNotContains(List<Department> departmentsAndParents, List<Department> temp) {
        for (Department department : temp) {
            if (!departmentsAndParents.contains(department)) {
                departmentsAndParents.add(department);
            }
        }
    }

    private void updateDepartmentFromDepartmentListItem(DepartmentListItem departmentListItem, Department departmentExistInDb, AppointmentRuleSetting appointmentRuleSetting) {
        ColumnUpdateTool columnUpdateTool = new ColumnUpdateTool(appointmentRuleSetting.getUpdateDepartmentExcludeColumns());
        if (columnUpdateTool.hasColumn(DepartmentColumn.PARENT_ID.getColumn())) {
            departmentExistInDb.setParentId(null);
        }
        if (!columnUpdateTool.hasColumn(DepartmentColumn.INTRODUCTION.getColumn()) && StringUtils.hasText(departmentListItem.getDepartmentAddress())) {
            departmentExistInDb.setIntroduction(departmentListItem.getDepartmentAddress());
        }
        if (!columnUpdateTool.hasColumn(DepartmentColumn.FIRST_LETTER.getColumn()) && StringUtils.hasText(departmentListItem.getFirstJP())) {
            departmentExistInDb.setFirstLetter(departmentListItem.getFirstJP());
        }
        if (!columnUpdateTool.hasColumn(DepartmentColumn.SORT.getColumn()) && Objects.nonNull(departmentListItem.getSort())) {
            departmentExistInDb.setSort(Integer.valueOf(String.valueOf(departmentListItem.getSort())));
        }


        if (!columnUpdateTool.hasColumn(DepartmentColumn.ADDRESS_INTRO.getColumn()) && StringUtils.hasText(departmentListItem.getDepartmentAddress())) {
            departmentExistInDb.setAddressIntro(departmentListItem.getDepartmentAddress());
        }
        if (!columnUpdateTool.hasColumn(DepartmentColumn.NAME.getColumn()) && StringUtils.hasText(departmentListItem.getDepartmentName())) {
            departmentExistInDb.setName(departmentListItem.getDepartmentName());
        }
        departmentExistInDb.setUpdateTime(new Date());
        tongrenDepartmentSync(departmentListItem, departmentExistInDb, departmentExistInDb.getHospitalCode());
        departmentRepository.update(departmentExistInDb);
    }

    private void recursionAddDepartmentFromDepartmentListItem(DepartmentListItem departmentListItem, Hospital hospital, AppointmentRuleSetting appointmentRuleSetting, Long parentId, List<Department> departmentsFromDb) {
        if (!StringUtils.hasText(hospital.getHospitalCode()) || !StringUtils.hasText(departmentListItem.getDepartmentCode())) {
            log.info("hospital code is null or department code is null");
            return;
        }

        Department departmentExistInDb;
        boolean dependOnHis = AppointmentSystemDepends.HIS.getCode().equals(appointmentRuleSetting.getSystemDepends());
        if (HospitalCode.KUNMING_MU_FIRST_AFFILIATED_HOSPITAL.getCode().equals(hospital.getHospitalCode())
                || HospitalCode.KUNMING_MU_FIRST_AFFILIATED_CG_CAMPUS_HOSPITAL.getCode().equals(hospital.getHospitalCode())
                || HospitalCode.KUNMING_FIRST_PEOPLE_HOSPITAL_SOUTH.getCode().equals(hospital.getHospitalCode())
                || HospitalCode.DISTRICT_DONGCHUAN_TCM_HOSPITAL.getCode().equals(hospital.getHospitalCode())) {
            departmentExistInDb = departmentsFromDb.stream()
                    .filter(q -> q.getHospitalCode().equals(hospital.getHospitalCode()) && q.getThrdpartDepCode().equals(departmentListItem.getDepartmentCode())
                            && q.getName().equals(departmentListItem.getDepartmentName())
                    )
                    .findFirst()
                    .orElse(null);
        } else {
            departmentExistInDb = departmentsFromDb.stream()
                    .filter(q -> q.getHospitalCode().equals(hospital.getHospitalCode()) && q.getThrdpartDepCode().equals(departmentListItem.getDepartmentCode()))
                    .findFirst()
                    .orElse(null);
        }

        if (departmentExistInDb == null) {
            parentId = addDepartmentFromDepartmentListItem(departmentListItem, hospital, dependOnHis, parentId, appointmentRuleSetting);
        } else {
            if (shengzhongHospitalCodeList.contains(hospital.getHospitalCode())) {
                departmentExistInDb.setParentId(parentId);
            }
            parentId = departmentExistInDb.getId();
            if (Boolean.TRUE.equals(appointmentRuleSetting.getUpdateDepartmentDependHis())) {
                updateDepartmentFromDepartmentListItem(departmentListItem, departmentExistInDb, appointmentRuleSetting);
            }
        }

        if (!CollectionUtils.isEmpty(departmentListItem.getChildren())) {
            for (DepartmentListItem child : departmentListItem.getChildren()) {
                recursionAddDepartmentFromDepartmentListItem(child, hospital, appointmentRuleSetting, parentId, departmentsFromDb);
            }
        }
    }

    private Long addDepartmentFromDepartmentListItem(DepartmentListItem departmentListItem, Hospital hospital, Boolean dependOnHis, Long parentId, AppointmentRuleSetting appointmentRuleSetting) {
        Department departmentDetail = new Department();
        departmentDetail.setTenantId(hospital.getTenantId());
        departmentDetail.setHospitalId(hospital.getParentId());
        departmentDetail.setHospitalAreaId(hospital.getId());
        departmentDetail.setHospitalCode(hospital.getHospitalCode());
        departmentDetail.setThrdpartDepCode(departmentListItem.getDepartmentCode());
        if (StringUtils.hasText(departmentListItem.getDepartmentName())) {
            departmentDetail.setName(departmentListItem.getDepartmentName());
        } else {
            departmentDetail.setName("未知科室");
        }
        departmentDetail.setAddressIntro(departmentListItem.getDepartmentAddress());
        departmentDetail.setFirstLetter(departmentListItem.getFirstJP());
        departmentDetail.setParentId(parentId);
        departmentDetail.setSource(DepartmentSourceEnum.HIS.getCode());
        departmentDetail.setSystemDepends(AppointmentSystemDepends.HIS.getCode());

        if (dependOnHis) {
            departmentDetail.setEnabled(appointmentRuleSetting.getHospitalDependHisEnabledDepartment());
        } else {
            departmentDetail.setEnabled(Boolean.FALSE);
        }

        if (departmentListItem.getSort() == null) {
            departmentDetail.setSort(0);
        } else {
            try {
                Integer sort = Integer.parseInt(String.valueOf(departmentListItem.getSort()));
                departmentDetail.setSort(sort);
            } catch (NumberFormatException ex) {
                departmentDetail.setSort(0);
                log.error("科室排序字段转换失败，科室信息：{}，排序字段：{}，当前科室排序字段设置为 0", departmentListItem, departmentListItem.getSort());
            }
        }
        //同仁医院特殊逻辑
        tongrenDepartmentSync(departmentListItem, departmentDetail, hospital.getHospitalCode());
        parentId = departmentRepository.create(departmentDetail);
        return parentId;
    }

    private void tongrenDepartmentSync(DepartmentListItem departmentListItem, Department department, String hospitalCode) {
        if (!hospitalCode.equals(HospitalCode.KUNMING_TONG_REN_HOSPITAL.getCode())) {
            return;
        }
        //同仁医院夜间门诊科室标识同步,dyt-his-gateway写死返回10,B端科室类型字典也为10
        if (StrUtil.isNotBlank(departmentListItem.getDepartmentTypeCode()) && departmentListItem.getDepartmentTypeCode().equals("10")) {
            String newCode = departmentListItem.getDepartmentTypeCode();
            String merged = StrUtil.isBlank(department.getCategory())
                    ? newCode
                    : Stream.concat(
                            Arrays.stream(department.getCategory().split(",")),
                            Stream.of(newCode)
                    )
                    .distinct()
                    .collect(Collectors.joining(","));
            department.setCategory(merged);
        }
    }

    private int calculateMaxDepth(List<DepartmentsTreeSearchVo> tree) {
        if (tree == null || CollectionUtils.isEmpty(tree)) {
            return 0;
        }

        int maxDepth = 0;
        for (DepartmentsTreeSearchVo department : tree) {
            int depth = calculateMaxDepth(department.getChildrens()) + 1;
            if (depth > maxDepth) {
                maxDepth = depth;
            }
        }

        return maxDepth;
    }

    private List<DepartmentsTreeSearchVo> getDepartmentsTreeFromDepartments(List<Department> departments, Long parentId) {
        List<Department> departmentsFilterByParentId = departments.stream().filter(department -> department.getParentId().equals(parentId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(departmentsFilterByParentId)) {
            return new ArrayList<>();
        }

        List<DepartmentsTreeSearchVo> vos = new ArrayList<>();

        for (Department department : departmentsFilterByParentId) {
            DepartmentsTreeSearchVo vo = DepartmentConverter.toDepartmentsTreeSearchVo(department);
            vos.add(vo);

            List<DepartmentsTreeSearchVo> children = getDepartmentsTreeFromDepartments(departments, department.getId());
            if (!CollectionUtils.isEmpty(children)) {
                vo.setChildrens(children);
            }
        }

        return vos;
    }

    private List<Department> getDepartmentAndParents(Department department, Collection<Department> allDepartments) {
        List<Department> departments = new ArrayList<>();
        departments.add(department);
        if (department.getParentId() != null && department.getParentId() != 0) {
            allDepartments.stream().filter(temp -> temp.getId().equals(department.getParentId())).findFirst().ifPresent(parentDepartment -> departments.addAll(getDepartmentAndParents(parentDepartment, allDepartments)));
        }
        return departments;
    }
}
