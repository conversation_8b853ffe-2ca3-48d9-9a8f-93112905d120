<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ynhdkc.tenant.dao.mapper.DoctorMapper">

    <!-- 定义 resultMap -->
    <resultMap id="DoctorResultMap" type="com.ynhdkc.tenant.entity.Doctor">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="hospital_id" property="hospitalId" />
        <result column="hospital_area_id" property="hospitalAreaId" />
        <result column="hospital_code" property="hospitalCode" />
        <result column="department_id" property="departmentId" />
        <result column="department_code" property="departmentCode" />
        <result column="thrdpart_doctor_code" property="thrdpartDoctorCode" />
        <result column="head_img_url" property="headImgUrl" />
        <result column="name" property="name" />
        <result column="email" property="email" />
        <result column="rank_dict_type" property="rankDictType" />
        <result column="rank_dict_value" property="rankDictValue" />
        <result column="rank_dict_label" property="rankDictLabel" />
        <result column="registration_level" property="registrationLevel" />
        <result column="honor" property="honor" />
        <result column="speciality" property="speciality" />
        <result column="category" property="category" />
        <result column="introduction" property="introduction" />
        <result column="appointment_rule_dict_type" property="appointmentRuleDictType" />
        <result column="appointment_rule_dict_label" property="appointmentRuleDictLabel" />
        <result column="appointment_rule" property="appointmentRule" />
        <result column="statement" property="statement" />
        <result column="gender" property="gender" />
        <result column="system_depends" property="systemDepends" />
        <result column="status" property="status" />
        <result column="user_id" property="userId" />
        <result column="sort" property="sort" />
        <result column="visiting_address" property="visitingAddress" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="visiting_introduction" property="visitingIntroduction" />
        <result column="appointment_notice" property="appointmentNotice" />
        <result column="payment_notice" property="paymentNotice" />
        <result column="judge_appointment_condition" property="judgeAppointmentCondition" />
        <result column="judge_appointment_rule" property="judgeAppointmentRule" />
        <result column="need_upload_resource" property="needUploadResource" />
        <result column="need_verify_resource" property="needVerifyResource" />
        <result column="success_notice_phones" property="successNoticePhones" />
        <result column="success_template_ids" property="successTemplateIds" />
        <result column="display" property="display" />
        <result column="forbidden_day" property="forbiddenDay" />
        <result column="advance_day" property="advanceDay" />
        <result column="source_activate_time" property="sourceActivateTime" />
        <result column="multi_department" property="multiDepartment" />
        <result column="need_divide_settlement" property="needDivideSettlement" />
        <result column="second_merchant_id" property="secondMerchantId" />
        <result column="settlement_rule_id" property="settlementRuleId" />
        <result column="display_department_name" property="displayDepartmentName" />
        <result column="shortening" property="shortening" />
        <result column="tags" property="tags" />
        <result column="update_from_his" property="updateFromHis" />
    </resultMap>

    <!-- 分页查询 -->
    <select id="selectDoctorWithCategory" resultMap="DoctorResultMap">
        SELECT *
        FROM t_doctor
        <where>
            <if test="option.tenantId != null">
                AND tenant_id = #{option.tenantId}
            </if>
            <if test="option.hospitalId != null">
                AND hospital_id = #{option.hospitalId}
            </if>
            <if test="option.hospitalAreaId != null">
                AND hospital_area_id = #{option.hospitalAreaId}
            </if>
            <if test="option.departmentId != null">
                AND department_id = #{option.departmentId}
            </if>
            <if test="option.id != null">
                AND id = #{option.id}
            </if>
            <if test="option.name != null and option.name != ''">
                AND name LIKE CONCAT('%', #{option.name}, '%')
            </if>
            <if test="option.thrdpartDoctorCode != null">
                AND thrdpart_doctor_code = #{option.thrdpartDoctorCode}
            </if>
            <if test="option.status != null">
                AND status = #{option.status}
            </if>
            <if test="option.startCreateTime != null and option.endCreateTime != null">
                AND create_time BETWEEN #{option.startCreateTime} AND #{option.endCreateTime}
            </if>
            <if test="option.userId != null">
                AND user_id = #{option.userId}
            </if>
            <if test="option.display != null">
                AND display = #{option.display}
            </if>
            <if test="option.tags != null and option.tags.size > 0">
                AND (
                <foreach collection="option.tags" item="tag" separator=" OR ">
                    FIND_IN_SET(#{tag}, tags) > 0
                </foreach>
                )
            </if>
            <if test="option.includeIds != null and option.includeIds.size > 0">
                AND id IN
                <foreach collection="option.includeIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="option.includeTenantIds != null and option.includeTenantIds.size > 0">
                AND tenant_id IN
                <foreach collection="option.includeTenantIds" item="tid" open="(" separator="," close=")">
                    #{tid}
                </foreach>
            </if>
            <if test="option.excludeTenantIds != null and option.excludeTenantIds.size > 0">
                AND tenant_id NOT IN
                <foreach collection="option.excludeTenantIds" item="tid" open="(" separator="," close=")">
                    #{tid}
                </foreach>
            </if>
            <if test="option.includeHospitalIds != null and option.includeHospitalIds.size > 0">
                AND hospital_id IN
                <foreach collection="option.includeHospitalIds" item="hid" open="(" separator="," close=")">
                    #{hid}
                </foreach>
            </if>
            <if test="option.excludeHospitalIds != null and option.excludeHospitalIds.size > 0">
                AND hospital_id NOT IN
                <foreach collection="option.excludeHospitalIds" item="hid" open="(" separator="," close=")">
                    #{hid}
                </foreach>
            </if>
            <if test="option.includeHospitalAreaIds != null and option.includeHospitalAreaIds.size > 0">
                AND hospital_area_id IN
                <foreach collection="option.includeHospitalAreaIds" item="haid" open="(" separator="," close=")">
                    #{haid}
                </foreach>
            </if>
            <if test="option.excludeHospitalAreaIds != null and option.excludeHospitalAreaIds.size > 0">
                AND hospital_area_id NOT IN
                <foreach collection="option.excludeHospitalAreaIds" item="haid" open="(" separator="," close=")">
                    #{haid}
                </foreach>
            </if>
            <if test="option.includeDepartmentIds != null and option.includeDepartmentIds.size > 0">
                AND department_id IN
                <foreach collection="option.includeDepartmentIds" item="did" open="(" separator="," close=")">
                    #{did}
                </foreach>
            </if>
            <if test="option.excludeDepartmentIds != null and option.excludeDepartmentIds.size > 0">
                AND department_id NOT IN
                <foreach collection="option.excludeDepartmentIds" item="did" open="(" separator="," close=")">
                    #{did}
                </foreach>
            </if>
            <if test="option.category != null and option.category.size > 0">
                AND (
                <foreach collection="option.category" item="cat" separator=" OR ">
                    FIND_IN_SET(#{cat}, category) > 0
                </foreach>
                )
            </if>
            <if test="option.excludeCategory != null and option.excludeCategory.size > 0">
                AND (
                <foreach collection="option.excludeCategory" item="cat" separator=" AND ">
                    FIND_IN_SET(#{cat}, category) = 0
                </foreach>
                )
            </if>
        </where>
        ORDER BY sort DESC, id ASC
    </select>

</mapper>