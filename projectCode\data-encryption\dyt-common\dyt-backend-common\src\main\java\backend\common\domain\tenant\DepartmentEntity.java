package backend.common.domain.tenant;

import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-28 10:11
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class DepartmentEntity extends BaseEntity {

    private Long tenantId;

    private Long hospitalId;

    private Long hospitalAreaId;

    private String hospitalCode;
}
