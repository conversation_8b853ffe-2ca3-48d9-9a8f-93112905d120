package com.ynhdkc.tenant.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class ApiOrderAppointment {

    @JsonProperty("appoint_channel")
    private Integer appointChannel;

    @JsonProperty("appoint_rule_setting")
    private AppointRuleSetting appointRuleSetting;

    @JsonProperty("appointment_rule")
    private String appointmentRule;

    @JsonProperty("cancel_channel")
    private Integer cancelChannel;

    @JsonProperty("cancel_status")
    private Integer cancelStatus;

    @JsonProperty("cancel_time")
    private String cancelTime;

    @JsonProperty("cancel_user")
    private String cancelUser;

    @JsonProperty("complex_status_text")
    private String complexStatusText;

    @JsonProperty("consultation_status")
    private Integer consultationStatus;

    @JsonProperty("consultation_status_text")
    private String consultationStatusText;

    @JsonProperty("create_time")
    private String createTime;

    @JsonProperty("current_time")
    private Long currentTime;

    @JsonProperty("deleted")
    private Integer deleted;

    @JsonProperty("department_code")
    private String departmentCode;

    @JsonProperty("department_id")
    private Long departmentId;

    @JsonProperty("department_name")
    private String departmentName;

    @JsonProperty("doctor_code")
    private String doctorCode;

    @JsonProperty("doctor_id")
    private Long doctorId;

    @JsonProperty("doctor_info")
    private DoctorInfo doctorInfo;

    @JsonProperty("doctor_level")
    private String doctorLevel;

    @JsonProperty("doctor_name")
    private String doctorName;

    @JsonProperty("end_time")
    private String endTime;

    @JsonProperty("ext_order_no")
    private String extOrderNo;

    @JsonProperty("ghf")
    private Integer ghf;

    @JsonProperty("hos_payed_no")
    private String hosPayedNo;

    @JsonProperty("hospital_area_code")
    private String hospitalAreaCode;

    @JsonProperty("hospital_area_id")
    private Long hospitalAreaId;

    @JsonProperty("hospital_area_name")
    private String hospitalAreaName;

    @JsonProperty("hospital_id")
    private Long hospitalId;

    @JsonProperty("hospital_name")
    private String hospitalName;

    @JsonProperty("id")
    private Long id;

    @JsonProperty("ip")
    private String ip;

    @JsonProperty("jz_address")
    private String jzAddress;

    @JsonProperty("jz_card")
    private String jzCard;

    @JsonProperty("mark")
    private String mark;

    @JsonProperty("message")
    private String message;

    @JsonProperty("order_id")
    private Long orderId;

    @JsonProperty("order_no")
    private String orderNo;

    @JsonProperty("order_type")
    private Integer orderType;

    @JsonProperty("patient_age")
    private Integer patientAge;

    @JsonProperty("patient_birthday")
    private String patientBirthday;

    @JsonProperty("patient_id")
    private Long patientId;

    @JsonProperty("patient_id_card")
    private String patientIdCard;

    @JsonProperty("patient_name")
    private String patientName;

    @JsonProperty("patient_phone")
    private String patientPhone;

    @JsonProperty("patient_sex")
    private Integer patientSex;

    @JsonProperty("pay_time")
    private String payTime;

    @JsonProperty("queue_sn")
    private String queueSn;

    @JsonProperty("reason")
    private String reason;

    @JsonProperty("sch_date")
    private String schDate;

    @JsonProperty("schedule_id")
    private String scheduleId;

    @JsonProperty("sign_in")
    private Integer signIn;

    @JsonProperty("start_time")
    private String startTime;

    @JsonProperty("status")
    private Integer status;

    @JsonProperty("system_depends")
    private Integer systemDepends;

    @JsonProperty("time_type")
    private Integer timeType;

    @JsonProperty("total_fee")
    private Integer totalFee;

    @JsonProperty("transaction_id")
    private String transactionId;

    @JsonProperty("update_time")
    private String updateTime;

    @JsonProperty("user_id")
    private Long userId;

    @JsonProperty("write_off_status")
    private Integer writeOffStatus;

    @JsonProperty("zjf")
    private Integer zjf;

    @JsonProperty("zlf")
    private Integer zlf;

    @Data
    public static class AppointRuleSetting {

        @JsonProperty("advance_day")
        private Integer advanceDay;

        @JsonProperty("all_department_background_color")
        private String allDepartmentBackgroundColor;

        @JsonProperty("appoint_today")
        private Boolean appointToday;

        @JsonProperty("appointment_guide")
        private String appointmentGuide;

        @JsonProperty("appointment_notify_contact")
        private String appointmentNotifyContact;

        @JsonProperty("appointment_result_notice")
        private String appointmentResultNotice;

        @JsonProperty("appointment_rule")
        private String appointmentRule;

        @JsonProperty("confirm_medical_insurance_card")
        private Boolean confirmMedicalInsuranceCard;

        @JsonProperty("department_level")
        private Integer departmentLevel;

        @JsonProperty("display_department_address")
        private Boolean displayDepartmentAddress;

        @JsonProperty("display_doctor_under_department")
        private Integer displayDoctorUnderDepartment;

        @JsonProperty("display_no")
        private Boolean displayNo;

        @JsonProperty("display_time")
        private String displayTime;

        @JsonProperty("display_visit_time")
        private Boolean displayVisitTime;

        @JsonProperty("function_id")
        private Long functionId;

        @JsonProperty("id")
        private Long id;

        @JsonProperty("need_partition_time")
        private Boolean needPartitionTime;

        @JsonProperty("need_payment")
        private Boolean needPayment;

        @JsonProperty("need_sign_in")
        private Boolean needSignIn;

        @JsonProperty("notice_template_id")
        private String noticeTemplateId;

        @JsonProperty("order_need_verify")
        private Boolean orderNeedVerify;

        @JsonProperty("payment_close_duration")
        private Integer paymentCloseDuration;

        @JsonProperty("refund_today")
        private Boolean refundToday;

        @JsonProperty("selected_payments")
        private List<String> selectedPayments;

        @JsonProperty("stop_appoint_time")
        private String stopAppointTime;

        @JsonProperty("stop_refund_time")
        private String stopRefundTime;

        @JsonProperty("support_cancel_appointment")
        private Boolean supportCancelAppointment;

        @JsonProperty("support_locking_appointment_no")
        private Boolean supportLockingAppointmentNo;

        @JsonProperty("system_depends")
        private Integer systemDepends;

        // Getters and setters
    }

    @Data
    public static class DoctorInfo {

        @JsonProperty("appointment_notice")
        private String appointmentNotice;

        @JsonProperty("appointment_rule_dict_label")
        private List<String> appointmentRuleDictLabel;

        @JsonProperty("appointment_rule_dict_type")
        private String appointmentRuleDictType;

        @JsonProperty("category")
        private List<Integer> category;

        @JsonProperty("create_time")
        private String createTime;

        @JsonProperty("department_id")
        private Long departmentId;

        @JsonProperty("department_name")
        private String departmentName;

        @JsonProperty("display")
        private Boolean display;

        @JsonProperty("email")
        private String email;

        @JsonProperty("head_img_url")
        private String headImgUrl;

        @JsonProperty("honor")
        private String honor;

        @JsonProperty("hospital_area_id")
        private Long hospitalAreaId;

        @JsonProperty("hospital_area_name")
        private String hospitalAreaName;

        @JsonProperty("hospital_code")
        private String hospitalCode;

        @JsonProperty("hospital_id")
        private Long hospitalId;

        @JsonProperty("hospital_name")
        private String hospitalName;

        @JsonProperty("id")
        private Long id;

        @JsonProperty("introduction")
        private String introduction;

        @JsonProperty("judge_appointment_condition")
        private Boolean judgeAppointmentCondition;

        @JsonProperty("judge_appointment_rule")
        private String judgeAppointmentRule;

        @JsonProperty("name")
        private String name;

        @JsonProperty("need_divide_settlement")
        private Boolean needDivideSettlement;

        @JsonProperty("need_upload_resource")
        private Boolean needUploadResource;

        @JsonProperty("need_verify_resource")
        private Boolean needVerifyResource;

        @JsonProperty("payment_notice")
        private String paymentNotice;

        @JsonProperty("rank_dict_label")
        private List<String> rankDictLabel;

        @JsonProperty("rank_dict_type")
        private String rankDictType;

        @JsonProperty("rank_dict_value")
        private List<String> rankDictValue;

        @JsonProperty("second_merchant_id")
        private Long secondMerchantId;

        @JsonProperty("settlement_rule_id")
        private Long settlementRuleId;

        @JsonProperty("sort")
        private Integer sort;

        @JsonProperty("speciality")
        private String speciality;

        @JsonProperty("statement")
        private String statement;

        @JsonProperty("status")
        private Integer status;

        @JsonProperty("success_notice_phones")
        private String successNoticePhones;

        @JsonProperty("success_template_ids")
        private String successTemplateIds;

        @JsonProperty("tenant_id")
        private Long tenantId;

        @JsonProperty("thrdpart_dep_code")
        private String thrdpartDepCode;

        @JsonProperty("thrdpart_doctor_code")
        private String thrdpartDoctorCode;

        @JsonProperty("user_id")
        private Long userId;

        @JsonProperty("visiting_address")
        private String visitingAddress;

        @JsonProperty("visiting_introduction")
        private String visitingIntroduction;
    }
}
