package com.ynhdkc.tenant.api.backend;

import backend.common.response.BaseOperationResponse;
import backend.security.method.BackendSecurityRequired;
import backend.security.oplog.DytSecurityRequired;
import com.ynhdkc.tenant.handler.RegionApi;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.RegionService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-07 15:21
 */
@Api(tags = "Region")
@RestController
@RequiredArgsConstructor
public class RegionController implements RegionApi {
    private final RegionService regionService;

    @BackendSecurityRequired(tenantManager = true)
    @DytSecurityRequired(needOpLog = true, value = "region:create")
    @Override
    public ResponseEntity<RegionVo> create(RegionCreateReqDto request) {
        return ResponseEntity.ok(regionService.create(request));
    }

    @BackendSecurityRequired(tenantManager = true)
    @DytSecurityRequired(needOpLog = true, value = "region:delete")
    @Override
    public ResponseEntity<BaseOperationResponse> delete(Long regionId) {
        return ResponseEntity.ok(regionService.delete(regionId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "region:query:page")
    public ResponseEntity<RegionPageVo> query(RegionQueryReqDto request) {
        return ResponseEntity.ok(regionService.query(request));
    }

    @BackendSecurityRequired(tenantManager = true)
    @DytSecurityRequired(needOpLog = true, value = "region:update")
    @Override
    public ResponseEntity<RegionVo> update(Long regionId, RegionUpdateReqDto request) {
        return ResponseEntity.ok(regionService.update(regionId, request));
    }
}
