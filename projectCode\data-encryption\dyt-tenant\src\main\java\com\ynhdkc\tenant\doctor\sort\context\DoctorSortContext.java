package com.ynhdkc.tenant.doctor.sort.context;

import com.ynhdkc.tenant.doctor.sort.impl.DefaultSortStrategy;
import com.ynhdkc.tenant.doctor.sort.strategy.DoctorSortStrategy;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.model.CustomerGroupedDoctorDetailVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class DoctorSortContext {

    private final List<DoctorSortStrategy> strategies;
    private final DefaultSortStrategy defaultSortStrategy;

    public void sortDoctors(List<Doctor> doctors, String hospitalAreaCode) {
        strategies.stream().filter(s -> s.supports(hospitalAreaCode)).findFirst().orElse(defaultSortStrategy).sort(doctors);
    }

    public void sortGroupVo(List<CustomerGroupedDoctorDetailVo> doctorGroupVos, String hospitalAreaCode) {
        strategies.stream().filter(s -> s.supports(hospitalAreaCode)).findFirst().orElse(defaultSortStrategy).sortGroupVo(doctorGroupVos);
    }
}
