package com.ynhdkc.tenant.dao.impl;

import com.ynhdkc.tenant.dao.TenantUserPasswordChangeLogMapper;
import com.ynhdkc.tenant.dao.TenantUserPasswordChangeLogRepository;
import com.ynhdkc.tenant.entity.TenantUserPasswordChangeLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-03 16:41
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class TenantUserPasswordChangeLogRepositoryImpl implements TenantUserPasswordChangeLogRepository {
    private final TenantUserPasswordChangeLogMapper tenantUserPasswordChangeLogMapper;

    @Override
    public void create(TenantUserPasswordChangeLog tenantUserPasswordChangeLog) {
        tenantUserPasswordChangeLogMapper.insert(tenantUserPasswordChangeLog);
    }

    @Override
    public void update(TenantUserPasswordChangeLog tenantUserPasswordChangeLog) {
        tenantUserPasswordChangeLogMapper.updateByPrimaryKeySelective(tenantUserPasswordChangeLog);
    }

    @Override
    public void delete(Long id) {

        tenantUserPasswordChangeLogMapper.deleteByPrimaryKey(id);
    }

}
