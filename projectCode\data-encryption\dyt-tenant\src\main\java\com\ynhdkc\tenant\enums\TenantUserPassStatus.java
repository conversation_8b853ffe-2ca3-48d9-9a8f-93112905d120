package com.ynhdkc.tenant.enums;

/**
 * <AUTHOR>
 * @date 2024/07/09/17:21
 */

public enum TenantUserPassStatus {
    NORMAL(0, "正常"),
    PASSWORD_STRENGTH_NOT_MATCH(1, "密码强度不符合要求"),
    PASSWORD_EXPIRED(2, "密码过期");

    private final int code;
    private final String desc;

    TenantUserPassStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
