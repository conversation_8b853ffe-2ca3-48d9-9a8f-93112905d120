package com.ynhdkc.tenant.api.backend;

import backend.common.response.BaseOperationResponse;
import backend.security.method.BackendSecurityRequired;
import backend.security.oplog.DytSecurityRequired;
import com.ynhdkc.tenant.handler.RecommendConfigApi;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.RecommendConfigService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Api(tags = "RecommendConfig")
@RequiredArgsConstructor
public class RecommendConfigController implements RecommendConfigApi {

    private final RecommendConfigService recommendConfigService;

    @Override
    @BackendSecurityRequired(tenantManager = true)
    @DytSecurityRequired(needOpLog = true, value = "recommend:config:create")
    public ResponseEntity<RecommendConfigVo> createRecommendConfig(CreateRecommendConfigReqDto request) {
        return ResponseEntity.ok(recommendConfigService.createRecommendConfig(request));
    }

    @Override
    @BackendSecurityRequired(tenantManager = true)
    @DytSecurityRequired(needOpLog = true, value = "recommend:config:delete")
    public ResponseEntity<BaseOperationResponse> deleteRecommendConfig(Long id) {
        return ResponseEntity.ok(recommendConfigService.deleteRecommendConfig(id));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "recommend:config:get")
    public ResponseEntity<RecommendConfigVo> getRecommendConfig(Long id) {
        return ResponseEntity.ok(recommendConfigService.getRecommendConfig(id));
    }

    @Override
    public ResponseEntity<List<String>> getRecommendConfigKeywords(Long id) {
        return ResponseEntity.ok(recommendConfigService.getRecommendConfigKeywords(id));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "recommend:config:query:page")
    public ResponseEntity<RecommendConfigPageVo> getRecommendConfigPage(GetRecommendConfigPageReqDto request) {
        return ResponseEntity.ok(recommendConfigService.getRecommendConfigPage(request));
    }

    @Override
    @BackendSecurityRequired(tenantManager = true)
    @DytSecurityRequired(needOpLog = true, value = "recommend:config:update")
    public ResponseEntity<RecommendConfigVo> updateRecommendConfig(Long id, UpdateRecommendConfigReqDto request) {
        return ResponseEntity.ok(recommendConfigService.updateRecommendConfig(id, request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "recommend:config:update:channel")
    public ResponseEntity<BaseOperationResponse> updateRecommendConfigChannel(UpdateRecommendConfigChannelReqDto request) {
        return ResponseEntity.ok(recommendConfigService.updateRecommendConfigChannel(request));
    }
}
