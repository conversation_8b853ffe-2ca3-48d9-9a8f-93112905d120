package com.ynhdkc.tenant.dao;

import backend.common.util.BaseQueryOption;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.entity.Building;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-04 10:25
 */
public interface BuildingQuery {
    Building queryBuildingById(Long buildingId);

    Page<Building> pageQuery(BuildingQueryOption option);

    List<Building> queryBuildingsByHospitalAreaId(Long hospitalAreaId);

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Accessors(chain = true)
    class BuildingQueryOption extends BaseQueryOption {
        public BuildingQueryOption(Integer currentPage, Integer pageSize) {
            super(currentPage, pageSize);
        }

        private Long tenantId;
        private Long hospitalId;
        private Long hospitalAreaId;
        private String name;
        private Integer status;

        private Collection<Long> includeTenantIds;
        private Collection<Long> excludeTenantIds;
        private Collection<Long> includeHospitalIds;
        private Collection<Long> excludeHospitalIds;
        private Collection<Long> includeHospitalAreaIds;
        private Collection<Long> excludeHospitalAreaIds;
    }
}
