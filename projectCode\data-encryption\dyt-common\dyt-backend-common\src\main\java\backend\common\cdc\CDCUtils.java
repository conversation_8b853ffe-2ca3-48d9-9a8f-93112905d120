package backend.common.cdc;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import javax.annotation.Nullable;

public class CDCUtils {
    final static ObjectMapper objectMapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    @Nullable
    public static CDCMessage buildCDCMessage(ConsumerRecord<String, byte[]> msg) throws Exception {
        if (msg == null || msg.key() == null || msg.value() == null) {
            return null;
        }
        JsonNode kroot = objectMapper.readTree(msg.key());
        if (kroot.hasNonNull("schema") && kroot.hasNonNull("payload")) {
            kroot = kroot.path("payload");
        }
        final long id = kroot.path("id").asLong();
        JsonNode vroot = objectMapper.readTree(msg.value());
        if (vroot.hasNonNull("schema") && vroot.hasNonNull("payload")) {
            vroot = vroot.path("payload");
        }
        final EventType op = EventType.parseFlag(vroot.path("op").asText());
        final String before = vroot.path("before").toString();
        final String after = vroot.path("after").toString();
        return (new CDCMessage(id, op, before, after));
    }
}
