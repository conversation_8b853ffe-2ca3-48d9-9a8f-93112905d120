package backend.security.oauth2;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.oauth2.client.OAuth2AuthorizeRequest;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClient;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.util.StringUtils;

public class OAuth2AuthorizedClientRequestInterceptor implements RequestInterceptor, ApplicationContextAware {
    public static final AnonymousAuthenticationToken ANONYMOUS_AUTHENTICATION_TOKEN = new AnonymousAuthenticationToken("key", "anonymous",
            AuthorityUtils.createAuthorityList("ROLE_ANONYMOUS"));
    private OAuth2AuthorizedClientManager authorizedClientManager;
    private final String clientRegistrationId;
    private String testToken;

    public OAuth2AuthorizedClientRequestInterceptor(String clientRegistrationId) {
        this.clientRegistrationId = clientRegistrationId;
    }

    @Override
    public void apply(RequestTemplate template) {
        if (StringUtils.hasText(this.testToken)) {
            template.header("Authorization", "Bearer " + this.testToken);
            return;
        }
        final OAuth2AuthorizedClient authorize = authorizedClientManager.authorize(OAuth2AuthorizeRequest
                .withClientRegistrationId(this.clientRegistrationId)
                .principal(ANONYMOUS_AUTHENTICATION_TOKEN)
                .build());
        if (authorize == null) {
            return;
        }
        final OAuth2AccessToken accessToken = authorize.getAccessToken();
        if (accessToken == null) {
            return;
        }
        template.header("Authorization", accessToken.getTokenType().getValue() + " " + accessToken.getTokenValue());
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        authorizedClientManager = applicationContext.getBean(OAuth2AuthorizedClientManager.class);
        this.testToken = applicationContext.getEnvironment().getProperty("spring.security.oauth2.client.registration." + this.clientRegistrationId + ".test-token");
    }
}
