package backend.common.entity.dto.payment.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RegistrationOrderWaitRefundResponse {

    @JsonProperty("order_number")
    private String orderNumber;

    @JsonProperty("total_fee")
    private BigDecimal totalFee;

    @JsonProperty("result")
    private Boolean result;


}
