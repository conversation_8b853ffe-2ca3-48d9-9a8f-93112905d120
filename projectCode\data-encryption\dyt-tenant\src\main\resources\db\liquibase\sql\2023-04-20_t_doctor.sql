create table t_doctor
(
    id                          bigint auto_increment comment '全局唯一标识'
        primary key,
    tenant_id                   bigint                                   not null comment '租户 ID',
    hospital_id                 bigint                                   not null comment '医院 ID',
    hospital_area_id            bigint                                   not null comment '院区 ID',
    hospital_code               varchar(20)                              null comment '医院编码',
    department_id               bigint                                   not null comment '科室 ID',
    thrdpart_doctor_code        varchar(64)                              null comment 'null 代表Saas模式下的医生， 是系统录入维护的。如果非null，则是第三放HIST平台提供数据同步的。此时应该满足约束：id,hostpital_id,department_id同步',
    head_img_url                varchar(200)                             null comment '头像 URL',
    name                        varchar(30)                              null comment '医生名称',
    email                       varchar(100)          null comment '邮箱',
    rank_dict_type              varchar(50)                              null comment '职级字典类型',
    rank_dict_value             varchar(50)                              null comment '职级，通过逗号隔离多个字典值',
    rank_dict_label             varchar(100)                             null comment '职级，通过逗号隔离多个字典标签',
    speciality                  varchar(200)                             null comment '擅长',
    introduction                varchar(300)                             null comment '简介',
    appointment_rule_dict_type  varchar(50)                              null comment '预约规则字典类型',
    appointment_rule_dict_label varchar(100)                             null comment '预约规则字典标签',
    statement                   varchar(200)                             null comment '特殊说明',
    system_depends              int                   not null comment '挂号系统依赖：0,HIS;1,小系统;',
    status                      int         default 0                    null comment '状态：0，正常；1，禁用',
    user_id                     bigint      default 0                    not null comment '关联用户表，针对在线问诊的医生设置相应值',
    sort                        int         default 0                    null comment '排序',
    visiting_address            varchar(300)          null comment '出诊地址',
    visiting_introduction       text                  null comment '出诊信息',
    appointment_notice          text                  null comment '预约提示',
    payment_notice              text                  null comment '缴费提示',
    judge_appointment_condition tinyint(1) default 0  null comment '判断预约条件，0：不判断，1：判断',
    judge_appointment_rule      text                  null comment '预约条件,|分割',
    need_upload_resource        tinyint(1) default 0  null comment '是否需要上传资料，0：不需要，1：需要',
    need_verify_resource        tinyint(1) default 0  null comment '是否需要审核资料，0：不需要，1：需要',
    success_notice_phones       varchar(300)          null comment '预约成功通知手机号，多个手机号用逗号隔开',
    success_template_ids        varchar(300)          null comment '预约成功通知模板ID，多个模板ID用逗号隔开',
    display                     tinyint(1) default 1  not null comment 'C端是否展示该医生',
    forbidden_day               int        default -1 not null comment '禁止预约天数',
    advance_day                 int        default -1 not null comment '提前预约天数',
    multi_department            tinyint(1) default 0  not null comment '医生显示多个科室，0：否，1：是',
    need_divide_settlement      tinyint(1) default 0  not null comment '是否需要分账，0：不需要，1：需要',
    second_merchant_id          bigint                null comment '二级商户ID',
    settlement_rule_id          bigint                null comment '结算规则ID',
    create_time                 datetime(3) default CURRENT_TIMESTAMP(3) not null,
    update_time                 datetime(3)                              null on update CURRENT_TIMESTAMP(3)
)
    comment 'Doctor';
