create table t_hospital_hospitalization_setting
(
    id                    bigint auto_increment comment '全局唯一标识'
        primary key,
    function_id           bigint                                   null comment '功能 ID',
    tenant_id             bigint                                   not null comment '租户 ID',
    hospital_id           bigint                                   not null comment '医院 ID',
    hospital_area_id      bigint                                   not null comment '院区 ID',
    hospital_code         varchar(20)                              null comment '医院编码',
    enable_payment        tinyint     default 0                    not null comment '是否开启缴费 0:否 1:是',
    support_online_refund tinyint     default 0                    not null comment '是否支持线上退费 0:否 1:是',
    enable_info_query     tinyint     default 0                    not null comment '是否开启住院信息查询 0:否 1:是',
    selected_payments     varchar(200)                             null comment '已选支付方式',
    payment_information   text                                     null comment '缴费信息',
    create_time           datetime(3) default CURRENT_TIMESTAMP(3) not null,
    update_time           datetime(3)                              null on update CURRENT_TIMESTAMP(3),
    constraint uidx_limit_single_setting
        unique (tenant_id, hospital_id, hospital_area_id)
)
    comment '医院住院设置';

insert into t_hospital_hospitalization_setting
    (id, tenant_id, hospital_id, hospital_area_id)
values (1, 1, 1, 2),
       (2, 1, 1, 3),
       (3, 2, 4, 5),
       (4, 2, 4, 6),
       (5, 2, 4, 7);