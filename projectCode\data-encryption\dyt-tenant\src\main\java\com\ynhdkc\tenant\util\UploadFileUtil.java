package com.ynhdkc.tenant.util;

import cn.hutool.core.codec.Base64;
import com.ynhdkc.tenant.client.DataClient;
import com.ynhdkc.tenant.dto.OssFileUploadResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.Objects;


/**
 * <AUTHOR>
 * @since 2023/8/3 12:56:44
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class UploadFileUtil {

    private static final String SERVER_NAME = "ALIYUN";
    private static final String BUCKET_NAME = "refactor-bucket";
    private final DataClient dataClient;

    public File getFile(String fileUrl) throws IOException {
        File tempFile = null;
        InputStream inputStream = null;
        OutputStream outputStream = null;

        try {
            URL url = new URL(fileUrl);
            String path = url.getPath();
            String fileName = path.substring(path.lastIndexOf('/') + 1);

            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()).replace("+", "%20");
            String encodedUrl = fileUrl.substring(0, fileUrl.lastIndexOf('/') + 1) + encodedFileName;

            tempFile = File.createTempFile("download-", "-" + fileName);
            tempFile.deleteOnExit();

            URL encodedURL = new URL(encodedUrl);
            URLConnection connection = encodedURL.openConnection();

            inputStream = connection.getInputStream();
            outputStream = Files.newOutputStream(tempFile.toPath());

            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            return tempFile;
        } catch (Exception e) {
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (!deleted) {
                    tempFile.deleteOnExit();
                    log.warn("Could not delete temp file after error: {}. Scheduled for deletion on JVM exit.",
                            tempFile.getAbsolutePath());
                }
            }
            throw e;
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("Error closing output stream", e);
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("Error closing input stream", e);
                }
            }
        }
    }

    /**
     * download file,then upload to oss
     */
    public String upload(String uploadPath, String fileUrl, String thrdpartDoctorCode) {
        File tempFile = null;
        try {
            if (PathUtil.isBase64(fileUrl)) {
                String fileName = thrdpartDoctorCode + ".jpg";
                byte[] fileContent = Base64.decode(fileUrl);
                MultipartFile multipartFile = new MockMultipartFile(fileName, fileName, "text/plain", fileContent);
                ResponseEntity<OssFileUploadResponse> resp = dataClient.uploadFile(SERVER_NAME, BUCKET_NAME, uploadPath, multipartFile);
                return Objects.requireNonNull(resp.getBody()).getFileUrl();
            } else {
                tempFile = getFile(fileUrl);
                String fileName = generateFileName(tempFile, thrdpartDoctorCode);

                try (InputStream input = Files.newInputStream(tempFile.toPath())) {
                    MultipartFile multipartFile = new MockMultipartFile(fileName, fileName, "text/plain", input);
                    ResponseEntity<OssFileUploadResponse> resp = dataClient.uploadFile(SERVER_NAME, BUCKET_NAME, uploadPath, multipartFile);
                    return Objects.requireNonNull(resp.getBody()).getFileUrl();
                }
            }
        } catch (Exception e) {
            log.info("upload fileUrl: {}", fileUrl);
            log.info("upload file error: {}", e.getMessage());
            return fileUrl;
        } finally {
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (!deleted) {
                    tempFile.deleteOnExit();
                    log.warn("Could not delete temp file: {}. Scheduled for deletion on JVM exit.", tempFile.getAbsolutePath());
                }
            }
        }
    }

    private String generateFileName(File file, String thirdPartDoctorCode) {
        String name = file.getName();
        String[] split = name.split("\\.");
        String suffix = split[split.length - 1];
        return thirdPartDoctorCode + "." + suffix;
    }

    public String preGenerateUrl(String uploadPath, String fileUrl, String thirdPartDoctorCode) {
        File tempFile = null;
        String result;
        try {
            String fileName;
            if (PathUtil.isBase64(fileUrl)) {
                fileName = thirdPartDoctorCode + ".jpg";
            } else {
                tempFile = getFile(fileUrl);
                fileName = generateFileName(tempFile, thirdPartDoctorCode);
            }
            result = uploadPath + "/" + fileName;
        } catch (Exception e) {
            log.error("Pre-generate URL error for fileUrl: {},error info: {}", fileUrl, e.getStackTrace());
            result = fileUrl;
        } finally {
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (!deleted) {
                    tempFile.deleteOnExit();
                    log.warn("Could not delete temp file: {}. Scheduled for deletion on JVM exit.", tempFile.getAbsolutePath());
                }
            }
        }
        return result;
    }
}
