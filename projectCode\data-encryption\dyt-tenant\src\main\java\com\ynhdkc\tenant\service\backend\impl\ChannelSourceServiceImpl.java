package com.ynhdkc.tenant.service.backend.impl;

import cn.hutool.core.collection.CollUtil;
import com.ynhdkc.tenant.dao.mapper.ChannelSourceMapper;
import com.ynhdkc.tenant.entity.ChannelSource;
import com.ynhdkc.tenant.entity.constant.ChannelSourceType;
import com.ynhdkc.tenant.service.backend.ChannelSourceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ChannelSourceServiceImpl implements ChannelSourceService {

    private final ChannelSourceMapper channelSourceMapper;

    @Override
    public Integer submitChannelSource(List<Long> sourceIds, ChannelSourceType sourceType, List<Integer> channels) {
        Integer count = 0;
        String channelStr = channels.stream().sorted().map(String::valueOf).collect(Collectors.joining(","));
        List<Long> finalSourceIds = sourceIds;
        List<ChannelSource> channelSources = channelSourceMapper.selectByExample(ChannelSource.class,
                helper -> helper.defGroup(where -> where.andIn(ChannelSource::getSourceId, finalSourceIds)
                        .andEqualTo(ChannelSource::getSourceType, sourceType.getCode())));
        List<Long> oldSourceIds = channelSources.stream().map(ChannelSource::getSourceId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(oldSourceIds)) {
            List<Long> oldIds = channelSources.stream().map(ChannelSource::getId).collect(Collectors.toList());
            count += channelSourceMapper.updateChannelInIds(oldIds, channelStr);
            sourceIds = sourceIds.stream().filter(id -> !oldSourceIds.contains(id)).collect(Collectors.toList());
        }
        List<ChannelSource> insertList = sourceIds.stream()
                .map(id -> {
                    ChannelSource channelSource = new ChannelSource();
                    channelSource.setSourceId(id);
                    channelSource.setSourceType(sourceType.getCode());
                    channelSource.setChannelCode(channelStr);
                    return channelSource;
                })
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(insertList)) {
            count += channelSourceMapper.insertList(insertList);
        }
        return count;
    }

    @Override
    public List<Long> selectSourceIdByChannel(ChannelSourceType sourceType, List<Integer> channelCodes) {
        List<ChannelSource> channelSources = channelSourceMapper.selectByExample(ChannelSource.class,
                helper -> helper.defGroup(where -> where.andEqualTo(ChannelSource::getSourceType, sourceType.getCode())));
        channelSources = channelSources.stream().filter(channelSource -> {
            List<Integer> channelCodeList = channelSource.getChannelCodeList();
            return channelCodeList.stream().anyMatch(channelCodes::contains);
        }).collect(Collectors.toList());
        return channelSources.stream().map(ChannelSource::getSourceId).collect(Collectors.toList());
    }

    @Override
    public Map<Long, List<Integer>> selectChannelMap(ChannelSourceType sourceType, List<Long> sourceId) {
        List<ChannelSource> channelSources = channelSourceMapper.selectByExample(ChannelSource.class,
                helper -> helper.defGroup(where -> {
                    if (!CollectionUtils.isEmpty(sourceId)) {
                        where.andIn(ChannelSource::getSourceId, sourceId);
                    }
                    if (sourceType != null) {
                        where.andEqualTo(ChannelSource::getSourceType, sourceType.getCode());
                    }
                }));
        return channelSources.stream().collect(Collectors.toMap(ChannelSource::getSourceId, ChannelSource::getChannelCodeList));
    }

    @Override
    public Integer deleteChannelSource(List<Long> sourceId, ChannelSourceType sourceType) {
        List<ChannelSource> channelSources = channelSourceMapper.selectByExample(ChannelSource.class,
                helper -> helper.defGroup(where -> where.andIn(ChannelSource::getSourceId, sourceId)
                        .andEqualTo(ChannelSource::getSourceType, sourceType.getCode())));
        if (!CollectionUtils.isEmpty(channelSources)) {
            List<Long> ids = channelSources.stream().map(ChannelSource::getId).collect(Collectors.toList());
            return channelSourceMapper.deleteByIds(CollUtil.join(ids, ","));
        }
        return 0;
    }
}
