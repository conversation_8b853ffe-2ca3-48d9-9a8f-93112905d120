package com.ynhdkc.tenant.dao;

import backend.common.util.BaseQueryOption;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.entity.HospitalAreaPosition;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/9/25 17:49
 */
public interface HospitalAreaPositionQuery {

    HospitalAreaPosition queryByDepartmentId(Long departmentId);

    HospitalAreaPosition queryByUniqueKey(Long buildingId, Long floorId, Long areaId, Integer type, String name);

    Page<HospitalAreaPosition> pageQueryPositions(PositionQueryOption option);

    HospitalAreaPosition queryById(Long positionId);

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Accessors(chain = true)
    class PositionQueryOption extends BaseQueryOption {
        private Long hospitalAreaId;
        private Long buildingId;
        private Long floorId;
        private Long areaId;
        private Integer type;
        private String name;

        public PositionQueryOption(Integer currentPage, Integer pageSize) {
            super(currentPage, pageSize);
        }
    }
}
