package backend.common.entity.dto.hisgateway.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MessageEnvelope<T> {

    @JsonProperty("hospital_id")
    private Long hospitalId;

    @JsonProperty("hospital_code")
    private String hospitalCode;

    @JsonProperty("hospital_area_id")
    private Long hospitalAreaId;

    @JsonProperty("department_id")
    private Long departmentId;

    @JsonProperty("doctor_id")
    private Long doctorId;

    @JsonProperty("message_trace_id")
    private String messageTraceId;

    private Long timestamp;

    @JsonProperty("pay_load")
    private T parameterMap;
}
