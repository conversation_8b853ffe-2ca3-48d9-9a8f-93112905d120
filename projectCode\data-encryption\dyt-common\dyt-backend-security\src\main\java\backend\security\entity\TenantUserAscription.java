package backend.security.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-24 16:25
 */
@Data
public class TenantUserAscription implements Serializable {
    public TenantUserAscription() {
        this.tenants = new HashSet<>();
        this.hospitals = new HashSet<>();
        this.hospitalAreas = new HashSet<>();
        this.departments = new HashSet<>();
    }

    public TenantUserAscription(Long userId) {
        this();
        this.userId = userId;
    }

    private Long userId;
    private Set<Long> tenants;
    private Set<Long> hospitals;
    private Set<Long> hospitalAreas;
    private Set<Long> departments;

    @JsonIgnore
    public void addTenant(Long tenantId) {
        this.tenants.add(tenantId);
    }

    @JsonIgnore
    public void addAllTenant(Collection<Long> tenantIds) {
        this.tenants.addAll(tenantIds);
    }

    @JsonIgnore
    public void addHospital(Long hospitalId) {
        this.hospitals.add(hospitalId);
    }

    @JsonIgnore
    public void addAllHospital(Collection<Long> hospitalIds) {
        this.hospitals.addAll(hospitalIds);
    }

    @JsonIgnore
    public void addHospitalArea(Long hospitalAreaId) {
        this.hospitalAreas.add(hospitalAreaId);
    }

    @JsonIgnore
    public void addAllHospitalArea(Collection<Long> hospitalAreaIds) {
        this.hospitalAreas.addAll(hospitalAreaIds);
    }

    @JsonIgnore
    public void addDepartment(Long departmentId) {
        this.departments.add(departmentId);
    }

    @JsonIgnore
    public void addAllDepartment(Collection<Long> departmentIds) {
        this.departments.addAll(departmentIds);
    }
}
