package backend.common.domain.tenant.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/11/9 9:56
 */
@Getter
public enum DictFileStatus {
    ENABLE(0, "启用"),
    DISABLE(1, "禁用"),
    DELETE(2, "删除");

    private final Integer code;
    private final String desc;

    DictFileStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DictFileStatus of(Integer code) {
        if (null == code) {
            return null;
        }
        for (DictFileStatus dictFileStatus : DictFileStatus.values()) {
            if (dictFileStatus.getCode().equals(code)) {
                return dictFileStatus;
            }
        }
        return null;
    }
}
