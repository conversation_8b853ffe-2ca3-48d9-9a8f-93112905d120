package com.ynhdkc.tenant.entity.constant;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-09 10:59
 */
public enum HospitalAreaFunctionStatus {
    OPEN(0, "开启"),
    MAINTAIN(1, "维护"),
    CLOSE(2, "关闭");

    private final Integer code;
    private final String desc;

    HospitalAreaFunctionStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static HospitalAreaFunctionStatus of(Integer code) {
        for (HospitalAreaFunctionStatus value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static boolean contains(Integer code) {
        if (null == code) {
            return false;
        }
        for (HospitalAreaFunctionStatus value : values()) {
            if (value.code.equals(code)) {
                return true;
            }
        }
        return false;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
