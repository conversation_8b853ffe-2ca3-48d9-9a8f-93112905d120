package com.ynhdkc.tenant.doctor.sort.impl;

import com.ynhdkc.tenant.doctor.sort.strategy.AbstractDoctorSortStrategy;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.model.CustomerGroupedDoctorDetailVo;
import com.ynhdkc.tenant.tool.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.ynhdkc.tenant.tool.DoctorSortUtils.getPinyin;

@Component
public class Hospital43SortStrategy extends AbstractDoctorSortStrategy {

    private static final Set<String> HOSPITAL_CODES = Collections.unmodifiableSet(new HashSet<>(Collections.singletonList("871256")));

    public Hospital43SortStrategy() {
        super(HOSPITAL_CODES);
    }

    @Override
    public void sort(List<Doctor> doctors) {
        doctors.forEach(doctor -> {
            if (doctor.getName().equals("徐永清（特需）")) {
                doctor.setSort(0);
            } else {
                doctor.setSort(getSortValue(doctor));
            }
        });
        doctors.sort(Comparator.comparing(Doctor::getSort).thenComparing(x -> {
            String name = x.getName();
            String pinyin = StringUtils.hasText(name) ? getPinyin(name) : "";
            return StringUtils.hasText(pinyin) ? pinyin : "";
        }));
    }

    private Integer getSortValue(Doctor doctor) {
        try {
            if (org.springframework.util.StringUtils.hasText(doctor.getRankDictValue())) {
                return Integer.parseInt(doctor.getRankDictValue());
            } else {
                return 0;
            }
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    @Override
    public void sortGroupVo(List<CustomerGroupedDoctorDetailVo> doctorGroupVos) {
        doctorGroupVos.sort(Comparator.comparing((CustomerGroupedDoctorDetailVo vo) -> vo.getSort() == null ? 0 : vo.getSort()));
    }

}
