package com.ynhdkc.tenant.service.customer;

import com.ynhdkc.tenant.entity.detailpage.HospitalAreaDetailPageSubNaviModule;
import com.ynhdkc.tenant.model.CustomerHospitalAreaDetailPageSubNaviModuleVo;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/7/19 16:11
 */
public interface CustomerHospitalAreaDetailPageSubNaviModuleService {
    List<CustomerHospitalAreaDetailPageSubNaviModuleVo> getHospitalAreaDetailPageSubNaviModuleByHospitalAreaId(Long hospitalAreaId, String hospitalAreaCode);

    static CustomerHospitalAreaDetailPageSubNaviModuleVo toVo(HospitalAreaDetailPageSubNaviModule entity) {
        CustomerHospitalAreaDetailPageSubNaviModuleVo vo = new CustomerHospitalAreaDetailPageSubNaviModuleVo();
        vo.setSubNaviType(entity.getSubNaviType());
        vo.setSubNaviDisplayLimit(entity.getSubNaviDisplayLimit());
        vo.setSort(entity.getSort());
        return vo;
    }
}
