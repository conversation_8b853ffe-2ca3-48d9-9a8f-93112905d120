package backend.common.densensitized.service;

import backend.common.densensitized.annotation.PrivacyEncrypt;

public abstract class AbstractDesensitizedPermissionService {


    public AbstractDesensitizedPermissionService() {
    }

    /**
     * 用于{@link backend.common.densensitized.PrivacySerialize}序列化时判断属性是否需要序列化的方法
     * 不要在实现该方法时进行耗时操作，Jackson会在对象序列化时针对每一个存在{@link PrivacyEncrypt}注解的属性调用该方法
     * @param payload 透传的参数，为{@link PrivacyEncrypt#permission()}，建议用于权限判断
     * @param description 透传的参数，为{@link PrivacyEncrypt#description()} ()}，建议用于审计记录
     * @return 一个boolean值，当返回值为true时{@link backend.common.densensitized.PrivacySerialize}将对属性进行脱敏
     */
    public abstract boolean evaluate(String payload, String description);

}
