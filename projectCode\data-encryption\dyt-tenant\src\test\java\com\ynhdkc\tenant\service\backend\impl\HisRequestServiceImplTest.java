package com.ynhdkc.tenant.service.backend.impl;

import backend.common.enums.HospitalCode;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ynhdkc.tenant.DytTenantApplication;
import com.ynhdkc.tenant.client.HisGatewayClient;
import com.ynhdkc.tenant.client.model.DepartmentList4MultiLevelResponse;
import com.ynhdkc.tenant.dao.DepartmentQuery;
import com.ynhdkc.tenant.dao.HospitalAreaSettingQuery;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.model.HospitalDependOnHisResponse;
import com.ynhdkc.tenant.service.backend.DepartmentService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.fasterxml.jackson.databind.PropertyNamingStrategy.SNAKE_CASE;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = {DytTenantApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("dev")
@Slf4j
class HisRequestServiceImplTest {

    private final ObjectMapper objectMapper = new ObjectMapper();
    @Autowired
    HospitalAreaSettingQuery hospitalAreaSettingQuery;
    @Autowired
    private HisRequestServiceImpl hisRequestService;
    @Autowired
    private DepartmentService departmentService;
    @MockBean
    private HisGatewayClient hisGatewayClient;
    @MockBean
    private DepartmentQuery departmentQuery;

    @Test
    void updateGongRenHospitalDepartmentInfo() {
        List<DepartmentList4MultiLevelResponse.Result> results = fromJson();
        if (results == null) {
            return;
        }
        log.info("results: {}", results);

        List<Department> departments = Objects.requireNonNull(departments()).stream().filter(q -> q.getEnabled().equals(Boolean.TRUE)).collect(Collectors.toList());
        when(departmentQuery.queryBy(HospitalCode.KUNMING_MU_SECOND_AFFILIATED_HOSPITAL.getCode())).thenReturn(departments);

        departmentService.createOrUpdateGongRenHospitalDepartmentInfo(results);
    }


    private List<DepartmentList4MultiLevelResponse.Result> fromJson() {
        try (InputStream inputStream = HisRequestServiceImplTest.class.getClassLoader().getResourceAsStream("gongren_dp.json")) {
            if (inputStream == null) {
                return null;
            }
            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            int nRead;
            byte[] data = new byte[1024];
            while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, nRead);
            }
            buffer.flush();
            String gongRenJSON = buffer.toString();
            return objectMapper.readValue(gongRenJSON, new TypeReference<List<DepartmentList4MultiLevelResponse.Result>>() {
            });
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    private List<Department> departments() {
        try (InputStream inputStream = HisRequestServiceImplTest.class.getClassLoader().getResourceAsStream("t_hospital_department.json")) {
            if (inputStream == null) {
                return null;
            }
            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            int nRead;
            byte[] data = new byte[1024];
            while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, nRead);
            }
            buffer.flush();
            String gongRenJSON = buffer.toString();
            objectMapper.setPropertyNamingStrategy(SNAKE_CASE);
            objectMapper.registerModule(new JavaTimeModule());
            // Set the date format
            objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS"));
            // Configure to fail on unknown properties
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            // Disable writing dates as timestamps
            objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

            return objectMapper.readValue(gongRenJSON, new TypeReference<List<Department>>() {
            });
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 查询工人医院科室树层级
     * <p>
     * 打印出每一个科室所在的层级
     */
    @Test
    void queryGongRenDepartmentTreeLevel() {
        Map<Long, Integer> longIntegerMap = departmentService.queryGongRenDepartmentTreeLevel();
        longIntegerMap.forEach((k, v) -> System.out.println("科室ID：" + k + "，所在层级：" + v));

        List<Long> level5 = longIntegerMap.entrySet().stream().filter(entry -> entry.getValue() == 5).map(Map.Entry::getKey).collect(Collectors.toList());
        System.out.println("层级为5的科室ID：" + level5.stream().map(String::valueOf).collect(Collectors.joining(",")));
    }

    @Test
    void queryDependOnHis() {
        Optional<List<HospitalDependOnHisResponse>> list = hospitalAreaSettingQuery.queryDependOnHIs(null, "安宁");
        list.ifPresent(System.out::println);
    }
}