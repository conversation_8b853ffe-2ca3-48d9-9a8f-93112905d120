### 如何对对象字段进行脱敏
该模块实现了一个基于Jackson JsonSerializer的脱敏工具，当需要对对象中特定属性进行脱敏时，可使用`@PrivacyEncrypt`注解，如
```java
@Data
@NoArgsConstructor
public class DemoDto {
    private Long id;

    @PrivacyEncrypt(type = PrivacyType.CHINESE_NAME)
    private String name;

    @PrivacyEncrypt(type = PrivacyType.ID_CARD)
    private String idCard;
}
```
该示例指示了对象属性name在序列化时应当以中文姓名模式进行脱敏，对象属性idCard在序列化时应当以身份证模式进行脱敏。
更多的脱敏类型及脱敏模式可查看`PrivacyType`枚举。

### 如何使用`swagger-codegen-maven-plugin`生成包含`PrivacyEncrypt`注解的属性
由于所有项目使用`swagger-codegen-maven-plugin`通过OpenAPI定义生成接口及接口使用的对象，所以在需要使用脱敏工具的项目内需为`swagger-codegen-maven-plugin`配置特殊的生成模板。

可用的模板目前放置在当前项目，也就是`dyt-common`项目`dyt-backend-common`模块的资源目录中，共有两个模板用于控制对象生成，分别为`model.mustache`和`pojo.mustache`，使用脱敏工具前请将两个模板均放置在相应模块的资源目录下。
同时需要对相应模块的`swagger-codegen-maven-plugin`插件进行配置

样例配置如下
```xml
<plugin>
  <groupId>io.swagger</groupId>
  <artifactId>swagger-codegen-maven-plugin</artifactId>
  <executions>
    <execution>
    <id>generate-api</id>
    <goals>
      <goal>generate</goal>
    </goals>
    <configuration>
      <inputSpec>${project.basedir}/src/main/resources/apis/hospital-model.yml</inputSpec>
      <language>spring</language>
        <modelPackage>com.ynhdkc.tenant.model</modelPackage>
      <importMappings>
        <importMapping>BaseOperationResponse=backend.common.response.BaseOperationResponse</importMapping>
      </importMappings>
      <templateDirectory>${project.basedir}/src/main/resources/apis/template</templateDirectory>
      <configOptions>
        <interfaceOnly>true</interfaceOnly>
        <useTags>true</useTags>
        <dateLibrary>legacy</dateLibrary>
      </configOptions>
    </configuration>
    </execution>
  </executions>
</plugin>
```

注意`templateDirectory`属性需指向放置的模板文件的配置

完成配置后可通过在OpenAPI定义文件中，通过`x-data-desensitized`、`x-desensitized-type`、`x-desensitized-permission`、`x-desensitized-description`四个字段配置生成属性的脱敏注解。
`x-data-desensitized`为true时表示当前属性需要脱敏
`x-desensitized-type`、`x-desensitized-permission`、`x-desensitized-description`三个字段可配置属性的脱敏模式及两个透传参数。

注意`x-desensitized-type`属性的值应当存在于`PrivacyType`枚举中。

如下例
```yml
  DemoDto:
    type: object
    properties:
      name:
        type: string
        description: 用户名
        minLength: 1
        maxLength: 20
      phone_number:
        type: string
        description: 手机号
        x-data-desensitized: true
        x-desensitized-type: MOBILE_PHONE
        x-desensitized-permission: real_mobile
        x-desensitized-description: 用户手机号
        minLength: 11
        maximum: 13
      id_card:
        type: string
        description: 身份证
        x-data-desensitized: true
        x-desensitized-type: ID_CARD
        x-desensitized-permission: real_idcard
        x-desensitized-description: 用户身份证
        minLength: 1
        maxLength: 32
```

### 如何控制脱敏逻辑
同时，为实现可通过业务控制脱敏及及脱敏时审计的相关需求，可通过实现`AbstractDesensitizedPermissionService`并对实现进行Bean装配从而控制脱敏逻辑。

```java
@Data
@NoArgsConstructor
public class DemoDto {
    private Long id;

    @PrivacyEncrypt(type = PrivacyType.CHINESE_NAME)
    private String name;

    @PrivacyEncrypt(type = PrivacyType.ID_CARD, permission = "admin", description = "用户身份证获取")
    private String idCard;
}
```
如上例，`PrivacySerialize`在处理idCard属性时，将透传`permission`及`description`至`AbstractDesensitizedPermissionService`的实现，从而允许开发人员完成判断及审计逻辑

注意不要在实现`AbstractDesensitizedPermissionService`时进行耗时操作，Jackson会在对象序列化时针对每一个存在{@link PrivacyEncrypt}注解的属性调用该方法。

`DefaultDesensitizedPermissionService`将在没有任何其他`AbstractDesensitizedPermissionService`实现的Bean存在时被装配并使用，默认进行属性脱敏并打印info级别日志。

