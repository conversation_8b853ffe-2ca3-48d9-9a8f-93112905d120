package backend.security.method;

import backend.security.service.BackendTenantUserService;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-16 21:06
 */
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class MethodSecurityConfiguration extends BackendMethodSecurityConfiguration {
    public MethodSecurityConfiguration(BackendTenantUserService backendTenantUserService) {
        super(backendTenantUserService);
    }
}
