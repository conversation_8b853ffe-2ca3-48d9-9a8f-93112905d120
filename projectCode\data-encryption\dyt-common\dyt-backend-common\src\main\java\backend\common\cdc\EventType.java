package backend.common.cdc;

import org.springframework.util.StringUtils;

import java.util.Objects;

public enum EventType {
    create("c"), update("u"), delete("d"), read("r");
    private String flag;

    EventType(String flag) {
        this.flag = flag;
    }

    public static EventType parseFlag(String flag) {
        if (!StringUtils.hasText(flag)) {
            return null;
        }
        for (EventType one : values()) {
            if (Objects.equals(one.flag, flag.toLowerCase())) {
                return one;
            }
        }
        return null;
    }
}