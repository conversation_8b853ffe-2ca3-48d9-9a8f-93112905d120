package com.ynhdkc.tenant.tool.convert;

import com.github.pagehelper.Page;
import com.ynhdkc.tenant.model.BasePage;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/11 15:38:57
 */
@Component
public final class PageVoConvert {

    private <T, V extends BasePage> void setPagination(Page<T> pageEntity, V pageVo) {
        pageVo.setTotalSize(pageEntity.getTotal());
        pageVo.setCurrentPage(pageEntity.getPageNum());
        pageVo.setPageSize(pageEntity.getPageSize());
    }

    @SneakyThrows
    public <S, D extends BasePage, V> D toPageVo(Page<S> source, Class<D> destination, Function<? super S, ? extends V> mapper) {
        D clazz = destination.newInstance();
        setPagination(source, clazz);

        final List<V> vos = source.stream().map(mapper).collect(Collectors.toList());
        final String methodName = "setList";
        clazz.getClass().getMethod(methodName, List.class).invoke(clazz, vos);
        return clazz;
    }

    @SneakyThrows
    public <S, D extends BasePage, V> D toPageVo(Page<S> source, Class<D> destination, Function<? super S, ? extends V> mapper, Predicate<? super S> filter) {
        D clazz = destination.newInstance();
        setPagination(source, clazz);

        final List<V> vos = source.stream().filter(filter).map(mapper).collect(Collectors.toList());
        final String methodName = "setList";
        clazz.getClass().getMethod(methodName, List.class).invoke(clazz, vos);
        return clazz;
    }
}
