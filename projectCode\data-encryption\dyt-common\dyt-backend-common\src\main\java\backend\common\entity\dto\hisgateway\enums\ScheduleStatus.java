package backend.common.entity.dto.hisgateway.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("排班状态")
public enum ScheduleStatus {

	/**
	 * 接受
	 */
	@ApiModelProperty("接受")
	Accept(1),
	/**
	 * 时间未到
	 */
	@ApiModelProperty("时间未到")
	NotYet(2),
	/**
	 * 已关闭
	 */
	@ApiModelProperty("已关闭")
	Close(3),
	/**
	 * 约满
	 */
	@ApiModelProperty("约满")
	Full(4),
	/**
	 * 不开放预约
	 */
	@ApiModelProperty("不开放预约")
	NotOpen(5),
	/**
	 * 停诊
	 */
	@ApiModelProperty("停诊")
	StopDiagnosis(6),

	UNKNOW(7);

	@JsonValue
	private int id;

	ScheduleStatus(int i) {
		this.id = i;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public static ScheduleStatus fromInteger(int x) {
		return getEnumFromValue(x);
	}

	@JsonCreator
	public static ScheduleStatus getEnumFromValue(int value) {
		for (ScheduleStatus testEnum : values()) {
			if (testEnum.getId() == value) {
				return testEnum;
			}
		}
		return UNKNOW;
	}

	public static ScheduleStatus getEnumFromName(String name) {
		for (ScheduleStatus testEnum : values()) {
			if (testEnum.name().equals(name)) {
				return testEnum;
			}
		}
		return UNKNOW;
	}

}
