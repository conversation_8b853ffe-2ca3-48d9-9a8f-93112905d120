package com.ynhdkc.tenant.api.backend;

import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import backend.security.method.BackendSecurityRequired;
import backend.security.oplog.DytSecurityRequired;
import backend.security.service.BackendTenantUserService;
import com.ynhdkc.tenant.handler.HospitalAreaPositionApi;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.AreaService;
import com.ynhdkc.tenant.service.backend.BuildingService;
import com.ynhdkc.tenant.service.backend.FloorService;
import com.ynhdkc.tenant.service.backend.HospitalAreaPositionService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2023/2/24 09:19:31
 */
@Api(tags = "HospitalAreaPosition")
@RestController
@RequiredArgsConstructor
public class HospitalAreaPositionController implements HospitalAreaPositionApi {
    private final HospitalAreaPositionService hospitalAreaPositionService;
    private final BuildingService buildingService;
    private final FloorService floorService;
    private final AreaService areaService;

    private final BackendTenantUserService backendTenantUserService;

    @BackendSecurityRequired(tenantIdExpr = "#request.tenantId", hospitalIdExpr = "#request.hospitalId", hospitalAreaIdExpr = "#request.hospitalAreaId")
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:position:create:area")
    @Override
    public ResponseEntity<AreaVo> createArea(AreaCreateReqDto request) {
        return ResponseEntity.ok(areaService.create(request));
    }

    @BackendSecurityRequired(tenantIdExpr = "#request.tenantId", hospitalIdExpr = "#request.hospitalId", hospitalAreaIdExpr = "#request.hospitalAreaId")
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:position:create:building")
    @Override
    public ResponseEntity<BuildingVo> createBuilding(BuildingCreateReqDto request) {
        return ResponseEntity.ok(buildingService.create(request));
    }

    @BackendSecurityRequired(tenantIdExpr = "#request.tenantId", hospitalIdExpr = "#request.hospitalId", hospitalAreaIdExpr = "#request.hospitalAreaId")
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:position:create:floor")
    @Override
    public ResponseEntity<FloorVo> createFloor(FloorCreateReqDto request) {
        return ResponseEntity.ok(floorService.create(request));
    }

    @BackendSecurityRequired(tenantIdExpr = "#request.tenantId", hospitalIdExpr = "#request.hospitalId", hospitalAreaIdExpr = "#request.hospitalAreaId")
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:position:create:location")
    @Override
    public ResponseEntity<BaseOperationResponse> createLocation(LocationCreateReqDto request) {
        return ResponseEntity.ok(hospitalAreaPositionService.createLocation(request));
    }

    @BackendSecurityRequired(tenantIdExpr = "#request.tenantId", hospitalIdExpr = "#request.hospitalId", hospitalAreaIdExpr = "#request.hospitalAreaId")
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:position:delete:area")
    @Override
    public ResponseEntity<BaseOperationResponse> deleteArea(Long areaId, Long tenantId, Long hospitalId, Long hospitalAreaId) {
        return ResponseEntity.ok(areaService.delete(areaId));
    }

    @BackendSecurityRequired(tenantIdExpr = "#tenantId", hospitalIdExpr = "#hospitalId", hospitalAreaIdExpr = "#hospitalAreaId")
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:position:delete:building")
    @Override
    public ResponseEntity<BaseOperationResponse> deleteBuilding(Long buildingId, Long tenantId, Long hospitalId, Long hospitalAreaId) {
        return ResponseEntity.ok(buildingService.delete(buildingId));
    }

    @BackendSecurityRequired(tenantIdExpr = "#request.tenantId", hospitalIdExpr = "#request.hospitalId", hospitalAreaIdExpr = "#request.hospitalAreaId")
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:position:delete:floor")
    @Override
    public ResponseEntity<BaseOperationResponse> deleteFloor(Long floorId, Long tenantId, Long hospitalId, Long hospitalAreaId) {
        return ResponseEntity.ok(floorService.delete(floorId));
    }

    @BackendSecurityRequired(tenantIdExpr = "#request.tenantId", hospitalIdExpr = "#request.hospitalId", hospitalAreaIdExpr = "#request.hospitalAreaId")
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:position:delete:location")
    @Override
    public ResponseEntity<BaseOperationResponse> deleteLocation(Long locationId, Long hospitalId, Long hospitalAreaId, Long tenantId) {
        return ResponseEntity.ok(hospitalAreaPositionService.deleteLocation(locationId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:position:get:area")
    public ResponseEntity<AreaVo> getAreaDetail(Long areaId, Long tenantId, Long hospitalId, Long hospitalAreaId) {
        if (!backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.HospitalAreaId(hospitalAreaId))) {
            throw new BizException(HttpStatus.FORBIDDEN, "无权限查看该院区信息");
        }
        return ResponseEntity.ok(areaService.getDetail(areaId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:position:get:building")
    public ResponseEntity<BuildingVo> getBuildingDetail(Long buildingId, Long tenantId, Long hospitalId, Long hospitalAreaId) {
        if (!backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.HospitalAreaId(hospitalAreaId))) {
            throw new BizException(HttpStatus.FORBIDDEN, "无权限查看该院区信息");
        }
        return ResponseEntity.ok(buildingService.getDetail(buildingId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:position:get:floor")
    public ResponseEntity<FloorVo> getFloorDetail(Long floorId, Long tenantId, Long hospitalId, Long hospitalAreaId) {
        if (!backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.HospitalAreaId(hospitalAreaId))) {
            throw new BizException(HttpStatus.FORBIDDEN, "无权限查看该院区信息");
        }
        return ResponseEntity.ok(floorService.getDetail(floorId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:position:query:area")
    public ResponseEntity<AreaPageVo> queryArea(AreaQueryReqDto request) {
        if (!backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.HospitalAreaId(request.getHospitalAreaId()))) {
            throw new BizException(HttpStatus.FORBIDDEN, "无权限查看该院区信息");
        }
        return ResponseEntity.ok(areaService.query(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:position:query:location")
    public ResponseEntity<HospitalAreaPositionQueryRespDto> queryLocation(HospitalAreaPositionQueryReqDto request) {
        return ResponseEntity.ok(hospitalAreaPositionService.queryLocation(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:position:query:positionTree")
    public ResponseEntity<HospitalAreaPositionTreeQueryRespDto> queryPositionTree(HospitalAreaPositionTreeQueryReqDto request) {
        return ResponseEntity.ok(hospitalAreaPositionService.queryPositionTree(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:position:query:building")
    public ResponseEntity<BuildingPageVo> queryBuilding(BuildingQueryReqDto request) {
        if (!backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.HospitalAreaId(request.getHospitalAreaId()))) {
            throw new BizException(HttpStatus.FORBIDDEN, "无权限查看该院区信息");
        }
        return ResponseEntity.ok(buildingService.query(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:position:query:floor")
    public ResponseEntity<FloorPageVo> queryFloor(FloorQueryReqDto request) {
        if (!backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.HospitalAreaId(request.getHospitalAreaId()))) {
            throw new BizException(HttpStatus.FORBIDDEN, "无权限查看该院区信息");
        }
        return ResponseEntity.ok(floorService.query(request));
    }

    @BackendSecurityRequired(tenantIdExpr = "#request.tenantId", hospitalIdExpr = "#request.hospitalId", hospitalAreaIdExpr = "#request.hospitalAreaId")
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:position:update:area")
    @Override
    public ResponseEntity<AreaVo> updateArea(Long areaId, AreaUpdateReqDto request) {
        return ResponseEntity.ok(areaService.update(areaId, request));
    }

    @BackendSecurityRequired(tenantIdExpr = "#request.tenantId", hospitalIdExpr = "#request.hospitalId", hospitalAreaIdExpr = "#request.hospitalAreaId")
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:position:update:building")
    @Override
    public ResponseEntity<BuildingVo> updateBuilding(Long buildingId, BuildingUpdateReqDto request) {
        return ResponseEntity.ok(buildingService.update(buildingId, request));
    }

    @BackendSecurityRequired(tenantIdExpr = "#request.tenantId", hospitalIdExpr = "#request.hospitalId", hospitalAreaIdExpr = "#request.hospitalAreaId")
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:position:update:floor")
    @Override
    public ResponseEntity<FloorVo> updateFloor(Long floorId, FloorUpdateReqDto request) {
        return ResponseEntity.ok(floorService.update(floorId, request));
    }

    @BackendSecurityRequired(tenantIdExpr = "#request.tenantId", hospitalIdExpr = "#request.hospitalId", hospitalAreaIdExpr = "#request.hospitalAreaId")
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:position:update:location")
    @Override
    public ResponseEntity<BaseOperationResponse> updateLocation(Long locationId, LocationUpdateReqDto request) {
        return ResponseEntity.ok(hospitalAreaPositionService.updateLocation(locationId, request));
    }
}
