package backend.encryption.example;

import backend.encryption.annotation.EncryptField;
import backend.encryption.converter.EncryptConverter;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * 用户实体示例
 * 演示如何使用@EncryptField注解和EncryptConverter进行字段加密
 *
 * <AUTHOR> Backend Team
 * @version 1.1-SNAPSHOT
 * @since 2024-06-24
 */
@Data
@Entity
@Table(name = "user_example")
public class TestEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户名（不加密）
     */
    @Column(name = "username", nullable = false, length = 50)
    private String username;

    /**
     * 手机号（使用默认AES-GCM加密）
     */
    @EncryptField(description = "用户手机号")
    @Convert(converter = EncryptConverter.class)
    @Column(name = "phone", length = 500)
    private String phone;

    /**
     * 身份证号（使用SM2算法加密）
     */
    @EncryptField(
        description = "用户身份证号",
        algorithm = EncryptField.AlgorithmType.SM2
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "id_card", length = 1000)
    private String idCard;

    /**
     * 邮箱（使用影子字段迁移策略）
     */
    @EncryptField(
        description = "用户邮箱",
        shadowField = "email_encrypted",
        migrationStrategy = EncryptField.MigrationStrategy.SHADOW_PRIORITY,
        strategyKey = "user-email"
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "email", length = 500)
    private String email;

    /**
     * 邮箱加密字段（影子字段，用于渐进式迁移）
     */
    @Column(name = "email_encrypted", length = 1000)
    private String emailEncrypted;

    /**
     * 地址（条件加密，可通过配置控制）
     */
    @EncryptField(
        description = "用户地址",
        enabled = true,
        strategyKey = "user-address"
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "address", length = 1000)
    private String address;

    /**
     * 创建时间（不加密）
     */
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 更新时间（不加密）
     */
    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;

    /**
     * 状态（不加密）
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 备注（可选加密）
     */
    @EncryptField(
        description = "备注信息",
        enabled = false,  // 默认不加密，可通过配置启用
        strategyKey = "user-remark"
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "remark", length = 2000)
    private String remark;
}
