package com.ynhdkc.tenant.entity.setting;

import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-09 9:31
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(name = "t_hospital_function_setting")
public class FunctionSetting extends BaseEntity {
    /**
     * 租户 ID
     */
    private Long tenantId;
    /**
     * 医院 ID
     */
    private Long hospitalId;
    /**
     * 院区 ID
     */
    private Long hospitalAreaId;
    /**
     * 功能名称
     */
    private String name;
    /**
     * 功能类型
     */
    private String type;
    /**
     * 功能图标
     */
    private String logo;
    /**
     * tab展示样式
     */
    private String tabDisplayStyle;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 状态 0:开启 1:维护 2:关闭
     */
    private Integer status;
}
