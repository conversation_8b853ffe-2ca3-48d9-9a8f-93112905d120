package com.ynhdkc.tenant.api.customer;

import com.ynhdkc.tenant.handler.CustomerRegionApi;
import com.ynhdkc.tenant.model.RegionPageVo;
import com.ynhdkc.tenant.model.RegionQueryReqDto;
import com.ynhdkc.tenant.service.backend.RegionService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/7/21 9:19
 */
@Api(tags = "CustomerRegion")
@RestController
@RequiredArgsConstructor
public class CustomerRegionController implements CustomerRegionApi {
    private final RegionService regionService;

    @Override
    public ResponseEntity<RegionPageVo> query(RegionQueryReqDto request) {
        return ResponseEntity.ok(regionService.query(request));
    }
}
