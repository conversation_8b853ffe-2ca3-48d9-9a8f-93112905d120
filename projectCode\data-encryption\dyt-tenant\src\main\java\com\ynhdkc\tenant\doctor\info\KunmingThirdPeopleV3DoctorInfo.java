package com.ynhdkc.tenant.doctor.info;

import backend.common.enums.HospitalCode;
import com.ynhdkc.tenant.client.model.DepartmentDoctorListEnvResponse;
import com.ynhdkc.tenant.dao.DoctorQuery;
import com.ynhdkc.tenant.dao.DoctorRepository;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.service.backend.DictLabelService;
import com.ynhdkc.tenant.tool.DoctorHeadImageHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor
public class KunmingThirdPeopleV3DoctorInfo implements IDoctorInfo {
    private final static String DOCTOR_RANK_DICT_TYPE = "doctor_title";
    private final DoctorQuery doctorQuery;
    private final DoctorRepository doctorRepository;
    private final DoctorHeadImageHandler doctorHeadImageHandler;
    private final DictLabelService dictLabelService;

    @Override
    public List<String> getHospitalCode() {
        return Arrays.asList(
                HospitalCode.KUNMING_THIRD_PEOPLE_HOSPITAL_WU_JING_V3.getCode(),
                HospitalCode.KUNMING_THIRD_PEOPLE_HOSPITAL_CHANG_PO_V3.getCode()
        );
    }

    @Override
    public void resolve(@NonNull DepartmentDoctorListEnvResponse response) {
        List<DepartmentDoctorListEnvResponse.Payload.Result> result = response.getPayload().getResult();
        if (CollectionUtils.isEmpty(result)) {
            log.warn("resolve doctor info failed, response: {}", response);
            return;
        }

        result.stream().filter(item -> StringUtils.hasText(item.getDoctorProfilePhoto())).forEach(this::updateDoctorProfilePhoto);
    }

    private void updateDoctorProfilePhoto(DepartmentDoctorListEnvResponse.Payload.Result item) {
        if (item.getDoctorProfilePhoto().contains("http://192.168.20.15:9400/service/file/get/image/")) {
            String avatarUrl = item.getDoctorProfilePhoto().replace("http://192.168.20.15:9400/service/file/get/image/", "http://119.62.71.114:8009/image/");
            item.setDoctorProfilePhoto(avatarUrl);
        }
    }

    @Override
    public void updateDoctorInfo(String hospitalCode, DepartmentDoctorListEnvResponse body) {
        List<Doctor> doctors = doctorQuery.queryEnableDoctorsBy(hospitalCode);
        if (CollectionUtils.isEmpty(doctors)) {
            return;
        }

        body.getPayload().getResult().forEach(result -> {
            List<Doctor> filterDoctorsByDoctorCode = doctors.stream().filter(d -> d.getThrdpartDoctorCode().equals(result.getDoctor().getThrdpartDoctorCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterDoctorsByDoctorCode)) {
                return;
            }

            filterDoctorsByDoctorCode = filterDoctorsByDoctorCode.stream().filter(q -> StringUtils.hasText(q.getThrdpartDoctorCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterDoctorsByDoctorCode)) {
                return;
            }


            for (Doctor doctor : filterDoctorsByDoctorCode) {
                if (StringUtils.hasText(result.getDoctor().getIntroduction())) {
                    doctor.setIntroduction(result.getDoctor().getIntroduction());
                }
                if (StringUtils.hasText(result.getDoctor().getSpeciality())) {
                    doctor.setSpeciality(result.getDoctor().getSpeciality());
                }
                String doctorImageUrl = doctorHeadImageHandler.getDoctorImageUrl(result.getDoctorProfilePhoto(), hospitalCode, result.getDoctor().getDepartmentCode(), result.getDoctor().getThrdpartDoctorCode());
                doctor.setHeadImgUrl(doctorImageUrl);
                doctor.setUpdateTime(new Date());
                doctorRepository.update(doctor);
            }
        });
    }
}
