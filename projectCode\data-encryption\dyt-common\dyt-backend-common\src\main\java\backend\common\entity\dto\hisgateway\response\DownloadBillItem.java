package backend.common.entity.dto.hisgateway.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class DownloadBillItem {

    @JsonProperty("pay_number")
    private String payNumber;

    @JsonProperty("query_type")
    private Integer queryType;

    @JsonProperty("pay_time")
    private Long payTime;

    @JsonProperty("refund_time")
    private Long refundTime;

    @JsonProperty("pay_amt")
    private Double payAmt;

    @JsonProperty("pay_type")
    private Integer payType;

    private String name;

    @JsonProperty("pay_channel")
    private Integer payChannel;

}
