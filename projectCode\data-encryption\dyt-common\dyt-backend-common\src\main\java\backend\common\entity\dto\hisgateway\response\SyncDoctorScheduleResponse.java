package backend.common.entity.dto.hisgateway.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class SyncDoctorScheduleResponse {

    @JsonProperty("hospital_code")
    private String hospitalCode;

    @JsonProperty("department_code")
    private String departmentCode;

    @JsonProperty("sync_doctor_number")
    private int syncDoctorNumber;

    @JsonProperty("sync_doctor_schedule_number")
    private int syncDoctorScheduleNumber;

    @JsonProperty("sync_time_schedule_number")
    private int syncTimeScheduleNumber;

    private String message;

    private boolean result = true;

    @JsonProperty("error_message")
    private String errorMessage;

    @JsonProperty("trace_id")
    private String traceId;

}
