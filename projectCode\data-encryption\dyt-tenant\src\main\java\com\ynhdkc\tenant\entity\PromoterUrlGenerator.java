package com.ynhdkc.tenant.entity;

import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "t_promoter_url_generator")
public class PromoterUrlGenerator extends BaseEntity {

    private String promoterName;

    private String promoterCode;

    private String targetUrl;

    private String promoterUrl;

    private String promoterScene;
}
