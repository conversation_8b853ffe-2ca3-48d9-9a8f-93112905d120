package backend.common.entity.dto.hisgateway.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class CreateArchivesRequest {

    private String name;

    @JsonProperty("hospital_code")
    private String hospitalCode;

    @JsonProperty("id_card_number")
    private String idCardNumber;

    @JsonProperty("id_card_type")
    private Integer idCardType;

    private Long birthday;

    private Integer sex;

    private String telephone;

    private String nation;

    private Integer occupation;

    private String address;

    @JsonProperty("insure_card_type")
    private Integer insureCardType;

    @JsonProperty("insure_card_number")
    private String insureCardNumber;

    @JsonProperty("guarantee_name")
    private String guaranteeName;

    @JsonProperty("guarantee_id_card")
    private String guaranteeIdCard;

    @JsonProperty("guarantee_relation")
    private Integer guaranteeRelation;

    @JsonProperty("medical_record_number")
    private String medicalRecordNumber;

    private String vuid;


}
