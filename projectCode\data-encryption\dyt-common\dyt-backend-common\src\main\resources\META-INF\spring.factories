org.springframework.boot.env.EnvironmentPostProcessor=\
backend.common.discovery.Routing2EurekaEnvironmentPostProcessor
# Auto Configuration
org.springframework.boot.autoconfigure.EnableAutoConfiguration=\
backend.common.swagger.WebMvcSwaggerAutoConfiguration,\
backend.common.exception.ExceptionHandlerAutoConfiguration,\
backend.common.cdc.CDCAutoConfiguration,\
backend.common.kafka.streams.KafkaChangeLogTopicAutoConfiguration,\
backend.common.kafka.KafkaPublisher,\
backend.common.kafka.streams.GlobalStoreAutoConfiguration,\
backend.common.health.HealthCheckConfiguration