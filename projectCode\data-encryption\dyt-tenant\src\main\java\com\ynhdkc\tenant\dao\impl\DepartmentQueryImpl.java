package com.ynhdkc.tenant.dao.impl;

import backend.common.util.MybatisUtil;
import backend.common.util.ObjectsUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.ynhdkc.tenant.dao.DepartmentQuery;
import com.ynhdkc.tenant.dao.HospitalQuery;
import com.ynhdkc.tenant.dao.mapper.AppointmentRuleSettingMapper;
import com.ynhdkc.tenant.dao.mapper.DepartmentMapper;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.enums.CategoryType;
import com.ynhdkc.tenant.model.DepartmentVo;
import com.ynhdkc.tenant.model.DepartmentsTreeSearchPageReqDto;
import com.ynhdkc.tenant.model.DepartmentsTreeSearchPageVo;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.RowBounds;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-25 22:10
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class DepartmentQueryImpl implements DepartmentQuery {
    private final DepartmentMapper departmentMapper;
    private final HospitalQuery hospitalQuery;
    private final AppointmentRuleSettingMapper appointmentRuleSettingMapper;
    private final PageVoConvert pageVoConvert;

    @Override
    public Department queryDepartmentById(Long departmentId) {
        return departmentMapper.selectByPrimaryKey(departmentId);
    }

    @Override
    public Page<Department> pageQueryDepartment(DepartmentQueryOption option) {
        try (final Page<Department> page = PageMethod.startPage(option.getCurrentPage(), option.getPageSize())) {
            return page.doSelectPage(() -> departmentMapper.selectByExample(Department.class, sql -> {
                sql.defGroup(condition -> {
                    if (null != option.getTenantId()) {
                        condition.andEqualTo(Department::getTenantId, option.getTenantId());
                    }
                    if (null != option.getHospitalId()) {
                        condition.andEqualTo(Department::getHospitalId, option.getHospitalId());
                    }
                    if (null != option.getHospitalAreaId()) {
                        condition.andEqualTo(Department::getHospitalAreaId, option.getHospitalAreaId());
                    }
                    if (null != option.getParentId()) {
                        condition.andEqualTo(Department::getParentId, option.getParentId());
                    }
                    if (null != option.getId()) {
                        condition.andEqualTo(Department::getId, option.getId());
                    }
                    if (null != option.getName()) {
                        condition.andLike(Department::getName, MybatisUtil.likeLeft(option.getName()));
                    }
                    if (null != option.getShortName()) {
                        condition.andLike(Department::getShortName, MybatisUtil.likeLeft(option.getShortName()));
                    }
                    if (null != option.getThrdpartDepCode()) {
                        condition.andEqualTo(Department::getThrdpartDepCode, option.getThrdpartDepCode());
                    }
                    if (null != option.getFirstLetter()) {
                        condition.andEqualTo(Department::getFirstLetter, MybatisUtil.likeLeft(option.getFirstLetter()));
                    }
                    if (null != option.getSort()) {
                        condition.andEqualTo(Department::getSort, option.getSort());
                    }
                    if (null != option.getRecommended()) {
                        condition.andEqualTo(Department::getRecommended, option.getRecommended());
                    }

                    if (!CollectionUtils.isEmpty(option.getCategory())) {
                        StringBuilder builder = new StringBuilder();
                        option.getCategory().sort(Integer::compareTo);
                        option.getCategory().forEach(category -> builder.append(category).append("%"));
                        String queryPattern = builder.toString();
                        condition.andLike(Department::getCategory, MybatisUtil.likeRight(queryPattern));
                    }

                    if (null != option.getEnabled()) {
                        condition.andEqualTo(Department::getEnabled, option.getEnabled());
                    }

                    if (!CollectionUtils.isEmpty(option.getIncludingIds())) {
                        condition.andIn(Department::getId, option.getIncludingIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getExcludingIds())) {
                        condition.andNotIn(Department::getId, option.getExcludingIds());
                    }
                });

                if (!CollectionUtils.isEmpty(option.getCategoryIn())) {
                    sql.newAndGroup(condition -> option.getCategoryIn().forEach(category -> condition.orLike(Department::getCategory, MybatisUtil.likeLeft(String.valueOf(category)))));
                }

                sql.builder(builder -> builder.orderByDesc(Department::getId));
            }));
        }
    }

    @Override
    public int countByHospitalAreaId(Long hospitalAreaId) {
        return departmentMapper.selectCountByExample2(Department.class, sql -> sql.andEqualTo(Department::getHospitalAreaId, hospitalAreaId));
    }

    @Override
    public List<Department> queryChildrenDepartmentById(Long departmentId) {
        return departmentMapper.selectByExample2(Department.class, sql -> sql.andEqualTo(Department::getParentId, departmentId));
    }


    @Override
    public List<Department> queryDepartments(Long hospitalAreaId, Set<String> departCode) {
        return departmentMapper.selectByExample2(Department.class, sql -> {
            if (null != hospitalAreaId) {
                sql.andEqualTo(Department::getHospitalAreaId, hospitalAreaId);
            }
            if (!CollectionUtils.isEmpty(departCode)) {
                sql.andIn(Department::getThrdpartDepCode, departCode);
            }
        });
    }

    @Override
    public List<Department> queryDepartmentsIn(List<String> hospitalCodes, Collection<String> departmentCodes) {
        return departmentMapper.selectByExample2(Department.class, sql -> {
            if (!CollectionUtils.isEmpty(hospitalCodes)) {
                sql.andIn(Department::getHospitalCode, hospitalCodes);
            }
            if (!CollectionUtils.isEmpty(departmentCodes)) {
                sql.andIn(Department::getThrdpartDepCode, departmentCodes);
            }
        });
    }

    @Override
    public @Nullable List<Department> queryDepartmentsByHospitalAreaId(@NonNull Long hospitalAreaId) {
        return departmentMapper.selectByExample2(Department.class, sql -> sql.andEqualTo(Department::getHospitalAreaId, hospitalAreaId));
    }

    @Override
    public List<Department> queryDepartmentCodeList(String hospitalAreaCode) {
        return departmentMapper.queryDepartmentCodeList(hospitalAreaCode);
    }

    @Override
    public List<Department> queryBy(List<Long> departmentIds) {
        if (CollectionUtils.isEmpty(departmentIds)) {
            return Collections.emptyList();
        }
        return departmentMapper.selectByExample2(Department.class, sql -> sql.andIn(Department::getId, departmentIds));
    }

    @Override
    public List<Department> queryByHospitalIds(@NonNull List<Long> hospitalIds) {
        return departmentMapper.selectByExample2(Department.class, sql -> sql.andIn(Department::getHospitalId, hospitalIds));
    }

    @Override
    public List<Department> queryList(char firstLetter) {
        Example example = new Example(Department.class);
        example.createCriteria().andEqualTo("firstLetter", firstLetter).andEqualTo("levelTag", 1)
                .andNotEqualTo("thrdpartDepCode", "");
        return departmentMapper.selectByExample(example);
    }

    @Override
    public List<Department> queryList(char firstLetter, List<String> hospitalCodeList) {
        Example example = new Example(Department.class);
        example.createCriteria().andEqualTo("firstLetter", firstLetter).andIn("hospitalCode", hospitalCodeList);
        return departmentMapper.selectByExample(example);
    }

    @Override
    public Department queryBy(String hospitalAreaCode, String departmentCode) {
        List<Department> departments = departmentMapper.selectByExample2(Department.class, sql -> {
            sql.andEqualTo(Department::getHospitalCode, hospitalAreaCode);
            sql.andEqualTo(Department::getThrdpartDepCode, departmentCode);
        });
        if (CollectionUtils.isEmpty(departments)) {
            return null;
        }

        return departments.get(0);
    }

    @Override
    public List<Department> queryList(String hospitalAreaCode, String departmentCode) {
        return departmentMapper.selectByExample2(Department.class, sql -> {
            sql.andEqualTo(Department::getHospitalCode, hospitalAreaCode);
            sql.andEqualTo(Department::getThrdpartDepCode, departmentCode);
        });
    }

    @Override
    public Optional<Department> queryDepartmentByCode(String hospitalAreaCode, String departmentCode) {
        return Optional.ofNullable(queryBy(hospitalAreaCode, departmentCode));
    }

    @Override
    public List<Department> queryBy(String hospitalCode) {
        return departmentMapper.selectByExample2(Department.class, sql -> sql.andEqualTo(Department::getHospitalCode, hospitalCode));
    }

    @Override
    public List<Long> queryEnabledDepartmentIds(List<Long> departmentIds) {
        if (CollectionUtils.isEmpty(departmentIds)) {
            return Collections.emptyList();
        }
        return departmentMapper.selectByExample2(Department.class, sql -> {
            sql.andIn(Department::getId, departmentIds);
            sql.andEqualTo(Department::getEnabled, 1);
        }).stream().map(Department::getId).collect(Collectors.toList());
    }

    @Override
    public Department queryShadowDepartment(String hospitalCode, String departmentCode) {
        Example example = new Example(Department.class);
        example.createCriteria().andEqualTo("hospitalCode", hospitalCode)
                .andEqualTo("thrdpartDepCode", departmentCode)
                .andEqualTo("enabled", 0)
                .andEqualTo("parentId", 0);
        example.orderBy("id").desc();

        List<Department> departments = departmentMapper.selectByExampleAndRowBounds(example, new RowBounds(0, 1));
        if (ObjectsUtils.isEmpty(departments)) {
            return null;
        }
        return departments.get(0);
    }

    @Override
    public void save(Department newDepartment) {
        departmentMapper.insertSelective(newDepartment);
    }

    @Override
    public DepartmentsTreeSearchPageVo queyrDepartPageByCondition(DepartmentsTreeSearchPageReqDto request) {
        Page<Department> page = PageHelper.startPage(request.getCurrent(), request.getSize());
        page.doSelectPage(() -> departmentMapper.queryDepartWithCategory(request));
        return pageVoConvert.toPageVo(page, DepartmentsTreeSearchPageVo.class, this::toVo);
    }

    @Override
    public List<Department> queryAllQuickCheckDepartment() {
        return departmentMapper.selectByExample2(Department.class, sql -> {
            sql.andEqualTo(Department::getEnabled, true);
            sql.andEqualTo(Department::getCategory, Lists.newArrayList(CategoryType.QUICK_CHECK.getValue()));
        });
    }

    private DepartmentVo toVo(Department department) {
        DepartmentVo vo = new DepartmentVo();
        vo.setId(department.getId());
        vo.setName(department.getName());
        vo.setThrdpartDepCode(department.getThrdpartDepCode());
        vo.setHospitalAreaId(department.getHospitalAreaId());
        vo.setHospitalId(department.getHospitalId());
        vo.setEnabled(department.getEnabled());
        vo.setSort(department.getSort());
        if (StringUtils.hasText(department.getCategory())) {
            String[] split = department.getCategory().split(",");
            List<Integer> categoryList = Arrays.stream(split).map(Integer::parseInt).collect(Collectors.toList());
            vo.setCategory(categoryList);
        }
        vo.setAddressIntro(department.getAddressIntro());
        vo.setCreateTime(department.getCreateTime());
        vo.setDisplayBgColor(department.getDisplayBgColor());
        vo.setDisplayDepartmentName(department.getDisplayDepartmentName());
        vo.setDisplayFields(department.getDisplayFields());
        vo.setFirstLetter(department.getFirstLetter());
        vo.setHospitalCode(department.getHospitalCode());
//        vo.setLayer(department.getLayer());
        vo.setName(department.getName());
        vo.setParentId(department.getParentId());
        vo.setSort(department.getSort());
        vo.setSourceActivateTime(department.getSourceActivateTime());
        Set<Long> hospitalIds = new HashSet<>(Arrays.asList(department.getHospitalId(), department.getHospitalAreaId()));
        Map<Long, String> hospitalNames = hospitalQuery.queryBy(hospitalIds).stream()
                .collect(Collectors.toMap(Hospital::getId, Hospital::getName));
        vo.setHospitalAreaName(hospitalNames.get(department.getHospitalAreaId()));
        vo.setHospitalName(hospitalNames.get(department.getHospitalId()));
        vo.setTenantId(department.getTenantId());
        return vo;
    }
}
