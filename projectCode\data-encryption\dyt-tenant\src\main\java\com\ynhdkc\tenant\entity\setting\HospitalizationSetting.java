package com.ynhdkc.tenant.entity.setting;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-09 10:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(name = "t_hospital_hospitalization_setting")
public class HospitalizationSetting extends BaseSetting {
    /**
     * 是否开启缴费 0:否 1:是
     */
    private Boolean enablePayment;
    /**
     * 是否支持线上退费 0:否 1:是
     */
    private Boolean supportOnlineRefund;
    /**
     * 是否开启住院信息查询 0:否 1:是
     */
    private Boolean enableInfoQuery;
    /**
     * 已选支付方式
     */
    private String selectedPayments;
    /**
     * 缴费信息
     */
    private String paymentInformation;
}
