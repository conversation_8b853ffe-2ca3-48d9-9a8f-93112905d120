package com.ynhdkc.tenant.dao.mapper;

import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.model.DepartmentsTreeSearchPageReqDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/22 15:08:44
 */
@Mapper
public interface DepartmentMapper extends BaseMapper<Department> {

    @Select("<script>select distinct(thrdpart_dep_code), id from t_hospital_department where hospital_code = #{hospitalCode} and thrdpart_dep_code != '';</script>")
    List<Department> queryDepartmentCodeList(@Param("hospitalCode") String hospitalCode);

    List<Department> queryDepartWithCategory(@Param("option") DepartmentsTreeSearchPageReqDto req);

}
