package backend.common.cdc;

import backend.common.cdc.capture.*;
import backend.common.cdc.dto.CDCHospitalBuilding;
import backend.common.cdc.listener.*;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.KafkaListener;

@Configuration
@ConditionalOnClass({KafkaListener.class, ConsumerRecord.class})
@ConditionalOnProperty(name = "backend.cdc.enabled", havingValue = "true", matchIfMissing = false)
public class CDCAutoConfiguration {

    @Bean
    @ConditionalOnBean(TenantChangeCapture.class)
    public CDCTenantListener cdcTenantListener(TenantChangeCapture capture) {
        return new CDCTenantListener(capture);
    }

    @Bean
    @ConditionalOnBean(DepartmentChangeCapture.class)
    public CDCDepartmentListener cdcDepartmentListener(DepartmentChangeCapture capture) {
        return new CDCDepartmentListener(capture);
    }

    @Bean
    @ConditionalOnBean(DoctorChangeCapture.class)
    public CDCDoctorListener cdcDoctorListener(DoctorChangeCapture capture) {
        return new CDCDoctorListener(capture);
    }

    @Bean
    @ConditionalOnBean(FloorAreaChangeCapture.class)
    public CDCFloorAreaListener cdcFloorAreaListener(FloorAreaChangeCapture capture) {
        return new CDCFloorAreaListener(capture);
    }

    @Bean
    @ConditionalOnBean(HospitalBuildingChangeCapture.class)
    public CDCHospitalBuildingListener cdcHospitalBuildingListener(HospitalBuildingChangeCapture capture) {
        return new CDCHospitalBuildingListener(capture);
    }

    @Bean
    @ConditionalOnBean(HospitalChangeCapture.class)
    public CDCHospitalListener cdcHospitalListener(HospitalChangeCapture capture) {
        return new CDCHospitalListener(capture);
    }
}
