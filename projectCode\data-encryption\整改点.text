租户服务数据存储分类
根据《数据安全法》《个人信息保护法》等法规要求，对提供的数据库表结构进行分析，以下是涉及用户隐私数据的字段分类分级评估结果：
隐私数据字段分类分级表
表名	字段名	字段含义	是否属于隐私数据	分类级别	分类依据说明
t_tenant_user	id_card_no	用户身份证号	是	核心数据	属于敏感个人信息，可唯一标识身份，泄露后可能导致身份冒用、诈骗等风险。
t_tenant_user	phone_number	用户手机号码	是	重要数据	可直接联系自然人，属于敏感联系方式，可能用于骚扰或身份关联。
t_tenant_user	name	帐号名（可能为姓名）	是	重要数据	可能关联自然人身份，属于个人身份标识信息。

表名	字段名	字段含义	是否属于隐私数据	分类级别	分类依据说明
t_recharge_record	patient_name	就诊人姓名	是	重要数据	属于个人身份标识信息，可直接关联自然人身份。
t_recharge_record	jz_card	就诊卡号	是	核心数据	关联个人医疗就诊信息，属于敏感健康标识，泄露可能影响医疗隐私。

表名	字段名	字段含义	是否属于隐私数据	分类级别	分类依据说明
t_tenant	contact_phone_number	租户联系人手机号	是	重要数据	属于联系人敏感信息，可用于联系相关人员。
t_tenant	contact_email	租户联系人邮箱	是	重要数据	属于敏感联系信息，可能用于身份关联或骚扰。
分类分级说明
1.核心数据：包含直接唯一标识个人身份的信息（如身份证号、就诊卡号），此类数据泄露可能导致严重的身份盗用、医疗隐私泄露等风险，需采取最高级别的保护措施（如全生命周期加密、严格访问控制）。
2.重要数据：包括可间接关联个人身份的信息（如姓名、手机号、用户 ID）及敏感联系信息，此类数据泄露可能导致隐私暴露或精准骚扰，需采用加密存储、访问审批等措施。
3.一般数据：表中未涉及此类数据，主要为基础业务配置或机构信息，不涉及个人隐私。

