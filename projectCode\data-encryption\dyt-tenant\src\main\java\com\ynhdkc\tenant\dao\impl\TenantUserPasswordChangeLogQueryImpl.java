package com.ynhdkc.tenant.dao.impl;

import backend.common.exception.BizException;
import com.ynhdkc.tenant.dao.TenantUserPasswordChangeLogMapper;
import com.ynhdkc.tenant.dao.TenantUserPasswordChangeLogQuery;
import com.ynhdkc.tenant.entity.TenantUserPasswordChangeLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-03 16:41
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class TenantUserPasswordChangeLogQueryImpl implements TenantUserPasswordChangeLogQuery {
    private final TenantUserPasswordChangeLogMapper tenantUserPasswordChangeLogMapper;

    @Override
    public List<TenantUserPasswordChangeLog> queryByUserId(Long tenantUserId) {
        return tenantUserPasswordChangeLogMapper.selectByExample(TenantUserPasswordChangeLog.class, sql -> {
            sql.defGroup(condition -> condition.andEqualTo(TenantUserPasswordChangeLog::getTenantUserId, tenantUserId));
            sql.builder(builder -> builder
                    .orderByDesc(TenantUserPasswordChangeLog::getCreateTime)); // Add this line to limit the result set to 3 records.
        });
    }

    @Override
    public TenantUserPasswordChangeLog queryLastByUserId(Long tenantUserId) {
        List<TenantUserPasswordChangeLog> tenantUserPasswordChangeLogs = this.queryByUserId(tenantUserId);
        if (!CollectionUtils.isEmpty(tenantUserPasswordChangeLogs)) {
            return tenantUserPasswordChangeLogs.get(0);
        }
        throw new BizException(HttpStatus.NOT_FOUND, "未查询到当前租户信息");
    }
}
