package backend.encryption.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * AES-GCM加密解密工具类
 * 提供高安全性的对称加密功能，支持认证加密
 * 
 * 特性：
 * - 使用AES-256-GCM算法
 * - 每次加密生成随机IV
 * - 提供完整性验证
 * - Base64编码输出
 * 
 * <AUTHOR> Backend Team
 * @version 1.1-SNAPSHOT
 * @since 2024-06-24
 */
@Slf4j
public class AESGCMUtil {
    
    /**
     * 加密算法
     */
    private static final String ALGORITHM = "AES/GCM/NoPadding";
    
    /**
     * GCM模式的IV长度（字节）
     */
    private static final int GCM_IV_LENGTH = 12;
    
    /**
     * GCM模式的认证标签长度（字节）
     */
    private static final int GCM_TAG_LENGTH = 16;
    
    /**
     * 默认密钥（生产环境应从配置文件或密钥管理系统获取）
     * AES-256需要32字节密钥
     */
    private static final String DEFAULT_SECRET_KEY = "DYT-Backend-Encryption-Key-32Byte";
    
    /**
     * 安全随机数生成器
     */
    private static final SecureRandom SECURE_RANDOM = new SecureRandom();
    
    /**
     * 获取加密密钥
     * 生产环境应从配置文件或密钥管理系统获取
     *
     * @return 密钥字节数组（32字节用于AES-256）
     */
    private static byte[] getSecretKeyBytes() {
        // TODO: 从配置文件或环境变量获取密钥
        String key = DEFAULT_SECRET_KEY;
        byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);

        // 确保密钥长度为32字节（AES-256）
        byte[] result = new byte[32];
        if (keyBytes.length >= 32) {
            System.arraycopy(keyBytes, 0, result, 0, 32);
        } else {
            System.arraycopy(keyBytes, 0, result, 0, keyBytes.length);
            // 如果密钥不足32字节，用0填充
            for (int i = keyBytes.length; i < 32; i++) {
                result[i] = 0;
            }
        }
        return result;
    }
    
    /**
     * 加密字符串
     * 
     * @param plainText 待加密的明文
     * @return 加密后的Base64编码字符串
     * @throws RuntimeException 加密失败时抛出
     */
    public static String encrypt(String plainText) {
        if (!StringUtils.hasText(plainText)) {
            return plainText;
        }
        
        try {
            // 生成随机IV
            byte[] iv = new byte[GCM_IV_LENGTH];
            SECURE_RANDOM.nextBytes(iv);
            
            // 创建密钥
            SecretKeySpec secretKey = new SecretKeySpec(getSecretKeyBytes(), "AES");
            
            // 初始化加密器
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv));
            
            // 加密数据
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            
            // 将IV和加密后的数据合并
            ByteBuffer byteBuffer = ByteBuffer.allocate(iv.length + encryptedBytes.length);
            byteBuffer.put(iv);
            byteBuffer.put(encryptedBytes);
            byte[] encryptedIvAndText = byteBuffer.array();
            
            // 转换为Base64编码
            String result = Base64.getEncoder().encodeToString(encryptedIvAndText);
            log.debug("Successfully encrypted data, original length: {}, encrypted length: {}", 
                plainText.length(), result.length());
            
            return result;
        } catch (Exception e) {
            log.error("Failed to encrypt data", e);
            throw new RuntimeException("加密失败", e);
        }
    }
    
    /**
     * 解密字符串
     * 
     * @param encryptedText 加密的Base64编码字符串
     * @return 解密后的明文
     * @throws RuntimeException 解密失败时抛出
     */
    public static String decrypt(String encryptedText) {
        if (!StringUtils.hasText(encryptedText)) {
            return encryptedText;
        }
        
        try {
            // 解码Base64数据
            byte[] encryptedIvTextBytes = Base64.getDecoder().decode(encryptedText);
            
            // 分离IV和加密数据
            ByteBuffer byteBuffer = ByteBuffer.wrap(encryptedIvTextBytes);
            byte[] iv = new byte[GCM_IV_LENGTH];
            byteBuffer.get(iv);
            byte[] cipherBytes = new byte[byteBuffer.remaining()];
            byteBuffer.get(cipherBytes);
            
            // 创建密钥
            SecretKeySpec secretKey = new SecretKeySpec(getSecretKeyBytes(), "AES");
            
            // 初始化解密器
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv));
            
            // 解密数据
            byte[] decryptedBytes = cipher.doFinal(cipherBytes);
            
            String result = new String(decryptedBytes, StandardCharsets.UTF_8);
            log.debug("Successfully decrypted data, encrypted length: {}, decrypted length: {}", 
                encryptedText.length(), result.length());
            
            return result;
        } catch (Exception e) {
            log.error("Failed to decrypt data", e);
            throw new RuntimeException("解密失败", e);
        }
    }
    
    /**
     * 验证加密解密功能
     * 
     * @param plainText 测试明文
     * @return 是否验证成功
     */
    public static boolean verify(String plainText) {
        try {
            String encrypted = encrypt(plainText);
            String decrypted = decrypt(encrypted);
            return plainText.equals(decrypted);
        } catch (Exception e) {
            log.error("Verification failed", e);
            return false;
        }
    }
}
