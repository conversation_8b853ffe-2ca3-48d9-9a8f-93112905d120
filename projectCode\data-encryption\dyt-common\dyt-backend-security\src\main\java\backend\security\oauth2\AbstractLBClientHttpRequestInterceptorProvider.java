package backend.security.oauth2;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.cloud.client.loadbalancer.LoadBalancerInterceptor;
import org.springframework.cloud.client.loadbalancer.RetryLoadBalancerInterceptor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.http.client.ClientHttpRequestInterceptor;

public abstract class AbstractLBClientHttpRequestInterceptorProvider implements ApplicationContextAware {
    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    public ClientHttpRequestInterceptor getLBClientHttpRequestInterceptor() {
        ClientHttpRequestInterceptor loadBalancerInterceptor;
        try {
            loadBalancerInterceptor = this.applicationContext.getBean(LoadBalancerInterceptor.class);
        } catch (NoSuchBeanDefinitionException e) {
            loadBalancerInterceptor = this.applicationContext.getBean(RetryLoadBalancerInterceptor.class);
        }
        return loadBalancerInterceptor;
    }
}
