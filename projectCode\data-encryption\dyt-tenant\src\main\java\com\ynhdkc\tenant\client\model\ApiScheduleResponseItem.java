package com.ynhdkc.tenant.client.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ApiScheduleResponseItem {
    @JsonProperty("id")
    private Long id = null;

    @JsonProperty("hospital_id")
    private Long hospitalId = null;

    @JsonProperty("hospital_area_id")
    private Long hospitalAreaId = null;

    @JsonProperty("hospital_name")
    private String hospitalName = null;

    @JsonProperty("hospital_area_name")
    private String hospitalAreaName = null;

    @JsonProperty("department_id")
    private Long departmentId = null;

    @JsonProperty("department_name")
    private String departmentName = null;

    @JsonProperty("doctor_id")
    private Long doctorId = null;

    @JsonProperty("doctor_name")
    private String doctorName = null;

    @JsonProperty("schedule_id")
    private String scheduleId = null;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date schDate;

    @JsonProperty("time_type")
    private Integer timeType = null;

    @JsonProperty("time_type_text")
    private String timeTypeText = null;

    @JsonProperty("start_time")
    private String startTime = null;

    @JsonProperty("end_time")
    private String endTime = null;

    @JsonProperty("total_fee")
    private BigDecimal totalFee = null;

    @JsonProperty("total_fee_format")
    private String totalFeeFormat = null;

    @JsonProperty("max_treat_num")
    private Integer maxTreatNum = null;

    @JsonProperty("used_num")
    private Integer usedNum = null;

    @JsonProperty("src_num")
    private Integer srcNum = null;

    @JsonProperty("time_part")
    private Integer timePart = null;

    @JsonProperty("schedule_info")
    private String scheduleInfo = null;

    @JsonProperty("status")
    private Integer status = null;

    @JsonProperty("show_sch_date")
    private Integer showSchDate = null;

    @JsonProperty("create_by")
    private String createBy = null;

    @JsonProperty("edit_by")
    private String editBy = null;

    @JsonProperty("create_time")
    private String createTime = null;

    @JsonProperty("update_time")
    private String updateTime = null;

    @JsonProperty("ghf")
    private BigDecimal ghf = null;

    @JsonProperty("zjf")
    private BigDecimal zjf = null;

    @JsonProperty("zlf")
    private BigDecimal zlf = null;

}

