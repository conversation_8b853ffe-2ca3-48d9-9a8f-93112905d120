server:
  port: 8080

spring:
  application:
    name: database-encryption-poc

  # H2 Database Configuration (for testing)
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password:
    schema: classpath:schema.sql
    initialization-mode: always

  # H2 Console (for debugging)
  h2:
    console:
      enabled: true
      path: /h2-console

  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.H2Dialect



# Logging Configuration
logging:
  level:
    com.example.encryption: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# MyBatis Configuration
mybatis:
  configuration:
    map-underscore-to-camel-case: true
    use-generated-keys: true
    default-executor-type: reuse
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  type-handlers-package: com.example.encryption.mybatis.handler

# Custom encryption configuration
encryption:
  # In production, this should be loaded from environment variables or secure vault
  secret-key: "MySecretKey12345"  # 16 bytes for AES-128, 32 bytes for AES-256

  # 迁移策略配置 - 支持通过配置文件统一管理，便于快速回滚
  migration:
    # 全局默认策略
    default-strategy: PLAINTEXT_PRIORITY
    # 是否启用全局策略覆盖（用于快速回滚）
    # 当为true时，所有字段都使用default-strategy，忽略字段级别配置
    # 当为false时，优先使用字段级别的策略配置
    enable-global-override: false
    # 具体字段策略配置
    strategies:
      phone: DIRECT_ENCRYPT        # 手机号：传统直接加密
      email: PLAINTEXT_PRIORITY    # 邮箱：明文优先（迁移初期）
      idCard: SHADOW_PRIORITY      # 身份证：影子字段优先（迁移中期）
      realName: SHADOW_ONLY        # 姓名：仅影子字段（迁移完成）

  # 加密算法配置 - 支持多种加密算法
  algorithms:
    # 默认加密算法
    default: AES
    # AES算法配置
    aes:
      enabled: true
      key-size: 256
      mode: GCM
      description: "高级加密标准，性能优秀，广泛使用"
    # 国密SM2算法配置
    sm2:
      enabled: true
      description: "国密椭圆曲线公钥密码算法，符合国家安全要求"
      # 注意：生产环境中密钥应从安全存储系统获取，不应硬编码在配置文件中
      public-key: "048356e642a40ebd18d29ba3532fbd9f3bbee8f027c3f6f39a5ba2f870369f9988981f5efe55d1f06d3e9b7b8e7a8b5c6d4e3f2a1b9c8d7e6f5a4b3c2d1e0f9e8"
      private-key: "3945208f7b2144b13f36e38ac6d39f95889393692860b51a42fb81ef4df7c5b8"
    # 国密SM4算法配置（预留）
    sm4:
      enabled: false
      description: "国密对称加密算法，开发中"
      note: "SM4算法实现正在开发中"
