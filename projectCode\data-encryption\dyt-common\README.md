# backend-common

A common module used by the Backend Platform.

## Backend Platform Java Practice

Ref [Backend Platform Java Practice](./docs/backend-java-practice.md)

## Requirements

#### Maven Settings

1. Open your local maven settings, which is usually `~/.m2/settings.xml`.
1. Add a new profile to keep the nexus information.
    ```xml
      <profile>
        <id>easeservice-sdk</id>
        <repositories>
          <repository>
            <id>megaease-nexus</id>
            <url>https://dev.megaease.cn/nexus/repository/maven-megaease/</url>
            <snapshots>
              <updatePolicy>always</updatePolicy>
            </snapshots>
          </repository>
        </repositories>
      </profile>
    ```
1. Active this profile.
    ```xml
    <activeProfiles>
      <activeProfile>easeservice-sdk</activeProfile>
    </activeProfiles>
    ```

## Features

### Enable backend-common module

Add dependencies to `pom.xml`.

```xml

<project>
    <parent>
        <artifactId>backend-common-parent</artifactId>
        <groupId>com.megaease.backend</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <dependencies>
        <dependency>
            <groupId>com.megaease.backend</groupId>
            <artifactId>backend-common</artifactId>
        </dependency>
    </dependencies>
</project>

```

### Enable application to be protected as OAuth 2.0 Resource Server

1. Add needed dependencies to `pom.xml`.

```xml

<dependencies>
    <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-config</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-oauth2-jose</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-oauth2-resource-server</artifactId>
    </dependency>
</dependencies>
```

2. Add a java configuration class for security.

```java

@EnableWebSecurity
@EnableConfigurationProperties(BackendOAuth2ResourceServerProperties.class)
public class SecurityConfiguration extends WebSecurityConfigurerAdapter {

    @Override
    public void configure(WebSecurity web) throws Exception {
        BackendOAuth2ResourceServerUtils.initializeOAuth2ResourceServer(web, getApplicationContext());
    }
   
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        BackendOAuth2ResourceServerUtils.initializeOAuth2ResourceServer(http);
    }

    @Bean
    LoadBalanceJwtDecoderFactoryBean loadBalanceJwtDecoderFactoryBean() {
        return new LoadBalanceJwtDecoderFactoryBean();
    }
    
    @EnableGlobalMethodSecurity(prePostEnabled = true)
    public static class MethodSecurityConfiguration extends BackendMethodSecurityConfiguration {
    }
}
```
3. Update Spring Boot configuration file.

```yaml
spring.security.oauth2.resourceserver.jwt:
  jwks-service-discovery: false  # set true when the host of jwk-set-uri is a service name under service discovery
  jwk-set-uri: http://backend-control/.well-known/jwks.json
backend.security:
  base-path-pattern: /v1/app-name/** # configure the path to be protected
  public-paths: # configure paths that don't require authentication
    - ant-pattern: /v1/app-name/verification-codes
      method: [ POST ]
    - ant-pattern: /v1/app-name/validate
```

### Using @BackendSecurityRequired and security manifest

When using `@BackendSecurityRequired`, you need to register the used scopes and authorities in the security manifest,
otherwise the application will start failure.  
The security manifest file is `src/main/resources/security-manifest.json`.  
For example, if you use `@BackendSecurityRequired(scope = "rpc", value = "tenant.device:read")` in your Controller,
your security manifest will look like the following:

```json
{
  "scopes": [
    {
      "name": "rpc",
      "description": "服务间内部访问"
    }
  ],
  "authorities": [
    {
      "name": "tenant.device:read",
      "description": "租户模块设备的读取权限"
    }
  ]
}
```

### Internal Access via OAuth 2.0 Client Credentials Grant Type

1. Add dependencies to `pom.xml`.

```xml

<dependencies>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-oauth2-client</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-openfeign</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
    </dependency>
</dependencies>
```

2. Update Spring Boot configuration file.

```yaml
spring.security.oauth2.client:
  registration:
    backend-platform-client:
      provider: spring
      client-id: backend-platform-client
      client-secret: Backend!38yXw&6
      authorization-grant-type: client_credentials
      scope: rpc
      # The test token is valid until 2023.5.9
      #test-token: eyJraWQiOiIyMDIxMDgyNjIwMTMyMSIsInR5cCI6IkpXVCIsImFsZyI6IlJTMjU2In0.**********************************************************************************************************************************************************************************************************.eB6ntCfze2cIJWCDUulzNdONHto6dzZQNNPZ9tr5js4pJ51xvOakyjFtyY9yLceyjzbPBkyj4uiycVhd5oiLEZgzM1awL_UH8gkxMJW93rHqCcTOzGSP3VyjgaKgWHjyjcPWm2ddzlPlYsajVXbDpLje4-DsT1-Z4OivBUtx_dM43QKbA2GBMEOIqF8QC5BtUmmWPu94zxdWknAMONbHCahn68ggNIGujgqnBm9zif-xP-jCFyNnAMOJjGU9RXFsxiy5RTIHfGqxN--cKkvjfQs1Z0E4rlDu-CKXtthKW5cFa5lizCz5Ju2huTC2cLaI1yfTTQYzNfdtdgwa3o7idw
  provider-service-discovery: false # set true when the host of token-uri is a service name under service discovery
  provider:
    spring:
      token-uri: http://backend-control/v1/control/client-token
      #token-uri: http://gateway-ip:port/v1/control/client-token
```

3. Update your security configuration class.

```java
public class SecurityConfiguration {
    @Bean
    LoadBalanceOAuth2AuthorizedClientManagerFactoryBean loadBalanceOAuth2AuthorizedClientManager() {
        return new LoadBalanceOAuth2AuthorizedClientManagerFactoryBean();
    }
}
```

4. Specify custom configuration for `@FeignClient`
   . `@FeignClient(value = "target-protected-service", configuration = CustomClientConfig.class)`.
5. Add interceptor to `CustomClientConfig`.

```java
public class CustomClientConfig {
    @Bean
    public RequestInterceptor basicAuthRequestInterceptor() {
        return new OAuth2AuthorizedClientRequestInterceptor("backend-platform-client");
    }
}
```

> If you enable the test token in the Spring Boot configuration file, you don't need the backend-control module.

### Logback Kafka Appender

By default, we send the output to Kafka via Logback's kafka appender. Users can use the following property to disable
it.

```yaml
backend.kafka-appender.enabled: false
```

### Data sync from other microservices

If you have a data processing application which need the student data from student-service, you can use this framework to sync the data.

- Declare a topic named `changelong-student` and config it `cleanup.policy=compact`.
- Generate and send CRUD records to the `changelong-student` topic when the student data in student-service changing. 
  Key is the id of student. Value is the JSON data of the student. Set value to null when deleting students.
- Change your data processing application to consumer the `changelong-student` topic and convert the data to a store.
- Add @EnableKafkaStreams to your application's entry class.
- You can inject the store to your other classes and use it.

You can find the following demonstration codes in the example project.
```java
@Configuration
public class KafkaTopicConfig {
   public static final String CHANGELOG_STUDENT = "changelog_student";

   @Bean
   NewTopic studentChangelogTopic() {
      return TopicBuilder.name(CHANGELOG_STUDENT).compact().build();
   }
}
@Configuration
public class AppConfig {


   @Bean
   public StudentGlobalStore studentGlobalStore() {
      return new StudentGlobalStore();
   }

   @Bean
   public StreamsBuilderFactoryBeanCustomizer streamsBuilderFactoryBean(StudentGlobalStore studentGlobalStore) {
      return new CallbackStreamsBuilderFactoryBeanCustomizer(Arrays.asList(studentGlobalStore));
   }
}
public class StudentGlobalStore extends GlobalStoreKafkaStreamsCallback<Long, Student> {
   public static final JsonSerde<Student> STUDENT_JSON_SERDE = new JsonSerde<>(Student.class);
   private ReadOnlyKeyValueStore<Long, Student> store;

   @Override
   public void processUpdate(Long key, Student update, Student old) {}

   @Override
   public String storeName() {return "student-global-store";}

   @Override
   public String sourceTopic() {return KafkaTopicConfig.CHANGELOG_STUDENT;}

   @Override
   public JsonSerde<Student> valueSerde() {return STUDENT_JSON_SERDE;}

   @Override
   public Serde<Long> keySerde() {return Serdes.Long();}

   @Override
   public void initialize(ReadOnlyKeyValueStore<Long, Student> store) {this.store = store;}

   public Optional<Student> getStudent(Long id) {return Optional.ofNullable(store).map(e -> e.get(id));}
}

@EnableKafkaStreams
public class ExampleApplication {}
```

**Don't forget to add the maven dependencies**
```xml
<dependency>
   <groupId>org.springframework.kafka</groupId>
   <artifactId>spring-kafka</artifactId>
</dependency>
<dependency>
   <groupId>org.apache.kafka</groupId>
   <artifactId>kafka-streams</artifactId>
</dependency>
```

### Change Data Capture

#### Debezium

Ref [Debezium Usage](./docs/debezium-usage.md)

#### Subscribe Data Changes

目前如果需要使用Debezium 来获得同步的数据，在部署了Debezium 的基础上，首先需要打开配置`backend.cdc.enabled: true` 并且在configuration中定义你需要的entity capture 实现，
这样你便可以在entity capture的实现中完成相应的业务逻辑。

- `TenantChangeCapture` is used to listen Tenant changes. [Example](dyt-backend-common/src/main/java/backend/common/cdc/capture/TenantChangeCapture.java)

> Kafka configuration(for consumer) is required when using this feature.


