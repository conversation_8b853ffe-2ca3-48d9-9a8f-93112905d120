FROM megaease/easeimg-javabuild:latest AS builder

ARG      REPOSITORY1
ARG      REPOSITORY2
ARG      REPOSITORY3
ARG      MIRROR1
ARG      MIRROR2
ARG      MIRROR3
ARG      SERVER1
ARG      SERVER2
ARG      SERVER3
ARG      PROJECT_NAME

COPY     pom /${PROJECT_NAME}

WORKDIR  /${PROJECT_NAME}


RUN 	 rewrite-settings.sh && \
         cd /${PROJECT_NAME}/ && \
         mvn de.qaware.maven:go-offline-maven-plugin:1.2.5:resolve-dependencies -B

COPY     full /${PROJECT_NAME}

RUN      mkdir out \
         && /${PROJECT_NAME}/resources/scripts/build-app.sh
