package com.ynhdkc.tenant.dao;/**
 * <AUTHOR>
 * @date 2024/07/23/11:28
 */

import com.ynhdkc.tenant.dao.mapper.DesensitizationWhiteListMapper;
import com.ynhdkc.tenant.entity.DesensitizationWhiteList;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @description
 * @date 2024/7/23 11:28
 **/
@Repository
@RequiredArgsConstructor
public class DesensitizationWhiteListRepositoryImpl implements DesensitizationWhiteListRepository {
    private final DesensitizationWhiteListMapper desensitizationWhiteListMapper;

    @Override
    public void create(DesensitizationWhiteList desensitizationWhiteList) {
        desensitizationWhiteListMapper.save(desensitizationWhiteList);
    }

    @Override
    public void update(DesensitizationWhiteList desensitizationWhiteList) {
        desensitizationWhiteListMapper.updateByPrimaryKeySelective(desensitizationWhiteList);
    }

    @Override
    public void delete(Long desensitizationWhiteListId) {
        desensitizationWhiteListMapper.deleteByPrimaryKey(desensitizationWhiteListId);
    }
}
