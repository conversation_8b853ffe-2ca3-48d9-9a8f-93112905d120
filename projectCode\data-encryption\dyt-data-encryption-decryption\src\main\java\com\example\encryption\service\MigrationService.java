package com.example.encryption.service;

import com.example.encryption.entity.MigrationUser;
import com.example.encryption.migration.ShadowFieldMigrationManager;
import com.example.encryption.repository.MigrationUserRepository;
import com.example.encryption.util.AESGCMUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 数据迁移服务
 * 提供渐进式影子字段迁移的核心功能
 */
@Service
@Transactional
public class MigrationService {

    private static final Logger logger = LoggerFactory.getLogger(MigrationService.class);

    @Autowired
    private MigrationUserRepository userRepository;

    @Autowired
    private ShadowFieldMigrationManager migrationManager;

    /**
     * 创建迁移用户
     */
    public MigrationUser createUser(MigrationUser user) {
        logger.info("Creating migration user: {}", user.getUsername());

        MigrationUser savedUser = userRepository.save(user);
        logger.info("Created migration user with ID: {}", savedUser.getId());

        return savedUser;
    }

    /**
     * 更新迁移用户
     */
    public MigrationUser updateUser(MigrationUser user) {
        logger.info("Updating migration user: {}", user.getId());

        MigrationUser savedUser = userRepository.save(user);
        logger.info("Updated migration user with ID: {}", savedUser.getId());

        return savedUser;
    }

    /**
     * 根据ID获取用户
     */
    @Transactional(readOnly = true)
    public Optional<MigrationUser> getUserById(Long id) {
        logger.debug("Getting migration user by ID: {}", id);

        return userRepository.findById(id);
    }

    /**
     * 根据用户名获取用户
     */
    @Transactional(readOnly = true)
    public Optional<MigrationUser> getUserByUsername(String username) {
        logger.debug("Getting migration user by username: {}", username);

        return userRepository.findByUsername(username);
    }

    /**
     * 获取所有用户
     */
    @Transactional(readOnly = true)
    public List<MigrationUser> getAllUsers() {
        logger.debug("Getting all migration users");

        return userRepository.findAll();
    }

    /**
     * 批量迁移用户数据到指定版本
     */
    public MigrationResult migrateUsersToVersion(Integer targetVersion) {
        logger.info("Starting migration to version: {}", targetVersion);

        List<MigrationUser> usersToMigrate = userRepository.findUsersNeedingMigration(targetVersion);
        MigrationResult result = new MigrationResult();
        result.setTargetVersion(targetVersion);
        result.setTotalUsers(usersToMigrate.size());

        for (MigrationUser user : usersToMigrate) {
            try {
                migrateUserToVersion(user, targetVersion);
                result.incrementSuccessCount();
                logger.debug("Successfully migrated user: {}", user.getId());
            } catch (Exception e) {
                result.incrementFailureCount();
                result.addError(user.getId(), e.getMessage());
                logger.error("Failed to migrate user: {}", user.getId(), e);
            }
        }

        logger.info("Migration completed. Success: {}, Failures: {}",
            result.getSuccessCount(), result.getFailureCount());

        return result;
    }

    /**
     * 回滚用户数据到指定版本
     */
    public MigrationResult rollbackUsersToVersion(Integer targetVersion) {
        logger.info("Starting rollback to version: {}", targetVersion);

        List<MigrationUser> usersToRollback = userRepository.findAll().stream()
            .filter(user -> user.getDataVersion() > targetVersion)
            .collect(java.util.stream.Collectors.toList());

        MigrationResult result = new MigrationResult();
        result.setTargetVersion(targetVersion);
        result.setTotalUsers(usersToRollback.size());

        for (MigrationUser user : usersToRollback) {
            try {
                rollbackUserToVersion(user, targetVersion);
                result.incrementSuccessCount();
                logger.debug("Successfully rolled back user: {}", user.getId());
            } catch (Exception e) {
                result.incrementFailureCount();
                result.addError(user.getId(), e.getMessage());
                logger.error("Failed to rollback user: {}", user.getId(), e);
            }
        }

        logger.info("Rollback completed. Success: {}, Failures: {}",
            result.getSuccessCount(), result.getFailureCount());

        return result;
    }

    /**
     * 获取迁移统计信息
     */
    @Transactional(readOnly = true)
    public MigrationStatistics getMigrationStatistics() {
        MigrationStatistics stats = new MigrationStatistics();

        // 统计迁移状态
        List<Object[]> statusCounts = userRepository.countByMigrationStatus();
        Map<MigrationUser.MigrationStatus, Long> statusMap = new HashMap<>();
        for (Object[] row : statusCounts) {
            statusMap.put((MigrationUser.MigrationStatus) row[0], (Long) row[1]);
        }
        stats.setStatusCounts(statusMap);

        // 统计数据版本
        List<Object[]> versionCounts = userRepository.countByDataVersion();
        Map<Integer, Long> versionMap = new HashMap<>();
        for (Object[] row : versionCounts) {
            versionMap.put((Integer) row[0], (Long) row[1]);
        }
        stats.setVersionCounts(versionMap);

        return stats;
    }



    /**
     * 迁移单个用户到指定版本
     */
    private void migrateUserToVersion(MigrationUser user, Integer targetVersion) {
        user.setDataVersion(targetVersion);
        user.setMigrationStatus(MigrationUser.MigrationStatus.MIGRATED);

        userRepository.save(user);
    }

    /**
     * 回滚单个用户到指定版本
     */
    private void rollbackUserToVersion(MigrationUser user, Integer targetVersion) {
        user.setDataVersion(targetVersion);
        user.setMigrationStatus(MigrationUser.MigrationStatus.ROLLBACK);

        // 根据目标版本调整数据格式
        if (targetVersion == 1) {
            // 回滚到版本1，恢复明文存储
            if (StringUtils.hasText(user.getEmailEncrypted())) {
                user.setEmail(AESGCMUtil.decrypt(user.getEmailEncrypted()));
                user.setEmailEncrypted(null);
            }

            if (StringUtils.hasText(user.getIdCardEncrypted())) {
                user.setIdCard(AESGCMUtil.decrypt(user.getIdCardEncrypted()));
                user.setIdCardEncrypted(null);
            }

            if (StringUtils.hasText(user.getRealNameEncrypted())) {
                user.setRealName(AESGCMUtil.decrypt(user.getRealNameEncrypted()));
                user.setRealNameEncrypted(null);
            }
        }

        userRepository.save(user);
    }

    /**
     * 迁移结果
     */
    public static class MigrationResult {
        private Integer targetVersion;
        private Integer totalUsers;
        private Integer successCount = 0;
        private Integer failureCount = 0;
        private Map<Long, String> errors = new HashMap<>();

        // Getters and setters
        public Integer getTargetVersion() { return targetVersion; }
        public void setTargetVersion(Integer targetVersion) { this.targetVersion = targetVersion; }

        public Integer getTotalUsers() { return totalUsers; }
        public void setTotalUsers(Integer totalUsers) { this.totalUsers = totalUsers; }

        public Integer getSuccessCount() { return successCount; }
        public void incrementSuccessCount() { this.successCount++; }

        public Integer getFailureCount() { return failureCount; }
        public void incrementFailureCount() { this.failureCount++; }

        public Map<Long, String> getErrors() { return errors; }
        public void addError(Long userId, String error) { this.errors.put(userId, error); }
    }

    /**
     * 迁移统计信息
     */
    public static class MigrationStatistics {
        private Map<MigrationUser.MigrationStatus, Long> statusCounts = new HashMap<>();
        private Map<Integer, Long> versionCounts = new HashMap<>();

        // Getters and setters
        public Map<MigrationUser.MigrationStatus, Long> getStatusCounts() { return statusCounts; }
        public void setStatusCounts(Map<MigrationUser.MigrationStatus, Long> statusCounts) { this.statusCounts = statusCounts; }

        public Map<Integer, Long> getVersionCounts() { return versionCounts; }
        public void setVersionCounts(Map<Integer, Long> versionCounts) { this.versionCounts = versionCounts; }
    }
}
