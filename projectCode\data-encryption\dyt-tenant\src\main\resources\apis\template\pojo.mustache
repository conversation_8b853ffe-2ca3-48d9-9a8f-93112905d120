/**
 * {{#description}}{{.}}{{/description}}{{^description}}{{classname}}{{/description}}
 */
{{#description}}
@ApiModel(description = "{{{description}}}")
{{/description}}
{{#useBeanValidation}}
@Validated
{{/useBeanValidation}}
{{>generatedAnnotation}}
{{#discriminator}}
{{>typeInfoAnnotation}}
{{/discriminator}}
{{>xmlAnnotation}}
{{#notNullJacksonAnnotation}}
@JsonInclude(JsonInclude.Include.NON_NULL)
{{/notNullJacksonAnnotation}}
{{#ignoreUnknownJacksonAnnotation}}
@JsonIgnoreProperties(ignoreUnknown = true)
{{/ignoreUnknownJacksonAnnotation}}
{{#additionalModelTypeAnnotations}}{{{.}}}
{{/additionalModelTypeAnnotations}}
public class {{classname}} {{#parent}}extends {{{parent}}}{{/parent}} {{#serializableModel}}implements Serializable{{/serializableModel}} {
{{#serializableModel}}
  private static final long serialVersionUID = 1L;

{{/serializableModel}}
  {{#vars}}
    {{#isEnum}}
    {{^isContainer}}
{{>enumClass}}
    {{/isContainer}}
    {{/isEnum}}
    {{#items.isEnum}}
      {{#items}}
      {{^isContainer}}
{{>enumClass}}
      {{/isContainer}}
      {{/items}}
    {{/items.isEnum}}
  {{#jackson}}
  @JsonProperty("{{baseName}}"){{#withXml}}
  {{^isXmlWrapped}}
  @JacksonXmlProperty({{#isXmlAttribute}}isAttribute = true, {{/isXmlAttribute}}{{#xmlNamespace}}namespace="{{xmlNamespace}}", {{/xmlNamespace}}localName = "{{#xmlName}}{{xmlName}}{{/xmlName}}{{^xmlName}}{{baseName}}{{/xmlName}}")
  {{/isXmlWrapped}}
  {{#isXmlWrapped}}
  // Is a container wrapped={{isXmlWrapped}}
  // items.xmlName={{items.xmlName}}
  @JacksonXmlProperty({{#xmlNamespace}}namespace="{{xmlNamespace}}", {{/xmlNamespace}}localName = "{{#items.xmlName}}{{items.xmlName}}{{/items.xmlName}}{{^items.xmlName}}{{items.baseName}}{{/items.xmlName}}")
  @JacksonXmlElementWrapper(useWrapping = {{isXmlWrapped}}, {{#xmlNamespace}}namespace="{{xmlNamespace}}", {{/xmlNamespace}}localName = "{{#xmlName}}{{xmlName}}{{/xmlName}}{{^xmlName}}{{baseName}}{{/xmlName}}")
  {{/isXmlWrapped}}
  {{/withXml}}{{/jackson}}
  {{#gson}}
  @SerializedName("{{baseName}}")
  {{/gson}}
  {{#vendorExtensions.x-data-desensitized}}
  @PrivacyEncrypt({{#vendorExtensions.x-desensitized-type}}type=PrivacyType.{{vendorExtensions.x-desensitized-type}}, {{/vendorExtensions.x-desensitized-type}}{{#vendorExtensions.x-desensitized-permission}}permission="{{vendorExtensions.x-desensitized-permission}}", {{/vendorExtensions.x-desensitized-permission}}{{#vendorExtensions.x-desensitized-description}}description="{{vendorExtensions.x-desensitized-description}}"{{/vendorExtensions.x-desensitized-description}})
  {{/vendorExtensions.x-data-desensitized}}
  {{#isContainer}}
  {{#useBeanValidation}}@Valid{{/useBeanValidation}}
  private {{{datatypeWithEnum}}} {{name}}{{#required}} = {{{defaultValue}}}{{/required}}{{^required}} = null{{/required}};
  {{/isContainer}}
  {{^isContainer}}
  private {{{datatypeWithEnum}}} {{name}} = {{{defaultValue}}};
  {{/isContainer}}

  {{/vars}}
  {{#vars}}
  public {{classname}} {{name}}({{{datatypeWithEnum}}} {{name}}) {
    this.{{name}} = {{name}};
    return this;
  }
  {{#isListContainer}}

  public {{classname}} add{{nameInCamelCase}}Item({{{items.datatypeWithEnum}}} {{name}}Item) {
    {{^required}}
    if (this.{{name}} == null) {
      this.{{name}} = {{{defaultValue}}};
    }
    {{/required}}
    this.{{name}}.add({{name}}Item);
    return this;
  }
  {{/isListContainer}}
  {{#isMapContainer}}

  public {{classname}} put{{nameInCamelCase}}Item(String key, {{{items.datatypeWithEnum}}} {{name}}Item) {
    {{^required}}
    if (this.{{name}} == null) {
      this.{{name}} = {{{defaultValue}}};
    }
    {{/required}}
    this.{{name}}.put(key, {{name}}Item);
    return this;
  }
  {{/isMapContainer}}

  /**
  {{#description}}
   * {{{description}}}
  {{/description}}
  {{^description}}
   * Get {{name}}
  {{/description}}
  {{#minimum}}
   * minimum: {{minimum}}
  {{/minimum}}
  {{#maximum}}
   * maximum: {{maximum}}
  {{/maximum}}
   * @return {{name}}
  **/
 {{#vendorExtensions.extraAnnotation}}
  {{{vendorExtensions.extraAnnotation}}}
  {{/vendorExtensions.extraAnnotation}}
  @ApiModelProperty({{#example}}example = "{{{example}}}", {{/example}}{{#required}}required = {{required}}, {{/required}}{{#isReadOnly}}readOnly = {{{isReadOnly}}}, {{/isReadOnly}}value = "{{{description}}}")
{{#useBeanValidation}}{{>beanValidation}}{{/useBeanValidation}}  public {{{datatypeWithEnum}}} {{#isBoolean}}is{{/isBoolean}}{{getter}}() {
    return {{name}};
  }

  public void {{setter}}({{{datatypeWithEnum}}} {{name}}) {
    this.{{name}} = {{name}};
  }

  {{/vars}}

  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }{{#hasVars}}
    {{classname}} {{classVarName}} = ({{classname}}) o;
    return {{#vars}}Objects.equals(this.{{name}}, {{classVarName}}.{{name}}){{#hasMore}} &&
        {{/hasMore}}{{/vars}}{{#parent}} &&
        super.equals(o){{/parent}};{{/hasVars}}{{^hasVars}}
    return super.equals(o);{{/hasVars}}
  }

  @Override
  public int hashCode() {
    return Objects.hash({{#vars}}{{name}}{{#hasMore}}, {{/hasMore}}{{/vars}}{{#parent}}{{#hasVars}}, {{/hasVars}}super.hashCode(){{/parent}});
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class {{classname}} {\n");
    {{#parent}}sb.append("    ").append(toIndentedString(super.toString())).append("\n");{{/parent}}
    {{#vars}}sb.append("    {{name}}: ").append(toIndentedString({{name}})).append("\n");
    {{/vars}}sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
