package com.ynhdkc.tenant.dao;

import backend.common.util.BaseQueryOption;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.entity.Hospital;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-25 21:46
 */
public interface HospitalQuery {
    Page<Hospital> pageQueryHospitalWithCategory(HospitalQueryOption option);

    Page<Hospital> pageQueryHospital(HospitalQueryOption option);

    Hospital queryHospitalById(Long hospitalId);

    Hospital queryHospitalById2(Long hospitalId);

    List<Hospital> queryBy(Set<Long> hospitalIds);

    @Nullable
    List<Hospital> queryBy(List<Long> hospitalIds, List<Long> hospitalAreaIds);

    List<Long> queryHisHospital();

    List<Long> queryHisHospital(String hospitalCode);

    Optional<Hospital> queryHospitalByCode(String hospitalCode);

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Accessors(chain = true)
    class HospitalQueryOption extends BaseQueryOption {
        @Setter(AccessLevel.NONE)
        private Integer hospitalTypeTag = 0;
        private Long id;
        private Long tenantId;
        private String hospitalCode;
        private String name;
        private String levelDictValue;
        private List<Integer> category;
        private Integer status;
        private Date startCreateTime;
        private Date endCreateTime;
        private Collection<Long> includeIds;
        private Collection<Long> excludeIds;
        private Collection<Long> includeTenantIds;
        private Collection<Long> excludeTenantIds;
        private Collection<Integer> excludeCategory;

        public HospitalQueryOption(Integer currentPage, Integer pageSize) {
            super(currentPage, pageSize);
        }
    }
}
