package com.ynhdkc.tenant.util;

import backend.security.service.BackendClientUserService;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 预生产环境域名白名单工具类
 *
 * <AUTHOR>
 * @date 2024/4/10 10:27
 */
@Component
@Slf4j
public class TestDomainWhiteListUtil {

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private HttpServletRequest httpServletRequest;

    @Resource
    BackendClientUserService backendClientUserService;

    private static final String TEST_DOMAIN_WHITELIST_KEY = "test-domain-whitelist-key";
    private static final String TEST_DOMAIN_HOST_KEY = "origin";

    /**
     * 预生产域名判断
     */
    public boolean isTestDomain() {
        String domain = (String) redisTemplate.opsForHash().get(TEST_DOMAIN_WHITELIST_KEY, TEST_DOMAIN_HOST_KEY);
        String httpDomain = httpServletRequest.getHeader(TEST_DOMAIN_HOST_KEY);
        if(StrUtil.isNotBlank(domain) && domain.contains("://")){
            domain = domain.split("://")[1];
        }
        log.info("tenant 预生产域名判断 test-domain：{},http-domain:{}",domain,httpDomain);
        if (StrUtil.isNotBlank(domain) && StrUtil.isNotBlank(httpDomain)) {
            return httpDomain.contains(domain);
        }
        return false;
    }

    public boolean isWhiteList(){
        String token = TokenUtil.getToken(httpServletRequest);
        if(StrUtil.isBlank(token)){
            return false;
        }
        Long currentUserId = backendClientUserService.getCurrentUserIdFromJwt(token);
        log.info("tenant 预生产域名判断成功,判断白名单,currentUserId:{}", currentUserId);
        return redisTemplate.opsForHash().hasKey(TEST_DOMAIN_WHITELIST_KEY, String.valueOf(currentUserId));
    }
}
