package backend.common.domain.tenant;

import backend.common.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-25 17:04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class TenantUserStructureEntity extends BaseEntity {
    @JsonIgnore
    public static final Long ADMIN_VALUE = 0L;
    private Long userId;
    private Long tenantId;
    private Long hospitalId;
    private Long hospitalAreaId;

    private String hospitalCode;

    private Long departmentId;
    /**
     * 是否为科室管理员
     */
    private Boolean departmentAdmin;
}
