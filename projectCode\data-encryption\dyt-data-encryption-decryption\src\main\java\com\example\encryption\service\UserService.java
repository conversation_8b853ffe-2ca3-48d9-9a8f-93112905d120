package com.example.encryption.service;

import com.example.encryption.entity.User;
import com.example.encryption.repository.UserRepository;
import com.example.encryption.util.AESGCMUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * 用户业务服务类
 * 演示加密字段的业务操作
 */
@Service
@Transactional
public class UserService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserService.class);
    
    @Autowired
    private UserRepository userRepository;
    
    /**
     * 创建用户
     * @param user 用户信息
     * @return 创建的用户
     */
    public User createUser(User user) {
        logger.info("Creating user with username: {}", user.getUsername());
        
        // 检查用户名是否已存在
        if (userRepository.existsByUsername(user.getUsername())) {
            throw new RuntimeException("用户名已存在: " + user.getUsername());
        }
        
        // 保存用户（敏感字段会自动加密）
        User savedUser = userRepository.save(user);
        logger.info("Successfully created user with ID: {}", savedUser.getId());
        
        return savedUser;
    }
    
    /**
     * 根据ID获取用户
     * @param id 用户ID
     * @return 用户信息
     */
    @Transactional(readOnly = true)
    public Optional<User> getUserById(Long id) {
        logger.debug("Getting user by ID: {}", id);
        return userRepository.findById(id);
    }
    
    /**
     * 根据用户名获取用户
     * @param username 用户名
     * @return 用户信息
     */
    @Transactional(readOnly = true)
    public Optional<User> getUserByUsername(String username) {
        logger.debug("Getting user by username: {}", username);
        return userRepository.findByUsername(username);
    }
    
    /**
     * 获取所有用户
     * @return 用户列表
     */
    @Transactional(readOnly = true)
    public List<User> getAllUsers() {
        logger.debug("Getting all users");
        return userRepository.findAll();
    }
    
    /**
     * 根据手机号查找用户
     * 注意：由于手机号是加密存储的，需要特殊处理
     * 
     * @param phone 手机号
     * @return 用户信息
     */
    @Transactional(readOnly = true)
    public Optional<User> getUserByPhone(String phone) {
        if (!StringUtils.hasText(phone)) {
            return Optional.empty();
        }
        
        logger.debug("Searching user by phone (encrypted search)");
        
        // 方法1：加密查询条件后在数据库中查找
        // 这种方法需要在Repository中添加相应的查询方法
        // 但是由于每次加密的结果都不同（因为IV是随机的），这种方法不可行
        
        // 方法2：查询所有用户，在应用层解密后匹配
        // 这种方法适用于数据量不大的情况
        List<User> allUsers = userRepository.findAll();
        
        for (User user : allUsers) {
            if (phone.equals(user.getPhone())) {
                logger.debug("Found user by phone");
                return Optional.of(user);
            }
        }
        
        logger.debug("No user found with the given phone");
        return Optional.empty();
    }
    
    /**
     * 根据邮箱查找用户
     * 注意：由于邮箱是加密存储的，需要特殊处理
     * 
     * @param email 邮箱
     * @return 用户信息
     */
    @Transactional(readOnly = true)
    public Optional<User> getUserByEmail(String email) {
        if (!StringUtils.hasText(email)) {
            return Optional.empty();
        }
        
        logger.debug("Searching user by email (encrypted search)");
        
        List<User> allUsers = userRepository.findAll();
        
        for (User user : allUsers) {
            if (email.equals(user.getEmail())) {
                logger.debug("Found user by email");
                return Optional.of(user);
            }
        }
        
        logger.debug("No user found with the given email");
        return Optional.empty();
    }
    
    /**
     * 更新用户信息
     * @param user 用户信息
     * @return 更新后的用户
     */
    public User updateUser(User user) {
        logger.info("Updating user with ID: {}", user.getId());
        
        if (!userRepository.existsById(user.getId())) {
            throw new RuntimeException("用户不存在: " + user.getId());
        }
        
        // 更新用户（敏感字段会自动加密）
        User updatedUser = userRepository.save(user);
        logger.info("Successfully updated user with ID: {}", updatedUser.getId());
        
        return updatedUser;
    }
    
    /**
     * 删除用户
     * @param id 用户ID
     */
    public void deleteUser(Long id) {
        logger.info("Deleting user with ID: {}", id);
        
        if (!userRepository.existsById(id)) {
            throw new RuntimeException("用户不存在: " + id);
        }
        
        userRepository.deleteById(id);
        logger.info("Successfully deleted user with ID: {}", id);
    }
    
    /**
     * 根据状态获取用户列表
     * @param status 用户状态
     * @return 用户列表
     */
    @Transactional(readOnly = true)
    public List<User> getUsersByStatus(User.UserStatus status) {
        logger.debug("Getting users by status: {}", status);
        return userRepository.findByStatus(status);
    }
    
    /**
     * 获取活跃用户数量
     * @return 活跃用户数量
     */
    @Transactional(readOnly = true)
    public long getActiveUserCount() {
        return userRepository.countActiveUsers();
    }
}
