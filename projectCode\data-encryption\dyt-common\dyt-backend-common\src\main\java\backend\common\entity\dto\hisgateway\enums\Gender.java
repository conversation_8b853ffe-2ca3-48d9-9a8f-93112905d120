package backend.common.entity.dto.hisgateway.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * GB/T 2261.1
 */
public enum Gender {

	UNKNOW("未知的性别", 0), MALE("男", 1), FEMALE("女", 2), UNDESC("未说明的性别", 9);

	private String value;

	private Integer code;

	Gender(String value, Integer code) {
		this.value = value;
		this.code = code;
	}

	@JsonCreator
	public static Gender getFromCode(int code) {
		for (Gender t : Gender.values()) {
			if (t.getCode().equals(code)) {
				return t;
			}
		}
		throw new IllegalArgumentException("性别不存在");
	}

	public static Gender getFromValue(String value) {
		for (Gender t : Gender.values()) {
			if (t.getValue().equals(value)) {
				return t;
			}
		}
		throw new IllegalArgumentException("性别不存在");
	}

	public String getValue() {
		return value;
	}

	public Integer getCode() {
		return code;
	}

	@JsonValue
	public String getRequestCode() {
		return code.toString();
	}

}
