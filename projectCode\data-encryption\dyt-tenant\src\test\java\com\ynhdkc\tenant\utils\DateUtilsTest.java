package com.ynhdkc.tenant.utils;

import com.ynhdkc.tenant.dto.DateRangeDto;
import com.ynhdkc.tenant.tool.DateUtils;
import com.ynhdkc.tenant.tool.DoctorUtils;
import org.junit.Test;

import java.time.LocalDateTime;
import java.util.Date;

public class DateUtilsTest {

    @Test
    public void testDateRange() {
        DateRangeDto dateRangeDto = DateUtils.getDateRange(7);

        System.out.println(dateRangeDto.getStartDate());
        System.out.println(dateRangeDto.getEndDate());
    }

    @Test
    public void testDate() {
        LocalDateTime localDateTime = DoctorUtils.convertDateToLocalDateTime(new Date());
        System.out.println(localDateTime.getHour());
    }

    @Test
    public void testTimeStamp() {
        long timeStamp = 1716825600000L;
        Date date = new Date(timeStamp);
        System.out.printf("Date: %s\n", date);
    }
}
