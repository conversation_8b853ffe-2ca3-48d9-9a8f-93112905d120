package backend.common.kafka.streams;

import com.ynhdkc.tenant.model.DictInfoKafkaVo;
import com.ynhdkc.tenant.model.DictLabelVo;
import com.ynhdkc.tenant.model.DictTypeVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.serialization.Serde;
import org.apache.kafka.common.serialization.Serdes;
import org.apache.kafka.streams.KeyValue;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.support.serializer.JsonSerde;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import static backend.common.kafka.constant.KafkaProperties.DICT_TOPIC_NAME;

/**
 * <AUTHOR>
 * @since 2023/6/28 17:57:03
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "backend.kafka.global-store.info.dict-info", havingValue = "true")
public class DictInfoGlobalStore extends GlobalStoreKafkaStreamsCallback<String, DictInfoKafkaVo> {
    public static final JsonSerde<DictInfoKafkaVo> VALUE_SERDE = new JsonSerde<>(DictInfoKafkaVo.class).noTypeInfo();
    private final ConcurrentMap<String, DictTypeVo> dictTypeMap = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, DictLabelVo> dictLabelMap = new ConcurrentHashMap<>();

    @Override
    public void processUpdate(String key, DictInfoKafkaVo update, DictInfoKafkaVo old) {
        log.info("DictInfoGlobalStore processUpdate key:{}, update:{}, old:{}", key, update, old);
        if (update == null && old == null) {
            return;
        }
        if (update == null) {
            dictTypeMap.clear();
            dictLabelMap.clear();
        } else {
            updateDictInfoFromDictKafkaVo(update);
        }
    }

    @Override
    public String storeName() {
        return "dict-info-model";
    }

    @Override
    public String sourceTopic() {
        return DICT_TOPIC_NAME;
    }

    @Override
    public Serde<DictInfoKafkaVo> valueSerde() {
        return VALUE_SERDE;
    }

    @Override
    public Serde<String> keySerde() {
        return Serdes.String();
    }

    @Override
    protected void doInitialize(KeyValue<String, DictInfoKafkaVo> next) {
        DictInfoKafkaVo value = next.value;
        updateDictInfoFromDictKafkaVo(value);
    }

    private void updateDictInfoFromDictKafkaVo(DictInfoKafkaVo value) {
        List<DictTypeVo> dictTypes = value.getDictTypes();
        dictTypes.forEach(dictType -> dictTypeMap.put(dictType.getType(), dictType));

        List<DictLabelVo> dictLabels = value.getDictLabels();
        dictLabels.forEach(dictLabel -> dictLabelMap.put(dictLabel.getDictType() + dictLabel.getDictValue(), dictLabel));
    }

    public DictTypeVo getDictType(String dictType) {
        return dictTypeMap.get(dictType);
    }

    public DictLabelVo getDictLabel(String dictType, String dictValue) {
        return dictLabelMap.get(dictType + dictValue);
    }
}
