package backend.security.oauth2;

import org.springframework.beans.factory.FactoryBean;
import org.springframework.core.env.Environment;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.converter.FormHttpMessageConverter;
import org.springframework.security.oauth2.client.*;
import org.springframework.security.oauth2.client.endpoint.DefaultClientCredentialsTokenResponseClient;
import org.springframework.security.oauth2.client.http.OAuth2ErrorResponseErrorHandler;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.security.oauth2.core.http.converter.OAuth2AccessTokenResponseHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.Collections;

public class LoadBalanceOAuth2AuthorizedClientManagerFactoryBean extends AbstractLBClientHttpRequestInterceptorProvider implements FactoryBean<OAuth2AuthorizedClientManager> {

    public static final String ENABLE_KEY = "spring.security.oauth2.client.provider-service-discovery";

    @Override
    public OAuth2AuthorizedClientManager getObject() throws Exception {
        final Environment env = getApplicationContext().getEnvironment();
        final Boolean enableLoadBalance = env.getProperty(ENABLE_KEY, Boolean.class, false);

        ClientRegistrationRepository clientRegistrationRepository = getApplicationContext().getBean(ClientRegistrationRepository.class);
        OAuth2AuthorizedClientService authorizedClientRepository = getApplicationContext().getBean(OAuth2AuthorizedClientService.class);

        if (!enableLoadBalance) {
            return new AuthorizedClientServiceOAuth2AuthorizedClientManager(clientRegistrationRepository, authorizedClientRepository);
        }

        ClientHttpRequestInterceptor finalLoadBalancerInterceptor = getLBClientHttpRequestInterceptor();
        OAuth2AuthorizedClientProvider authorizedClientProvider =
                OAuth2AuthorizedClientProviderBuilder.builder()
                        .clientCredentials(builder -> {
                            RestTemplate restTemplate = new RestTemplate(Arrays.asList(
                                    new FormHttpMessageConverter(), new OAuth2AccessTokenResponseHttpMessageConverter()));
                            restTemplate.setErrorHandler(new OAuth2ErrorResponseErrorHandler());
                            restTemplate.setInterceptors(Collections.singletonList(finalLoadBalancerInterceptor));
                            final DefaultClientCredentialsTokenResponseClient client = new DefaultClientCredentialsTokenResponseClient();
                            client.setRestOperations(restTemplate);
                            builder.accessTokenResponseClient(client);
                        })
                        .build();
        AuthorizedClientServiceOAuth2AuthorizedClientManager authorizedClientManager = new AuthorizedClientServiceOAuth2AuthorizedClientManager(
                clientRegistrationRepository, authorizedClientRepository);
        authorizedClientManager.setAuthorizedClientProvider(authorizedClientProvider);

        return authorizedClientManager;
    }

    @Override
    public Class<?> getObjectType() {
        return OAuth2AuthorizedClientManager.class;
    }
}
