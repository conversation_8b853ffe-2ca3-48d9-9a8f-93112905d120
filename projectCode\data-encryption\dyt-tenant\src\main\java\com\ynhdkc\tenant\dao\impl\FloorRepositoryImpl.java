package com.ynhdkc.tenant.dao.impl;

import com.ynhdkc.tenant.dao.FloorRepository;
import com.ynhdkc.tenant.dao.mapper.AreaMapper;
import com.ynhdkc.tenant.dao.mapper.FloorMapper;
import com.ynhdkc.tenant.entity.Area;
import com.ynhdkc.tenant.entity.Floor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-03 10:48
 */
@Repository
@RequiredArgsConstructor
public class FloorRepositoryImpl implements FloorRepository {
    private final FloorMapper floorMapper;
    private final AreaMapper areaMapper;

    @Override
    public void create(Floor floor) {
        floorMapper.insertSelective(floor);
    }

    @Override
    public void update(Floor floor) {
        floorMapper.updateByPrimaryKeySelective(floor);
    }

    @Override
    public void delete(Long floorId) {
        floorMapper.deleteByPrimaryKey(floorId);

        areaMapper.deleteByExample2(Area.class, sql -> sql.andEqualTo(Area::getFloorId, floorId));
    }
}
