package com.ynhdkc.tenant.dao;

import backend.common.util.BaseQueryOption;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.entity.Tenant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Collection;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-25 21:46
 */
public interface TenantQuery {
    Tenant queryTenantById(Long tenantId);

    Page<Tenant> pageQueryTenant(TenantQueryOption option);

    Collection<Tenant> queryByIds(Set<Long> tenants);

    Integer countByName(String name);


    @EqualsAndHashCode(callSuper = true)
    @Data
    @Accessors(chain = true)
    class TenantQueryOption extends BaseQueryOption {
        public TenantQueryOption(Integer currentPage, Integer pageSize) {
            super(currentPage, pageSize);
        }

        private String name;
        private String contactPerson;
        private String contactPhoneNumber;

        private Collection<Long> includeIds;
        private Collection<Long> excludeIds;
    }
}
