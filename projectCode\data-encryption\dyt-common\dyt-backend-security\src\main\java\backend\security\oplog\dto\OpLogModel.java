package backend.security.oplog.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

@Data
public class OpLogModel {

    @JsonProperty("op_code")
    private String opCode = "";

    @JsonProperty("op_code_cn")
    private String opCodeCn = "";

    private String url = "";

    private String method = "";

    @JsonProperty("tenant_id")
    private long tenantId = -1;

    @JsonProperty("user_id")
    private long userId = -1;

    @JsonProperty("user_name")
    private String userName = "";

    private boolean error = false;

    private String exception = "";

    @JsonProperty("module_name")
    private String moduleName = "";

    @JsonProperty("raw_message")
    private String rawMessage = "";

    private String type = "op-log";

    private final Long timestamp = System.currentTimeMillis();

}
