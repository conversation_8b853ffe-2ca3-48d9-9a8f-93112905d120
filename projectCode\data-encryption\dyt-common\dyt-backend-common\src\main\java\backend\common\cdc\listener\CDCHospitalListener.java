package backend.common.cdc.listener;

import backend.common.cdc.CDCListener;
import backend.common.cdc.CDCTopics;
import backend.common.cdc.ResourceChangeCapture;
import backend.common.cdc.dto.CDCHospital;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;

@Slf4j
public class CDCHospitalListener extends CDCListener<CDCHospital> {

    public CDCHospitalListener(ResourceChangeCapture<CDCHospital> capture) {
        super(capture);
    }

    @KafkaListener(topics = CDCTopics.BACKEND_HOSPITAL, groupId = "${spring.application.name}")
    public void kafkaListener(ConsumerRecord<String, byte[]> msg) throws Exception {
        this.doWork(msg, CDCHospital.class);
    }

}
