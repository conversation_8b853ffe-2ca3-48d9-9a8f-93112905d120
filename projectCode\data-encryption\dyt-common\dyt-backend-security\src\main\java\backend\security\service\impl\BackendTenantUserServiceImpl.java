package backend.security.service.impl;

import backend.security.entity.Endpoint;
import backend.security.entity.Role;
import backend.security.entity.TenantUserAscription;
import backend.security.entity.TenantUserStructureTree;
import backend.security.oauth2.BackendOAuth2Constants;
import backend.security.oauth2.BackendOAuth2User;
import backend.security.repository.BackendPrivilegeRepository;
import backend.security.service.BackendTenantUserService;
import com.ynhdkc.tenant.model.UserDepartmentPrivilegeConfig;
import com.ynhdkc.tenant.model.UserHospitalAreaPrivilegeConfig;
import com.ynhdkc.tenant.model.UserHospitalPrivilegeConfig;
import com.ynhdkc.tenant.model.UserTenantPrivilegeConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.HandlerMapping;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-27 15:03
 */
@RequiredArgsConstructor
public class BackendTenantUserServiceImpl implements BackendTenantUserService {
    private final BackendPrivilegeRepository backendPrivilegeRepository;

    public boolean checkApiAuthorities(Set<String> authorities) {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes instanceof ServletRequestAttributes) {
            ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) requestAttributes;
            HttpServletRequest request = servletRequestAttributes.getRequest();
            String method = request.getMethod();
            String requestPath = (String) request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE);
            for (String authority : authorities) {
                if (!authority.startsWith(BackendOAuth2Constants.PREFIX_AUTH)) {
                    continue;
                }
                Long roleId = Long.valueOf(authority.replace(BackendOAuth2Constants.PREFIX_AUTH, ""));
                /* 查询角色 */
                Role role = backendPrivilegeRepository.getRoleById(roleId);
                if (null == role) {
                    continue;
                }
                /* 判断当前角色是否拥有该路径的访问权限 */
                Map<Long, Endpoint> endpoints = role.getEndpoints();
                for (Map.Entry<Long, Endpoint> endpoint : endpoints.entrySet()) {
                    boolean match = endpoint.getValue().isMatch(requestPath, method);
                    /* 如果拥有则返回，否则继续遍历 */
                    if (match) {
                        return true;
                    }
                }
            }
        }
        /* 所有角色都没有查询到访问路径 或 没有获取到访问路径，拒绝访问 */
        return false;
    }

    @Override
    public boolean isSuperAdmin() {
        return this.isSuperAdmin(getCurrentUserId());
    }

    @Override
    public boolean isSuperAdmin(Long userId) {
        return backendPrivilegeRepository.getTenantUserStructureTree(userId).isSuperAdmin();
    }

    @Override
    public boolean isCurrentLayerAdmin(TenantId tenantId) {
        TenantUserStructureTree tenantUserStructureTree = backendPrivilegeRepository.getTenantUserStructureTree(getCurrentUserId());
        if (tenantUserStructureTree.isSuperAdmin()) {
            return true;
        }
        TenantUserStructureTree.TenantLayer tenantLayer = tenantUserStructureTree.getTenants().get(tenantId.getId());
        return null != tenantLayer && Boolean.TRUE.equals(tenantLayer.getTenantAdmin());
    }

    @Override
    public boolean isCurrentLayerAdmin(HospitalId hospitalId) {
        TenantUserStructureTree tenantUserStructureTree = backendPrivilegeRepository.getTenantUserStructureTree(getCurrentUserId());
        if (tenantUserStructureTree.isSuperAdmin()) {
            return true;
        }
        for (Map.Entry<Long, TenantUserStructureTree.TenantLayer> entry : tenantUserStructureTree.getTenants().entrySet()) {
            TenantUserStructureTree.TenantLayer tenantLayer = entry.getValue();
            TenantUserStructureTree.HospitalLayer hospitalLayer = tenantLayer.getHospitals().get(hospitalId.getId());
            if (null != hospitalLayer && Boolean.TRUE.equals(hospitalLayer.getHospitalAdmin())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean isCurrentLayerAdmin(HospitalAreaId hospitalAreaId) {
        TenantUserStructureTree tenantUserStructureTree = backendPrivilegeRepository.getTenantUserStructureTree(getCurrentUserId());
        if (tenantUserStructureTree.isSuperAdmin()) {
            return true;
        }
        for (Map.Entry<Long, TenantUserStructureTree.TenantLayer> entry : tenantUserStructureTree.getTenants().entrySet()) {
            TenantUserStructureTree.TenantLayer tenantLayer = entry.getValue();
            for (Map.Entry<Long, TenantUserStructureTree.HospitalLayer> hospitalLayerEntry : tenantLayer.getHospitals().entrySet()) {
                TenantUserStructureTree.HospitalLayer hospitalLayer = hospitalLayerEntry.getValue();
                TenantUserStructureTree.HospitalAreaLayer hospitalAreaLayer = hospitalLayer.getHospitalAreas().get(hospitalAreaId.getId());
                if (null != hospitalAreaLayer && Boolean.TRUE.equals(hospitalAreaLayer.getHospitalAreaAdmin())) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public boolean isCurrentLayerAdmin(DepartmentId departmentId) {
        TenantUserStructureTree tenantUserStructureTree = backendPrivilegeRepository.getTenantUserStructureTree(getCurrentUserId());
        if (tenantUserStructureTree.isSuperAdmin()) {
            return true;
        }
        for (Map.Entry<Long, TenantUserStructureTree.TenantLayer> entry : tenantUserStructureTree.getTenants().entrySet()) {
            TenantUserStructureTree.TenantLayer tenantLayer = entry.getValue();
            for (Map.Entry<Long, TenantUserStructureTree.HospitalLayer> hospitalLayerEntry : tenantLayer.getHospitals().entrySet()) {
                TenantUserStructureTree.HospitalLayer hospitalLayer = hospitalLayerEntry.getValue();
                for (Map.Entry<Long, TenantUserStructureTree.HospitalAreaLayer> hospitalAreaLayerEntry : hospitalLayer.getHospitalAreas().entrySet()) {
                    TenantUserStructureTree.HospitalAreaLayer hospitalAreaLayer = hospitalAreaLayerEntry.getValue();
                    TenantUserStructureTree.DepartmentLayer departmentLayer = hospitalAreaLayer.getDepartments().get(departmentId.getId());
                    if (null != departmentLayer && Boolean.TRUE.equals(departmentLayer.getDepartmentAdmin())) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    @Override
    public boolean currentUserHasWhitePrivilege(Long tenantId, Long hospitalId, Long hospitalAreaId, Long departmentId) {
        return this.hasWhitePrivilege(getCurrentUserId(), tenantId, hospitalId, hospitalAreaId, departmentId);
    }

    @Override
    public boolean hasWhitePrivilege(Long userId, Long tenantId, Long hospitalId, Long hospitalAreaId, Long departmentId) {
        TenantUserStructureTree tenantUserStructureTree = backendPrivilegeRepository.getTenantUserStructureTree(userId);
        if (tenantUserStructureTree.isSuperAdmin()) {
            return true;
        }
        return tenantUserStructureTree.hasPrivilege(tenantId, hospitalId, hospitalAreaId, departmentId);
    }

    @Override
    public boolean currentUserHasReadPrivilege(TenantId tenantId) {
        return this.hasReadPrivilege(getCurrentUserId(), tenantId);
    }

    @Override
    public boolean hasReadPrivilege(Long userId, TenantId tenantId) {
        TenantUserStructureTree tenantUserStructureTree = backendPrivilegeRepository.getTenantUserStructureTree(userId);
        TenantUserAscription tenantUserAscription = backendPrivilegeRepository.getTenantUserAscription(userId);
        if (null == tenantId || null == tenantId.getId()) {
            return tenantUserStructureTree.isSuperAdmin();
        }
        return tenantUserAscription.getTenants().contains(tenantId.getId());
    }

    @Override
    public boolean currentUserHasReadPrivilege(HospitalId hospitalId) {
        return this.hasReadPrivilege(getCurrentUserId(), hospitalId);
    }

    @Override
    public boolean hasReadPrivilege(Long userId, HospitalId hospitalId) {
        TenantUserStructureTree tenantUserStructureTree = backendPrivilegeRepository.getTenantUserStructureTree(userId);
        TenantUserAscription tenantUserAscription = backendPrivilegeRepository.getTenantUserAscription(userId);
        if (null == hospitalId || null == hospitalId.getId()) {
            return tenantUserStructureTree.isSuperAdmin();
        }
        return tenantUserAscription.getHospitals().contains(hospitalId.getId());
    }

    @Override
    public boolean currentUserHasReadPrivilege(HospitalAreaId hospitalAreaId) {
        return this.hasReadPrivilege(getCurrentUserId(), hospitalAreaId);
    }

    @Override
    public boolean hasReadPrivilege(Long userId, HospitalAreaId hospitalAreaId) {
        TenantUserStructureTree tenantUserStructureTree = backendPrivilegeRepository.getTenantUserStructureTree(userId);
        TenantUserAscription tenantUserAscription = backendPrivilegeRepository.getTenantUserAscription(userId);
        if (null == hospitalAreaId || null == hospitalAreaId.getId()) {
            return tenantUserStructureTree.isSuperAdmin();
        }
        return tenantUserAscription.getHospitalAreas().contains(hospitalAreaId.getId());
    }

    @Override
    public boolean currentUserHasReadPrivilege(DepartmentId departmentId) {
        return this.hasReadPrivilege(getCurrentUserId(), departmentId);
    }

    @Override
    public boolean hasReadPrivilege(Long userId, DepartmentId departmentId) {
        TenantUserStructureTree tenantUserStructureTree = backendPrivilegeRepository.getTenantUserStructureTree(userId);
        TenantUserAscription tenantUserAscription = backendPrivilegeRepository.getTenantUserAscription(userId);
        if (null == departmentId || null == departmentId.getId()) {
            return tenantUserStructureTree.isSuperAdmin();
        }
        return tenantUserAscription.getDepartments().contains(departmentId.getId());
    }

    @Override
    public Set<Long> getCurrentUserTenantReadRange(boolean fillSet) {
        return this.getUserTenantReadRange(getCurrentUserId(), fillSet);
    }

    @Override
    public Set<Long> getUserTenantReadRange(Long userId, boolean fillSet) {
        TenantUserAscription tenantUserAscription = backendPrivilegeRepository.getTenantUserAscription(userId);
        Set<Long> ids = tenantUserAscription.getTenants();
        if (ids.isEmpty() && fillSet) {
            ids.add(-1L);
        }
        return ids;
    }

    @Override
    public Set<Long> getCurrentUserHospitalReadRange(boolean fillSet) {
        TenantUserAscription tenantUserAscription = backendPrivilegeRepository.getTenantUserAscription(getCurrentUserId());
        Set<Long> ids = tenantUserAscription.getHospitals();
        if (ids.isEmpty() && fillSet) {
            ids.add(-1L);
        }
        return ids;
    }

    @Override
    public Set<Long> getCurrentUserHospitalAreaReadRange(boolean fillSet) {
        TenantUserAscription tenantUserAscription = backendPrivilegeRepository.getTenantUserAscription(getCurrentUserId());
        Set<Long> ids = tenantUserAscription.getHospitalAreas();
        if (ids.isEmpty() && fillSet) {
            ids.add(-1L);
        }
        return ids;
    }

    @Override
    public Set<Long> getCurrentUserDepartmentReadRange(boolean fillSet) {
        TenantUserAscription tenantUserAscription = backendPrivilegeRepository.getTenantUserAscription(getCurrentUserId());
        Set<Long> ids = tenantUserAscription.getDepartments();
        if (ids.isEmpty() && fillSet) {
            ids.add(-1L);
        }
        return ids;
    }

    @Override
    public List<UserTenantPrivilegeConfig> getCurrentUserTenantPrivilegeConfig() {
        return getUserTenantPrivilegeConfig(getCurrentUserId());
    }

    @Override
    public List<UserTenantPrivilegeConfig> getUserTenantPrivilegeConfig(Long userId) {
        TenantUserStructureTree tenantUserStructureTree = backendPrivilegeRepository.getTenantUserStructureTree(userId);
        /* 查询用户租户层级 */
        Map<Long, TenantUserStructureTree.TenantLayer> tenantLayers = tenantUserStructureTree.getTenants();

        List<UserTenantPrivilegeConfig> tenantPrivilegeConfigs = new ArrayList<>();
        /* 遍历租户层级 */
        tenantLayers.forEach((tenantId, tenantLayer) -> {
            UserTenantPrivilegeConfig tenantPrivilegeConfig = new UserTenantPrivilegeConfig();
            tenantPrivilegeConfig.setTenantId(tenantId);
            tenantPrivilegeConfig.setTenantAdmin(tenantLayer.getTenantAdmin());
            /* 遍历医院层级 */
            List<UserHospitalPrivilegeConfig> hospitalPrivilegeConfigs = new ArrayList<>();
            tenantLayer.getHospitals().forEach((hospitalId, hospitalLayer) -> {
                UserHospitalPrivilegeConfig hospitalPrivilegeConfig = new UserHospitalPrivilegeConfig();
                hospitalPrivilegeConfig.setTenantId(tenantId);
                hospitalPrivilegeConfig.setHospitalId(hospitalId);
                hospitalPrivilegeConfig.setHospitalAdmin(hospitalLayer.getHospitalAdmin());
                /* 遍历院区层级 */
                List<UserHospitalAreaPrivilegeConfig> hospitalAreaPrivilegeConfigs = new ArrayList<>();
                hospitalLayer.getHospitalAreas().forEach((hospitalAreaId, hospitalAreaLayer) -> {
                    UserHospitalAreaPrivilegeConfig hospitalAreaPrivilegeConfig = new UserHospitalAreaPrivilegeConfig();
                    hospitalAreaPrivilegeConfig.setTenantId(tenantId);
                    hospitalAreaPrivilegeConfig.setHospitalId(hospitalId);
                    hospitalAreaPrivilegeConfig.setHospitalAreaId(hospitalAreaId);
                    hospitalAreaPrivilegeConfig.setHospitalAreaAdmin(hospitalAreaLayer.getHospitalAreaAdmin());
                    /* 遍历科室层级 */
                    List<UserDepartmentPrivilegeConfig> departmentPrivilegeConfigs = new ArrayList<>();
                    hospitalAreaLayer.getDepartments().forEach((departmentId, departmentLayer) ->
                            departmentPrivilegeConfigs.add(createUserDepartmentPrivilegeConfig(tenantId, hospitalId, hospitalAreaId, departmentId, departmentLayer.getDepartmentAdmin())));
                    hospitalAreaPrivilegeConfig.setDepartments(departmentPrivilegeConfigs);
                    hospitalAreaPrivilegeConfigs.add(hospitalAreaPrivilegeConfig);
                });
                hospitalPrivilegeConfig.setHospitalAreaPrivilegeConfigs(hospitalAreaPrivilegeConfigs);
                hospitalPrivilegeConfigs.add(hospitalPrivilegeConfig);
            });
            tenantPrivilegeConfig.setHospitalPrivilegeConfigs(hospitalPrivilegeConfigs);
            tenantPrivilegeConfigs.add(tenantPrivilegeConfig);
        });
        return tenantPrivilegeConfigs;
    }

    @Override
    public String getCurrentUserNickname() {
        return BackendOAuth2User.getProertyFromJwt(BackendOAuth2Constants.NICKNAME);
    }

    private UserDepartmentPrivilegeConfig createUserDepartmentPrivilegeConfig(Long tenantId, Long hospitalId, Long hospitalAreaId, Long departmentId, Boolean departmentAdmin) {
        UserDepartmentPrivilegeConfig departmentPrivilegeConfig = new UserDepartmentPrivilegeConfig();
        departmentPrivilegeConfig.setTenantId(tenantId);
        departmentPrivilegeConfig.setHospitalId(hospitalId);
        departmentPrivilegeConfig.setHospitalAreaId(hospitalAreaId);
        departmentPrivilegeConfig.setDepartmentId(departmentId);
        departmentPrivilegeConfig.setDepartmentAdmin(departmentAdmin);
        return departmentPrivilegeConfig;
    }
}
