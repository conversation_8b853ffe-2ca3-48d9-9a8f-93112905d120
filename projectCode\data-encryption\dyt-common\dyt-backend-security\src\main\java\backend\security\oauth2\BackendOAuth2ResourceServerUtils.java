package backend.security.oauth2;

import backend.common.util.MessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.core.convert.converter.Converter;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationConverter;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Nonnull;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@SuppressWarnings({"AlibabaLowerCamelCaseVariableNaming", "AlibabaClassMustHaveAuthor", "AlibabaClassNamingShouldBeCamel"})
public class BackendOAuth2ResourceServerUtils {

    public static final String CLAIM_NAME = "nickname";
    public static final String CLAIM_NICK_NAME = "nickname";

    public static void initializeOAuth2ResourceServer(WebSecurity web, ApplicationContext applicationContext) {
        final BackendOAuth2ResourceServerProperties properties = applicationContext.getBean(BackendOAuth2ResourceServerProperties.class);
        if (!CollectionUtils.isEmpty(properties.getPublicPaths())) {
            /* 忽略公共路径 */
            WebSecurity.IgnoredRequestConfigurer ignoring = web.ignoring();
            for (BackendOAuth2ResourceServerProperties.PathMethod pp : properties.getPublicPaths()) {
                if (CollectionUtils.isEmpty(pp.getMethod())) {
                    ignoring.antMatchers(pp.getAntPattern());
                } else {
                    for (HttpMethod m : pp.getMethod()) {
                        ignoring.antMatchers(m, pp.getAntPattern());
                    }
                }
            }
        }
    }

    public static void initializeOAuth2ResourceServer(HttpSecurity http) throws Exception {
        final ApplicationContext applicationContext = http.getSharedObject(ApplicationContext.class);
        /* 公共资源路径 */
        final BackendOAuth2ResourceServerProperties properties = applicationContext.getBean(BackendOAuth2ResourceServerProperties.class);
        /* 配置基础请求路径 */
        http.requestMatchers().antMatchers(properties.getBasePathPattern(), properties.getRpcPathPattern())
                .and()
                .csrf().disable()
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS));
        /* 配置路径和 Scope */
        for (BackendOAuth2ResourceServerProperties.AuthenticationPathMethod i : properties.getAuthorizedPaths()) {
            /* 默认scope */
            i.getScope().add(BackendOAuth2Constants.SCOPE_GLOBAL);
            http.authorizeRequests(authorizeRequests -> {
                List<String> scope = new ArrayList<>(i.getScope().size());
                i.getScope().forEach(j -> scope.add(BackendOAuth2Constants.PREFIX_SCOPE + j));
                authorizeRequests
                        .antMatchers(i.getAntPattern())
                        .hasAnyAuthority(scope.toArray(new String[]{}));
            });
        }
        http.oauth2ResourceServer(oauth2 -> oauth2.jwt(jwt -> {
            /* 配置Jwt权限转化器 */
            jwt.jwtAuthenticationConverter(jwtAuthenticationConverter(properties));
        }));

        if (BackendOAuth2User.isClientUser()) {
            BackendOAuth2User iotOAuth2User = BackendOAuth2User.tryGetCurrent();
//            log.info("[CLIENT_USER_AUTH_INFO]: {}", MessageUtil.object2JSONString(iotOAuth2User));
        }
    }

    public static JwtAuthenticationConverter jwtAuthenticationConverter(BackendOAuth2ResourceServerProperties
                                                                                properties) {
        /* 读取yml自定义配置 */
        Converter<Jwt, Collection<GrantedAuthority>> grantedAuthoritiesConverter = new CustomAuthoritiesConverter(properties);
        JwtAuthenticationConverter jwtAuthenticationConverter = new JwtAuthenticationConverter();
        /* 设置自定义Converter */
        jwtAuthenticationConverter.setJwtGrantedAuthoritiesConverter(grantedAuthoritiesConverter);
        return jwtAuthenticationConverter;
    }

    static class CustomAuthoritiesConverter implements Converter<Jwt, Collection<GrantedAuthority>> {
        BackendOAuth2ResourceServerProperties properties;

        public CustomAuthoritiesConverter(BackendOAuth2ResourceServerProperties properties) {
            /* 没用到 */
            this.properties = properties;
        }

        @Override
        public Collection<GrantedAuthority> convert(@Nonnull Jwt jwt) {
            /* 提取Jwt中的payload scope字段，分割前缀为 SCOPE_ ，转化为权限清单 */
            List<GrantedAuthority> rst = new LinkedList<>(extract(jwt, BackendOAuth2Constants.SCOPE_CLAIM_NAME, BackendOAuth2Constants.PREFIX_SCOPE));
            /* 提取Jwt中的payload auth，分割前缀为 AUTH_ ，转化为权限清单 */
            rst.addAll(extract(jwt, BackendOAuth2Constants.AUTHORITIES_CLAIM_NAME, BackendOAuth2Constants.PREFIX_AUTH));
            return rst;
        }

        @SuppressWarnings("unchecked")
        public Collection<GrantedAuthority> extract(Jwt jwt, String claimName, String prefix) {
            if (!StringUtils.hasText(claimName)) {
                return Collections.emptyList();
            }
            /* 获取 Map Value */
            List<String> rst = new LinkedList<>();
            Object authorities = jwt.getClaim(claimName);
            /* 是String的话，通过 “ ” 进行分割 */
            if (authorities instanceof String) {
                if (StringUtils.hasText((String) authorities)) {
                    rst.addAll(Arrays.asList(((String) authorities).split(" ")));
                }
                /* 如果是数组，直接放入list */
            } else if (authorities instanceof Collection) {
                rst.addAll((Collection<String>) authorities);
            }
            /* 拼接前缀和权限 */
            return rst.stream().map(authority -> new SimpleGrantedAuthority(prefix + authority)).collect(Collectors.toList());
        }
    }
}
