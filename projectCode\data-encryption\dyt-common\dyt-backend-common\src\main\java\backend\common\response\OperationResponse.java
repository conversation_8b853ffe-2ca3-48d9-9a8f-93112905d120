package backend.common.response;

import lombok.Data;

@Data
public class OperationResponse<T> {
    private static final String SUCCESS_CODE = "200";
    private static final String DEFAULT_SUCCESS_MESSAGE = "success";
    private static final Integer DEFAULT_OPERATION_COUNT = 1;
    private static final OperationResponse<?> SUCCESS_DEFAULT_INSTANCE = new OperationResponse<>(DEFAULT_OPERATION_COUNT, SUCCESS_CODE, DEFAULT_SUCCESS_MESSAGE);

    /**
     * 操作记录的生效条数
     */
    private Integer effectiveCount;
    /**
     * 操作状态
     */
    private String status;
    /**
     * 展示信息
     */
    private String message;
    /**
     * 返回记录
     */
    private T objects;

    /**
     * Generally used for a success response that don't need object return.
     *
     * @param effectiveCount operation effective lines
     * @param status         operation status
     * @param message        help information
     */
    public OperationResponse(Integer effectiveCount, String status, String message) {
        this.effectiveCount = effectiveCount;
        this.status = status;
        this.message = message;
    }

    /**
     * Generally used for a success response that need object return.
     *
     * @param effectiveCount operation effective lines
     * @param status         operation status
     * @param message        help information
     * @param objects        returns data
     */
    public OperationResponse(Integer effectiveCount, String status, String message, T objects) {
        this(effectiveCount, status, message);
        this.objects = objects;
    }

    public static OperationResponse<?> success(Integer count, String message) {
        return new OperationResponse<>(count, SUCCESS_CODE, message);
    }

    public static <T> OperationResponse<T> success(T object) {
        return new OperationResponse<>(null, SUCCESS_CODE, DEFAULT_SUCCESS_MESSAGE, object);
    }

    public static <T> OperationResponse<T> success(Integer count, T object) {
        return new OperationResponse<>(count, SUCCESS_CODE, DEFAULT_SUCCESS_MESSAGE, object);
    }

    public static <T> OperationResponse<T> success(Integer count, String message, T object) {
        return new OperationResponse<>(count, SUCCESS_CODE, message, object);
    }

    public static OperationResponse<?> success(String message) {
        return new OperationResponse<>(DEFAULT_OPERATION_COUNT, SUCCESS_CODE, message);
    }

    public static OperationResponse<?> success() {
        return SUCCESS_DEFAULT_INSTANCE;
    }
}
