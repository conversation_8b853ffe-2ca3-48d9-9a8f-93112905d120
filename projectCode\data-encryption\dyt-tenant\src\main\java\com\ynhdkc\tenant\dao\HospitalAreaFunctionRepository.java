package com.ynhdkc.tenant.dao;

import com.ynhdkc.tenant.entity.setting.FunctionSetting;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-09 10:26
 */
public interface HospitalAreaFunctionRepository {
    void create(FunctionSetting entity);

    void update(FunctionSetting entity);

    void delete(Long functionId);

    List<FunctionSetting> queryByHospitalAreaId(Long hospitalAreaId);
}
