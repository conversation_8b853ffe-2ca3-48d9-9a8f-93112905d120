package backend.common.util;

import backend.common.exception.BizException;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class MessageUtil {

    private MessageUtil() {
    }

    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        objectMapper.configure(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature(), true);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
    }

    public static String object2JSONString(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException(HttpStatus.BAD_REQUEST, "", "object 2 json string error");
        }
    }

    public static <T> T json2Obj(String json, Class<T> obj) {
        try {
            return objectMapper.readValue(json, obj);
        } catch (IOException e) {
            e.printStackTrace();
            log.error("json 2 object exception ", e);
            throw new BizException(HttpStatus.BAD_REQUEST, "", "json 2 object error");
        }
    }

    public static List<Map<String, Object>> json2ListMap(String json) {
        try {
            CollectionType typeReference = TypeFactory.defaultInstance().constructCollectionType(List.class, Map.class);
            return objectMapper.readValue(json, typeReference);
        } catch (Exception e) {
            log.error("json to list map error ", e);
            throw new BizException(HttpStatus.BAD_REQUEST, "", "json to list map error");
        }
    }

    public static <T> List<T> json2ListObj(String json, Class<T> obj) {
        try {
            CollectionType typeReference = TypeFactory.defaultInstance().constructCollectionType(List.class, obj);
            return objectMapper.readValue(json, typeReference);
        } catch (Exception e) {
            log.error("json to list map error ", e);
            throw new BizException(HttpStatus.BAD_REQUEST, "", "json to list map error");
        }
    }

    public static Map<String, Object> json2ObjMap(String json) {
        try {
            return new ObjectMapper().readValue(json, new TypeReference<HashMap<String, Object>>() {
            });
        } catch (Exception e) {
            log.error("json2ObjMap error, ", e);
            return new HashMap<>();
        }
    }

    public static Map<String, String> json2StringMap(String json) {
        try {
            return new ObjectMapper().readValue(json, new TypeReference<HashMap<String, String>>() {
            });
        } catch (Exception e) {
            log.error("json2ObjMap error, ", e);
            return new HashMap<>();
        }
    }

    public static String clearLineBreak(String multipleLine) {
        if (StringUtils.isEmpty(multipleLine)) {
            return multipleLine;
        }
        Pattern p = Pattern.compile("\\s*|\t|\r|\n");
        Matcher m = p.matcher(multipleLine);
        return m.replaceAll("");
    }

    public static String updateJsonValue(String newValue, String name, List<String> namespaces, String oldJson) {
        try {
            JsonNode baseNode = objectMapper.readTree(oldJson);
            if (namespaces == null || namespaces.isEmpty()) {
                ObjectNode objectNode = (ObjectNode) baseNode;
                objectNode.put(name, newValue);
                return objectMapper.writeValueAsString(baseNode);
            } else {
                JsonNode node = null;
                for (String namespace : namespaces) {
                    if (node == null) {
                        node = baseNode.findValue(namespace);
                    } else {
                        node = node.findValue(namespace);
                    }
                }
                if (node instanceof ObjectNode) {
                    ObjectNode objectNode = (ObjectNode) node;
                    objectNode.put(name, newValue);
                    return objectMapper.writeValueAsString(baseNode);
                }
            }
        } catch (Exception e) {
            log.error("updateJsonValue error ", e);
        }
        return oldJson;
    }

    public static String clearJSONString(String value) {
        if (ObjectUtils.isEmpty(value)) {
            return "";
        }
        if (value.startsWith("[")) {
            int lastSize = value.lastIndexOf("]");
            return value.substring(1, lastSize);
        }
        return value;
    }
}
