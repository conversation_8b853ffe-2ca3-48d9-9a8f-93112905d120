package com.ynhdkc.tenant.dao;

import backend.common.util.BaseQueryOption;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.entity.setting.FunctionSetting;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Collection;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-09 10:12
 */
public interface HospitalAreaFunctionQuery {
    FunctionSetting queryFunctionById(Long hospitalAreaId, Long functionId);

    Page<FunctionSetting> pageQuery(HospitalAreaFunctionQueryOption option);

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Accessors(chain = true)
    class HospitalAreaFunctionQueryOption extends BaseQueryOption {
        public HospitalAreaFunctionQueryOption(Integer current, Integer size) {
            super(current, size);
        }

        private Long tenantId;
        private Long hospitalId;
        private Long hospitalAreaId;
        private String name;
        private String type;
        private Integer status;

        private Collection<Long> includeHospitalAreaIds;
        private Collection<Long> excludeHospitalAreaIds;
    }
}
