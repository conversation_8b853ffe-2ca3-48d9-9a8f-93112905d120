package com.ynhdkc.tenant.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.integration.redis.util.RedisLockRegistry;
import org.springframework.integration.support.locks.ExpirableLockRegistry;

/**
 * <AUTHOR>
 * @since 2023/8/30 17:49:08
 */
@Configuration
public class RedisLockConfiguration {

    @Bean
    public ExpirableLockRegistry redisLockRegistry(RedisConnectionFactory redisConnectionFactory) {
        return new RedisLockRegistry(redisConnectionFactory, "dyt-tenant-lock");
    }
}
