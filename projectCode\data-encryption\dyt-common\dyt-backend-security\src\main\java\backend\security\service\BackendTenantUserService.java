package backend.security.service;


import com.ynhdkc.tenant.model.UserTenantPrivilegeConfig;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-27 14:46
 */
public interface BackendTenantUserService extends BackendUserService {

    /**
     * @param authorities JWT Scope
     * @return 是否有权限
     */
    boolean checkApiAuthorities(Set<String> authorities);

    /**
     * 判断当前用户是否为超管，获取用户ID失败时会抛出异常。
     *
     * @return 是否为超管
     * @throws backend.common.exception.BizException 获取不到JWT用户
     */
    boolean isSuperAdmin();

    /**
     * 判断当前用户是否为超管，获取用户ID失败时会抛出异常。
     *
     * @param userId 用户ID
     * @return 是否为超管
     * @throws backend.common.exception.BizException 获取不到JWT用户
     */
    boolean isSuperAdmin(Long userId);

    /**
     * 判断当前用户是否为目标租户管理员，获取失败时会抛出异常。
     *
     * @param tenantId 租户ID
     * @return 是否为当前租户管理员
     */
    boolean isCurrentLayerAdmin(TenantId tenantId);

    /**
     * 判断当前用户是否为目标医院管理员，获取失败时会抛出异常。
     *
     * @param hospitalId 医院ID
     * @return 是否为当前医院管理员
     */
    boolean isCurrentLayerAdmin(HospitalId hospitalId);

    /**
     * 判断当前用户是否为目标院区管理员，获取失败时会抛出异常。
     *
     * @param hospitalAreaId 医院区域ID
     * @return 是否为当前院区管理员
     */
    boolean isCurrentLayerAdmin(HospitalAreaId hospitalAreaId);

    /**
     * 判断当前用户是否为目标科室管理员，获取失败时会抛出异常。
     *
     * @param departmentId 科室ID
     * @return 是否为当前科室管理员
     */
    boolean isCurrentLayerAdmin(DepartmentId departmentId);

    /**
     * 当前用户是否有修改、写入目标资源权限。
     * 参数如果为空，顺序必须由大到小传递，即：tenantId -> hospitalId -> hospitalAreaId -> departmentId。
     * 规则明细：
     * 1.如果tenantId为空，则表示判断是否为超管。
     * 2.如果tenantId不为空，hospitalId为空，则表示判断是否为租户管理员。
     * 3.如果tenantId不为空，hospitalId不为空，hospitalAreaId为空，则表示判断是否为医院管理员。
     * 4.如果tenantId不为空，hospitalId不为空，hospitalAreaId不为空，departmentId为空，则表示判断是否为院区管理员。
     * 5.如果tenantId不为空，hospitalId不为空，hospitalAreaId不为空，departmentId不为空，则表示判断是否为科室成员。
     *
     * @param tenantId       租户ID
     * @param hospitalId     医院ID
     * @param hospitalAreaId 医院区域ID
     * @param departmentId   科室ID
     * @return 是否有权限
     * @throws backend.common.exception.BizException 有以下2种情况：1.获取不到JWT用户时 2.获取不到用户权限信息
     */
    boolean currentUserHasWhitePrivilege(Long tenantId, Long hospitalId, Long hospitalAreaId, Long departmentId);

    /**
     * 当前用户是否有修改、写入目标资源权限。
     * 参数如果为空，顺序必须由大到小传递，即：tenantId -> hospitalId -> hospitalAreaId -> departmentId。
     * 规则明细：
     * 1.如果tenantId为空，则表示判断是否为超管。
     * 2.如果tenantId不为空，hospitalId为空，则表示判断是否为租户管理员。
     * 3.如果tenantId不为空，hospitalId不为空，hospitalAreaId为空，则表示判断是否为医院管理员。
     * 4.如果tenantId不为空，hospitalId不为空，hospitalAreaId不为空，departmentId为空，则表示判断是否为院区管理员。
     * 5.如果tenantId不为空，hospitalId不为空，hospitalAreaId不为空，departmentId不为空，则表示判断是否为科室成员。
     *
     * @param userId         用户ID
     * @param tenantId       租户ID
     * @param hospitalId     医院ID
     * @param hospitalAreaId 医院区域ID
     * @param departmentId   科室ID
     * @return 是否有权限
     * @throws backend.common.exception.BizException 有以下2种情况：1.获取不到JWT用户时 2.获取不到用户权限信息
     */
    boolean hasWhitePrivilege(Long userId, Long tenantId, Long hospitalId, Long hospitalAreaId, Long departmentId);

    /**
     * 判断当前用户是否有读取目标租户资源权限。
     *
     * @param tenantId 租户ID
     * @return 是否有权限
     * @throws backend.common.exception.BizException 获取不到JWT用户
     */
    boolean currentUserHasReadPrivilege(TenantId tenantId);

    /**
     * 判断用户是否有读取目标租户资源权限。
     *
     * @param userId   用户ID
     * @param tenantId 租户ID
     * @return 是否有权限
     * @throws backend.common.exception.BizException 获取不到JWT用户
     */
    boolean hasReadPrivilege(Long userId, TenantId tenantId);

    /**
     * 判断当前用户是否有读取目标医院资源权限。
     *
     * @param hospitalId 医院ID
     * @return 是否有权限
     * @throws backend.common.exception.BizException 获取不到JWT用户
     */
    boolean currentUserHasReadPrivilege(HospitalId hospitalId);

    /**
     * 判断用户是否有读取目标医院资源权限。
     *
     * @param userId     用户ID
     * @param hospitalId 医院ID
     * @return 是否有权限
     * @throws backend.common.exception.BizException 获取不到JWT用户
     */
    boolean hasReadPrivilege(Long userId, HospitalId hospitalId);

    /**
     * 判断当前用户是否有读取目标院区资源权限。
     *
     * @param hospitalAreaId 院区ID
     * @return 是否有权限
     * @throws backend.common.exception.BizException 获取不到JWT用户
     */
    boolean currentUserHasReadPrivilege(HospitalAreaId hospitalAreaId);

    /**
     * 判断用户是否有读取目标院区资源权限。
     *
     * @param userId         用户ID
     * @param hospitalAreaId 院区ID
     * @return 是否有权限
     * @throws backend.common.exception.BizException 获取不到JWT用户
     */
    boolean hasReadPrivilege(Long userId, HospitalAreaId hospitalAreaId);

    /**
     * 判断当前用户是否有读取目标科室资源权限。
     *
     * @param departmentId 科室ID
     * @return 是否有权限
     * @throws backend.common.exception.BizException 获取不到JWT用户
     */
    boolean currentUserHasReadPrivilege(DepartmentId departmentId);

    /**
     * 判断用户是否有读取目标科室资源权限。
     *
     * @param userId       用户ID
     * @param departmentId 科室ID
     * @return 是否有权限
     * @throws backend.common.exception.BizException 获取不到JWT用户
     */
    boolean hasReadPrivilege(Long userId, DepartmentId departmentId);

    /**
     * 获取当前用户可读取的租户ID集合
     *
     * @param fillSet 如果读取范围为空，是否填充不存在的id到集合中（避免IN查询出现异常）
     * @return 租户ID集合
     * @throws backend.common.exception.BizException 获取不到JWT用户
     */
    Set<Long> getCurrentUserTenantReadRange(boolean fillSet);

    /**
     * 获取用户可读取的租户ID集合
     *
     * @param userId  用户ID
     * @param fillSet 如果读取范围为空，是否填充不存在的id到集合中（避免IN查询出现异常）
     * @return 租户ID集合
     * @throws backend.common.exception.BizException 获取不到JWT用户
     */
    Set<Long> getUserTenantReadRange(Long userId, boolean fillSet);

    /**
     * 获取当前用户可读取的医院ID集合
     *
     * @param fillSet 如果读取范围为空，是否填充不存在的id到集合中（避免IN查询出现异常）
     * @return 医院ID集合
     * @throws backend.common.exception.BizException 获取不到JWT用户
     */
    Set<Long> getCurrentUserHospitalReadRange(boolean fillSet);

    /**
     * 获取当前用户可读取的医院区域ID集合
     *
     * @param fillSet 如果读取范围为空，是否填充不存在的id到集合中（避免IN查询出现异常）
     * @return 医院区域ID集合
     * @throws backend.common.exception.BizException 获取不到JWT用户
     */
    Set<Long> getCurrentUserHospitalAreaReadRange(boolean fillSet);

    /**
     * 获取当前用户可读取的科室ID集合
     *
     * @param fillSet 如果读取范围为空，是否填充不存在的id到集合中（避免IN查询出现异常）
     * @return 科室ID集合
     */
    Set<Long> getCurrentUserDepartmentReadRange(boolean fillSet);

    /**
     * 获取当前用户的权限树
     *
     * @return 当前用户的权限配置
     * @throws backend.common.exception.BizException 获取不到JWT用户
     */
    List<UserTenantPrivilegeConfig> getCurrentUserTenantPrivilegeConfig();

    /**
     * 获取用户的权限树
     *
     * @param userId 用户ID
     * @return 用户的权限配置
     * @throws backend.common.exception.BizException 获取不到JWT用户
     */
    List<UserTenantPrivilegeConfig> getUserTenantPrivilegeConfig(Long userId);

    String getCurrentUserNickname();

    class ResourceId {
        protected ResourceId(Long id) {
            this.id = id;
        }

        private final Long id;

        public Long getId() {
            return id;
        }
    }

    class TenantId extends ResourceId {
        public TenantId(Long id) {
            super(id);
        }
    }

    class HospitalId extends ResourceId {
        public HospitalId(Long id) {
            super(id);
        }
    }

    class HospitalAreaId extends ResourceId {
        public HospitalAreaId(Long id) {
            super(id);
        }
    }

    class DepartmentId extends ResourceId {
        public DepartmentId(Long id) {
            super(id);
        }
    }
}
