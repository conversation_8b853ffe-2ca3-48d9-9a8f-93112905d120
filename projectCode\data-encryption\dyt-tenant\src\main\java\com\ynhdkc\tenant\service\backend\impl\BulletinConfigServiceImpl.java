package com.ynhdkc.tenant.service.backend.impl;

import backend.common.exception.BizException;
import backend.common.util.JsonUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.ynhdkc.tenant.dao.BulletinConfigMapper;
import com.ynhdkc.tenant.entity.BulletinConfig;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.BulletinConfigService;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_bulletin_config(系统公告配置)】的数据库操作Service实现
 * @createDate 2024-03-04 17:34:29
 */
@Service
@RequiredArgsConstructor
public class BulletinConfigServiceImpl
        implements BulletinConfigService {
    private final BulletinConfigMapper bulletinConfigMapper;
    private final PageVoConvert pageVoConvert;

    @Override
    public BulletinConfigVo get(Long id) {
        return BulletinConfigService.toBulletinVo(bulletinConfigMapper.selectByPrimaryKey(id));
    }

    @Override
    public BulletinConfigVo create(BulletinConfigDto request) {

        BulletinConfig bulletinConfig = BulletinConfigService.toBulletinConfig(request);
        bulletinConfigMapper.insert(bulletinConfig);
        return BulletinConfigService.toBulletinVo(bulletinConfig);
    }

    @Override
    public BulletinConfigVo update(BulletinConfigDto dto) {
        if (!CollectionUtils.isEmpty(bulletinConfigMapper.selectByExample(BulletinConfig.class, sql -> {
            sql.defGroup(condition -> {
                        condition.andEqualTo(BulletinConfig::getStatus, 0);
                        condition.andNotEqualTo(BulletinConfig::getId, dto.getId());
                    }
            );
            sql.builder(builder -> builder
                    .orderByDesc(BulletinConfig::getCreateTime));
        }))) {
            throw new RuntimeException("系统公告配置只能启用一个，请勿重复配置");
        }

        BulletinConfig bulletinConfig = getBulletinConfig(dto);
        bulletinConfigMapper.updateByPrimaryKey(bulletinConfig);
        return BulletinConfigService.toBulletinVo(bulletinConfig);
    }

    private BulletinConfig getBulletinConfig(BulletinConfigDto dto) {
        BulletinConfig bulletinConfig = bulletinConfigMapper.selectOneByExample2(BulletinConfig.class, sql -> sql.andEqualTo(BulletinConfig::getId, dto.getId()));
        if (null == bulletinConfig) {
            throw new BizException(HttpStatus.NOT_FOUND, "公告配置不存在");
        }
        if (null != dto.getStatus()) {
            bulletinConfig.setStatus(dto.getStatus());
        }
        if (null != dto.getSystemStatus()) {
            bulletinConfig.setSystemStatus(dto.getSystemStatus());
        }
        if (StringUtils.hasText(dto.getBulletinContent())) {
            bulletinConfig.setBulletinContent(dto.getBulletinContent());
        }
        return bulletinConfig;
    }

    @Override
    public BulletinConfigPageVo page(BulletinConfigReqDto request) {
        try (final Page<BulletinConfig> page = PageMethod.startPage(request.getCurrentPage(), request.getPageSize())) {
            Page<BulletinConfig> resultPage = page.doSelectPage(() -> bulletinConfigMapper.selectByExample(BulletinConfig.class, sql -> {
                        sql.builder(builder -> builder
                                .orderByDesc(BulletinConfig::getCreateTime));
                    })
            );
            return pageVoConvert.toPageVo(resultPage, BulletinConfigPageVo.class, BulletinConfigService::toBulletinVo);
        }


    }

    @Override
    public CustomBulletinConfigVo queryCurrentBulletinConfig(Long userId) {
        BulletinConfig bulletinConfig = bulletinConfigMapper.selectOneByExample(BulletinConfig.class, sql -> {
            sql.defGroup(condition -> condition.andEqualTo(BulletinConfig::getStatus, 0));
            sql.builder(builder -> builder
                    .orderByDesc(BulletinConfig::getCreateTime));
        });
        if (bulletinConfig != null && StringUtils.hasText(bulletinConfig.getWhiteList())) {
            List<Long> whiteList = JsonUtil.deserializeList(bulletinConfig.getWhiteList(), Long.class);
            if (whiteList.contains(userId)) {
                bulletinConfig.setSystemStatus(0);
            }
        }
        return BulletinConfigService.toCustomBulletinVo(bulletinConfig);
    }
}




