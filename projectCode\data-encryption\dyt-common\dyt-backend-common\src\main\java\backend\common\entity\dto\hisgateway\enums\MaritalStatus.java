package backend.common.entity.dto.hisgateway.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonGetter;

public enum MaritalStatus {

	UNMARRIED("未婚", 10), MARRIED("已婚", 20), WIDOWED("丧偶", 30), DIVORCED("离婚", 40), OTHER("其他", 90);

	private final String value;

	private final Integer code;

	MaritalStatus(String value, Integer code) {
		this.value = value;
		this.code = code;
	}

	@JsonCreator
	public static MaritalStatus getFromCode(int code) {
		for (MaritalStatus t : MaritalStatus.values()) {
			if (t.getCode().equals(code)) {
				return t;
			}
		}
		throw new IllegalArgumentException("婚姻状况不存在");
	}

	public static MaritalStatus getFromValue(String value) {
		for (MaritalStatus t : MaritalStatus.values()) {
			if (t.getValue().equals(value)) {
				return t;
			}
		}
		throw new IllegalArgumentException("婚姻状况不存在");
	}

	public String getValue() {
		return value;
	}

	public Integer getCode() {
		return code;
	}

	@JsonGetter("code")
	public String getRequestCode() {
		return code.toString();
	}

}
