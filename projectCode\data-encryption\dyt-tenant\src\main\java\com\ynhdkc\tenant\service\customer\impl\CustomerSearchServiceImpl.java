package com.ynhdkc.tenant.service.customer.impl;

import backend.common.domain.BaseEntity;
import backend.common.enums.HospitalCode;
import backend.common.exception.BizException;
import backend.common.util.HospitalUtils;
import backend.common.util.MessageUtil;
import backend.common.util.ObjectsUtils;
import com.github.pagehelper.Page;
import com.ynhdkc.oldsystem.integration.api.response.KeywordAdvertisementQueryRespDto;
import com.ynhdkc.oldsystem.integration.client.AdvertisementClient;
import com.ynhdkc.tenant.component.doctors.KunmingMU1stDoctorListHandler;
import com.ynhdkc.tenant.component.doctors.KunmingMU1stScheduleHandler;
import com.ynhdkc.tenant.dao.*;
import com.ynhdkc.tenant.dto.ScheduleResponseDto;
import com.ynhdkc.tenant.dto.document.DepartmentDocument;
import com.ynhdkc.tenant.dto.document.DoctorDocument;
import com.ynhdkc.tenant.dto.document.HospitalDocument;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.entity.DoctorGroupRelation;
import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.entity.constant.HospitalAreaFunctionStatus;
import com.ynhdkc.tenant.entity.constant.HospitalAreaFunctionType;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import com.ynhdkc.tenant.entity.setting.FunctionSetting;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.customer.CustomerDepartmentService;
import com.ynhdkc.tenant.service.customer.CustomerSearchService;
import com.ynhdkc.tenant.tool.CacheComponent;
import com.ynhdkc.tenant.tool.DoctorUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.CaseUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MatchPhraseQueryBuilder;
import org.elasticsearch.index.query.MatchQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.util.Pair;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/8/10 15:50
 */
@SuppressWarnings("SameParameterValue")
@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerSearchServiceImpl implements CustomerSearchService {
    private static final String HIGHLIGHT_PRE_TAG = "<span style=\"color: red\">";
    private static final String HIGHLIGHT_POST_TAG = "</span>";
    private static final String SCORE = "_score";
    private static final long ONE_HOUR = 1000 * 60 * 60;
    private static final Integer SEARCH_PAGE_SIZE = 100;
    private static final Set<String> HOSPITAL_CODES = Collections.unmodifiableSet(new HashSet<>(Arrays.asList("871333", "871232")));
    private final HospitalQuery hospitalQuery;
    private final HospitalAreaQuery hospitalAreaQuery;
    private final HospitalAreaFunctionQuery hospitalAreaFunctionQuery;
    private final DepartmentQuery departmentQuery;
    private final DoctorQuery doctorQuery;
    private final ElasticsearchRestTemplate elasticsearchTemplate;
    private final AdvertisementClient advertisementClient;
    private final CacheComponent cacheComponent;
    private final CustomerDepartmentService customerDepartmentService;
    private final HospitalAreaSettingQuery hospitalAreaSettingQuery;
    private final KunmingMU1stDoctorListHandler kunmingMU1stDoctorListHandler;

    private static Boolean convertToBoolean(Integer value) {
        return null == value ? null : value == 1 ? Boolean.TRUE : Boolean.FALSE;
    }

    private static String hospitalFunctionToTag(FunctionSetting functionSetting) {
        HospitalAreaFunctionType functionType = HospitalAreaFunctionType.getByName(functionSetting.getType());
        if (null == functionType) {
            return null;
        }
        switch (functionType) {
            case appointment_registration:
            case diagnosis_payment:
            case patient_report:
            case hospitalization:
            case special_needs_clinic:
            case multidisciplinary_clinic:
                return functionType.getDesc();
            default:
                return null;
        }
    }

    private static HighlightBuilder.Field createHighlightField(String fieldName) {
        return new HighlightBuilder.Field(fieldName)
                .preTags(HIGHLIGHT_PRE_TAG)
                .postTags(HIGHLIGHT_POST_TAG);
    }

    private static String tryGetHighlightContext(SearchHit<?> hit, String fieldName) {
        String fileName = CaseUtils.toCamelCase(fieldName, false, '_');
        if (!CollectionUtils.isEmpty(hit.getHighlightField(fileName))) {
            return hit.getHighlightField(fileName).get(0);
        }
        try {
            Field field = hit.getContent().getClass().getDeclaredField(fileName);
            field.setAccessible(true);
            return field.get(hit.getContent()).toString();
        } catch (Exception e) {
            return null;
        }
    }

    private static int getElasticsearchPage(Integer currentPage) {
        if (null == currentPage) {
            return 0;
        }
        return Math.max(currentPage - 1, 0);
    }

    private static <T> void safeAddToIndex(int index, List<T> list, T element) {
        if (null == list) {
            list = new ArrayList<>();
            list.add(element);
            return;
        }
        if (index >= list.size()) {
            list.add(element);
            return;
        }
        list.add(index, element);
    }

    private static void processShengZhongSort(SearchDepartmentAndDoctorVo vo) {
        //处理省中袁卓君特殊排序,始终需要排第一
        if (!CollectionUtils.isEmpty(vo.getDoctorList())) {
            CustomerAllScheduleDoctorDetailVo customerAllScheduleDoctorDetailVo = vo.getDoctorList().stream().filter(x -> "袁卓珺".equals(x.getName())).findFirst().orElse(null);
            if (null != customerAllScheduleDoctorDetailVo && HOSPITAL_CODES.contains(customerAllScheduleDoctorDetailVo.getHospitalAreaCode())) {
                vo.getDoctorList().remove(customerAllScheduleDoctorDetailVo);
                vo.getDoctorList().add(0, customerAllScheduleDoctorDetailVo);
            }
        }
    }

    private void handlerGongRenSearchResult(Long hospitalAreaId, List<CustomerAllScheduleDoctorDetailVo> processList) {
        final Long gongRenHospitalId = 54L;
        if (hospitalAreaId != null && hospitalAreaId.equals(gongRenHospitalId)) {
            List<Long> doctorIds = processList.stream().map(CustomerAllScheduleDoctorDetailVo::getId).collect(Collectors.toList());
            List<Doctor> doctors = doctorQuery.queryBy(doctorIds);

            List<Long> departmentIds = doctors.stream().map(Doctor::getDepartmentId).collect(Collectors.toList());
            List<Department> departments = departmentQuery.queryBy(departmentIds);

            for (CustomerAllScheduleDoctorDetailVo doctorDetailVo : processList) {
                Optional<Doctor> doctorOptional = doctors.stream().filter(doctor -> doctor.getId().equals(doctorDetailVo.getId())).findFirst();
                if (doctorOptional.isPresent()) {
                    Doctor doctor = doctorOptional.get();
                    doctorDetailVo.setDisplayDepartmentName(doctor.getDisplayDepartmentName());
                }

                Optional<Department> departmentOptional = departments.stream().filter(department -> department.getId().equals(doctorDetailVo.getDepartmentId())).findFirst();
                if (departmentOptional.isPresent()) {
                    Department department = departmentOptional.get();
                    doctorDetailVo.setDepartmentName(department.getName());
                }
            }
        }
    }

    @Override
    public SearchHospitalVo searchHospital(SearchHospitalReqDto request) {
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(QueryBuilders.boolQuery()
                        .must(QueryBuilders.multiMatchQuery(request.getKeywords(), HospitalDocument.PROPERTY_NAME))
                        .must(QueryBuilders.termsQuery(HospitalDocument.PROPERTY_STATUS, request.getStatus())))
                .withPageable(PageRequest.of(getElasticsearchPage(request.getCurrentPage()), request.getPageSize()))
                .withSort(SortBuilders.fieldSort(SCORE).order(SortOrder.DESC))
                .withHighlightFields(createHighlightField(HospitalDocument.PROPERTY_NAME))
                .build();
        SearchHits<HospitalDocument> searchHits = elasticsearchTemplate.search(query, HospitalDocument.class, IndexCoordinates.of(HospitalDocument.INDEX_NAME));
        List<CustomerHospitalSearchVo> collected = searchHits.stream()
                .sorted(Comparator.comparing(hospitalDocumentSearchHit -> ((SearchHit<?>) hospitalDocumentSearchHit).getScore())
                        .thenComparingInt(hospitalDocumentSearchHit -> {
                            Integer sort = ((HospitalDocument) ((SearchHit<?>) hospitalDocumentSearchHit).getContent()).getDisplaySort();
                            return null == sort ? 0 : sort;
                        }).reversed())
                .map(hit -> {
                    CustomerHospitalSearchVo vo = new CustomerHospitalSearchVo();
                    vo.setId(hit.getContent().getId());
                    vo.setName(tryGetHighlightContext(hit, HospitalDocument.PROPERTY_NAME));
                    vo.setLogo(hit.getContent().getLogo());
                    vo.setDisplay(convertToBoolean(hit.getContent().getDisplay()));
                    vo.setStatus(hit.getContent().getStatus());
                    Set<Long> hospitalAreaIds = hospitalAreaQuery.queryHospitalAreaByParentId(hit.getContent().getId())
                            .stream()
                            .map(Hospital::getId)
                            .collect(Collectors.toSet());
                    List<String> businessTags = hospitalAreaFunctionQuery.pageQuery(
                                    new HospitalAreaFunctionQuery.HospitalAreaFunctionQueryOption(1, Integer.MAX_VALUE)
                                            .setIncludeHospitalAreaIds(hospitalAreaIds)
                            ).stream()
                            .map(functionSetting -> {
                                if (HospitalAreaFunctionStatus.OPEN.getCode().equals(functionSetting.getStatus())) {
                                    return hospitalFunctionToTag(functionSetting);
                                }
                                return null;
                            })
                            .filter(Objects::nonNull)
                            .distinct()
                            .collect(Collectors.toList());
                    vo.setBusinessTags(businessTags);
                    vo.setCustomerTags(new ArrayList<>());
                    return vo;
                })
                .collect(Collectors.toList());
        Set<Long> hospitalIds = collected.stream()
                .map(CustomerHospitalSearchVo::getId)
                .collect(Collectors.toSet());

        Map<Long, List<Hospital>> hospitalAreaMap = hospitalAreaQuery.pageQueryHospitalArea(new HospitalAreaQuery.HospitalAreaQueryOption(1, Integer.MAX_VALUE)
                        .setIncludeHospitalIds(hospitalIds)
                ).stream()
                .collect(Collectors.groupingBy(Hospital::getParentId));
        Set<Integer> activeStatus = new HashSet<>(request.getStatus());
        List<AppointmentRuleSetting> appointmentRuleSettings = hospitalAreaSettingQuery.All();
        collected.forEach(hospitalSearchVo -> {
            List<Hospital> hospitalAreaList = hospitalAreaMap.get(hospitalSearchVo.getId());
            if (CollectionUtils.isEmpty(hospitalAreaList)) {
                hospitalSearchVo.setHospitalAreaList(Collections.emptyList());
                return;
            }
            hospitalSearchVo.setHospitalAreaList(hospitalAreaList.stream()
                    .filter(hospitalArea -> activeStatus.contains(hospitalArea.getStatus()) && hospitalArea.getDisplay())
                    .map(hospitalArea -> {
                        CustomerHospitalAreaVo vo = new CustomerHospitalAreaVo();
                        vo.setId(hospitalArea.getId());
                        vo.setHospitalId(hospitalArea.getParentId());
                        vo.setName(hospitalArea.getName());
                        vo.setHospitalAreaCode(hospitalArea.getHospitalCode());
                        return vo;
                    })
                    .collect(Collectors.toList()));
            if (!hospitalSearchVo.getHospitalAreaList().isEmpty()) {
                hospitalSearchVo.getHospitalAreaList().forEach(hospitalArea -> {
                    Optional<AppointmentRuleSetting> appointmentRuleSettingOptional = appointmentRuleSettings.stream().filter(appointmentRuleSetting -> appointmentRuleSetting.getHospitalAreaId().equals(hospitalArea.getId())).findFirst();
                    hospitalArea.setCurrentSystemType(appointmentRuleSettingOptional.map(AppointmentRuleSetting::getCurrentSystemType).orElse(null));
                });
            }
        });
        return new SearchHospitalVo()
                .total(searchHits.getTotalHits())
                .hospitalList(collected);
    }

    @Override
    public SearchDepartmentAndDoctorVo searchDepartmentAndDoctor(SearchDepartmentAndDoctorReqDto request) {
        SearchDepartmentAndDoctorVo vo = new SearchDepartmentAndDoctorVo();
        switch (request.getType()) {
            case 0: {
                Pair<Long, List<CustomerDepartmentSearchVo>> searchedDepartment = searchDepartment(request.getHospitalAreaId(), request.getKeywords(), request.isDepartmentEnabled(), getElasticsearchPage(request.getCurrentPage()), request.getPageSize());
                vo.setDepartmentTotal(searchedDepartment.getFirst());
                vo.setDepartmentList(searchedDepartment.getSecond());
                Pair<Long, List<CustomerAllScheduleDoctorDetailVo>> searchedDoctor = searchDoctor(request.getHospitalAreaId(), request.getKeywords(), request.getDoctorStatus(), request.getCurrentPage(), request.getPageSize());
                vo.setDoctorTotal(searchedDoctor.getFirst());
                vo.setDoctorList(searchedDoctor.getSecond());
                break;
            }
            case 1: {
                Pair<Long, List<CustomerDepartmentSearchVo>> searchedDepartment = searchDepartment(request.getHospitalAreaId(), request.getKeywords(), request.isDepartmentEnabled(), getElasticsearchPage(request.getCurrentPage()), request.getPageSize());
                vo.setDepartmentTotal(searchedDepartment.getFirst());
                vo.setDepartmentList(searchedDepartment.getSecond());
                break;
            }
            case 2: {
                Pair<Long, List<CustomerAllScheduleDoctorDetailVo>> searchedDoctor = searchDoctor(request.getHospitalAreaId(), request.getKeywords(), request.getDoctorStatus(), getElasticsearchPage(request.getCurrentPage()), request.getPageSize());
                vo.setDoctorTotal(searchedDoctor.getFirst());
                vo.setDoctorList(searchedDoctor.getSecond());
                break;
            }
            default:
                throw new BizException(HttpStatus.BAD_REQUEST, "搜索类型错误");
        }
//        if (request.getType() != 1) {
//            importAdvertisementDoctor(request.getKeywords(), vo);
//        }

        processShengZhongSort(vo);
        return vo;
    }

    private Pair<Long, List<CustomerDepartmentSearchVo>> searchDepartment(Long hospitalAreaId, String keywords, Boolean enabled, Integer currentPage, Integer pageSize) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.multiMatchQuery(keywords, DepartmentDocument.PROPERTY_NAME, DepartmentDocument.PROPERTY_SHORT_NAME, DepartmentDocument.PROPERTY_INTRODUCTION))
                .must(QueryBuilders.termQuery(DepartmentDocument.PROPERTY_ENABLED, Boolean.TRUE.equals(enabled) ? 1 : 0));
        if (null != hospitalAreaId) {
            boolQueryBuilder.must(QueryBuilders.termQuery(DepartmentDocument.PROPERTY_HOSPITAL_AREA_ID, hospitalAreaId));
        }

        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withSort(SortBuilders.fieldSort(SCORE).order(SortOrder.DESC))
                .withPageable(PageRequest.of(currentPage, pageSize))
                .withHighlightFields(
                        createHighlightField(DepartmentDocument.PROPERTY_NAME),
                        createHighlightField(DepartmentDocument.PROPERTY_SHORT_NAME),
                        createHighlightField(DepartmentDocument.PROPERTY_INTRODUCTION)
                ).build();

        SearchHits<DepartmentDocument> searchHits = elasticsearchTemplate.search(query, DepartmentDocument.class, IndexCoordinates.of(DepartmentDocument.INDEX_NAME));

        Pair<Long, List<CustomerDepartmentSearchVo>> totalHitsAndDepartments = Pair.of(
                searchHits.getTotalHits(),
                searchHits.stream()
                        .map(hit -> {
                            CustomerDepartmentSearchVo vo = new CustomerDepartmentSearchVo();
                            vo.setId(hit.getContent().getId());
                            vo.setName(tryGetHighlightContext(hit, DepartmentDocument.PROPERTY_NAME));
                            vo.setShortName(tryGetHighlightContext(hit, DepartmentDocument.PROPERTY_SHORT_NAME));
                            vo.setHospitalId(hit.getContent().getHospitalId());
                            vo.setHospitalLogo(hit.getContent().getHospitalLogo());
                            vo.setHospitalAreaId(hit.getContent().getHospitalAreaId());
                            vo.setHospitalAreaName(tryGetHighlightContext(hit, DepartmentDocument.PROPERTY_HOSPITAL_AREA_NAME));
                            vo.setHospitalAreaCode(hit.getContent().getHospitalAreaCode());
                            vo.setAddressIntro(hit.getContent().getAddressIntro());
                            vo.setIntroduction(tryGetHighlightContext(hit, DepartmentDocument.PROPERTY_INTRODUCTION));
                            vo.setRecommended(convertToBoolean(hit.getContent().getRecommended()));
                            vo.setUri(hit.getContent().getUri());
                            vo.setThrdpartDepCode(hit.getContent().getThrdpartDepCode());
                            vo.setEnabled(convertToBoolean(hit.getContent().getEnabled()));
                            return vo;
                        })
                        .collect(Collectors.toList())
        );

        List<CustomerDepartmentSearchVo> departmentList = totalHitsAndDepartments.getSecond();
        if (CollectionUtils.isEmpty(departmentList)) {
            return totalHitsAndDepartments;
        }

        List<Long> departmentIds = departmentList.stream().map(CustomerDepartmentSearchVo::getId).collect(Collectors.toList());
        List<Doctor> doctors = doctorQuery.queryByDepartmentIds(departmentIds);
        departmentList.forEach(item -> {
            List<Doctor> doctorList = doctors.stream().filter(doctor -> doctor.getDepartmentId().equals(item.getId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(doctorList)) {
                item.setCanOrder(false);
                return;
            }

            doctorList.forEach(doctorItem -> {
                if (item.isCanOrder() != null && item.isCanOrder()) {
                    return;
                }

                if (!StringUtils.hasText(doctorItem.getHospitalCode()) || doctorItem.getDepartmentId() == null || !StringUtils.hasText(doctorItem.getDepartmentCode()) || doctorItem.getId() == null || !StringUtils.hasText(doctorItem.getThrdpartDoctorCode())) {
                    item.setCanOrder(false);
                    return;
                }
                List<ScheduleResponseDto.ScheduleInfo> scheduleInfo = cacheComponent.getDoctorScheduleList(doctorItem.getHospitalCode(), doctorItem.getDepartmentId(), doctorItem.getDepartmentCode(), doctorItem.getThrdpartDoctorCode(), doctorItem.getId());
                if (CollectionUtils.isEmpty(scheduleInfo)) {
                    item.setCanOrder(false);
                    return;
                }

                boolean canOrder = scheduleInfo.stream().anyMatch(schedule -> null != schedule.getSrcNum() && schedule.getSrcNum() > 0);
                item.setCanOrder(canOrder);
            });
        });
        return totalHitsAndDepartments;
    }

    private BoolQueryBuilder assembleBoolQueryBuilder(String hospitalAreaCode, String keywords) {
        if (HospitalUtils.isKunmingMU1stHospital(hospitalAreaCode)) {
//            return QueryBuilders.boolQuery()
//                    .must(QueryBuilders.matchPhraseQuery(DoctorDocument.PROPERTY_NAME, keywords))
//                    .must(QueryBuilders.termQuery(DoctorDocument.PROPERTY_DISPLAY, true));
//                    .must(QueryBuilders.termQuery(DoctorDocument.PROPERTY_SOURCE, DepartmentSourceEnum.HIS.getCode()));

            // 1. 医生名字必须完全匹配
            MatchPhraseQueryBuilder nameQuery = QueryBuilders.matchPhraseQuery(DoctorDocument.PROPERTY_NAME, keywords);
            // 2. 擅长领域中包含关键字（模糊匹配）
            MatchQueryBuilder specialityQuery = QueryBuilders.matchQuery(DoctorDocument.PROPERTY_SPECIALITY, keywords);
            // 3. 组合名字和擅长领域的条件（必须匹配名字，或者擅长领域中包含关键字）
            BoolQueryBuilder nameOrSpecialityQuery = QueryBuilders.boolQuery()
                    .should(nameQuery)  // 医生名字完全匹配
                    .should(specialityQuery)  // 或者擅长领域中包含关键字
                    .minimumShouldMatch(1);  // 至少匹配一个 should 条件
            return QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery(DoctorDocument.PROPERTY_DISPLAY, true))
                    .must(nameOrSpecialityQuery);
        }
        if (HospitalCode.YUNNAN_FIRST_PEOPLE_HOSPITAL.getCode().equals(hospitalAreaCode)
                || HospitalCode.YUNNAN_FIRST_PEOPLE_HOSPITAL_V2.getCode().equals(hospitalAreaCode)) {
            return QueryBuilders.boolQuery()
                    .must(assembleSubQuery(keywords))
                    .must(QueryBuilders.termQuery(DoctorDocument.PROPERTY_DISPLAY, true));
        }
        return QueryBuilders.boolQuery()
                .must(QueryBuilders.multiMatchQuery(keywords, DoctorDocument.PROPERTY_NAME, DoctorDocument.PROPERTY_SPECIALITY))
                .must(QueryBuilders.termQuery(DoctorDocument.PROPERTY_DISPLAY, true));
    }

    private BoolQueryBuilder assembleSubQuery(String keywords) {
        return QueryBuilders.boolQuery()
                .should(QueryBuilders.matchPhraseQuery(DoctorDocument.PROPERTY_NAME, keywords))
                .should(QueryBuilders.matchPhraseQuery(DoctorDocument.PROPERTY_SPECIALITY, keywords));
    }


    private Pair<Long, List<CustomerAllScheduleDoctorDetailVo>> searchDoctor(Long hospitalAreaId, String keywords, List<Integer> statusList, Integer currentPage, Integer pageSize) {
        Hospital hospital = hospitalAreaQuery.queryHospitalAreaBy(hospitalAreaId);
        String hospitalAreaCode = hospital == null ? "" : hospital.getHospitalCode();
        List<CustomerAllScheduleDoctorDetailVo> resultList;
        if (StringUtils.hasText(keywords)) {
            currentPage = getElasticsearchPage(currentPage);
            resultList = getDoctorInfoFromEs(hospitalAreaId, keywords, statusList, currentPage, pageSize + 10, hospitalAreaCode);
        } else {
            resultList = getDoctorInfoFromDb(hospitalAreaId, currentPage, pageSize, hospitalAreaCode);
        }
        log.info("从es查询到的医生数据如下:{}", MessageUtil.object2JSONString(resultList));


        List<Long> departmentIds = resultList.stream().map(CustomerDoctorVo::getDepartmentId).collect(Collectors.toList());
        List<Long> enableDepartmentIds = departmentQuery.queryEnabledDepartmentIds(departmentIds);
        if (enableDepartmentIds.isEmpty() && !HospitalUtils.isKunmingMU1stHospital(hospitalAreaCode)) {
            return Pair.of(0L, Collections.emptyList());
        }
        List<Long> enabledDoctorIds = doctorQuery.queryEnableDoctorIds(departmentIds);

        Map<String, CustomerAllScheduleDoctorDetailVo> processDoctorMap = new LinkedHashMap<>();

        resultList = filterSearchDoctors(resultList, hospitalAreaCode);
        log.info("被莫名过滤后的数据如下:{}", MessageUtil.object2JSONString(resultList));

        for (CustomerAllScheduleDoctorDetailVo customerAllScheduleDoctorDetailVo : resultList) {
            if (!enabledDoctorIds.contains(customerAllScheduleDoctorDetailVo.getId())) {
                continue;
            }
            String key = String.format("%s_%s", customerAllScheduleDoctorDetailVo.getDepartmentCode(), customerAllScheduleDoctorDetailVo.getThrdpartDoctorCode());
            CustomerAllScheduleDoctorDetailVo lastOne = processDoctorMap.get(key);
            if (lastOne != null && lastOne.getDepartmentId() > customerAllScheduleDoctorDetailVo.getDepartmentId()) {
                continue;
            }
            processDoctorMap.put(key, customerAllScheduleDoctorDetailVo);
        }
        log.info("search_doctor_2: {}", MessageUtil.object2JSONString(processDoctorMap));

        List<CustomerAllScheduleDoctorDetailVo> processList = processList(processDoctorMap, pageSize, hospitalAreaId);

        log.info("search_doctor_3: {}", MessageUtil.object2JSONString(processList));


        return Pair.of((long) processList.size(), processList);
    }

    private List<CustomerAllScheduleDoctorDetailVo> filterSearchDoctors(List<CustomerAllScheduleDoctorDetailVo> resultList,
                                                                        String hospitalAreaCode) {
        if (HospitalUtils.isKunmingMU1stHospital(hospitalAreaCode)) {
            return resultList.stream().filter(y -> !KunmingMU1stScheduleHandler.CONSULTATION_DEPARTMENT_CODE.equals(y.getDepartmentCode())
                    && !ObjectsUtils.isEmpty(y.getDateInWeeks())).collect(Collectors.toList());
        }
        return resultList;
    }

    private List<CustomerAllScheduleDoctorDetailVo> getDoctorInfoFromEs(Long hospitalAreaId, String keywords, List<Integer> statusList,
                                                                        Integer currentPage, Integer pageSize, String hospitalAreaCode) {
        BoolQueryBuilder boolQueryBuilder = assembleBoolQueryBuilder(hospitalAreaCode, keywords);
        if (null != hospitalAreaId) {
            boolQueryBuilder.must(QueryBuilders.termQuery(DoctorDocument.PROPERTY_HOSPITAL_AREA_ID, hospitalAreaId));
        }
        if (!ObjectsUtils.isEmpty(statusList)) {
            int status = statusList.get(0);
            boolQueryBuilder.must(QueryBuilders.termQuery(DoctorDocument.PROPERTY_STATUS, status));
        }

        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withSort(SortBuilders.fieldSort(SCORE).order(SortOrder.DESC))
                .withPageable(PageRequest.of(currentPage, pageSize))
                .withHighlightFields(
                        createHighlightField(DoctorDocument.PROPERTY_NAME),
                        createHighlightField(DoctorDocument.PROPERTY_SPECIALITY)
                )
                .build();

        String doctorIdxName = getDoctorIndex();

        SearchHits<DoctorDocument> searchHits2 = elasticsearchTemplate.search(query, DoctorDocument.class, IndexCoordinates.of(doctorIdxName));

        log.info("search_doctor_1: {} {} {}", MessageUtil.object2JSONString(searchHits2), keywords, currentPage);

        List<Long> doctorIds = searchHits2.stream().map(q -> q.getContent().getId()).collect(Collectors.toList());

        List<DoctorGroupRelation> doctorGroupRelations = doctorQuery.listDoctorGroupRelationBy(doctorIds);
        Optional<AppointmentRuleSetting> appointmentRuleSetting = hospitalAreaSettingQuery.queryAppointmentRuleSettingBy(hospitalAreaId);

        List<CustomerAllScheduleDoctorDetailVo> resultList = searchHits2.stream()
                .map(hit -> {
                    CustomerAllScheduleDoctorDetailVo vo = new CustomerAllScheduleDoctorDetailVo();
                    vo.setId(hit.getContent().getId());
                    vo.setName(tryGetHighlightContext(hit, DoctorDocument.PROPERTY_NAME));
                    vo.setThrdpartDoctorCode(hit.getContent().getDoctorCode());
                    vo.setHospitalId(hit.getContent().getHospitalId());
                    vo.setHospitalAreaId(hit.getContent().getHospitalAreaId());
                    vo.setHospitalAreaName(tryGetHighlightContext(hit, DoctorDocument.PROPERTY_HOSPITAL_AREA_NAME));
                    vo.setHospitalAreaCode(hit.getContent().getHospitalAreaCode());
                    vo.setDepartmentId(hit.getContent().getDepartmentId());
                    vo.setDepartmentName(hit.getContent().getDepartmentName());
                    vo.setDepartmentCode(hit.getContent().getDepartmentCode());
                    vo.setHeadImgUrl(hit.getContent().getHeadImg());
                    vo.setSpeciality(tryGetHighlightContext(hit, DoctorDocument.PROPERTY_SPECIALITY));
                    vo.setBelongDoctorGroups(doctorQuery.hasDoctorGroup(vo.getId()));

                    enrichDoctorScheduleInfo(vo, doctorGroupRelations, appointmentRuleSetting, hospitalAreaCode);
                    return vo;
                })
                .collect(Collectors.toList());
        return resultList;
    }

    private List<CustomerAllScheduleDoctorDetailVo> getDoctorInfoFromDb(Long hospitalAreaId, Integer currentPage, Integer pageSize, String hospitalAreaCode) {

        List<Department> departmentList = departmentQuery.queryBy(hospitalAreaCode);
        List<Long> departmentIds = departmentList.stream().filter(Department::getEnabled).map(Department::getId).collect(Collectors.toList());
        DoctorQuery.DoctorQueryOption option = new DoctorQuery.DoctorQueryOption(currentPage, pageSize)
                .setHospitalAreaId(hospitalAreaId)
                .setIncludeDepartmentIds(departmentIds);
        Page<Doctor> doctorPage = doctorQuery.pageQuery(option);
        List<Doctor> doctors = doctorPage.getResult();

        List<Long> doctorIds = doctors.stream().map(BaseEntity::getId).collect(Collectors.toList());

        List<DoctorGroupRelation> doctorGroupRelations = doctorQuery.listDoctorGroupRelationBy(doctorIds);
        Optional<AppointmentRuleSetting> appointmentRuleSetting = hospitalAreaSettingQuery.queryAppointmentRuleSettingBy(hospitalAreaId);

        List<CustomerAllScheduleDoctorDetailVo> resultList = doctors.stream()
                .map(hit -> {
                    CustomerAllScheduleDoctorDetailVo vo = new CustomerAllScheduleDoctorDetailVo();
                    vo.setId(hit.getId());
                    vo.setName(hit.getName());
                    vo.setThrdpartDoctorCode(hit.getThrdpartDoctorCode());
                    vo.setHospitalId(hit.getHospitalId());
                    vo.setHospitalAreaId(hit.getHospitalAreaId());
                    vo.setHospitalAreaCode(hit.getHospitalCode());
                    vo.setDepartmentId(hit.getDepartmentId());
                    vo.setDepartmentName(hit.getDisplayDepartmentName());
                    vo.setDepartmentCode(hit.getDepartmentCode());
                    vo.setHeadImgUrl(hit.getHeadImgUrl());
                    vo.setSpeciality(hit.getSpeciality());
                    vo.setBelongDoctorGroups(doctorQuery.hasDoctorGroup(vo.getId()));

                    enrichDoctorScheduleInfo(vo, doctorGroupRelations, appointmentRuleSetting, hospitalAreaCode);
                    return vo;
                })
                .collect(Collectors.toList());
        return resultList;
    }

    private List<CustomerAllScheduleDoctorDetailVo> processList(Map<String, CustomerAllScheduleDoctorDetailVo> processDoctorMap,
                                                                int pageSize, Long hospitalAreaId) {
        List<CustomerAllScheduleDoctorDetailVo> processList = new ArrayList<>(processDoctorMap.values());
        handlerGongRenSearchResult(hospitalAreaId, processList);

        return processList.subList(0, Math.min(processList.size(), pageSize));
    }

    private String getDoctorIndex() {
        LocalDateTime localDateTime = DoctorUtils.convertDateToLocalDateTime(new Date());
        int hour = localDateTime.getHour();
        int minute = localDateTime.getMinute();
        if (hour == 8 && minute <= 30) {
            long dateTime = System.currentTimeMillis() - (9 * ONE_HOUR);
            return String.format("doctor-info-%s", DoctorUtils.getUTCDateStr(dateTime, "yyyy.MM.dd"));
        }
        return String.format("doctor-info-%s", DoctorUtils.getUTCDateStr(System.currentTimeMillis(), "yyyy.MM.dd"));
    }

    private void enrichDoctorScheduleInfo(CustomerAllScheduleDoctorDetailVo vo, List<DoctorGroupRelation> doctorGroupRelations,
                                          Optional<AppointmentRuleSetting> appointmentRuleSettingOptional, String hospitalAreaCode) {
        if (!StringUtils.hasText(vo.getHospitalAreaCode()) || vo.getDepartmentId() == null || !StringUtils.hasText(vo.getDepartmentCode()) || vo.getId() == null || !StringUtils.hasText(vo.getThrdpartDoctorCode())) {
            return;
        }
        List<ScheduleResponseDto.ScheduleInfo> scheduleInfo = cacheComponent.getDoctorScheduleList(vo.getHospitalAreaCode(), vo.getDepartmentId(), vo.getDepartmentCode(), vo.getThrdpartDoctorCode(), vo.getId());
        if (scheduleInfo == null && HospitalUtils.isKunmingMU1stHospital(hospitalAreaCode)) {
            scheduleInfo = kunmingMU1stDoctorListHandler.queryScheduleInfo4Search(vo);
        }
        if (CollectionUtils.isEmpty(scheduleInfo)) {
            return;
        }
        Integer displayDoctorUnderDepartment = appointmentRuleSettingOptional.map(AppointmentRuleSetting::getDisplayDoctorUnderDepartment).orElse(0);

        if (HospitalUtils.isKunmingMU1stHospital(hospitalAreaCode)) {
            log.info("enrichDoctorScheduleInfo_1: {}", MessageUtil.object2JSONString(scheduleInfo));
            kunmingMU1stDoctorListHandler.enrichCustomerAllScheduleDoctorDetailVo(scheduleInfo, vo.getDepartmentName(), doctorGroupRelations, displayDoctorUnderDepartment, vo);
        } else {
            customerDepartmentService.enrichCustomerAllScheduleDoctorDetailVo(scheduleInfo, vo.getDepartmentName(), doctorGroupRelations, displayDoctorUnderDepartment, vo);
        }
    }

    private void importAdvertisementDoctor(String keyword, SearchDepartmentAndDoctorVo vo) {
        KeywordAdvertisementQueryRespDto advertisementQueryRespDto = advertisementClient.queryRelatedAdvertisementByKeyword(keyword);
        if (CollectionUtils.isEmpty(advertisementQueryRespDto.getRecommendAdvertisements())) {
            return;
        }
        if (null == vo.getDoctorList()) {
            vo.setDoctorList(new ArrayList<>());
        }
        Set<String> doctorSet = vo.getDoctorList().stream()
                .map(doctor -> doctor.getHospitalAreaCode() + doctor.getDepartmentCode() + doctor.getThrdpartDoctorCode())
                .collect(Collectors.toSet());
        advertisementQueryRespDto.getRecommendAdvertisements().stream()
                .filter(recommendAdvertisement -> Objects.equals(recommendAdvertisement.getType(), "DOCTOR"))
                .filter(recommendAdvertisement -> null != recommendAdvertisement.getHospitalAreaCode())
                .filter(recommendAdvertisementDto -> null != recommendAdvertisementDto.getDepartmentCode())
                .filter(recommendAdvertisementDto -> null != recommendAdvertisementDto.getDoctorCode())
                .forEach(recommendAdvertisementDto -> {
                    if (doctorSet.contains(recommendAdvertisementDto.getHospitalAreaCode() + recommendAdvertisementDto.getDepartmentCode() + recommendAdvertisementDto.getDoctorCode())) {
                        return;
                    }
                    Doctor doctor = doctorQuery.queryByCode(recommendAdvertisementDto.getHospitalAreaCode(), recommendAdvertisementDto.getDepartmentCode(), recommendAdvertisementDto.getDoctorCode());
                    CustomerAllScheduleDoctorDetailVo doctorSearchVo = new CustomerAllScheduleDoctorDetailVo();
                    doctorSearchVo.setId(doctor.getId());
                    doctorSearchVo.setName(doctor.getName());
                    doctorSearchVo.setThrdpartDoctorCode(doctor.getThrdpartDoctorCode());
                    doctorSearchVo.setHospitalId(doctor.getHospitalId());

                    doctorSearchVo.setHospitalAreaId(doctor.getHospitalAreaId());
                    doctorSearchVo.setHospitalAreaCode(doctor.getHospitalCode());
                    Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaById(doctor.getHospitalAreaId());
                    if (null != hospitalArea) {
                        doctorSearchVo.setHospitalAreaName(hospitalArea.getName());
                    }

                    doctorSearchVo.setDepartmentId(doctor.getDepartmentId());
                    doctorSearchVo.setDepartmentCode(doctor.getDepartmentCode());
                    Department department = departmentQuery.queryDepartmentById(doctor.getDepartmentId());
                    if (null != department) {
                        doctorSearchVo.setDepartmentName(department.getName());
                    }

                    doctorSearchVo.setHeadImgUrl(doctor.getHeadImgUrl());
                    doctorSearchVo.setSpeciality(doctor.getSpeciality());
                    if (CollectionUtils.isEmpty(recommendAdvertisementDto.getCustomerTags())) {
                        doctorSearchVo.setCustomerTags(Collections.emptyList());
                    } else {
                        doctorSearchVo.setCustomerTags(recommendAdvertisementDto.getCustomerTags());
                    }

                    safeAddToIndex(recommendAdvertisementDto.getPosition() - 1, vo.getDoctorList(), doctorSearchVo);
                });
    }
}
