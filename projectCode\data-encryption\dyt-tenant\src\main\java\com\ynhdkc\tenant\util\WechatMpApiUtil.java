package com.ynhdkc.tenant.util;

import backend.common.constants.ChannelConstant;
import backend.common.exception.BizException;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class WechatMpApiUtil {
    private static final String DYT_ACCESS_TOKEN_URL = "https://touch.ynhdkc.com/Token/new-wechat";
    private static final String DYT_OLD_ACCESS_TOKEN_URL = "https://touch.ynhdkc.com/Token/wechat";
    private static final Map<Integer, String> accessTokenUrlMap = new HashMap<>();

    static {
        accessTokenUrlMap.put(ChannelConstant.WechatMpChannel.DYT.getChannelCode(), DYT_ACCESS_TOKEN_URL);
        accessTokenUrlMap.put(ChannelConstant.WechatMpChannel.DYT_OLD.getChannelCode(), DYT_OLD_ACCESS_TOKEN_URL);
    }

    public static final Map<Integer, String> redisKeyMap = new HashMap<>();

    static {
        redisKeyMap.put(ChannelConstant.WechatMpChannel.DYT.getChannelCode(), "dyt-tenant:backend:new-media:default-wx-mp-menu:dyt");
        redisKeyMap.put(ChannelConstant.WechatMpChannel.DYT_OLD.getChannelCode(), "dyt-tenant:backend:new-media:default-wx-mp-menu:dyt-old");
    }

    private static final String CREATE_WECHAT_MP_MENU_URL = "https://api.weixin.qq.com/cgi-bin/menu/create?access_token=";
    private static final String QUERY_WECHAT_MP_MENU_URL = "https://api.weixin.qq.com/cgi-bin/get_current_selfmenu_info?access_token=";

    private final RestTemplate restTemplate = new RestTemplate();
    private final RedisTemplate<String, String> redisTemplate;

    private String getAccessToken(Integer channelCode) {
        String url = accessTokenUrlMap.get(channelCode);
        if (url == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "非法的公众号渠道");
        }
        try {
            JSONObject result = restTemplate.getForObject(url, JSONObject.class);
            assert result != null;
            return result.getStr("token");
        } catch (RuntimeException e) {
            log.error("获取公众号access_token失败", e);
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "获取公众号access_token失败");
        }
    }

    public void publishWechatMpMenu(Integer channelCode, String menuJson) {
        String accessToken = getAccessToken(channelCode);
        String url = CREATE_WECHAT_MP_MENU_URL + accessToken;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        HttpEntity<String> httpEntity = new HttpEntity<>(menuJson, headers);
        JSONObject result = restTemplate.postForObject(url, httpEntity, JSONObject.class);
        assert result != null;
        if (result.getInt("errcode") != 0) {
            log.error("发布公众号菜单错误，code:{}，errMsg:{}", result.getInt("errcode"), result.getStr("errmsg"));
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "设置公众号菜单失败");
        }
    }

    public void publishDefaultWechatMpMenu(Integer channelCode) {
        String defaultMenuRedisKey = redisKeyMap.get(channelCode);
        if (defaultMenuRedisKey == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "非法的公众号渠道");
        }
        String defaultMenuStr = redisTemplate.opsForValue().get(defaultMenuRedisKey);
        if (defaultMenuStr == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "不存在默认公众号菜单数据");
        }
        publishWechatMpMenu(channelCode, defaultMenuStr);
    }

    public JSONObject getWechatMpMenu(Integer channelCode) {
        String accessToken = getAccessToken(channelCode);
        String url = QUERY_WECHAT_MP_MENU_URL + accessToken;
        JSONObject response = restTemplate.getForObject(url, JSONObject.class);
        assert response != null;
        return response.getJSONObject("selfmenu_info");
    }

    public JSONObject convertQueryMenuToSetMenu(JSONObject queryMenuJson) {
        JSONArray button = queryMenuJson.getJSONArray("button");
        JSONArray resultButton = new JSONArray();
        button.forEach(item -> {
            JSONObject itemJson = JSONUtil.parseObj(item);
            JSONObject subButton = itemJson.getJSONObject("sub_button");
            JSONArray list = subButton.getJSONArray("list");
            itemJson.set("sub_button", list);
            resultButton.add(itemJson);
        });

        return new JSONObject().set("button", resultButton);
    }
}
