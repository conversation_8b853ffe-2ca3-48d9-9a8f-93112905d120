package com.ynhdkc.tenant.doctor.info;

import lombok.Getter;

@Getter
public enum YunDaLevelEnum {
    JUNIOR_WORKER(20, "初级工"),
    TRANSLATOR(32, "翻译"),
    ASSOCIATE_PROFESSOR(903, "副教授"),
    ASSOCIATE_RESEARCH_LIBRARIAN(33, "副研究馆员"),
    ASSOCIATE_RESEARCHER(911, "副研究员"),
    DEPUTY_CHIEF_NURSE(8, "副主任护师"),
    DEPUTY_CHIEF_TECHNICIAN(52, "副主任技师"),
    DEPUTY_CHIEF_LABORATORY_TECHNICIAN(56, "副主任检验师"),
    DEPUTY_CHIEF_PHARMACIST(41, "副主任药师"),
    DEPUTY_CHIEF_PHYSICIAN(2, "副主任医师"),
    SENIOR_WORKER(22, "高级工"),
    SENIOR_ENGINEER(15, "高级工程师"),
    SENIOR_ACCOUNTANT(25, "高级会计师"),
    SENIOR_POLITICAL_WORKER(63, "高级政工师"),
    ENGINEER(16, "工程师"),
    WORKER_TECHNICIAN(23, "工人技师"),
    LIBRARIAN(34, "馆员"),
    STANDARDIZED_NURSE(104, "规培护士"),
    STANDARDIZED_PHYSICIAN(103, "规培医师"),
    CONTRACT_NURSE(65, "合同护士"),
    NURSE(11, "护士"),
    ACCOUNTANT(26, "会计师"),
    ACCOUNTANT_CLERK(28, "会计员"),
    TECHNICIAN(51, "技师"),
    TECHNICIAN_2(54, "技师"),
    TECHNICIAN_3(55, "技士"),
    TECHNICIAN_4(44, "技士"),
    TECHNICIAN_5(18, "技术员"),
    LABORATORY_TECHNICIAN(58, "检验师"),
    LABORATORY_TECHNICIAN_2(59, "检验士"),
    LECTURER(904, "讲师"),
    PROFESSOR(902, "教授"),
    ADVANCED_NURSE(101, "进修护士"),
    ADVANCED_PHYSICIAN(100, "进修医师"),
    ECONOMIST(24, "经济师"),
    GENERAL_WORKER(19, "普工"),
    EXPERIMENTALIST(13, "实验师"),
    STATISTICIAN(29, "统计师"),
    STATISTICIAN_2(31, "统计员"),
    NONE(64, "无"),
    PRIMARY_SCHOOL_SECONDARY(908, "小教二级"),
    PRIMARY_SCHOOL_SENIOR(906, "小教高级"),
    PRIMARY_SCHOOL_PRIMARY(907, "小教一级"),
    RESEARCHER(910, "研究员"),
    PHARMACIST(43, "药师"),
    PHARMACIST_2(45, "药士"),
    PHYSICIAN(5, "医师"),
    POLITICAL_WORKER(61, "政工师"),
    INTERMEDIATE_WORKER(21, "中级工"),
    INTERMEDIATE_SCHOOL_SECONDARY(909, "中教二级"),
    DEPUTY_CHIEF_NURSE_2(9, "主管护师"),
    DEPUTY_CHIEF_TECHNICIAN_2(53, "主管技师"),
    DEPUTY_CHIEF_LABORATORY_TECHNICIAN_2(57, "主管检验师"),

    DEPUTY_CHIEF_PHARMACIST_2(42, "主管药师"),
    CHIEF_NURSE(7, "主任护师"),
    TEMPORARY_DIRECTOR(901, "主任临时"),
    CHIEF_PHARMACIST(40, "主任药师"),
    CHIEF_PHYSICIAN(1, "主任医师"),
    ATTENDING_PHYSICIAN(3, "主治医师"),
    ASSISTANT(905, "助教"),
    ASSISTANT_ENGINEER(17, "助理工程师"),
    ASSISTANT_LIBRARIAN(35, "助理馆员"),
    ASSISTANT_ACCOUNTANT(27, "助理会计师"),
    ASSISTANT_EXPERIMENTALIST(14, "助理实验师"),
    ASSISTANT_STATISTICIAN(30, "助理统计师"),
    ASSISTANT_RESEARCHER(12, "助理研究员"),
    ASSISTANT_POLITICAL_WORKER(62, "助理政工师");

    private final int code;
    private final String name;

    YunDaLevelEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getTitleByCode(String levelCode) {
        for (YunDaLevelEnum value : YunDaLevelEnum.values()) {
            if (String.valueOf(value.code).equals(levelCode)) {
                return value.name;
            }
        }
        return null;
    }
}
