package backend.common.dao;

import tk.mybatis.mapper.common.IdsMapper;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.common.MySqlMapper;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.util.List;
import java.util.function.Consumer;


public interface MySqlCommonMapper<T> extends Mapper<T>, MySqlMapper<T>, IdsMapper<T> {
    default List<T> selectByExample2(Class<T> clazz, Consumer<WeekendSqls<T>> custom) {
        WeekendSqls<T> conditions = WeekendSqls.<T>custom();
        custom.accept(conditions);
        Example example = Example.builder(clazz).where(conditions).build();
        return selectByExample(example);
    }

    default List<T> selectByExample(Class<T> clazz, Consumer<Helper<T>> consumer) {
        Example example = buildExample(clazz, consumer);
        return selectByExample(example);
    }

    default int selectCountByExample2(Class<T> clazz, Consumer<WeekendSqls<T>> custom) {
        return selectCountByExample(clazz, helper -> helper.defGroup(custom));
    }

    default int selectCountByExample(Class<T> clazz, Consumer<Helper<T>> consumer) {
        Example example = buildExample(clazz, consumer);
        return selectCountByExample(example);
    }

    default Example buildExample(Class<T> clazz, Consumer<Helper<T>> consumer) {
        WeekendExampleBuilder<T> builder = new WeekendExampleBuilder<T>(clazz);
        WeekendSqls<T> conditions = WeekendSqls.<T>custom();
        builder.where(conditions);
        consumer.accept(new Helper<T>() {
            @Override
            public Helper<T> defGroup(Consumer<WeekendSqls<T>> consumer) {
                consumer.accept(conditions);
                return this;
            }

            @Override
            public Helper<T> newOrGroup(Consumer<WeekendSqls<T>> consumer) {
                WeekendSqls<T> conditions = WeekendSqls.<T>custom();
                builder.orWhere(conditions);
                consumer.accept(conditions);
                return this;
            }

            @Override
            public Helper<T> newAndGroup(Consumer<WeekendSqls<T>> consumer) {
                WeekendSqls<T> conditions = WeekendSqls.<T>custom();
                builder.andWhere(conditions);
                consumer.accept(conditions);
                return this;
            }

            @Override
            public Helper<T> builder(Consumer<WeekendExampleBuilder<T>> consumer) {
                consumer.accept(builder);
                return this;
            }
        });
        return builder.build();
    }

    default T selectOneByExample2(Class<T> clazz, Consumer<WeekendSqls<T>> custom) {
        WeekendSqls<T> conditions = WeekendSqls.<T>custom();
        custom.accept(conditions);
        Example example = Example.builder(clazz).where(conditions).build();
        return selectOneByExample(example);
    }

    default T selectOneByExample(Class<T> clazz, Consumer<Helper<T>> custom) {
        Example example = buildExample(clazz, custom);
        return selectOneByExample(example);
    }

    default int deleteByExample2(Class<T> clazz, Consumer<WeekendSqls<T>> custom) {
        WeekendSqls<T> conditions = WeekendSqls.<T>custom();
        custom.accept(conditions);
        Example example = Example.builder(clazz).where(conditions).build();
        return deleteByExample(example);
    }

    default int updateByExample2(Class<T> clazz, T data, Consumer<WeekendSqls<T>> custom) {
        WeekendSqls<T> conditions = WeekendSqls.<T>custom();
        custom.accept(conditions);
        Example example = Example.builder(clazz).where(conditions).build();
        return this.updateByExample(data, example);
    }

    default int updateByExampleSelective2(Class<T> clazz, T data, Consumer<WeekendSqls<T>> custom) {
        WeekendSqls<T> conditions = WeekendSqls.<T>custom();
        custom.accept(conditions);
        Example example = Example.builder(clazz).where(conditions).build();
        return this.updateByExampleSelective(data, example);
    }

    interface Helper<T> {
        Helper<T> defGroup(Consumer<WeekendSqls<T>> consumer);

        Helper<T> newOrGroup(Consumer<WeekendSqls<T>> consumer);

        Helper<T> newAndGroup(Consumer<WeekendSqls<T>> consumer);

        Helper<T> builder(Consumer<WeekendExampleBuilder<T>> consumer);
    }
}
