package backend.common.densensitized;

import backend.common.densensitized.annotation.PrivacyEncrypt;
import backend.common.densensitized.enums.PrivacyType;
import backend.common.densensitized.service.DelegatingDesensitizedPermissionService;
import backend.common.densensitized.utils.DesensitizedUtils;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Objects;
import org.springframework.context.ApplicationContext;
@Component
public class PrivacySerialize extends JsonSerializer<String> implements ContextualSerializer {
    private PrivacyType type;

    private Integer prefixNoMaskLen;

    private Integer suffixNoMaskLen;

    private String maskStr;

    private String description;

    private String permissionStr;

    private DelegatingDesensitizedPermissionService desensitizedPermissionService;

    @Autowired
    public PrivacySerialize(ApplicationContext applicationContext) {
     this.desensitizedPermissionService =   applicationContext.getBean(DelegatingDesensitizedPermissionService.class);
    }

    public PrivacySerialize(PrivacyType type, Integer prefixNoMaskLen, Integer suffixNoMaskLen, String maskStr) {
        this.type = type;
        this.prefixNoMaskLen = prefixNoMaskLen;
        this.suffixNoMaskLen = suffixNoMaskLen;
        this.maskStr = maskStr;
    }

    public PrivacySerialize setting(PrivacyType type, Integer prefixNoMaskLen, Integer suffixNoMaskLen, String maskStr, String permissionStr, String description) {
        this.type = type;
        this.prefixNoMaskLen = prefixNoMaskLen;
        this.suffixNoMaskLen = suffixNoMaskLen;
        this.maskStr = maskStr;
        this.permissionStr = permissionStr;
        this.description = description;
        return this;
    }

    @Override
    public void serialize(String origin, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
//        if (StringUtils.hasText(origin) && null != type && desensitizedPermissionService.evaluate(permissionStr, description)) {
            switch (type) {
                case CHINESE_NAME:
                    jsonGenerator.writeString(DesensitizedUtils.chineseName(origin));
                    break;
                case ID_CARD:
                    jsonGenerator.writeString(DesensitizedUtils.idCardNum(origin));
                    break;
                case FIXED_PHONE:
                    jsonGenerator.writeString(DesensitizedUtils.fixedPhone(origin));
                    break;
                case MOBILE_PHONE:
                    jsonGenerator.writeString(DesensitizedUtils.mobilePhone(origin));
                    break;
                case ADDRESS:
                    jsonGenerator.writeString(DesensitizedUtils.address(origin));
                    break;
                case EMAIL:
                    jsonGenerator.writeString(DesensitizedUtils.email(origin));
                    break;
                case BANK_CARD:
                    jsonGenerator.writeString(DesensitizedUtils.bankCard(origin));
                    break;
                case PASSWORD:
                    jsonGenerator.writeString(DesensitizedUtils.password(origin));
                    break;
                case KEY:
                    jsonGenerator.writeString(DesensitizedUtils.key(origin));
                    break;
                case CUSTOME:
                    jsonGenerator.writeString(DesensitizedUtils.desValue(origin, prefixNoMaskLen, suffixNoMaskLen, maskStr));
                    break;
                default:
                    throw new IllegalArgumentException("Unknow sensitive type enum " + type);
            }


    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider serializerProvider, BeanProperty beanProperty) throws JsonMappingException {
        if (beanProperty != null) {
            if (Objects.equals(beanProperty.getType().getRawClass(), String.class)) {
                PrivacyEncrypt encrypt = beanProperty.getAnnotation(PrivacyEncrypt.class);
                if (encrypt == null) {
                    encrypt = beanProperty.getContextAnnotation(PrivacyEncrypt.class);
                }
                if (encrypt != null) {
                    return setting(encrypt.type(), encrypt.prefixNoMaskLen(),
                            encrypt.suffixNoMaskLen(), encrypt.maskStr(), encrypt.permission(), encrypt.description());
                }
            }
            return serializerProvider.findValueSerializer(beanProperty.getType(), beanProperty);
        }
        return serializerProvider.findNullValueSerializer(null);
    }
}
