package backend.common.cdc;

import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.Data;

import javax.annotation.Nullable;

@Data
public class CDCMessage {
    private final Long id;
    private final EventType op;
    private final String before;
    private final String after;

    @Nullable
    public <T> T beforeTo(Class<T> clazz) throws JsonProcessingException {
        if (before != null) {
            return CDCUtils.objectMapper.readValue(before, clazz);
        }
        return null;
    }

    @Nullable
    public <T> T afterTo(Class<T> clazz) throws JsonProcessingException {
        if (after != null) {
            return CDCUtils.objectMapper.readValue(after, clazz);
        }
        return null;
    }
}