package backend.common.entity.dto.hisgateway.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum CardType {

	DIGIT_CARD("电子健康卡", 1), RESIDENTS_CARD("居民健康卡", 2), SOCIAL_CARD("社保卡", 3), MEDICARE_CARD("医保卡", 4),
	HOSPITAL_CARD("医院就诊卡", 5), UNIFY_CARD("统一就诊卡(一卡通)", 6), SOLDIER_CARD("市管干部/在编军人", 7), TEMP_CARD("临时卡", 8),
	OTHER_CARD("其他", 9);

	private final String value;

	private final Integer code;

	CardType(String value, Integer code) {
		this.value = value;
		this.code = code;
	}

	@JsonCreator
	public static CardType getFromCode(int code) {
		for (CardType t : CardType.values()) {
			if (t.getCode().equals(code)) {
				return t;
			}
		}
		throw new IllegalArgumentException("卡类型不存在");
	}

	public static CardType getFromValue(String value) {
		for (CardType t : CardType.values()) {
			if (t.getValue().equals(value)) {
				return t;
			}
		}
		throw new IllegalArgumentException("卡类型不存在");
	}

	public String getValue() {
		return value;
	}

	public Integer getCode() {
		return code;
	}

	@JsonValue
	public String getRequestCode() {
		return code.toString();
	}

}
