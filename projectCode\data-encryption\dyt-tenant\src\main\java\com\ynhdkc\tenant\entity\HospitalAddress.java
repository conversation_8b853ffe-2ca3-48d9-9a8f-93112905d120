package com.ynhdkc.tenant.entity;

import backend.common.domain.user.AddressEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @since 2023/2/9 11:51:39
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name = "t_hospital_address")
public final class HospitalAddress extends AddressEntity {

    private Long hospitalId;

    private String hospitalCode;
}
