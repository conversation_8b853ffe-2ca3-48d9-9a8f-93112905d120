package com.ynhdkc.tenant.service.customer.impl;

import backend.common.domain.tenant.constant.AppointmentSystemDepends;
import backend.common.enums.HospitalCode;
import backend.common.exception.BizException;
import backend.common.util.MessageUtil;
import backend.common.util.ObjectsUtils;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.ynhdkc.tenant.client.AppointmentOrderClient;
import com.ynhdkc.tenant.client.model.ApiOrderAppointment;
import com.ynhdkc.tenant.client.model.DepartmentList4MultiLevelResponse;
import com.ynhdkc.tenant.client.model.RecentApiOrderAppointmentRespDto;
import com.ynhdkc.tenant.component.doctors.KunmingMU1stDoctorListHandler;
import com.ynhdkc.tenant.dao.*;
import com.ynhdkc.tenant.doctor.sort.context.DoctorSortContext;
import com.ynhdkc.tenant.dto.DoctorAdvanceAndForbiddenDay;
import com.ynhdkc.tenant.dto.ScheduleResponseDto;
import com.ynhdkc.tenant.entity.*;
import com.ynhdkc.tenant.entity.constant.TimePeriodDisplayType;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.IHisRequestService;
import com.ynhdkc.tenant.service.customer.CustomerDepartmentService;
import com.ynhdkc.tenant.service.customer.IHisGatewayService;
import com.ynhdkc.tenant.tool.CustomerDepartmentComparator;
import com.ynhdkc.tenant.tool.DoctorListTool;
import com.ynhdkc.tenant.tool.SetDictInfoHelper;
import com.ynhdkc.tenant.tool.convert.DepartmentConverter;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import com.ynhdkc.tenant.util.ChineseToEnglishUtil;
import com.ynhdkc.tenant.util.DoctorUtils;
import com.ynhdkc.tenant.util.HospitalUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.http.HttpStatus;
import org.springframework.integration.support.locks.ExpirableLockRegistry;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ynhdkc.tenant.tool.convert.DoctorConvert.toDoctorAllScheduleInfoVo;
import static com.ynhdkc.tenant.tool.convert.DoctorConvert.toGroupedDoctorDetailVo;
import static com.ynhdkc.tenant.util.DateUtil.*;
import static java.util.Comparator.comparing;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/7/21 9:45
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerDepartmentServiceImpl implements CustomerDepartmentService {
    private static final String TODAY = "今天";
    private static final String TOMORROW = "明天";
    // 安宁市第一人民医院编码
    private static final String ANNING_FIRST_HOSPITAL_CODE = "871040";
    // 特色门诊科室类型枚举值
    private static final Integer SPECIAL_DEPARTMENT_CATEGORY = 6;
    // 特需科室编码
    private static final String SPECIAL_DEPARTMENT_CODE = "7025";
    private static final String YUNNAN_SECOND_PEOPLE_HOSPITAL_CODE = "871045";
    private static final List<String> noCheckDepartCodeList4filterScheduleDoctor = Lists.newArrayList(
            HospitalCode.FU_WAI_YUNNAN_HOSPITAL.getCode(),
            HospitalCode.YUNNAN_THIRD_PEOPLE_HOSPITAL_V2.getCode(),
            HospitalCode.YILIANG_TCM_HOSPITAL.getCode(),
            HospitalCode.WU_DING_PEOPLE_HOSPITAL.getCode(),
            HospitalCode.DISTRICT_PANLONG_MATERNAL_CHILDREN_HOSPITAL.getCode()
    );
    //红会医院 眼科门诊 科室编码
    private final String YUNNAN_SECOND_PEOPLE_HOSPITAL_EYE_DEPARTMENT_CODE = "25";
    private final String YUNNAN_SECOND_PEOPLE_HOSPITAL_TE_XU_CODE = "348";
    private final String YUNNAN_SECOND_PEOPLE_HOSPITAL_EYE_DEPARTMENT_NAME = "眼科门诊";
    private final DepartmentQuery departmentQuery;
    private final DoctorQuery doctorQuery;
    private final HospitalAreaSettingQuery hospitalAreaSettingQuery;
    private final SetDictInfoHelper setDictInfoHelper;
    private final IHisRequestService hisRequestService;
    private final AppointmentOrderClient appointmentOrderClient;
    private final PageVoConvert pageVoConvert;
    private final DoctorListTool doctorListTool;
    private final IHisGatewayService hisGatewayService;
    private final HospitalAreaQuery hospitalAreaQuery;
    private final HospitalAreaPositionQuery hospitalAreaPositionQuery;
    private final KunmingMU1stDoctorListHandler kunmingMU1StDoctorListHandler;
    private final DoctorSortContext doctorSortContext;
    private final ExpirableLockRegistry expirableLockRegistry;

    private static boolean isRequestHis(Optional<AppointmentRuleSetting> ruleSetting) {
        boolean requestHis = false;
        if (!ruleSetting.isPresent()) {
            return false;
        }

        AppointmentRuleSetting rule = ruleSetting.get();
        if (rule.getSystemDepends().equals(AppointmentSystemDepends.HIS.getCode()) && rule.getUpdateDepartmentDependHis()) {
            requestHis = true;
        }
        return requestHis;
    }

    private static void buildChildrenTree(CustomerDepartmentTreeVo root, Map<Long, List<Department>> departmentParentMap) {
        List<Department> children = departmentParentMap.get(root.getId());
        if (CollectionUtils.isEmpty(children)) {
            return;
        }
        List<CustomerDepartmentTreeVo> childrenVos = children.stream().map(DepartmentConverter::toCustomerDepartmentTreeVo).collect(Collectors.toList());
        root.setChildrens(childrenVos);

        root.getChildrens().forEach(child -> buildChildrenTree(child, departmentParentMap));
    }

    private static void sortMultiLevelDepartment(Comparator<CustomerDepartmentTreeVo> departmentTreeVoComparator, List<CustomerDepartmentTreeVo> vos) {
        vos.sort(departmentTreeVoComparator);
        for (CustomerDepartmentTreeVo vo : vos) {
            if (!CollectionUtils.isEmpty(vo.getChildrens())) {
                sortMultiLevelDepartment(departmentTreeVoComparator, vo.getChildrens());
            }
        }
    }

    @Override
    public CustomerDoctorScheduleInfoVo queryGroupedDoctorListByDepartmentId(Long departmentId) {
        Department department = getDepartment(departmentId);
        if (HospitalUtils.isKunmingMU1stHospital(department.getHospitalCode())) {
            return kunmingMU1StDoctorListHandler.queryGroupedDoctorListByDepartmentId(department);
        }
        Optional<AppointmentRuleSetting> ruleSettingOptional = hospitalAreaSettingQuery.queryAppointmentRuleSettingBy(department.getHospitalAreaId());
        if (!ruleSettingOptional.isPresent()) {
            throw new BizException(HttpStatus.NOT_FOUND, "预约规则不存在");
        }

        AppointmentRuleSetting appointmentRuleSetting = ruleSettingOptional.get();

        List<ScheduleResponseDto.ScheduleInfo> scheduleInfos;
        List<Doctor> doctors;
//        String key = CacheConfig.CREATE_DOCTOR.concat(String.valueOf(department.getId()));
//        Lock lock = expirableLockRegistry.obtain(key);
//        lock.lock();
//        try {
        doctors = getAllDoctors(department);
        scheduleInfos = hisGatewayService.getDepartmentDoctorScheduleList(department, doctors, appointmentRuleSetting);
//        }finally {
//            lock.unlock();
//        }
        //红会眼科亚科室只展示系统中已经配置的医生排班，不以his的为依据
        if (!CollectionUtils.isEmpty(scheduleInfos) && (department.getHospitalCode().equals(YUNNAN_SECOND_PEOPLE_HOSPITAL_CODE)
                && department.getThrdpartDepCode().equals(YUNNAN_SECOND_PEOPLE_HOSPITAL_EYE_DEPARTMENT_CODE))) {
            if (!department.getName().equals(YUNNAN_SECOND_PEOPLE_HOSPITAL_EYE_DEPARTMENT_NAME)) {
                //只展示配置的医生
                List<Doctor> finalDoctors = doctors;
                scheduleInfos.removeIf(scheduleInfo -> !finalDoctors.stream().map(Doctor::getThrdpartDoctorCode).collect(Collectors.toList()).contains(scheduleInfo.getDoctorCode()));
            }

        }
        doctors = getEnabledDoctors(department);
        if (CollectionUtils.isEmpty(doctors)) {
            log.info("doctors is empty, request his, hospital code {}, department code {},department info {}", department.getHospitalCode(), department.getThrdpartDepCode(), department);
            return new CustomerDoctorScheduleInfoVo();
        }


        // 屎山代码继续堆积
        if (!CollectionUtils.isEmpty(scheduleInfos) && (department.getHospitalCode().equals(YUNNAN_SECOND_PEOPLE_HOSPITAL_CODE)
                && department.getThrdpartDepCode().equals(YUNNAN_SECOND_PEOPLE_HOSPITAL_TE_XU_CODE))) {
            //只展示配置的医生
            List<Doctor> finalDoctors = doctors;
            scheduleInfos.removeIf(scheduleInfo -> !finalDoctors.stream().map(Doctor::getThrdpartDoctorCode).collect(Collectors.toList()).contains(scheduleInfo.getDoctorCode()));
        }


        Integer displayDoctorUnderDepartment = ruleSettingOptional.map(AppointmentRuleSetting::getDisplayDoctorUnderDepartment).orElse(0);

        List<DoctorGroupRelation> doctorGroupRelations = getDoctorGroupRelations(doctors.stream().map(Doctor::getId).collect(Collectors.toList()));

        CustomerDoctorScheduleInfoVo vo = new CustomerDoctorScheduleInfoVo();
        Set<LocalDate> scheduleDates = getScheduleDate(doctors, department, appointmentRuleSetting);

        doctorSortContext.sortDoctors(doctors, department.getHospitalCode());

        List<CustomerDoctorScheduleGroupedByDateVo> customerDoctorScheduleGroupedByDateVos = compositeScheduleDoctorInGroup(scheduleDates, doctors, scheduleInfos, department, doctorGroupRelations, appointmentRuleSetting);
        vo.setGroupedDoctors(customerDoctorScheduleGroupedByDateVos);

        List<CustomerAllScheduleDoctorDetailVo> customerAllScheduleDoctorDetailVos = compositeAllDoctor(doctors, scheduleInfos, department, doctorGroupRelations, displayDoctorUnderDepartment);

        if (HospitalUtils.isKunmingMU1stHospital(department.getHospitalCode())) {
            removedDoctorCodeDuplicateAndNoScheduleDoctor(customerAllScheduleDoctorDetailVos);
        }
        if (!ObjectsUtils.isEmpty(customerAllScheduleDoctorDetailVos)) {
            customerAllScheduleDoctorDetailVos.forEach(y -> y.setRequestDepartmentId(department.getId()));
        }
        vo.setAllDoctors(customerAllScheduleDoctorDetailVos);

        vo.setSystemDepends(ruleSettingOptional.map(AppointmentRuleSetting::getSystemDepends).orElse(null));
        DoctorUtils.setDoctorTitle(ruleSettingOptional.get(), vo.getAllDoctors());
        vo.getGroupedDoctors().forEach(v -> {
            DoctorUtils.setDoctorTitle(ruleSettingOptional.get(), v.getDoctors());
        });
        return vo;
    }


    @Override
    public CustomerDoctorScheduleInfoVo queryGroupedDoctorList(Long departmentId, Integer timeType) {
        Department department = getDepartment(departmentId);
        if (HospitalUtils.isTongRenHospital(department.getHospitalCode()) && timeType != null) {
            return queryTongRenGroupedDoctorListByDepartmentIdAndTimeType(departmentId, timeType);
        }
        return queryGroupedDoctorListByDepartmentId(departmentId);
    }

    private CustomerDoctorScheduleInfoVo queryTongRenGroupedDoctorListByDepartmentIdAndTimeType(Long departmentId, Integer timeType) {
        Department department = getDepartment(departmentId);
        Optional<AppointmentRuleSetting> ruleSettingOptional = hospitalAreaSettingQuery.queryAppointmentRuleSettingBy(department.getHospitalAreaId());
        if (!ruleSettingOptional.isPresent()) {
            throw new BizException(HttpStatus.NOT_FOUND, "预约规则不存在");
        }

        AppointmentRuleSetting appointmentRuleSetting = ruleSettingOptional.get();
        List<Doctor> doctors = getAllDoctors(department);
        List<ScheduleResponseDto.ScheduleInfo> scheduleInfos = hisGatewayService.getDepartmentDoctorTimeTypeScheduleList(department, doctors, appointmentRuleSetting, timeType);
        doctors = getEnabledDoctors(department);
        if (CollectionUtils.isEmpty(doctors)) {
            log.info("doctors is empty, request his, hospital code {}, department code {},department info {}", department.getHospitalCode(), department.getThrdpartDepCode(), department);
            return new CustomerDoctorScheduleInfoVo();
        }

        Integer displayDoctorUnderDepartment = ruleSettingOptional.map(AppointmentRuleSetting::getDisplayDoctorUnderDepartment).orElse(0);

        List<DoctorGroupRelation> doctorGroupRelations = getDoctorGroupRelations(doctors.stream().map(Doctor::getId).collect(Collectors.toList()));

        CustomerDoctorScheduleInfoVo vo = new CustomerDoctorScheduleInfoVo();
        Set<LocalDate> scheduleDates = getScheduleDate(doctors, department, appointmentRuleSetting);

        doctorSortContext.sortDoctors(doctors, department.getHospitalCode());

        List<CustomerDoctorScheduleGroupedByDateVo> customerDoctorScheduleGroupedByDateVos = compositeScheduleDoctorInGroup(scheduleDates, doctors, scheduleInfos, department, doctorGroupRelations, appointmentRuleSetting);
        vo.setGroupedDoctors(customerDoctorScheduleGroupedByDateVos);

        List<CustomerAllScheduleDoctorDetailVo> customerAllScheduleDoctorDetailVos = compositeAllDoctor(doctors, scheduleInfos, department, doctorGroupRelations, displayDoctorUnderDepartment);

        vo.setAllDoctors(customerAllScheduleDoctorDetailVos.stream().filter(v -> !v.getDateInWeeks().isEmpty()).collect(Collectors.toList()));

        vo.setSystemDepends(ruleSettingOptional.map(AppointmentRuleSetting::getSystemDepends).orElse(null));
        DoctorUtils.setDoctorTitle(ruleSettingOptional.get(), vo.getAllDoctors());
        vo.getGroupedDoctors().forEach(v -> {
            DoctorUtils.setDoctorTitle(ruleSettingOptional.get(), v.getDoctors());
        });
        return vo;
    }

    public CustomerDoctorScheduleInfoVo queryGroupedDoctorListByHospitalAreaId(Long hospitalAreaId) {
        Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaById(hospitalAreaId);
        if (HospitalUtils.isKunmingMU1stHospital(hospitalArea.getHospitalCode())) {
            return kunmingMU1StDoctorListHandler.queryGroupedDoctorListByHospitalAreaId(hospitalArea);
        }

        Department department = getYnhDaDepartment(hospitalAreaId);
        Optional<AppointmentRuleSetting> ruleSettingOptional = hospitalAreaSettingQuery.queryAppointmentRuleSettingBy(hospitalAreaId);
        if (!ruleSettingOptional.isPresent()) {
            throw new BizException(HttpStatus.NOT_FOUND, "预约规则不存在");
        }

        AppointmentRuleSetting appointmentRuleSetting = ruleSettingOptional.get();
        List<Doctor> doctors = doctorQuery.queryDoctorsByHospitalAreaIdAndDepartmentCode(hospitalAreaId, SPECIAL_DEPARTMENT_CODE);

        List<ScheduleResponseDto.ScheduleInfo> scheduleInfos = hisGatewayService.getDepartmentDoctorScheduleList(department, doctors, appointmentRuleSetting);
        if (CollectionUtils.isEmpty(doctors)) {
            log.info("doctors is empty, request his, hospital code {}, department code {},department info {}", SPECIAL_DEPARTMENT_CODE, department.getThrdpartDepCode(), department);
            return new CustomerDoctorScheduleInfoVo();
        }

        Integer displayDoctorUnderDepartment = ruleSettingOptional.map(AppointmentRuleSetting::getDisplayDoctorUnderDepartment).orElse(0);
        List<DoctorGroupRelation> doctorGroupRelations = getDoctorGroupRelations(doctors.stream().map(Doctor::getId).collect(Collectors.toList()));
        CustomerDoctorScheduleInfoVo vo = new CustomerDoctorScheduleInfoVo();
        Set<LocalDate> scheduleDates = getScheduleDate(doctors, department, appointmentRuleSetting);
        List<CustomerDoctorScheduleGroupedByDateVo> customerDoctorScheduleGroupedByDateVos = compositeScheduleDoctorInGroup(scheduleDates, doctors, scheduleInfos, department, doctorGroupRelations, appointmentRuleSetting);
        vo.setGroupedDoctors(customerDoctorScheduleGroupedByDateVos);
        doctorSortContext.sortDoctors(doctors, SPECIAL_DEPARTMENT_CODE);

        List<CustomerAllScheduleDoctorDetailVo> customerAllScheduleDoctorDetailVos;
        if (HospitalUtils.isKunmingMU1stHospital(hospitalArea.getHospitalCode())) {
            customerAllScheduleDoctorDetailVos = kunmingMU1StDoctorListHandler.compositeAllDoctor(doctors, scheduleInfos, department, doctorGroupRelations, displayDoctorUnderDepartment);
        } else {
            customerAllScheduleDoctorDetailVos = compositeAllDoctor(doctors, scheduleInfos, department, doctorGroupRelations, displayDoctorUnderDepartment);
        }

        if (HospitalUtils.isKunmingMU1stHospital(SPECIAL_DEPARTMENT_CODE)) {
            removedDoctorCodeDuplicateAndNoScheduleDoctor(customerAllScheduleDoctorDetailVos);
        }
        vo.setAllDoctors(customerAllScheduleDoctorDetailVos);

        return vo;
    }


    public Department getYnhDaDepartment(Long hospitalAreaId) {
        Set<String> departmentCodes = new HashSet<>();
        departmentCodes.add(SPECIAL_DEPARTMENT_CODE);
        List<Department> departmentList = departmentQuery.queryDepartments(hospitalAreaId, departmentCodes);
        Department department = departmentList.stream().filter(d -> d.getThrdpartDepCode().equals(SPECIAL_DEPARTMENT_CODE)).findFirst().orElse(null);
        if (null == department) {
            throw new BizException(HttpStatus.NOT_FOUND, "医院特需门诊科室不存在");
        }
        department.setDoctors(departmentList.stream().map(Department::getDoctors).collect(Collectors.joining("|")));
        department.setId(Long.parseLong(SPECIAL_DEPARTMENT_CODE));
        return department;
    }

    private List<Doctor> getAllDoctors(Department department) {
        return doctorQuery.queryBy(department.getId());
    }

    private List<Doctor> getEnabledDoctors(Department department) {
        List<Doctor> doctorList = getAllDoctors(department);
        return doctorList.stream().filter(q -> q.getStatus() == 0).collect(Collectors.toList());
    }

    private void removedDoctorCodeDuplicateAndNoScheduleDoctor(List<CustomerAllScheduleDoctorDetailVo> customerAllScheduleDoctorDetailVos) {
        List<CustomerAllScheduleDoctorDetailVo> temp = new ArrayList<>();
        customerAllScheduleDoctorDetailVos.forEach(q -> {
            if (!CollectionUtils.isEmpty(q.getDateInWeeks())) {
                temp.add(q);
            }
        });
        customerAllScheduleDoctorDetailVos.clear();
        customerAllScheduleDoctorDetailVos.addAll(temp);
    }


    private List<CustomerAllScheduleDoctorDetailVo> compositeAllDoctor(List<Doctor> doctors, List<ScheduleResponseDto.ScheduleInfo> scheduleInfos, Department department, List<DoctorGroupRelation> doctorGroupRelations, Integer displayDoctorUnderDepartment) {
        return doctors.stream().parallel().map(doctor -> {
            CustomerAllScheduleDoctorDetailVo vo = toDoctorAllScheduleInfoVo(doctor);
            setDictInfoHelper.setDictInfo(doctor, vo);

            if (enrichCustomerAllScheduleDoctorDetailVo(scheduleInfos, department.getName(), doctorGroupRelations, displayDoctorUnderDepartment, vo)) {
                return null;
            }
            return vo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public boolean enrichCustomerAllScheduleDoctorDetailVo(List<ScheduleResponseDto.ScheduleInfo> scheduleInfos, String departmentName, List<DoctorGroupRelation> doctorGroupRelations, Integer displayDoctorUnderDepartment, CustomerAllScheduleDoctorDetailVo vo) {
        if (displayDoctorUnderDepartment == 1) {
            List<ScheduleResponseDto.ScheduleInfo> scheduleInfoList = scheduleInfos.stream().filter(scheduleInfo -> scheduleInfo.getDoctorCode().equals(vo.getThrdpartDoctorCode())).collect(Collectors.toList());

            log.info("enrichCustomerAllScheduleDoctorDetailVo: {}", MessageUtil.object2JSONString(scheduleInfoList));

            if (CollectionUtils.isEmpty(scheduleInfoList)) {
                return true;
            }
        }
        List<ScheduleResponseDto.ScheduleInfo> doctorScheduleInfos = scheduleInfos.stream().filter(scheduleInfo -> filterScheduleDoctor(scheduleInfo, vo)).collect(Collectors.toList());

        vo.setDateInWeeks(doctorScheduleInfos.stream().map(this::scheduleInfoToDateInWeek).collect(Collectors.toSet()).stream().sorted(comparing(CustomerDateInWeekDto::getLocalDate)).collect(Collectors.toList()));
        vo.setShowSchDate(doctorScheduleInfos.stream().anyMatch(scheduleInfo -> scheduleInfo.getShowSchDate() == 1));
        vo.setCanOrder(vo.getDateInWeeks().stream().anyMatch(CustomerDateInWeekDto::isCanOrder));

        Map<String, String> departmentCodeAndDepartmentName = scheduleInfos.stream().filter(q -> q.getDepartmentCode() != null && q.getDepartmentName() != null).collect(Collectors.toMap(ScheduleResponseDto.ScheduleInfo::getDepartmentCode, ScheduleResponseDto.ScheduleInfo::getDepartmentName, (k1, k2) -> k1));
        vo.setDepartmentName(getDepartmentName(departmentCodeAndDepartmentName, vo.getDepartmentCode(), departmentName));
        vo.setBelongDoctorGroups(doctorGroupRelations.stream().anyMatch(q -> q.getDoctorId().equals(vo.getId())));
        return false;
    }

    private String getDepartmentName(Map<String, String> departmentNameMap, String departmentCode, String departmentNameFromDepartment) {
        if (ObjectsUtils.isEmpty(departmentNameMap)) {
            return departmentNameFromDepartment;
        }

        String departmentName = departmentNameMap.get(departmentCode);
        if (departmentName == null) {
            return departmentNameFromDepartment;
        }
        return departmentName;
    }

    private boolean filterScheduleDoctor(ScheduleResponseDto.ScheduleInfo scheduleInfo, CustomerAllScheduleDoctorDetailVo doctor) {
        if (ObjectsUtils.isEmpty(scheduleInfo.getDepartmentCode())) {
            return scheduleInfo.getDoctorCode().equals(doctor.getThrdpartDoctorCode());
        }

        //部分医院的医生排班返回的科室编码与医生数据中的科室编码不一致
        if (noCheckDepartCodeList4filterScheduleDoctor.contains(doctor.getHospitalAreaCode())) {
            return scheduleInfo.getDoctorCode().equals(doctor.getThrdpartDoctorCode());
        }

        return scheduleInfo.getDepartmentCode().equals(doctor.getDepartmentCode()) && scheduleInfo.getDoctorCode().equals(doctor.getThrdpartDoctorCode());
    }


    private List<CustomerDoctorScheduleGroupedByDateVo> compositeScheduleDoctorInGroup(Set<LocalDate> scheduleDates, List<Doctor> doctors, List<ScheduleResponseDto.ScheduleInfo> scheduleInfos, Department department, List<DoctorGroupRelation> doctorGroupRelations, AppointmentRuleSetting ruleSetting) {
        List<ScheduleResponseDto.ScheduleInfo> scheduleInfoList = scheduleInfos.stream().filter(scheduleInfo -> scheduleDates.contains(convertDateToLocalDate(scheduleInfo.getSchDate()))).collect(Collectors.toList());

        Map<LocalDate, List<ScheduleResponseDto.ScheduleInfo>> doctorScheduleGroupedByDate = scheduleInfoList.stream().collect(Collectors.groupingBy(scheduleInfo -> convertDateToLocalDate(scheduleInfo.getSchDate())));
        List<CustomerDoctorScheduleGroupedByDateVo> groupedByDateVoList = doctorScheduleGroupedByDate.entrySet().stream()
                .map(dateScheduleEntry -> {
                    LocalDate date = dateScheduleEntry.getKey();
                    List<ScheduleResponseDto.ScheduleInfo> schedulesForDate = dateScheduleEntry.getValue();

                    if (date == null || CollectionUtils.isEmpty(schedulesForDate)) {
                        return null;
                    }

                    CustomerDoctorScheduleGroupedByDateVo doctorScheduleGroupedByDateVo = new CustomerDoctorScheduleGroupedByDateVo();
                    String dateInWeek = convertLocalDateToWeekday(date);
                    doctorScheduleGroupedByDateVo.setDateInWeek(dateInWeek);

                    String dateString = convertLocalDateToMMDD(date);
                    doctorScheduleGroupedByDateVo.setDoctorGroupId(dateString);
                    doctorScheduleGroupedByDateVo.setScheduleDate(dateString);

                    Map<String, List<ScheduleResponseDto.ScheduleInfo>> doctorScheduleGroupedByDoctorCode = schedulesForDate.stream().collect(Collectors.groupingBy(ScheduleResponseDto.ScheduleInfo::getDoctorCode));
                    List<CustomerGroupedDoctorDetailVo> doctorVos = doctorScheduleGroupedByDoctorCode.entrySet().stream().map(doctorScheduleEntry -> createCustomerGroupedDoctorDetailVo(doctorScheduleEntry, doctors, department, doctorGroupRelations)).filter(Objects::nonNull).collect(Collectors.toList());
                    doctorSortContext.sortGroupVo(doctorVos, department.getHospitalCode());
                    doctorScheduleGroupedByDateVo.setDoctors(doctorVos);
                    doctorScheduleGroupedByDateVo.setCanOrder(doctorVos.stream().anyMatch(CustomerGroupedDoctorDetailVo::isCanOrder));
                    doctorScheduleGroupedByDateVo.setScheduleLocalDate(convertLocalDateToDate(date));
                    return doctorScheduleGroupedByDateVo;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (ruleSetting.getTimePeriodDisplayType().equals(TimePeriodDisplayType.DISPLAY_ALL.getCode())) {
            scheduleDates.forEach(scheduleDate -> {
                List<CustomerDoctorScheduleGroupedByDateVo> temp = groupedByDateVoList.stream().filter(q -> q.getScheduleLocalDate().equals(convertLocalDateToDate(scheduleDate))).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(temp)) {
                    CustomerDoctorScheduleGroupedByDateVo emptyVo = new CustomerDoctorScheduleGroupedByDateVo();
                    emptyVo.setCanOrder(false);
                    emptyVo.setScheduleLocalDate(convertLocalDateToDate(scheduleDate));
                    emptyVo.setDateInWeek(convertLocalDateToWeekday(scheduleDate));
                    String dateString = convertLocalDateToMMDD(scheduleDate);
                    emptyVo.setDoctorGroupId(dateString);
                    emptyVo.setScheduleDate(dateString);
                    groupedByDateVoList.add(emptyVo);
                }
            });
        }

        groupedByDateVoList.sort(Comparator.comparing(CustomerDoctorScheduleGroupedByDateVo::getScheduleLocalDate));
        return groupedByDateVoList;
    }


    private Set<LocalDate> getScheduleDate(List<Doctor> doctors, Department department, AppointmentRuleSetting ruleSetting) {
        List<DoctorAdvanceAndForbiddenDay> doctorAdvanceAndForbiddenDays = getDoctorAdvanceAndForbiddenDays(doctors, department, ruleSetting);
        Pair<Integer, Integer> maxAdvanceAndMinForbiddenDays = getMaxAdvanceAndMinForbiddenDays(doctorAdvanceAndForbiddenDays);
        Integer maxAdvanceDay = maxAdvanceAndMinForbiddenDays.getLeft();
        Integer minForbiddenDay = maxAdvanceAndMinForbiddenDays.getRight();

        LocalDate now = LocalDate.now();
        LocalDate start = now.plusDays(minForbiddenDay);
        LocalDate end = now.plusDays(maxAdvanceDay);
        return splitLocalDateByDay(start, end);
    }


    private Pair<Integer, Integer> getMaxAdvanceAndMinForbiddenDays(List<DoctorAdvanceAndForbiddenDay> doctorAdvanceAndForbiddenDays) {
        Integer maxAdvanceDay = doctorAdvanceAndForbiddenDays.stream().map(DoctorAdvanceAndForbiddenDay::getAdvanceDay).max(Integer::compareTo).orElse(0);
        Integer minForbiddenDay = doctorAdvanceAndForbiddenDays.stream().map(DoctorAdvanceAndForbiddenDay::getForbiddenDay).min(Integer::compareTo).orElse(0);
        return Pair.of(maxAdvanceDay, minForbiddenDay);
    }

    @Override
    public List<DoctorAdvanceAndForbiddenDay> getDoctorAdvanceAndForbiddenDays(List<Doctor> doctors, Department department, AppointmentRuleSetting ruleSetting) {
        List<DoctorAdvanceAndForbiddenDay> doctorAdvanceAndForbiddenDays = new ArrayList<>();
        doctors.forEach(doctor -> {
            Pair<Integer, Integer> defaultAdvanceAndForbiddenDays = doctorListTool.defaultAdvanceAndForbiddenDays(doctor, department, ruleSetting);
            doctorAdvanceAndForbiddenDays.add(DoctorAdvanceAndForbiddenDay.builder()
                    .doctorId(doctor.getId())
                    .advanceDay(defaultAdvanceAndForbiddenDays.getLeft())
                    .forbiddenDay(defaultAdvanceAndForbiddenDays.getRight())
                    .build());
        });
        return doctorAdvanceAndForbiddenDays;
    }

    private Department getDepartment(Long departmentId) {
        Department department = departmentQuery.queryDepartmentById(departmentId);
        if (null == department) {
            throw new BizException(HttpStatus.NOT_FOUND, "科室不存在");
        }
        return department;
    }

    @Override
    public CustomerDepartmentsTreeSearchListVo queryTree(Long hospitalAreaId, Integer category, Integer level) {
        Optional<AppointmentRuleSetting> ruleSetting = hospitalAreaSettingQuery.queryAppointmentRuleSettingBy(hospitalAreaId);

        boolean requestHis = isRequestHis(ruleSetting);
        if (requestHis) {
            hisRequestService.requestDepartmentsFromHisBy(hospitalAreaId);
        }

        List<Department> departments = departmentQuery.pageQueryDepartment(new DepartmentQuery.DepartmentQueryOption(1, Integer.MAX_VALUE).setHospitalAreaId(hospitalAreaId).setCategory(null == category ? null : Collections.singletonList(category)));
        departments = departments.stream().filter(Department::getEnabled).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(departments)) {
            return new CustomerDepartmentsTreeSearchListVo()._list(Collections.emptyList()).levelCount(0);
        }

        List<Department> departmentsAndParents = new ArrayList<>();
        for (Department department : departments) {
            List<Department> temp = getDepartmentAndParents(department, departments);
            addDepartmentsNotContains(departmentsAndParents, temp);
        }
        if (CollectionUtils.isEmpty(departmentsAndParents)) {
            return new CustomerDepartmentsTreeSearchListVo()._list(Collections.emptyList()).levelCount(0);
        }
        List<CustomerDepartmentTreeVo> vos = getDepartmentsTreeFromDepartments(departmentsAndParents, 0L);
        CustomerDepartmentsTreeSearchListVo vo = new CustomerDepartmentsTreeSearchListVo();
        if (level != null) {
            List<CustomerDepartmentTreeVo> departmentsByLevel = getDepartmentsByLevel2(vos, level);
            vo.setLevelCount(1);
            vo.setList(departmentsByLevel);
        } else {
            vo.setList(vos);
            vo.setLevelCount(calculateMaxDepth(vos));
        }
        if (vo.getLevelCount() == 1 && (!ANNING_FIRST_HOSPITAL_CODE.equals(departments.get(0).getHospitalCode()) && Objects.equals(category, SPECIAL_DEPARTMENT_CATEGORY))) {
            vos.sort(new CustomerDepartmentComparator());
        } else {
            Integer departmentOrderType = ruleSetting.map(AppointmentRuleSetting::getDepartmentOrderType).orElse(0);// 默认按sort倒序
            Comparator<CustomerDepartmentTreeVo> departmentTreeVoComparator = comparing(CustomerDepartmentTreeVo::getSort).reversed();
            if (departmentOrderType == 1) {
                departmentTreeVoComparator = comparing(CustomerDepartmentTreeVo::getSort);
            }
            sortMultiLevelDepartment(departmentTreeVoComparator, vos);
        }
        ruleSetting.ifPresent(rule -> vo.setAllDepartmentBackgroundColor(rule.getAllDepartmentBackgroundColor()));
        ruleSetting.ifPresent(rule -> vo.setDisplayAddress(rule.getDisplayDepartmentAddress()));
        return vo;
    }


    @Override
    public Optional<Department> queryDepartmentByCode(String hospitalAreaCode, String departmentCode) {
        return departmentQuery.queryDepartmentByCode(hospitalAreaCode, departmentCode);
    }

    @Override
    public CustomerDepartmentsTreeSearchListVo getSubDepartments(Long departmentId) {
        try {
            Department department = getDepartment(departmentId);
            DepartmentList4MultiLevelResponse departmentList4MultiLevel = hisGatewayService.getDepartmentList4MultiLevel(department.getHospitalCode(), department.getThrdpartDepCode());
            if (departmentList4MultiLevel == null) {
                return new CustomerDepartmentsTreeSearchListVo()._list(Collections.emptyList()).levelCount(0);
            }

            Set<String> departmentCodes = new HashSet<>();
            for (DepartmentList4MultiLevelResponse.Result temp : departmentList4MultiLevel.getResult()) {
                departmentCodes.add(temp.getDepartmentCode());
                if (!CollectionUtils.isEmpty(temp.getChildren())) {
                    temp.getChildren().forEach(child -> departmentCodes.add(child.getDepartmentCode()));
                }
            }
            List<Department> departments = departmentQuery.queryDepartments(department.getHospitalAreaId(), departmentCodes);

            List<CustomerDepartmentTreeVo> vos = new ArrayList<>();
            departmentList4MultiLevel.getResult().forEach(temp -> {
                CustomerDepartmentTreeVo vo = new CustomerDepartmentTreeVo();
                vo.setName(temp.getDepartmentName());
                vo.setHospitalAreaId(department.getHospitalAreaId());
                vo.setHospitalAreaCode(temp.getHospitalCode());
                vo.setThrdpartDepCode(temp.getDepartmentCode());
                vo.setHospitalId(department.getHospitalId());
                vo.setAddressIntro(temp.getDepartmentAddress());
                vo.setParentDepartmentCode(temp.getParentDepartmentCode());
                Department parentDepartment = findDepartment(temp, departments);
                if (parentDepartment == null) {
                    parentDepartment = saveDepartment(temp, department);
                    vo.setSort(0);
                } else {
                    vo.setSort(parentDepartment.getSort() == null ? 0 : parentDepartment.getSort());
                }
                vo.setId(parentDepartment.getId());

                Department finalParentDepartment = parentDepartment;
                List<CustomerDepartmentTreeVo> children = temp.getChildren().stream().map(child -> {
                    CustomerDepartmentTreeVo childVo = new CustomerDepartmentTreeVo();
                    childVo.setName(child.getDepartmentName());
                    childVo.setHospitalAreaId(department.getHospitalAreaId());
                    childVo.setHospitalAreaCode(temp.getHospitalCode());
                    childVo.setThrdpartDepCode(child.getDepartmentCode());
                    childVo.setHospitalId(department.getHospitalId());
                    childVo.setAddressIntro(child.getDepartmentAddress());
                    childVo.setParentDepartmentCode(temp.getDepartmentCode());
                    Department childDepartment = findDepartment(child, departments);
                    if (childDepartment == null) {
                        childDepartment = saveDepartment(child, finalParentDepartment);
                        childVo.setSort(0);
                    } else {
                        childVo.setSort(childDepartment.getSort() == null ? 0 : childDepartment.getSort());
                    }

                    childVo.setId(childDepartment.getId());
                    return childVo;
                }).collect(Collectors.toList());
                vo.setChildrens(children);
                vos.add(vo);
            });

            CustomerDepartmentsTreeSearchListVo vo = new CustomerDepartmentsTreeSearchListVo();
            vo._list(vos).levelCount(2);

            AppointmentRuleSetting ruleSetting = hospitalAreaSettingQuery.queryAppointmentRuleSettingBy(department.getHospitalAreaId()).orElse(null);
            if (ruleSetting == null) {
                return vo;
            }

            if (ruleSetting.getDepartmentOrderType() == 1) {
                List<CustomerDepartmentTreeVo> allDepartmentVos = vo.getList().stream()
                        .peek(item -> {
                            if (item.getSort() == null) {
                                item.setSort(0);
                            }
                        })
                        .sorted(Comparator.comparing(CustomerDepartmentTreeVo::getSort)).collect(Collectors.toList());
                vo.setList(allDepartmentVos);

                allDepartmentVos.forEach(allDepartmentVo -> {
                    if (!CollectionUtils.isEmpty(allDepartmentVo.getChildrens())) {
                        allDepartmentVo.getChildrens().forEach(child -> {
                            if (child.getSort() == null) {
                                child.setSort(0);
                            }
                        });
                        allDepartmentVo.getChildrens().sort(Comparator.comparing(CustomerDepartmentTreeVo::getSort));
                    }
                });
            } else {
                List<CustomerDepartmentTreeVo> allDepartmentVos = vo.getList().stream()
                        .peek(item -> {
                            if (item.getSort() == null) {
                                item.setSort(0);
                            }
                        })
                        .sorted(Comparator.comparing(CustomerDepartmentTreeVo::getSort).reversed()).collect(Collectors.toList());
                vo.setList(allDepartmentVos);

                allDepartmentVos.forEach(allDepartmentVo -> {
                    if (!CollectionUtils.isEmpty(allDepartmentVo.getChildrens())) {
                        allDepartmentVo.getChildrens().forEach(child -> {
                            if (child.getSort() == null) {
                                child.setSort(0);
                            }
                        });
                        allDepartmentVo.getChildrens().sort(Comparator.comparing(CustomerDepartmentTreeVo::getSort).reversed());
                    }
                });
            }
            return vo;
        } catch (Exception e) {
            log.error("getSubDepartments error: ", e);
        }
        return null;
    }

    private Department findDepartment(DepartmentList4MultiLevelResponse.Result temp, List<Department> departments) {
        return departments.stream().filter(department -> department.getThrdpartDepCode().equals(temp.getDepartmentCode())).findFirst().orElse(null);
    }

    private Department saveDepartment(DepartmentList4MultiLevelResponse.Result temp, Department parentDepartment) {
        Department newDepartment = new Department();
        newDepartment.setTenantId(parentDepartment.getTenantId());
        newDepartment.setHospitalId(parentDepartment.getHospitalId());
        newDepartment.setHospitalAreaId(parentDepartment.getHospitalAreaId());
        newDepartment.setHospitalCode(parentDepartment.getHospitalCode());
        newDepartment.setThrdpartDepCode(temp.getDepartmentCode());
        newDepartment.setName(temp.getDepartmentName());
        newDepartment.setFirstLetter(ChineseToEnglishUtil.getFirstLetter(temp.getDepartmentName()));
        newDepartment.setSort(temp.getIntSortValue());
        newDepartment.setParentId(parentDepartment.getId());
        newDepartment.setSystemDepends(AppointmentSystemDepends.HIS.getCode());
        departmentQuery.save(newDepartment);
        return newDepartment;
    }

    @Override
    public CustomerDepartmentDetailVo getDetail(Long departmentId) {
        Department department = getDepartment(departmentId);
        hospitalAreaPositionQuery.queryByDepartmentId(departmentId);
        return DepartmentConverter.toCustomerDepartmentDetailVo(department);
    }

    @Override
    public CustomerDepartmentDetailVo getDetailByCode(String hospitalAreaCode, String departmentCode) {
        Optional<Department> department = queryDepartmentByCode(hospitalAreaCode, departmentCode);
        if (department.isPresent()) {
            return DepartmentConverter.toCustomerDepartmentDetailVo(department.get());

        }
        return null;
    }

    private String getDepartmentPosition(HospitalAreaPosition hospitalAreaPosition) {
        if (null == hospitalAreaPosition) {
            return "";
        }
        StringBuilder position = new StringBuilder();
        if (StringUtils.hasText(hospitalAreaPosition.getBuildingName())) {
            position.append(hospitalAreaPosition.getBuildingName()).append("/");
        }
        if (StringUtils.hasText(hospitalAreaPosition.getFloorName())) {
            position.append(hospitalAreaPosition.getFloorName()).append("/");
        }
        if (StringUtils.hasText(hospitalAreaPosition.getName())) {
            position.append(hospitalAreaPosition.getName()).append("/");
        }
        if (StringUtils.hasText(hospitalAreaPosition.getTriageDeskAddress())) {
            position.append(hospitalAreaPosition.getTriageDeskAddress());
        }
        return position.toString();
    }

    @Override
    public List<CustomerDepartmentVo> getRecentSuccess(Long userId, Long hospitalAreaId) {
        List<Long> lastDepartmentId = Collections.emptyList();
        try {
            Hospital hospital = hospitalAreaQuery.queryHospitalAreaById(hospitalAreaId);
            if (null == hospital) {
                return Collections.emptyList();
            }
            RecentApiOrderAppointmentRespDto respDto = appointmentOrderClient.getOrderAppointmentByUserIdAndHospitalAreaId(userId, hospital.getHospitalCode());
            if (!CollectionUtils.isEmpty(respDto.getList())) {
                lastDepartmentId = respDto.getList().stream().map(ApiOrderAppointment::getDepartmentId).distinct().collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("查询用户最近预约订单失败", e);
        }

        List<CustomerDepartmentVo> voList = lastDepartmentId.stream()
                .map(departmentId -> {
                            try {
                                return getDetail(departmentId);
                            } catch (Exception e) {
                                return null;
                            }
                        }
                )
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<Long> parentIds = voList.stream().filter(vo -> ObjectUtils.isEmpty(vo.getParentId()) || vo.getParentId() > 0).map(CustomerDepartmentVo::getParentId).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(parentIds)) {
            List<Department> departmentList = departmentQuery.queryBy(parentIds);
            if (!ObjectUtils.isEmpty(departmentList)) {
                Map<Long, Department> departmentMap = departmentList.stream().collect(Collectors.toMap(Department::getId, Function.identity()));
                voList.forEach(vo -> Optional.ofNullable(departmentMap.get(vo.getParentId())).ifPresent(parent -> vo.setParentName(parent.getName())));
            }
        }
        return voList;
    }

    @Override
    public CustomerDepartmentPageVo queryDepartmentPage(CustomerQueryDepartmentPageReqDto request) {
        try (Page<Department> departmentPage = departmentQuery.pageQueryDepartment(new DepartmentQuery.DepartmentQueryOption(request.getCurrentPage(), request.getPageSize())
                .setHospitalAreaId(request.getHospitalAreaId())
                .setCategory(request.getCategory())
                .setCategoryIn(request.getCategoryIn()))) {
            return pageVoConvert.toPageVo(departmentPage, CustomerDepartmentPageVo.class, DepartmentConverter::toCustomerDepartmentVo);
        }
    }

    @Override
    public CustomerDepartmentsTreeSearchListVo queryChildrenTree(Long departmentId) {
        Department department = getDepartment(departmentId);
        List<Department> departments = departmentQuery.queryDepartmentsByHospitalAreaId(department.getHospitalAreaId());

        String kunHuaHospitalCode = "871900";
        if (department.getHospitalCode().equals(kunHuaHospitalCode)) {
            departments = departments.stream().filter(d -> d.getParentId().equals(0L)).collect(Collectors.toList());
        }

        CustomerDepartmentTreeVo root = DepartmentConverter.toCustomerDepartmentTreeVo(department);
        Map<Long, List<Department>> departmentParentMap = departments
                .stream()
                .collect(Collectors.groupingBy(Department::getParentId));
        buildChildrenTree(root, departmentParentMap);

        List<CustomerDepartmentTreeVo> vos = Collections.singletonList(root);
        return new CustomerDepartmentsTreeSearchListVo()
                ._list(vos)
                .levelCount(calculateMaxDepth(vos));
    }

    @Override
    public CustomerDoctorScheduleInfoVo queryGroupedDoctorListBy(String hospitalAreaCode, String departmentCode) {
        Department department = departmentQuery.queryBy(hospitalAreaCode, departmentCode);
        if (null == department) {
            throw new BizException(HttpStatus.NOT_FOUND, "科室不存在");
        }
        return queryGroupedDoctorListByDepartmentId(department.getId());
    }

    @Override
    public List<CustomerAllScheduleDoctorDetailVo> querySpecialNeedsDoctorList(Long hospitalAreaId, Long departmentId, String doctorName) {
        List<CustomerAllScheduleDoctorDetailVo> vos = new ArrayList<>();
        if (null != departmentId) {
            vos = queryGroupedDoctorListByDepartmentId(departmentId).getAllDoctors();
        } else if (null != hospitalAreaId) {
            vos = queryGroupedDoctorListByHospitalAreaId(hospitalAreaId).getAllDoctors();
        }
        if (StringUtils.hasText(doctorName)) {
            return filterDoctorsByName(vos, doctorName);
        }
        return vos;
    }


    private List<CustomerAllScheduleDoctorDetailVo> filterDoctorsByName(List<CustomerAllScheduleDoctorDetailVo> doctors, String doctorName) {
        return doctorName != null && !doctorName.isEmpty() ? doctors.stream().filter(d -> d.getName().contains(doctorName)).collect(Collectors.toList()) : doctors;
    }

    private List<DoctorGroupRelation> getDoctorGroupRelations(List<Long> doctorIds) {
        return doctorQuery.listDoctorGroupRelationBy(doctorIds);
    }

    private CustomerGroupedDoctorDetailVo createCustomerGroupedDoctorDetailVo(Map.Entry<String, List<ScheduleResponseDto.ScheduleInfo>> doctorScheduleEntry, List<Doctor> doctors, Department department, List<DoctorGroupRelation> doctorGroupRelations) {
        String doctorCode = doctorScheduleEntry.getKey();
        List<ScheduleResponseDto.ScheduleInfo> schedulesForDoctor = doctorScheduleEntry.getValue();
        if (doctorCode == null || CollectionUtils.isEmpty(schedulesForDoctor)) {
            return null;
        }

        Doctor doctor = doctors.stream().filter(d -> d.getThrdpartDoctorCode().equals(doctorCode)).findFirst().orElse(null);
        if (doctor == null) {
            return null;
        }

        CustomerGroupedDoctorDetailVo groupedDoctorDetailVo = toGroupedDoctorDetailVo(doctor);
        groupedDoctorDetailVo.setId(doctor.getId());
        groupedDoctorDetailVo.setCanOrder(schedulesForDoctor.stream().anyMatch(schedule -> null != schedule.getSrcNum() && schedule.getSrcNum() > 0));
        groupedDoctorDetailVo.setDepartmentName(department.getName());

        groupedDoctorDetailVo.setDateInWeeks(schedulesForDoctor.stream().map(this::scheduleInfoToDateInWeek).collect(Collectors.toSet()).stream().sorted(comparing(CustomerDateInWeekDto::getLocalDate)).collect(Collectors.toList()));

        schedulesForDoctor.stream().findFirst().ifPresent(schedule -> groupedDoctorDetailVo.setShowSchDate(null != schedule.getShowSchDate() && schedule.getShowSchDate() == 1));

        boolean isDoctorInGroup = doctorGroupRelations.stream().anyMatch(q -> q.getDoctorId().equals(doctor.getId()));
        groupedDoctorDetailVo.setBelongDoctorGroups(isDoctorInGroup);
        return groupedDoctorDetailVo;
    }

    private CustomerDateInWeekDto scheduleInfoToDateInWeek(ScheduleResponseDto.ScheduleInfo schedule) {
        CustomerDateInWeekDto customerDateInWeek = new CustomerDateInWeekDto();
        String dateInWeek = convertDateToWeekday(schedule.getSchDate());
        if (TODAY.equals(dateInWeek)) {
            customerDateInWeek.setDateInWeek(dateInWeek);
        } else if (TOMORROW.equals(dateInWeek)) {
            customerDateInWeek.setDateInWeek(dateInWeek);
        } else {
            customerDateInWeek.setDateInWeek(convertDateToMMDD(schedule.getSchDate()));
        }

        customerDateInWeek.setDate(convertDateToMMDD(schedule.getSchDate()));
        customerDateInWeek.setLocalDate(schedule.getSchDate());
        customerDateInWeek.setCanOrder(null != schedule.getSrcNum() && schedule.getSrcNum() > 0);
        return customerDateInWeek;
    }

    private List<Department> getDepartmentAndParents(Department department, Collection<Department> allDepartments) {
        List<Department> departments = new ArrayList<>();
        departments.add(department);
        if (department.getParentId() != null && department.getParentId() != 0) {
            allDepartments.stream().filter(temp -> temp.getId().equals(department.getParentId())).findFirst().ifPresent(parentDepartment -> departments.addAll(getDepartmentAndParents(parentDepartment, allDepartments)));
        }
        return departments;
    }

    private void addDepartmentsNotContains(List<Department> departmentsAndParents, List<Department> temp) {
        for (Department department : temp) {
            if (!departmentsAndParents.contains(department)) {
                departmentsAndParents.add(department);
            }
        }
    }

    private List<CustomerDepartmentTreeVo> getDepartmentsTreeFromDepartments(List<Department> departments, Long parentId) {
        List<Department> departmentsFilterByParentId = departments.stream().filter(department -> department.getParentId().equals(parentId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(departmentsFilterByParentId)) {
            return new ArrayList<>();
        }

        List<CustomerDepartmentTreeVo> vos = new ArrayList<>();

        for (Department department : departmentsFilterByParentId) {
            CustomerDepartmentTreeVo vo = new CustomerDepartmentTreeVo();
            vo.setId(department.getId());
            vo.setName(department.getName());
            vo.setParentId(department.getParentId());
            vo.setHospitalId(department.getHospitalId());
            vo.setHospitalAreaId(department.getHospitalAreaId());
            vo.setThrdpartDepCode(department.getThrdpartDepCode());
            vo.setHospitalAreaCode(department.getHospitalCode());
            vo.setDisplayBgColor(department.getDisplayBgColor());
            vo.setUri(department.getUri());
            vo.setFirstLetter(department.getFirstLetter());
            vo.setAddressIntro(department.getAddressIntro());
            vo.setCaution(department.getCaution());
            vo.setRemark(department.getRemark());
            vo.setCategory(department.getCategoryStr());
            vo.setSort(department.getSort());
            vo.setEnableDepartmentDetail(department.getEnableDepartmentDetail());
            vo.setDisplayFields(department.getDisplayFields());
            vo.setDisplayDepartmentName(department.getDisplayDepartmentName());
            vos.add(vo);

            List<CustomerDepartmentTreeVo> children = getDepartmentsTreeFromDepartments(departments, department.getId());
            if (!CollectionUtils.isEmpty(children)) {
                vo.setChildrens(children);
            }
        }

        if (parentId != 0 || departmentsFilterByParentId.get(0).getHospitalCode().equals(ANNING_FIRST_HOSPITAL_CODE)) {
            return vos.stream().sorted(comparing(CustomerDepartmentTreeVo::getSort).reversed()).collect(Collectors.toList());
        }

        return vos;
    }

    private int calculateMaxDepth(List<CustomerDepartmentTreeVo> tree) {
        if (tree == null || CollectionUtils.isEmpty(tree)) {
            return 0;
        }

        int maxDepth = 0;
        for (CustomerDepartmentTreeVo department : tree) {
            int depth = calculateMaxDepth(department.getChildrens()) + 1;
            if (depth > maxDepth) {
                maxDepth = depth;
            }
        }

        return maxDepth;
    }

    public List<CustomerDepartmentTreeVo> getDepartmentsByLevel2(List<CustomerDepartmentTreeVo> departments, int needLevel) {
        List<CustomerDepartmentTreeVo> result = new ArrayList<>();
        if (departments != null) {
            for (CustomerDepartmentTreeVo dept : departments) {
                traverseAndCollect(dept, needLevel - 1, result);
            }
        }
        return result;
    }

    private void traverseAndCollect(CustomerDepartmentTreeVo department, int level, List<CustomerDepartmentTreeVo> result) {
        if (level == 0) {
            result.add(department);
            department.setChildrens(null);
        } else if (department.getChildrens() != null && !department.getChildrens().isEmpty()) {
            for (CustomerDepartmentTreeVo child : department.getChildrens()) {
                traverseAndCollect(child, level - 1, result);
            }
        }
    }
}
