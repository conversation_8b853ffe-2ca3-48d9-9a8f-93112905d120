-- 数据库敏感数据加密存储POC - H2数据库初始化脚本

-- 创建JPA用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    phone VARCHAR(500),  -- 加密字段需要更大的存储空间
    email VARCHAR(500),  -- 加密字段需要更大的存储空间
    id_card VARCHAR(500), -- 加密字段需要更大的存储空间
    real_name VARCHAR(500), -- 加密字段需要更大的存储空间
    age INTEGER,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建MyBatis用户表
CREATE TABLE IF NOT EXISTS mybatis_users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    phone VARCHAR(500),  -- 加密字段需要更大的存储空间
    email VARCHAR(500),  -- 加密字段需要更大的存储空间
    id_card VARCHAR(500), -- 加密字段需要更大的存储空间
    real_name VARCHAR(500), -- 加密字段需要更大的存储空间
    age INTEGER,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建JPA用户表索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- 创建MyBatis用户表索引
CREATE INDEX IF NOT EXISTS idx_mybatis_users_username ON mybatis_users(username);
CREATE INDEX IF NOT EXISTS idx_mybatis_users_status ON mybatis_users(status);
CREATE INDEX IF NOT EXISTS idx_mybatis_users_created_at ON mybatis_users(created_at);

-- 创建影子字段迁移演示用户表
CREATE TABLE IF NOT EXISTS migration_users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,

    -- 手机号：传统直接加密方式
    phone VARCHAR(500),

    -- 邮箱：明文字段优先策略
    email VARCHAR(255),
    email_encrypted VARCHAR(500),

    -- 身份证号：影子字段优先策略
    id_card VARCHAR(255),
    id_card_encrypted VARCHAR(500),

    -- 真实姓名：仅影子字段策略
    real_name VARCHAR(255),
    real_name_encrypted VARCHAR(500),

    -- 迁移相关字段
    data_version INT NOT NULL DEFAULT 1,
    migration_status VARCHAR(20) NOT NULL DEFAULT 'NOT_MIGRATED',

    -- 其他字段
    age INTEGER,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建迁移用户表索引
CREATE INDEX IF NOT EXISTS idx_migration_users_username ON migration_users(username);
CREATE INDEX IF NOT EXISTS idx_migration_users_status ON migration_users(status);
CREATE INDEX IF NOT EXISTS idx_migration_users_migration_status ON migration_users(migration_status);
CREATE INDEX IF NOT EXISTS idx_migration_users_data_version ON migration_users(data_version);
CREATE INDEX IF NOT EXISTS idx_migration_users_created_at ON migration_users(created_at);

-- 创建加密策略配置表
CREATE TABLE IF NOT EXISTS encryption_strategies (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    strategy_key VARCHAR(100) NOT NULL UNIQUE,
    migration_strategy VARCHAR(50) NOT NULL,
    algorithm_type VARCHAR(20) DEFAULT 'AES',
    description VARCHAR(500),
    enabled BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    version BIGINT DEFAULT 0
);

-- 创建策略表索引
CREATE INDEX IF NOT EXISTS idx_encryption_strategies_key ON encryption_strategies(strategy_key);
CREATE INDEX IF NOT EXISTS idx_encryption_strategies_enabled ON encryption_strategies(enabled);
CREATE INDEX IF NOT EXISTS idx_encryption_strategies_priority ON encryption_strategies(priority);

-- 插入默认策略配置（如果不存在）
INSERT INTO encryption_strategies (strategy_key, migration_strategy, algorithm_type, description, created_by, updated_by)
SELECT 'phone', 'DIRECT_ENCRYPT', 'AES', '手机号：传统直接加密', 'system', 'system'
WHERE NOT EXISTS (SELECT 1 FROM encryption_strategies WHERE strategy_key = 'phone');

INSERT INTO encryption_strategies (strategy_key, migration_strategy, algorithm_type, description, created_by, updated_by)
SELECT 'email', 'PLAINTEXT_PRIORITY', 'AES', '邮箱：明文优先（迁移初期）', 'system', 'system'
WHERE NOT EXISTS (SELECT 1 FROM encryption_strategies WHERE strategy_key = 'email');

INSERT INTO encryption_strategies (strategy_key, migration_strategy, algorithm_type, description, created_by, updated_by)
SELECT 'idCard', 'SHADOW_PRIORITY', 'SM2', '身份证：影子字段优先，使用国密算法', 'system', 'system'
WHERE NOT EXISTS (SELECT 1 FROM encryption_strategies WHERE strategy_key = 'idCard');

INSERT INTO encryption_strategies (strategy_key, migration_strategy, algorithm_type, description, created_by, updated_by)
SELECT 'realName', 'SHADOW_ONLY', 'AES', '姓名：仅影子字段（迁移完成）', 'system', 'system'
WHERE NOT EXISTS (SELECT 1 FROM encryption_strategies WHERE strategy_key = 'realName');
