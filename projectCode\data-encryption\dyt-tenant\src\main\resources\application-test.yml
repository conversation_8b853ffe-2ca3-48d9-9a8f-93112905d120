server:
  port: 9001

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************************************************************************
    username: root
    password: Dyt123456
  redis:
    cluster:
      nodes: 192.168.1.227:6380,192.168.1.228:6379,192.168.1.229:6379,192.168.1.227:6379,192.168.1.228:6380,192.168.1.229:6380
    password: xQ3F5cRX6X
    database: 1
  elasticsearch:
    rest:
      uris: https://biz-es.ynhdkc.com
      username: elastic
      password: 58JWl2Y2ay
  cloud:
    loadbalancer:
      cache:
        enabled: false
      ribbon:
        enabled: true
  security.oauth2.resourceserver.jwt:
    jwks-service-discovery: false
    jwk-set-uri: http://*************:30880/apis/v1/privilege/.well-known/jwks.json
    jws-algorithm: RS256

eureka:
  client:
    serviceUrl:
      defaultZone: http://localhost:20001/eureka

backend.kafka-appender.enabled: false

megaease:
  output:
    type: console
  trace:
    enabled: false
  metrics:
    enabled: false

#mybatis:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
logging:
  level:
    #    com.ynhdkc.tenant.dao.mapper: debug
    org.apache.kafka.clients.producer: off
    backend.common.kafka.streams: off # 关闭kafka stream日志