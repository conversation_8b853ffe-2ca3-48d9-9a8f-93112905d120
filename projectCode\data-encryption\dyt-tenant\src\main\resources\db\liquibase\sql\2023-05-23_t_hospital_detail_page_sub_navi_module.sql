CREATE TABLE t_hospital_detail_page_sub_navi_module
(
    id                     BIGINT      NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    tenant_id              BIGINT      NOT NULL COMMENT '租户id',
    hospital_id            BIGINT      NOT NULL COMMENT '医院id',
    hospital_area_id       bigint      not null comment '院区id',
    hospital_code          varchar(20) null comment '医院编码',
    sub_navi_type          INT         NOT NULL DEFAULT 0 COMMENT '副标题样式：0，固定；1，横向滑动；',
    sub_navi_display_limit INT         NOT NULL DEFAULT 4 COMMENT '副标题单行显示上限',
    sort                   INT         NOT NULL DEFAULT 0 COMMENT '排序',
    channels varchar(100) NOT NULL DEFAULT '' COMMENT '渠道',
    create_time            DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time            DATETIME    NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT '医院详情页子导航模块';
