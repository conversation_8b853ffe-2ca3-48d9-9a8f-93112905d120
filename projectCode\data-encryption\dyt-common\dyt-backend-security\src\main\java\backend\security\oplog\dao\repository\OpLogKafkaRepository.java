package backend.security.oplog.dao.repository;

import backend.security.oplog.dto.OpLogMessageBuilder;
import backend.security.oplog.dto.OpLogModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

@Slf4j
@Repository
public class OpLogKafkaRepository {

//    private static final String DATA_FORMAT = "yyyy.MM.dd";

//    protected RestHighLevelClient client;

    @Resource
    private OpLogMessageBuilder opLogMessageBuilder;

    private static final String TOPIC_NAME = "dyt-op-log";

    private KafkaTemplate<String, String> template;

    public void createOne(OpLogModel opLogModel) {
        template.send(TOPIC_NAME, opLogMessageBuilder.getOpLogKey(), opLogMessageBuilder.getOpLogMessage(opLogModel));
    }

    public void setTemplate(KafkaTemplate<String, String> template) {
        this.template = template;
    }

//    public void createOne(Map<String, Object> opLog) {
//        String formatDateStr = DateFormatUtils.format(new Date(), DATA_FORMAT, TimeZone.getTimeZone(ZoneOffset.UTC.getId()));
//        String index = INDEX_PREFIX + formatDateStr;
//        IndexRequest indexRequest = new IndexRequest(index).source(opLog);
//        try {
//            IndexResponse response = client.index(indexRequest, RequestOptions.DEFAULT);
//            if (!response.status().equals(RestStatus.OK)
//                    && !response.status().equals(RestStatus.CREATED)) {
//                log.error("es-response status: {}", response.status().getStatus());
//            }
//        } catch (IOException e) {
//            log.error("es-error: {}", e.getMessage());
//        }
//    }

//    public static void main(String[] arg) {
//        OpLogModel opLogModel = new OpLogModel();
//        opLogModel.setRawMessage("{\"id\":100011270,\"order_no\":\"DYT240506172134637728888\",\"hos_payed_no\":\"\",\"ext_order_no\":\"DYT240506172134637728888\",\"transaction_id\":\"\",\"user_id\":2043556,\"patient_id\":18809186,\"jz_card\":\"**********\",\"patient_name\":\"陈青云\",\"patient_birthday\":7132,\"patient_phone\":\"19184280612\",\"patient_id_card\":\"******************\",\"patient_sex\":2,\"hospital_id\":109,\"hospital_area_id\":110,\"hospital_code\":\"871045\",\"hospital_area_name\":\"云南大学附属医院(云南省第二人民医院)\",\"department_id\":2082,\"department_code\":\"234\",\"department_name\":\"肝病内科\",\"doctor_id\":134752,\"doctor_code\":\"156\",\"doctor_name\":\"李晖\",\"doctor_level\":\"主任医师\",\"total_fee\":15.5,\"order_type\":0,\"status\":-1,\"cancel_status\":0,\"consultation_status\":0,\"write_off_status\":0,\"pay_time\":null,\"create_time\":1715016094000,\"update_time\":1715016094000,\"cancel_time\":null,\"appoint_channel\":0,\"is_deleted\":0,\"cancel_channel\":0,\"cancel_user\":\"用户端退费\",\"sch_date\":19856,\"schedule_id\":\"533||122\",\"time_type\":2,\"queue_sn\":\"8\",\"start_time\":\"15:00\",\"end_time\":\"15:20\",\"jz_address\":\"\"}");
//        opLogModel.setUrl("test url");
//        opLogModel.setUserName("test");
//        opLogModel.setUserId(1L);
//        opLogModel.setOpCode("123");
//
//
//        OpLogMessageBuilder opLogMessageBuilder = new OpLogMessageBuilder();
//        System.out.println(opLogMessageBuilder.getOpLogMessage(opLogModel));
//    }

}
