package com.ynhdkc.tenant.api.backend;

import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import backend.security.method.BackendSecurityRequired;
import backend.security.oplog.DytSecurityRequired;
import backend.security.service.BackendTenantUserService;
import com.ynhdkc.tenant.handler.HospitalApi;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.HospitalKafkaService;
import com.ynhdkc.tenant.service.backend.HospitalService;
import com.ynhdkc.tenant.service.backend.OrganizationStructureService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2023/2/8 13:11:25
 */
@Api(tags = "Hospital")
@RestController
@RequiredArgsConstructor
public class HospitalController implements HospitalApi {
    private final HospitalService hospitalService;
    private final HospitalKafkaService hospitalKafkaService;
    private final OrganizationStructureService organizationStructureService;

    private final BackendTenantUserService backendTenantUserService;

    @BackendSecurityRequired(tenantIdExpr = "#request.tenantId")
    @DytSecurityRequired(needOpLog = true, value = "hospital:create")
    @Override
    public ResponseEntity<HospitalDetailVo> create(HospitalCreateReqDto request) {
        HospitalDetailVo hospitalVo = hospitalService.create(request);
        hospitalKafkaService.syncHospital(hospitalVo.getId());

        organizationStructureService.asyncUserOrganizationStructureAndUser();

        return ResponseEntity.ok(hospitalVo);
    }

    @BackendSecurityRequired(tenantManager = true)
    @DytSecurityRequired(needOpLog = true, value = "hospital:delete")
    @Override
    public ResponseEntity<BaseOperationResponse> delete(Long hospitalId, Long tenantId) {
        BaseOperationResponse baseOperationResponse = hospitalService.delete(hospitalId);
        hospitalKafkaService.deleteHospital(hospitalId);

        organizationStructureService.asyncUserOrganizationStructureAndUser();

        return ResponseEntity.ok(baseOperationResponse);
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:query:detail")
    public ResponseEntity<HospitalDetailVo> get(Long hospitalId) {
        if (!backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.HospitalId(hospitalId))) {
            throw new BizException(HttpStatus.FORBIDDEN, "用户没有该医院权限");
        }
        return ResponseEntity.ok(hospitalService.getDetail(hospitalId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:query:list")
    public ResponseEntity<HospitalPageVo> query(HospitalQueryReqDto request) {
        if ((null == request.getStartCreateTime() && null != request.getEndCreateTime()) || (null != request.getStartCreateTime() && null == request.getEndCreateTime())) {
            throw new BizException(HttpStatus.BAD_REQUEST, "必须同时填写开始时间、结束时间");
        }
        return ResponseEntity.ok(hospitalService.query(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:query:list")
    public ResponseEntity<HospitalAreaQueryLabelsVo> queryHospitalAreaQueryLabels(HospitalAreaQueryLabelsReqDto request) {
        if (!backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.TenantId(request.getTenantId()))) {
            throw new BizException(HttpStatus.FORBIDDEN, "用户没有该租户权限");
        }
        return ResponseEntity.ok(hospitalService.queryHospitalAreaQueryLabels(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:query:page")
    public ResponseEntity<CustomerHospitalPageVo> queryHospitalListPage(QueryCustomerHospitalPageReqDto queryHospitalPageDto) {
        return ResponseEntity.ok(hospitalService.customerQueryHospitalList(queryHospitalPageDto.getUserId(), queryHospitalPageDto));
    }

    @BackendSecurityRequired(tenantManager = true)
    @DytSecurityRequired(needOpLog = true, value = "hospital:sync")
    @Override
    public ResponseEntity<BaseOperationResponse> sync() {
        hospitalKafkaService.syncAllHospital();
        return ResponseEntity.ok(new BaseOperationResponse("同步成功"));
    }

    @BackendSecurityRequired(tenantIdExpr = "#request.tenantId", hospitalIdExpr = "#hospitalId")
    @DytSecurityRequired(needOpLog = true, value = "hospital:update")
    @Override
    public ResponseEntity<HospitalDetailVo> update(Long hospitalId, HospitalUpdateReqDto request) {
        HospitalDetailVo hospitalVo = hospitalService.update(hospitalId, request);
        hospitalKafkaService.syncHospital(hospitalVo.getId());

        organizationStructureService.asyncUserOrganizationStructureAndUser();

        return ResponseEntity.ok(hospitalVo);
    }
}
