package backend.common.entity.dto.hisgateway.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * CV02.01.101
 */
public enum IdCardType {

    ID_CARD("居民身份证", "01", 1),
    HUKOU("居民户口簿", "02", 2),
    PASSPORT("护照", "03", 3),
    OFFICER_CERTIFICATE("军官证", "04", 4),
    DRIVER_LICENSE("驾驶证", "05", 5),
    HKTP("港澳居民来往内地通行证", "06", 6),
    TWTP("台湾居民来往内地通行证", "07", 7),
    BIRTH_CERTIFICATION("出生证明", "08", 8),
    MEDICAL_INSURANCE_CARD("医保卡", "09", 9),
    FOREIGN_ID_CARD("外国人永久居留身份证", "15", 15),
    HONG_KONG_AND_MACAU_RESIDENCE_PERMIT("港澳台居民居住证", "17", 17),
    MOTHER_ID_CARD("母亲身份证", "19", 19),

    OTHER_CARD("其他法定有效证件", "99", 99);

    private final String value;

    private final String code;

    // 滇医通数据库
    private final Integer number;

    IdCardType(String value, String code, Integer number) {
        this.value = value;
        this.code = code;
        this.number = number;
    }


    @JsonCreator
    public static IdCardType getFromNumber(Integer number) {
        for (IdCardType t : IdCardType.values()) {
            if (t.getNumber().equals(number)) {
                return t;
            }
        }
        throw new IllegalArgumentException("卡类型不存在");
    }

    @JsonCreator
    public static IdCardType getFromCode(String code) {
        for (IdCardType t : IdCardType.values()) {
            if (t.getCode().equals(code)) {
                return t;
            }
        }
        throw new IllegalArgumentException("卡类型不存在");
    }

    public static IdCardType getFromValue(String value) {
        for (IdCardType t : IdCardType.values()) {
            if (t.getValue().equals(value)) {
                return t;
            }
        }
        throw new IllegalArgumentException("卡类型不存在");
    }

    public String getValue() {
        return value;
    }

    @JsonValue
    public String getCode() {
        return code;
    }

    public Integer getNumber() {
        return number;
    }

}
