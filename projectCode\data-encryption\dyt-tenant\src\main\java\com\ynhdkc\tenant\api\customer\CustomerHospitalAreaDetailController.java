package com.ynhdkc.tenant.api.customer;

import backend.common.exception.BizException;
import com.ynhdkc.tenant.handler.CustomerHospitalAreaDetailApi;
import com.ynhdkc.tenant.model.CustomerHospitalAreaDetailPageSubNaviModuleVo;
import com.ynhdkc.tenant.service.customer.CustomerHospitalAreaDetailPageSubNaviModuleService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/7/19 16:03
 */
@Api(tags = "CustomerHospitalArea")
@RestController
@RequiredArgsConstructor
public class CustomerHospitalAreaDetailController implements CustomerHospitalAreaDetailApi {
    private final CustomerHospitalAreaDetailPageSubNaviModuleService customerHospitalAreaDetailPageSubNaviModuleService;

    @Override
    public ResponseEntity<List<CustomerHospitalAreaDetailPageSubNaviModuleVo>> getHospitalAreaDetailPageSubNaviModuleByHospitalAreaId(Long hospitalAreaId, String hospitalAreaCode) {
        if (null == hospitalAreaId && null == hospitalAreaCode) {
            throw new BizException(HttpStatus.BAD_REQUEST, "hospitalAreaId和hospitalAreaCode不能同时为空");
        }
        return ResponseEntity.ok(customerHospitalAreaDetailPageSubNaviModuleService.getHospitalAreaDetailPageSubNaviModuleByHospitalAreaId(hospitalAreaId, hospitalAreaCode));
    }
}
