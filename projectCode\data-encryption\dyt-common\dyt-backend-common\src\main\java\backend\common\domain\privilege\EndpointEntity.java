package backend.common.domain.privilege;


import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EndpointEntity extends BaseEntity {
    /**
     * 模块
     */
    private String module;
    /**
     * 模块名称
     */
    private String moduleName;
    private String groupName;
    private String summary;
    private String description;
    private String path;
    private String httpMethod;
}
