package com.ynhdkc.tenant.dao;

import com.ynhdkc.tenant.entity.setting.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-09 16:03
 */
public interface HospitalAreaSettingRepository {
    void createAppointmentRuleSetting(AppointmentRuleSetting entity);

    void updateAppointmentRuleSetting(AppointmentRuleSetting entity);

    void createDiagnosisPaymentSetting(DiagnosisPaymentSetting entity);

    void updateDiagnosisPaymentSetting(DiagnosisPaymentSetting entity);

    void createHospitalizationSetting(HospitalizationSetting entity);

    void updateHospitalizationSetting(HospitalizationSetting entity);

    void createPatientReportSetting(PatientReportSetting entity);

    void updatePatientReportSetting(PatientReportSetting entity);

    void createPatientCardSetting(PatientCardSetting entity);

    void updatePatientCardSetting(PatientCardSetting entity);

    void createCustomBusinessSetting(CustomBusinessSetting entity);

    void updateCustomBusinessSetting(CustomBusinessSetting entity);
}
