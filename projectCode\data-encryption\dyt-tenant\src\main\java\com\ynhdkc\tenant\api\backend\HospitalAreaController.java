package com.ynhdkc.tenant.api.backend;

import backend.common.domain.tenant.constant.AppointmentSystemDepends;
import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import backend.security.method.BackendSecurityRequired;
import backend.security.oplog.DytSecurityRequired;
import backend.security.service.BackendTenantUserService;
import com.ynhdkc.tenant.entity.constant.*;
import com.ynhdkc.tenant.handler.HospitalAreaApi;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.HospitalAreaService;
import com.ynhdkc.tenant.service.backend.HospitalKafkaService;
import com.ynhdkc.tenant.service.backend.IHisRequestService;
import com.ynhdkc.tenant.service.backend.OrganizationStructureService;
import com.ynhdkc.tenant.util.ParameterUtil;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-27 11:47
 */
@Api(tags = "HospitalArea")
@RestController
@RequiredArgsConstructor
public class HospitalAreaController implements HospitalAreaApi {
    private final HospitalAreaService hospitalAreaService;
    private final HospitalKafkaService hospitalKafkaService;
    private final OrganizationStructureService organizationStructureService;

    private final BackendTenantUserService backendTenantUserService;
    private final IHisRequestService hisRequestService;

    @DytSecurityRequired(needOpLog = true, value = "hospital:area:batchSetStopGenerateSchedule")
    @Override
    public ResponseEntity<Void> batchSetStopGenerateSchedule(HospitalAreaBatchSetStopGenerateScheduleReqDto request) {
        hospitalAreaService.batchSetStopGenerateSchedule(request);
        return ResponseEntity.ok().build();
    }

    @DytSecurityRequired(needOpLog = true, tenantIdExpr = "#request.tenantId", value = "hospital:area:create")
    @BackendSecurityRequired(tenantIdExpr = "#request.tenantId", hospitalIdExpr = "#request.hospitalId")
    @Override
    public ResponseEntity<HospitalAreaDetailVo> create(HospitalAreaCreateReqDto request) {
        HospitalAreaDetailVo vo = hospitalAreaService.create(request);
        hospitalKafkaService.syncHospital(vo.getHospitalId());

        organizationStructureService.asyncUserOrganizationStructureAndUser();

        return ResponseEntity.ok(vo);
    }

    @DytSecurityRequired(needOpLog = true, value = "hospital:area:createSetting")
    @BackendSecurityRequired(tenantIdExpr = "#request.tenantId", hospitalIdExpr = "#request.hospitalId", hospitalAreaIdExpr = "#hospitalAreaId")
    @Override
    public ResponseEntity<HospitalAreaSettingDetailVo> createSetting(Long hospitalAreaId, HospitalAreaSettingCreateReqDto request) {
        CustomBusinessSettingDto customBusinessSetting = request.getCustomBusinessSetting();
        if (null == customBusinessSetting) {
            throw new BizException(HttpStatus.BAD_REQUEST, "自定义业务规则不能为空");
        }

        if (null == customBusinessSetting.getWechatOpenPath() && null == customBusinessSetting.getMiniProgramAppId()) {
            throw new BizException(HttpStatus.BAD_REQUEST, "微信小程序和微信公众号至少填写一个");
        }
        if (null != customBusinessSetting.getMiniProgramAppId() && null == customBusinessSetting.getMiniProgramPath()) {
            throw new BizException(HttpStatus.BAD_REQUEST, "微信小程序路径不能为空");
        }
        return ResponseEntity.ok(hospitalAreaService.createSetting(hospitalAreaId, request));
    }

    @DytSecurityRequired(needOpLog = true, value = "hospital:area:delete")
    @BackendSecurityRequired(tenantManager = true)
    @Override
    public ResponseEntity<BaseOperationResponse> delete(Long hospitalAreaId, Long tenantId, Long hospitalId) {
        Pair<BaseOperationResponse, Long> resp = hospitalAreaService.delete(hospitalAreaId);
        hospitalKafkaService.deleteHospital(resp.getSecond());

        organizationStructureService.asyncUserOrganizationStructureAndUser();

        return ResponseEntity.ok(resp.getFirst());
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:deleteDepartmentCache")
    public ResponseEntity<BaseOperationResponse> deleteDepartmentCache(Long hospitalAreaId) {
        if (!backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.HospitalAreaId(hospitalAreaId))) {
            throw new BizException(HttpStatus.FORBIDDEN, "用户没有该院区权限");
        }
        BaseOperationResponse baseOperationResponse = new BaseOperationResponse();
        baseOperationResponse.setMessage("删除成功");
        baseOperationResponse.setStatus("success");
        return ResponseEntity.ok(baseOperationResponse);
    }

    @DytSecurityRequired(needOpLog = true, value = "hospital:area:get")
    @Override
    public ResponseEntity<HospitalAreaDetailVo> get(Long hospitalAreaId) {
        if (!backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.HospitalAreaId(hospitalAreaId))) {
            throw new BizException(HttpStatus.FORBIDDEN, "用户没有该院区权限");
        }
        return ResponseEntity.ok(hospitalAreaService.getDetail(hospitalAreaId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:area:getAppointNotifyConfig")
    public ResponseEntity<AppointNotifyConfigVo> getAppointNotifyConfig(String hospitalCode) {
        return ResponseEntity.ok(hospitalAreaService.getAppointNotifyConfig(hospitalCode));
    }

    @DytSecurityRequired(needOpLog = true, value = "hospital:area:getFunctionDetail")
    @Override
    public ResponseEntity<HospitalAreaFunctionDetailVo> getFunctionDetail(Long hospitalAreaId) {
        if (!backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.HospitalAreaId(hospitalAreaId))) {
            throw new BizException(HttpStatus.FORBIDDEN, "用户没有该院区权限");
        }
        return ResponseEntity.ok(hospitalAreaService.getFunctionDetail(hospitalAreaId));
    }

    @DytSecurityRequired(needOpLog = true, value = "hospital:area:getLayout")
    @Override
    public ResponseEntity<HospitalAreaLayoutVo> getLayout(Long hospitalAreaId) {
        if (!backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.HospitalAreaId(hospitalAreaId))) {
            throw new BizException(HttpStatus.FORBIDDEN, "用户没有该院区权限");
        }
        return ResponseEntity.ok(hospitalAreaService.getLayout(hospitalAreaId));
    }

    @DytSecurityRequired(needOpLog = true, value = "hospital:area:getSettingDetail")
    @Override
    public ResponseEntity<HospitalAreaSettingDetailVo> getSettingDetail(Long hospitalAreaId, Long tenantId, Long hospitalId) {
        if (!backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.HospitalAreaId(hospitalAreaId))) {
            throw new BizException(HttpStatus.FORBIDDEN, "用户没有该院区权限");
        }
        return ResponseEntity.ok(hospitalAreaService.getSettingDetail(tenantId, hospitalId, hospitalAreaId));
    }

    @DytSecurityRequired(needOpLog = true, value = "hospital:area:query")
    @Override
    public ResponseEntity<HospitalAreaPageVo> query(HospitalAreaQueryReqDto request) {
        return ResponseEntity.ok(hospitalAreaService.query(request));
    }

    @DytSecurityRequired(needOpLog = true, value = "hospital:area:queryHospitalAreaDepartmentInfo")
    @BackendSecurityRequired
    @Override
    public ResponseEntity<HospitalAreaDepartmentsInfoPageVo> queryHospitalAreaDepartmentInfo(HospitalAreaDepartmentsInfoQueryReqDto request) {
        return ResponseEntity.ok(hospitalAreaService.queryHospitalAreaDepartmentInfo(request));
    }

    @Override
    public ResponseEntity<SetAppointNotifyConfigResponse> setAppointNotifyConfig(String hospitalCode, SetAppointNotifyConfigReqDto reqDto) {
        return ResponseEntity.ok(hospitalAreaService.setAppointNotifyConfig(hospitalCode, reqDto));
    }

    @DytSecurityRequired(needOpLog = true, value = "hospital:area:sync")
    @Override
    public ResponseEntity<BaseOperationResponse> syncFromHis(Long id) {
        Boolean isDependOnHis = hospitalAreaService.isHospitalAreaDependOnHis(id);
        if (Boolean.FALSE.equals(isDependOnHis)) {
            throw new BizException(HttpStatus.BAD_REQUEST, "该医院不依赖 HIS，无法进行此操作");
        }
        hisRequestService.requestDepartmentsFromHisBy(id);
        return ResponseEntity.ok(new BaseOperationResponse("同步成功"));
    }

    @DytSecurityRequired(needOpLog = true, value = "hospital:area:update")
    @BackendSecurityRequired(tenantIdExpr = "#request.tenantId", hospitalIdExpr = "#request.hospitalId", hospitalAreaIdExpr = "#hospitalAreaId")
    @Override
    public ResponseEntity<HospitalAreaDetailVo> update(Long hospitalAreaId, HospitalAreaUpdateReqDto request) {
        HospitalAreaDetailVo vo = hospitalAreaService.update(hospitalAreaId, request);
        hospitalKafkaService.syncHospital(vo.getHospitalId());

        organizationStructureService.asyncUserOrganizationStructureAndUser();

        return ResponseEntity.ok(vo);
    }

    @DytSecurityRequired(needOpLog = true, value = "hospital:area:updateFunction")
    @BackendSecurityRequired(tenantIdExpr = "#request.tenantId", hospitalIdExpr = "#request.hospitalId", hospitalAreaIdExpr = "#hospitalAreaId")
    @Override
    public ResponseEntity<HospitalAreaFunctionDetailVo> updateFunction(Long hospitalAreaId, HospitalAreaFunctionUpdateReqDto request) {
        if (!HospitalAreaFunctionStatus.contains(request.getStatus())) {
            throw new BizException(HttpStatus.BAD_REQUEST, "院区功能状态错误");
        }
        return ResponseEntity.ok(hospitalAreaService.updateFunction(hospitalAreaId, request));
    }

    @DytSecurityRequired(needOpLog = true, value = "hospital:area:updateSetting")
    @BackendSecurityRequired(tenantIdExpr = "#request.tenantId", hospitalIdExpr = "#request.hospitalId", hospitalAreaIdExpr = "#hospitalAreaId")
    @Override
    public ResponseEntity<HospitalAreaSettingDetailVo> updateSetting(Long hospitalAreaId, HospitalAreaSettingUpdateReqDto request) {
        HospitalAreaFunctionType functionType = HospitalAreaFunctionType.getByName(request.getType());
        if (null == functionType) {
            throw new BizException(HttpStatus.BAD_REQUEST, "院区功能类型错误");
        }
        switch (functionType) {
            case appointment_registration:
                AppointmentRuleSettingDto appointmentRuleSetting = request.getAppointmentRuleSetting();
                if (null == appointmentRuleSetting) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "预约挂号规则不能为空");
                }
                if (null != appointmentRuleSetting.getSystemDepends() && !AppointmentSystemDepends.contains(appointmentRuleSetting.getSystemDepends())) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "系统依赖错误");
                }
                if (null != appointmentRuleSetting.getDisplayTime() && !ParameterUtil.isTime(appointmentRuleSetting.getDisplayTime())) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "时间格式错误");
                }
                if (null != appointmentRuleSetting.getStopRefundTime() && !ParameterUtil.isTime(appointmentRuleSetting.getStopRefundTime())) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "时间格式错误");
                }
                break;
            case diagnosis_payment:
                DiagnosisPaymentSettingDto diagnosisPaymentSetting = request.getDiagnosisPaymentSetting();
                if (null == diagnosisPaymentSetting) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "门诊缴费规则不能为空");
                }
                if (null != diagnosisPaymentSetting.getStopRefundTime() && !ParameterUtil.isTime(diagnosisPaymentSetting.getStopRefundTime())) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "时间格式错误");
                }
                break;
            case patient_report:
                PatientReportSettingDto reportSetting = request.getPatientReportSetting();
                if (null == reportSetting) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "报告查询规则不能为空");
                }
                if (null != reportSetting.getSupportReportType() && !ReportSupportType.contains(reportSetting.getSupportReportType())) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "支持的报告类型错误");
                }
                if (null != reportSetting.getSupportSearchDateRange() && !ReportSupportSearchDateRange.contains(reportSetting.getSupportSearchDateRange())) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "支持查询日期范围错误");
                }
                if (!CollectionUtils.isEmpty(reportSetting.getSupportSearchTime())) {
                    for (Integer searchTime : reportSetting.getSupportSearchTime()) {
                        if (!ReportSupportSearchTime.contains(searchTime)) {
                            throw new BizException(HttpStatus.BAD_REQUEST, "支持的查询时间错误");
                        }
                    }
                }
                break;
            case hospitalization:
                if (null == request.getHospitalizationSetting()) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "住院规则不能为空");
                }
                break;
            case patient_card:
                PatientCardSettingDto patientCardSetting = request.getPatientCardSetting();
                if (null == patientCardSetting) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "就诊卡规则不能为空");
                }
                if (null != patientCardSetting.getBindType() && !PatientCardBindType.contains(patientCardSetting.getBindType())) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "就诊卡绑定类型错误");
                }
                if (null != patientCardSetting.getSupportPatientType() && !PatientCardSupportPatientType.contains(patientCardSetting.getSupportPatientType())) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "就诊卡支持的患者类型错误");
                }
                break;
            case custom:
                CustomBusinessSettingDto customBusinessSetting = request.getCustomBusinessSetting();
                if (null == customBusinessSetting) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "自定义业务规则不能为空");
                }
                if (null == request.getSettingId()) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "自定义业务规则id不能为空");
                }

                if (null == customBusinessSetting.getWechatOpenPath() && null == customBusinessSetting.getMiniProgramAppId()) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "微信小程序和微信公众号至少填写一个");
                }
                if (null != customBusinessSetting.getMiniProgramAppId() && null == customBusinessSetting.getMiniProgramPath()) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "微信小程序路径不能为空");
                }
                break;
            default:
                throw new BizException(HttpStatus.BAD_REQUEST, "院区功能类型错误");
        }
        HospitalAreaSettingDetailVo vo = hospitalAreaService.updateSetting(hospitalAreaId, request, functionType);
        hospitalKafkaService.syncHospital(vo.getHospitalId());
        return ResponseEntity.ok(vo);
    }
}
