package com.ynhdkc.tenant.entity.setting;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-09 10:01
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(name = "t_hospital_custom_business_setting")
public class CustomBusinessSetting extends BaseSetting {

    /**
     * 名称
     */
    private String name;
    /**
     * logo
     */
    private String logo;
    /**
     * 微信公众号路径
     */
    private String wechatOpenPath;
    /**
     * 小程序 app id
     */
    private String miniProgramAppId;
    /**
     * 小程序路径
     */
    private String miniProgramPath;
}
