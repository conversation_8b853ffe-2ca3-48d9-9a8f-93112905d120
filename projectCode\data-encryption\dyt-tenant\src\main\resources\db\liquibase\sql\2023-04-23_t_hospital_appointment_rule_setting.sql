create table t_hospital_appointment_rule_setting
(
    id                                bigint auto_increment comment '全局唯一标识'
        primary key,
    function_id                       bigint                                   null comment '功能 ID',
    tenant_id                         bigint                                   not null comment '租户 ID',
    hospital_id                       bigint                                   not null comment '医院 ID',
    hospital_area_id                  bigint                                   not null comment '院区 ID',
    hospital_code                     varchar(20)                              null comment '医院编码',
    department_level                  int                                      not null comment '科室级别：0，一级科室；1，二级科室；2，三级科室；3，四级科室；',
    system_depends                    int         default 0                    not null comment '挂号系统依赖：0，HIS；1，小系统；',
    appoint_today                     tinyint     default 1                    not null comment '当天预约：0，关闭；1，开启；',
    display_no                        tinyint     default 1                    not null comment '显示序号：0，关闭；1，开启；',
    stop_appoint_time                 time                                     not null comment '停止预约时间',
    forbidden_day          int     default 0 not null comment '禁止预约天数',
    advance_day                       int         default 7                    not null comment '提前预约天数',
    display_time                      time                                     not null comment '号源显示时间',
    payment_close_duration            bigint                                   not null comment '支付关闭时间，单位分钟',
    refund_today                      tinyint     default 0                    not null comment '是否允许当天退款：0，不允许；1，允许；',
    stop_refund_time                  time                                     not null comment '停止退款时间',
    confirm_medical_insurance_card    tinyint     default 0                    not null comment '是否需要确认医保卡：0，不需要；1，需要；',
    order_need_verify                 tinyint     default 0                    not null comment '是否需要核销：0，不需要；1，需要；',
    support_cancel_appointment        tinyint     default 1                    not null comment '是否支持取消预约：0，不支持；1，支持；',
    display_department_address        tinyint     default 1                    not null comment '显示科室地址：0，不显示；1，显示；',
    display_visit_time                tinyint     default 1                    not null comment '显示就诊时间：0，不显示；1，显示；',
    appointment_notice                varchar(250)                             null comment '预约提示',
    appointment_result_notice         text                                     null comment '预约结果提示',
    appointment_notify_contact        varchar(100)                             null comment '预约后联系人',
    notice_template_id                varchar(50)                              null comment '预约成功通知模板ID',
    appointment_rule                  text                                     null comment '预约规则',
    appointment_guide                 text                                     null comment '预约指南',
    selected_payments                 varchar(200)                             null comment '已选支付方式',
    all_department_background_color   varchar(20)                              null comment '全部科室背景色',
    update_department_exclude_columns text                                     null comment '更新科室排除字段',
    update_doctor_exclude_columns     text                                     null comment '更新医生排除字段',
    create_doctor_from_his tinyint default 1 not null comment '是否从HIS创建医生：0，否；1，是；',
    create_time                       datetime(3) default CURRENT_TIMESTAMP(3) not null,
    update_time                       datetime(3)                              null on update CURRENT_TIMESTAMP(3),
    constraint uidx_limit_single_setting
        unique (tenant_id, hospital_id, hospital_area_id)
)
    comment '医院预约挂号规则配置';