create table t_hospital
(
    id                             bigint auto_increment comment '全局唯一标识'
        primary key,
    parent_id                      bigint       default 0                    not null comment '父节点 ID',
    tenant_id                      bigint                                    not null comment '租户 ID',
    hospital_type_tag              int          default 0                    null comment '医院类型，0:医院，1:院区',
    hospital_code                  varchar(20)                               null comment '医院编码',
    name                           varchar(40)                               null comment '名称',
    level_dict_type                varchar(100) default 'hospital_level'     null comment '医院级别，字典类型',
    level_dict_value               varchar(300)                              null comment '医院级别，字典值',
    level_dict_label               varchar(100)                              null comment '医院级别，字典标签',
    category                       varchar(100) default '0'                  null comment '医院分类，必须单字符串[0-9][a-z][A-Z]：0，常规；1，疫苗预约；2，特需门诊；3，多学科；9，其他；',
    property                       int                                       null comment '医院性质：0，公立；1，民营；10，其他；',
    introduction                   text                                      null comment '医院简介',
    announcement                   text                                      null comment '公告通知',
    environment                    text                                      null comment '环境展示',
    address_id                     bigint                                    null comment '医院地址',
    appointment_caution_dict_type  varchar(100)                              null comment '医院医院挂号警告，字典类型',
    appointment_caution_dict_label varchar(200)                              null comment '医院医院挂号警告，通过逗号隔离字典标签',
    contact_person                 varchar(30)                               null comment '联系人',
    contact_phone_number           varchar(100)                              null comment '联系人手机号',
    status                         int          default 0                    null comment '0:启用，1:开发中，2:维护，3:禁用',
    tag_dict_type                  varchar(100)                              null comment '医院标签,字典类型',
    tag_dict_label                 varchar(300)                              null comment '医院标签,通过逗号隔离字典标签',
    tag_dict_value                 varchar(300)                              null comment '医院标签,通过逗号隔离字典值',
    display                        int          default 1                    not null comment '是否显示在医院列表：0，不显示；1，显示',
    display_guide                  int          default 1                    not null comment '是否显示去院导航：0，不显示；1，显示',
    display_floor                  int          default 1                    not null comment '是否显示楼层分布：0，不显示；1，显示',
    display_sort                   int          default 255                  null comment '显示排序',
    map_keyword                    varchar(50)                               null comment '地图关键词',
    stop_service_begin_time        time                                      null comment '停诊开始时间',
    stop_service_end_time          time                                      null comment '停诊结束时间',
    department_layer               int          default 1                    null comment '科室层级，1：一级科室，2：二级科室',
    logo                           varchar(255)                              null comment 'Logo',
    pictures                       varchar(600)                              null comment '医院图片，以逗号隔离图片URL',
    appointment_scheduling_time    text                                      null comment '放号时间',
    create_time                    datetime(3)  default CURRENT_TIMESTAMP(3) not null,
    update_time                    datetime(3)                               null on update CURRENT_TIMESTAMP(3)
)
    comment 'Hospital';

insert into t_hospital
(id, parent_id, tenant_id, hospital_type_tag, hospital_code, name, level_dict_type, level_dict_value, level_dict_label,
 status)
values (1, 0, 1, 0, null, '云南省第一人民医院', 'hospital_level', '三甲', '三甲', 0);
insert into t_hospital
(id, parent_id, tenant_id, hospital_type_tag, hospital_code, name, level_dict_type, level_dict_value, level_dict_label,
 address_id, status)
values (2, 1, 1, 1, '871900', '云南省第一人民医院(昆华医院)', 'hospital_level', '三甲', '三甲', 1, 0),
       (3, 1, 1, 1, '871140', '云南省第一人民医院新昆华医院', 'hospital_level', '三甲', '三甲', 2, 0);

insert into t_hospital
(id, parent_id, tenant_id, hospital_type_tag, hospital_code, name, level_dict_type, level_dict_value, level_dict_label,
 status)
values (4, 0, 2, 0, null, '云南省第二人民医院', 'hospital_level', '三甲', '三甲', 0);
insert into t_hospital
(id, parent_id, tenant_id, hospital_type_tag, hospital_code, name, level_dict_type, level_dict_value, level_dict_label,
 address_id, status)
values (5, 4, 2, 1, '871045', '云南大学附属医院(云南省第二人民医院)', 'hospital_level', '三甲', '三甲', 1, 0),
       (6, 4, 2, 1, '871923', '云南大学附属医院(云南省第二人民医院)-疫苗', 'hospital_level', '三甲', '三甲', 1, 0),
       (7, 4, 2, 1, '871072', '云南大学附属医院(云南省第二人民医院)-多学科', 'hospital_level', '三甲', '三甲', 2, 0);