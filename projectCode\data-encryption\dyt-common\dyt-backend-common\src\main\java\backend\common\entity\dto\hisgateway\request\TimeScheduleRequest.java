package backend.common.entity.dto.hisgateway.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class TimeScheduleRequest {

    @JsonProperty("start_date")
    private Long startDate;

    @JsonProperty("end_date")
    private Long endDate;

    @JsonProperty("registration_date")
    private Long registrationDate;

    @JsonProperty("division_code")
    private String divisionCode;

    // 滇医通自定义唯一标识
    @JsonProperty("hospital_code")
    private String hospitalCode;

    // code 是 his 的唯一标识
    @JsonProperty("department_code")
    private String departmentCode;

    // code 是 his 的唯一标识
    @JsonProperty("doctor_code")
    private String doctorCode;

    // 排班唯一标识
    @JsonProperty("schedule_id")
    private String scheduleId;

    @JsonProperty("time_type")
    private Integer timeType;

    @JsonProperty("resource_type")
    private String resourceType;
}
