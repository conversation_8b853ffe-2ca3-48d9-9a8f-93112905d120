package com.ynhdkc.tenant.service.customer;


import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.entity.Vaccine;
import com.ynhdkc.tenant.entity.VaccineCategory;
import com.ynhdkc.tenant.model.*;

import java.util.List;

public interface CustomerVaccineService {

    CustomerVaccineCategoryPageVo getVaccineCategoryPage(CustomerVaccineCategoryPageReqDto request);

    List<CustomerVaccineOrganizationVo> getVaccineOrganizationList(CustomerVaccineOrganizationListReqDto request);

    CustomerVaccineSearchRespVo vaccineSearch(String keyword, Double longitude, Double latitude);

    List<CustomerVaccineVo> getVaccineList(CustomerVaccineListReqDto request);

    static CustomerVaccineCategoryVo toCustomerVaccineCategoryVo(VaccineCategory vaccineCategory) {
        CustomerVaccineCategoryVo vo = new CustomerVaccineCategoryVo();
        vo.setId(vaccineCategory.getId());
        vo.setName(vaccineCategory.getName());
        vo.setSort(vaccineCategory.getSort());
        vo.setLogo(vaccineCategory.getLogo());
        vo.setIsHot(vaccineCategory.getIsHot());
        vo.setHotSort(vaccineCategory.getHotSort());
        return vo;
    }

    static CustomerVaccineOrganizationVo hospitalToVaccineOrganizationVo(Hospital hospital) {
        CustomerVaccineOrganizationVo vo = new CustomerVaccineOrganizationVo();
        vo.setHospitalId(hospital.getParentId());
        vo.setHospitalAreaId(hospital.getId());
        vo.setHospitalCode(hospital.getHospitalCode());
        vo.setHospitalName(hospital.getName());
        vo.setLogo(hospital.getLogo());
        vo.setAddressId(hospital.getAddressId());

        return vo;
    }

    static CustomerVaccineVo toCustomerVaccineVo(Vaccine vaccine) {
        CustomerVaccineVo vo = new CustomerVaccineVo();
        vo.setId(vaccine.getId());
        vo.setCategoryId(vaccine.getVaccineCategoryId());
        vo.setHospitalId(vaccine.getHospitalId());
        vo.setHospitalAreaId(vaccine.getHospitalAreaId());
        vo.setDepartmentId(vaccine.getDepartmentId());
        vo.setDoctorId(vaccine.getDoctorId());
        vo.setSort(vaccine.getSort());
        vo.setTips(vaccine.getTips());
        vo.setRemark(vaccine.getRemark());
        return vo;
    }
}
