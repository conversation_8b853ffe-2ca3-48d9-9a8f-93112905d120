package backend.common.entity.dto.hisgateway.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class PreRegistrationRequest {

    @JsonProperty("hospital_code")
    private String hospitalCode;

    @JsonProperty("medical_record_number")
    private String medicalRecordNumber;

    @JsonProperty("medical_card_expansion")
    private String medicalCardExpansion;

    @JsonProperty("schedule_id")
    private String scheduleId;

    private String resource;

    @JsonProperty("visiting_serial_number")
    private String visitingSerialNumber;

    @JsonProperty("start_date")
    private Long startDate;

    @JsonProperty("end_date")
    private Long endDate;

    @JsonProperty("time_type")
    private Integer timeType;

    @JsonProperty("time_type_desc")
    private String timeTypeDesc;

    @JsonProperty("order_number")
    private String orderNumber;

    @JsonProperty("registration_amt")
    private Double registrationAmt;

    @JsonProperty("treat_amt")
    private Double treatAmt;

    @JsonProperty("expert_amt")
    private Double expertAmt;

    @JsonProperty("total_amt")
    private Double totalAmt;

    private Double amt;

    @JsonProperty("visiting_date")
    private Long visitingDate;

    @JsonProperty("patient_name")
    private String patientName;

    @JsonProperty("patient_sex")
    private Integer patientSex;

    @JsonProperty("patient_birthday")
    private String patientBirthday;

    @JsonProperty("patient_phone")
    private String patientPhone;

    @JsonProperty("id_card_number")
    private String idCardNumber;

    @JsonProperty("id_card_type")
    private Integer idCardType;

    @JsonProperty("doctor_code")
    private String doctorCode;

    @JsonProperty("department_code")
    private String departmentCode;

    @JsonProperty("order_create_time")
    private String orderCreateTime;

    @JsonProperty("start_time")
    private Long startTime;

    @JsonProperty("end_time")
    private Long endTime;

    @JsonProperty("start_time_text")
    private String startTimeText;

    @JsonProperty("end_time_text")
    private String endTimeText;

}
