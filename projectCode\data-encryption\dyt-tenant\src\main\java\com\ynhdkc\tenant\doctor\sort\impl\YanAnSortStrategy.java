package com.ynhdkc.tenant.doctor.sort.impl;

import com.ynhdkc.tenant.doctor.sort.strategy.AbstractDoctorSortStrategy;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.model.CustomerGroupedDoctorDetailVo;
import com.ynhdkc.tenant.tool.DoctorSortUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @Author: zhang<PERSON>ngyun
 * @Date: 2025/5/15 10:37
 * @Description:
 **/
@Component
public class YanAnSortStrategy extends AbstractDoctorSortStrategy {

    private static final Set<String> HOSPITAL_CODES = Collections.unmodifiableSet(new HashSet<>(Collections.singletonList("871093")));

    public YanAnSortStrategy() {
        super(HOSPITAL_CODES);
    }

    @Override
    public void sort(List<Doctor> doctors) {
        DoctorSortUtils.levelDescAndNameAsc(doctors);
    }

    @Override
    public void sortGroupVo(List<CustomerGroupedDoctorDetailVo> doctorGroupVos) {
        DoctorSortUtils.levelDescAndNameAscGroupDoc(doctorGroupVos);
    }
}
