package backend.security.entity;

import backend.common.domain.tenant.DepartmentEntity;
import backend.common.domain.tenant.HospitalEntity;
import backend.common.domain.tenant.TenantEntity;
import backend.common.domain.tenant.TenantUserStructureEntity;
import backend.common.util.JsonUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentSkipListSet;
import java.util.stream.Collectors;

/**
 * 组织架构树，每个节点包括了当前节点的用户列表。
 * 用户权限由大到小，即：超级管理员 > 租户管理员 > 医院管理员 > 院区管理员 > 科室成员。
 * 用户出现在某一节点，则代表该用户拥有该节点所有子节点的权限。
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-23 15:13
 */
@Data
public class OrganizationStructureTree implements Serializable {
    public OrganizationStructureTree() {
        this.tenants = new ConcurrentHashMap<>();
        this.adminUsers = new ConcurrentSkipListSet<>();
    }

    public <T extends TenantEntity, H extends HospitalEntity, D extends DepartmentEntity, S extends TenantUserStructureEntity>
    OrganizationStructureTree(Collection<T> tenantIds,
                              Collection<H> hospitalIds,
                              Collection<H> hospitalAreaIds,
                              Collection<D> departmentIds) {
        this();
        initializeStructure(tenantIds, hospitalIds, hospitalAreaIds, departmentIds);
    }

    @JsonIgnore
    public OrganizationStructureTree deepCopyTree() {
        return JsonUtil.deserializeObject(JsonUtil.serializeObject(this), OrganizationStructureTree.class);
    }


    /**
     * 超级管理员用户ID列表
     */
    @Setter(AccessLevel.NONE)
    private Set<Long> adminUsers;
    /**
     * 所有租户组织架构列表
     */
    @Setter(AccessLevel.NONE)
    private Map<Long, TenantStructure> tenants;

    /**
     * 【警告：切勿用来做写入数据权限控制，需要数据权限控制请使用 {@link TenantUserStructureTree#hasPrivilege(Long, Long, Long, Long)} 】
     * 查询用户组织架构归属。
     * 如：用户ID=1，是A租户B医院C院区D科室成员。那么该用户的归属定义为：
     * tenant={A}
     * hospital={B}
     * hospitalArea={C}
     * department={D}。
     *
     * @param userId 租户用户ID
     * @return 当前用户组织架构归属
     */
    @JsonIgnore
    public TenantUserAscription getUserAscriptionByUserId(Long userId) {
        TenantUserAscription ascription = new TenantUserAscription(userId);
        /* 是否为超管 */
        if (adminUsers.contains(userId)) {
            ascription.addAllTenant(getAllTenantId());
            ascription.addAllHospital(getAllHospitalId());
            ascription.addAllHospitalArea(getAllHospitalAreaId());
            ascription.addAllDepartment(getAllDepartmentId());
            return ascription;
        }
        tenants.forEach((tenantId, tenantNode) -> {
            /* 是否为租户管理员 */
            if (tenantNode.getUsers().contains(userId)) {
                ascription.addTenant(tenantNode.getId());

                ascription.addAllHospital(tenantNode.getAllHospitalId());
                ascription.addHospital(TenantUserStructureEntity.ADMIN_VALUE);

                ascription.addAllHospitalArea(tenantNode.getAllHospitalAreaId());
                ascription.addHospitalArea(TenantUserStructureEntity.ADMIN_VALUE);

                ascription.addAllDepartment(tenantNode.getAllDepartmentId());
                ascription.addDepartment(TenantUserStructureEntity.ADMIN_VALUE);
                return;
            }
            /* 非管理员，则遍历医院 */
            tenantNode.getHospitals().forEach((hospitalId, hospitalNode) -> {
                /* 是否医院管理员 */
                if (hospitalNode.getUsers().contains(userId)) {
                    ascription.addTenant(hospitalNode.getTenantId());

                    ascription.addHospital(hospitalNode.getId());

                    ascription.addAllHospitalArea(hospitalNode.getAllHospitalAreaId());
                    ascription.addHospitalArea(TenantUserStructureEntity.ADMIN_VALUE);

                    ascription.addAllDepartment(hospitalNode.getAllDepartmentId());
                    ascription.addDepartment(TenantUserStructureEntity.ADMIN_VALUE);
                    return;
                }
                hospitalNode.getHospitalAreas().forEach((hospitalAreaId, hospitalAreaNode) -> {
                    /* 是否院区管理员 */
                    if (hospitalAreaNode.getUsers().contains(userId)) {
                        ascription.addTenant(hospitalAreaNode.getTenantId());

                        ascription.addHospital(hospitalAreaNode.getHospitalId());

                        ascription.addHospitalArea(hospitalAreaNode.getId());

                        ascription.addAllDepartment(hospitalAreaNode.getAllDepartmentId());
                        ascription.addDepartment(TenantUserStructureEntity.ADMIN_VALUE);
                        return;
                    }
                    hospitalAreaNode.getDepartments().forEach((departmentId, departmentNode) -> {
                        /* 是否科室管理员 */
                        if (departmentNode.getUsers().contains(userId)) {
                            ascription.addTenant(departmentNode.getTenantId());
                            ascription.addHospital(departmentNode.getHospitalId());
                            ascription.addHospitalArea(departmentNode.getHospitalAreaId());
                            ascription.addDepartment(departmentNode.getId());
                            return;
                        }
                        /* 是否科室成员 */
                        if (departmentNode.getMembers().contains(userId)) {
                            ascription.addTenant(departmentNode.getTenantId());
                            ascription.addHospital(departmentNode.getHospitalId());
                            ascription.addHospitalArea(departmentNode.getHospitalAreaId());
                            ascription.addDepartment(departmentNode.getId());
                        }
                    });
                });
            });
        });
        return ascription;
    }

    /**
     * 查询用户数据权限树
     *
     * @param userId 租户用户ID
     * @return 用户数据权限树
     */
    @JsonIgnore
    public TenantUserStructureTree getUserPrivilegeTreeByUserId(Long userId, final Boolean includingChild) {
        TenantUserStructureTree privilegeTree = new TenantUserStructureTree(userId);
        /* 是否为超管 */
        if (this.adminUsers.contains(userId)) {
            privilegeTree.setSuperAdmin(true);
            if (Boolean.TRUE.equals(includingChild)) {
                getAllTenantNode().forEach(tenantStructure -> privilegeTree.addTenant(tenantStructure.getId(), true));
                getAllHospitalNode().forEach(hospitalStructure -> privilegeTree.addHospital(hospitalStructure.getTenantId(), hospitalStructure.getId(), true));
                getAllHospitalAreaNode().forEach(hospitalAreaStructure -> privilegeTree.addHospitalArea(hospitalAreaStructure.getTenantId(), hospitalAreaStructure.getHospitalId(), hospitalAreaStructure.getId(), true));
                getAllDepartmentNode().forEach(node -> privilegeTree.addDepartment(node.getTenantId(), node.getHospitalId(), node.getHospitalAreaId(), node.getId(), true));
            }
            return privilegeTree;
        }

        tenants.forEach((tenantId, tenantNode) -> {
            /* 是否为租户管理员 */
            if (tenantNode.getUsers().contains(userId)) {
                privilegeTree.addTenant(tenantId, true);
                if (Boolean.TRUE.equals(includingChild)) {
                    tenantNode.getAllHospitalNode().forEach(hospitalStructure -> privilegeTree.addHospital(tenantId, hospitalStructure.getId(), true));
                    tenantNode.getAllHospitalAreaNode().forEach(hospitalAreaStructure -> privilegeTree.addHospitalArea(tenantId, hospitalAreaStructure.getHospitalId(), hospitalAreaStructure.getId(), true));
                    tenantNode.getAllDepartmentNode().forEach(node -> privilegeTree.addDepartment(tenantId, node.getHospitalId(), node.getHospitalAreaId(), node.getId(), true));
                }
                return;
            }
            /* 非管理员，则遍历医院 */
            tenantNode.getHospitals().forEach((hospitalId, hospitalNode) -> {
                /* 是否是医院管理员 */
                if (hospitalNode.getUsers().contains(userId)) {
                    privilegeTree.addHospital(tenantId, hospitalId, true);
                    if (Boolean.TRUE.equals(includingChild)) {
                        hospitalNode.getAllHospitalAreaNode().forEach(hospitalAreaStructure -> privilegeTree.addHospitalArea(tenantId, hospitalId, hospitalAreaStructure.getId(), true));
                        hospitalNode.getAllDepartmentNode().forEach(node -> privilegeTree.addDepartment(tenantId, hospitalId, node.getHospitalAreaId(), node.getId(), true));
                    }
                    return;
                }
                /* 非管理员，则遍历院区 */
                hospitalNode.getHospitalAreas().forEach((hospitalAreaId, hospitalAreaNode) -> {
                    /* 是否是院区管理员 */
                    if (hospitalAreaNode.getUsers().contains(userId)) {
                        privilegeTree.addHospitalArea(tenantId, hospitalId, hospitalAreaId, true);
                        if (Boolean.TRUE.equals(includingChild)) {
                            for (DepartmentStructure node : hospitalAreaNode.getAllDepartmentNode()) {
                                privilegeTree.addDepartment(tenantId, hospitalId, hospitalAreaId, node.getId(), true);
                            }
                        }
                        return;
                    }
                    /* 非管理员，则遍历科室 */
                    hospitalAreaNode.getDepartments().forEach((departmentId, departmentNode) -> {
                        /* 是否是科室管理员 */
                        if (departmentNode.getUsers().contains(userId)) {
                            privilegeTree.addDepartment(tenantId, hospitalId, hospitalAreaId, departmentId, true);
                            return;
                        }
                        /* 是否是科室成员 */
                        if (departmentNode.getMembers().contains(userId)) {
                            privilegeTree.addDepartment(tenantId, hospitalId, hospitalAreaId, departmentId, false);
                        }
                    });
                });
            });
        });
        return privilegeTree;
    }

    @JsonIgnore
    public Set<Long> getAllTenantId() {
        return tenants.keySet();
    }

    @JsonIgnore
    public List<TenantStructure> getAllTenantNode() {
        return new ArrayList<>(tenants.values());
    }

    @JsonIgnore
    public Set<Long> getAllHospitalId() {
        return tenants.values().stream()
                .flatMap(tenantNode -> tenantNode.getAllHospitalId().stream())
                .collect(Collectors.toSet());
    }

    @JsonIgnore
    public List<HospitalStructure> getAllHospitalNode() {
        return tenants.values().stream()
                .flatMap(tenantNode -> tenantNode.getAllHospitalNode().stream())
                .collect(Collectors.toList());
    }

    @JsonIgnore
    public Set<Long> getAllHospitalAreaId() {
        return tenants.values().stream()
                .flatMap(tenantNode -> tenantNode.getAllHospitalAreaId().stream())
                .collect(Collectors.toSet());
    }

    @JsonIgnore
    public List<HospitalAreaStructure> getAllHospitalAreaNode() {
        return tenants.values().stream()
                .flatMap(tenantNode -> tenantNode.getAllHospitalAreaNode().stream())
                .collect(Collectors.toList());
    }

    @JsonIgnore
    public List<DepartmentStructure> getAllDepartmentNode() {
        return tenants.values().stream()
                .flatMap(tenantNode -> tenantNode.getAllDepartmentNode().stream())
                .collect(Collectors.toList());
    }

    @JsonIgnore
    public Set<Long> getAllDepartmentId() {
        return tenants.values().stream()
                .flatMap(tenantNode -> tenantNode.getAllDepartmentId().stream())
                .collect(Collectors.toSet());
    }

    @JsonIgnore
    private <T extends TenantEntity, H extends HospitalEntity, D extends DepartmentEntity>
    void initializeStructure(Collection<T> tenants, Collection<H> hospitals, Collection<H> hospitalAreas, Collection<D> departments) {
        tenants.forEach(tenant -> createTenantStructureIfAbsent(tenant.getId()));
        hospitals.forEach(hospital -> {
            TenantStructure tenantStructure = this.tenants.get(hospital.getTenantId());
            if (null == tenantStructure) {
                return;
            }
            if (!tenantStructure.hospitals.containsKey(hospital.getId())) {
                tenantStructure.hospitals.put(hospital.getId(), new HospitalStructure(hospital.getId(), hospital.getTenantId()));
            }
        });
        hospitalAreas.forEach(hospitalArea -> {
            TenantStructure tenantStructure = this.tenants.get(hospitalArea.getTenantId());
            if (null == tenantStructure) {
                return;
            }
            HospitalStructure hospitalStructure = tenantStructure.hospitals.get(hospitalArea.getParentId());
            if (null == hospitalStructure) {
                return;
            }
            if (!hospitalStructure.hospitalAreas.containsKey(hospitalArea.getId())) {
                hospitalStructure.hospitalAreas.put(hospitalArea.getId(), new HospitalAreaStructure(hospitalArea.getId(), hospitalArea.getTenantId(), hospitalArea.getParentId()));
            }
        });
        departments.forEach(department -> {
            TenantStructure tenantStructure = this.tenants.get(department.getTenantId());
            if (null == tenantStructure) {
                return;
            }
            HospitalStructure hospitalStructure = tenantStructure.hospitals.get(department.getHospitalId());
            if (null == hospitalStructure) {
                return;
            }
            HospitalAreaStructure hospitalAreaStructure = hospitalStructure.hospitalAreas.get(department.getHospitalAreaId());
            if (null == hospitalAreaStructure) {
                return;
            }
            if (!hospitalAreaStructure.departments.containsKey(department.getId())) {
                hospitalAreaStructure.departments.put(department.getId(), new DepartmentStructure(department.getId(), department.getTenantId(), department.getHospitalId(), department.getHospitalAreaId()));
            }
        });
    }

    @JsonIgnore
    public <T extends TenantUserStructureEntity> void initializeUsers(Collection<T> entities) {
        entities.forEach(entity -> {
            if (null == entity) {
                return;
            }
            /* 科室成员 */
            if (!entity.getDepartmentId().equals(TenantUserStructureEntity.ADMIN_VALUE)) {
                userJoinDepartment(entity, entity.getDepartmentAdmin());
                return;
            }
            /* 院区管理员 */
            if (!entity.getHospitalAreaId().equals(TenantUserStructureEntity.ADMIN_VALUE)) {
                userJoinHospitalArea(entity);
                return;
            }
            /* 医院管理员 */
            if (!entity.getHospitalId().equals(TenantUserStructureEntity.ADMIN_VALUE)) {
                userJoinHospital(entity);
                return;
            }
            /* 租户管理员 */
            if (!entity.getTenantId().equals(TenantUserStructureEntity.ADMIN_VALUE)) {
                userJoinTenant(entity);
                return;
            }
            /* 超管 */
            adminUsers.add(entity.getUserId());
        });
    }

    @JsonIgnore
    private void userJoinTenant(TenantUserStructureEntity entity) {
        TenantStructure tenantStructure = this.tenants.get(entity.getTenantId());
        if (null == tenantStructure) {
            return;
        }
        tenantStructure.addUser(entity.getUserId());
    }

    @JsonIgnore
    private void userJoinHospital(TenantUserStructureEntity entity) {
        TenantStructure tenantStructure = this.tenants.get(entity.getTenantId());
        if (null == tenantStructure) {
            return;
        }
        HospitalStructure hospitalStructure = tenantStructure.hospitals.get(entity.getHospitalId());
        if (null == hospitalStructure) {
            return;
        }
        hospitalStructure.addUser(entity.getUserId());
    }

    @JsonIgnore
    private void userJoinHospitalArea(TenantUserStructureEntity entity) {
        TenantStructure tenantStructure = this.tenants.get(entity.getTenantId());
        if (null == tenantStructure) {
            return;
        }
        HospitalStructure hospitalStructure = tenantStructure.hospitals.get(entity.getHospitalId());
        if (null == hospitalStructure) {
            return;
        }
        HospitalAreaStructure hospitalAreaStructure = hospitalStructure.hospitalAreas.get(entity.getHospitalAreaId());
        if (null == hospitalAreaStructure) {
            return;
        }
        hospitalAreaStructure.addUser(entity.getUserId());
    }

    @JsonIgnore
    private void userJoinDepartment(TenantUserStructureEntity entity, Boolean departmentAdmin) {
        TenantStructure tenantStructure = this.tenants.get(entity.getTenantId());
        if (null == tenantStructure) {
            return;
        }
        HospitalStructure hospitalStructure = tenantStructure.hospitals.get(entity.getHospitalId());
        if (null == hospitalStructure) {
            return;
        }
        HospitalAreaStructure hospitalAreaStructure = hospitalStructure.hospitalAreas.get(entity.getHospitalAreaId());
        if (null == hospitalAreaStructure) {
            return;
        }
        DepartmentStructure departmentStructure = hospitalAreaStructure.departments.get(entity.getDepartmentId());
        if (null == departmentStructure) {
            return;
        }
        if (Boolean.TRUE.equals(departmentAdmin)) {
            departmentStructure.addUser(entity.getUserId());
        } else {
            departmentStructure.addMember(entity.getUserId());
        }
    }

    public void setAdminUsers(Set<Long> adminUsers) {
        if (adminUsers instanceof ConcurrentSkipListSet) {
            this.adminUsers = adminUsers;
            return;
        }
        this.adminUsers = new ConcurrentSkipListSet<>(adminUsers);
    }

    public void setTenants(Map<Long, TenantStructure> tenants) {
        if (tenants instanceof ConcurrentHashMap) {
            this.tenants = tenants;
            return;
        }
        this.tenants = new ConcurrentHashMap<>(tenants);
    }

    @JsonIgnore
    private void createTenantStructureIfAbsent(Long tenantId) {
        TenantStructure tenantStructure = this.tenants.get(tenantId);
        if (null == tenantStructure) {
            tenantStructure = new TenantStructure(tenantId);
            this.tenants.put(tenantId, tenantStructure);
        }
    }

    @JsonIgnore
    public List<TenantUserStructureTree> getAllUserPrivilegeTree(Collection<Long> userIds, Boolean includingChild) {
        List<TenantUserStructureTree> result = new ArrayList<>();
        if (null == userIds || userIds.isEmpty()) {
            return result;
        }
        userIds.forEach(userId -> result.add(this.getUserPrivilegeTreeByUserId(userId, includingChild)));
        return result;
    }

    @JsonIgnore
    public List<TenantUserAscription> getAllUserAscription(Collection<Long> userIds) {
        List<TenantUserAscription> result = new ArrayList<>();
        if (null == userIds || userIds.isEmpty()) {
            return result;
        }
        userIds.forEach(userId -> result.add(this.getUserAscriptionByUserId(userId)));
        return result;
    }

    /**
     * 租户组织架构
     */
    @EqualsAndHashCode(callSuper = true)
    public static class TenantStructure extends OrganizationStructure {
        public TenantStructure() {
            super();
            this.hospitals = new ConcurrentHashMap<>();
        }

        public TenantStructure(Long id) {
            super(id);
            this.hospitals = new ConcurrentHashMap<>();
        }

        /**
         * 当前租户下医院
         */
        @Getter
        private Map<Long, HospitalStructure> hospitals;

        @JsonIgnore
        public Set<Long> getAllHospitalId() {
            return hospitals.keySet();
        }

        @JsonIgnore
        public Set<Long> getAllHospitalAreaId() {
            return hospitals.values().stream()
                    .flatMap(hospitalNode -> hospitalNode.getAllHospitalAreaId().stream())
                    .collect(Collectors.toSet());
        }

        @JsonIgnore
        public Set<Long> getAllDepartmentId() {
            return hospitals.values().stream()
                    .flatMap(hospitalNode -> hospitalNode.getAllDepartmentId().stream())
                    .collect(Collectors.toSet());
        }

        public void setHospitals(Map<Long, HospitalStructure> hospitals) {
            this.hospitals = new ConcurrentHashMap<>(hospitals);
        }

        @JsonIgnore
        public List<HospitalStructure> getAllHospitalNode() {
            return new ArrayList<>(hospitals.values());
        }

        @JsonIgnore
        public List<HospitalAreaStructure> getAllHospitalAreaNode() {
            return hospitals.values().stream()
                    .flatMap(hospitalNode -> hospitalNode.getAllHospitalAreaNode().stream())
                    .collect(Collectors.toList());
        }

        @JsonIgnore
        public List<DepartmentStructure> getAllDepartmentNode() {
            return hospitals.values().stream()
                    .flatMap(hospitalNode -> hospitalNode.getAllDepartmentNode().stream())
                    .collect(Collectors.toList());
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class HospitalStructure extends OrganizationStructure {
        public HospitalStructure() {
            super();
            this.hospitalAreas = new ConcurrentHashMap<>();
        }

        public HospitalStructure(Long id, Long tenantId) {
            super(id);
            this.tenantId = tenantId;
            this.hospitalAreas = new ConcurrentHashMap<>();
        }

        private Long tenantId;
        @Setter(AccessLevel.NONE)
        private Map<Long, HospitalAreaStructure> hospitalAreas;

        @JsonIgnore
        public Set<Long> getAllHospitalAreaId() {
            return hospitalAreas.keySet();
        }

        @JsonIgnore
        public Set<Long> getAllDepartmentId() {
            return hospitalAreas.values().stream()
                    .flatMap(hospitalAreaNode -> hospitalAreaNode.getAllDepartmentId().stream())
                    .collect(Collectors.toSet());
        }

        public void setHospitalAreas(Map<Long, HospitalAreaStructure> hospitalAreas) {
            this.hospitalAreas = new ConcurrentHashMap<>(hospitalAreas);
        }

        @JsonIgnore
        public List<HospitalAreaStructure> getAllHospitalAreaNode() {
            return new ArrayList<>(hospitalAreas.values());
        }

        @JsonIgnore
        public List<DepartmentStructure> getAllDepartmentNode() {
            return hospitalAreas.values().stream()
                    .flatMap(hospitalAreaNode -> hospitalAreaNode.getAllDepartmentNode().stream())
                    .collect(Collectors.toList());
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class HospitalAreaStructure extends OrganizationStructure {
        public HospitalAreaStructure() {
            super();
            this.departments = new ConcurrentHashMap<>();
        }

        public HospitalAreaStructure(Long id, Long tenantId, Long hospitalId) {
            super(id);
            this.tenantId = tenantId;
            this.hospitalId = hospitalId;
            this.departments = new ConcurrentHashMap<>();
        }

        private Long tenantId;
        private Long hospitalId;
        @Setter(AccessLevel.NONE)
        private Map<Long, DepartmentStructure> departments;

        @JsonIgnore
        public Set<Long> getAllDepartmentId() {
            return departments.keySet();
        }

        public void setDepartments(Map<Long, DepartmentStructure> departments) {
            this.departments = new ConcurrentHashMap<>(departments);
        }

        @JsonIgnore
        public List<DepartmentStructure> getAllDepartmentNode() {
            return new ArrayList<>(departments.values());
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class DepartmentStructure extends OrganizationStructure {
        public DepartmentStructure() {
            super();
            this.members = new ConcurrentSkipListSet<>();
        }

        public DepartmentStructure(Long id, Long tenantId, Long hospitalId, Long hospitalAreaId) {
            super(id);
            this.tenantId = tenantId;
            this.hospitalId = hospitalId;
            this.hospitalAreaId = hospitalAreaId;
            this.members = new ConcurrentSkipListSet<>();
        }

        private Long tenantId;
        private Long hospitalId;
        private Long hospitalAreaId;

        /**
         * 科室成员
         */
        private Set<Long> members;

        public void addMember(Long userId) {
            this.members.add(userId);
        }
    }

    /**
     * 组织架构
     */
    @Data
    public static class OrganizationStructure {
        public OrganizationStructure() {
            this.users = new ConcurrentSkipListSet<>();
        }

        public OrganizationStructure(Long id) {
            this();
            this.id = id;
        }

        /**
         * 当前组织节点ID
         */
        private Long id;
        /**
         * 当前组织下用户ID列表
         */
        private Set<Long> users;

        public void addUser(Long userId) {
            this.users.add(userId);
        }
    }
}
