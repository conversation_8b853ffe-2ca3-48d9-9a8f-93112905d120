CREATE TABLE t_hospital_detail_page_cube_module
(
    id                BIGINT          NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    tenant_id         BIGINT          NOT NULL COMMENT '租户id',
    hospital_id       BIGINT          NOT NULL COMMENT '医院id',
    hospital_area_id  bigint          not null comment '院区id',
    hospital_code     varchar(20)     null comment '医院编码',
    title             VARCHAR(30)     NULL COMMENT '模块标题',
    title_status      int default 0   not null comment '标题状态：0，展示；1，禁用；',
    cube_display_type INT default 0   NOT NULL COMMENT '模块展示类型',
    sort              INT default 255 NOT NULL COMMENT '排序',
    channels varchar(100) NOT NULL DEFAULT '' COMMENT '渠道',
    create_time       DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time       DATETIME        NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT '医院详情页cube模块表';

CREATE INDEX idx_tenant_id ON t_hospital_detail_page_cube_module (tenant_id);
CREATE INDEX idx_hospital_id ON t_hospital_detail_page_cube_module (hospital_id);
CREATE INDEX idx_cube_display_type ON t_hospital_detail_page_cube_module (cube_display_type);
