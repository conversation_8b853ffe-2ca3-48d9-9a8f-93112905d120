package com.ynhdkc.tenant.api.customer;

import com.ynhdkc.tenant.handler.CustomerLinkConvertApi;
import com.ynhdkc.tenant.link.convert.Constants;
import com.ynhdkc.tenant.link.convert.ILinkConvert;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.ynhdkc.tenant.util.UrlUtil.getHospitalCodeFromUrl;

@RestController
@Api(tags = "链接转换")
@RequiredArgsConstructor
@Slf4j
public class CustomerLinkConvertController implements CustomerLinkConvertApi {

    private final List<ILinkConvert> linkConvertList;

    private String getOldDomain(String source) {
        Pattern pattern = Pattern.compile("https://[^/]+");
        Matcher matcher = pattern.matcher(source);
        if (matcher.find()) {
            return matcher.group();
        }
        return Constants.DEFAULT_URL;
    }


    @Override
    public ResponseEntity<String> getConvertLink(String source) {
        if (!StringUtils.hasText(source)) {
            return ResponseEntity.ok(Constants.DEFAULT_URL);
        }

        try {
            source = URLDecoder.decode(source, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error("url decode error: {}", e.getMessage());
        }

        String hospitalCode = getHospitalCodeFromUrl(source);
        if (!StringUtils.hasText(hospitalCode) || !Constants.APP_V3_HOSPITAL_CODE.contains(hospitalCode)) {
            return ResponseEntity.ok(replaceDomain(source, getOldDomain(source)));
        }

        for (ILinkConvert linkConvert : linkConvertList) {
            if (linkConvert.isSupport(source)) {
                return ResponseEntity.ok(linkConvert.getConvertLink(source));
            }
        }

        return ResponseEntity.ok(Constants.DEFAULT_URL);
    }

    private String replaceDomain(String source, String oldDomain) {
        return source.replace(oldDomain, "https://appv3.ynhdkc.com");
    }

}
