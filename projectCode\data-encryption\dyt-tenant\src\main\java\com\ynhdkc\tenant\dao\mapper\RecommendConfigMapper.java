package com.ynhdkc.tenant.dao.mapper;

import com.ynhdkc.tenant.entity.RecommendConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.util.StringUtils;

import java.util.List;

@Mapper
public interface RecommendConfigMapper extends BaseMapper<RecommendConfig> {
    default List<RecommendConfig> selectDoctorRecommendList(String dictType, String dictLabel, String doctorId) {
        return selectByExample(RecommendConfig.class, helper -> {
            helper.defGroup(condition -> {
                condition.andEqualTo(RecommendConfig::getBizType, dictType);
                condition.andEqualTo(RecommendConfig::getDataType, dictLabel);
                if (StringUtils.hasText(doctorId)) {
                    condition.andEqualTo(RecommendConfig::getRedirectUrl, doctorId);
                }
            });
            helper.builder(builder -> {
                builder.orderByAsc(RecommendConfig::getSort);
            });
        });
    }

    @Select("select redirect_url from t_recommend_config where biz_type = 'source_recommend' group by redirect_url HAVING COUNT(distinct (data_type)) >= 2")
    List<String> selectDoctorCount();
}
