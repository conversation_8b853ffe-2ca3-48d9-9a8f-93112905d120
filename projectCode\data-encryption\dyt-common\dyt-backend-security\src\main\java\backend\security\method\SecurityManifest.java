package backend.security.method;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class SecurityManifest {
    private List<Item> scopes = Collections.emptyList();
    private List<Item> authorities = Collections.emptyList();
    private Set<String> scopeSet = Collections.emptySet();
    private Set<String> authoritySet = Collections.emptySet();
    private Map<String, String> authorityMap = Collections.emptyMap();

    public List<Item> getScopes() {
        return scopes;
    }

    public void setScopes(List<Item> scopes) {
        this.scopes = scopes;
    }

    public List<Item> getAuthorities() {
        return authorities;
    }

    public void setAuthorities(List<Item> authorities) {
        this.authorities = authorities;
    }

    public boolean hasScope(String scope) {
        return this.scopeSet.contains(scope);
    }

    public boolean hasAuthority(String authority) {
        return this.authoritySet.contains(authority);
    }

    public void initialize() {
        this.scopeSet = this.scopes.stream().map(Item::getName).collect(Collectors.toSet());
        this.authoritySet = this.authorities.stream().map(Item::getName).collect(Collectors.toSet());
        this.authorityMap = this.authorities.stream().collect(Collectors.toMap(Item::getName, Item::getDescription));
    }

    public Map<String, String> getAuthorityMap(){
        return this.authorityMap;
    }

    public static class Item {
        private String name;
        private String description;

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
}
