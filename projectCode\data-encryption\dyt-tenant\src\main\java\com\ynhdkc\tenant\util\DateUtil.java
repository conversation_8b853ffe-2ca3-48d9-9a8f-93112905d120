package com.ynhdkc.tenant.util;

import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;


/**
 * <AUTHOR>
 * @since 2023/7/10 16:07:26
 */
@Slf4j
public class DateUtil {

    public static String convertDateToWeekday(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        Calendar today = Calendar.getInstance();

        if (calendar.get(Calendar.YEAR) == today.get(Calendar.YEAR) && calendar.get(Calendar.DAY_OF_YEAR) == today.get(Calendar.DAY_OF_YEAR)) {
            return "今天";
        }

        if (calendar.get(Calendar.YEAR) == today.get(Calendar.YEAR) && calendar.get(Calendar.DAY_OF_YEAR) == today.get(Calendar.DAY_OF_YEAR) + 1) {
            return "明天";
        }

        int weekday = calendar.get(Calendar.DAY_OF_WEEK);
        String weekdayText = "";
        switch (weekday) {
            case Calendar.SUNDAY:
                weekdayText = "周天";
                break;
            case Calendar.MONDAY:
                weekdayText = "周一";
                break;
            case Calendar.TUESDAY:
                weekdayText = "周二";
                break;
            case Calendar.WEDNESDAY:
                weekdayText = "周三";
                break;
            case Calendar.THURSDAY:
                weekdayText = "周四";
                break;
            case Calendar.FRIDAY:
                weekdayText = "周五";
                break;
            case Calendar.SATURDAY:
                weekdayText = "周六";
                break;
        }
        return weekdayText;
    }


    public static String convertLocalDateToWeekday(LocalDate date) {
        Date temp = convertLocalDateToDate(date);
        return convertDateToWeekday(temp);
    }


    public static String convertDateToMMDD(Date key) {
        SimpleDateFormat sdf = new SimpleDateFormat("MM-dd");
        return sdf.format(key);
    }

    public static String convertLocalDateToMMDD(LocalDate key) {
        Date temp = convertLocalDateToDate(key);
        return convertDateToMMDD(temp);
    }

    public static Date convertDateFromMMDD(String dateStr) {
        Calendar calendar = Calendar.getInstance();
        long current = System.currentTimeMillis();
        calendar.setTimeInMillis(current);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date date = format.parse(calendar.get(Calendar.YEAR) + "-" + dateStr);
            if (date.getTime() >= current || isToday(dateStr, current)) {
                return date;
            }
            calendar.add(Calendar.YEAR, 1);
            return format.parse(calendar.get(Calendar.YEAR) + "-" + dateStr);
        } catch (Exception e) {
            log.error("convertDateFromMMDD_error {}", dateStr);
            return null;
        }
    }

    public static boolean isToday(String dateStr, long current) {
        SimpleDateFormat sdf = new SimpleDateFormat("MM-dd");
        String currentDate = sdf.format(new Date(current));
        return currentDate.equals(dateStr);
    }

    public static String convertDateToYYYYMMDD(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(date);
    }

    public static Set<LocalDate> splitLocalDateByDay(LocalDate start, LocalDate end) {
        Set<LocalDate> dates = new HashSet<>();

        LocalDate current = start;
        while (!current.isAfter(end)) {
            dates.add(current);
            current = current.plusDays(1);
        }

        return dates;
    }

    public static LocalDate convertDateToLocalDate(Date date) {
        return date.toInstant().atZone(ZoneId.of("Asia/Shanghai")).toLocalDate();
    }

    public static Date convertLocalDateToDate(LocalDate localDate) {
        return Date.from(localDate.atStartOfDay(ZoneId.of("Asia/Shanghai")).toInstant());
    }

    public static Long convertLocalDateToLong(LocalDate localDate) {
        return localDate.atStartOfDay(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli();
    }

    public static int convert2Time() {
        SimpleDateFormat sdf = new SimpleDateFormat("HHmmss");
        String time = sdf.format(new Date());
        return Integer.parseInt(time);
    }
}
