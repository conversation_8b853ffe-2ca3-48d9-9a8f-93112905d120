package backend.common.delegate.impl;

import backend.common.delegate.CacheKeyDelegate;
import backend.common.entity.dto.hisgateway.response.MessageEnvelope;
import backend.common.util.CacheKeyUtils;
import backend.common.util.ObjectsUtils;

import java.util.Map;

public class Doctor<PERSON>acheKeyImpl implements CacheKeyDelegate {

    private final String hisOperationValue;

    public DoctorCacheKeyImpl(String hisOperationValue) {
        this.hisOperationValue = hisOperationValue;
    }

    @Override
    public String getCacheKey(MessageEnvelope<?> messageEnvelope, Map<String, Object> requestParameters) {
        String hospitalCode = ObjectsUtils.getStringValue(requestParameters, "hospital_code");

        if (ObjectsUtils.isEmpty(hospitalCode)) {
            return null;
        }
        String departmentValue = "";
        String departmentCode = ObjectsUtils.getStringValue(requestParameters, "department_code");
        long departmentId = ObjectsUtils.getLongValue(requestParameters, "department_id");
        if (departmentId == 0) {
            departmentValue = departmentCode;
        } else {
            departmentValue = "dyt_department_id:" + departmentId;
        }
        long startDate = ObjectsUtils.getLongValue(requestParameters, "start_date");
        if (startDate <= 0) {
            return CacheKeyUtils.getCacheKey(hisOperationValue, hospitalCode, departmentValue);
        } else {
            return CacheKeyUtils.getCacheKey(hisOperationValue, hospitalCode, departmentValue, startDate);
        }
    }
}
