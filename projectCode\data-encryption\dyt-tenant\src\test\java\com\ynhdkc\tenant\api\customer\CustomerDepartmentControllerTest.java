package com.ynhdkc.tenant.api.customer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ynhdkc.tenant.DytTenantApplication;
import com.ynhdkc.tenant.client.HisGatewayClient;
import com.ynhdkc.tenant.client.model.DepartmentDoctorListEnvResponse;
import com.ynhdkc.tenant.model.CustomerDepartmentsTreeSearchListVo;
import com.ynhdkc.tenant.model.CustomerDoctorScheduleInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = {DytTenantApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("dev")
@Slf4j
class CustomerDepartmentControllerTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private CustomerDepartmentController customerDepartmentController;

    @MockBean
    private HisGatewayClient hisGatewayClient;

    @Test
    void queryGroupedDoctorListByDepartmentId() throws JsonProcessingException {
        when(hisGatewayClient.getDoctors(any(), any(), any())).thenReturn(ResponseEntity.ok(fromJson()));
        ResponseEntity<CustomerDoctorScheduleInfoVo> customerDoctorScheduleInfoVoResponseEntity = customerDepartmentController.queryGroupedDoctorListByDepartmentId(7292L, null, null,null);
        log.info("result: {}", objectMapper.writeValueAsString(customerDoctorScheduleInfoVoResponseEntity));
    }

    @Test
    void testDepartmentTree() {
        ResponseEntity<CustomerDepartmentsTreeSearchListVo> customerDepartmentsTreeSearchListVoResponseEntity = customerDepartmentController.queryTree(50L, null, null);
        log.info("result: {}", customerDepartmentsTreeSearchListVoResponseEntity);
    }

    private DepartmentDoctorListEnvResponse fromJson() {
        String json = "{\"hospital_id\":55,\"hospital_code\":\"871023\",\"hospital_area_id\":542,\"department_id\":7292,\"pay_load\":{\"result\":[{\"amt\":0.5,\"sort\":100,\"doctor\":{\"createTime\":1705657931195,\"updateTime\":1705657931195,\"departmentCode\":\"7553\",\"thrdpartDoctorCode\":\"000618\",\"name\":\"侯萍\",\"sort\":100,\"displayDepartmentName\":\"妇科专家门诊（呈贡）\",\"categoryStr\":[]},\"rawParameters\":{},\"department_name\":\"妇科专家门诊（呈贡）\",\"department_code\":\"7553\",\"doctor_name\":\"侯萍\",\"doctor_code\":\"000618\",\"registration_level\":\"5\",\"registration_level_desc\":\"主任医师\",\"src_number\":8,\"is_doctor\":true,\"registration_date\":1705852800000,\"show_schedule_date\":\"01-25 01-22\",\"src_date_map\":{\"01-25\":8,\"01-22\":0},\"schedule_list\":[{\"registration_date\":1705852800000,\"department_code\":\"7553\",\"doctor_code\":\"000618\",\"src_number\":0},{\"registration_date\":1705852800000,\"department_code\":\"7553\",\"doctor_code\":\"000618\",\"src_number\":0},{\"registration_date\":1706112000000,\"department_code\":\"7553\",\"doctor_code\":\"000618\",\"src_number\":8}]},{\"amt\":0.5,\"sort\":100,\"doctor\":{\"createTime\":1705657931195,\"updateTime\":1705657931195,\"departmentCode\":\"7553\",\"thrdpartDoctorCode\":\"651554\",\"name\":\"刘鹏燕\",\"sort\":100,\"displayDepartmentName\":\"妇科专家门诊（呈贡）\",\"categoryStr\":[]},\"rawParameters\":{},\"department_name\":\"妇科专家门诊（呈贡）\",\"department_code\":\"7553\",\"doctor_name\":\"刘鹏燕\",\"doctor_code\":\"651554\",\"registration_level\":\"4\",\"registration_level_desc\":\"副主任医师\",\"src_number\":0,\"is_doctor\":true,\"registration_date\":1705852800000,\"show_schedule_date\":\"01-22\",\"src_date_map\":{\"01-22\":0},\"schedule_list\":[{\"registration_date\":1705852800000,\"department_code\":\"7553\",\"doctor_code\":\"651554\",\"src_number\":0},{\"registration_date\":1705852800000,\"department_code\":\"7553\",\"doctor_code\":\"651554\",\"src_number\":0}]},{\"amt\":0.5,\"sort\":100,\"doctor\":{\"createTime\":1705657931195,\"updateTime\":1705657931195,\"departmentCode\":\"7553\",\"thrdpartDoctorCode\":\"000676\",\"name\":\"段振玲\",\"sort\":100,\"displayDepartmentName\":\"妇科专家门诊（呈贡）\",\"categoryStr\":[]},\"rawParameters\":{},\"department_name\":\"妇科专家门诊（呈贡）\",\"department_code\":\"7553\",\"doctor_name\":\"段振玲\",\"doctor_code\":\"000676\",\"registration_level\":\"5\",\"registration_level_desc\":\"主任医师\",\"src_number\":0,\"is_doctor\":true,\"registration_date\":1705939200000,\"show_schedule_date\":\"01-23\",\"src_date_map\":{\"01-23\":0},\"schedule_list\":[{\"registration_date\":1705939200000,\"department_code\":\"7553\",\"doctor_code\":\"000676\",\"src_number\":0},{\"registration_date\":1705939200000,\"department_code\":\"7553\",\"doctor_code\":\"000676\",\"src_number\":0}]},{\"amt\":0.5,\"sort\":100,\"doctor\":{\"createTime\":1705657931195,\"updateTime\":1705657931195,\"departmentCode\":\"7553\",\"thrdpartDoctorCode\":\"000632\",\"name\":\"张红芸\",\"sort\":100,\"displayDepartmentName\":\"妇科专家门诊（呈贡）\",\"categoryStr\":[]},\"rawParameters\":{},\"department_name\":\"妇科专家门诊（呈贡）\",\"department_code\":\"7553\",\"doctor_name\":\"张红芸\",\"doctor_code\":\"000632\",\"registration_level\":\"5\",\"registration_level_desc\":\"主任医师\",\"src_number\":29,\"is_doctor\":true,\"registration_date\":1706198400000,\"show_schedule_date\":\"01-26\",\"src_date_map\":{\"01-26\":29},\"schedule_list\":[{\"registration_date\":1706198400000,\"department_code\":\"7553\",\"doctor_code\":\"000632\",\"src_number\":29}]}]}}";
        try {
            return objectMapper.readValue(json, DepartmentDoctorListEnvResponse.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
}