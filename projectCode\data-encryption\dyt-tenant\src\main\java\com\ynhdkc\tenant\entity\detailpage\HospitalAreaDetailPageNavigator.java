package com.ynhdkc.tenant.entity.detailpage;

import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @since 2023/5/24 17:59:19
 */
@Table(name = "t_hospital_detail_page_navigator")
@Data
@EqualsAndHashCode(callSuper = true)
public class HospitalAreaDetailPageNavigator extends BaseEntity {

    private Long tenantId;
    private Long hospitalId;
    private Long hospitalAreaId;

    private String hospitalCode;

    private Long naviModuleId;
    private Integer type;
    private String title;
    private String subTitle;
    private String picture;
    private String url;
    private Integer sort;
    private String channels;
}
