package backend.common.discovery;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.HashMap;

public class Routing2EurekaEnvironmentPostProcessor implements EnvironmentPostProcessor {
    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        final String basePathPattern = environment.getProperty("backend.security.base-path-pattern");
        if (StringUtils.hasText(basePathPattern)) {
            if (!environment.getPropertySources().contains("eureka-meta")) {
//                System.out.println("###### Routing2EurekaEnvironmentPostProcessor");
                final HashMap<String, Object> source = new HashMap<>();
                source.put("eureka.instance.metadataMap.basePathPattern", basePathPattern);
                try {
                    source.put("eureka.instance.metadataMap.testJons", new ObjectMapper().writeValueAsString(Collections.singletonMap("hello","world test")));
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
                environment.getPropertySources().addFirst(new MapPropertySource("eureka-meta", source));
            }
        }
//        for (int i = 0; i < 10000; i++) {
//            final String antPattern = environment.getProperty("backend.oauth2.resourceserver.public-paths[" + i + "].ant-pattern");
//            final String method = environment.getProperty("backend.oauth2.resourceserver.public-paths[" + i + "].method");
//        }

    }
}
