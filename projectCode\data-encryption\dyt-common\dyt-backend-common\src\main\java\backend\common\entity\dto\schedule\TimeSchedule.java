package backend.common.entity.dto.schedule;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class TimeSchedule {

    /**
     * 排班id
     */
    @JsonProperty("schedule_id")
    private String scheduleId;
    /**
     * 开始时间
     */
    @JsonProperty("start_time")
    private String startTime;
    /**
     * 结束时间
     */
    @JsonProperty("end_time")
    private String endTime;

    /**
     * 序号
     */
    @JsonProperty("queue_no")
    private Integer queueNo;

}