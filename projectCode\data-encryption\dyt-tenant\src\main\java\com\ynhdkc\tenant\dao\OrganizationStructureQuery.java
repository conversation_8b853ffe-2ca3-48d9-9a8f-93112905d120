package com.ynhdkc.tenant.dao;

import backend.common.util.BaseQueryOption;
import com.ynhdkc.tenant.entity.TenantUserStructure;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-25 16:13
 */
public interface OrganizationStructureQuery {
    List<TenantUserStructure> queryUserStructure(OrganizationStructureQueryOption option);

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Accessors(chain = true)
    class OrganizationStructureQueryOption extends BaseQueryOption {
        public OrganizationStructureQueryOption(Integer currentPage, Integer pageSize) {
            super(currentPage, pageSize);
        }

        private Long userId;
        private Long tenantId;
        private Long hospitalId;
        private Long hospitalAreaId;
        private Long departmentId;

        private Collection<Long> includeTenantIds;
        private Collection<Long> excludeTenantIds;
        private Collection<Long> includeHospitalIds;
        private Collection<Long> excludeHospitalIds;
        private Collection<Long> includeHospitalAreaIds;
        private Collection<Long> excludeHospitalAreaIds;
        private Collection<Long> includeDepartmentIds;
        private Collection<Long> excludeDepartmentIds;
        private Collection<Long> excludeUserIds;
    }
}
