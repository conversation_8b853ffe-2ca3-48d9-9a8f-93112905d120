package backend.common.entity.dto.hisgateway.enums;

import backend.common.exception.BizException;
import com.fasterxml.jackson.annotation.JsonValue;
import org.springframework.http.HttpStatus;

public enum DeliverType {

    APP("APP在线申请", 1), KIOSK("医疗卫生机构自助机申请", 2), SCENE("医疗卫生机构窗口申请", 3), PRE("批量预生成", 4), WECHAT_SERVICE("微信服务号", 5),
    WECHAT_PROGRAM("微信小程序", 6), ALIPAY_SERVICE("支付宝生活号", 7), ALIPAY_PROGRAM("支付宝小程序", 8), OTHER_CARD("其他", 9);

    private final String value;

    private final Integer code;

    DeliverType(String value, Integer code) {
        this.value = value;
        this.code = code;
    }

    public static DeliverType getFromCode(int code) {
        for (DeliverType t : DeliverType.values()) {
            if (t.getCode().equals(code)) {
                return t;
            }
        }
        throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "分发类型不存在");
    }

    public static DeliverType getFromValue(String value) {
        for (DeliverType t : DeliverType.values()) {
            if (t.getValue().equals(value)) {
                return t;
            }
        }
        throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "分发类型不存在");
    }

    public String getValue() {
        return value;
    }

    public Integer getCode() {
        return code;
    }

    @JsonValue
    public String getRequestCode() {
        return code.toString();
    }

}
