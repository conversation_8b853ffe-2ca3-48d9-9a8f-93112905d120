package backend.security.exception.handler;

import backend.common.response.BaseOperationResponse;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.AuthenticationException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-16 20:48
 */
@RestControllerAdvice
public class AuthenticationExceptionHandler extends ResponseEntityExceptionHandler {
    @ExceptionHandler(value = {AuthenticationException.class})
    public ResponseEntity<Object> handleAuthenticationException(AuthenticationException ex, WebRequest request) {
        return handleExceptionInternal(ex, new BaseOperationResponse("401", "您的访问被拒绝，因为您未经身份验证"), new HttpHeaders(), HttpStatus.UNAUTHORIZED, request);
    }
}
