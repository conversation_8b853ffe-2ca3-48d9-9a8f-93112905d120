package com.ynhdkc.tenant.service.backend;

import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.entity.detailpage.HospitalAreaDetailPageNavigator;
import com.ynhdkc.tenant.model.CreateHospitalAreaDetailPageNavigatorReqDto;
import com.ynhdkc.tenant.model.HospitalAreaDetailPageNavigatorVo;
import com.ynhdkc.tenant.model.HospitalAreaLayoutVo;
import com.ynhdkc.tenant.model.UpdateHospitalAreaDetailPageNavigatorReqDto;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/24 17:58:03
 */
public interface IHospitalAreaDetailPageNavigatorService {
    HospitalAreaDetailPageNavigatorVo createHospitalAreaDetailPageNavigator(CreateHospitalAreaDetailPageNavigatorReqDto dto);

    BaseOperationResponse deleteHospitalAreaDetailPageNavigator(Long id);

    HospitalAreaDetailPageNavigatorVo getHospitalAreaDetailPageNavigator(Long hospitalId);

    HospitalAreaDetailPageNavigatorVo updateHospitalAreaDetailPageNavigator(Long hospitalId, UpdateHospitalAreaDetailPageNavigatorReqDto dto);

    void setPageNavigator(HospitalAreaLayoutVo vo, Long hospitalAreaId);

    default List<HospitalAreaDetailPageNavigator> toEntity(HospitalAreaDetailPageNavigator entity, UpdateHospitalAreaDetailPageNavigatorReqDto dto) {
        List<HospitalAreaDetailPageNavigator> entities = new ArrayList<>();
        dto.getNaviInfo().forEach(naviInfo -> {
            entity.setTenantId(dto.getTenantId());
            entity.setHospitalId(dto.getHospitalId());
            entity.setHospitalAreaId(dto.getHospitalAreaId());
            entity.setType(dto.getType());
            entity.setTitle(naviInfo.getTitle());
            entity.setSubTitle(naviInfo.getSubTitle());
            entity.setPicture(naviInfo.getPicture());
            entity.setUrl(naviInfo.getUrl());
            entity.setSort(naviInfo.getSort());
            if (!CollectionUtils.isEmpty(naviInfo.getChannels())) {
                entity.setChannels(naviInfo.getChannels().stream().map(String::valueOf).collect(Collectors.joining(",")));
            }
            entities.add(entity);
        });
        return entities;
    }
}
