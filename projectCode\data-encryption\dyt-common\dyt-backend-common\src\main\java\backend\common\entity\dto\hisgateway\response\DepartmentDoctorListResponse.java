package backend.common.entity.dto.hisgateway.response;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class DepartmentDoctorListResponse {

    private List<DepartmentDoctorItem> result = new ArrayList<>();

    public void setResult(List<DepartmentDoctorItem> result) {
        if (result == null) {
            return;
        }
        this.result = result;
    }
}
