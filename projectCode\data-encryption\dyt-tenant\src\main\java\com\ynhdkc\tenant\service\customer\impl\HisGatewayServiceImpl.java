package com.ynhdkc.tenant.service.customer.impl;

import backend.common.domain.tenant.constant.AppointmentSystemDepends;
import backend.common.enums.HospitalCode;
import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import backend.common.util.JsonUtil;
import backend.common.util.MessageUtil;
import backend.common.util.ObjectsUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ynhdkc.tenant.client.HisGatewayClient;
import com.ynhdkc.tenant.client.ScheduleClient;
import com.ynhdkc.tenant.client.model.DepartmentDoctorListEnvRequest;
import com.ynhdkc.tenant.client.model.DepartmentDoctorListEnvResponse;
import com.ynhdkc.tenant.client.model.DepartmentList4MultiLevelResponse;
import com.ynhdkc.tenant.dao.*;
import com.ynhdkc.tenant.doctor.info.IDoctorInfo;
import com.ynhdkc.tenant.dto.ScheduleResponseDto;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.DictLabel;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.entity.constant.DoctorColumn;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import com.ynhdkc.tenant.service.backend.DictLabelService;
import com.ynhdkc.tenant.service.customer.IHisGatewayService;
import com.ynhdkc.tenant.tool.*;
import com.ynhdkc.tenant.util.DoctorUtils;
import com.ynhdkc.tenant.util.HospitalUtils;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ynhdkc.tenant.util.DateUtil.convertDateFromMMDD;
import static com.ynhdkc.tenant.util.DateUtil.convertLocalDateToDate;

@Service
@RequiredArgsConstructor
@Slf4j
public class HisGatewayServiceImpl implements IHisGatewayService {

    private final static String DOCTOR_RANK_DICT_TYPE = "doctor_title";
    private static final String SPECIAL_DEPARTMENT_CODE = "7025";
    private static final String UPDATE_DOCTOR_KEY = "dyt:tenant:update_doctor:";
    private final HisGatewayClient hisGatewayClient;
    private final HospitalAreaSettingQuery hospitalAreaSettingQuery;
    private final DoctorListTool doctorListTool;
    private final HospitalAreaQuery hospitalAreaQuery;
    private final DepartmentQuery departmentQuery;
    private final DoctorRepository doctorRepository;
    private final DoctorQuery doctorQuery;
    private final ScheduleClient scheduleClient;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final CacheComponent cacheComponent;
    private final DictLabelService dictLabelService;
    private final ObjectMapper objectMapper;
    private final RedisTemplate<String, String> redisTemplate;
    private final String YUNNAN_SECOND_PEOPLE_HOSPITAL_EYE_DEPARTMENT_CODE = "25";
    private final String YUNNAN_SECOND_PEOPLE_HOSPITAL_DEPARTMENT_NAME = "眼科门诊";
    private final List<IDoctorInfo> doctorInfos;
    private final DoctorHeadImageHandler doctorHeadImageHandler;
    private final AsyncServiceUtils asyncServiceUtils;
    String YUNNAN_SECOND_PEOPLE_HOSPITAL_CODE = "871045";

    private static String getSyncDoctorInfoKey(String hospitalCode, String depCode) {
        return "tenant:sync_doctor_info_" + hospitalCode + "_" + depCode;
    }

    private static void filterSchedulesByStopScheduleDateRangeThenDisabled(AppointmentRuleSetting appointmentRuleSetting, ResponseEntity<ScheduleResponseDto> innerScheduleByDoctor) {
        if (innerScheduleByDoctor.getBody() == null) {
            return;
        }

        if (CollectionUtils.isEmpty(innerScheduleByDoctor.getBody().getList())) {
            return;
        }

        int scheduleDisabledStatus = 0;
        if (appointmentRuleSetting.getStopGenerateScheduleBeginDate() != null && appointmentRuleSetting.getStopGenerateScheduleEndDate() != null) {
            innerScheduleByDoctor.getBody().getList().forEach(scheduleInfo -> {
                if (scheduleInfo.getSchDate().getTime() >= appointmentRuleSetting.getStopGenerateScheduleBeginDate().getTime() && scheduleInfo.getSchDate().getTime() <= appointmentRuleSetting.getStopGenerateScheduleEndDate().getTime()) {
                    scheduleInfo.setStatus(scheduleDisabledStatus);
                }
            });
        }
    }

    @Override
    public List<ScheduleResponseDto.ScheduleInfo> getDepartmentDoctorTimeTypeScheduleList(Department department, List<Doctor> doctors, AppointmentRuleSetting appointmentRuleSetting, Integer timeType) {
        List<ScheduleResponseDto.ScheduleInfo> schedules = new ArrayList<>();

        if (AppointmentSystemDepends.HIS.getCode().equals(department.getSystemDepends())) {
            String doctorScheduleList = redisTemplate.opsForValue().get(cacheComponent.getDoctorTimeTypeScheduleKey(department, timeType));
            if (StringUtils.hasText(doctorScheduleList)) {
                return JsonUtil.deserializeList(doctorScheduleList, ScheduleResponseDto.ScheduleInfo.class);
            }
            DepartmentDoctorListEnvResponse response = makeRequest(department, timeType);

            if (Objects.nonNull(response)) {
                createOrUpdateDoctor(response, department, doctors, appointmentRuleSetting);
                doctors = getEnabledDoctors(department);
                schedules = toScheduleInfo(response, department, doctors);
            }
        } else {
            for (Doctor doctor : doctors) {
                ResponseEntity<ScheduleResponseDto> innerScheduleByDoctor = scheduleClient.getInnerScheduleByDoctor(doctor.getHospitalId(), doctor.getHospitalAreaId(), doctor.getDepartmentId(), doctor.getId(), doctor.getHospitalCode(), doctor.getDepartmentCode(), doctor.getThrdpartDoctorCode());
                if (null == innerScheduleByDoctor.getBody() || CollectionUtils.isEmpty(innerScheduleByDoctor.getBody().getList())) {
                    continue;
                }
                try {
                    log.info("get doctor schedule response from his: {}", objectMapper.writeValueAsString(innerScheduleByDoctor));
                } catch (JsonProcessingException e) {
                    throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "获取医生排班信息失败");
                }

                filterSchedulesByStopScheduleDateRangeThenDisabled(appointmentRuleSetting, innerScheduleByDoctor);

                innerScheduleByDoctor.getBody().getList().forEach(q -> q.setSrcNum(q.getMaxTreatNum() - q.getUsedNum()));
                schedules.addAll(innerScheduleByDoctor.getBody().getList());
            }
        }
        cacheDoctorScheduleInfo(schedules, department);
        cacheDepartmentDoctorScheduleInfo(schedules, department, appointmentRuleSetting.getDoctorScheduleCacheExpTime());
        return schedules;
    }

    @Override
    public List<ScheduleResponseDto.ScheduleInfo> getDepartmentDoctorScheduleList(Department department, List<Doctor> doctors, AppointmentRuleSetting appointmentRuleSetting) {
        List<ScheduleResponseDto.ScheduleInfo> schedules = new ArrayList<>();

        if (AppointmentSystemDepends.HIS.getCode().equals(department.getSystemDepends())) {
            String doctorScheduleList = redisTemplate.opsForValue().get(cacheComponent.getDoctorScheduleKey(department));
            if (StringUtils.hasText(doctorScheduleList)) {
                return JsonUtil.deserializeList(doctorScheduleList, ScheduleResponseDto.ScheduleInfo.class);
            }
            DepartmentDoctorListEnvResponse response = makeRequest(department, null);

            if (Objects.nonNull(response)) {
                createOrUpdateDoctor(response, department, doctors, appointmentRuleSetting);
                doctors = getEnabledDoctors(department);
                schedules = toScheduleInfo(response, department, doctors);
            }
        } else {
            for (Doctor doctor : doctors) {
                ResponseEntity<ScheduleResponseDto> innerScheduleByDoctor = scheduleClient.getInnerScheduleByDoctor(doctor.getHospitalId(), doctor.getHospitalAreaId(), doctor.getDepartmentId(), doctor.getId(), doctor.getHospitalCode(), doctor.getDepartmentCode(), doctor.getThrdpartDoctorCode());
                if (null == innerScheduleByDoctor.getBody() || CollectionUtils.isEmpty(innerScheduleByDoctor.getBody().getList())) {
                    continue;
                }
                try {
                    log.info("get doctor schedule response from his: {}", objectMapper.writeValueAsString(innerScheduleByDoctor));
                } catch (JsonProcessingException e) {
                    throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "获取医生排班信息失败");
                }

                filterSchedulesByStopScheduleDateRangeThenDisabled(appointmentRuleSetting, innerScheduleByDoctor);

                innerScheduleByDoctor.getBody().getList().forEach(q -> q.setSrcNum(q.getMaxTreatNum() - q.getUsedNum()));
                schedules.addAll(innerScheduleByDoctor.getBody().getList());
            }
        }
        cacheDoctorScheduleInfo(schedules, department);
        cacheDepartmentDoctorScheduleInfo(schedules, department, appointmentRuleSetting.getDoctorScheduleCacheExpTime());
        return schedules;
    }

    private List<Doctor> getAllDoctors(Department department) {
        if (HospitalUtils.isKunmingMU1stHospital(department.getHospitalCode()) && SPECIAL_DEPARTMENT_CODE.equals(department.getThrdpartDepCode())) {
            return doctorQuery.queryByDepartmentCodeAndHospitalCode(department.getThrdpartDepCode(), department.getHospitalCode());
        }
        //红会医院眼科门诊 根据部门编码查询医生
        if (HospitalUtils.isYunnanSecondPeopleHospital(department.getHospitalCode()) && YUNNAN_SECOND_PEOPLE_HOSPITAL_EYE_DEPARTMENT_CODE.equals(department.getThrdpartDepCode())) {
            return doctorQuery.queryByDepartmentCodeAndHospitalCode(department.getThrdpartDepCode(), department.getHospitalCode());
        }
        return doctorQuery.queryBy(department.getId());
    }

    private List<Doctor> getEnabledDoctors(Department department) {
        List<Doctor> doctorList = getAllDoctors(department);
        return doctorList.stream().filter(q -> q.getStatus() == 0).collect(Collectors.toList());
    }

    public void cacheDoctorScheduleInfo(List<ScheduleResponseDto.ScheduleInfo> schedules, Department department) {
        threadPoolTaskExecutor.submit(() -> {
            try {
                cacheComponent.cacheDoctorScheduleList(department.getHospitalCode(), department.getThrdpartDepCode(), schedules);
            } catch (Exception e) {
                log.error("缓存医生排班信息失败，departmentId={}", department.getId(), e);
            }
        });
    }

    public void cacheEmptyDoctorScheduleInfo(String hospitalAreaCode, Department department, Doctor doctor) {
        threadPoolTaskExecutor.submit(() -> {
            try {
                cacheComponent.cacheEmptyDoctorSchedule(hospitalAreaCode, department, doctor);
            } catch (Exception e) {
                log.error("cacheEmptyDoctorScheduleInfo_error，departmentId={}", department.getId(), e);
            }
        });
    }

    public void cacheDepartmentDoctorScheduleInfo(List<ScheduleResponseDto.ScheduleInfo> schedules, Department department, Long expTimes) {
        if (expTimes > 0L && !CollectionUtils.isEmpty(schedules)) {
            threadPoolTaskExecutor.submit(() -> {
                try {
                    cacheComponent.cacheDoctorScheduleList(schedules, department, expTimes);
                } catch (Exception e) {
                    log.error("同步医生排班信息失败，departmentId={}", department.getId(), e);
                }
            });
        }
    }

    private List<ScheduleResponseDto.ScheduleInfo> toScheduleInfo(DepartmentDoctorListEnvResponse response, Department department, List<Doctor> doctors) {
        if (response.getPayload() == null || CollectionUtils.isEmpty(response.getPayload().getResult())) {
            return Collections.emptyList();
        }

        if (HospitalUtils.isKunmingMU1stHospital(department.getHospitalCode())) {
            return handleForYunDa(response, department, doctors);
        }
        return handleForOther(response, department, doctors);
    }

    private List<ScheduleResponseDto.ScheduleInfo> handleForOther(DepartmentDoctorListEnvResponse response, Department department, List<Doctor> doctors) {
        List<ScheduleResponseDto.ScheduleInfo> schedules = new ArrayList<>();

        response.getPayload().getResult().forEach(item -> {
            if (item.getSrcDateMap() == null) {
                return;
            }

            Doctor doctor = doctors.stream().filter(q -> q.getThrdpartDoctorCode().equals(item.getDoctor().getThrdpartDoctorCode()) && q.getDepartmentCode().equals(department.getThrdpartDepCode())).findFirst().orElse(null);
            if (doctor == null) {
                return;
            }

            item.getSrcDateMap().forEach((dateStr, number) -> schedules.add(createScheduleInfo(doctor, item, dateStr, number)));
        });
        return schedules;
    }

    private List<ScheduleResponseDto.ScheduleInfo> handleForYunDa(DepartmentDoctorListEnvResponse response, Department department, List<Doctor> doctors) {
        String[] departmentCodes = department.getThrdpartDepCode().split("\\|");
        List<ScheduleResponseDto.ScheduleInfo> schedules = new ArrayList<>();

//        log.info("handleForYunDa_1: {}", MessageUtil.object2JSONString(response));
//        log.info("handleForYunDa_2: {}", MessageUtil.object2JSONString(department));
//        log.info("handleForYunDa_3: {}", MessageUtil.object2JSONString(doctors));


        for (Doctor doctor : doctors) {
            for (String departmentCode : departmentCodes) {
                if (response.getPayload() == null) {
                    continue;
                }

                if (CollectionUtils.isEmpty(response.getPayload().getResult())) {
                    continue;
                }
                DepartmentDoctorListEnvResponse.Payload.Result result = response.getPayload().getResult().stream().filter(Objects::nonNull).filter(q -> q.getDepartmentCode() != null && q.getDoctorCode() != null)
                        .filter(q -> {
                            String[] split = q.getDepartmentCode().split("\\|");
                            return Arrays.asList(split).contains(departmentCode) && q.getDoctorCode().equals(doctor.getThrdpartDoctorCode());
                        }).findFirst().orElse(null);
                if (result == null) {
                    continue;
                }
                if (result.getSrcDateMap() == null) {
                    continue;
                }

                result.getSrcDateMap().forEach((dateStr, number) -> schedules.add(createScheduleInfo(doctor, result, dateStr, number)));
            }
        }

        log.info("schedules is {}", MessageUtil.object2JSONString(schedules));

        return schedules;
    }

    public ScheduleResponseDto.ScheduleInfo createScheduleInfo(Doctor doctor, DepartmentDoctorListEnvResponse.Payload.Result item, String dateStr, Integer number) {
        ScheduleResponseDto.ScheduleInfo scheduleInfo = new ScheduleResponseDto.ScheduleInfo();
        scheduleInfo.setDoctorId(doctor.getId());
        scheduleInfo.setDoctorCode(doctor.getThrdpartDoctorCode());
        scheduleInfo.setHospitalId(doctor.getHospitalId());
        scheduleInfo.setHospitalAreaId(doctor.getHospitalAreaId());
        scheduleInfo.setDepartmentId(doctor.getDepartmentId());
        scheduleInfo.setDepartmentName(item.getDepartmentName());
        scheduleInfo.setDepartmentCode(item.getDepartmentCode());
        scheduleInfo.setShowSchDate(1);
        scheduleInfo.setSrcNum(number);
        scheduleInfo.setSchDate(convertDateFromMMDD(dateStr));
        scheduleInfo.setRegistrationLevelDesc(item.getRegistrationLevelDesc());
        scheduleInfo.setTimeType(item.getTimeType());
        return scheduleInfo;
    }

    private DepartmentDoctorListEnvResponse makeRequest(Department department, Integer timeType) {
        DepartmentDoctorListEnvRequest request = buildbuildDepartmentDoctorListEnvRequest(department, timeType);
        log.info("get doctor list request: {}", JsonUtil.serializeObject(request));
        if (StringUtils.hasText(department.getThrdpartDepCode())) {
            ResponseEntity<DepartmentDoctorListEnvResponse> temp = hisGatewayClient.getDoctors(department.getHospitalCode(), department.getThrdpartDepCode(), request);
            return handleDepartmentDoctorListEnvResponse(temp, request);
        } else {
            return null;
        }
    }

    public DepartmentDoctorListEnvRequest buildbuildDepartmentDoctorListEnvRequest(Department department) {
        return buildbuildDepartmentDoctorListEnvRequest(department, null);
    }

    public DepartmentDoctorListEnvRequest buildbuildDepartmentDoctorListEnvRequest(Department department, Integer timeType) {
        DepartmentDoctorListEnvRequest request = new DepartmentDoctorListEnvRequest();
        request.setDepartmentId(department.getId());
        request.setHospitalAreaId(department.getHospitalAreaId());
        request.setHospitalCode(department.getHospitalCode());
        request.setHospitalId(department.getHospitalId());

        DepartmentDoctorListEnvRequest.PayLoad payLoad = new DepartmentDoctorListEnvRequest.PayLoad();
        payLoad.setHospitalCode(department.getHospitalCode());
        payLoad.setDepartmentCode(department.getThrdpartDepCode());
        payLoad.setDepartmentId(department.getId());

        Pair<Long, Long> startAndEnd = getStartAndEnd(department.getHospitalAreaId(), department);
        payLoad.setStartDate(startAndEnd.getLeft());
        payLoad.setEndDate(startAndEnd.getRight());
        payLoad.setTimeType(timeType);

        request.setPayLoad(payLoad);
        processDoctorList(department.getHospitalCode(), department.getId(), request);
        return request;
    }

    private DepartmentDoctorListEnvResponse handleDepartmentDoctorListEnvResponse(ResponseEntity<DepartmentDoctorListEnvResponse> responseEntity, DepartmentDoctorListEnvRequest request) {
        if (responseEntity.getStatusCode() != HttpStatus.OK) {
            log.error("Failed to get doctors. Request: {}. Response Status: {}", JsonUtil.serializeObject(request), responseEntity.getStatusCode());
            throw new BizException(responseEntity.getStatusCode(), "获取排班周期失败");
        }

        DepartmentDoctorListEnvResponse response = responseEntity.getBody();
        if (response == null) {
            log.error("Response body is null. Request: {}", JsonUtil.serializeObject(request));
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "获取排班周期失败");
        }

        if (response.getPayload() == null) {
            DepartmentDoctorListEnvResponse.Payload payload = new DepartmentDoctorListEnvResponse.Payload();
            payload.setResult(new ArrayList<>());
            response.setPayload(payload);
        }

        filterDoctorScheduleList(request.getPayLoad().getStartDate(), request.getPayLoad().getEndDate(), response.getPayload().getResult());
//        log.info("get doctor list request: {}", JsonUtil.serializeObject(request));
        log.info("schedule info response: {}", JsonUtil.serializeObject(response));
        return response;
    }

    public void filterDoctorScheduleList(Long startDate, Long endDate, List<DepartmentDoctorListEnvResponse.Payload.Result> result) {
        if (!CollectionUtils.isEmpty(result)) {
            // 排班数据不超过结束日期当天最后1秒
            long deadlineTime = endDate + 24 * 60 * 60 * 1000L;
            result.forEach(item -> {
                Map<String, Integer> srcDateMap = item.getSrcDateMap();
                Map<String, Integer> tempSrcDateMap = new HashMap<>();
                //srcDateMap可能为null，导致NPE
                if (ObjectUtil.isNotEmpty(srcDateMap)) {
                    srcDateMap.forEach((dateStr, number) -> {
                        Date schDate = convertDateFromMMDD(dateStr);
                        if (Objects.nonNull(schDate)) {
                            if (schDate.getTime() >= startDate && schDate.getTime() < deadlineTime) {
                                tempSrcDateMap.put(dateStr, number);
                            }
                        }
                    });
                    if (tempSrcDateMap.size() != srcDateMap.size()) {
                        item.setSrcDateMap(tempSrcDateMap);
                        item.setShowScheduleDate(String.join(" ", tempSrcDateMap.keySet()));
                    }
                }
            });
        }
    }

    private void createOrUpdateDoctor(DepartmentDoctorListEnvResponse response, Department department, List<Doctor> dbDoctors, AppointmentRuleSetting appointmentRuleSetting) {
        List<Doctor> doctors = response.getPayload().getResult().stream().map(result -> {
            assembleDoctorInfo(result);
            Doctor doctor = result.getDoctor();
            doctor.setRegistrationLevel(result.getRegistrationLevelDesc());
            if (org.apache.commons.lang3.StringUtils.isNotBlank(doctor.getName())) {
                return doctor;
            } else {
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(doctors)) {
            return;
        }

        if (HospitalUtils.isKunmingMU1stHospital(department.getHospitalCode())) {
            List<DictLabel> dictLabels = dictLabelService.getDictLabelBy(DOCTOR_RANK_DICT_TYPE);
            doctors.forEach(item -> {
                Optional<DictLabel> dictLabelOptional = dictLabels.stream().filter(q -> q.getDictLabel().equals(item.getRankDictLabel())).findFirst();
                if (dictLabelOptional.isPresent()) {
                    DictLabel dictLabel = dictLabelOptional.get();
                    item.setRankDictType(dictLabel.getDictType());
                    item.setRankDictValue(dictLabel.getDictValue());
                    item.setRankDictLabel(dictLabel.getDictLabel());
                }
            });

            saveYunDaDepartmentDoctor(doctors, dbDoctors, department, appointmentRuleSetting);
            return;
        }
        List<Doctor> doctorsFromHis = needCreatedOrUpdateDoctorList(doctors, department);
        if (CollectionUtils.isEmpty(dbDoctors)) {
            doctorsFromHis.forEach(item -> createDoctorFromDepartmentDoctorItem(item, department));
            return;
        }
        for (Doctor item : doctorsFromHis) {
            Doctor doctor = dbDoctors.stream().filter(q -> q.getThrdpartDoctorCode().equals(item.getThrdpartDoctorCode())).findFirst().orElse(null);
            if (doctor == null && Boolean.TRUE.equals(appointmentRuleSetting.getCreateDoctorFromHis())) {
                createDoctorFromDepartmentDoctorItem(item, department);
            } else if (doctor != null) {
                asyncServiceUtils.updateDoctorInfoFromDepartmentDoctorItemAsync(item, doctor, department, appointmentRuleSetting);
            }
        }
        // 如果是昆明第一人民医院(甘美)，禁用掉未返回排班的医生
        if (Objects.equals(HospitalCode.KUNMING_FIRST_PEOPLE_HOSPITAL.getCode(), department.getHospitalCode())) {
            asyncServiceUtils.disabledDoctors(dbDoctors, doctorsFromHis);
        }
    }

    /**
     * 医院眼科门诊下二级科室只有眼科门诊需要更新和新增
     */
    List<Doctor> needCreatedOrUpdateDoctorList(List<Doctor> doctorsFromHis, Department department) {

        if (department.getHospitalCode().equals(YUNNAN_SECOND_PEOPLE_HOSPITAL_CODE) && department.getThrdpartDepCode().equals(YUNNAN_SECOND_PEOPLE_HOSPITAL_EYE_DEPARTMENT_CODE)) {
            if (YUNNAN_SECOND_PEOPLE_HOSPITAL_DEPARTMENT_NAME.equals(department.getName())) {
                return doctorsFromHis;
            }
            return new ArrayList<>();
        }
        return doctorsFromHis;
    }

    public void assembleDoctorInfo(DepartmentDoctorListEnvResponse.Payload.Result result) {
        String registrationLevelDesc = result.getRegistrationLevelDesc();
        if (ObjectsUtils.isEmpty(registrationLevelDesc)) {
            return;
        }
        DoctorUtils.DoctorTitleInfo doctorTitleInfo = DoctorUtils.getDoctorTitleInfo(registrationLevelDesc);
        if (doctorTitleInfo == null) {
            return;
        }
        result.setDoctorRankType(doctorTitleInfo.getDoctorRankType());
        result.setDoctorRankLabel(doctorTitleInfo.getDoctorRankDictLabel());
        result.setDoctorRankValue(doctorTitleInfo.getDoctorRankDictValue());
    }

    private void saveYunDaDepartmentDoctor(List<Doctor> doctorsFromHis, List<Doctor> dbDoctors, Department department, AppointmentRuleSetting appointmentRuleSetting) {
        List<Doctor> departmentDirectDBDoctorList = doctorQuery.queryBy(department.getId());
        if (DoctorUtils.isCollectEveryDptDoctor(department, departmentDirectDBDoctorList)) {
            saveDoctors4ExecDepartment(doctorsFromHis, department);
            return;
        }
        if (ObjectsUtils.isEmpty(dbDoctors)) {
            doctorsFromHis.forEach(item -> createDoctorFromDepartmentDoctorItem(item, department));
            return;
        }

        doctorsFromHis.forEach(item -> {
            List<Doctor> doctors = dbDoctors.stream().filter(q -> q.getThrdpartDoctorCode().equals(item.getThrdpartDoctorCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(doctors)) {
                return;
            }

            for (Doctor doctor : doctors) {
                updateDoctorInfoFromDepartmentDoctorItem(item, doctor, department, appointmentRuleSetting);
            }
        });
    }

    private void saveDoctors4ExecDepartment(List<Doctor> doctorsFromHis, Department department) {
        Map<String, Department> execDepartmentMap = queryExecDepartmentMap(doctorsFromHis, department);
        Map<String, Set<String>> execDptDoctorMap = queryExecDepartmentDoctors(execDepartmentMap, department);
        List<Doctor> saveDoctorList = new ArrayList<>();
        for (Doctor doctor : doctorsFromHis) {
            String execDepartmentCode = doctor.getDepartmentCode();
            Department execDepartment = execDepartmentMap.get(execDepartmentCode);
            if (execDepartment == null) {
                continue;
            }
            doctor.setDepartmentId(execDepartment.getId());
            Set<String> doctorCodeSet = execDptDoctorMap.get(execDepartmentCode);
            if (doctorCodeSet.contains(doctor.getThrdpartDoctorCode())) {
                continue;
            }
            saveDoctorList.add(doctor);
        }
        if (ObjectsUtils.isEmpty(saveDoctorList)) {
            return;
        }
        saveDoctorList.forEach(item -> createDoctorFromDepartmentDoctorItem(item, department));
    }

    private Map<String, Set<String>> queryExecDepartmentDoctors(Map<String, Department> departmentMap, Department department) {
        Map<String, Set<String>> departmentDoctorMap = new HashMap<>();
        if (ObjectsUtils.isEmpty(departmentMap)) {
            return departmentDoctorMap;
        }
        for (Map.Entry<String, Department> entry : departmentMap.entrySet()) {
            Long execDepartmentId = entry.getValue().getId();
            List<Doctor> doctorList = doctorQuery.queryBy(department.getHospitalAreaId(), execDepartmentId);
            if (ObjectsUtils.isEmpty(doctorList)) {
                continue;
            }
            Set<String> doctorCodeSet = doctorList.stream().map(Doctor::getThrdpartDoctorCode).collect(Collectors.toSet());
            departmentDoctorMap.put(entry.getKey(), doctorCodeSet);
        }
        return departmentDoctorMap;
    }

    private Map<String, Department> queryExecDepartmentMap(List<Doctor> doctorsFromHis, Department department) {
        Map<String, Department> departmentMap = new HashMap<>();
        Set<String> departmentCodeSet = doctorsFromHis.stream().map(Doctor::getDepartmentCode).collect(Collectors.toSet());
        if (ObjectsUtils.isEmpty(departmentCodeSet)) {
            return departmentMap;
        }
        for (String execDepartmentCode : departmentCodeSet) {
            Department execDepartment = departmentQuery.queryBy(department.getHospitalCode(), execDepartmentCode);
            if (execDepartment == null) {
                continue;
            }
            departmentMap.put(execDepartment.getThrdpartDepCode(), execDepartment);
        }
        return departmentMap;
    }

    private void processDoctorList(String hospitalCode, long departmentId, DepartmentDoctorListEnvRequest request) {
        if (!HospitalUtils.isKunmingMU1stHospital(hospitalCode)) {
            return;
        }

        Department department = departmentQuery.queryDepartmentById(departmentId);
        if (department == null || ObjectsUtils.isEmpty(department.getDoctors())) {
            return;
        }

        request.getPayLoad().setDoctors(department.getDoctors());
    }

    private Pair<Long, Long> getStartAndEnd(Long hospitalAreaId, Department department) {
        Optional<AppointmentRuleSetting> ruleSetting = hospitalAreaSettingQuery.queryAppointmentRuleSettingBy(hospitalAreaId);
        if (!ruleSetting.isPresent()) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "未配置预约周期");
        }

        Pair<Integer, Integer> advanceAndForbiddenDays = doctorListTool.defaultAdvanceAndForbiddenDays(null, department, ruleSetting.get());
        Integer advanceDay = advanceAndForbiddenDays.getLeft();
        Integer forbiddenDay = advanceAndForbiddenDays.getRight();
        LocalDate now = LocalDate.now();
        LocalDate start = now.plusDays(forbiddenDay);
        LocalDate end = now.plusDays(advanceDay);
        return Pair.of(convertLocalDateToDate(start).getTime(), convertLocalDateToDate(end).getTime());
    }

    public void createDoctorFromDepartmentDoctorItem(Doctor doctorInfoFromHis, Department department) {
        Doctor doctor = new Doctor();
        doctor.setHospitalId(department.getHospitalId());
        doctor.setHospitalCode(department.getHospitalCode());
        doctor.setDepartmentId(department.getId());
        doctor.setTenantId(department.getTenantId());
        doctor.setThrdpartDoctorCode(doctorInfoFromHis.getThrdpartDoctorCode());

        if (HospitalUtils.isKunmingMU1stHospital(department.getHospitalCode())) {
            doctor.setDepartmentCode(doctorInfoFromHis.getDepartmentCode());
        } else {
            doctor.setDepartmentCode(department.getThrdpartDepCode());
        }

        if (HospitalUtils.isGongRenYiYuan(department.getHospitalCode())) {
            doctor.setDisplayDepartmentName(doctorInfoFromHis.getHonor());
        } else {
            doctor.setHonor(doctorInfoFromHis.getHonor());
        }

        doctor.setHospitalAreaId(department.getHospitalAreaId());
        doctor.setName(doctorInfoFromHis.getName());
        doctor.setRankDictType(doctorInfoFromHis.getRankDictType());
        doctor.setRankDictLabel(doctorInfoFromHis.getRankDictLabel());
        doctor.setRankDictValue(doctorInfoFromHis.getRankDictValue());
        doctor.setRegistrationLevel(doctorInfoFromHis.getRegistrationLevel());
        doctor.setIntroduction(doctorInfoFromHis.getIntroduction());
        doctor.setSpeciality(doctorInfoFromHis.getSpeciality());
        doctor.setSort(doctorInfoFromHis.getSort());
        doctor.setDisplay(true);
        doctor.setSystemDepends(AppointmentSystemDepends.HIS.getCode());
        doctorHeadImageHandler.setDoctorImageUrl(doctor, doctorInfoFromHis.getHeadImgUrl());

        doctorRepository.create(doctor);
    }

    public void updateDoctorInfoFromDepartmentDoctorItem(Doctor doctorInfoFromHis, Doctor databaseDoctor, Department department, AppointmentRuleSetting setting) {
        Set<Long> doctorIdSetDoesNotNeedUpdate = new HashSet<>();
        doctorIdSetDoesNotNeedUpdate.add(194462L);
        doctorIdSetDoesNotNeedUpdate.add(196494L);
        if (doctorIdSetDoesNotNeedUpdate.contains(doctorInfoFromHis.getId())) {
            log.info("Doctor ID {} does not need update", doctorInfoFromHis.getId());
            return;
        }

        if (Boolean.FALSE.equals(databaseDoctor.getUpdateFromHis())) {
            return;
        }

        ColumnUpdateTool columnUpdateTool = new ColumnUpdateTool(setting.getUpdateDoctorExcludeColumns());

        if (!columnUpdateTool.hasColumn(DoctorColumn.HONOR.getColumn()) && StringUtils.hasText(doctorInfoFromHis.getHonor())) {
            if (HospitalUtils.isGongRenYiYuan(department.getHospitalCode())) {
                databaseDoctor.setDisplayDepartmentName(doctorInfoFromHis.getHonor());
            } else {
                databaseDoctor.setHonor(doctorInfoFromHis.getHonor());
            }
        }
        if (!columnUpdateTool.hasColumn(DoctorColumn.INTRODUCTION.getColumn()) && StringUtils.hasText(doctorInfoFromHis.getIntroduction())) {
            databaseDoctor.setIntroduction(doctorInfoFromHis.getIntroduction());
        }
        if (!columnUpdateTool.hasColumn(DoctorColumn.SPECIALITY.getColumn()) && StringUtils.hasText(doctorInfoFromHis.getSpeciality())) {
            databaseDoctor.setSpeciality(doctorInfoFromHis.getSpeciality());
        }
        if (!columnUpdateTool.hasColumn(DoctorColumn.NAME.getColumn()) && StringUtils.hasText(doctorInfoFromHis.getName())) {
            databaseDoctor.setName(doctorInfoFromHis.getName());
        }
        if (!columnUpdateTool.hasColumn(DoctorColumn.HEAD_IMG_URL.getColumn()) && StringUtils.hasText(doctorInfoFromHis.getHeadImgUrl()) && Boolean.FALSE.equals(redisTemplate.hasKey(UPDATE_DOCTOR_KEY + databaseDoctor.getId()))) {
            doctorHeadImageHandler.setDoctorImageUrl(databaseDoctor, doctorInfoFromHis.getHeadImgUrl());
            redisTemplate.opsForValue().set(UPDATE_DOCTOR_KEY + databaseDoctor.getId(), "1", 3, TimeUnit.DAYS);
        }
        if (!columnUpdateTool.hasColumn(DoctorColumn.RANK_DICT_TYPE.getColumn()) && StringUtils.hasText(doctorInfoFromHis.getRankDictType())) {
            databaseDoctor.setRankDictType(doctorInfoFromHis.getRankDictType());
        }
        if (!columnUpdateTool.hasColumn(DoctorColumn.RANK_DICT_LABEL.getColumn()) && StringUtils.hasText(doctorInfoFromHis.getRankDictLabel())) {
            databaseDoctor.setRankDictLabel(doctorInfoFromHis.getRankDictLabel());
        }
        if (!columnUpdateTool.hasColumn(DoctorColumn.RANK_DICT_VALUE.getColumn()) && StringUtils.hasText(doctorInfoFromHis.getRankDictValue())) {
            databaseDoctor.setRankDictValue(doctorInfoFromHis.getRankDictValue());
        }
        if (!columnUpdateTool.hasColumn(DoctorColumn.SORT.getColumn())) {
            databaseDoctor.setSort(doctorInfoFromHis.getSort());
        }
        databaseDoctor.setRegistrationLevel(doctorInfoFromHis.getRegistrationLevel());
        databaseDoctor.setHospitalId(department.getHospitalId());
        databaseDoctor.setHospitalAreaId(department.getHospitalAreaId());
        databaseDoctor.setHospitalCode(department.getHospitalCode());
        if (HospitalUtils.isKunmingMU1stHospital(department.getHospitalCode())) {
            databaseDoctor.setDepartmentCode(doctorInfoFromHis.getDepartmentCode());
            databaseDoctor.setDisplayDepartmentName(doctorInfoFromHis.getDisplayDepartmentName());
            databaseDoctor.setSort(null);
        } else {
            databaseDoctor.setDepartmentCode(department.getThrdpartDepCode());
        }
        if (Objects.equals(HospitalCode.KUNMING_FIRST_PEOPLE_HOSPITAL.getCode(), department.getHospitalCode())) {
            databaseDoctor.setStatus(0);
        }
        databaseDoctor.setUpdateTime(new Date());
        doctorRepository.update(databaseDoctor);
    }

    @Override
    public BaseOperationResponse syncInfoByHospitalAreaId(Long hospitalAreaId) {
        Hospital hospital = hospitalAreaQuery.queryHospitalAreaById(hospitalAreaId);
        if (hospital == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "院区不存在");
        }
        List<Department> departmentList = departmentQuery.queryDepartments(hospitalAreaId, null);
        List<Doctor> doctors = doctorQuery.queryBy(hospitalAreaId, null);
        AppointmentRuleSetting ruleSetting = hospitalAreaSettingQuery.queryAppointmentRuleSettingBy(hospitalAreaId).orElse(null);
        return syncDoctorInfoFromHis(hospital.getHospitalCode(), null, null, departmentList, doctors, ruleSetting);
    }

    @Override
    public void syncYunDaHospitalDoctorInfo() {
        List<String> hospitalCodes = Arrays.asList(
                HospitalCode.KUNMING_THIRD_PEOPLE_HOSPITAL_WU_JING_V3.getCode(), HospitalCode.KUNMING_THIRD_PEOPLE_HOSPITAL_CHANG_PO_V3.getCode()
                , HospitalCode.KUNMING_MU_FIRST_AFFILIATED_HOSPITAL.getCode(), HospitalCode.KUNMING_MU_FIRST_AFFILIATED_CG_CAMPUS_HOSPITAL.getCode());
        for (String hospitalCode : hospitalCodes) {
            try {
                ResponseEntity<DepartmentDoctorListEnvResponse> all = hisGatewayClient.syncDoctorInfo(hospitalCode, "all", null);
                log.info("{},sync doctor info from his response: {}", hospitalCode, JsonUtil.serializeObject(all));
                if (all.getBody() == null || CollectionUtils.isEmpty(all.getBody().getPayload().getResult())) {
                    continue;
                }

                IDoctorInfo doctorInfo = doctorInfos.stream().filter(q -> q.getHospitalCode().contains(hospitalCode)).findFirst().orElse(null);
                if (doctorInfo != null) {
                    log.info("sync doctor info from his, hospitalCode: {}", hospitalCode);
                    threadPoolTaskExecutor.submit(() -> {
                        doctorInfo.resolve(all.getBody());
                        doctorInfo.updateDoctorInfo(hospitalCode, all.getBody());
                    });
                }
            } catch (Exception e) {
                log.error("同步医院科室医生信息失败，hospitalCode={}", hospitalCode, e);
            }
        }
    }

    @Override
    public DepartmentList4MultiLevelResponse getDepartmentList4MultiLevel(String hospitalCode, String parentDepartmentCode) {
        return hisGatewayClient.getDepartmentList4MultiLevel(hospitalCode, parentDepartmentCode).getBody();
    }

    @Override
    public BaseOperationResponse syncInfoByDepartmentId(Long departmentId) {
        Department department = departmentQuery.queryDepartmentById(departmentId);
        if (department == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "科室不存在");
        }
        if (!StringUtils.hasText(department.getThrdpartDepCode())) {
            return new BaseOperationResponse();
        }
        List<Doctor> doctors = doctorQuery.queryBy(departmentId);
        AppointmentRuleSetting ruleSetting = hospitalAreaSettingQuery.queryAppointmentRuleSettingBy(department.getHospitalAreaId()).orElse(null);

        if (StringUtils.hasText(department.getThrdpartDepCode()) && department.getThrdpartDepCode().contains("|")) {
            String[] split = department.getThrdpartDepCode().split("\\|");
            for (String depCode : split) {
                if (hasSyncDataInOneHour(department.getHospitalCode(), depCode)) {
                    continue;
                }

                log.info("sync doctor info from his, hospitalCode: {}, departmentCode: {}", department.getHospitalCode(), depCode);
                syncDoctorInfoFromHis(department.getHospitalCode(), depCode, null, Collections.singletonList(department), doctors, ruleSetting);
                tagSyncDataInOneHour(department.getHospitalCode(), depCode);
            }
            return new BaseOperationResponse();
        }

        return syncDoctorInfoFromHis(department.getHospitalCode(), department.getThrdpartDepCode(), null, Collections.singletonList(department), doctors, ruleSetting);
    }

    private void tagSyncDataInOneHour(String hospitalCode, String depCode) {
        String key = getSyncDoctorInfoKey(hospitalCode, depCode);
        redisTemplate.opsForValue().set(key, "1", 1, java.util.concurrent.TimeUnit.HOURS);
    }

    private Boolean hasSyncDataInOneHour(String hospitalCode, String depCode) {
        String key = getSyncDoctorInfoKey(hospitalCode, depCode);
        return redisTemplate.hasKey(key);
    }

    @Override
    public BaseOperationResponse syncInfoByDoctorId(Long doctorId) {
        Doctor doctor = doctorQuery.queryDoctorById(doctorId);
        if (doctor == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "医生不存在");
        }
        Department department = departmentQuery.queryDepartmentById(doctor.getDepartmentId());
        AppointmentRuleSetting ruleSetting = hospitalAreaSettingQuery.queryAppointmentRuleSettingBy(doctor.getHospitalAreaId()).orElse(null);
        return syncDoctorInfoFromHis(doctor.getHospitalCode(), doctor.getDepartmentCode(), doctor.getThrdpartDoctorCode(), Collections.singletonList(department), Collections.singletonList(doctor), ruleSetting);
    }

    public BaseOperationResponse syncDoctorInfoFromHis(@NonNull String hospitalCode,
                                                       String departmentCode,
                                                       String doctorCode,
                                                       List<Department> departmentListDb,
                                                       List<Doctor> doctorListDb,
                                                       AppointmentRuleSetting appointmentRuleSetting) {
        ResponseEntity<DepartmentDoctorListEnvResponse> doctorInfoResp = hisGatewayClient.syncDoctorInfo(hospitalCode, departmentCode, doctorCode);
        log.info("sync doctor info from his response: {}", doctorInfoResp);
        DepartmentDoctorListEnvResponse respBody = doctorInfoResp.getBody();
        if (respBody == null) {
            return new BaseOperationResponse();
        }
        if (CollectionUtils.isEmpty(respBody.getPayload().getResult())) {
            return new BaseOperationResponse();
        }

        doctorListDb = doctorListDb.stream().filter(q -> StringUtils.hasText(q.getThrdpartDoctorCode())).collect(Collectors.toList());
        Map<Long, List<Doctor>> doctorMap = doctorListDb.stream().collect(Collectors.groupingBy(Doctor::getDepartmentId));
        Map<Long, Department> departmentMap = departmentListDb.stream().collect(Collectors.toMap(Department::getId, Function.identity()));
        List<DepartmentDoctorListEnvResponse.Payload.Result> allInfoFromHis = respBody.getPayload().getResult();
        DepartmentDoctorListEnvResponse response = BeanUtil.copyProperties(respBody, DepartmentDoctorListEnvResponse.class);
        departmentMap.forEach((id, depart) -> {
            List<Doctor> doctors = doctorMap.get(id);
            if (CollectionUtils.isEmpty(doctors)) {
                return;
            }
            doctors = doctors.stream().filter(q -> StringUtils.hasText(q.getThrdpartDoctorCode())).collect(Collectors.toList());
            List<String> doctorCodeList = doctors.stream().map(Doctor::getThrdpartDoctorCode).collect(Collectors.toList());
            List<DepartmentDoctorListEnvResponse.Payload.Result> infoList = allInfoFromHis.stream().filter(info -> doctorCodeList.contains(info.getDoctorCode())).collect(Collectors.toList());
            response.getPayload().setResult(infoList);

            doctorInfos.forEach(info -> {
                if (info.getHospitalCode().contains(hospitalCode)) {
                    info.resolve(response);
                }
            });

            createOrUpdateDoctor(response, depart, doctors, appointmentRuleSetting);
        });

        return new BaseOperationResponse();
    }
}
