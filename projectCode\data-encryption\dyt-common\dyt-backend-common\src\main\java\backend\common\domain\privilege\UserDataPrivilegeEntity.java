package backend.common.domain.privilege;

import backend.common.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-21 14:08
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserDataPrivilegeEntity extends BaseEntity {
    /**
     * 当前层级最大权限
     */
    @JsonIgnore
    public static final Long MAX_PRIVILEGE_OF_CURRENT_LAYER = 0L;
    private Long userId;
    private Long tenantId;
    private Long hospitalId;
    private Long hospitalAreaId;
    private Long departmentId;
}
