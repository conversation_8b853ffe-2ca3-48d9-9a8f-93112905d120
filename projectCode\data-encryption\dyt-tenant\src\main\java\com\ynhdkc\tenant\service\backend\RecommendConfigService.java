package com.ynhdkc.tenant.service.backend;

import backend.common.response.BaseOperationResponse;
import backend.common.util.StringUtils;
import com.ynhdkc.tenant.entity.RecommendConfig;
import com.ynhdkc.tenant.model.*;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;
import java.util.List;

public interface RecommendConfigService {

    RecommendConfigVo createRecommendConfig(CreateRecommendConfigReqDto request);

    RecommendConfigVo updateRecommendConfig(Long id, UpdateRecommendConfigReqDto request);

    RecommendConfigVo getRecommendConfig(Long id);

    BaseOperationResponse deleteRecommendConfig(Long id);

    RecommendConfigPageVo getRecommendConfigPage(GetRecommendConfigPageReqDto request);

    List<String> getRecommendConfigKeywords(Long id);

    default RecommendConfigVo toVo(RecommendConfig entity) {
        RecommendConfigVo vo = new RecommendConfigVo();
        vo.setId(entity.getId());
        vo.setBizType(entity.getBizType());
        vo.setDataType(entity.getDataType());
        vo.setDataTag(entity.getDataTag());
        vo.setDataId(entity.getDataId());
        vo.setSort(entity.getSort());
        vo.setEnabled(entity.getEnabled());
        vo.setRedirectUrl(entity.getRedirectUrl());
        vo.setDisplayName(entity.getDisplayName());
        String displayTags = entity.getDisplayTags();
        if (!ObjectUtils.isEmpty(displayTags)) {
            vo.setDisplayTags(Arrays.asList(displayTags.split(",")));
        }
        vo.setImgUrl(entity.getImgUrl());
        vo.setKeyWords(entity.getKeyWords());
        vo.setRecommendScore(entity.getRecommendScore());
        vo.setSpecially(entity.getSpecially());
        vo.setRecommendReason(entity.getRecommendReason());
        if (org.springframework.util.StringUtils.hasText(entity.getKeyWords())) {
            vo.setKeyWordList(Arrays.asList(entity.getKeyWords().split("\\|")));
        }
        return vo;
    }

    default <T extends CreateRecommendConfigReqDto> RecommendConfig toEntity(T request) {
        RecommendConfig entity = new RecommendConfig();
        entity.setBizType(request.getBizType());
        entity.setDataType(request.getDataType());
        entity.setDataTag(request.getDataTag());
        entity.setDataId(request.getDataId());
        entity.setSort(request.getSort());
        entity.setEnabled(request.getEnabled());
        entity.setRedirectUrl(request.getRedirectUrl());
        entity.setDisplayTags(StringUtils.join(request.getDisplayTags(), ","));
        entity.setImgUrl(request.getImgUrl());
        entity.setDisplayName(request.getDisplayName());
        entity.setKeyWords(request.getKeyWords());
        entity.setRecommendScore(request.getRecommendScore());
        entity.setRecommendReason(request.getRecommendReason());
        entity.setSpecially(request.getSpecially());
        if (!CollectionUtils.isEmpty(request.getKeyWordList())) {
            entity.setKeyWords(StringUtils.join(request.getKeyWordList(), "|"));
        }
        return entity;
    }

    BaseOperationResponse updateRecommendConfigChannel(UpdateRecommendConfigChannelReqDto request);

    int selectCount(String dictLabel);

    List<RecommendConfig> selectDoctorRecommondList(String dictType);

    List<RecommendConfigDoctorDto> createRecommendDoctor(UpdateRecommendConfigDoctorDto request, String dictType);

    void updateDoctorRecommend(UpdateRecommendConfigDoctorDto list);

    void deleteByIds(List<Long> ids, Long dictLabelId);

    RecommendConfig selectRecommendGroup(Long doctorId, boolean flag);

    List<MultipleRecommendConfigDoctorDto> getMultipleDoctorList();
}
