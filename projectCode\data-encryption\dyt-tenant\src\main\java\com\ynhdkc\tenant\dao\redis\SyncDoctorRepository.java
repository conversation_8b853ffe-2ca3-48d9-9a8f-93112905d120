package com.ynhdkc.tenant.dao.redis;

import com.ynhdkc.tenant.dao.redis.template.IntegerRedisTemplate;
import com.ynhdkc.tenant.util.ServiceRedisKeyUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

@Slf4j
@Repository
public class SyncDoctorRepository {

    @Resource
    private RedisConnectionFactory redisConnectionFactory;

    public Integer queryPageNumber(String hospitalCode) {
        IntegerRedisTemplate integerRedisTemplate = IntegerRedisTemplate.getInstance(redisConnectionFactory);

        String key = ServiceRedisKeyUtils.getDepartmentDoctorPageKey(hospitalCode);
        ValueOperations<String, Integer> valueOperations = integerRedisTemplate.opsForValue();

        return valueOperations.get(key);
    }

    public void savePageNumber(String hospitalCode, Integer pageNumber) {
        IntegerRedisTemplate integerRedisTemplate = IntegerRedisTemplate.getInstance(redisConnectionFactory);

        String key = ServiceRedisKeyUtils.getDepartmentDoctorPageKey(hospitalCode);
        ValueOperations<String, Integer> valueOperations = integerRedisTemplate.opsForValue();
        valueOperations.set(key, pageNumber);
    }

    public void increasePageNumber(String hospitalCode) {
        IntegerRedisTemplate integerRedisTemplate = IntegerRedisTemplate.getInstance(redisConnectionFactory);

        String key = ServiceRedisKeyUtils.getDepartmentDoctorPageKey(hospitalCode);
        ValueOperations<String, Integer> valueOperations = integerRedisTemplate.opsForValue();
        valueOperations.increment(key);
    }

    public void deletePageNumber(String hospitalCode) {
        IntegerRedisTemplate integerRedisTemplate = IntegerRedisTemplate.getInstance(redisConnectionFactory);

        String key = ServiceRedisKeyUtils.getDepartmentDoctorPageKey(hospitalCode);
        integerRedisTemplate.delete(key);
    }

}
