CREATE TABLE `t_vaccine`
(
    `id`                  BIGINT(20)   NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `vaccine_category_id` BIGINT(20)   NOT NULL COMMENT '疫苗id',
    `tenant_id`           BIGINT(20)   NOT NULL COMMENT '租户id',
    `hospital_id`         BIGINT(20)   NOT NULL COMMENT '医院id',
    `hospital_area_id`    BIGINT(20)   NOT NULL COMMENT '院区id',
    `department_id`       BIGINT(20)   NOT NULL COMMENT '科室id',
    `doctor_id`           BIGINT(20)   NOT NULL COMMENT '医生id',
    `sort`                INT(10)      NULL COMMENT '排序',
    `tips`                VARCHAR(255) NULL COMMENT '预约提示(弹窗等)',
    `remark`              VARCHAR(255) NULL COMMENT '其他说明(放号时间等)',
    `create_time`         DATETIME(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `update_time`         DATETIME(3)  NULL ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='疫苗关联表';