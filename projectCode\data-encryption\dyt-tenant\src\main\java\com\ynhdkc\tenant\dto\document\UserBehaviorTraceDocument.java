package com.ynhdkc.tenant.dto.document;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;

import static com.ynhdkc.tenant.dto.document.UserBehaviorTraceDocument.INDEX_NAME;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/11/2 14:33
 */
@Data
@Document(indexName = INDEX_NAME, createIndex = false)
public class UserBehaviorTraceDocument {
    @Field(name = "user_id", type = FieldType.Long, store = true)
    private Long userId;

    @Field(name = "ip", type = FieldType.Ip, store = true)
    private String ip;

    @Field(type = FieldType.Integer, store = true)
    private Integer channel;

    @Field(type = FieldType.Integer, store = true)
    private Integer action;

    @Field(name = "action_resource", type = FieldType.Text, store = true)
    private String actionResource;

    @Field(name = "action_time", type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", store = true)
    private Date actionTime;

    @JsonIgnore
    public static final String INDEX_NAME = "user_behavior_trace_v1";
}
