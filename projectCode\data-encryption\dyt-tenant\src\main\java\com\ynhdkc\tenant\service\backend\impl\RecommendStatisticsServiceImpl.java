package com.ynhdkc.tenant.service.backend.impl;

import cn.hutool.core.date.DateTime;
import com.ynhdkc.tenant.dao.mapper.RecommendConfigMapper;
import com.ynhdkc.tenant.dao.mapper.RecommendStatisticsMapper;
import com.ynhdkc.tenant.entity.RecommendConfig;
import com.ynhdkc.tenant.entity.RecommendStatistics;
import com.ynhdkc.tenant.entity.constant.RecommendStatisticsConstant;
import com.ynhdkc.tenant.service.backend.RecommendStatisticsService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/11/2 10:50
 */
@Service
@RequiredArgsConstructor
public class RecommendStatisticsServiceImpl implements RecommendStatisticsService {
    private final RecommendConfigMapper recommendConfigMapper;
    private final RecommendStatisticsMapper recommendStatisticsMapper;

    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    public void mergeStatistics() {
        List<RecommendConfig> recommendConfigs = recommendConfigMapper.selectByExample2(RecommendConfig.class,
                sql -> sql.andEqualTo(RecommendConfig::getEnabled, 1)
        );

        recommendConfigs.forEach(recommendConfig -> {
            String key = RecommendStatisticsConstant.RECOMMEND_STATISTICS_KEY_PREX + ":" + recommendConfig.getId();
            String lastKey = RecommendStatisticsConstant.LAST_RECOMMEND_STATISTICS_KEY_PREX + ":" + recommendConfig.getId();

            Integer statistics = getRecommendStatistics(key);
            Integer lastStatistics = getRecommendStatistics(lastKey);

            RecommendStatistics recommendStatistics = createRecommendStatistics(recommendConfig, statistics, lastStatistics);
            recommendStatisticsMapper.insertSelective(recommendStatistics);

            redisTemplate.opsForValue().set(lastKey, statistics);
        });
    }

    private Integer getRecommendStatistics(String key) {
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            Object object = redisTemplate.opsForValue().get(key);
            if (null != object) {
                return (Integer) object;
            }
            return 0;
        }
        return 0;
    }

    private static RecommendStatistics createRecommendStatistics(RecommendConfig recommendConfig, Integer statistics, Integer lastStatistics) {
        RecommendStatistics recommendStatistics = new RecommendStatistics();
        recommendStatistics.setRecommendId(recommendConfig.getId());
        recommendStatistics.setQuantity(statistics - lastStatistics);
        recommendStatistics.setTotal(statistics);
        recommendStatistics.setCreateTime(new DateTime(new DateTime().toString("yyyy-MM-dd HH:00:00")));
        return recommendStatistics;
    }
}
