package backend.common.health;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.boot.actuate.health.Status;

@AllArgsConstructor
@Getter
public class HealthResult {
    Status status;
    String message;

    public static HealthResult up(String message) {
        return new HealthResult(Status.UP, message);
    }
    public static HealthResult down(String message) {
        return new HealthResult(Status.DOWN, message);
    }

    public String toString() {
        return "HealthResult(status=" + this.getStatus() + ", message=" + this.getMessage() + ")";
    }

}
