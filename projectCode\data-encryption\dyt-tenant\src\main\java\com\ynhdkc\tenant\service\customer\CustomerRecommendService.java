package com.ynhdkc.tenant.service.customer;

import com.ynhdkc.tenant.model.*;

import java.util.List;

public interface CustomerRecommendService {
    RecommendRespVo getRecommendList(String bizType, Integer channel, String dataType, String dataTag, Double longitude, Double latitude);

    RecommendRespVo getRecommendById(Long recommendId, Double longitude, Double latitude);

    List<RecommendConfigDepartmentDto> getRecommendDepartment(String bizType);

    CustomRecommendDoctorPageVo getRecommendDepartmentDetail(CustomRecommendDoctorQueryDto request);

    RecentlyRegistrationDepartmentVo getRecentRegistrationDepartment(Long userId);

    CustomerDoctorScheduleInfoVo queryGroupedDoctorListByDepartmentId(Long departmentId, String hospitalAreaCode, String departmentCode, Integer timeType, Long recommendId, String dataTag);
}
