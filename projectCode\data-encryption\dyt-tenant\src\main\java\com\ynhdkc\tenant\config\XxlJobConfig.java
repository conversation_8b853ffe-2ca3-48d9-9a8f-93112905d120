package com.ynhdkc.tenant.config;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(name = "xxl.job.executorAppName", havingValue = "dyt-tenant")
@Slf4j
public class XxlJobConfig {
    @Value("${xxl.job.adminAddresses}")
    private String adminAddresses;

    @Value("${xxl.job.executorPort}")
    private Integer executorPort;

    @Value("${xxl.job.executorLogRetentionDays}")
    private Integer executorLogRetentionDays;

    @Value("${xxl.job.accessToken}")
    private String accessToken;

    @Value("${xxl.job.executorAppName}")
    private String executorAppName;

    @Bean
    public XxlJobSpringExecutor xxlJobExecutor() {
        log.info(">>>>>>>>>>> xxl-job config init. {}", adminAddresses);

        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
        xxlJobSpringExecutor.setAppname(executorAppName);
        xxlJobSpringExecutor.setPort(executorPort);
        xxlJobSpringExecutor.setAccessToken(accessToken);
        xxlJobSpringExecutor.setLogRetentionDays(executorLogRetentionDays);

        return xxlJobSpringExecutor;
    }
}
