package com.ynhdkc.tenant.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.text.SimpleDateFormat;
import java.util.Date;

@Configuration
@EnableCaching
@EnableScheduling
@Slf4j
public class CacheConfig {
    public static final String DOCTOR_INFO = "doctor_info";
    public static final String DOCTOR_SETTING = "doctor_setting";
    public static final String DICT_LABEL_CACHE = "dictLabelCache";
    public static final String QUERY_HOSPITAL_PAGE_CACHE = "queryHospitalPageCache";
    public static final String CREATE_DOCTOR= "dyt-tenant:create_doctor:";

    @Bean
    public CacheManager cacheManager() {
        return new ConcurrentMapCacheManager(DOCTOR_INFO, DICT_LABEL_CACHE, QUERY_HOSPITAL_PAGE_CACHE, DOCTOR_SETTING);
    }

    @CacheEvict(allEntries = true, value = {DOCTOR_INFO, DOCTOR_SETTING})
    @Scheduled(fixedDelay = 10 * 60 * 1000, initialDelay = 500)
    public void reportCacheEvict() {
        SimpleDateFormat dateFormat = new SimpleDateFormat();
        System.out.println("Flush Cache " + dateFormat.format(new Date()));
    }

    @CacheEvict(allEntries = true, value = {DICT_LABEL_CACHE, QUERY_HOSPITAL_PAGE_CACHE})
    @Scheduled(cron = "${cache.dict-label-cache.evict.cron}")
    public void dictLabelCacheEvict() {

    }
}
