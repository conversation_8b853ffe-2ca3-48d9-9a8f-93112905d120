package com.ynhdkc.tenant.service.backend.impl;

import backend.common.domain.tenant.constant.DictFileClusterStatus;
import backend.common.domain.tenant.constant.DictFileStatus;
import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import backend.common.util.RedisKeyBuilder;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.client.DataClient;
import com.ynhdkc.tenant.client.model.DictFileDto;
import com.ynhdkc.tenant.client.model.OssDictFileUploadRequest;
import com.ynhdkc.tenant.client.model.OssDictFileUploadResponse;
import com.ynhdkc.tenant.dao.SearchManagementQuery;
import com.ynhdkc.tenant.dao.SearchManagementRepository;
import com.ynhdkc.tenant.entity.DictFile;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/11/8 17:17
 */
@Service
public class AliyunSearchManagementServiceImpl extends BaseSearchManagementServiceImpl {
    public AliyunSearchManagementServiceImpl(SearchManagementQuery searchManagementQuery,
                                             SearchManagementRepository searchManagementRepository,
                                             PageVoConvert pageVoConvert,
                                             DataClient dataClient,
                                             RedisTemplate<String, Object> redisTemplate) {
        super(searchManagementQuery, searchManagementRepository, pageVoConvert);
        this.dataClient = dataClient;
        this.redisTemplate = redisTemplate;
    }

    private final DataClient dataClient;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String SYNC_LOCK_KEY = new RedisKeyBuilder("dyt-tenant", "search-management")
            .nextNode("sync-lock")
            .build();

    @Override
    public BaseOperationResponse syncDict() {
        try (Page<DictFile> page = searchManagementQuery.pageQueryDictFile(new SearchManagementQuery.DictFileQueryOption(1, Integer.MAX_VALUE)
                .setStatus(DictFileStatus.ENABLE.getCode())
        )) {
            Object lock = redisTemplate.opsForValue().get(SYNC_LOCK_KEY);
            if (null != lock) {
                throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "同步任务正在进行中，请稍后再试");
            }

            List<DictFileDto> dtos = page.stream()
                    .filter(entity -> StringUtils.hasText(entity.getWords()))
                    .map(entity -> {
                        DictFileDto dto = new DictFileDto();
                        dto.setName(entity.getName());
                        dto.setType(entity.getType());
                        if (DictFileClusterStatus.NOT_SYNC.getCode().equals(entity.getClusterStatus())) {
                            dto.setNewDict(true);
                            dto.setWords(Arrays.asList(entity.getWords().split(",")));
                        } else {
                            dto.setNewDict(false);
                        }
                        return dto;
                    })
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(dtos)) {
                throw new BizException(HttpStatus.NOT_FOUND, "没有需要同步的字典文件");
            }

            OssDictFileUploadResponse response = dataClient.uploadDicFile(new OssDictFileUploadRequest()
                    .dictList(dtos)
            );

            if (!Boolean.TRUE.equals(response.isSuccess())) {
                throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "同步失败");
            }
            redisTemplate.opsForValue().setIfAbsent(SYNC_LOCK_KEY, true, 90, TimeUnit.MINUTES);

            Date now = new Date();
            page.forEach(entity -> {
                entity.setClusterStatus(DictFileClusterStatus.SYNC.getCode());
                entity.setSyncTime(now);
                searchManagementRepository.updateDictFile(entity);
            });
            return new BaseOperationResponse("下发同步任务成功，同步生效时间约为1小时");
        }
    }
}
