package backend.common.entity.dto.hisgateway.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.persistence.Column;


@Data
public class DoctorCommentRequest {


    @JsonProperty("id")
    private Long id;

    @JsonProperty("appointment_serial_number")
    private String appointmentSerialNumber;

    @JsonProperty("medical_record_number")
    private String medicalRecordNumber;

    @JsonProperty("visiting_date")
    private Long visitingDate;


    /**
     * 医院编码
     */
    @JsonProperty("hospital_code")
    private String hospitalCode;

    /**
     * 科室编码
     */
    @JsonProperty("department_code")
    private String departmentCode;


    /**
     * 科室名字
     */
    @JsonProperty("department_name")
    private String departmentName;


    /**
     * 医生编码
     */
    @JsonProperty("doctor_code")
    private String doctorCode;

    /**
     * 医生名字
     */
    @JsonProperty("doctor_name")
    private String doctorName;

    /**
     * 整体评价评分
     */
    @JsonProperty("whole_score")
    private Integer wholeScore;

    /**
     * 医院评分
     */
    @JsonProperty("hospital_score")
    private Integer hospitalScore;

    /**
     * 医生评分
     */
    @JsonProperty("doctor_score")
    private Integer doctorScore;

    /**
     * 医生专业技能
     */
    @JsonProperty("doctor_skill_score")
    private Integer doctorSkillScore;

    /**
     * 医务工作人员评分
     */
    @JsonProperty("worker_score")
    private Integer workerScore;

    /**
     * 护士医务人员评分
     */
    @JsonProperty("nurse_score")
    private Integer nurseScore;

    /**
     * 导医态度评分
     */
    @JsonProperty("guider_score")
    private Integer guiderScore;

    /**
     * 挂号等候时间评分
     */
    @JsonProperty("register_wait_score")
    private Integer registerWaitScore;

    /**
     * 候诊等候时间评分
     */
    @JsonProperty("visit_wait_score")
    private Integer visitWaitScore;

    /**
     * 检查等候时间评分
     */
    @JsonProperty("check_wait_score")
    private Integer checkWaitScore;

    /**
     * 取药等候时间
     */
    @JsonProperty("drug_wait_score")
    private Integer drugWaitScore;

    /**
     * 医生评价
     */
    @JsonProperty("doctor_comment")
    private String doctorComment;

    /**
     * 医院评价
     */
    @Column(name = "hospital_comment")
    private String hospitalComment;


    /**
     * 服务态度
     */
    @JsonProperty("server_attitude")
    private Integer serverAttitude;

    /**
     * 服务质量
     */
    @JsonProperty("server_quality")
    private Integer serverQuality;

    /**
     * 服务效果
     */
    @JsonProperty("server_result")
    private Integer serverResult;

    /**
     * 满意程度
     */
    private Integer satisfaction;

}
