package backend.common.cdc.listener;

import backend.common.cdc.CDCListener;
import backend.common.cdc.CDCTopics;
import backend.common.cdc.ResourceChangeCapture;
import backend.common.cdc.dto.CDCFloorArea;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;

public class CDCFloorAreaListener extends CDCListener<CDCFloorArea> {

    public CDCFloorAreaListener(ResourceChangeCapture<CDCFloorArea> capture) {
        super(capture);
    }

    @KafkaListener(topics = CDCTopics.BACKEND_FLOOR_AREA, groupId = "${spring.application.name}")
    public void kafkaListener(ConsumerRecord<String, byte[]> msg) throws Exception {
        this.doWork(msg, CDCFloorArea.class);
    }

}
