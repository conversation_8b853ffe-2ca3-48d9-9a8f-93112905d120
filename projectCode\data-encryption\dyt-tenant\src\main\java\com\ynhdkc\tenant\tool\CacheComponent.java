package com.ynhdkc.tenant.tool;

import backend.common.entity.dto.hisgateway.response.DepartmentDoctorItem;
import backend.common.util.HospitalUtils;
import backend.common.util.MessageUtil;
import backend.common.util.RedisKeyBuilder;
import com.ynhdkc.tenant.dto.ScheduleResponseDto;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.Doctor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/11/17 10:39
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CacheComponent {

    private final RedisTemplate<String, String> redisTemplate;

    private static String getCacheKey(String hospitalAreaCode, String departmentCode) {
        return new RedisKeyBuilder("dyt-tenant", "department")
                .nextNode("doctor-list")
                .nextNode(hospitalAreaCode)
                .nextNode(departmentCode)
                .build();
    }

    private static String createRequestHospitalDepartmentStatusKey(Long hospitalAreaId) {
        return new RedisKeyBuilder("dyt-tenant", "hospital-departments").nextNode("is-request-department").nextNode(hospitalAreaId).build();
    }

    public DepartmentDoctorItem getDoctorItem(String hospitalAreaCode, String departmentCode, String doctorCode) {
        String key = getCacheKey(hospitalAreaCode, departmentCode);
        String value = redisTemplate.opsForValue().get(key);
        if (null == value) {
            return null;
        }
        try {
            List<DepartmentDoctorItem> doctorItems = MessageUtil.json2ListObj(value, DepartmentDoctorItem.class);
            return doctorItems.stream().filter(item -> item.getDoctorCode().equals(doctorCode)).findFirst().orElse(null);
        } catch (Exception e) {
            return null;
        }
    }

    public void cacheEmptyDoctorSchedule(String hospitalAreaCode, Department department, Doctor doctor) {
        LocalDate localDate = LocalDate.now();
        String key = getDoctorScheduleKey(hospitalAreaCode, department.getThrdpartDepCode(), department.getId(),
                doctor.getThrdpartDoctorCode(), doctor.getId(), localDate.toString());
        String scheduleValue = MessageUtil.object2JSONString(new ArrayList<>());

        redisTemplate.opsForValue().set(key, scheduleValue, 1, TimeUnit.HOURS);
    }

    public void cacheDoctorScheduleList(String hospitalAreaCode, String departmentCode, List<ScheduleResponseDto.ScheduleInfo> scheduleInfoList) {
        LocalDate localDate = LocalDate.now();
        Map<Long, List<ScheduleResponseDto.ScheduleInfo>> doctorIdAndSchedules = scheduleInfoList.stream().collect(Collectors.groupingBy(ScheduleResponseDto.ScheduleInfo::getDoctorId));
        doctorIdAndSchedules.forEach((doctorId, schedules) -> {
            String key = getDoctorScheduleKey(hospitalAreaCode, departmentCode, schedules.get(0).getDepartmentId(), schedules.get(0).getDoctorCode(), doctorId, localDate.toString());
            String scheduleValue = MessageUtil.object2JSONString(schedules);

            if (HospitalUtils.isKunmingMU1stHospital(hospitalAreaCode)) {
                redisTemplate.opsForValue().set(key, scheduleValue, 10, TimeUnit.MINUTES);
            } else {
                redisTemplate.opsForValue().set(key, scheduleValue, 8, TimeUnit.HOURS);
            }
        });
    }

    private String getDoctorScheduleKey(String hospitalAreaCode, String departmentCode, Long departmentId, String doctorCode, Long doctorId, String startDate) {
        String key = String.format("%s_%s_%s_%s_%s_%s", hospitalAreaCode, departmentCode, departmentId, doctorCode, doctorId, startDate);
        return new RedisKeyBuilder("dyt-tenant", "doctor").nextNode("schedule").nextNode(key).build();
    }

    public void cacheDoctorScheduleList(List<ScheduleResponseDto.ScheduleInfo> scheduleInfoList, Department department, Long expTimes) {
        String key = getDoctorScheduleKey(department);
        String scheduleValue = MessageUtil.object2JSONString(scheduleInfoList);
        redisTemplate.opsForValue().set(key, scheduleValue, expTimes, TimeUnit.SECONDS);
    }

    public String getDoctorScheduleKey(Department department) {
        LocalDate localDate = LocalDate.now();
        String key = String.format("%s_%s_%s_%s", department.getHospitalId(), department.getHospitalCode(), department.getId(), localDate.toString());
        return new RedisKeyBuilder("dyt-tenant", "department").nextNode("doctor").nextNode("schedule").nextNode(key).build();
    }

    public String getDoctorTimeTypeScheduleKey(Department department,Integer timeType) {
        LocalDate localDate = LocalDate.now();
        String key = String.format("%s_%s_%s_%s_%s", department.getHospitalId(), department.getHospitalCode(), department.getId(), localDate.toString(),timeType.toString());
        return new RedisKeyBuilder("dyt-tenant", "department").nextNode("doctor").nextNode("schedule").nextNode("time-type").nextNode(key).build();
    }


    /**
     * 获取医生排班列表
     *
     * @param hospitalAreaCode 医院编码
     * @param departmentId     科室id
     * @param departmentCode   科室编码
     * @param doctorCode       医生编码
     * @param doctorId         医生id
     */
    public @Nullable List<ScheduleResponseDto.ScheduleInfo> getDoctorScheduleList(String hospitalAreaCode, Long departmentId, String departmentCode, String doctorCode, Long doctorId) {
        Date date = new Date();
        String startDate = com.ynhdkc.tenant.util.DateUtil.convertDateToYYYYMMDD(date);
        String key = getDoctorScheduleKey(hospitalAreaCode, departmentCode, departmentId, doctorCode, doctorId, startDate);
        String value = redisTemplate.opsForValue().get(key);
        if (null == value) {
            log.info("getDoctorScheduleList key:{} value is null", key);
            return null;
        }

        List<ScheduleResponseDto.ScheduleInfo> scheduleInfoList = MessageUtil.json2ListObj(value, ScheduleResponseDto.ScheduleInfo.class);
        if (CollectionUtils.isEmpty(scheduleInfoList)) {
            return scheduleInfoList;
        }
        for (ScheduleResponseDto.ScheduleInfo scheduleInfo : scheduleInfoList) {
            scheduleInfo.setSchDate(scheduleInfo.getSchDate());
            scheduleInfo.setShowSchDate(1);
        }
        return scheduleInfoList;
    }

    public void cacheRequestHospitalDepartmentStatus(Long hospitalAreaId) {
        String hospital = createRequestHospitalDepartmentStatusKey(hospitalAreaId);
        redisTemplate.opsForValue().set(hospital, "_", 1, TimeUnit.HOURS);
    }

    public Boolean isRequestHospitalDepartmentStatus(Long hospitalAreaId) {
        String hospital = createRequestHospitalDepartmentStatusKey(hospitalAreaId);
        return redisTemplate.hasKey(hospital);
    }

}
