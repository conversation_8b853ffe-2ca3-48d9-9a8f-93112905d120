package com.ynhdkc.tenant.config;

import backend.common.component.HisGatewayResponseParser;
import backend.common.dao.respository.HisGatewayMessageRepository;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class BaseSysConfig {

    @Bean
    public HisGatewayResponseParser hisGatewayResponseParser() {
        return new HisGatewayResponseParser();
    }

    @Bean
    public HisGatewayMessageRepository hisGatewayMessageRepository() {
        return new HisGatewayMessageRepository();
    }
}