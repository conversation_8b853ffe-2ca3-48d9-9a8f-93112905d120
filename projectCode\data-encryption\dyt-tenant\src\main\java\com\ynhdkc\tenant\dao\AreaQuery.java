package com.ynhdkc.tenant.dao;

import backend.common.util.BaseQueryOption;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.entity.Area;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-04 11:59
 */
public interface AreaQuery {
    Page<Area> pageQuery(AreaQueryOption option);

    Area queryAreaById(Long areaId);

    List<Area> queryAreasByHospitalAreaId(Long hospitalAreaId);

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Accessors(chain = true)
    class AreaQueryOption extends BaseQueryOption {
        public AreaQueryOption(Integer currentPage, Integer pageSize) {
            super(currentPage, pageSize);
        }

        private Long tenantId;
        private Long hospitalId;
        private Long hospitalAreaId;
        private Long buildingId;
        private Long floorId;
        private String name;
        private Integer status;

        private Collection<Long> includeTenantIds;
        private Collection<Long> excludeTenantIds;
        private Collection<Long> includeHospitalIds;
        private Collection<Long> excludeHospitalIds;
        private Collection<Long> includeHospitalAreaIds;
        private Collection<Long> excludeHospitalAreaIds;
        private Collection<Long> includeFloorIds;
    }
}
