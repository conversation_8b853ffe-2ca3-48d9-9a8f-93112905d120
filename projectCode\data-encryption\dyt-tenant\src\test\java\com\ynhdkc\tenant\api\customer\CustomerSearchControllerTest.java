package com.ynhdkc.tenant.api.customer;

import com.ynhdkc.tenant.DytTenantApplication;
import com.ynhdkc.tenant.model.SearchDepartmentAndDoctorReqDto;
import com.ynhdkc.tenant.model.SearchDepartmentAndDoctorVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;

import java.util.Collections;

@SpringBootTest(classes = {DytTenantApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@Slf4j
class CustomerSearchControllerTest {

    @Autowired
    private CustomerSearchController customerSearchController;

    @Test
    void searchDepartmentAndDoctor() {
        //{"current_page":1,"page_size":20,"keywords":"张剑","hospital_area_id":"120","type":0,"department_enabled":true,"doctor_status":[0]}
        SearchDepartmentAndDoctorReqDto request = new SearchDepartmentAndDoctorReqDto();
        request.setCurrentPage(1);
        request.setPageSize(20);
        request.setKeywords("张剑");
        request.setHospitalAreaId(120L);
        request.setType(0);
        request.setDepartmentEnabled(true);
        request.setDoctorStatus(Collections.singletonList(0));
        ResponseEntity<SearchDepartmentAndDoctorVo> searchDepartmentAndDoctorVoResponseEntity = customerSearchController.searchDepartmentAndDoctor(request);
        log.info("searchDepartmentAndDoctorVoResponseEntity:{}", searchDepartmentAndDoctorVoResponseEntity);
    }
}