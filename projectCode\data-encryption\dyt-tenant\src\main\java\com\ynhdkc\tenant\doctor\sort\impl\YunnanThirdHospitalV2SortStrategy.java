package com.ynhdkc.tenant.doctor.sort.impl;

import com.ynhdkc.tenant.doctor.sort.strategy.AbstractDoctorSortStrategy;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.tool.DoctorSortUtils;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class YunnanThirdHospitalV2SortStrategy  extends AbstractDoctorSortStrategy {

    private static final Set<String> HOSPITAL_CODES = Collections.unmodifiableSet(new HashSet<>(Arrays.asList("871896")));

    protected YunnanThirdHospitalV2SortStrategy() {
        super(HOSPITAL_CODES);
    }

    @Override
    public void sort(List<Doctor> doctors) {
        DoctorSortUtils.levelDescAndSortDescAndNameAsc(doctors);
    }
}
