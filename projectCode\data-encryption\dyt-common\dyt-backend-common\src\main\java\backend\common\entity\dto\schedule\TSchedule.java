package backend.common.entity.dto.schedule;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.persistence.Id;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class TSchedule {
    /**
     * pk
     */
    @Id
    private Long id;

    /**
     * hosptal表id
     */
    @JsonProperty("hospital_id")
    private Long hospitalId;

    @JsonProperty("hospital_area_id")
    private Long hospitalAreaId;

    /**
     * department表id
     */
    @JsonProperty("department_id")
    private Long departmentId;

    /**
     * doctor表id
     */
    @JsonProperty("doctor_id")
    private Long doctorId;

    /**
     * 排班id
     */
    @JsonProperty("schedule_id")
    private String scheduleId;

    /**
     * 出诊日期
     */
    @JsonProperty("sch_date")
    private Date schDate;

    /**
     * 时间类型：0未知 1上午 2下午 3中午 4晚上 5前夜 6后夜 7全天 8白天 9昼夜 10夜间 11傍晚
     */
    @JsonProperty("time_type")
    private Integer timeType;

    /**
     * 开始时间
     */
    @JsonProperty("start_time")
    private String startTime;

    /**
     * 结束时间
     */
    @JsonProperty("end_time")
    private String endTime;

    /**
     * 分
     */
    @JsonProperty("total_fee")
    private BigDecimal totalFee;

    /**
     * 最大诊疗数
     */
    @JsonProperty("max_treat_num")
    private Integer maxTreatNum;

    /**
     * 已用诊疗数
     */
    @JsonProperty("used_num")
    private Integer usedNum;

    /**
     * 1分时段 0不分时段
     */
    @JsonProperty("is_time_part")
    private Integer isTimePart;

    /**
     * 排版说明
     */
    @JsonProperty("schedule_info")
    private String scheduleInfo;

    /**
     * 操作人或说明
     */
    private String operator;

    /**
     * 1启用 0禁用
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonProperty("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonProperty("update_time")
    private Date updateTime;

    /**
     * 显不显示就诊日期 0否 1是
     */
    @JsonProperty("show_sch_date")
    private Integer showSchDate;

    private BigDecimal zjf;
    private BigDecimal zlf;
    private BigDecimal ghf;

}