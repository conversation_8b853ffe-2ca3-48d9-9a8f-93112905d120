package backend.common.entity.dto.notification.request;

import backend.common.constants.ChannelConstant;
import backend.common.entity.dto.notification.constant.MessageChannelEnum;
import backend.common.entity.dto.notification.constant.MessageTypeTagEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Data
public class NotificationSendRequest {

    @JsonProperty("message_type_tag")
    @JsonIgnoreProperties(ignoreUnknown = true)
    private MessageTypeTagEnum messageTypeTag;

    @JsonProperty("from_app")
    @JsonIgnoreProperties(ignoreUnknown = true)
    private String fromApp;

    /**
     * 医院id
     */
    @JsonProperty("hospital_id")
    @JsonIgnoreProperties(ignoreUnknown = true)
    private Long hospitalId;

    /**
     * 科室id
     */
    @JsonProperty("department_id")
    @JsonIgnoreProperties(ignoreUnknown = true)
    private Long departmentId;

    /**
     * 医生id
     */
    @JsonProperty("doctor_id")
    @JsonIgnoreProperties(ignoreUnknown = true)
    private Long doctorId;

    /**
     * 收件人
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    private List<Recipient> recipients;

    /**
     * 跳转链接 参考管理端配置的地址，需要自行将参数拼接
     */
    @JsonProperty("redirect_url")
    private String redirectUrl;

    /**
     * 指定发送渠道，不传则默认发对应消息类型下的所有模板
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    private List<MessageChannelEnum> channels;

    @JsonIgnoreProperties(ignoreUnknown = true)
    private Map<String, String> payload;

    public void setChannels(MessageChannelEnum... channels) {
        this.channels = Arrays.asList(channels);
    }

    public void setRecipientsSingle(String phoneNumber,
                                    String wxMpOpenId,
                                    String cid,
                                    ChannelConstant.WechatMpChannel wxMpTag) {
        Recipient recipient = new Recipient()
                .setPhoneNumber(phoneNumber)
                .setWxMpOpenId(wxMpOpenId)
                .setCid(cid);
        if (!ObjectUtils.isEmpty(wxMpTag)) {
            recipient.setWxMpTag(wxMpTag);
        }
        this.recipients = Collections.singletonList(recipient);
    }
}
