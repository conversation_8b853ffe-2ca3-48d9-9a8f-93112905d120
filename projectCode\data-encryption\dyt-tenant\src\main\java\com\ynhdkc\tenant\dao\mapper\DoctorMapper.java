package com.ynhdkc.tenant.dao.mapper;

import com.ynhdkc.tenant.dao.DoctorQuery;
import com.ynhdkc.tenant.entity.Doctor;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/22 17:45:02
 */
@Mapper
public interface DoctorMapper extends BaseMapper<Doctor> {
    List<Doctor> selectDoctorWithCategory(@Param("option") DoctorQuery.DoctorQueryOption option);

}
