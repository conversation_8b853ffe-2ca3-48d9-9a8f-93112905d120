package com.ynhdkc.tenant.tool;

import com.ynhdkc.tenant.model.CustomerDepartmentTreeVo;

import java.util.Comparator;

import static com.ynhdkc.tenant.tool.BackendDepartmentComparator.departmentComparator;

public class CustomerDepartmentComparator implements Comparator<CustomerDepartmentTreeVo> {

    /**
     * 先按 firstLetter 正序排序，如果 firstLetter 相同，按科室名称排序，最后按 sort 逆序排序
     *
     * @param o1 the first object to be compared.
     * @param o2 the second object to be compared.
     * @return a negative integer, zero, or a positive integer as the first argument is less than, equal to, or greater
     */
    @Override
    public int compare(CustomerDepartmentTreeVo o1, CustomerDepartmentTreeVo o2) {
        return departmentComparator(o1.getFirstLetter(), o2.getFirstLetter(), o2.getSort(), o1.getSort());
    }
}
