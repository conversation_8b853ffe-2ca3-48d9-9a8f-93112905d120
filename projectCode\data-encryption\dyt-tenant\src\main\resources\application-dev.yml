server:
  port: 9002

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    #    url: ****************************************************************************************************************************************************
    #    username: root
    #    password: root
    url: ****************************************************************************************************************************************************
    username: root
    password: Hwc132465
  redis:
    cluster:
      nodes: *************:6380,*************:6379,*************:6379,*************:6379,*************:6380,*************:6380
    password: xQ3F5cRX6X
    database: 1
  elasticsearch:
    rest:
      uris: http://*************:9200,http://*************:9200,http://*************:9200
      username: elastic
      password: bl8ydfJJgq
  cloud:
    loadbalancer:
      cache:
        enabled: false
      ribbon:
        enabled: true
  security.oauth2.resourceserver.jwt:
    jwks-service-discovery: false
    jwk-set-uri: http://*************:30880/apis/v1/privilege/.well-known/jwks.json
    jws-algorithm: RS256

eureka:
  client:
    serviceUrl:
      defaultZone: http://localhost:20001/eureka

backend.kafka-appender.enabled: false

megaease:
  output:
    type: console
  trace:
    enabled: false
  metrics:
    enabled: false

mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
logging:
  level:
    #    com.ynhdkc.tenant.dao.mapper: debug
    org.apache.kafka.clients.producer: off
    backend.common.kafka.streams: off # 关闭kafka stream日志
tenant-user-login-config:
  password-policy: DEFAULT
  min-password-length: 8
  max-login-attempts: 5
  login-cooloff-time: 30
  multi-device-login: false
  account-lock-duration: 60
  password-expiry-days: 90
  password-history-count: 5
  password-regex: ^(?=.*[a-z])(?=.*[A-Z])(?=.*\d|.*[@$!%*?&]).{8,16}$

