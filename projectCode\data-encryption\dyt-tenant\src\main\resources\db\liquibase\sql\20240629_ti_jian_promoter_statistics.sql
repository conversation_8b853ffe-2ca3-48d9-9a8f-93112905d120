CREATE TABLE t_promoter
(
    id          BIGINT PRIMARY KEY AUTO_INCREMENT primary key AUTO_INCREMENT,
    name        VARCHAR(50)                              NOT NULL,
    phone       VARCHAR(20)                              NOT NULL,
    code        VARCHAR(50)                              NOT NULL,
    create_time datetime(3) default CURRENT_TIMESTAMP(3) not null,
    update_time datetime(3)                              null on update CURRENT_TIMESTAMP(3),
    index (code),
    index (phone),
    unique (code)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='推广人员表';


CREATE TABLE t_promoter_performance
(
    id          BIGINT PRIMARY KEY AUTO_INCREMENT,
    code        VARCHAR(50)                              NOT NULL,
    order_no    VARCHAR(50)                              NOT NULL,
    user_id     BIGINT                                   NOT NULL,
    create_time datetime(3) default CURRENT_TIMESTAMP(3) not null,
    update_time datetime(3)                              null on update CURRENT_TIMESTAMP(3),
    index (code),
    index (order_no),
    index (user_id),
    unique (order_no, user_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='业绩表';

CREATE TABLE t_promoter_performance_statistics
(
    id          BIGINT PRIMARY KEY AUTO_INCREMENT,
    code        VARCHAR(50)                              NOT NULL,
    promoter    VARCHAR(50)                              NOT NULL,
    promoter_id BIGINT                                   NOT NULL,
    order_count INT                                      NOT NULL,
    user_count  INT                                      NOT NULL,
    create_time datetime(3) default CURRENT_TIMESTAMP(3) not null,
    update_time datetime(3)                              null on update CURRENT_TIMESTAMP(3),
    index (code),
    index (promoter_id),
    index (code, promoter_id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='推广人员业绩统计表';