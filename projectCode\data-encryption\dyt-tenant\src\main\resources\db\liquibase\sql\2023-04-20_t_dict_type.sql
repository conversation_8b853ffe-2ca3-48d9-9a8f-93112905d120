create table t_dict_type
(
    id          bigint auto_increment comment '全局唯一标识'
        primary key,
    type        varchar(40)                              not null comment '数据字典类型',
    description varchar(100)                             null comment '类型描述',
    create_time datetime(3) default CURRENT_TIMESTAMP(3) not null,
    update_time datetime(3)                              null on update CURRENT_TIMESTAMP(3),
    constraint uk_type
        unique (type)
)
    comment 'DictType';
