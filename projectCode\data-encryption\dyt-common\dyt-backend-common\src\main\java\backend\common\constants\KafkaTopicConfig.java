package backend.common.constants;

public class KafkaTopicConfig {

    public static final String HOSPITAL_CHANGE_LOG = "hospital_change_log";
    // 直接读取排班列表数据 kafka global store
    public static final String DOCTOR_SCHEDULE_LIST = "DoctorScheduleList";
    public static final String TIME_SCHEDULE_LIST = "TimeScheduleList";
    public static final String RECHARGE_PAYMENT_RESPONSE = "RechargeOrderWaitPaymentResponse";

    public static final String RECHARGE_REFUND_REQUEST = "RechargeOrderWaitRefundRequest";

    private KafkaTopicConfig() {
    }

    public static class ScheduleSrcNumberSync {
        public static final String REQUEST_TOPIC_NAME = "ScheduleSrcNumberSyncRequest";
        public static final String RESPONSE_TOPIC_NAME = "ScheduleSrcNumberSyncResponse";

        private ScheduleSrcNumberSync() {
        }
    }

    //获取科室列表
    public static class DepartmentList {
        public static final String REQUEST_TOPIC_NAME = "DepartmentListRequest";
        public static final String RESPONSE_TOPIC_NAME = "DepartmentListResponse";

        private DepartmentList() {
        }
    }

    //获取科室医生列表(新)
    public static class DptDoctorList {
        public static final String REQUEST_TOPIC_NAME = "DptDoctorListRequest";
        public static final String RESPONSE_TOPIC_NAME = "DptDoctorListResponse";

        private DptDoctorList() {
        }
    }

    // 医师评价
    public static class DoctorComment {
        public static final String REQUEST_TOPIC_NAME = "DoctorCommentRequest";
        public static final String RESPONSE_TOPIC_NAME = "DoctorCommentResponse";

        private DoctorComment() {
        }
    }

    //医生停诊
    public static class DoctorOutService {
        public static final String REQUEST_TOPIC_NAME = "DoctorOutServiceRequest";
        public static final String RESPONSE_TOPIC_NAME = "DoctorOutServiceResponse";

        private DoctorOutService() {
        }
    }

    //医生停诊
    public static class HospitalOutService {
        public static final String REQUEST_TOPIC_NAME = "HospitalOutServiceRequest";
        public static final String RESPONSE_TOPIC_NAME = "HospitalOutServiceResponse";

        private HospitalOutService() {
        }
    }

    //获取分时段排班列表信息
    public static class TimeSchedule {
        public static final String REQUEST_TOPIC_NAME = "TimeScheduleRequest";
        public static final String RESPONSE_TOPIC_NAME = "TimeScheduleResponse";

        private TimeSchedule() {
        }
    }

    //获取医生排班列表信息
    public static class DoctorScheduleList {
        public static final String REQUEST_TOPIC_NAME = "DoctorScheduleRequest";
        public static final String RESPONSE_TOPIC_NAME = "DoctorScheduleResponse";

        private DoctorScheduleList() {
        }
    }

    //锁号
    public static class LockNumber {
        public static final String REQUEST_TOPIC_NAME = "LockNumberRequest";
        public static final String RESPONSE_TOPIC_NAME = "LockNumberResponse";

        private LockNumber() {
        }
    }

    //取消锁号
    public static class CancelPreRegistration {
        public static final String REQUEST_TOPIC_NAME = "CancelPreRegistrationRequest";
        public static final String RESPONSE_TOPIC_NAME = "CancelPreRegistrationResponse";

        private CancelPreRegistration() {
        }
    }

    //挂号（下单）
    public static class Registered {
        public static final String REQUEST_TOPIC_NAME = "RegisteredRequest";
        public static final String RESPONSE_TOPIC_NAME = "RegisteredResponse";

        private Registered() {
        }
    }

    //挂号（云大医院下单）
    public static class KunmingMU1stRegistered {
        public static final String REQUEST_TOPIC_NAME = "KunmingMU1stRegisteredRequest";
        public static final String RESPONSE_TOPIC_NAME = "KunmingMU1stRegisteredResponse";

        private KunmingMU1stRegistered() {
        }
    }

    //挂号（昆华医院下单）
    public static class Yunnan1stRegistered {
        public static final String REQUEST_TOPIC_NAME = "Yunnan1stRegisteredRequest";
        public static final String RESPONSE_TOPIC_NAME = "Yunnan1stRegisteredResponse";

        private Yunnan1stRegistered() {
        }
    }

    //取消挂号
    public static class CancelRegistered {
        public static final String REQUEST_TOPIC_NAME = "CancelRegisteredRequest";
        public static final String RESPONSE_TOPIC_NAME = "CancelRegisteredResponse";

        private CancelRegistered() {
        }
    }

    // 创建预约挂号订单
    public static class RegistrationOrderCreated {
        public static final String REQUEST_TOPIC_NAME = "RegistrationOrderCreatedRequest";
        public static final String RESPONSE_TOPIC_NAME = "RegistrationOrderCreatedResponse";

        private RegistrationOrderCreated() {
        }
    }


    // 创建预约挂号订单的冷数据
    public static class OrderAppointmentColdCreated {
        public static final String REQUEST_TOPIC_NAME = "OrderAppointmentColdCreatedRequest";
        public static final String RESPONSE_TOPIC_NAME = "OrderAppointmentColdCreatedResponse";

        private OrderAppointmentColdCreated() {
        }
    }

    // 支付通知
    public static class RegistrationOrderWaitPayment {
        public static final String REQUEST_TOPIC_NAME = "RegistrationOrderWaitPaymentRequest";
        public static final String RESPONSE_TOPIC_NAME = "RegistrationOrderWaitPaymentResponse";

        private RegistrationOrderWaitPayment() {
        }
    }

    // 预约、挂号His下单失败通知
    public static class RegistrationHisOrderFailed {
        public static final String REQUEST_TOPIC_NAME = "RegistrationHisOrderFailedRequest";
        public static final String RESPONSE_TOPIC_NAME = "RegistrationHisOrderFailedResponse";

        private RegistrationHisOrderFailed() {
        }
    }

    // 支付中心退费
    public static class RegistrationOrderWaitRefund {
        public static final String REQUEST_TOPIC_NAME = "RegistrationOrderWaitRefundRequest";
        public static final String RESPONSE_TOPIC_NAME = "RegistrationOrderWaitRefundResponse";

        private RegistrationOrderWaitRefund() {
        }
    }


    // 查询患者档案消息
    public static class QueryPatientArchives {
        public static final String REQUEST_TOPIC_NAME = "QueryPatientArchivesRequest";
        public static final String RESPONSE_TOPIC_NAME = "QueryPatientArchivesResponse";

        private QueryPatientArchives() {
        }
    }


    // 建档
    public static class CreateArchives {
        public static final String REQUEST_TOPIC_NAME = "CreateArchivesRequest";
        public static final String RESPONSE_TOPIC_NAME = "CreateArchivesResponse";

        private CreateArchives() {
        }
    }


    // 更新患者档案消息
    public static class UpdatePatientArchives {
        public static final String REQUEST_TOPIC_NAME = "UpdatePatientArchivesRequest";
        public static final String RESPONSE_TOPIC_NAME = "UpdatePatientArchivesResponse";

        private UpdatePatientArchives() {
        }
    }

    //通知HIS
    public static class NotifyHis {
        public static final String REQUEST_TOPIC_NAME = "NotifyHisRequest";
        public static final String RESPONSE_TOPIC_NAME = "NotifyHisResponse";

        private NotifyHis() {
        }
    }

    // 挂号（下单）结果核验
    public static class RegistrationResultCheck {
        public static final String REQUEST_TOPIC_NAME = "RegistrationResultCheckRequest";
        public static final String RESPONSE_TOPIC_NAME = "RegistrationResultCheckResponse";

        private RegistrationResultCheck() {
        }
    }


    // 医师资料同步
    public static class DoctorDataSync {
        public static final String REQUEST_TOPIC_NAME = "DoctorDataSyncRequest";
        public static final String RESPONSE_TOPIC_NAME = "DoctorDataSyncResponse";

        private DoctorDataSync() {
        }
    }

    // 只退款
    public static class JustRefund {
        public static final String REQUEST_TOPIC_NAME = "JustRefundRequest";
        public static final String RESPONSE_TOPIC_NAME = "JustRefundResponse";

        private JustRefund() {
        }
    }

    // 同步医生排班数据
    public static class SyncDoctorSchedule {
        public static final String REQUEST_TOPIC_NAME = "SyncDoctorScheduleRequest";
        public static final String RESPONSE_TOPIC_NAME = "SyncDoctorScheduleResponse";

        private SyncDoctorSchedule() {
        }
    }

    public static class ElectronicInvoice {
        public static final String REQUEST_TOPIC_NAME = "ElectronicInvoiceRequest";
        public static final String RESPONSE_TOPIC_NAME = "ElectronicInvoiceResponse";

        private ElectronicInvoice() {
        }
    }

}
