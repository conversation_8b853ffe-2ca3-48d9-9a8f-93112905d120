package com.ynhdkc.tenant.entity;

import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@NoArgsConstructor
@Table(name = "t_tenant_user_password_change_log")
public class TenantUserPasswordChangeLog extends BaseEntity {
    private Long tenantUserId;
    private String oldPassword;

    public TenantUserPasswordChangeLog(Long tenantUserId, String oldPassword) {
        this.tenantUserId = tenantUserId;
        this.oldPassword = oldPassword;
    }

}
