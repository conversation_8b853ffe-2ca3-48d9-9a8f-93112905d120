create table t_dict_file
(
    id             bigint auto_increment primary key comment '字典id',
    name           varchar(50)                              not null comment '字典名称',
    type           int                                      not null comment '字典类型:0,关键词;1,屏蔽词;',
    words          text                                     null comment '关键字集合',
    status         int         default 0                    not null comment '字典状态:0,启用;1,禁用;',
    cluster_status int         default 0                    not null comment '集群状态:0,未同步;1,已同步;',
    remark varchar(100) null comment '备注',
    create_time    datetime(3) default CURRENT_TIMESTAMP(3) not null,
    update_time    datetime(3)                              null on update CURRENT_TIMESTAMP(3),
    sync_time      datetime(3)                              null comment '同步时间',
    unique index uk_name_type (name, type)
);
