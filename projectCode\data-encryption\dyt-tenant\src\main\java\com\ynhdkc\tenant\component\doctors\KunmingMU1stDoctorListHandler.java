package com.ynhdkc.tenant.component.doctors;

import backend.common.exception.BizException;
import backend.common.util.MessageUtil;
import backend.common.util.ObjectsUtils;
import com.ynhdkc.tenant.client.model.DepartmentDoctorListEnvResponse;
import com.ynhdkc.tenant.dao.HospitalAreaSettingQuery;
import com.ynhdkc.tenant.doctor.sort.context.DoctorSortContext;
import com.ynhdkc.tenant.dto.ScheduleResponseDto;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.entity.DoctorGroupRelation;
import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.entity.model.DoctorScheduleModel;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import com.ynhdkc.tenant.model.CustomerAllScheduleDoctorDetailVo;
import com.ynhdkc.tenant.model.CustomerDateInWeekDto;
import com.ynhdkc.tenant.model.CustomerDoctorScheduleGroupedByDateVo;
import com.ynhdkc.tenant.model.CustomerDoctorScheduleInfoVo;
import com.ynhdkc.tenant.tool.DoctorSortUtils;
import com.ynhdkc.tenant.tool.SetDictInfoHelper;
import com.ynhdkc.tenant.util.DoctorUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.ynhdkc.tenant.tool.convert.DoctorConvert.toDoctorAllScheduleInfoVo;
import static java.util.Comparator.comparing;

@Slf4j
@Component
public class KunmingMU1stDoctorListHandler extends KunmingMU1stScheduleHandler {

    private static final String SPECIAL_DEPARTMENT_CODE = "7025";
    @Resource
    private DoctorSortContext doctorSortContext;
    @Resource
    private HospitalAreaSettingQuery hospitalAreaSettingQuery;
    @Resource
    private SetDictInfoHelper setDictInfoHelper;

    public List<ScheduleResponseDto.ScheduleInfo> queryScheduleInfo4Search(CustomerAllScheduleDoctorDetailVo vo) {
        if (vo == null) {
            return Collections.emptyList();
        }
        Department department = departmentQuery.queryDepartmentById(vo.getDepartmentId());
        if (department == null) {
            return Collections.emptyList();
        }
        List<Doctor> processedDoctors = new ArrayList<>();
        Doctor doctor = new Doctor();
        BeanUtils.copyProperties(vo, doctor);
        processedDoctors.add(doctor);

        log.info("queryScheduleInfo4Search_doctor: {}", MessageUtil.object2JSONString(doctor));

        Map<String, Department> execDepartmentMap = new HashMap<>();
        execDepartmentMap.put(department.getThrdpartDepCode(), department);

        List<ScheduleResponseDto.ScheduleInfo> scheduleInfoList = new ArrayList<>();
        try {
            DepartmentDoctorListEnvResponse response = makeRequest(department);
            if (response == null) {
                return Collections.emptyList();
            }
            scheduleInfoList = toScheduleInfo(response, processedDoctors, execDepartmentMap);
            scheduleInfoList = scheduleInfoList.stream().filter(y -> y.getDoctorCode().equals(vo.getThrdpartDoctorCode())).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("doctor_toScheduleInfo_error", e);
        }
        if (ObjectsUtils.isEmpty(scheduleInfoList)) {
            hisGatewayService.cacheEmptyDoctorScheduleInfo(vo.getHospitalAreaCode(), department, doctor);
        } else {
            hisGatewayService.cacheDoctorScheduleInfo(scheduleInfoList, department);
        }
        return scheduleInfoList;
    }

    public CustomerDoctorScheduleInfoVo queryGroupedDoctorListByDepartmentId(Department department) {
        Optional<AppointmentRuleSetting> ruleSettingOptional = hospitalAreaSettingQuery.queryAppointmentRuleSettingBy(department.getHospitalAreaId());
        if (!ruleSettingOptional.isPresent()) {
            throw new BizException(HttpStatus.NOT_FOUND, "预约规则不存在");
        }

        AppointmentRuleSetting appointmentRuleSetting = ruleSettingOptional.get();
        List<Doctor> doctors = queryDoctorListByDeptId(department);
        DoctorScheduleModel doctorScheduleModel = processScheduleInfo(department, doctors, appointmentRuleSetting);
        List<ScheduleResponseDto.ScheduleInfo> scheduleInfoList = doctorScheduleModel.getScheduleInfoList();
        doctors = doctorScheduleModel.getDoctors();
        if (CollectionUtils.isEmpty(doctors)) {
            log.info("doctors is empty, request his, hospital code {}, department code {},department info {}", department.getHospitalCode(), department.getThrdpartDepCode(), department);
            return new CustomerDoctorScheduleInfoVo();
        }

        Integer displayDoctorUnderDepartment = ruleSettingOptional.map(AppointmentRuleSetting::getDisplayDoctorUnderDepartment).orElse(0);

        List<DoctorGroupRelation> doctorGroupRelations = getDoctorGroupRelations(doctors.stream().map(Doctor::getId).collect(Collectors.toList()));

        CustomerDoctorScheduleInfoVo vo = new CustomerDoctorScheduleInfoVo();
        Set<LocalDate> scheduleDates = getScheduleDate(doctors, department, appointmentRuleSetting);
        doctorSortContext.sortDoctors(doctors, department.getHospitalCode());

        List<CustomerAllScheduleDoctorDetailVo> customerAllScheduleDoctorDetailVos = compositeAllDoctor(doctors, scheduleInfoList,
                department, doctorGroupRelations, displayDoctorUnderDepartment);


        removedDoctorCodeDuplicateAndNoScheduleDoctor(customerAllScheduleDoctorDetailVos);

        vo.setGroupedDoctors(compositeCustomDoctorInGroup(scheduleDates, customerAllScheduleDoctorDetailVos, scheduleInfoList,
                doctorGroupRelations, appointmentRuleSetting, department.getHospitalCode()));

        if (!ObjectsUtils.isEmpty(customerAllScheduleDoctorDetailVos)) {
            customerAllScheduleDoctorDetailVos.forEach(y -> y.setRequestDepartmentId(department.getId()));
        }
        vo.setAllDoctors(customerAllScheduleDoctorDetailVos);

        vo.setSystemDepends(ruleSettingOptional.map(AppointmentRuleSetting::getSystemDepends).orElse(null));
        DoctorUtils.setDoctorTitle(ruleSettingOptional.get(), vo.getAllDoctors());
        vo.getGroupedDoctors().forEach(v -> DoctorUtils.setDoctorTitle(ruleSettingOptional.get(), v.getDoctors()));
        return vo;
    }

    public CustomerDoctorScheduleInfoVo queryGroupedDoctorListByHospitalAreaId(Hospital hospitalArea) {
        Department department = getSpecialDepartment(hospitalArea.getId());
        Long hospitalAreaId = hospitalArea.getId();
        Optional<AppointmentRuleSetting> ruleSettingOptional = hospitalAreaSettingQuery.queryAppointmentRuleSettingBy(hospitalAreaId);
        if (!ruleSettingOptional.isPresent()) {
            throw new BizException(HttpStatus.NOT_FOUND, "预约规则不存在");
        }

        AppointmentRuleSetting appointmentRuleSetting = ruleSettingOptional.get();
        List<Doctor> doctors = doctorQuery.queryDoctorsByHospitalAreaIdAndDepartmentCode(hospitalAreaId, SPECIAL_DEPARTMENT_CODE);
        doctors = new ArrayList<>(doctors.stream()
                .collect(Collectors.groupingBy(
                        Doctor::getThrdpartDoctorCode,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .filter(d -> d.getCategory() != null && d.getCategory().contains("5"))
                                        .findFirst()
                                        .orElse(list.get(0))
                        )
                )).values());
        log.debug("queryGroupedDoctorListByHospitalAreaId_1: {}", doctors.size());

        DoctorScheduleModel doctorScheduleModel = processScheduleInfo(department, doctors, appointmentRuleSetting);
        List<ScheduleResponseDto.ScheduleInfo> scheduleInfoList = doctorScheduleModel.getScheduleInfoList();
        if (CollectionUtils.isEmpty(doctors)) {
            log.error("doctors is empty, request his, hospital code {}, department code {},department info {}", SPECIAL_DEPARTMENT_CODE, department.getThrdpartDepCode(), department);
            return new CustomerDoctorScheduleInfoVo();
        }

        Integer displayDoctorUnderDepartment = ruleSettingOptional.map(AppointmentRuleSetting::getDisplayDoctorUnderDepartment).orElse(0);
        List<DoctorGroupRelation> doctorGroupRelations = getDoctorGroupRelations(doctors.stream().map(Doctor::getId).collect(Collectors.toList()));
        CustomerDoctorScheduleInfoVo vo = new CustomerDoctorScheduleInfoVo();
        Set<LocalDate> scheduleDates = getScheduleDate(doctors, department, appointmentRuleSetting);
        doctors.sort(Comparator.comparing(Doctor::getSort).reversed());

        log.debug("queryGroupedDoctorListByHospitalAreaId_2: {}", MessageUtil.object2JSONString(doctorGroupRelations));

        List<CustomerAllScheduleDoctorDetailVo> customerAllScheduleDoctorDetailVos = compositeAllDoctor(doctors, scheduleInfoList, department, doctorGroupRelations, displayDoctorUnderDepartment);

        List<CustomerDoctorScheduleGroupedByDateVo> customerDoctorScheduleGroupedByDateVos = compositeCustomDoctorInGroup(scheduleDates, customerAllScheduleDoctorDetailVos, scheduleInfoList, doctorGroupRelations, appointmentRuleSetting, department.getHospitalCode());
        vo.setGroupedDoctors(customerDoctorScheduleGroupedByDateVos);


        removedDoctorCodeDuplicateAndNoScheduleDoctor(customerAllScheduleDoctorDetailVos);
        DoctorSortUtils.sortDoctorWithVO(customerAllScheduleDoctorDetailVos);
        vo.setAllDoctors(customerAllScheduleDoctorDetailVos);

        return vo;
    }

    public List<CustomerAllScheduleDoctorDetailVo> compositeAllDoctor(List<Doctor> doctors,
                                                                      List<ScheduleResponseDto.ScheduleInfo> scheduleInfos,
                                                                      Department department,
                                                                      List<DoctorGroupRelation> doctorGroupRelations,
                                                                      Integer displayDoctorUnderDepartment) {

        log.debug("compositeAllDoctor 1: {}", MessageUtil.object2JSONString(doctors));
        log.debug("compositeAllDoctor 2: {}", MessageUtil.object2JSONString(scheduleInfos));

        return doctors.stream().parallel().map(doctor -> {
            CustomerAllScheduleDoctorDetailVo vo = toDoctorAllScheduleInfoVo(doctor);
            setDictInfoHelper.setDictInfo(doctor, vo);

            if (enrichCustomerAllScheduleDoctorDetailVo(scheduleInfos, department.getName(), doctorGroupRelations, displayDoctorUnderDepartment, vo)) {
                return null;
            }
            return vo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public boolean enrichCustomerAllScheduleDoctorDetailVo(List<ScheduleResponseDto.ScheduleInfo> scheduleInfoList, String departmentName, List<DoctorGroupRelation> doctorGroupRelations, Integer displayDoctorUnderDepartment, CustomerAllScheduleDoctorDetailVo vo) {
        if (displayDoctorUnderDepartment == 1) {
            List<ScheduleResponseDto.ScheduleInfo> tmpScheduleList = scheduleInfoList.stream().filter(scheduleInfo -> scheduleInfo.getDoctorCode().equals(vo.getThrdpartDoctorCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(tmpScheduleList)) {
                return true;
            }
        }
        List<ScheduleResponseDto.ScheduleInfo> doctorScheduleInfoList = scheduleInfoList.stream().filter(scheduleInfo -> filterScheduleDoctor(scheduleInfo, vo)).collect(Collectors.toList());

        vo.setDateInWeeks(doctorScheduleInfoList.stream().map(this::scheduleInfoToDateInWeek).collect(Collectors.toSet()).stream().sorted(comparing(CustomerDateInWeekDto::getLocalDate)).collect(Collectors.toList()));
        vo.setShowSchDate(doctorScheduleInfoList.stream().anyMatch(scheduleInfo -> scheduleInfo.getShowSchDate() == 1));
        vo.setCanOrder(vo.getDateInWeeks().stream().anyMatch(CustomerDateInWeekDto::isCanOrder));

        Map<String, String> departmentCodeAndDepartmentName = calculateDepartmentNameMap(scheduleInfoList);
        vo.setDepartmentName(getDepartmentName(departmentCodeAndDepartmentName, vo.getDepartmentCode(), departmentName));
        vo.setBelongDoctorGroups(doctorGroupRelations.stream().anyMatch(q -> q.getDoctorId().equals(vo.getId())));
        return false;
    }

    private List<DoctorGroupRelation> getDoctorGroupRelations(List<Long> doctorIds) {
        return doctorQuery.listDoctorGroupRelationBy(doctorIds);
    }

    public void removedDoctorCodeDuplicateAndNoScheduleDoctor(List<CustomerAllScheduleDoctorDetailVo> customerAllScheduleDoctorDetailVos) {
        List<CustomerAllScheduleDoctorDetailVo> temp = new ArrayList<>();
        customerAllScheduleDoctorDetailVos.forEach(q -> {
            if (!CollectionUtils.isEmpty(q.getDateInWeeks())) {
                temp.add(q);
            }
        });
        customerAllScheduleDoctorDetailVos.clear();
        customerAllScheduleDoctorDetailVos.addAll(temp);
    }

    private Map<String, String> calculateDepartmentNameMap(List<ScheduleResponseDto.ScheduleInfo> scheduleInfos) {
        Map<String, String> departmentCodeAndDepartmentName = new HashMap<>();
        scheduleInfos.stream().filter(q -> q.getExecDepartmentCode() != null && q.getDepartmentName() != null).forEach(scheduleInfo -> {
            if (!departmentCodeAndDepartmentName.containsKey(scheduleInfo.getExecDepartmentCode())) {
                departmentCodeAndDepartmentName.put(scheduleInfo.getExecDepartmentCode(), scheduleInfo.getDepartmentName());
            }
        });
        return departmentCodeAndDepartmentName;
    }

    private String getDepartmentName(Map<String, String> departmentNameMap, String departmentCode, String departmentNameFromDepartment) {

        log.debug("get_department_name 1: {}", MessageUtil.object2JSONString(departmentNameMap));
        log.debug("get_department_name 2: {}", departmentCode);
        log.debug("get_department_name 3: {}", MessageUtil.object2JSONString(departmentNameFromDepartment));

        if (ObjectsUtils.isEmpty(departmentNameMap)) {
            return departmentNameFromDepartment;
        }

        String departmentName = departmentNameMap.get(departmentCode);
        if (departmentName == null) {
            return departmentNameFromDepartment;
        }
        return departmentName;
    }

    private Department getSpecialDepartment(Long hospitalAreaId) {
        Set<String> departmentCodes = new HashSet<>();
        departmentCodes.add(SPECIAL_DEPARTMENT_CODE);
        List<Department> departmentList = departmentQuery.queryDepartments(hospitalAreaId, departmentCodes);
        Department department = departmentList.stream().filter(d -> d.getThrdpartDepCode().equals(SPECIAL_DEPARTMENT_CODE)).findFirst().orElse(null);
        if (null == department) {
            throw new BizException(HttpStatus.NOT_FOUND, "医院特需门诊科室不存在");
        }
        department.setDoctors(departmentList.stream().map(Department::getDoctors).collect(Collectors.joining("|")));
        department.setId(Long.parseLong(SPECIAL_DEPARTMENT_CODE));
        return department;
    }
}
