package com.ynhdkc.tenant.dao.impl;

import com.ynhdkc.tenant.dao.AreaRepository;
import com.ynhdkc.tenant.dao.mapper.AreaMapper;
import com.ynhdkc.tenant.entity.Area;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-04 13:12
 */
@Repository
@RequiredArgsConstructor
public class AreaRepositoryImpl implements AreaRepository {
    private final AreaMapper areaMapper;

    @Override
    public void create(Area area) {
        areaMapper.insertSelective(area);
    }

    @Override
    public void update(Area area) {
        areaMapper.updateByPrimaryKeySelective(area);
    }

    @Override
    public void delete(Long areaId) {
        areaMapper.deleteByPrimaryKey(areaId);
    }
}
