package com.ynhdkc.tenant.encryption;

import backend.encryption.util.EncryptionUtil;
import com.ynhdkc.tenant.dao.mapper.TenantMapper;
import com.ynhdkc.tenant.dao.mapper.TenantUserMapper;
import com.ynhdkc.tenant.entity.Tenant;
import com.ynhdkc.tenant.entity.TenantUser;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据加密功能集成测试
 * 
 * 测试内容：
 * 1. 加密工具类基本功能
 * 2. TenantUser实体加密功能
 * 3. Tenant实体加密功能
 * 4. 影子字段数据存储验证
 * 5. 批量操作加密功能
 * 6. 性能测试
 * 
 * <AUTHOR> Backend Team
 * @version 1.1-SNAPSHOT
 * @since 2024-06-24
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("encrypted")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class DataEncryptionIntegrationTest {

    @Autowired
    private TenantUserMapper tenantUserMapper;
    
    @Autowired
    private TenantMapper tenantMapper;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @BeforeEach
    void setUp() {
        log.info("=== 开始执行加密功能测试 ===");
    }

    @AfterEach
    void tearDown() {
        log.info("=== 测试执行完成 ===\n");
    }

    /**
     * 测试1: 加密工具类基本功能
     */
    @Test
    @Order(1)
    void testEncryptionUtilBasicFunctions() {
        log.info("测试1: 加密工具类基本功能");
        
        String[] testData = {
            "13800138000",
            "<EMAIL>", 
            "张三",
            "Test User Name",
            "复杂的中英文混合数据123!@#"
        };
        
        for (String original : testData) {
            // 测试加密
            String encrypted = EncryptionUtil.encrypt(original);
            assertNotNull(encrypted, "加密结果不应为空");
            assertNotEquals(original, encrypted, "加密后数据应与原文不同");
            assertTrue(encrypted.length() > original.length(), "加密后数据长度应大于原文");
            
            // 测试解密
            String decrypted = EncryptionUtil.decrypt(encrypted);
            assertEquals(original, decrypted, "解密后数据应与原文相同");
            
            log.debug("原文: {} -> 密文长度: {} -> 解密: {}", original, encrypted.length(), decrypted);
        }
        
        // 测试空值处理
        assertNull(EncryptionUtil.encrypt(null), "null值加密应返回null");
        assertEquals("", EncryptionUtil.encrypt(""), "空字符串加密应返回空字符串");
        
        log.info("✓ 加密工具类基本功能测试通过");
    }

    /**
     * 测试2: TenantUser实体加密功能
     */
    @Test
    @Order(2)
    @Transactional
    void testTenantUserEncryption() {
        log.info("测试2: TenantUser实体加密功能");
        
        // 创建测试用户
        TenantUser user = createTestTenantUser("加密测试用户", "13900139001", "<EMAIL>");
        
        // 保存用户
        tenantUserMapper.insertSelective(user);
        assertNotNull(user.getId(), "用户ID应该被自动生成");
        log.info("创建用户成功，ID: {}", user.getId());
        
        // 重新查询验证解密功能
        TenantUser savedUser = tenantUserMapper.selectByPrimaryKey(user.getId());
        assertNotNull(savedUser, "应该能查询到保存的用户");
        
        // 验证敏感字段正确解密
        assertEquals("加密测试用户", savedUser.getName(), "用户名应该正确解密");
        assertEquals("13900139001", savedUser.getPhoneNumber(), "手机号应该正确解密");
        assertEquals("<EMAIL>", savedUser.getEmail(), "邮箱应该正确解密");
        
        // 验证影子字段数据存储
        verifyTenantUserShadowFields(user.getId());
        
        log.info("✓ TenantUser实体加密功能测试通过");
    }

    /**
     * 测试3: Tenant实体加密功能
     */
    @Test
    @Order(3)
    @Transactional
    void testTenantEncryption() {
        log.info("测试3: Tenant实体加密功能");
        
        // 创建测试租户
        Tenant tenant = createTestTenant("加密测试租户", "13800138001", "<EMAIL>");
        
        // 保存租户
        tenantMapper.insertSelective(tenant);
        assertNotNull(tenant.getId(), "租户ID应该被自动生成");
        log.info("创建租户成功，ID: {}", tenant.getId());
        
        // 重新查询验证解密功能
        Tenant savedTenant = tenantMapper.selectByPrimaryKey(tenant.getId());
        assertNotNull(savedTenant, "应该能查询到保存的租户");
        
        // 验证敏感字段正确解密
        assertEquals("13800138001", savedTenant.getContactPhoneNumber(), "联系人手机号应该正确解密");
        assertEquals("<EMAIL>", savedTenant.getContactEmail(), "联系人邮箱应该正确解密");
        
        // 验证影子字段数据存储
        verifyTenantShadowFields(tenant.getId());
        
        log.info("✓ Tenant实体加密功能测试通过");
    }

    /**
     * 测试4: 更新操作加密功能
     */
    @Test
    @Order(4)
    @Transactional
    void testUpdateOperationEncryption() {
        log.info("测试4: 更新操作加密功能");
        
        // 创建初始用户
        TenantUser user = createTestTenantUser("原始用户", "13900139002", "<EMAIL>");
        tenantUserMapper.insertSelective(user);
        Long userId = user.getId();
        
        // 更新用户信息
        TenantUser updateUser = new TenantUser();
        updateUser.setId(userId);
        updateUser.setName("更新后用户");
        updateUser.setPhoneNumber("13900139003");
        updateUser.setEmail("<EMAIL>");
        
        tenantUserMapper.updateByPrimaryKeySelective(updateUser);
        
        // 验证更新后的数据
        TenantUser updatedUser = tenantUserMapper.selectByPrimaryKey(userId);
        assertEquals("更新后用户", updatedUser.getName(), "更新后的用户名应该正确解密");
        assertEquals("13900139003", updatedUser.getPhoneNumber(), "更新后的手机号应该正确解密");
        assertEquals("<EMAIL>", updatedUser.getEmail(), "更新后的邮箱应该正确解密");
        
        log.info("✓ 更新操作加密功能测试通过");
    }

    /**
     * 测试5: 批量操作加密功能
     */
    @Test
    @Order(5)
    @Transactional
    void testBatchOperationEncryption() {
        log.info("测试5: 批量操作加密功能");
        
        // 创建多个测试用户
        for (int i = 1; i <= 5; i++) {
            TenantUser user = createTestTenantUser(
                "批量用户" + i, 
                "1390013900" + i, 
                "batch" + i + "@example.com"
            );
            tenantUserMapper.insertSelective(user);
        }
        
        // 查询所有测试用户
        List<TenantUser> users = jdbcTemplate.query(
            "SELECT * FROM t_tenant_user WHERE name LIKE '批量用户%'",
            (rs, rowNum) -> {
                TenantUser user = new TenantUser();
                user.setId(rs.getLong("id"));
                user.setName(rs.getString("name"));
                user.setPhoneNumber(rs.getString("phone_number"));
                user.setEmail(rs.getString("email"));
                return user;
            }
        );
        
        assertEquals(5, users.size(), "应该查询到5个批量创建的用户");
        
        // 验证每个用户的数据都正确解密
        for (int i = 0; i < users.size(); i++) {
            TenantUser user = users.get(i);
            assertTrue(user.getName().startsWith("批量用户"), "用户名应该正确解密");
            assertTrue(user.getPhoneNumber().startsWith("1390013900"), "手机号应该正确解密");
            assertTrue(user.getEmail().startsWith("batch"), "邮箱应该正确解密");
        }
        
        log.info("✓ 批量操作加密功能测试通过");
    }

    /**
     * 验证TenantUser影子字段数据存储
     */
    private void verifyTenantUserShadowFields(Long userId) {
        log.debug("验证TenantUser影子字段数据存储，用户ID: {}", userId);
        
        List<Map<String, Object>> results = jdbcTemplate.queryForList(
            "SELECT name, name_encrypted, phone_number, phone_number_encrypted FROM t_tenant_user WHERE id = ?",
            userId
        );
        
        assertFalse(results.isEmpty(), "应该能查询到用户数据");
        
        Map<String, Object> row = results.get(0);
        String name = (String) row.get("name");
        String nameEncrypted = (String) row.get("name_encrypted");
        String phoneNumber = (String) row.get("phone_number");
        String phoneNumberEncrypted = (String) row.get("phone_number_encrypted");
        
        log.debug("数据库原始数据 - 姓名: {}, 姓名加密: {}, 手机: {}, 手机加密: {}", 
                name, nameEncrypted, phoneNumber, phoneNumberEncrypted);
        
        // 注意：根据影子字段策略，可能原字段或影子字段存储加密数据
        // 这里主要验证数据存储的一致性
        assertNotNull(name, "姓名字段应该存在");
        assertNotNull(phoneNumber, "手机号字段应该存在");
    }

    /**
     * 验证Tenant影子字段数据存储
     */
    private void verifyTenantShadowFields(Long tenantId) {
        log.debug("验证Tenant影子字段数据存储，租户ID: {}", tenantId);
        
        List<Map<String, Object>> results = jdbcTemplate.queryForList(
            "SELECT contact_phone_number, contact_phone_number_encrypted, contact_email, contact_email_encrypted FROM t_tenant WHERE id = ?",
            tenantId
        );
        
        assertFalse(results.isEmpty(), "应该能查询到租户数据");
        
        Map<String, Object> row = results.get(0);
        String contactPhone = (String) row.get("contact_phone_number");
        String contactPhoneEncrypted = (String) row.get("contact_phone_number_encrypted");
        String contactEmail = (String) row.get("contact_email");
        String contactEmailEncrypted = (String) row.get("contact_email_encrypted");
        
        log.debug("数据库原始数据 - 联系电话: {}, 联系电话加密: {}, 联系邮箱: {}, 联系邮箱加密: {}", 
                contactPhone, contactPhoneEncrypted, contactEmail, contactEmailEncrypted);
        
        assertNotNull(contactPhone, "联系电话字段应该存在");
        assertNotNull(contactEmail, "联系邮箱字段应该存在");
    }

    /**
     * 创建测试TenantUser
     */
    private TenantUser createTestTenantUser(String name, String phone, String email) {
        TenantUser user = new TenantUser();
        user.setName(name);
        user.setPhoneNumber(phone);
        user.setEmail(email);
        user.setNickname("测试昵称");
        user.setPassword("$2a$10$test.password.hash");
        user.setAdmin(false);
        return user;
    }

    /**
     * 测试6: 性能测试
     */
    @Test
    @Order(6)
    @Transactional
    void testEncryptionPerformance() {
        log.info("测试6: 加密功能性能测试");

        int testCount = 100;
        long startTime, endTime;

        // 测试加密性能
        startTime = System.currentTimeMillis();
        for (int i = 0; i < testCount; i++) {
            String testData = "性能测试数据" + i + "_13800138" + String.format("%03d", i);
            EncryptionUtil.encrypt(testData);
        }
        endTime = System.currentTimeMillis();
        long encryptTime = endTime - startTime;

        // 测试数据库操作性能
        startTime = System.currentTimeMillis();
        for (int i = 0; i < 10; i++) { // 减少数据库操作次数
            TenantUser user = createTestTenantUser(
                "性能测试用户" + i,
                "1380013800" + i,
                "perf" + i + "@example.com"
            );
            tenantUserMapper.insertSelective(user);
        }
        endTime = System.currentTimeMillis();
        long dbTime = endTime - startTime;

        log.info("性能测试结果:");
        log.info("- {}次加密操作耗时: {}ms, 平均: {}ms/次", testCount, encryptTime, encryptTime / testCount);
        log.info("- 10次数据库加密存储耗时: {}ms, 平均: {}ms/次", dbTime, dbTime / 10);

        // 性能断言（根据实际情况调整阈值）
        assertTrue(encryptTime < 5000, "加密操作性能应该在可接受范围内");
        assertTrue(dbTime < 10000, "数据库加密存储性能应该在可接受范围内");

        log.info("✓ 加密功能性能测试通过");
    }

    /**
     * 创建测试Tenant
     */
    private Tenant createTestTenant(String name, String contactPhone, String contactEmail) {
        Tenant tenant = new Tenant();
        tenant.setName(name);
        tenant.setDescription("测试租户描述");
        tenant.setContact("测试联系人");
        tenant.setContactPhoneNumber(contactPhone);
        tenant.setContactEmail(contactEmail);
        tenant.setAddressId(1001L); // 使用addressId而不是address
        tenant.setLogoUrl("http://example.com/logo.png");
        return tenant;
    }
}
