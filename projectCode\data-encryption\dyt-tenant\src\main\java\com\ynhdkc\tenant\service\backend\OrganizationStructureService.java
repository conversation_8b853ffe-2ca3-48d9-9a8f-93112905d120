package com.ynhdkc.tenant.service.backend;

import com.ynhdkc.tenant.model.UserTenantPrivilegeConfig;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-25 13:28
 */
public interface OrganizationStructureService {
    /**
     * 同步组织架构
     */
    void syncOrganizationStructure();

    /**
     * 同步所有用户组织架构
     */
    void syncUserOrganizationStructure();

    /**
     * 同步用户组织架构
     *
     * @param userId 用户id
     */
    void syncUserOrganizationStructure(Long userId);

    /**
     * 同步删除用户组织架构
     *
     * @param userId 用户id
     */
    void syncDeleteUserOrganizationStructure(Long userId);

    /**
     * 创建或者更新用户的角色和组织架构，角色是增量更新，组织架构是覆盖更新。
     *
     * @param userId        用户id
     * @param tenantConfigs 租户权限配置
     */
    void createOrUpdateUserStructure(Long userId, List<UserTenantPrivilegeConfig> tenantConfigs);

    /**
     * 异步同步用户组织架构和用户
     */
    void asyncUserOrganizationStructureAndUser();

    /**
     * 异步同步用户组织架构
     *
     * @param userId 用户id
     */
    void asyncUserOrganizationStructureAndUser(Long userId);

    /**
     * 异步同步删除用户组织架构
     *
     * @param userId 用户id
     */
    void asyncDeleteUserOrganizationStructure(Long userId);
}
