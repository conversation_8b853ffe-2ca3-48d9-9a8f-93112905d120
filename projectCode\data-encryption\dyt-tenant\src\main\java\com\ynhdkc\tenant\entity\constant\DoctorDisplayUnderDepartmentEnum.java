package com.ynhdkc.tenant.entity.constant;

/**
 * <AUTHOR>
 * @since 2023/7/11 14:59:02
 */
public enum DoctorDisplayUnderDepartmentEnum {

    DISPLAY_ALL(0, "全部"),

    SCHEDULED_DOCTOR(1, "有排班的医生");

    private final Integer code;

    private final String desc;

    DoctorDisplayUnderDepartmentEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DoctorDisplayUnderDepartmentEnum fromCode(Integer code) {
        for (DoctorDisplayUnderDepartmentEnum value : DoctorDisplayUnderDepartmentEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
