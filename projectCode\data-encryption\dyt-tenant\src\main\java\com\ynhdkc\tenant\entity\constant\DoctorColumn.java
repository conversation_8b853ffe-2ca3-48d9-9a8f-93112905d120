package com.ynhdkc.tenant.entity.constant;

import lombok.Getter;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/11/22 13:54
 */
@Getter
public enum DoctorColumn {
    INTRODUCTION(0, "简介", "introduction"),
    HONOR(1, "荣誉", "honor"),
    SPECIALITY(2, "擅长", "speciality"),
    STATEMENT(3, "简介", "statement"),
    HEAD_IMG_URL(4, "头像URL", "head_img_url"),
    NAME(5, "医生名称", "name"),
    RANK_DICT_TYPE(6, "医生职称类型", "rank_dict_type"),
    RANK_DICT_LABEL(7, "医生职称标签", "rank_dict_label"),
    RANK_DICT_VALUE(8, "医生职称值", "rank_dict_value"),
    SORT(9, "排序", "sort"),
    ;

    private final Integer code;
    private final String desc;
    private final String column;
    DoctorColumn(Integer code, String desc, String column) {
        this.code = code;
        this.desc = desc;
        this.column = column;
    }

    public static boolean contains(Integer code) {
        if (code == null) {
            return false;
        }
        for (DoctorColumn value : DoctorColumn.values()) {
            if (value.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

    public static DoctorColumn of(Integer code) {
        if (code == null) {
            return null;
        }
        for (DoctorColumn value : DoctorColumn.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static List<Integer> getCodesByColumns(String updateDoctorExcludeColumns) {
        if (!StringUtils.hasText(updateDoctorExcludeColumns)) {
            return Collections.emptyList();
        }
        Set<String> columns = Arrays.stream(updateDoctorExcludeColumns.split(",")).collect(Collectors.toSet());
        return Arrays.stream(DoctorColumn.values())
                .filter(column -> columns.contains(column.getColumn()))
                .map(DoctorColumn::getCode)
                .collect(Collectors.toList());
    }

    public static String getMergedColumnsByCodes(List<Integer> updateDoctorExcludeColumns) {
        if (CollectionUtils.isEmpty(updateDoctorExcludeColumns)) {
            return "";
        }
        return updateDoctorExcludeColumns.stream()
                .map(DoctorColumn::of)
                .filter(Objects::nonNull)
                .map(DoctorColumn::getColumn)
                .collect(Collectors.joining(","));
    }
}
