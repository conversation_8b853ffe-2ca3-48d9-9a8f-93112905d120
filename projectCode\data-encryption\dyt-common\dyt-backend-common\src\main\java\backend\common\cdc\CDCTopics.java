package backend.common.cdc;

public interface CDCTopics {
    /**
     * debezium 输出的kafka topic名字 分为3部分
     * 1. backend debezium的中的配置名称
     * 2. backend_things 数据库名
     * 3. t_things 表名
     *
     */
    String BACKEND_TENANT = "backend-backend_tenant-t_tenant";


    String BACKEND_HOSPITAL = "backend-backend_tenant-t_hospital";
    String BACKEND_DOCTOR = "backend-backend_tenant-t_doctor";
    String BACKEND_FLOOR_AREA = "backend-backend_tenant-t_floor_area";
    String BACKEND_HOSPITAL_BUILDING = "backend-backend_tenant-t_hospital_building";
    String BACKEND_DEPARTMENT = "backend-backend_tenant-t_hospital_department";

}
