package com.ynhdkc.tenant.api.customer;

import backend.security.service.BackendClientUserService;
import com.ynhdkc.tenant.handler.CustomerQuickCheckApi;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.customer.CustomerQuickCheckService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/7/21 9:42
 */
@Slf4j
@Api(tags = "CustomerDepartment")
@RestController
@RequiredArgsConstructor
public class CustomerQuickCheckController implements CustomerQuickCheckApi {
    private final CustomerQuickCheckService customerQuickCheckService;

    private final BackendClientUserService backendClientUserService;

    @Override
    public ResponseEntity<CustomerQuickCheckDoctorPageVo> queryDoctors(CustomerQuickCheckQueryDoctorsPageReqDto request) {
        List<CustomerQuickCheckDoctorVo> list = customerQuickCheckService.queryDoctors(request);
        CustomerQuickCheckDoctorPageVo reponse = new CustomerQuickCheckDoctorPageVo();
        reponse.setCurrentPage(request.getCurrentPage());
        reponse.setPageSize(request.getPageSize());
        reponse.setList(list);
        return ResponseEntity.ok(reponse);
    }

    @Override
    public ResponseEntity<CustomerQuickCheckDepartmentReponse> queryGroupedDepartsAndDoctors(CustomerQuickCheckQueryDepartmentReqDto request) {
        List<CustomerQuickCheckDepartmentVo> list = customerQuickCheckService.queryGroupedDepartsAndDoctors(request);
        CustomerQuickCheckDepartmentReponse reponse = new CustomerQuickCheckDepartmentReponse();
        reponse.setList(list);
        return ResponseEntity.ok(reponse);
    }

    @Override
    public ResponseEntity<ExpertAppointmentDoctorPageVo> queryExpertAppointmentDoctors(List<String> displayTags, Integer current, Integer pageSize) {
        return ResponseEntity.ok(customerQuickCheckService.queryExpertAppointmentDoctors(displayTags, current, pageSize));
    }

    @Override
    public ResponseEntity<List<CustomerQuickCheckAllHospitalVo>> getAllHospital(Double longitude, Double latitude,Boolean hot) {
        return ResponseEntity.ok(customerQuickCheckService.getAllHospital(longitude, latitude,hot));
    }
}
