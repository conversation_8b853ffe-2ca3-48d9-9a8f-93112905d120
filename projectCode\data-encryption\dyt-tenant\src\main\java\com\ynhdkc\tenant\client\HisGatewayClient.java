package com.ynhdkc.tenant.client;

import com.ynhdkc.tenant.client.model.DepartmentDoctorListEnvRequest;
import com.ynhdkc.tenant.client.model.DepartmentDoctorListEnvResponse;
import com.ynhdkc.tenant.client.model.DepartmentList4MultiLevelResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = "dyt-his-gateway", path = "/apis/v1/his-gateway")
//@FeignClient(name = "dyt-his-gateway", url = "https://megacloud.ynhdkc.com", path = "/apis/v1/his-gateway", decode404 = true)
public interface HisGatewayClient {

    @PostMapping(value = "/base-service/hospital-code/{hospitalCode}/department-code/{departmentCode}/department-doctor-list")
    ResponseEntity<DepartmentDoctorListEnvResponse> getDoctors(@PathVariable("hospitalCode") String hospitalCode, @PathVariable("departmentCode") String departmentCode, @RequestBody DepartmentDoctorListEnvRequest request);

    @GetMapping("/maintenance/hospital-code/{hospitalCode}/department-code/{departmentCode}/sync-doctor-info")
    ResponseEntity<DepartmentDoctorListEnvResponse> syncDoctorInfo(@PathVariable("hospitalCode") String hospitalCode,
                                                                   @PathVariable("departmentCode") String departmentCode,
                                                                   @RequestParam("doctorCode") String doctorCode);

    @GetMapping("/base-service/hospital-code/{hospitalCode}/department-list-4-multi-level")
    ResponseEntity<DepartmentList4MultiLevelResponse> getDepartmentList4MultiLevel(@PathVariable("hospitalCode") String hospitalCode);

    @GetMapping("/base-service/hospital-code/{hospitalCode}/department-list-4-multi-level")
    ResponseEntity<DepartmentList4MultiLevelResponse> getDepartmentList4MultiLevel(@PathVariable("hospitalCode") String hospitalCode, @RequestParam("parentDepartmentCode") String parentDepartmentCode);
}
