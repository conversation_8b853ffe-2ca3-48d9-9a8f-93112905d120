package com.ynhdkc.tenant.util;

import backend.common.util.ObjectsUtils;
import cn.hutool.core.collection.CollUtil;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import com.ynhdkc.tenant.enums.DoctorTitleShowType;
import com.ynhdkc.tenant.model.CustomerDoctorVo;
import com.ynhdkc.tenant.model.DoctorVo;
import com.ynhdkc.tenant.tool.StringUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;
import org.springframework.util.ObjectUtils;

import java.util.*;

@Slf4j
public class DoctorUtils {

    private static final String DOCTOR_TITLE_DICT_TYPE = "doctor_title";

    private static final Map<String, Integer> DOCTOR_TITLE_TO_DICT_VALUE = new HashMap<String, Integer>() {{
        put("主任医师", 231);
        put("副主任医师", 232);
        put("主治医师", 233);
        put("医师", 234);
        put("医士", 235);
        put("主任药师", 241);
        put("副主任药师", 242);
        put("主管药师", 243);
        put("药师", 244);
        put("药士", 245);
        put("主任护师", 251);
        put("副主任护师", 252);
        put("主管护师", 253);
        put("护师", 254);
        put("护士", 255);
        put("主任技师", 261);
        put("副主任技师", 262);
        put("主管技师", 263);
        put("技师", 264);
        put("技士", 265);
    }};

    private static final Map<String, Integer> SPEC_TITLE = new HashMap<>();

    static {
        SPEC_TITLE.put("产科特需", 240);
        SPEC_TITLE.put("急诊", 800);
        SPEC_TITLE.put("普通号", 900);
        SPEC_TITLE.put("vip号", 901);
        SPEC_TITLE.put("普通", 902);
        SPEC_TITLE.put("普通(挂费)", 903);
    }


    private DoctorUtils() {
    }

    public static String getDoctorTitle(DoctorVo doctorVo) {
        Map<String, String> titleMap = getTitleMap(doctorVo);
        String[] doctorDicTypeList = getDoctorDicTypeList(doctorVo);
        List<String> doctorTitleList = new ArrayList<>();
        for (String doctorDicType : doctorDicTypeList) {
            String doctorTitle = titleMap.get(doctorDicType);
            if (doctorTitle != null) {
                doctorTitleList.add(doctorTitle);
            }
        }
        return StringUtils.join(doctorTitleList, ",");
    }

    private static Map<String, String> getTitleMap(DoctorVo doctorVo) {
        List<String> rankDictLabelList = doctorVo.getRankDictLabel();
        List<String> rankDictValueList = doctorVo.getRankDictValue();
        if (rankDictLabelList
                == null || rankDictValueList == null) {
            return Collections.emptyMap();
        }
        Map<String, String> titleMap = new HashMap<>();
        int length = Math.min(rankDictValueList.size(), rankDictLabelList.size());
        for (int idx = 0; idx < length; idx++) {
            titleMap.put(rankDictValueList.get(idx), rankDictLabelList.get(idx));
        }
        return titleMap;
    }

    private static String[] getDoctorDicTypeList(DoctorVo doctorVo) {
        if (StringUtils.isEmpty(doctorVo.getRankDictType())) {
            return new String[0];
        }
        return doctorVo.getRankDictType().split(",");
    }

    public static @Nullable DoctorTitleInfo getDoctorTitleInfo(@Nullable String title) {
        if (StringUtils.isEmpty(title)) {
            return null;
        }

        DoctorTitleInfo doctorTitleInfo = new DoctorTitleInfo();
        Integer dictValue = DOCTOR_TITLE_TO_DICT_VALUE.get(title);
        if (dictValue != null) {
            doctorTitleInfo.setDoctorRankType(DOCTOR_TITLE_DICT_TYPE);
            doctorTitleInfo.setDoctorRankDictValue(String.valueOf(dictValue));
            doctorTitleInfo.setDoctorRankDictLabel(title);
            return doctorTitleInfo;
        }
        return null;
    }

    public static Integer getTitleValue(String title) {
        if (ObjectsUtils.isEmpty(title)) {
            return Integer.MAX_VALUE;
        }
        Integer titleValue = SPEC_TITLE.get(title);
        if (titleValue != null) {
            return titleValue;
        }
        titleValue = DOCTOR_TITLE_TO_DICT_VALUE.get(title);
        if (titleValue == null) {
            return Integer.MAX_VALUE;
        }
        return titleValue;
    }

    public static void setDoctorTitle(@Nullable AppointmentRuleSetting ruleSetting, List<? extends CustomerDoctorVo> allDoctors) {
        if (CollUtil.isEmpty(allDoctors)) {
            return;
        }
        if (ruleSetting != null) {
            Integer doctorTitleShowType = ruleSetting.getDoctorTitleShowType();
            if (DoctorTitleShowType.FROM_SYSTEM.getValue().equals(doctorTitleShowType)) {
                allDoctors.forEach(v -> v.setDoctorTitle(CollUtil.isEmpty(v.getRankDictLabel()) ? "" : v.getRankDictLabel().get(0)));
            } else if (DoctorTitleShowType.FROM_HIS.getValue().equals(doctorTitleShowType)) {
                allDoctors.forEach(v -> v.setDoctorTitle(v.getRegistrationLevel()));
            } else if (DoctorTitleShowType.FROM_HONOR.getValue().equals(doctorTitleShowType)) {
                allDoctors.forEach(v -> v.setDoctorTitle(v.getHonor()));
            }
        }
    }

    public static boolean isCollectEveryDptDoctor(Department department, List<Doctor> departmentDirectDBDoctorList) {
        return ObjectUtils.isEmpty(department.getDoctors()) && ObjectUtils.isEmpty(departmentDirectDBDoctorList) &&
                HospitalUtils.isKunmingMU1stHospital(department.getHospitalCode());
    }

    @Data
    public static class DoctorTitleInfo {
        private String doctorRankType;
        private String doctorRankDictValue;
        private String doctorRankDictLabel;

    }

}
