package com.ynhdkc.tenant.service.backend.impl;

import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ynhdkc.tenant.dao.mapper.HospitalDetailPageCubeMapper;
import com.ynhdkc.tenant.dao.mapper.HospitalDetailPageCubeModuleMapper;
import com.ynhdkc.tenant.entity.detailpage.HospitalAreaDetailPageCube;
import com.ynhdkc.tenant.entity.detailpage.HospitalAreaDetailPageCubeModule;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.IHospitalAreaDetailPageCubeModuleService;
import com.ynhdkc.tenant.service.backend.IHospitalAreaDetailPageCubeService;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/24 18:52:13
 */
@Service
@RequiredArgsConstructor
public class HospitalAreaDetailPageCubeModuleServiceImpl implements IHospitalAreaDetailPageCubeModuleService {

    private final HospitalDetailPageCubeModuleMapper mapper;
    private final HospitalDetailPageCubeMapper cubeMapper;
    private final IHospitalAreaDetailPageCubeService hospitalDetailPageCubeService;
    private final PageVoConvert pageVoConvert;

    @Override
    public HospitalAreaDetailPageCubeModuleVo createHospitalAreaDetailPageCubeModule(CreateHospitalAreaDetailPageCubeModuleReqDto dto) {
        HospitalAreaDetailPageCubeModule hospitalAreaDetailPageCubeModule = IHospitalAreaDetailPageCubeModuleService.toEntity(dto);
        int effectRows = mapper.insertSelective(hospitalAreaDetailPageCubeModule);
        if (effectRows == 0) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "创建失败");
        }
        return IHospitalAreaDetailPageCubeModuleService.toVo(hospitalAreaDetailPageCubeModule);
    }

    @Override
    public BaseOperationResponse deleteHospitalAreaDetailPageCubeModule(Long id) {
        int effectRows = mapper.deleteByPrimaryKey(id);
        if (effectRows == 0) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "删除失败");
        }
        return BaseOperationResponse.builder().effectiveCount(effectRows).build();
    }

    @Override
    public HospitalAreaDetailPageCubeModuleVo getHospitalAreaDetailPageCubeModule(Long id) {
        HospitalAreaDetailPageCubeModule hospitalAreaDetailPageCubeModule = mapper.selectByPrimaryKey(id);
        if (hospitalAreaDetailPageCubeModule == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "查询失败");
        }
        HospitalAreaDetailPageCubeModuleVo vo = IHospitalAreaDetailPageCubeModuleService.toVo(hospitalAreaDetailPageCubeModule);
        vo.setCubeVo(hospitalDetailPageCubeService.getCubeVosByCubeModuleId(id));
        return vo;
    }

    @Override
    public HospitalAreaDetailPageCubeModulePageVo searchHospitalAreaDetailPageCubeModule(SearchHospitalAreaDetailPageCubeModuleReqDto dto) {
        Page<HospitalAreaDetailPageCubeModule> page = PageHelper.startPage(dto.getCurrentPage(), dto.getPageSize());
        page.doSelectPage(() -> mapper.selectByExample2(HospitalAreaDetailPageCubeModule.class, sql -> {
            if (dto.getTenantId() != null) {
                sql.andEqualTo(HospitalAreaDetailPageCubeModule::getTenantId, dto.getTenantId());
            }
            if (dto.getHospitalId() != null) {
                sql.andEqualTo(HospitalAreaDetailPageCubeModule::getHospitalId, dto.getHospitalId());
            }
            if (dto.getHospitalAreaId() != null) {
                sql.andEqualTo(HospitalAreaDetailPageCubeModule::getHospitalAreaId, dto.getHospitalAreaId());
            }
            if (dto.getCubeDisplayType() != null) {
                sql.andEqualTo(HospitalAreaDetailPageCubeModule::getCubeDisplayType, dto.getCubeDisplayType());
            }
        }));
        return pageVoConvert.toPageVo(page, HospitalAreaDetailPageCubeModulePageVo.class, IHospitalAreaDetailPageCubeModuleService::toVo);
    }

    @Override
    public HospitalAreaDetailPageCubeModuleVo updateHospitalAreaDetailPageCubeModule(Long id, UpdateHospitalAreaDetailPageCubeModuleReqDto dto) {
        HospitalAreaDetailPageCubeModule hospitalAreaDetailPageCubeModule = mapper.selectByPrimaryKey(id);
        if (hospitalAreaDetailPageCubeModule == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "更新失败");
        }
        boolean updateCube = false;
        if (!CollectionUtils.isEmpty(dto.getCubeList())) {
            List<HospitalAreaDetailPageCube> cubes = cubeMapper.selectByExample2(HospitalAreaDetailPageCube.class, sql ->
                    sql.andEqualTo(HospitalAreaDetailPageCube::getHospitalAreaId, dto.getHospitalAreaId())
                            .andIn(HospitalAreaDetailPageCube::getId, dto.getCubeList()));
            if (cubes.size() != dto.getCubeList().size()) {
                throw new BizException(HttpStatus.BAD_REQUEST, "更新失败，魔方功能ID不合法");
            }
            updateCube = true;
        }

        toEntity(dto, hospitalAreaDetailPageCubeModule);

        int effectRows = mapper.updateByPrimaryKeySelective(hospitalAreaDetailPageCubeModule);
        if (effectRows == 0) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "更新失败");
        }
        if (updateCube) {
            cubeMapper.updateModuleIdByModuleId(hospitalAreaDetailPageCubeModule.getHospitalAreaId(), id, null);
            dto.getCubeList().forEach(cubeId -> {
                HospitalAreaDetailPageCube cube = new HospitalAreaDetailPageCube();
                cube.setId(cubeId);
                cube.setCubeModuleId(id);
                cube.setHospitalAreaId(dto.getHospitalAreaId());
                cubeMapper.updateByPrimaryKeySelective(cube);
            });
        }

        return IHospitalAreaDetailPageCubeModuleService.toVo(hospitalAreaDetailPageCubeModule);
    }

    @Override
    public void setCubeModule(HospitalAreaLayoutVo vo, Long hospitalAreaId) {
        List<HospitalAreaDetailPageCubeModule> cubeModules = mapper.selectByExample(HospitalAreaDetailPageCubeModule.class, helper -> {
            helper.defGroup(q -> q.andEqualTo(HospitalAreaDetailPageCubeModule::getHospitalAreaId, hospitalAreaId));
            helper.builder(q -> q.orderByDesc(HospitalAreaDetailPageCubeModule::getSort));
        });
        if (CollectionUtils.isEmpty(cubeModules)) {
            return;
        }

        List<HospitalAreaDetailPageCubeModuleVo> hospitalDetailPageCubeModuleVos = setCubeVo(cubeModules);
        vo.setCubeModuleVo(hospitalDetailPageCubeModuleVos);
    }

    private List<HospitalAreaDetailPageCubeModuleVo> setCubeVo(List<HospitalAreaDetailPageCubeModule> cubeModules) {
        List<Long> cubeModuleIds = cubeModules.stream().map(HospitalAreaDetailPageCubeModule::getId).collect(Collectors.toList());
        List<HospitalAreaDetailPageCubeVo> cubeVos = hospitalDetailPageCubeService.getCubeVosByIds(cubeModuleIds);

        List<HospitalAreaDetailPageCubeModuleVo> cubeModuleVos = cubeModules.stream().map(IHospitalAreaDetailPageCubeModuleService::toVo).collect(Collectors.toList());
        cubeModuleVos.forEach(q -> q.setCubeVo(cubeVos.stream().filter(w -> w.getCubeModuleId().equals(q.getId())).collect(Collectors.toList())));
        return cubeModuleVos;
    }

    @Override
    public List<HospitalAreaDetailPageCubeModuleVo> getHospitalAreaDetailPageCubeModuleByHospitalAreaId(Long hospitalAreaId) {
        List<HospitalAreaDetailPageCubeModule> cubeModules = mapper.selectByExample(HospitalAreaDetailPageCubeModule.class, helper -> {
            helper.defGroup(q -> q.andEqualTo(HospitalAreaDetailPageCubeModule::getHospitalAreaId, hospitalAreaId));
            helper.builder(q -> q.orderByDesc(HospitalAreaDetailPageCubeModule::getSort));
        });
        if (CollectionUtils.isEmpty(cubeModules)) {
            return null;
        }

        return setCubeVo(cubeModules);
    }
}
