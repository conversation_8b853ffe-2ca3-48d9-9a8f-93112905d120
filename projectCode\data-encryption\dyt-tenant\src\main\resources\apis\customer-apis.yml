swagger: "2.0"
info:
  title: Tenant customer API
  version: "1.0"
host: localhost:9001
basePath: /apis/v1/tenant/customer
schemes:
  - http
  - https

paths:
  /cron/get-department-stop-schedule:
    get:
      summary: 获取科室停止计划
      tags:
        - Crond
      operationId: stopDepartmentSchedule
      responses:
        200:
          description: OK
  /cron/get-hospital-area-stop-schedule:
    get:
      summary: 获取院区停止计划
      tags:
        - Crond
      operationId: stopHospitalAreaSchedule
      responses:
        200:
          description: OK
  /public/regions/query:
    post:
      summary: 地区查询
      tags:
        - CustomerRegion
      operationId: query
      parameters:
        - name: request
          in: body
          schema:
            $ref: "apis.yml#/definitions/RegionQueryReqDto"
          required: true
      responses:
        200:
          description: 成功
          schema:
            $ref: "apis.yml#/definitions/RegionPageVo"

  /public/dict/labels:
    get:
      summary: 获取字典标签列表
      description: 获取字典标签列表
      operationId: getDictLabelList
      tags:
        - CustomerDict
      parameters:
        - name: name
          in: query
          description: 字典标签名称
          type: string
        - name: dict_type_name
          in: query
          description: 字典类型名称
          type: string
        - name: sort_by
          in: query
          description: 按 sort 排序:asc,desc
          type: string
          enum:
            - asc
            - desc
        - name: current_page
          in: query
          description: 当前页
          type: integer
          default: 1
        - name: page_size
          in: query
          description: 每页条数
          type: integer
          default: 10
      responses:
        200:
          description: 成功
          schema:
            $ref: "apis.yml#/definitions/DictLabelPageVo"

  /public/hospitals:
    post:
      tags:
        - CustomerHospital
      summary: 医院列表
      description: 医院列表
      operationId: queryHospitalPage
      produces:
        - application/json
      parameters:
        - name: Authorization
          type: string
          in: header
          required: false
        - name: queryHospitalPageDto
          in: body
          description: queryHospitalPageDto
          required: true
          schema:
            $ref: '#/definitions/CustomerQueryHospitalPageReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/CustomerHospitalPageVo'
        404:
          description: Not found

  /public/hospitals/{hospital-id}/hospital-areas/query:
    post:
      summary: 查询院区列表
      tags:
        - CustomerHospital
      description: 查询院区列表
      operationId: queryHospitalAreaListByHospitalId
      parameters:
        - name: hospital-id
          in: path
          type: integer
          format: int64
          required: true
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CustomerHospitalAreaPageVo"

  /public/hospital-areas/{hospital-area-code}/appointment-rule-settings:
    get:
      summary: 根据院区编码查询院区预约挂号配置
      tags:
        - CustomerHospitalArea
      operationId: getAppointmentRuleSettingDetail
      parameters:
        - name: hospital-area-code
          in: path
          description: 院区编码
          required: true
          type: string
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CustomAppointmentRuleSettingVo"

  /public/hospital-areas:
    post:
      summary: 查询院区详情
      description: 查询院区详情
      tags:
        - CustomerHospitalArea
      operationId: getDetail
      parameters:
        - name: hospital_area_id
          in: query
          description: 院区id
          required: false
          type: integer
          format: int64
        - name: hospital_area_code
          in: query
          description: 院区code
          required: false
          type: string
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CustomerHospitalAreaDetailVo"
  /public/hospital-areas-detail:
    post:
      summary: 查询院区详情
      description: 查询院区详情
      tags:
        - CustomerHospitalArea
      operationId: getDetailAndHospitalInfo
      parameters:
        - name: hospital_area_id
          in: query
          description: 院区id
          required: false
          type: integer
          format: int64
        - name: hospital_area_code
          in: query
          description: 院区code
          required: false
          type: string
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CustomerHospitalAreaDetailVo"
  /public/hospital-areas/query:
    post:
      summary: 查询院区列表
      description: 查询院区列表
      tags:
        - CustomerHospitalArea
      operationId: queryHospitalAreaPage
      parameters:
        - name: request
          in: body
          required: true
          schema:
            $ref: "#/definitions/CustomerQueryHospitalAreaPageReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CustomerHospitalAreaPageVo"

  /public/hospital-areas/{hospital-area-id}/departments/tree:
    post:
      summary: 根据院区 id 查询科室树
      description: 根据院区 id 查询科室树
      tags:
        - CustomerDepartment
      operationId: queryTree
      parameters:
        - name: hospital-area-id
          in: path
          type: integer
          format: int64
          required: true
        - name: category
          in: query
          type: integer
          description: 科室类别：0，常规；1，热门；2，特色；3，多学科；4，特需；5，疫苗；9，其他；
          required: false
        - name: level
          in: query
          type: integer
          description: 科室层级
      responses:
        200:
          description: OK
          schema:
            $ref: "#/definitions/CustomerDepartmentsTreeSearchListVo"

  /public/departments/{department-id}/sub-departments:
    get:
      summary: 根据院区 ID 和科室 ID 获取科室详情
      description: 根据院区 ID 和科室 ID 获取科室详情
      operationId: getSubDepartments
      tags:
        - CustomerDepartment
      parameters:
        - name: department-id
          in: path
          type: integer
          format: int64
          description: 科室 ID
          required: true
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CustomerDepartmentsTreeSearchListVo"

  /public/hospital-area-detail/page-sub-navi-modules/get-by-hospital-area:
    get:
      tags:
        - CustomerHospitalAreaDetail
      summary: 根据院区 ID 获取所有副导航模块
      description: 根据院区 ID 获取所有副导航模块
      operationId: getHospitalAreaDetailPageSubNaviModuleByHospitalAreaId
      produces:
        - application/json
      parameters:
        - name: hospital_area_id
          in: query
          required: false
          type: integer
          format: int64
        - name: hospital_area_code
          in: query
          required: false
          type: string
      responses:
        200:
          description: successful operation
          schema:
            type: array
            items:
              $ref: '#/definitions/CustomerHospitalAreaDetailPageSubNaviModuleVo'
        404:
          description: Not found

  /public/hospital-areas/layout:
    get:
      summary: 获取院区布局
      tags:
        - CustomerHospitalArea
      operationId: getLayout
      parameters:
        - name: hospital_area_id
          in: query
          description: 院区id
          required: false
          type: integer
          format: int64
        - name: hospital_area_code
          in: query
          description: 院区code
          required: false
          type: string
        - name: channel
          in: query
          type: integer
          description: 渠道
          required: true
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CustomerHospitalAreaLayoutVo"

  /public/hospital-areas/settings:
    get:
      summary: 查询院区配置
      tags:
        - CustomerHospitalArea
      operationId: getSettingDetail
      parameters:
        - name: hospital_area_id
          in: query
          description: 院区id
          required: false
          type: integer
          format: int64
        - name: hospital_area_code
          in: query
          description: 院区code
          required: false
          type: string
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CustomerHospitalAreaSettingDetailVo"

  /public/hospital-area/position:
    post:
      summary: 获取院区位置
      tags:
        - CustomerHospitalArea
      operationId: getHospitalAreaPosition
      parameters:
        - name: request
          in: body
          required: true
          schema:
            $ref: "#/definitions/CustomerHospitalAreaPositionReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CustomerHospitalAreaPositionVo"

  /public/departments/query:
    post:
      summary: 查询科室列表
      description: 查询科室列表
      tags:
        - CustomerDepartment
      operationId: queryDepartmentPage
      parameters:
        - name: request
          in: body
          required: true
          schema:
            $ref: "#/definitions/CustomerQueryDepartmentPageReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CustomerDepartmentPageVo"

  /public/departments/{department-id}:
    get:
      summary: 根据科室 ID 获取科室详情
      description: 根据科室 ID 获取科室详情
      operationId: getDetail
      tags:
        - CustomerDepartment
      parameters:
        - name: department-id
          in: path
          type: integer
          format: int64
          description: 科室 ID
          required: true
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CustomerDepartmentDetailVo"
  /public/departments/{hospital-area-code}/{department-code}:
    get:
      summary: 根据科室编码和医院编码获取科室详情
      description: 根据科室编码和医院编码获取科室详情
      operationId: getDetailByCode
      tags:
        - CustomerDepartment
      parameters:
        - name: hospital-area-code
          in: path
          type: string
          description: 院区编码
          required: true
        - name: department-code
          in: path
          type: string
          description: 科室编码
          required: true
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CustomerDepartmentDetailVo"

  /public/departments/{department-id}/children/tree:
    post:
      summary: 根据科室 ID 查询该节点树
      description: 根据科室 ID 查询该节点树
      tags:
        - CustomerDepartment
      operationId: queryChildrenTree
      parameters:
        - name: department-id
          in: path
          type: integer
          format: int64
          required: true
      responses:
        200:
          description: OK
          schema:
            $ref: "#/definitions/CustomerDepartmentsTreeSearchListVo"

  /public/departments/doctors:
    get:
      summary: 根据科室 ID 获取按日期分组后的医生列表数据
      description: 根据科室 ID 获取按日期分组后的医生列表数据
      operationId: queryGroupedDoctorListByDepartmentId
      tags:
        - CustomerDepartment
      parameters:
        - name: department_id
          in: query
          type: integer
          format: int64
          description: 科室 ID
        - name: hospital_area_code
          in: query
          type: string
          description: 院区 code
        - name: department_code
          in: query
          type: string
          description: 科室 code
        - name: time_type
          in: query
          type: integer
          description: 时间类型
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CustomerDoctorScheduleInfoVo"
        400:
          description: Invalid inputs supplied

  /public/departments/doctors/special-needs:
    get:
      summary: 根据科室 ID 或院区id获取特需专家医生列表数据
      description: 根据科室 ID 或院区id获取特需专家医生列表数据
      operationId: querySpecialNeedsDoctorList
      tags:
        - CustomerDepartment
      parameters:

        - name: hospital-area-id
          in: query
          type: integer
          format: int64
          description: 院区id
        - name: department-id
          in: query
          type: integer
          format: int64
          description: 科室 ID
        - name: doctor-name
          in: query
          type: string
          description: 医生姓名
      responses:
        200:
          description: 成功
          schema:
            type: array
            items:
              $ref: "#/definitions/CustomerAllScheduleDoctorDetailVo"
        400:
          description: Invalid inputs supplied

  /departments/recent-success:
    get:
      summary: 获取最近成功预约的科室
      description: 获取最近成功预约的科室
      operationId: getRecentSuccess
      tags:
        - CustomerDepartment
      parameters:
        - name: hospital_area_id
          in: query
          description: 院区id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            type: array
            items:
              $ref: "#/definitions/CustomerDepartmentVo"

  /public/doctors/query:
    post:
      summary: 获取医生列表
      description: 获取医生列表
      operationId: query
      tags:
        - CustomerDoctor
      parameters:
        - name: request
          in: body
          description: 获取医生列表
          required: true
          schema:
            $ref: "#/definitions/CustomerDoctorQueryReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CustomerDoctorPageVo"
        400:
          description: Invalid inputs supplied

  /public/doctors/{doctor-id}:
    get:
      summary: 获取医生详情
      description: 获取医生详情
      operationId: getDetail
      tags:
        - CustomerDoctor
      parameters:
        - name: doctor-id
          in: path
          description: 医生id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CustomerDoctorDetailVo"
        404:
          description: Not found

  /public/doctors/{hospital_code}/{department_code}/{doctor_code}:
    get:
      summary: 获取医生详情
      description: 获取医生详情
      operationId: getDetailByCode
      tags:
        - CustomerDoctor
      parameters:
        - name: hospital_code
          in: path
          description: 医院code
          required: true
          type: string
        - name: department_code
          in: path
          description: 科室code
          required: true
          type: string
        - name: doctor_code
          in: path
          description: 医生code
          required: true
          type: string
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CustomerDoctorDetailVo"
        404:
          description: Not found

  /public/doctors/{doctor-id}/ask-services:
    get:
      summary: 获取医生的在线问诊服务列表
      description: 获取医生的在线问诊服务列表
      operationId: getAskServiceList
      tags:
        - CustomerDoctor
      parameters:
        - name: doctor-id
          in: path
          description: 医生id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            type: object
            $ref: "#/definitions/CustomerDoctorAskServiceVo"
        404:
          description: Not found

  /public/doctors/schduled-departments:
    get:
      summary: 查询医生所有科室排班
      description: 查询医生所有科室排班
      operationId: queryDoctorScheduledDepartment
      tags:
        - CustomerDoctor
      parameters:
        - name: base_doctor_id
          in: query
          description: 老系统医生主页ID
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CustomerDoctorScheduledDepartmentVo"
  # vaccine
  /public/vaccine/category/page:
    post:
      summary: 分页查询疫苗分类
      description: 分页查询疫苗分类
      operationId: getVaccineCategoryPage
      tags:
        - CustomerVaccine
      parameters:
        - name: request
          in: body
          description: 分页查询疫苗分类
          required: true
          schema:
            $ref: "#/definitions/CustomerVaccineCategoryPageReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CustomerVaccineCategoryPageVo"
        400:
          description: Invalid inputs supplied
  /public/vaccine/organization/list:
    post:
      summary: 查询疫苗机构列表
      description: 查询疫苗机构列表
      operationId: getVaccineOrganizationList
      tags:
        - CustomerVaccine
      parameters:
        - name: request
          in: body
          description: 查询疫苗机构列表
          required: true
          schema:
            $ref: "#/definitions/CustomerVaccineOrganizationListReqDto"
      responses:
        200:
          description: 成功
          schema:
            type: array
            items:
              $ref: "#/definitions/CustomerVaccineOrganizationVo"
        400:
          description: Invalid inputs supplied
  /public/vaccine/list:
    post:
      summary: 查询疫苗列表
      description: 查询疫苗列表
      operationId: getVaccineList
      tags:
        - CustomerVaccine
      parameters:
        - name: request
          in: body
          description: 查询疫苗列表
          required: true
          schema:
            $ref: "#/definitions/CustomerVaccineListReqDto"
      responses:
        200:
          description: 成功
          schema:
            type: array
            items:
              $ref: '#/definitions/CustomerVaccineVo'
        400:
          description: Invalid inputs supplied
  /public/vaccine/search:
    get:
      summary: 疫苗综合搜索
      description: 疫苗综合搜索
      operationId: vaccineSearch
      tags:
        - CustomerVaccine
      parameters:
        - name: keyword
          in: query
          description: 搜索关键字
          required: true
          type: string
        - name: longitude
          in: query
          description: 经度
          required: false
          type: number
          format: double
        - name: latitude
          in: query
          description: 纬度
          required: false
          type: number
          format: double
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CustomerVaccineSearchRespVo"
        400:
          description: Invalid inputs supplied

  /public/search/hospital:
    post:
      tags:
        - CustomerSearch
      summary: 搜索医院
      description: 搜索医院
      operationId: searchHospital
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: searchHospitalReqDto
          required: true
          schema:
            $ref: '#/definitions/SearchHospitalReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/SearchHospitalVo'
  /public/search/department-doctor:
    post:
      tags:
        - CustomerSearch
      summary: 搜索科室医生
      description: 搜索科室医生
      operationId: searchDepartmentAndDoctor
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          required: true
          schema:
            $ref: '#/definitions/SearchDepartmentAndDoctorReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/SearchDepartmentAndDoctorVo'

  /public/code-mapping:
    post:
      tags:
        - CustomerCodeMapping
      summary: 根据code映射系统id
      description: 根据code映射系统id
      operationId: getCodeMapping
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: getCodeMappingReqDto
          required: true
          schema:
            $ref: '#/definitions/GetCodeMappingReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/GetCodeMappingVo'

  # recommend
  /public/recommend:
    get:
      tags:
        - CustomerRecommend
      summary: 获取推荐列表
      description: 获取推荐列表
      operationId: getRecommendList
      produces:
        - application/json
      parameters:
        - name: biz_type
          in: query
          description: 业务类型
          required: true
          type: string
        - name: data_type
          in: query
          description: 数据类型
          required: false
          type: string
        - name: data_tag
          in: query
          description: 数据标签
          required: false
          type: string
        - name: longitude
          in: query
          description: 经度
          required: false
          type: number
          format: double
        - name: latitude
          in: query
          description: 纬度
          required: false
          type: number
          format: double
        - name: channel
          in: query
          type: integer
          default: 0
          description: 渠道
          required: true
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/RecommendRespVo'
  /public/recommend/{recommend-id}:
    get:
      tags:
        - CustomerRecommend
      summary: 获取推荐详情
      description: 获取推荐详情
      operationId: getRecommendDetail
      produces:
        - application/json
      parameters:
        - name: recommend-id
          in: path
          description: 推荐id
          required: true
          type: integer
          format: int64
        - name: longitude
          in: query
          description: 经度
          required: false
          type: number
          format: double
        - name: latitude
          in: query
          description: 纬度
          required: false
          type: number
          format: double
      responses:
        200:
          description: successful operation
          schema:
            $ref: '#/definitions/RecommendRespVo'

  /public/statistics/recommend:
    put:
      summary: 推荐点击统计
      description: 推荐点击统计
      operationId: recommendIncrement
      tags:
        - CustomerStatistics
      parameters:
        - name: Authorization
          type: string
          in: header
          required: false
        - name: request
          in: body
          required: true
          schema:
            $ref: '#/definitions/RecommendStatisticsReqDto'
      responses:
        200:
          description: 成功

  /public/hospital-code/{hospitalCode}/sync-doctor:
    put:
      summary: 同步指定医院数据
      description: 同步指定医院数据
      operationId: syncDoctorData
      tags:
        - Maintenance
      parameters:
        - name: hospitalCode
          type: string
          in: path
          description: 医院code
          required: true
      responses:
        200:
          description: 成功

  /public/link-converts:
    get:
      tags:
        - CustomerLinkConvert
      summary: 根据传入的链接，获取转换后的链接
      operationId: getConvertLink
      produces:
        - application/json
      parameters:
        - name: source
          in: query
          required: true
          type: string
      responses:
        200:
          description: successful operation
          schema:
            type: string
  /bulletin-config/query:
    get:
      tags:
        - CustomBulletinConfig
      summary: 获取当前公告配置
      description: 获取当前公告配置
      operationId: getCurrentBulletinConfig
      produces:
        - application/json
      parameters:
        - name: user_id
          in: query
          description: 用户id
          required: false
          type: integer
          format: int64
      responses:
        200:
          description: OK
          schema:
            $ref: "#/definitions/CustomBulletinConfigVo"

  /public/sync/task/kun-hua/{department_code}:
    get:
      tags:
        - CustomerSyncTask
      summary: 同步昆华数据
      description: 同步昆华数据
      operationId: syncKunHuaData
      produces:
        - application/json
      parameters:
        - name: department_code
          in: path
          description: 科室编码
          required: true
          type: string
      responses:
        200:
          description: 成功
          schema:
            type: string
  /source-recommend-config/recommend-doctors:
    get:
      tags:
        - CustomerSourceRecommendConfig
      summary: 获取推荐列表
      description: 获取推荐列表
      operationId: getRecommendDoctors
      produces:
        - application/json
      parameters:
        - name: doctor_id
          in: query
          description: 医生id
          type: integer
          format: int64
          required: true
      responses:
        200:
          description: 成功
          schema:
            type: array
            items:
              $ref: "apis.yml#/definitions/RecommendConfigDoctorDto"
  /quick-registration/recommend-department:
    get:
      tags:
        - CustomerRecommend
      summary: 获取推荐科室列表
      description: 获取推荐科室列表
      operationId: getRecommendDepartment
      produces:
        - application/json
      parameters:
        - name: biz_type
          in: query
          description: 业务类型
          type: string
          required: true
      responses:
        200:
          description: 成功
          schema:
            type: array
            items:
              $ref: "#/definitions/RecommendConfigDepartmentDto"
  /quick-registration/recommend-department-detail:
    post:
      summary: 分页查询推荐科室详情
      description: 分页查询推荐科室详情
      operationId: getRecommendDepartmentDetail
      tags:
        - CustomerRecommend
      parameters:
        - name: request
          in: body
          schema:
            $ref: "#/definitions/CustomRecommendDoctorQueryDto"
          required: true
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CustomRecommendDoctorPageVo"
  /quick-registration/recently-registration-department:
    get:
      summary: 查询最近挂号科室
      description: 查询最近挂号科室
      operationId: getRecentRegistrationDepartment
      tags:
        - CustomerRecommend
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/RecentlyRegistrationDepartmentVo"
  /quick-registration/departments/doctors:
    get:
      summary: 快速挂号获取按日期分组后的医生列表数据
      description: 快速挂号获取获取按日期分组后的医生列表数据
      operationId: queryGroupedDoctorListByDepartmentId
      tags:
        - CustomerRecommend
      parameters:
        - name: department_id
          in: query
          type: integer
          format: int64
          description: 科室 ID
        - name: hospital_area_code
          in: query
          type: string
          description: 院区 code
        - name: department_code
          in: query
          type: string
          description: 科室 code
        - name: time_type
          in: query
          type: integer
          description: 时间类型
        - name: recommend_id
          in: query
          type: integer
          format: int64
          description: 科室推荐配置id
        - name: data_tag
          in: query
          type: string
          description: 数据标签
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CustomerDoctorScheduleInfoVo"
        400:
          description: Invalid inputs supplied
  /user-doctor-subscriptions:
    post:
      summary: 添加用户医生订阅
      description: 创建新的用户医生订阅
      operationId: addUserDoctorSubscription
      tags:
        - UserDoctorSubscription
      parameters:
        - name: body
          in: body
          description: 用户医生订阅对象
          required: true
          schema:
            $ref: "#/definitions/UserDoctorSubscriptionCreateDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/UserDoctorSubscriptionVo"
    get:
      summary: 查询放号通知用户订阅记录
      description: 获取放号通知用户订阅记录
      operationId: getUserDoctorSubscriptions
      tags:
        - UserDoctorSubscription
      parameters:
        - name: status
          in: query
          type: integer
          enum:
            - 0
            - 1
          description: 订阅状态（0：当前订阅，1：历史订阅）
          required: true
      responses:
        200:
          description: 成功
          schema:
            type: array
            items:
              $ref: "#/definitions/UserDoctorSubscriptionVo"
  /user-doctor-subscriptions/{id}:
    put:
      summary: 取消放号通知医生订阅
      description: 取消放号通知医生订阅
      operationId: cancelUserDoctorSubscription
      tags:
        - UserDoctorSubscription
      parameters:
        - name: id
          in: path
          description: 医生ID
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
  /user-doctor-subscriptions/check-user-doctor-subscription/{doctor-id}:
    get:
      summary: 校验当前用户是否订阅了指定医生
      description: 校验当前用户是否订阅了指定医生
      operationId: checkUserDoctorSubscription
      tags:
        - UserDoctorSubscription
      parameters:
        - name: doctor-id
          in: path
          description: 医生ID
          required: true
          type: integer
          format: int64
      responses:
        200:
          schema:
            type: object
            properties:
              is_subscribed:
                type: boolean
                description: 是否已订阅
                example: true
                default: false
          description: 成功

  /public/quick-check/departments/doctors:
    post:
      summary: 获取按科室(检查分类)、日期分组后的医生(检查项)列表数据
      description: 获取按科室(检查分类)、日期分组后的医生(检查项)列表数据
      operationId: queryGroupedDepartsAndDoctors
      tags:
        - CustomerQuickCheck
      parameters:
        - name: request
          in: body
          required: true
          schema:
            $ref: "#/definitions/CustomerQuickCheckQueryDepartmentReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CustomerQuickCheckDepartmentReponse"
        400:
          description: Invalid inputs supplied

  /public/quick-check/doctors:
    post:
      summary: 获取医生(检查项)列表数据
      description: 获取医生(检查项)列表数据
      operationId: queryDoctors
      tags:
        - CustomerQuickCheck
      parameters:
        - name: request
          in: body
          required: true
          schema:
            $ref: "#/definitions/CustomerQuickCheckQueryDoctorsPageReqDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/CustomerQuickCheckDoctorPageVo"
        400:
          description: Invalid inputs supplied
  /public/quick-check/expert-appointment/doctors:
    post:
      summary: 获取专家预约列表数据
      description: 获取专家预约列表数据
      operationId: queryExpertAppointmentDoctors
      tags:
        - CustomerQuickCheck
      parameters:
        - name: displayTags
          in: body
          required: false
          schema:
            type: array
            items:
              type: string
              description: 查询标签
        - name: current
          in: query
          description: 当前页
          required: false
          type: integer
          default: 1
        - name: page_size
          in: query
          description: 每页条数
          required: false
          type: integer
          default: 10
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/ExpertAppointmentDoctorPageVo"
        400:
          description: Invalid inputs supplied
  /public/quick-check/all-hospital:
    get:
      summary: 获取所有医院列表
      description: 获取所有医院列表
      operationId: getAllHospital
      tags:
        - CustomerQuickCheck
      parameters:
        - name: longitude
          in: query
          description: 经度
          required: true
          type: number
          format: double
        - name: latitude
          in: query
          description: 纬度
          required: true
          type: number
          format: double
        - name: hot
          in: query
          description: 是否热门医院
          required: false
          type: boolean
      responses:
        200:
          description: 成功
          schema:
            type: array
            items:
              $ref: "#/definitions/CustomerQuickCheckAllHospitalVo"





definitions:
  CustomerQueryHospitalPageReqDto:
    allOf:
      - $ref: "apis.yml#/definitions/QueryCustomerHospitalPageReqDto"
  CustomerHospitalPageVo:
    type: object
    allOf:
      - $ref: 'apis.yml#/definitions/BasePage'
      - type: object
        properties:
          records:
            type: array
            items:
              $ref: 'apis.yml#/definitions/CustomerHospitalVo'
  CustomerHospitalAreaPageVo:
    allOf:
      - $ref: "apis.yml#/definitions/BasePage"
      - type: object
        properties:
          name:
            type: string
            description: 医院名称
          logo:
            type: string
            description: 医院logo
          list:
            type: array
            items:
              $ref: "#/definitions/CustomerHospitalAreaVo"
  CustomerHospitalAreaVo:
    type: object
    properties:
      id:
        type: integer
        description: 院区id
        format: int64
      hospital_area_code:
        type: string
        description: 院区编码
      name:
        type: string
        description: 院区名称
      hospital_id:
        type: integer
        description: 所属医院id
        format: int64
      hospital_name:
        type: string
        description: 所属医院名称
      hospital_logo:
        type: string
        description: 所属医院logo
      status:
        type: integer
        description: 状态,0:启用,1:开发,2:维护,3:停用
      current_system_type:
        type: integer
        description: 当前使用系統类型：1,老系统;2,新系统;3,新老系统共用;
      display:
        type: boolean
        description: 是否显示,0:显示,1:不显示
      sort:
        type: integer
        description: 排序
      category:
        type: array
        items:
          type: integer
        description: 院区类别：0，常规；1，疫苗预约；2，特需门诊；3，多学科；9，其他；
      address:
        $ref: "apis.yml#/definitions/AddressDto"
  CustomerHospitalAreaDetailVo:
    allOf:
      - $ref: "#/definitions/CustomerHospitalAreaVo"
      - type: object
        properties:
          picture:
            type: string
            description: 院区图片
          contact_phone_number:
            type: string
            description: 联系电话
          appointment_scheduling_time:
            type: string
            description: 放号时间
          announcement:
            type: string
            description: 院区公告
          introduction:
            type: string
            description: 院区介绍
          environment:
            type: string
            description: 院区环境
          display_guide:
            type: boolean
            description: 是否显示去院导航
          map_keyword:
            type: string
            description: 地图关键字
          display_floor:
            type: boolean
            description: 是否显示楼层
          stop_service_begin_time:
            type: string
            description: 停止服务开始时间
          stop_service_end_time:
            type: string
            description: 停止服务结束时间
  CustomAppointmentRuleSettingVo:
    type: object
    properties:
      current_system_type:
        type: integer
        description: 当前使用系統类型： 1,老系统;2,新系统;3,新老系统共用
      doctor_title_show_type:
        type: integer
        description: 医生职称展示类型。1-平台职称，2-医院HIS职称，3-荣誉

  CustomerHospitalAreaLayoutVo:
    type: object
    properties:
      map_keyword:
        type: string
        description: 地图关键字
      page_config:
        $ref: "#/definitions/CustomerHospitalAreaDetailPageConfigVo"
      navigation_vo:
        $ref: "#/definitions/CustomerHospitalAreaDetailPageNavigatorVo"
      sub_navigation_vo:
        type: array
        items:
          description: 医院详情页子导航模块
          type: object
          $ref: "#/definitions/CustomerHospitalAreaDetailPageSubNaviModuleVo"
      cube_module_vo:
        type: array
        items:
          description: 医院详情页魔方模块
          $ref: "#/definitions/CustomerHospitalAreaDetailPageCubeModuleVo"
      tab_vo:
        type: array
        items:
          description: 医院详情页Tab模块
          $ref: "#/definitions/CustomerHospitalAreaDetailPageTabVo"
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      hospital_area_code:
        type: string
        description: 院区编码
      hospital_name:
        type: string
        description: 医院名称
      hospital_area_name:
        type: string
        description: 医院院区名称
      hospital_area_image:
        type: string
        description: 医院院区图片
      hospital_areas:
        type: array
        description: 医院院区集合
        items:
          type: object
          properties:
            hospital_area_id:
              type: integer
              format: int64
              description: 医院院区ID
            hospital_area_name:
              type: string
              description: 医院院区名称
      hospital_area_address:
        $ref: "apis.yml#/definitions/AddressVo"
      appointment_schedule_time:
        type: string
        description: 放号时间
      appointment_notice:
        type: string
        description: 预约须知
      appointment_rule:
        type: string
        description: 预约规则
      introduction:
        type: string
        description: 医院介绍
      announcement:
        type: string
        description: 公告，又名：温馨提示
      hospital_area_level:
        type: string
        description: 医院院区等级
      hospital_area_phone:
        type: string
        description: 医院电话
      level_dict_type:
        type: string
        description: 医院等级字典类型
        default: hospital_level
      level_dict_label:
        type: array
        description: 医院等级字典标签
        items:
          type: string
      level_dict_value:
        type: array
        description: 医院级别
        items:
          type: string
  CustomerHospitalAreaDetailPageConfigVo:
    type: object
    properties:
      display_hospital_area:
        type: boolean
        description: 是否显示院区：0，不显示；1，显示；
      display_address:
        type: boolean
        description: 是否显示地址：0，不显示；1，显示；
      display_level:
        type: boolean
        description: 是否显示等级：0，不显示；1，显示；
      display_open_scheduling_time:
        type: boolean
        description: 是否显示医院放号时间：0，不显示；1，显示；
      display_appoint_notice:
        type: boolean
        description: 是否显示预约须知：0，不显示；1，显示；
      display_notice:
        type: boolean
        description: 是否显示医院温馨提示：0，不显示；1，显示；
      display_floor_distribution:
        type: boolean
        description: 是否显示楼层分布：0，不显示；1，显示；
      display_tab:
        type: boolean
        description: 是否显示tab：0，不显示；1，显示；
  CustomerHospitalAreaDetailPageNavigatorVo:
    type: object
    properties:
      type:
        type: integer
        description: 导航栏类型，0:主图文导航，1:副图文导航
      navi_info:
        type: array
        items:
          type: object
          properties:
            title:
              type: string
              description: 主标题
            sub_title:
              type: string
              description: 副标题，副导航栏无效
            picture:
              type: string
              description: 导航图片
            url:
              type: string
              description: 导航链接
            sort:
              type: integer
              description: 排序
              default: 0
  CustomerHospitalAreaDetailPageSubNaviModuleVo:
    type: object
    properties:
      sub_navi_type:
        type: integer
        description: 副标题样式：0，固定；1，横向滑动；
      sub_navi_display_limit:
        type: integer
        description: 副标题单行显示上限
      sort:
        type: integer
        description: 排序
      sub_navigation_list:
        type: array
        items:
          $ref: "#/definitions/CustomerNaviVo"
  CustomerNaviVo:
    type: object
    properties:
      title:
        type: string
        description: 主标题
      sub_title:
        type: string
        description: 副标题，副导航栏无效
      picture:
        type: string
        description: 导航图片
      url:
        type: string
        description: 导航链接
      sort:
        type: integer
        description: 排序
        default: 0
  CustomerHospitalAreaDetailPageCubeModuleVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 魔方模块ID
      title:
        type: string
        description: 标题
      title_status:
        type: integer
        description: 标题状态：0，展示；1，禁用；
      cube_display_type:
        type: integer
        description: 魔方类型：0，一行两个；1，一行三个；2，二左二右；3，一左二右；4，一左三右
      sort:
        type: integer
        description: 排序
      create_time:
        description: 创建时间
        type: string
        format: date-time
      update_time:
        description: 更新时间
        type: string
        format: date-time
      cube_vo:
        type: array
        items:
          $ref: "#/definitions/CustomerHospitalAreaDetailPageCubeVo"
  CustomerHospitalAreaDetailPageCubeVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 魔方ID
      cube_module_id:
        type: integer
        format: int64
        description: 魔方模块ID
      title:
        type: string
        description: 标题
      picture:
        type: string
        description: 图片
      url:
        type: string
        description: 链接
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态：0，启动；1，禁用；
        default: 0
  CustomerHospitalAreaDetailPageTabVo:
    type: object
    properties:
      title:
        type: string
        description: 标题
      component_type:
        type: integer
        description: 组件类型
      recommend_id:
        type: integer
        format: int64
        description: 推荐ID
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态：0，启动；1，禁用；
        default: 0

  CustomerDoctorVo:
    type: object
    properties:
      id:
        type: integer
        description: 医生id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_name:
        type: string
        description: 医院名称
      thrdpart_doctor_code:
        type: string
        description: 第三方医生编码
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      hospital_area_code:
        type: string
        description: 院区编码
      hospital_area_name:
        type: string
        description: 院区名称
      department_id:
        type: integer
        description: 科室id
        format: int64
      request_department_id:
        type: integer
        description: 前端请求的department id
        format: int64
      department_name:
        type: string
        description: 科室名称
      department_code:
        type: string
        description: 科室编码
      name:
        type: string
        description: 医生姓名
      head_img_url:
        type: string
        description: 头像 URL 地址
      rank_dict_type:
        type: string
        description: 职称字典类型
      rank_dict_label:
        type: array
        description: 职称
        items:
          type: string
      rank_dict_value:
        type: array
        description: 职称字典值
        items:
          type: string
      registration_level:
        type: string
        description: his职称
      speciality:
        type: string
        description: 专长
      status:
        type: integer
        description: 状态：0，正常；1，禁用
      honor:
        type: string
        description: 荣誉
      introduction:
        type: string
        description: 简介
      belong_doctor_groups:
        type: boolean
        description: 是否属于某个医生团队
      sort:
        type: integer
        description: 排序
      email:
        type: string
        description: 邮箱
      display_department_name:
        type: string
        description: 显示科室名称
      appointment_rule_list:
        type: array
        items:
          type: string
        description: 预约规则
      doctor_title:
        type: string
        description: 医生职称统一返回字段
      tags:
        type: string
        description: 医生标签
      second_merchant_id:
        type: integer
        description: 二级商户id
        format: int64
      settlement_rule_id:
        type: integer
        description: 结算规则id
        format: int64
      need_divide_settlement:
        type: boolean
        description: 是否需要分账，0：不需要，1：需要


  CustomerDoctorDetailVo:
    allOf:
      - $ref: "#/definitions/CustomerDoctorVo"
      - type: object
        properties:
          statement:
            type: string
            description: 特殊说明
          appointment_rule_dict_type:
            type: string
            description: 预约规则字典类型
          appointment_rule_dict_label:
            type: array
            description: 预约规则
            items:
              type: string
          user_id:
            type: integer
            description: 与在线问诊医生关联的用户 id
            format: int64
            default: 0
          appointment_rule:
            type: string
            description: 预约规则
          category:
            description: 医生类别
            type: array
            items:
              type: integer
              format: int32
          visiting_address:
            type: string
            description: 出诊地址
          visiting_introduction:
            type: string
            description: 出诊信息
          appointment_notice:
            type: string
            description: 预约提示
          payment_notice:
            type: string
            description: 支付提示
          judge_appointment_condition:
            type: boolean
            description: 是否判断预约条件
          judge_appointment_rule:
            type: string
            description: 预约条件,|分割
          need_upload_resource:
            type: boolean
            description: 是否需要上传资源
          need_verify_resource:
            type: boolean
            description: 是否需要审核资源
          success_notice_phones:
            type: string
            description: 预约成功通知手机号,分割
          success_template_ids:
            type: string
            description: 预约成功通知模板id,分割

  CustomerDepartmentVo:
    type: object
    properties:
      id:
        type: integer
        description: 科室 id
        format: int64
      name:
        type: string
        description: 科室名称
      parent_id:
        type: integer
        description: 父节点科室 id
        format: int64
      parent_name:
        type: string
        description: 父节点科室名称
      hospital_id:
        type: integer
        description: 医院 id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区 id
        format: int64
      hospital_area_code:
        type: string
        description: 院区编码
      thrdpart_dep_code:
        type: string
        description: 第三方科室编码
      display_bg_color:
        type: string
        description: 显示背景色
      enable_department_detail:
        type: boolean
        description: 是否启用科室详情
      category:
        type: array
        items:
          type: integer
        description: 科室类别：0，常规；1，热门；2，特色；3，多学科；4，特需；5，疫苗；9，其他；
      uri:
        type: string
        description: 科室跳转地址
      first_letter:
        type: string
        description: 首字母
      address_intro:
        type: string
        description: 地址简介
      sort:
        type: integer
        description: 排序
      remark:
        type: string
        description: 备注
      display_fields:
        type: integer
        description: 显示字段（0：name、1:displayDepartmentName)
      display_department_name:
        type: string
        description: 显示科室名称
      parent_department_code:
        type: string
        description: 父科室编码

  CustomerDepartmentDetailVo:
    allOf:
      - $ref: "#/definitions/CustomerDepartmentVo"
      - type: object
        properties:
          short_name:
            type: string
            description: 科室简称
          introduction:
            type: string
            description: 科室简介
          recommended:
            type: boolean
            description: 是否推荐
          caution:
            type: string
            description: 科室提醒
          triage_desk_address:
            type: string
            description: 分诊台地址


  CustomerDepartmentsTreeSearchListVo:
    type: object
    properties:
      list:
        type: array
        items:
          $ref: "#/definitions/CustomerDepartmentTreeVo"
      level_count:
        type: integer
        description: 科室层级数
      display_address:
        type: boolean
        description: 是否显示地址,true:显示地址，false:不显示地址
      all_department_background_color:
        type: string
        description: 全科背景色

  CustomerDepartmentTreeVo:
    allOf:
      - $ref: "#/definitions/CustomerDepartmentVo"
      - type: object
        properties:
          caution:
            type: string
            description: 科室提醒
          childrens:
            type: array
            items:
              $ref: "#/definitions/CustomerDepartmentTreeVo"

  CustomerDoctorQueryReqDto:
    type: object
    properties:
      current_page:
        description: 当前页
        type: integer
        default: 1
      page_size:
        description: 每页条数
        type: integer
        default: 10
      hospital_id:
        description: 医院id
        type: integer
        format: int64
      hospital_area_id:
        description: 院区id
        type: integer
        format: int64
      department_id:
        description: 科室id
        type: integer
        format: int64
      name:
        description: 医生姓名
        type: string
      sort_by:
        description: 排序字段:create_time
        type: string
  CustomerDoctorPageVo:
    allOf:
      - $ref: "apis.yml#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/CustomerDoctorVo"

  CustomerDoctorScheduleInfoVo:
    type: object
    properties:
      system_depends:
        type: integer
        description: 挂号系统依赖：0，HIS；1，小系统；
      all_doctors:
        type: array
        description: 医生集合
        items:
          $ref: "#/definitions/CustomerAllScheduleDoctorDetailVo"
      grouped_doctors:
        type: array
        description: 分组后的医生
        items:
          $ref: "#/definitions/CustomerDoctorScheduleGroupedByDateVo"

  CustomerAllScheduleDoctorDetailVo:
    type: object
    allOf:
      - $ref: "#/definitions/CustomerDoctorVo"
      - type: object
        properties:
          introduction:
            type: string
            description: 简介
          date_in_weeks:
            type: array
            description: 医生排班日期
            items:
              $ref: "#/definitions/CustomerDateInWeekDto"
          show_sch_date:
            type: boolean
            description: 显示排班日期
          can_order:
            type: boolean
            description: 是否可预约(true：显示可预约，false：显示暂无号)
          customer_tags:
            type: array
            items:
              type: string
            description: 自定义标签
          data_tag:
            type: string
            description: 数据标签
          appointment_rule_list:
            type: array
            description: 预约规则
            items:
              type: string
  CustomerDoctorScheduleGroupedByDateVo:
    type: object
    description: 分组后的医生
    properties:
      doctor_group_id:
        type: string
        description: 医生分组ID，日期
      date_in_week:
        type: string
        description: 星期几
      schedule_date:
        type: string
        description: 排班日期
      schedule_local_date:
        type: string
        description: 排班日期，LocalDate 类型
        format: date
      can_order:
        type: boolean
        description: 是否可预约(true：显示有号，false：显示无号)
      doctors:
        type: array
        description: 医生集合
        items:
          $ref: "#/definitions/CustomerGroupedDoctorDetailVo"
  CustomerGroupedDoctorDetailVo:
    type: object
    allOf:
      - $ref: "#/definitions/CustomerDoctorVo"
      - type: object
        properties:
          show_sch_date:
            type: boolean
            description: 显示排班日期
          can_order:
            type: boolean
            description: 是否可预约(true：显示可预约，false：显示暂无号)
          date_in_weeks:
            type: array
            description: 医生排班日期
            items:
              $ref: "#/definitions/CustomerDateInWeekDto"
  CustomerDateInWeekDto:
    type: object
    properties:
      date_in_week:
        type: string
        description: 星期几
      date:
        type: string
        description: 日期
      local_date:
        type: string
        description: 全量日期
        format: date
      can_order:
        type: boolean
        description: 是否可预约(true：显示有号，false：显示无号)
  CustomerHospitalAreaSettingDetailVo:
    type: object
    properties:
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
        minimum: 1
      hospital_area_code:
        type: string
        description: 院区编码
      hospitalization_setting:
        $ref: "#/definitions/CustomerHospitalizationSettingVo"
      diagnosis_payment_setting:
        $ref: "#/definitions/CustomerDiagnosisPaymentSettingVo"
      appointment_rule_setting:
        $ref: "#/definitions/CustomerAppointmentRuleSettingVo"
      patient_card_setting:
        $ref: "#/definitions/CustomerPatientCardSettingVo"
      patient_report_setting:
        $ref: "#/definitions/CustomerPatientReportSettingVo"
  CustomerHospitalizationSettingVo:
    type: object
    properties:
      enable_payment:
        type: boolean
        description: 是否开启支付
      support_online_refund:
        type: boolean
        description: 是否支持在线退款
      enable_info_query:
        type: boolean
        description: 是否开启住院信息查询
      selected_payments:
        type: array
        description: 支付方式
        items:
          type: string
      payment_information:
        type: string
        description: 缴费提示信息
  CustomerDiagnosisPaymentSettingVo:
    type: object
    properties:
      support_merger_payment:
        type: boolean
        description: 是否支持合并支付
      support_online_refund:
        type: boolean
        description: 是否支持在线退款
      refund_today:
        type: boolean
        description: 是否支持当天退款
      stop_refund_time:
        type: string
        description: 停止退款时间
      support_invoice:
        type: boolean
        description: 是否支持开票
      selected_payments:
        type: array
        description: 支付方式
        items:
          type: string
      payment_information:
        type: string
        description: 缴费提示信息
  CustomerPatientCardSettingVo:
    type: object
    properties:
      card_name:
        type: string
        description: 就诊卡名称
      need_patient_card:
        type: boolean
        description: 是否需要就诊卡
      need_recharge:
        type: boolean
        description: 是否需要充值
      bind_type:
        type: integer
        description: 绑定类型：0，自动绑卡；1，手动填写自动开卡；2，手工录入
        enum:
          - 0
          - 1
          - 2
      support_patient_type:
        type: integer
        description: 支持的患者类型：0，全部；1，成人；2，儿童
      need_electron_card:
        type: boolean
        description: 是否需要电子就诊卡
  CustomerAppointmentRuleSettingVo:
    type: object
    properties:
      department_level:
        type: integer
        description: 科室级别，从字典取
      system_depends:
        type: integer
        description: 挂号系统依赖：0，HIS；1，小系统；
      appoint_today:
        type: boolean
        description: 是否支持当天预约
      display_no:
        type: boolean
        description: 是否显示序号
      stop_appoint_time:
        type: string
        description: 停止预约时间
      advance_day:
        type: integer
        description: 提前预约天数：-1，不限制；
        format: int32
        minimum: -1
      display_time:
        type: string
        description: 号源显示时间
      payment_close_duration:
        type: integer
        description: 支付关闭时间，单位分钟
        format: int64
        minimum: 0
      refund_today:
        type: boolean
        description: 是否支持当天退款
      stop_refund_time:
        type: string
        description: 停止退款时间
      confirm_medical_insurance_card:
        type: boolean
        description: 是否需要确认医保卡
      order_need_verify:
        type: boolean
        description: 是否需要核销
      appointment_result_notice:
        type: string
        description: 预约结果通知
      appointment_notify_contact:
        type: string
        description: 预约后联系人
      notice_template_id:
        type: string
        description: 通知模板id
      appointment_notice:
        type: string
        description: 预约提示
      appointment_rule:
        type: string
        description: 预约规则
      appointment_guide:
        type: string
        description: 预约指引
      selected_payments:
        type: array
        description: 支付方式
        items:
          type: string
      display_doctor_under_department:
        type: integer
        description: 科室下医生列表：0，显示科室下所有医生；1，显示有排班的医生；
      need_payment:
        type: boolean
        description: 是否需要支付， 0:不需要，1:需要
      support_locking_appointment_no:
        type: boolean
        description: 是否支持锁号,false:不支持,true:支持
      need_partition_time:
        type: boolean
        description: 是否需要分时,false:不需要,true:需要
      display_department_address:
        type: boolean
        description: 是否显示科室地址
      display_visit_time:
        type: boolean
        description: 是否显示就诊时间
  CustomerPatientReportSettingVo:
    type: object
    properties:
      support_report_type:
        type: integer
        description: 支持的报告类型，0：全部，1：检验报告，2：检查报告
        enum:
          - 0
          - 1
          - 2
      support_search_date_range:
        type: integer
        description: 支持的按钮刻度，0：无限制，1：30天内，2：90天内，3：180天内，3：一年内
        enum:
          - 0
          - 1
          - 2
          - 3
      support_search_time:
        type: array
        description: 支持的查询时间
        items:
          type: integer
          description: 0：全部，1：最近一周，2：最近一月，3：最近三月，4：最近半年，5：最近一年
          enum:
            - 0
            - 1
            - 2
            - 3
            - 4
            - 5
      notice:
        type: string
        description: 提示消息

  CustomerQueryHospitalAreaPageReqDto:
    type: object
    required:
      - current_page
      - page_size
    properties:
      current_page:
        description: 当前页
        type: integer
        default: 1
        minimum: 1
      page_size:
        description: 每页条数
        type: integer
        default: 10
        minimum: 10
      category:
        type: array
        items:
          type: integer
        description: 院区类型,0，常规；1，疫苗预约；2，特需门诊；3，多学科；9，其他；
      tag_dict_label:
        type: string
        description: 标签名称

  CustomerQueryDepartmentPageReqDto:
    type: object
    required:
      - current_page
      - page_size
      - hospital_area_id
    properties:
      current_page:
        description: 当前页
        type: integer
        default: 1
        minimum: 1
      page_size:
        description: 每页条数
        type: integer
        default: 10
        minimum: 10
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      category:
        type: array
        items:
          type: integer
        description: 科室类别：0，常规；1，热门；2，特色；3，多学科；4，特需；5，疫苗；9，其他；
      category_in:
        type: array
        items:
          type: integer
        description: 科室类别：0，常规；1，热门；2，特色；3，多学科；4，特需；5，疫苗；9，其他；


  CustomerDepartmentPageVo:
    allOf:
      - $ref: 'apis.yml#/definitions/BasePage'
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: '#/definitions/CustomerDepartmentVo'

  # vaccine
  CustomerVaccineCategoryVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 苗种id
      name:
        type: string
        description: 苗种名称
      sort:
        type: integer
        format: int32
        description: 排序
      logo:
        type: string
        description: 苗种图标地址
      is_hot:
        type: integer
        description: 是否热门
      hot_sort:
        type: integer
        format: int32
        description: 热门排序
  CustomerVaccineCategoryPageVo:
    allOf:
      - $ref: 'apis.yml#/definitions/BasePage'
      - type: object
        properties:
          list:
            type: array
            description: 疫苗分类列表
            items:
              $ref: '#/definitions/CustomerVaccineCategoryVo'
  CustomerVaccineCategoryPageReqDto:
    type: object
    required:
      - current_page
      - page_size
    properties:
      current_page:
        description: 当前页
        type: integer
        default: 1
        minimum: 1
      page_size:
        description: 每页条数
        type: integer
        default: 10
        minimum: 10
      name:
        type: string
        description: 苗种名称
  CustomerVaccineOrganizationVo:
    type: object
    properties:
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_area_id:
        type: integer
        format: int64
        description: 医院院区id
      hospital_code:
        type: string
        description: 医院编码
      hospital_name:
        type: string
        description: 医院名称
      logo:
        type: string
        description: 医院logo
      address_id:
        type: integer
        format: int64
        description: 医院地址id
      distance:
        type: number
        format: double
        description: 距离，单位km
      address:
        type: string
        description: 医院地址
      hot_vaccine_list:
        type: array
        description: 热门苗种列表
        items:
          $ref: '#/definitions/HotVaccineCategoryItemVo'
  HotVaccineCategoryItemVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 苗种id
      name:
        type: string
        description: 苗种名称
      is_hot:
        type: integer
        format: int32
        description: 是否热门 0 - 否 1 - 是
      hot_sort:
        type: integer
        format: int32
        description: 热门排序
  CustomerVaccineOrganizationListVo:
    type: object
    properties:
      records:
        type: array
        description: 疫苗机构列表
        items:
          $ref: '#/definitions/CustomerVaccineOrganizationVo'
  CustomerVaccineOrganizationListReqDto:
    type: object
    properties:
      vaccine_category_id:
        type: integer
        format: int64
        description: 疫苗分类id
      name:
        type: string
        description: 医院名称
      longitude:
        type: number
        description: 经度
        format: double
      latitude:
        type: number
        description: 纬度
        format: double
      county:
        type: string
        description: 县/区
      sort:
        type: integer
        format: int32
        default: 0
        description: 排序，0：默认，1：距离
  CustomerVaccineVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 疫苗id
      category_id:
        type: integer
        format: int64
        description: 苗种id
      category_name:
        type: string
        description: 疫苗分类名称
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_area_id:
        type: integer
        format: int64
        description: 医院院区id
      hospital_area_name:
        type: string
        description: 医院院区名称
      hospital_code:
        type: string
        description: 医院编码
      department_id:
        type: integer
        format: int64
        description: 科室id
      department_name:
        type: string
        description: 科室名称
      doctor_id:
        type: integer
        format: int64
        description: 医生id
      doctor_name:
        type: string
        description: 医生名称
      logo:
        type: string
        description: 疫苗图标地址
      is_hot:
        type: integer
        format: int32
        description: 是否热门 0 - 否 1 - 是
      hot_sort:
        type: integer
        format: int32
        description: 热门排序
      tips:
        type: string
        description: 预约提示(弹窗等)
      remark:
        type: string
        description: 其他说明(放号时间等)
      sort:
        type: integer
        format: int32
        description: 排序
      status:
        type: integer
        format: int32
        description: 状态，0：停用，1：启用
      distance:
        type: number
        format: double
        description: 距离，单位km
      address:
        type: string
        description: 医院地址
      schedule_list:
        type: array
        description: 排班列表
        items:
          type: string
      appointment_type:
        type: integer
        format: int32
        description: 预约类型，1：可预约；2：可登记；3：暂无号
  CustomerVaccineListReqDto:
    type: object
    properties:
      vaccine_category_id:
        type: integer
        format: int64
        description: 疫苗分类id
      hospital_area_id:
        type: integer
        format: int64
        description: 机构id(医院院区id)
      name:
        type: string
        description: 苗种名称或医生名称
      longitude:
        type: number
        format: double
        description: 经度
      latitude:
        type: number
        format: double
        description: 纬度
  CustomerVaccineSearchRespVo:
    type: object
    properties:
      vaccine_list_stocked:
        type: array
        description: 有号源疫苗列表
        items:
          $ref: '#/definitions/CustomerVaccineVo'
      vaccine_organization_list:
        type: array
        description: 疫苗机构列表
        items:
          $ref: '#/definitions/CustomerVaccineOrganizationVo'
      vaccine_list_no_stocked:
        type: array
        description: 无号源疫苗列表
        items:
          $ref: '#/definitions/CustomerVaccineVo'

  SearchHospitalReqDto:
    type: object
    required:
      - current_page
      - page_size
      - keywords
      - status
    properties:
      current_page:
        type: integer
        description: 当前页
        minimum: 1
      page_size:
        type: integer
        description: 每页条数
        minimum: 10
        maximum: 30
      keywords:
        type: string
        description: 搜索关键字
      status:
        type: array
        items:
          type: integer
          format: int32
        description: 0:启用，1:开发中，2:维护，3:禁用
  SearchHospitalVo:
    type: object
    properties:
      total:
        type: integer
        format: int64
        description: 医院总数
      hospital_list:
        type: array
        description: 医院列表
        items:
          $ref: '#/definitions/CustomerHospitalSearchVo'
  CustomerHospitalSearchVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 医院id
      name:
        type: string
        description: 医院名称
      logo:
        type: string
        description: 医院logo
      display:
        type: boolean
        description: 是否展示
      status:
        type: integer
        format: int32
        description: 0:启用，1:开发中，2:维护，3:禁用
      business_tags:
        type: array
        items:
          type: string
        description: 业务标签
      customer_tags:
        type: array
        items:
          type: string
        description: 自定义标签
      hospital_area_list:
        type: array
        description: 院区列表
        items:
          $ref: '#/definitions/CustomerHospitalAreaVo'
  SearchDepartmentAndDoctorReqDto:
    type: object
    required:
      - current_page
      - page_size
      - keywords
    properties:
      current_page:
        type: integer
        description: 当前页
        minimum: 1
      page_size:
        type: integer
        description: 每页条数
        minimum: 10
        maximum: 30
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      keywords:
        type: string
        description: 搜索关键字
      type:
        type: integer
        format: int32
        description: 搜索类型，0:全部,1：科室,2：医生
        default: 0
      department_enabled:
        type: boolean
        description: 是否启用
      doctor_status:
        type: array
        items:
          type: integer
          format: int32
        description: 医生状态，0:启用，1:禁用

  SearchDepartmentAndDoctorVo:
    type: object
    properties:
      department_total:
        type: integer
        format: int64
        description: 科室总数
      department_list:
        type: array
        description: 科室列表
        items:
          $ref: '#/definitions/CustomerDepartmentSearchVo'
      doctor_total:
        type: integer
        format: int64
        description: 医生总数
      doctor_list:
        type: array
        description: 医生列表
        items:
          $ref: '#/definitions/CustomerAllScheduleDoctorDetailVo'

  CustomerDepartmentSearchVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 科室id
      name:
        type: string
        description: 科室名称
      short_name:
        type: string
        description: 科室简称
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_logo:
        type: string
        description: 医院logo
      hospital_area_id:
        type: integer
        format: int64
        description: 医院院区id
      hospital_area_name:
        type: string
        description: 医院院区名称
      hospital_area_code:
        type: string
        description: 医院院区编码
      address_intro:
        type: string
        description: 医院地址
      introduction:
        type: string
        description: 科室简介
      recommended:
        type: boolean
        description: 是否推荐
      uri:
        type: string
        description: 科室跳转地址
      enabled:
        type: boolean
        description: 是否启用
      can_order:
        type: boolean
        description: 是否可预约
      thrdpart_dep_code:
        type: string
        description: 科室编码
  CustomerDoctorSearchVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 医生id
      name:
        type: string
        description: 医生名称
      doctor_code:
        type: string
        description: 医生编码
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      hospital_area_name:
        type: string
        description: 院区名称
      hospital_area_code:
        type: string
        description: 院区编码
      department_id:
        type: integer
        format: int64
        description: 科室id
      department_name:
        type: string
        description: 科室名称
      department_code:
        type: string
        description: 科室编码
      head_img:
        type: string
        description: 医生头像
      speciality:
        type: string
        description: 医生擅长
      can_order:
        type: boolean
        description: 是否可预约
      show_sch_date:
        type: boolean
      belong_doctor_groups:
        type: boolean
        description: 是否属于某个医生团队
      can_order_days:
        type: array
        items:
          $ref: '#/definitions/CustomerDoctorScheduleDateVo'
      customer_tags:
        type: array
        items:
          type: string
        description: 自定义标签
  CustomerDoctorScheduleDateVo:
    type: object
    properties:
      sch_date:
        type: string
      date_in_week:
        type: string
      can_order:
        type: boolean
      show_sch_date:
        type: boolean

  GetCodeMappingReqDto:
    type: object
    required:
      - hospital_area_code
    properties:
      hospital_area_code:
        type: string
        description: 院区编码
      department_code:
        type: string
        description: 科室编码
      doctor_code:
        type: string
        description: 医生编码
  GetCodeMappingVo:
    type: object
    properties:
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      department_id:
        type: integer
        format: int64
        description: 科室id
      doctor_id:
        type: integer
        format: int64
        description: 医生id

  # recommend
  RecommendBaseItemVo:
    type: object
    properties:
      recommend_id:
        type: integer
        format: int64
        description: 推荐id
      redirect_url:
        type: string
        description: 跳转链接
      display_tags:
        type: array
        items:
          type: string
        description: 展示标签
      biz_type:
        type: string
        description: 业务类型
      data_type:
        type: string
        description: 数据类型
      data_tag:
        type: string
        description: 数据标签
      img_url:
        type: string
        description: 图片地址
      display_name:
        type: string
        description: 展示名称
      recommend_sort:
        type: integer
        format: int32
        description: 推荐配置排序
  RecommendHospitalListItemVo:
    type: object
    allOf:
      - $ref: "#/definitions/RecommendBaseItemVo"
    properties:
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_name:
        type: string
        description: 医院名称
      logo:
        type: string
        description: 医院logo
      hospital_area_list:
        type: array
        items:
          $ref: "#/definitions/RecommendHospitalAreaItem"
      status:
        type: integer
        format: int32
        description: 0:启用，1:开发中，2:维护，3:禁用
      province:
        type: string
        description: 省
      city:
        type: string
        description: 市
      county:
        type: string
        description: 区
      level:
        type: string
        description: 医院等级
  RecommendHospitalAreaItem:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 医院院区id
      code:
        type: string
        description: 医院院区编码
      status:
        type: integer
        format: int32
        description: 0:启用，1:开发中，2:维护，3:禁用
      categories:
        type: array
        items:
          type: integer
          format: int32
        description: 院区类别
  RecommendHospitalAreaListItemVo:
    type: object
    allOf:
      - $ref: "#/definitions/RecommendBaseItemVo"
    properties:
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_code:
        type: string
        description: 医院编码
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      hospital_area_name:
        type: string
        description: 院区名称
      logo:
        type: string
        description: 医院logo
      address:
        type: string
        description: 医院地址
      distance:
        type: number
        format: double
        description: 距离，单位km
      status:
        type: integer
        format: int32
        description: 0:启用，1:开发中，2:维护，3:禁用
      level:
        type: string
        description: 医院等级
  RecommendDepartmentListItemVo:
    type: object
    allOf:
      - $ref: "#/definitions/RecommendBaseItemVo"
    properties:
      department_id:
        type: integer
        format: int64
        description: 科室id
      department_name:
        type: string
        description: 科室名称
      hospital_code:
        type: string
        description: 医院编码
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_name:
        type: string
        description: 医院名称
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      hospital_area_name:
        type: string
        description: 院区名称
      hospital_area_logo:
        type: string
        description: 院区logo
      address_intro:
        type: string
        description: 医院地址
      introduction:
        type: string
        description: 科室简介
  RecommendDoctorListItemVo:
    type: object
    allOf:
      - $ref: "#/definitions/RecommendBaseItemVo"
    properties:
      doctor_id:
        type: integer
        format: int64
        description: 医生id
      doctor_name:
        type: string
        description: 医生名称
      doctor_code:
        type: string
        description: 医生编码
      hospital_code:
        type: string
        description: 医院编码
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      hospital_area_name:
        type: string
        description: 院区名称
      department_id:
        type: integer
        format: int64
        description: 科室id
      department_name:
        type: string
        description: 科室名称
      department_code:
        type: string
        description: 科室编码
      logo:
        type: string
        description: 医生头像
      speciality:
        type: string
        description: 医生擅长
      level:
        type: string
        description: 医生职称
      introduction:
        type: string
        description: 医生简介
      can_order:
        type: boolean
      show_sch_date:
        type: boolean
      can_order_days:
        type: array
        items:
          $ref: '#/definitions/CustomerDoctorScheduleDateVo'
  RecommendOnlineDoctorListItemVo:
    type: object
    allOf:
      - $ref: "#/definitions/RecommendBaseItemVo"
    properties:
      doctor_id:
        type: integer
        format: int64
        description: 医生id
      doctor_name:
        type: string
        description: 医生名称
      hospital_code:
        type: string
        description: 医院编码
      hospital_name:
        type: string
        description: 医院名称
      department_name:
        type: string
        description: 科室名称
      department_code:
        type: string
        description: 科室编码
      doctor_code:
        type: string
        description: 医生编码
      logo:
        type: string
        description: 医生头像
      speciality:
        type: string
        description: 医生擅长
      level:
        type: string
        description: 医生职称
      working_years:
        type: integer
        format: int32
        description: 从业年限
      reply_speed:
        type: string
        description: 回复速度
      answer_count:
        type: integer
        description: 回答数
      introduction:
        type: string
        description: 医生简介
      is_recommend:
        type: boolean
        description: 是否推荐
      is_hot:
        type: boolean
        description: 是否热门
  RecommendRespVo:
    type: object
    properties:
      hospital_list:
        type: array
        description: 医院列表
        items:
          $ref: '#/definitions/RecommendHospitalListItemVo'
      hospital_area_list:
        type: array
        description: 院区列表
        items:
          $ref: '#/definitions/RecommendHospitalAreaListItemVo'
      department_list:
        type: array
        description: 科室列表
        items:
          $ref: '#/definitions/RecommendDepartmentListItemVo'
      doctor_list:
        type: array
        description: 医生列表
        items:
          $ref: '#/definitions/RecommendDoctorListItemVo'
      online_doctor_list:
        type: array
        description: 在线医生列表
        items:
          $ref: '#/definitions/RecommendOnlineDoctorListItemVo'

  CustomerDoctorAskServiceVo:
    type: object
    properties:
      doctor_id:
        type: integer
        format: int64
        description: 医生id
      doctor_code:
        type: string
        description: 医生编码
      doctor_groups:
        type: array
        description: 医生分组
        items:
          $ref: '#/definitions/CustomerDoctorGroupVo'
  CustomerDoctorGroupVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 医生团队ID
      name:
        type: string
        description: 医生团队名称
      free:
        type: boolean
        description: 是否免费
      free_start_time:
        type: string
        description: 免费开始时间
      free_end_time:
        type: string
        description: 免费结束时间
      count:
        type: integer
        description: 服务次数
      services:
        type: array
        description: 服务列表
        items:
          $ref: '#/definitions/CustomerDoctorGroupServiceVo'
  CustomerDoctorGroupServiceVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 服务ID
      name:
        type: string
        description: 服务名称
      description:
        type: string
        description: 服务描述
      price:
        type: number
        format: double
        description: 服务价格
      type:
        type: integer
        format: int32
        description: 服务类型
      duration:
        type: integer
        format: int32
        description: 服务时长
      times:
        type: integer
        format: int32
        description: 服务次数
      status:
        type: integer
        format: int32
        description: 服务状态

  CustomerHospitalAreaPositionReqDto:
    type: object
    properties:
      hospital_area_id:
        type: integer
        format: int64
        description: 医院院区id
      hospital_area_code:
        type: string
        description: 医院院区编码

  CustomerHospitalAreaPositionVo:
    type: object
    properties:
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      hospital_area_code:
        type: string
        description: 院区编码
      children:
        type: array
        description: 大楼列表
        items:
          $ref: '#/definitions/CustomerHospitalAreaBuildingVo'
  CustomerHospitalAreaBuildingVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 大楼ID
      name:
        type: string
        description: 大楼名称
      sort:
        type: integer
      location_list:
        type: array
        description: 位置列表
        items:
          $ref: '#/definitions/CustomerHospitalAreaLocationVo'
      children:
        type: array
        description: 楼层列表
        items:
          $ref: '#/definitions/CustomerHospitalAreaFloorVo'
  CustomerHospitalAreaFloorVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 楼层ID
      name:
        type: string
        description: 楼层名称
      sort:
        type: integer
      location_list:
        type: array
        description: 位置列表
        items:
          $ref: '#/definitions/CustomerHospitalAreaLocationVo'
      children:
        type: array
        description: 位置列表
        items:
          $ref: '#/definitions/CustomerHospitalAreaAreaVo'
  CustomerHospitalAreaAreaVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 区域ID
      name:
        type: string
        description: 位置名称
      sort:
        type: integer
      location_list:
        type: array
        description: 位置列表
        items:
          $ref: '#/definitions/CustomerHospitalAreaLocationVo'
  CustomerHospitalAreaLocationVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 位置ID
      type:
        type: integer
        format: int32
        description: 位置类型,0:科室,9:自定义
      department_id:
        type: integer
        format: int64
        description: 科室ID
      name:
        type: string
        description: 位置名称
      sort:
        type: integer

  CustomerDoctorScheduledDepartmentVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 医生ID
      name:
        type: string
        description: 医生名称
      head_img_url:
        type: string
        description: 医生头像
      level:
        type: string
        description: 医生职称
      summary:
        type: string
        description: 医生简介
      speciality:
        type: string
        description: 医生擅长
      hospital_area_name:
        type: string
        description: 院区名称
      department_name:
        type: string
        description: 科室名称
      department_schedule_list:
        type: array
        description: 科室排班列表
        items:
          $ref: '#/definitions/CustomerScheduledDepartmentVo'
  CustomerScheduledDepartmentVo:
    type: object
    properties:
      department_id:
        type: integer
        format: int64
        description: 科室ID
      department_name:
        type: string
        description: 科室名称
      department_code:
        type: string
        description: 科室编码
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      hospital_area_code:
        type: string
        description: 院区编码
      hospital_area_name:
        type: string
        description: 院区名称
      doctor_id:
        type: integer
        format: int64
        description: 医生ID
      doctor_code:
        type: string
        description: 医生编码
      doctor_category:
        type: string
      rank_dict_label:
        type: array
        items:
          type: string
      rank_dict_value:
        type: array
        items:
          type: string
      hospital_area_appointment_notice:
        type: string
        description: 院区预约提示
      doctor_appointment_notice:
        type: string
        description: 医生预约提示
      sort:
        type: integer
  CustomerScheduleVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 排班ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      hospital_code:
        type: string
        description: 医院编码
      department_id:
        type: integer
        format: int64
        description: 科室ID
      department_name:
        type: string
        description: 科室名称
      department_code:
        type: string
        description: 科室编码
      doctor_code:
        type: string
        description: 医生编码
      doctor_id:
        type: integer
        format: int64
        description: 医生ID
      doctor_name:
        type: string
        description: 医生名称
      start_time:
        type: string
        description: 开始时间
      end_time:
        type: string
        description: 结束时间
      schedule_id:
        type: string
        description: 排班ID
      schedule_info:
        type: string
        description: 排班信息
      sch_date:
        type: string
        description: 排班日期
      ghf:
        type: integer
        format: int32
      zjf:
        type: integer
        format: int32
      zlf:
        type: integer
        format: int32
      total_fee:
        type: integer
        format: int32
      total_fee_format:
        type: string
      src_num:
        type: integer
        format: int32
      used_num:
        type: integer
        format: int32
      time_part:
        type: integer
        format: int32
      time_type:
        type: integer
        format: int32
      time_type_text:
        type: string
        description: 时间类型
      time_type_his:
        type: string
      status:
        type: integer
        format: int32

  RecommendStatisticsReqDto:
    type: object
    properties:
      rid:
        type: integer
        format: int64
        description: 推荐id
      user_action:
        type: object
        $ref: '#/definitions/UserActionDto'

  UserActionDto:
    type: object
    properties:
      ip:
        type: string
        description: ip地址
      channel:
        type: integer
        format: int32
        description: 渠道
      action:
        type: integer
        format: int32
        description: 用户动作
      action_resource:
        type: string
        description: 用户动作资源

  CustomBulletinConfigVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 通知公告id
      system_status:
        type: integer
        description: 系统状态：0，正常；1，维护，默认正常
      bulletin_content:
        type: string
        description: 通知公告内容
      status:
        type: integer
        description: 状态：0，正常；1，停用，默认停用
  RecommendConfigDepartmentDto:
    type: object
    properties:
      department_name:
        type: string
        description: 科室名称
      biz_type:
        type: string
        description: 业务类型
      sort:
        type: integer
        description: 排序
  CustomRecommendDoctorQueryDto:
    type: object
    properties:
      page_num:
        type: integer
        format: int32
        description: 页码
      page_size:
        type: integer
        format: int32
        description: 每页显示数量
      doctor_id:
        type: integer
        format: int64
        description: 医生ID（可选）
      hospital_id:
        type: integer
        format: int64
        description: 医院ID（可选）
      department_id:
        type: integer
        format: int64
        description: 科室ID（可选）
      hospital_area_id:
        type: integer
        format: int64
        description: 医院区域ID（可选）
      hospital_area_code:
        type: string
        description: 医院区域代码（可选）
      hospital_area_name:
        type: string
        description: 医院区域名称（可选）
      hospital_name:
        type: string
        description: 医院名称（可选）
      data_tag:
        type: string
        description: 数据标签（可选）
      enabled:
        type: boolean
        description: 是否启用（可选）
      longitude:
        type: number
        description: 经度
        format: double
      latitude:
        type: number
        description: 纬度
        format: double
  CustomRecommendDoctorPageVo:
    type: object
    allOf:
      - $ref: 'apis.yml#/definitions/BasePage'
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/CustomRecommendDepartInfoVo"
  CustomRecommendDepartInfoVo:
    type: object
    properties:
      recommend_id:
        type: integer
        format: int64
        description: 推荐id
      biz_type:
        type: string
        description: 业务类型
      hospital_name:
        type: string
        description: 医院名称
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_name:
        type: string
        description: 院区名称
      hospital_area_code:
        type: string
        description: 院区代码
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      hospital_logo_img_url:
        type: string
        description: 院区logo地址
      department_id:
        type: integer
        format: int64
        description: 科室ID
      department_name:
        type: string
        description: 科室名称
      department_code:
        type: string
        description: 科室编码
      key_words:
        type: string
        description: 关键词(用|分隔)
      key_word_list:
        type: array
        description: 关键词列表
        items:
          $ref: '#/definitions/CustomRecommendKeyWordVo'
      recommend_score:
        type: number
        format: double
        description: 推荐分数
        default: 0
      recommend_reason:
        type: string
        description: 推荐原因
      specially:
        type: string
        description: 擅长
      sort:
        type: integer
        format: int32
        description: 排序，由大到小
      data_type:
        type: integer
        description: 数据类型（2：科室，4：：院区）
      distance:
        type: number
        format: double
        description: 距离
      address_id:
        type: integer
        format: int64
        description: 地址

  CustomRecommendKeyWordVo:
    type: object
    properties:
      key_word:
        type: string
        description: 关键词
      recommend_doctor_list:
        type: array
        description: 推荐医生列表
        items:
          $ref: '#/definitions/CustomRecommendDoctorVo'
  CustomRecommendDoctorVo:
    type: object
    properties:
      id:
        type: integer
        description: 主键ID
        format: int64
      recommend_id:
        type: integer
        description: 推荐配置ID
        format: int64
      doctor_id:
        type: integer
        description: 医生ID
        format: int64
      doctor_image_url:
        type: string
        description: 医生头像
      doctor_name:
        type: string
        description: 医生名称
      doctor_code:
        type: string
        description: 医生编码
      department_id:
        type: integer
        description: 所属科室ID
        format: int64
      department_code:
        type: string
        description: 所属科室名称
      department_name:
        type: string
        description: 所属科室名称
      hospital_area_id:
        type: integer
        description: 所属院区ID
        format: int64
      hospital_area_code:
        type: string
        description: 所属院区编码
      hospital_area_name:
        type: string
        description: 所属院区名称
      hospital_id:
        type: integer
        description: 所属医院编码
        format: int64
      sort:
        type: integer
        description: 排序号，默认为0
        default: 0
      hospital_name:
        type: string
        description: 所属医院名称
      data_tag:
        type: string
        description: 数据标签，当在相同的 biz_type、data_type 下，通过 data_tag，可以同一 data_type 设置多次
      data_id:
        type: string
        description: 数据全局唯一标识
      data_name:
        type: string
        description: 数据名称
      doctor_title:
        type: string
        description: 医生职称
      introduction:
        type: string
        description: 医生简介
  RecentlyRegistrationDepartmentVo:
    type: object
    properties:
      value:
        type: string
        description: 最近挂号科室字典值
        default: ''
  UserDoctorSubscriptionCreateDto:
    type: object
    description: 用于创建用户医生订阅的数据传输对象
    properties:
      user_id:
        type: integer
        description: 用户ID
        format: int64
      doctor_id:
        type: integer
        description: 医生ID
        format: int64
      channel_tag:
        type: string
        description: 订阅渠道标签(小程序：dyt-min-app，新官方号：dyt-official-account，老官方号：dyt-official-account-old)
        default: 'dyt-official-account'

  UserDoctorSubscriptionUpdateDto:
    type: object
    description: 用于更新用户医生订阅的数据传输对象
    properties:
      doctor_code:
        type: string
        description: 医生编码
      doctor_name:
        type: string
        description: 医生名称
      department_id:
        type: integer
        format: int64
        description: 科室ID
      department_code:
        type: string
        description: 科室编码
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      hospital_area_code:
        type: string
        description: 院区编码
      hospital_area_name:
        type: string
        description: 院区名称
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_name:
        type: string
        description: 医院名称
      status:
        type: integer
        description: 订阅状态（0：待通知，1：开始通知，2：通知结束，3：通知失败，-1：已取消）

  UserDoctorSubscriptionVo:
    type: object
    description: 用户医生订阅的视图对象
    properties:
      id:
        type: integer
        format: int64
        description: 用户医生订阅ID
      user_id:
        type: integer
        format: int64
        description: 用户ID
      doctor_id:
        type: integer
        format: int64
        description: 医生ID
      doctor_code:
        type: string
        description: 医生编码

      doctor_name:
        type: string
        description: 医生名称
      department_id:
        type: integer
        format: int64
        description: 科室ID
      department_code:
        type: string
        description: 科室编码
      department_name:
        type: string
        description: 科室名称
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      hospital_area_code:
        type: string
        description: 院区编码
      hospital_area_name:
        type: string
        description: 院区名称
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_name:
        type: string
        description: 医院名称
      status:
        type: integer
        description: 订阅状态（0：待通知，1：开始通知，2：通知结束，3：通知失败，-1：已取消）
      notice_count:
        type: integer
        description: 已通知次数
      remain_notice_count:
        type: integer
        description: 剩余通知次数
      doctor_title:
        type: string
        description: 医生职称统一返回字段
      head_img_url:
        type: string
        description: 头像 URL 地址

  # begin-------快速检查--------begin
  CustomerQuickCheckQueryDepartmentReqDto:
    type: object
    required:
      - hospital_area_id
    properties:
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      time_type:
        type: integer
        description: 时间类型

  CustomerQuickCheckDepartmentVo:
    allOf:
      - $ref: "#/definitions/CustomerDepartmentVo"
      - type: object
        properties:
          all_doctors:
            type: array
            description: 医生集合
            items:
              $ref: "#/definitions/CustomerQuickCheckAllScheduleDoctorDetailVo"

  CustomerQuickCheckDepartmentReponse:
    type: object
    properties:
      list:
        type: array
        items:
          $ref: '#/definitions/CustomerQuickCheckDepartmentVo'

  CustomerQuickCheckQueryDoctorsPageReqDto:
    type: object
    required:
      - current_page
      - page_size
      - quick_check_type
    properties:
      current_page:
        description: 当前页
        type: integer
        default: 1
        minimum: 1
      page_size:
        description: 每页条数
        type: integer
        default: 10
        minimum: 10
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      quick_check_type:
        type: string
        description: 快速检查类型编码
      doctor_tags:
        type: array
        items:
          type: integer
        description: 医生标签(筛选)
      sort_by:
        type: string
        description: 排序方式(默认：综合排序)
      direction:
        type: string
        description: 排序方向(默认：升序)
      longitude:
        type: number
        description: 经度
        format: double
      latitude:
        type: number
        description: 纬度
        format: double
      max_price:
        type: number
        description: 最高服务价格
        format: double
      min_price:
        type: number
        format: double
        description: 最低服务价格

  CustomerQuickCheckDoctorVo:
    allOf:
      - $ref: "#/definitions/CustomerDoctorVo"
      - type: object
        properties:
          min_price:
            type: number
            description: 最低服务价格
          data_tag:
            type: string
            description: 数据标签（可选）
          booked_num:
            type: integer
            description: 已预约数量
          quick_check_type:
            type: string
            description: 快速检查类型
          distance:
            type: number
            format: double
            description: 距离

  CustomerQuickCheckDoctorPageVo:
    allOf:
      - $ref: 'apis.yml#/definitions/BasePage'
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: '#/definitions/CustomerQuickCheckDoctorVo'



  CustomerQuickCheckAllScheduleDoctorDetailVo:
    type: object
    allOf:
      - $ref: "#/definitions/CustomerDoctorVo"
      - type: object
        properties:
          introduction:
            type: string
            description: 简介
          date_in_weeks:
            type: array
            description: 医生排班日期
            items:
              $ref: "#/definitions/CustomerQuickCheckDateInWeekDto"
          show_sch_date:
            type: boolean
            description: 显示排班日期
          can_order:
            type: boolean
            description: 是否可预约(true：显示可预约，false：显示暂无号)
          customer_tags:
            type: array
            items:
              type: string
            description: 自定义标签
          data_tag:
            type: string
            description: 数据标签
          appointment_rule_list:
            type: array
            description: 预约规则
            items:
              type: string

  CustomerQuickCheckDateInWeekDto:
    type: object
    properties:
      date_in_week:
        type: string
        description: 星期几
      date:
        type: string
        description: 日期
      local_date:
        type: string
        description: 全量日期
        format: date
      can_order:
        type: boolean
        description: 是否可预约(true：显示有号，false：显示无号)
      schedule_info:
        type: object

  CustomerQuickCheckDateInWeekScheduleInfo:
    type: object
    properties:
      create_by:
        type: string
        description: 创建者
      create_time:
        type: string
        description: 创建时间
      department_id:
        type: integer
        format: int64
        description: 科室ID
      department_name:
        type: string
        description: 科室名称
      department_code:
        type: string
        description: 科室编码
      exec_department_code:
        type: string
        description: 执行科室编码
      doctor_id:
        type: integer
        format: int64
        description: 医生ID
      doctor_code:
        type: string
        description: 医生编码
      doctor_name:
        type: string
        description: 医生名称
      edit_by:
        type: string
        description: 编辑者
      end_time:
        type: string
        description: 结束时间
      ghf:
        type: integer
        format: int32
        description: 费用GHF
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      hospital_area_name:
        type: string
        description: 院区名称
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_name:
        type: string
        description: 医院名称
      id:
        type: integer
        format: int64
        description: 主键ID
      max_treat_num:
        type: integer
        format: int32
        description: 最大就诊数
      sch_date:
        type: string
        format: date
        description: 排班日期
      schedule_id:
        type: string
        description: 排班ID
      schedule_info:
        type: string
        description: 排班信息
      show_sch_date:
        type: integer
        description: 显示排班日期
      src_number:
        type: integer
        format: int32
        description: 号源数量
      start_time:
        type: string
        description: 开始时间
      status:
        type: integer
        format: int32
        description: 状态
      time_part:
        type: integer
        format: int32
        description: 时间部分
      time_type:
        type: integer
        format: int32
        description: 时间类型
      time_type_his:
        type: string
        description: 历史时间类型
      time_type_text:
        type: string
        description: 时间类型文本
      total_fee:
        type: number
        description: 总费用
      total_fee_format:
        type: string
        description: 总费用格式化
      update_time:
        type: string
        description: 更新时间
      used_num:
        type: integer
        format: int32
        description: 已使用号数量
      zjf:
        type: integer
        format: int32
        description: 自费金额
      zlf:
        type: integer
        format: int32
        description: 自理金额
      registration_date:
        type: integer
        format: int64
        description: 挂号日期
      registration_level_desc:
        type: string
        description: 挂号级别描述


  CustomerQuickCheckDoctorScheduleInfoVo:
    type: object
    properties:
      system_depends:
        type: integer
        description: 挂号系统依赖：0，HIS；1，小系统；
      all_doctors:
        type: array
        description: 医生集合
        items:
          $ref: "#/definitions/CustomerQuickCheckAllScheduleDoctorDetailVo"

  ExpertAppointmentDoctorPageVo:
    allOf:
      - $ref: 'apis.yml#/definitions/BasePage'
      - type: object
        properties:
          list:
            type: array
            items:
              type: object
              properties:
                hospital_id:
                  type: integer
                  format: int64
                  description: 医院id
                hospital_area_id:
                  type: integer
                  format: int64
                  description: 院区id
                hospital_code:
                  type: string
                  description: 医院编码
                hospital_name:
                  type: string
                  description: 医院名称
                hospital_logo:
                  type: string
                  description: 医院logo
                doctors:
                  type: array
                  description: 医生列表
                  items:
                    type: object
                    properties:
                      doctor_id:
                        type: integer
                        format: int64
                        description: 医生id
                      doctor_name:
                        type: string
                        description: 医生名称
                      doctor_head_img_url:
                        type: string
                        description: 医生头像url
                      doctor_code:
                        type: string
                        description: 医生编码
                      department_id:
                        type: integer
                        format: int64
                        description: 科室id
                      department_code:
                        type: string
                        description: 科室编码

  CustomerQuickCheckAllHospitalVo:
    type: object
    properties:
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      hospital_code:
        type: string
        description: 医院编码
      hospital_name:
        type: string
        description: 医院名称
      hospital_level:
        type: string
        description: 医院等级
      hospital_logo:
        type: string
        description: 医院logo
      distance:
        type: number
        format: double
        description: 距离
      booked_num:
        type: integer
        description: 已预约数量
      check_items:
        type: array
        description: 检查项目列表
        items:
          type: object
          properties:
            check_item_name:
              type: string
              description: 检查项目名称
            min_price:
              type: number
              description: 最低服务价格
            quick_check_type:
              type: string
              description: 快速检查类型
      sort:
        type: integer
        description: 排序
# end-------快速检查--------end
