package com.ynhdkc.tenant.dao.impl;

import com.ynhdkc.tenant.dao.OrganizationStructureQuery;
import com.ynhdkc.tenant.dao.mapper.TenantUserStructureMapper;
import com.ynhdkc.tenant.entity.TenantUserStructure;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-25 18:01
 */
@Repository
@RequiredArgsConstructor
public class OrganizationStructureQueryImpl implements OrganizationStructureQuery {
    private final TenantUserStructureMapper tenantUserStructureMapper;

    @Override
    public List<TenantUserStructure> queryUserStructure(OrganizationStructureQueryOption option) {
        return tenantUserStructureMapper.selectByExample(TenantUserStructure.class, sql -> {
            sql.defGroup(where -> {
                if (null != option.getUserId()) {
                    where.andEqualTo(TenantUserStructure::getUserId, option.getUserId());
                }
                if (null != option.getTenantId()) {
                    where.andEqualTo(TenantUserStructure::getTenantId, option.getTenantId());
                }
                if (null != option.getHospitalId()) {
                    where.andEqualTo(TenantUserStructure::getHospitalId, option.getHospitalId());
                }
                if (null != option.getHospitalAreaId()) {
                    where.andEqualTo(TenantUserStructure::getHospitalAreaId, option.getHospitalAreaId());
                }
                if (null != option.getDepartmentId()) {
                    where.andEqualTo(TenantUserStructure::getDepartmentId, option.getDepartmentId());
                }

                if (!CollectionUtils.isEmpty(option.getIncludeTenantIds())) {
                    where.andIn(TenantUserStructure::getTenantId, option.getIncludeTenantIds());
                }
                if (!CollectionUtils.isEmpty(option.getExcludeTenantIds())) {
                    where.andNotIn(TenantUserStructure::getTenantId, option.getExcludeTenantIds());
                }
                if (!CollectionUtils.isEmpty(option.getIncludeHospitalIds())) {
                    where.andIn(TenantUserStructure::getHospitalId, option.getIncludeHospitalIds());
                }
                if (!CollectionUtils.isEmpty(option.getExcludeHospitalIds())) {
                    where.andNotIn(TenantUserStructure::getHospitalId, option.getExcludeHospitalIds());
                }
                if (!CollectionUtils.isEmpty(option.getIncludeHospitalAreaIds())) {
                    where.andIn(TenantUserStructure::getHospitalAreaId, option.getIncludeHospitalAreaIds());
                }
                if (!CollectionUtils.isEmpty(option.getExcludeHospitalAreaIds())) {
                    where.andNotIn(TenantUserStructure::getHospitalAreaId, option.getExcludeHospitalAreaIds());
                }
                if (!CollectionUtils.isEmpty(option.getIncludeDepartmentIds())) {
                    where.andIn(TenantUserStructure::getDepartmentId, option.getIncludeDepartmentIds());
                }
                if (!CollectionUtils.isEmpty(option.getExcludeDepartmentIds())) {
                    where.andNotIn(TenantUserStructure::getDepartmentId, option.getExcludeDepartmentIds());
                }
                if (!CollectionUtils.isEmpty(option.getExcludeUserIds())) {
                    where.andNotIn(TenantUserStructure::getUserId, option.getExcludeUserIds());
                }
            });
            sql.builder(builder -> builder.orderByDesc(TenantUserStructure::getId));
        });
    }
}
