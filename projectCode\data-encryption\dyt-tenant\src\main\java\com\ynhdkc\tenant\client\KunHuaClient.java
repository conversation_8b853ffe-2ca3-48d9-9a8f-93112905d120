package com.ynhdkc.tenant.client;

import com.ynhdkc.tenant.client.model.AuthorizeRequest;
import com.ynhdkc.tenant.client.model.KunHuaAuthorizeResponse;
import com.ynhdkc.tenant.client.model.KunHuaSubDeptRequest;
import com.ynhdkc.tenant.client.model.KunHuaSubDeptResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "kun-hua", url = "http://119.62.102.80:8111/api/")
public interface KunHuaClient {

    @PostMapping("authorize")
    ResponseEntity<KunHuaAuthorizeResponse> getAuthorization(@RequestBody AuthorizeRequest request);

    @PostMapping("DataService")
    ResponseEntity<KunHuaSubDeptResponse> querySubDept(@RequestBody KunHuaSubDeptRequest request);

}
