package com.ynhdkc.tenant.service.backend.impl;

import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.dao.mapper.IHospitalDetailPageNavigatorMapper;
import com.ynhdkc.tenant.entity.constant.NavigatorTypeEnum;
import com.ynhdkc.tenant.entity.detailpage.HospitalAreaDetailPageNavigator;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.IHospitalAreaDetailPageNavigatorService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/24 17:58:21
 */
@Service
@RequiredArgsConstructor
public class HospitalAreaDetailPageNavigatorServiceImpl implements IHospitalAreaDetailPageNavigatorService {
    private static final int MAIN_NAVI_MAX_COUNT = 3;

    private final IHospitalDetailPageNavigatorMapper mapper;

    @Override
    public HospitalAreaDetailPageNavigatorVo createHospitalAreaDetailPageNavigator(CreateHospitalAreaDetailPageNavigatorReqDto dto) {
        if (Objects.equals(dto.getType(), NavigatorTypeEnum.MAIN_NAVIGATOR.getCode())) {
            List<HospitalAreaDetailPageNavigator> mainNaviList = mapper.selectByExample2(HospitalAreaDetailPageNavigator.class, sql -> {
                sql.andEqualTo(HospitalAreaDetailPageNavigator::getTenantId, dto.getTenantId());
                sql.andEqualTo(HospitalAreaDetailPageNavigator::getHospitalId, dto.getHospitalId());
                sql.andEqualTo(HospitalAreaDetailPageNavigator::getHospitalAreaId, dto.getHospitalAreaId());
                sql.andEqualTo(HospitalAreaDetailPageNavigator::getType, NavigatorTypeEnum.MAIN_NAVIGATOR.getCode());
            });
            if (!CollectionUtils.isEmpty(mainNaviList) && mainNaviList.size() >= MAIN_NAVI_MAX_COUNT) {
                throw new BizException(HttpStatus.BAD_REQUEST, "主导航最多只能添加3个");
            }
        }
        List<HospitalAreaDetailPageNavigator> entities = toEntity(dto);
        int effectiveRow = mapper.insertList(entities);
        if (effectiveRow != entities.size()) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "创建失败");
        }

        List<HospitalAreaDetailPageNavigator> hospitalAreaDetailPageNavigators = mapper.selectByExample2(HospitalAreaDetailPageNavigator.class, sql -> {
            sql.andEqualTo(HospitalAreaDetailPageNavigator::getTenantId, dto.getTenantId());
            sql.andEqualTo(HospitalAreaDetailPageNavigator::getHospitalId, dto.getHospitalId());
            sql.andEqualTo(HospitalAreaDetailPageNavigator::getHospitalAreaId, dto.getHospitalAreaId());
            sql.andEqualTo(HospitalAreaDetailPageNavigator::getType, dto.getType());
        });

        return toVo(hospitalAreaDetailPageNavigators);
    }

    @Override
    public BaseOperationResponse deleteHospitalAreaDetailPageNavigator(Long id) {
        int effectiveRow = mapper.deleteByPrimaryKey(id);
        if (effectiveRow != 1) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "删除失败");
        }
        return BaseOperationResponse.builder().effectiveCount(effectiveRow).build();
    }

    @Override
    public HospitalAreaDetailPageNavigatorVo getHospitalAreaDetailPageNavigator(Long hospitalAreaId) {
        List<HospitalAreaDetailPageNavigator> hospitalAreaDetailPageNavigators = mapper.selectByExample2(HospitalAreaDetailPageNavigator.class, sql -> {
            sql.andEqualTo(HospitalAreaDetailPageNavigator::getHospitalAreaId, hospitalAreaId);
            sql.andEqualTo(HospitalAreaDetailPageNavigator::getType, NavigatorTypeEnum.MAIN_NAVIGATOR.getCode());
        });
        return toVo(hospitalAreaDetailPageNavigators);
    }

    @Override
    public HospitalAreaDetailPageNavigatorVo updateHospitalAreaDetailPageNavigator(Long hospitalAreaId, UpdateHospitalAreaDetailPageNavigatorReqDto dto) {
        List<Long> naviIds = dto.getNaviInfo()
                .stream()
                .map(NaviVo::getId)
                .collect(Collectors.toList());
        List<HospitalAreaDetailPageNavigator> navigators = mapper.selectByExample2(HospitalAreaDetailPageNavigator.class, sql ->
                sql.andIn(HospitalAreaDetailPageNavigator::getId, naviIds)
        );
        if (dto.getNaviInfo().size() != navigators.size()) {
            throw new BizException(HttpStatus.BAD_REQUEST, "导航ID不合法");
        }
        navigators.forEach(navigator -> {
            if (!navigator.getHospitalAreaId().equals(hospitalAreaId)) {
                throw new BizException(HttpStatus.BAD_REQUEST, "导航ID不合法");
            }
        });
        List<HospitalAreaDetailPageNavigator> entities = updateDto2Entities(dto);
        entities.forEach(mapper::updateByPrimaryKeySelective);
        return toVo(entities);
    }

    @Override
    public void setPageNavigator(HospitalAreaLayoutVo vo, Long hospitalAreaId) {
        List<HospitalAreaDetailPageNavigator> mainNavigators = mapper.selectByExample2(HospitalAreaDetailPageNavigator.class, sql ->
                sql.andEqualTo(HospitalAreaDetailPageNavigator::getHospitalAreaId, hospitalAreaId)
                        .andEqualTo(HospitalAreaDetailPageNavigator::getType, NavigatorTypeEnum.MAIN_NAVIGATOR.getCode()));

        if (CollectionUtils.isEmpty(mainNavigators)) {
            return;
        }

        HospitalAreaDetailPageNavigatorVo mainNavigatorVo = toVo(mainNavigators);
        vo.setNavigationVo(mainNavigatorVo);
    }

    private List<HospitalAreaDetailPageNavigator> updateDto2Entities(UpdateHospitalAreaDetailPageNavigatorReqDto dto) {
        return dto.getNaviInfo()
                .stream()
                .map(naviInfo -> {
                    HospitalAreaDetailPageNavigator entity = new HospitalAreaDetailPageNavigator();
                    entity.setId(naviInfo.getId());
                    entity.setTenantId(dto.getTenantId());
                    entity.setHospitalId(dto.getHospitalId());
                    entity.setHospitalAreaId(dto.getHospitalAreaId());
                    entity.setType(dto.getType());
                    if (null == dto.getId()) {
                        entity.setNaviModuleId(0L);
                    } else {
                        entity.setNaviModuleId(dto.getId());
                    }
                    entity.setTitle(naviInfo.getTitle());
                    entity.setSubTitle(naviInfo.getSubTitle());
                    entity.setPicture(naviInfo.getPicture());
                    entity.setUrl(naviInfo.getUrl());
                    entity.setSort(naviInfo.getSort());
                    if (!CollectionUtils.isEmpty(naviInfo.getChannels())) {
                        entity.setChannels(naviInfo.getChannels().stream().map(String::valueOf).collect(Collectors.joining(",")));
                    }
                    return entity;
                })
                .collect(Collectors.toList());
    }

    private List<HospitalAreaDetailPageNavigator> toEntity(CreateHospitalAreaDetailPageNavigatorReqDto dto) {
        List<HospitalAreaDetailPageNavigator> entities = new ArrayList<>();
        dto.getNaviInfo().forEach(naviInfo -> {
            HospitalAreaDetailPageNavigator entity = new HospitalAreaDetailPageNavigator();
            entity.setTenantId(dto.getTenantId());
            entity.setHospitalId(dto.getHospitalId());
            entity.setHospitalAreaId(dto.getHospitalAreaId());
            entity.setType(dto.getType());
            if (null == dto.getId()) {
                entity.setNaviModuleId(0L);
            } else {
                entity.setNaviModuleId(dto.getId());
            }
            entity.setTitle(naviInfo.getTitle());
            entity.setSubTitle(naviInfo.getSubTitle());
            entity.setPicture(naviInfo.getPicture());
            entity.setUrl(naviInfo.getUrl());
            entity.setSort(naviInfo.getSort());
            entity.setChannels(naviInfo.getChannels().stream().map(String::valueOf).collect(java.util.stream.Collectors.joining(",")));
            entities.add(entity);
        });
        return entities;
    }

    private HospitalAreaDetailPageNavigatorVo toVo(List<HospitalAreaDetailPageNavigator> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return null;
        }

        HospitalAreaDetailPageNavigatorVo vo = new HospitalAreaDetailPageNavigatorVo();
        vo.setTenantId(entities.get(0).getTenantId());
        vo.setHospitalId(entities.get(0).getHospitalId());
        vo.setHospitalAreaId(entities.get(0).getHospitalAreaId());
        vo.setType(entities.get(0).getType());
        vo.setNaviInfo(
                entities.stream()
                        .map(entity -> {
                            HospitalAreaDetailPageNavigatorVoNaviInfo naviInfo = new HospitalAreaDetailPageNavigatorVoNaviInfo();
                            naviInfo.setId(entity.getId());
                            naviInfo.setTitle(entity.getTitle());
                            naviInfo.setSubTitle(entity.getSubTitle());
                            naviInfo.setPicture(entity.getPicture());
                            naviInfo.setUrl(entity.getUrl());
                            naviInfo.setSort(entity.getSort());
                            if (StringUtils.hasText(entity.getChannels())) {
                                naviInfo.setChannels(Arrays.stream(entity.getChannels().split(",")).map(Integer::valueOf).collect(Collectors.toList()));
                            }
                            naviInfo.setCreateTime(entity.getCreateTime());
                            naviInfo.setUpdateTime(entity.getUpdateTime());
                            return naviInfo;
                        })
                        .sorted(Comparator.comparing(HospitalAreaDetailPageNavigatorVoNaviInfo::getSort))
                        .collect(Collectors.toList())
        );
        return vo;
    }
}
