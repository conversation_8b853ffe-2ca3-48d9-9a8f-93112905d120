create table t_dict_label
(
    id          bigint auto_increment comment '全局唯一标识'
        primary key,
    dict_type   varchar(40)                              not null comment '数据字典类型',
    dict_label  varchar(100)                             not null comment '数据字典标签',
    dict_value  varchar(50)                              not null comment '数据字典标签对应的值',
    description varchar(100)                             null comment '描述',
    sort        int         default 0                    null comment '排序',
    create_time datetime(3) default CURRENT_TIMESTAMP(3) not null,
    update_time datetime(3)                              null on update CURRENT_TIMESTAMP(3),
    constraint unique_key_dict_type_and_label
        unique (dict_type, dict_label)
)
    comment 'DictLabel';
