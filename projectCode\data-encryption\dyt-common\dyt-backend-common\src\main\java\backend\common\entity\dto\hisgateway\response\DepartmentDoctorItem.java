package backend.common.entity.dto.hisgateway.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class DepartmentDoctorItem {

    @JsonProperty("hospital_code")
    private String hospitalCode;

    @JsonProperty("department_name")
    private String departmentName;

    @JsonProperty("department_code")
    private String departmentCode;

    @JsonProperty("exec_department_code")
    private String execDepartmentCode;

    @JsonProperty("doctor_name")
    private String doctorName;

    @JsonProperty("doctor_code")
    private String doctorCode;

    @JsonProperty("doctor_type")
    private String doctorType;

    @JsonProperty("doctor_type_desc")
    private String doctorTypeDesc;

    @JsonProperty("doctor_rank_type")
    private String doctorRankType;

    @JsonProperty("doctor_rank_value")
    private String doctorRankValue;

    @JsonProperty("doctor_rank_label")
    private String doctorRankLabel;

    @JsonProperty("registration_level")
    private String registrationLevel;

    @JsonProperty("registration_level_desc")
    private String registrationLevelDesc;

    @JsonProperty("doctor_profile_photo")
    private String doctorProfilePhoto;

    @JsonProperty("doctor_description")
    private String doctorDescription;

    @JsonProperty("doctor_advantage")
    private String doctorAdvantage;

    @JsonProperty("src_sum")
    private Integer srcSum;

    @JsonProperty("src_number")
    private Integer srcNumber;

    @JsonProperty("is_doctor")
    private Integer isDoctor;

    @JsonProperty("registration_date")
    private Long registrationDate;

    @JsonProperty("show_schedule_date")
    private String showScheduleDate;

    @JsonProperty("src_date_map")
    private Map<String, Integer> srcDateMap;

    @JsonProperty("schedule_list")
    private List<DoctorScheduleListItem> scheduleList;

    private BigDecimal amt;

    @JsonProperty("registration_amt")
    private Double registrationAmt;

    @JsonProperty("treat_amt")
    private Double treatAmt;

    @JsonProperty("expert_amt")
    private Double expertAmt;

    @JsonProperty("inspection_fees")
    private Double inspectionFees;

    @JsonProperty("time_type")
    private Integer timeType;

    @JsonProperty("time_type_desc")
    private String timeTypeDesc;

    @JsonProperty("schedule_id")
    private String scheduleId;

    @JsonProperty("begin_time")
    private Long beginTime;

    @JsonProperty("end_time")
    private Long endTime;

    private String honor;

    private Integer status;

    private int sort;

    private Map<String, Object> rawParameters = new HashMap<>();
}
