-- =====================================================
-- 测试环境数据库初始化脚本
-- 包含影子字段的表结构定义
-- =====================================================

-- 创建租户用户表（包含影子字段）
CREATE TABLE IF NOT EXISTS t_tenant_user (
    id                          BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '全局唯一标识',
    name                        VARCHAR(30) NOT NULL COMMENT '租户帐号名',
    name_encrypted              VARCHAR(1000) NULL COMMENT '用户名加密字段（影子字段）',
    nickname                    VARCHAR(20) NULL COMMENT '用户名',
    phone_number                VARCHAR(13) NULL COMMENT '用户手机号码',
    phone_number_encrypted      VARCHAR(1000) NULL COMMENT '手机号码加密字段（影子字段）',
    email                       VARCHAR(75) NULL COMMENT '邮箱',
    gender                      INT DEFAULT 0 NOT NULL COMMENT '0:未知；1:男；2:女；',
    id_card_no                  VARCHAR(20) NULL COMMENT '用户身份证号',
    password                    VARCHAR(60) NOT NULL COMMENT '登录密码',
    register_platform           INT DEFAULT 0 NOT NULL COMMENT '注册平台 0:未知 1:PC',
    login_ip                    INT DEFAULT 0 NULL COMMENT '登录 IP',
    daily_login_retries         BIGINT DEFAULT 0 NULL COMMENT '当天登录重试次数',
    status                      INT DEFAULT 0 NOT NULL COMMENT '状态：0:正常，1:冻结',
    admin                       TINYINT DEFAULT 0 NULL COMMENT '是否是超级管理员，0:不是，1：是',
    head_image_url              VARCHAR(300) NULL COMMENT '头像地址',
    create_time                 TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_time                 TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP
);

-- 创建租户表（包含影子字段）
CREATE TABLE IF NOT EXISTS t_tenant (
    id                              BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '全局唯一标识',
    name                            VARCHAR(50) NOT NULL COMMENT '租户名称',
    description                     VARCHAR(200) NULL COMMENT '租户描述',
    address                         VARCHAR(200) NULL COMMENT '租户地址',
    logo_url                        VARCHAR(300) NULL COMMENT 'Logo URL',
    contact                         VARCHAR(30) NULL COMMENT '联系人',
    contact_phone_number            VARCHAR(13) NULL COMMENT '联系人手机号',
    contact_phone_number_encrypted  VARCHAR(1000) NULL COMMENT '联系人手机号加密字段（影子字段）',
    contact_email                   VARCHAR(75) NULL COMMENT '联系人邮箱',
    contact_email_encrypted         VARCHAR(1000) NULL COMMENT '联系人邮箱加密字段（影子字段）',
    status                          INT DEFAULT 0 NOT NULL COMMENT '状态：0:正常，1:禁用',
    create_time                     TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_time                     TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP
);

-- 创建租户用户结构表
CREATE TABLE IF NOT EXISTS t_tenant_user_structure (
    id                  BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '全局唯一标识',
    user_id             BIGINT NOT NULL COMMENT '用户ID',
    tenant_id           BIGINT NOT NULL COMMENT '租户ID',
    hospital_id         BIGINT NULL COMMENT '医院ID',
    hospital_area_id    BIGINT NULL COMMENT '医院区域ID',
    hospital_code       VARCHAR(50) NULL COMMENT '医院编码',
    department_id       BIGINT NULL COMMENT '科室ID',
    department_admin    TINYINT DEFAULT 0 NULL COMMENT '是否为科室管理员',
    create_time         TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_time         TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP
);

-- 创建索引
CREATE UNIQUE INDEX IF NOT EXISTS idx_tenant_user_name ON t_tenant_user (name);
CREATE INDEX IF NOT EXISTS idx_tenant_user_phone ON t_tenant_user (phone_number);
CREATE INDEX IF NOT EXISTS idx_tenant_user_email ON t_tenant_user (email);

CREATE UNIQUE INDEX IF NOT EXISTS idx_tenant_name ON t_tenant (name);
CREATE INDEX IF NOT EXISTS idx_tenant_contact_phone ON t_tenant (contact_phone_number);
CREATE INDEX IF NOT EXISTS idx_tenant_contact_email ON t_tenant (contact_email);

CREATE INDEX IF NOT EXISTS idx_tenant_user_structure_user ON t_tenant_user_structure (user_id);
CREATE INDEX IF NOT EXISTS idx_tenant_user_structure_tenant ON t_tenant_user_structure (tenant_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_tenant_user_structure_unique ON t_tenant_user_structure (user_id, tenant_id);

-- 插入测试数据
INSERT INTO t_tenant_user (id, name, phone_number, email, password, admin) VALUES 
(1, 'test_admin', '13333333333', '<EMAIL>', '$2a$10$1BPqCovBDFFcLUkii1ElrOn1.Fmb2n/0Js27Vi25M3RyPpA7FbW3W', 1),
(2, 'test_user1', '13333333332', '<EMAIL>', '$2a$10$1BPqCovBDFFcLUkii1ElrOn1.Fmb2n/0Js27Vi25M3RyPpA7FbW3W', 0),
(3, 'test_user2', '13333333331', '<EMAIL>', '$2a$10$1BPqCovBDFFcLUkii1ElrOn1.Fmb2n/0Js27Vi25M3RyPpA7FbW3W', 0);

INSERT INTO t_tenant (id, name, description, contact, contact_phone_number, contact_email) VALUES
(1, '测试租户1', '用于测试的租户1', '张三', '13800138001', '<EMAIL>'),
(2, '测试租户2', '用于测试的租户2', '李四', '13800138002', '<EMAIL>');

INSERT INTO t_tenant_user_structure (user_id, tenant_id, hospital_id) VALUES
(1, 1, 1001),
(2, 1, 1001),
(3, 2, 1002);
