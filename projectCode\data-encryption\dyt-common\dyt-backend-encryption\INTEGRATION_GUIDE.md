# DYT Backend Encryption 集成指南

## 🎯 集成完成情况

✅ **已完成的工作**：

1. **模块创建**：成功创建了`dyt-backend-encryption`模块
2. **依赖管理**：已集成到dyt-common的依赖管理体系中
3. **自动装配**：实现了Spring Boot自动配置
4. **核心功能**：
   - AES-GCM加密算法（生产就绪）
   - SM2国密算法（演示实现）
   - JPA AttributeConverter透明加密
   - 注解驱动的字段加密
   - 渐进式迁移策略支持
5. **测试验证**：所有单元测试通过
6. **文档完善**：提供了完整的使用文档和示例

## 🚀 如何在其他项目中使用

### 1. 添加依赖

在你的项目`pom.xml`中添加：

```xml
<dependency>
    <groupId>com.ynhdkc</groupId>
    <artifactId>dyt-backend-encryption</artifactId>
    <version>1.1-SNAPSHOT</version>
</dependency>
```

### 2. 配置文件

在`application.yml`中添加配置：

```yaml
backend:
  encryption:
    enabled: true
    secret-key: "your-production-secret-key-32-bytes"
    algorithm: AES_GCM
    debug-enabled: false
```

### 3. 实体类使用

```java
@Entity
public class User {
    @Id
    private Long id;
    
    // 加密手机号
    @EncryptField(description = "用户手机号")
    @Convert(converter = EncryptConverter.class)
    @Column(name = "phone", length = 500)
    private String phone;
    
    // 使用国密算法加密身份证
    @EncryptField(
        description = "身份证号",
        algorithm = EncryptField.AlgorithmType.SM2
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "id_card", length = 1000)
    private String idCard;
}
```

## 🔧 高级配置

### 渐进式迁移配置

```yaml
backend:
  encryption:
    strategies:
      user-email:
        strategy: SHADOW_PRIORITY
        shadow-field: "email_encrypted"
        algorithm: AES_GCM
        enabled: true
```

### 国密算法配置

```yaml
backend:
  encryption:
    sm2:
      enabled: true
      public-key: "your-sm2-public-key"
      private-key: "your-sm2-private-key"
```

## 📊 性能特点

- **AES-GCM加密**：高性能，适合大量数据
- **透明加解密**：对业务代码无侵入
- **批量操作优化**：支持JPA批量操作
- **缓存友好**：加密后的数据可以正常缓存

## 🔒 安全建议

1. **生产环境密钥管理**：
   ```yaml
   backend:
     encryption:
       secret-key: ${ENCRYPTION_SECRET_KEY:default-key}
   ```

2. **密钥轮换策略**：定期更换加密密钥
3. **权限控制**：限制对加密配置的访问
4. **审计日志**：记录加密操作日志

## 🧪 测试验证

运行加密功能测试：

```bash
# 在dyt-common目录下
mvn test -f dyt-backend-encryption/pom.xml

# 或者测试整个项目
mvn test
```

## 📈 迁移策略

### 新项目
直接使用`DIRECT_ENCRYPT`策略：

```java
@EncryptField
@Convert(converter = EncryptConverter.class)
private String sensitiveData;
```

### 现有项目迁移

1. **第一阶段**：添加影子字段
```sql
ALTER TABLE user ADD COLUMN phone_encrypted VARCHAR(1000);
```

2. **第二阶段**：配置PLAINTEXT_PRIORITY
```yaml
backend:
  encryption:
    strategies:
      user-phone:
        strategy: PLAINTEXT_PRIORITY
        shadow-field: "phone_encrypted"
```

3. **第三阶段**：切换到SHADOW_PRIORITY
4. **第四阶段**：最终切换到SHADOW_ONLY

## 🔍 故障排查

### 常见问题

1. **密钥长度错误**
   - 确保AES密钥为32字节
   - 检查配置文件中的密钥设置

2. **加密失败**
   - 检查日志中的错误信息
   - 验证数据库字段长度是否足够

3. **性能问题**
   - 启用性能监控：`performance-monitor-enabled: true`
   - 检查批量操作配置

### 调试模式

```yaml
backend:
  encryption:
    debug-enabled: true
    performance-monitor-enabled: true

logging:
  level:
    backend.encryption: DEBUG
```

## 📞 技术支持

如有问题，请联系DYT后端团队或查看：
- 项目README.md
- 单元测试示例
- 配置示例文件

## 🎉 集成成功！

恭喜！您已成功将加解密功能集成到dyt-common中。现在可以在任何依赖dyt-common的项目中使用这些加密功能了。
