package backend.common.entity.dto.hisgateway.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class JustRefundResponse {

    @JsonProperty("visiting_serial_number")
    private String visitingSerialNumber;

    @JsonProperty("medical_record_number")
    private String medicalRecordNumber;

    @JsonProperty("schedule_id")
    private String scheduleId;

    @JsonProperty("appointment_serial_number")
    private String appointmentSerialNumber;

    @JsonProperty("lock_registration_number")
    private String lockRegistrationNumber;

    @JsonProperty("order_number")
    private String orderNumber;

    @JsonProperty("trade_serial_number")
    private String tradeSerialNumber;

    private String amt;

    @JsonProperty("visiting_date")
    private Long visitingDate;

    @JsonProperty("start_time")
    private Long startTime;

    @JsonProperty("end_time")
    private Long endTime;

    @JsonProperty("start_time_text")
    private String startTimeText;

    @JsonProperty("end_time_text")
    private String endTimeText;

}
