package backend.common.entity.dto.notification.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum MessageTypeTagEnum {
    SEND_VERIFICATION_CODE("SEND_VERIFICATION_CODE", "发送验证码"),
    DATA_UPLOAD_REMIND("DATA_UPLOAD_REMIND", "资料上传结果提醒"),
    APPOINT_SUCCESS("APPOINT_SUCCESS", "预约成功通知"),
    CANCEL_APPOINTMENT("CANCEL_APPOINTMENT", "取消通知"),
    APPOINT_FAILED_HIS("APPOINT_FAILED_HIS", "预约失败-his未成功"),
    DOCTOR_CANCELLATION("DOCTOR_CANCELLATION", "医生停诊通知"),
    TIMEOUT_CANCELLATION("TIMEOUT_CANCELLATION", "系统自动撤销（超时未支付通知）"),
    APPOINTMENT_REMINDER("APPOINTMENT_REMINDER", "就诊提醒通知"),
    OUTPATIENT_PAYMENT_SUCCESSFUL("OUTPATIENT_PAYMENT_SUCCESSFUL", "门诊缴费成功"),
    OUTPATIENT_PAYMENT_FAILED_HIS("OUTPATIENT_PAYMENT_FAILED_HIS", "门诊缴费失败-his未成功"),
    MEDICAL_CARD_RECHARGE_SUCCESSFUL("MEDICAL_CARD_RECHARGE_SUCCESSFUL", "就诊卡充值成功"),
    NOTIFY_HOSPITAL_ADMIN("NOTIFY_HOSPITAL_ADMIN", "通知医院负责人（配置了医院负责人才发送）"),
    NOTIFY_MEDICAL_TECHMOLOGY_APPOINT("NOTIFY_MEDICAL_TECHMOLOGY_APPOINT", "医技预约成功通知"),
    NOTIFY_VISIT_NAVIGATION("NOTIFY_VISIT_NAVIGATION", "就诊导航"),
    SYSTEM_ERROR_NOTIFY("SYSTEM_ERROR_NOTIFY", "系统异常通知"),
    NEW_REGISTRATION_RESOURCE_NOTIFICATION("NEW_REGISTRATION_RESOURCE_NOTIFICATION", "新挂号资源公众号通知"),
    NEW_REGISTRATION_RESOURCE_MIN_NOTIFICATION("NEW_REGISTRATION_RESOURCE_MIN_NOTIFICATION", "新挂号资源小程序通知"),
    ONLINE_ASK_NOTIFY("ONLINE_ASK_NOTIFY", "在线问诊工单通知"),
    INSPECTION_APPOINT_SUCCESS("INSPECTION_APPOINT_SUCCESS", "检查项目预约成功通知"),

    ;
    final String tag;
    final String description;
}
