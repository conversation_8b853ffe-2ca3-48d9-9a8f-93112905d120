package com.ynhdkc.tenant.entity.detailpage;

import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @since 2023/5/24 18:53:12
 */
@Data
@Table(name = "t_hospital_detail_page_cube_module")
@EqualsAndHashCode(callSuper = true)
public class HospitalAreaDetailPageCubeModule extends BaseEntity {

    private Long tenantId;

    private Long hospitalId;

    private Long hospitalAreaId;

    private String hospitalCode;

    private String title;

    private Integer titleStatus;

    private Integer cubeDisplayType;

    private Integer sort;

    private String channels;
}
