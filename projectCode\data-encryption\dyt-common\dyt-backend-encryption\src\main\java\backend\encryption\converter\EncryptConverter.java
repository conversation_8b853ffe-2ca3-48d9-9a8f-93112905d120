package backend.encryption.converter;

import backend.encryption.util.AESGCMUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * JPA字段加密转换器
 * 实现数据库字段的透明加密存储
 * 
 * 使用方式：
 * <pre>
 * {@code
 * @Entity
 * public class User {
 *     @Convert(converter = EncryptConverter.class)
 *     @Column(name = "phone")
 *     private String phone;
 * }
 * }
 * </pre>
 * 
 * 或者配合@EncryptField注解使用：
 * <pre>
 * {@code
 * @Entity
 * public class User {
 *     @EncryptField
 *     @Column(name = "phone")
 *     private String phone;
 * }
 * }
 * </pre>
 * 
 * <AUTHOR> Backend Team
 * @version 1.1-SNAPSHOT
 * @since 2024-06-24
 */
@Slf4j
@Converter
public class EncryptConverter implements AttributeConverter<String, String> {
    
    /**
     * 将实体属性转换为数据库列值（加密）
     * 在保存到数据库之前调用
     * 
     * @param attribute 实体中的明文属性值
     * @return 加密后的数据库列值
     */
    @Override
    public String convertToDatabaseColumn(String attribute) {
        if (!StringUtils.hasText(attribute)) {
            log.debug("Attribute is null or empty, skipping encryption");
            return attribute;
        }
        
        try {
            String encrypted = AESGCMUtil.encrypt(attribute);
            log.debug("Successfully converted attribute to database column (encrypted)");
            return encrypted;
        } catch (Exception e) {
            log.error("Failed to encrypt attribute for database storage", e);
            // 在生产环境中，可能需要抛出异常或者有其他处理策略
            throw new RuntimeException("Failed to encrypt sensitive data", e);
        }
    }
    
    /**
     * 将数据库列值转换为实体属性（解密）
     * 在从数据库查询后调用
     * 
     * @param dbData 数据库中的加密列值
     * @return 解密后的实体属性值
     */
    @Override
    public String convertToEntityAttribute(String dbData) {
        if (!StringUtils.hasText(dbData)) {
            log.debug("Database data is null or empty, skipping decryption");
            return dbData;
        }
        
        try {
            String decrypted = AESGCMUtil.decrypt(dbData);
            log.debug("Successfully converted database column to entity attribute (decrypted)");
            return decrypted;
        } catch (Exception e) {
            log.error("Failed to decrypt database data to entity attribute", e);
            // 在生产环境中，可能需要抛出异常或者有其他处理策略
            throw new RuntimeException("Failed to decrypt sensitive data", e);
        }
    }
}
