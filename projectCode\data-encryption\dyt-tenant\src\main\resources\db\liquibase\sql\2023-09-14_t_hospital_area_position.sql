create table t_hospital_area_position
(
    id               bigint auto_increment primary key,
    tenant_id        bigint                                   not null comment '租户id',
    hospital_id      bigint                                   not null comment '医院id',
    hospital_area_id bigint                                   not null comment '院区id',
    type             int         default 0                    not null comment '类型，0：科室，9：自定义',
    name             varchar(50)                              not null comment '名称',
    department_id    bigint                                   null comment '科室id',
    building_id      bigint                                   null comment '大楼id',
    building_name    varchar(50)                              null comment '大楼名称',
    floor_id         bigint                                   null comment '楼层id',
    floor_name       varchar(50)                              null comment '楼层名称',
    area_id          bigint                                   null comment '区域id',
    area_name        varchar(50)                              null comment '区域名称',
    sort int default 0 null comment '排序',
    status           int         default 0                    not null comment '状态，0：正常，1：禁用',
    create_time      datetime(3) default CURRENT_TIMESTAMP(3) not null,
    update_time      datetime(3)                              null on update CURRENT_TIMESTAMP(3),
    unique (department_id),
    unique (building_id, floor_id, area_id, type, name)
) comment '位置关系表';