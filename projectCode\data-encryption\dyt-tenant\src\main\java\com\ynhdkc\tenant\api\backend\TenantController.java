package com.ynhdkc.tenant.api.backend;

import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import backend.security.method.BackendSecurityRequired;
import backend.security.oplog.DytSecurityRequired;
import backend.security.service.BackendTenantUserService;
import com.ynhdkc.tenant.handler.TenantApi;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.OrganizationStructureService;
import com.ynhdkc.tenant.service.backend.TenantService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2023/2/14 15:51:28
 */
@Api(tags = "Tenant")
@RestController
@RequiredArgsConstructor
public class TenantController implements TenantApi {
    private final TenantService tenantService;
    private final OrganizationStructureService organizationStructureService;

    private final BackendTenantUserService backendTenantUserService;

    @BackendSecurityRequired(tenantManager = true)
    @DytSecurityRequired(needOpLog = true, value = "tenant:create")
    @Override
    public ResponseEntity<TenantDetailVo> create(TenantCreateReqDto request) {
        TenantDetailVo vo = tenantService.create(request);

        organizationStructureService.asyncUserOrganizationStructureAndUser();

        return ResponseEntity.ok(vo);
    }

    @BackendSecurityRequired(tenantManager = true)
    @DytSecurityRequired(needOpLog = true, value = "tenant:delete")
    @Override
    public ResponseEntity<BaseOperationResponse> delete(Long tenantId) {
        BaseOperationResponse operationResponse = tenantService.delete(tenantId);

        organizationStructureService.asyncUserOrganizationStructureAndUser();

        return ResponseEntity.ok(operationResponse);
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "tenant:get:detail")
    public ResponseEntity<TenantDetailVo> getDetail(Long tenantId) {
        if (!backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.TenantId(tenantId))) {
            throw new BizException(HttpStatus.FORBIDDEN, "用户没有该租户权限");
        }
        return ResponseEntity.ok(tenantService.getDetail(tenantId));
    }

    @BackendSecurityRequired(tenantManager = true)
    @Override
    @DytSecurityRequired(needOpLog = true, value = "tenant:get:page")
    public ResponseEntity<TenantPageVo> query(TenantQueryReqDto request) {
        return ResponseEntity.ok(tenantService.query(request));
    }

    @BackendSecurityRequired(tenantManager = true)
    @DytSecurityRequired(needOpLog = true, value = "tenant:update")
    @Override
    public ResponseEntity<TenantDetailVo> update(Long tenantId, TenantUpdateReqDto request) {
        TenantDetailVo vo = tenantService.update(tenantId, request);

        organizationStructureService.asyncUserOrganizationStructureAndUser();

        return ResponseEntity.ok(vo);
    }
}
