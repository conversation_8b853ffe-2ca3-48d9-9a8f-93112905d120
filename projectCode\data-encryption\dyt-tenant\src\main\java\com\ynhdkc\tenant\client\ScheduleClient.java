package com.ynhdkc.tenant.client;

import com.ynhdkc.tenant.client.model.ApiSchedulePageResponse;
import com.ynhdkc.tenant.dto.ScheduleResponseDto;
import com.ynhdkc.tenant.dto.TimeSpanDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/7/25 11:43:44
 */
@FeignClient(name = "${feign.name.scheduler}", path = "${feign.path.scheduler}")
//@FeignClient(value = "dyt-schedule", url = "https://megacloud.ynhdkc.com",path = "/rpc/v1/schedule", decode404 = true)
public interface ScheduleClient {

    @GetMapping(value = "/schedule-time-span/get-time-span")
    TimeSpanDto getTimeSpan(@RequestParam("hospital_id") Long hospitalId, @RequestParam("hospital_area_id") Long hospitalAreaId, @RequestParam("department_id") Long departmentId);

    @GetMapping(value = "/schedules/for-inner-his")
    ApiSchedulePageResponse getSchedulesBatch(@RequestParam("start_sch_date") String startSchDate,
                                              @RequestParam("end_sch_date") String endSchDate,
                                              @RequestParam("doctor_ids") List<Long> doctorIds,
                                              @RequestParam("current_page") Integer currentPage,
                                              @RequestParam("page_size") Integer pageSize);

    @GetMapping(value = "/schedules/get-inner-schedule")
    ScheduleResponseDto getSchedules(@RequestParam("hospital_id") Long hospitalId,
                                     @RequestParam("hospital_area_id") Long hospitalAreaId,
                                     @RequestParam("department_id") Long departmentId,
                                     @RequestParam("doctor_ids") List<Long> doctorIds,
                                     @RequestParam("page_size") Integer pageSize,
                                     @RequestParam("current_page") Integer currentPage);

    @GetMapping(value = "/schedules/get-inner-schedule-by-doctor")
    ResponseEntity<ScheduleResponseDto> getInnerScheduleByDoctor(@RequestParam(value = "hospital_id", required = false) Long hospitalId,
                                                                 @RequestParam(value = "hospital_area_id", required = false) Long hospitalAreaId,
                                                                 @RequestParam(value = "department_id", required = false) Long departmentId,
                                                                 @RequestParam(value = "doctor_id", required = false) Long doctorId,
                                                                 @RequestParam(value = "hospital_code", required = false) String hospitalCode,
                                                                 @RequestParam(value = "department_code", required = false) String departmentCode,
                                                                 @RequestParam(value = "doctor_code", required = false) String doctorCode);


}
