package com.ynhdkc.tenant.config;

import backend.common.densensitized.service.AbstractDesensitizedPermissionService;
import backend.common.densensitized.service.DefaultDesensitizedPermissionService;
import backend.common.densensitized.service.DelegatingDesensitizedPermissionService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class DesensitizedPermissionConfig {

    @Bean
    @ConditionalOnMissingBean(AbstractDesensitizedPermissionService.class)
    public AbstractDesensitizedPermissionService defaultDesensitizedPermissionService() {
        return new DefaultDesensitizedPermissionService();
    }

    @Bean
    public DelegatingDesensitizedPermissionService delegatingDesensitizedPermissionService(
            AbstractDesensitizedPermissionService desensitizedPermissionService) {
        return new DelegatingDesensitizedPermissionService(desensitizedPermissionService);
    }

}