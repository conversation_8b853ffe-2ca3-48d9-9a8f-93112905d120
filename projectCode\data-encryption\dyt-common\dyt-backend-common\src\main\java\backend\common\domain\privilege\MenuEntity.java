package backend.common.domain.privilege;


import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MenuEntity extends BaseEntity {
    private Long parentId;
    private String name;
    private Integer type;
    private String path;
    private String icon;
    private Integer sort;
    /**
     * 嵌套层级
     */
    private Integer layer;
    private String metaTitle;
    private Boolean metaNoCache;
    private String componentPath;
    private String redirect;
    private Boolean hidden;
    private Integer status;
}
