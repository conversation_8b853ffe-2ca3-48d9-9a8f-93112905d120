package backend.common.util;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-21 14:40
 */
public abstract class BaseQueryOption {
    private static final Integer DEFAULT_CURRENT_PAGE = 1;
    private static final Integer DEFAULT_PAGE_SIZE = 20;

    protected BaseQueryOption() {
        this.currentPage = DEFAULT_CURRENT_PAGE;
        this.pageSize = DEFAULT_PAGE_SIZE;
    }

    protected BaseQueryOption(Integer currentPage, Integer pageSize) {
        this.currentPage = currentPage;
        this.pageSize = pageSize;
    }

    /**
     * 当前页
     */
    @Getter
    @Setter
    private Integer currentPage;
    /**
     * 页大小
     */
    @Getter
    @Setter
    private Integer pageSize;

    public Integer nextPage() {
        this.currentPage++;
        return this.currentPage;
    }
}
