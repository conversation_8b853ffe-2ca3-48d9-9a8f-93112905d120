package com.example.encryption.repository;

import com.example.encryption.annotation.EncryptField;
import com.example.encryption.entity.EncryptionStrategy;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 加密策略配置Repository
 * 提供策略配置的数据访问功能
 */
@Repository
public interface EncryptionStrategyRepository extends JpaRepository<EncryptionStrategy, Long> {

    /**
     * 根据策略键查找策略配置
     * 
     * @param strategyKey 策略键
     * @return 策略配置
     */
    Optional<EncryptionStrategy> findByStrategyKey(String strategyKey);

    /**
     * 根据策略键和启用状态查找策略配置
     * 
     * @param strategyKey 策略键
     * @param enabled 是否启用
     * @return 策略配置
     */
    Optional<EncryptionStrategy> findByStrategyKeyAndEnabled(String strategyKey, Boolean enabled);

    /**
     * 查找所有启用的策略配置
     * 
     * @return 启用的策略配置列表
     */
    List<EncryptionStrategy> findByEnabledTrueOrderByPriorityAsc();

    /**
     * 根据迁移策略查找配置
     * 
     * @param migrationStrategy 迁移策略
     * @return 策略配置列表
     */
    List<EncryptionStrategy> findByMigrationStrategyAndEnabledTrue(EncryptField.MigrationStrategy migrationStrategy);

    /**
     * 根据算法类型查找配置
     * 
     * @param algorithmType 算法类型
     * @return 策略配置列表
     */
    List<EncryptionStrategy> findByAlgorithmTypeAndEnabledTrue(String algorithmType);

    /**
     * 检查策略键是否存在
     * 
     * @param strategyKey 策略键
     * @return 是否存在
     */
    boolean existsByStrategyKey(String strategyKey);

    /**
     * 统计启用的策略数量
     * 
     * @return 启用的策略数量
     */
    @Query("SELECT COUNT(e) FROM EncryptionStrategy e WHERE e.enabled = true")
    long countEnabledStrategies();

    /**
     * 根据优先级范围查找策略
     * 
     * @param minPriority 最小优先级
     * @param maxPriority 最大优先级
     * @return 策略配置列表
     */
    @Query("SELECT e FROM EncryptionStrategy e WHERE e.enabled = true AND e.priority BETWEEN :minPriority AND :maxPriority ORDER BY e.priority ASC")
    List<EncryptionStrategy> findByPriorityRange(@Param("minPriority") Integer minPriority, 
                                                @Param("maxPriority") Integer maxPriority);

    /**
     * 查找需要更新的策略（基于更新时间）
     * 
     * @return 策略配置列表
     */
    @Query("SELECT e FROM EncryptionStrategy e WHERE e.enabled = true ORDER BY e.updatedAt DESC")
    List<EncryptionStrategy> findRecentlyUpdatedStrategies();

    /**
     * 批量更新策略状态
     * 
     * @param strategyKeys 策略键列表
     * @param enabled 启用状态
     * @return 更新的记录数
     */
    @Query("UPDATE EncryptionStrategy e SET e.enabled = :enabled, e.updatedAt = CURRENT_TIMESTAMP WHERE e.strategyKey IN :strategyKeys")
    int updateEnabledStatusByStrategyKeys(@Param("strategyKeys") List<String> strategyKeys, 
                                        @Param("enabled") Boolean enabled);
}
