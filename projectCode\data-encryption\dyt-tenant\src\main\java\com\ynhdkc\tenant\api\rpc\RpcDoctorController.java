package com.ynhdkc.tenant.api.rpc;

import com.ynhdkc.tenant.handler.RpcDoctorApi;
import com.ynhdkc.tenant.model.DoctorDetailVo;
import com.ynhdkc.tenant.model.RpcBatchGetByIdReqDto;
import com.ynhdkc.tenant.service.backend.IDoctorService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-23 10:48
 */
@Api(tags = "RpcDoctor")
@RestController
@RequiredArgsConstructor
public class RpcDoctorController implements RpcDoctorApi {

    private final IDoctorService doctorService;

    @Override
    public ResponseEntity<List<DoctorDetailVo>> batchGetDoctorDetail(RpcBatchGetByIdReqDto request) {
        return ResponseEntity.ok(doctorService.rpcBatchGetDetail(request));
    }

    @Override
    public ResponseEntity<List<DoctorDetailVo>> batchGetDoctorDetailByAreaId(Long areaId, Long departmentId) {
        return ResponseEntity.ok(doctorService.rpcBatchGetDetailByAreaId(areaId, departmentId));
    }

    @Override
    public ResponseEntity<List<Long>> batchGetDoctorIdLikeName(String name) {
        return ResponseEntity.ok(doctorService.rpcBatchGetIdLikeName(name));
    }

    @Override
    public ResponseEntity<DoctorDetailVo> getDoctorDetail(Long id) {
        return ResponseEntity.ok(doctorService.rpcGetDetail(id));
    }
}
