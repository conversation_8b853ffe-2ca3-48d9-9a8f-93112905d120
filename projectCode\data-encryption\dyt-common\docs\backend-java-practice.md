# Backend Platform Java Practice

### 本机Maven设置

设置文件通常为用户目录下`.m2/settings.xml`文件。

在其中加入`nexus`私服的`profile`。

```xml
 <profile>
    <id>my-nexus</id>
    <repositories>
      <repository>
        <id>my-nexus</id>
        <url>http://192.168.24.17:8081/nexus/repository/maven-public/</url>
        <snapshots>
          <updatePolicy>always</updatePolicy>
        </snapshots>
      </repository>
    </repositories>
  </profile>
```

设置该`profile`为默认激活。

```xml
<activeProfiles>
  <activeProfile>my-nexus</activeProfile>
</activeProfiles>
```

### 项目的Maven Pom设置

编辑项目目录下的`pom.xml`文件

设置项目的`parent`

```xml
<parent>
    <groupId>com.megaease.backend</groupId>
    <artifactId>backend-common-parent</artifactId>
    <version>1.0-SNAPSHOT</version>
</parent>
```

引入基础依赖

```xml
<dependencies>
    <dependency>
        <groupId>com.megaease.backend</groupId>
        <artifactId>backend-common</artifactId>
    </dependency>
</dependencies>
```

### 项目包结构

包的前面一部分根据项目自定。
比如Backend平台项目整体包名为：`com.megaease.backend` + 应用名。
比如租户模块，具体为`com.megaease.backend.tenant`。

在上述包之下建议使用如下子包

- `entity` 存放与数据库表对应的实体类的包
- `dao` 存放操作数据库的`mapper`或者`repository`
- `api` 存放`MVC`中的`Controller`，其中的类名一般也以`Controller`结尾
- `schedule` 存放定时任务配置类以及入口类
- `service` 存放服务类，其中的类名一般也以`Service`结尾
- `util` 存放本项目用到的工具类
- `config` 存放`@Configuration`相关类
- `client` 存放`Feign`远程调用相关接口类

### WEB接口开发时的异常处理
  
建议使用`backend-common`模块提供的`BizException`，此类异常会被专门处理。
响应示例
```json
{
  "timestamp": "2021-08-25T03:10:53.846+00:00",
  "status": 404,
  "path": "/v1/tenant/tenants/2/invitations",
  "code": "tenant.10009",
  "error": "Not Found",
  "message": "user[id=0] cannot be found"
}
```
抛出方式

```java
if (tenantEntity == null) {
    throw new BizException(HttpStatus.NOT_FOUND, "tenant.10006", tenantId);
}
```

异常码及错误信息定义. 
默认使用文件`src/main/resources/messages.properties`.
异常码格式建议为`{应用标识}.{10000-99999}`

```properties
tenant.10006=编号为{0}的租户无法找到
```
> 这只是后台定义异常码及异常信息国际化的方式。前端可以根据需要只取异常码，然后自定义展示信息。

### 关系数据库相关

- 关系数据库一般为`MySQL`
- 字符集采用 `utf8mb4_unicode_ci`
- 建库和建用户及授权语句放到README中，方便其他开发人员快速上手
- 建议使用`liquibase`进行数据库自动变更
- 建议使用`MyBatis`进行数据库操作

#### liquibase 相关

- `changelog`文件存放位置 `src/main/resources/db/liquibase/changelog.xml`
- `sql`文件存放位置 `src/main/resources/db/liquibase/sql/yyyy-MM-dd_{title}.sql`

#### MyBatis 相关

- 使用`tk.mybatis:mapper-spring-boot-starter`简化原始的`MyBatis API`
- 使用`com.github.pagehelper:pagehelper-spring-boot-starter`简化分页查询

### OpenAPI

建议采用文档优先的模式来开发API
- 先使用`OpenAPI`规范编写`API`文档。
- 然后使用`Maven`插件根据`API`文档生成代码。生成的代码主要包括`API Interface`和`Parameter Bean`.
- 编写自己的`Controller`来`implements`生成的`interface`

> 鉴于规范本身的特性覆盖和学习成本、配套工具的完善程度，目前建议使用`OpenAPI 2.0`的规范。

**API编写命名规范**

- Path 命名
  - 固定部分：使用中划线(-)进行分隔，比如`/v1/tenant/tenant-users`。
  - 变量部分：使用驼峰方式命名，比如`/v1/tenant/tenant-users/{tenantUserId}`。
- 参数命名
  - query 参数：使用下划线(_)分隔，比如`page_size=100&current_page=1`。
  - body 参数：包括request body和response body使用下划线(_)分隔，比如：
    ```json
    {
      "user_id": 100,
      "user_address": "hello street",
      "age": 18
    }
    ```

**其他**

- 建议API文档存放位置 `src/main/resources/apis/apis.yml`
- 代码生成插件配置如下(注意将其中的`#your-package-name#`替换为实际的项目包名)
    ```xml
    <plugin>
        <groupId>io.swagger</groupId>
        <artifactId>swagger-codegen-maven-plugin</artifactId>
        <version>2.4.19</version>
        <executions>
            <execution>
                <id>generate-api</id>
                <goals>
                    <goal>generate</goal>
                </goals>
                <configuration>
                    <inputSpec>${project.basedir}/src/main/resources/apis/apis.yml</inputSpec>
                    <language>spring</language>
                    <library>spring-boot</library>
                    <apiPackage>#your-package-name#.api</apiPackage>
                    <modelPackage>#your-package-name#.api.model</modelPackage>
                    <modelNamePrefix>Api</modelNamePrefix>
                    <generateApis>true</generateApis>
                    <generateSupportingFiles>false</generateSupportingFiles>
                    <configOptions>
                        <interfaceOnly>true</interfaceOnly>
                        <useTags>true</useTags>
                    </configOptions>
                </configuration>
            </execution>
        </executions>
    </plugin>
    ```

### 远程调用

建议使用`Spring Cloud OpenFeign`进行远程调用
- 声明式，方便开发
- 所有远程调用集中管理
- 方便后续增加容错（熔断、限流、重试）功能
### Lombok

建议使用Lombok提供的注解来简化Java Bean的开发
具体请参考
- https://projectlombok.org/features/all
- https://projectlombok.org/setup/maven

另外使用IDE时需要安装对应的Lombok插件
- https://projectlombok.org/setup/intellij
- https://projectlombok.org/setup/vscode

### 配置相关

`Spring Boot`应用默认使用`src/main/resources/application.yml`作为配置文件。
开发过程中建议新建一个`src/main/resources/application-dev.yml`文件来覆盖`application.yml`中的配置。
- `application.yml` 中主要存放必须配置。比如开发、测试、生产环节都不会变的配置。
- `application-dev.yml` 存放易变配置，比如开发、测试、生产环节不一样的配置。

需要在`application.yml`中配置如下内容来激活`application-dev.yml`.
```yml
spring.profiles.active: dev
```
#### 关键配置

```yml
# application.yml
spring.application.name: backend-tenant #应用唯一标识

feign.client.config.default.connectTimeout: 30000 #远程调用默认连接超时时间
feign.client.config.default.readTimeout: 60000 #远程调用默认读取超时时间
```

```yml
# application-dev.yml
megaease.output.type: console #设定输出目的地为控制台，默认的输出为kafka
megaease.trace.enabled: false #关闭调用链采集功能
megaease.metrics.enabled: false #关闭指标采集功能
```

## 开发相关规范
### Controller
* 外部调用业务处理的入口，Controller没有任何业务处理的操作
* 作为承上启下的功能，对外接收请求
* 然后转换数据到业务需要的格式
* 对数据进行格式验证
* 调用Service 进行业务处理
* 对Service返回值进行包装，返回给调用者，大多都是返回json格式


### Service
* 业务逻辑处理类，相关业务放到一个service中，例如用户相关，放到UserService ,账户相关，放到AccountService
* 方法都是对业务进行处理的，包括对象的属性更改，更改后，对数据的保存，查询。
* 业务方法中尽量少做sql处理，把sql处理的逻辑都封装到Mapper类中。


### Dao
* Mapper类都是对数据表进行处理的java文件，这里不要写任何业务逻辑，只做对数据进行 增删改查 的操作。
* 每一个Mapper类都是对应一个表的操作，尽量不要进行表join操作，如果需要，不要超过2张表的jion。
* 返回的数据都是返回对应的对象或者集合。
* 没有特殊情况不需要返回部分字段，如果需要返回部分字段，最好有新的对象与之对应。例如
```
//查询部分字段, UserMapper中

@Select("select user_id, name from t_user;")
List<UserPart1> selectList();

```
* 以上例子如果返回 User(userId, name, show, createTime)，会造成调用者无法获取其他字段数据，因此在没有性能损耗的前提下，不要返回部分字段


### Entity
Entity 作为实体与数据表，应保持与数据表字段的一致。对于数据表使用小写+下划线分割的方式，对于java对象的属性，使用驼峰命名的方式。

```java
@Table(name = "t_user")
public class User{ // User 与 表t_user对应


	private Long userId;  // 与表字段 user_id对应
	private String userName; // 与表字段 user_name对应
	private boolean showUser; // 与表字段 show_user对应
	private Date createTime; // 与表字段 create_time对应
}

```
对于java中对象属性尽量与数据表字段类型一致，例如 
```
bigint  --  Long
varchar  --  String
text longtext -- String
datetime -- java.util.Date
int smallint tinyint -- Integer
对于值只有 0 1 表示 true false的可以 使用 tinyint(1) 0=false 1=true

```
数据表尽量不要存储二进制文件，例如下载包等。通过文件存储的服务来处理文件。

对于使用MyBatis，可以配置驼峰匹配的方式：

```
mybatis:
  configuration:
    map-underscore-to-camel-case: true

```
