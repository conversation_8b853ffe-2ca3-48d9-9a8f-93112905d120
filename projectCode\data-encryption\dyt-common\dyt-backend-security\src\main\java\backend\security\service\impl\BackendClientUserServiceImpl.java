package backend.security.service.impl;

import backend.common.exception.BizException;
import backend.security.oauth2.BackendOAuth2Constants;
import backend.security.service.BackendClientUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/6/30 16:52
 */
@RequiredArgsConstructor
public class BackendClientUserServiceImpl implements BackendClientUserService {
    private final JwtDecoder jwtDecoder;

    @Override
    public Long getCurrentUserIdFromJwt(String jwtString) {
        Jwt jwt = jwtDecoder.decode(jwtString);
        Long userId = jwt.getClaim(BackendOAuth2Constants.SCOPE_USER);
        if (null == userId) {
            throw new BizException(HttpStatus.BAD_REQUEST, "用户信息读取错误");
        }
        return userId;
    }

    @Override
    public String getCurrentUserOpenId() {
        final Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (!(authentication instanceof JwtAuthenticationToken)) {
            return null;
        }
        JwtAuthenticationToken token = (JwtAuthenticationToken) authentication;
        String openid = token.getToken().getClaim("new_openid");
        if (null == openid) {
            openid = token.getToken().getClaim("openid");
        }
        return openid;
    }
}
