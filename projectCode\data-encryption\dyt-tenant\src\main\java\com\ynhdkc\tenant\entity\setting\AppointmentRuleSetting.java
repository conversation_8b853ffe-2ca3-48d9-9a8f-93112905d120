package com.ynhdkc.tenant.entity.setting;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-08 18:01
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(name = "t_hospital_appointment_rule_setting")
public class AppointmentRuleSetting extends BaseSetting {
    /**
     * 科室级别：0，一级科室；1，二级科室；2，三级科室；3，四级科室；
     */
    private Integer departmentLevel;
    /**
     * 科室排序类型：0，倒序；1，正序；
     */
    private Integer departmentOrderType;
    /**
     * 挂号系统依赖：0，HIS；1，小系统，2:依赖 HIS 不更新科室
     */
    private Integer systemDepends;

    /**
     * 当前使用系統类型: 1,老系统;2,新系统;3,新老系统共用;
     */
    private Integer currentSystemType;
    /**
     * 当天预约：0，关闭；1，开启；
     */
    private Boolean appointToday;
    /**
     * 显示序号：0，关闭；1，开启；
     */
    private Boolean displayNo;
    /**
     * 停止预约时间
     */
    private String stopAppointTime;
    /**
     * 医生排班缓存过期时间（单位：秒/s）
     */
    private Long doctorScheduleCacheExpTime;

    /**
     * 停止预约天数
     */
    private Integer forbiddenDay;
    /**
     * 提前预约天数：-1，不限制；
     */
    private Integer advanceDay;

    private String sourceActivateTime;

    /**
     * 号源显示时间
     */
    private String displayTime;
    /**
     * 支付关闭时间，单位分钟
     */
    private Long paymentCloseDuration;
    /**
     * 是否允许当天退款：0，不允许；1，允许；
     */
    private Boolean refundToday;
    /**
     * 停止退款时间
     */
    private String stopRefundTime;
    /**
     * 是否需要确认医保卡：0，不需要；1，需要；
     */
    private Boolean confirmMedicalInsuranceCard;
    /**
     * 是否需要核销：0，不需要；1，需要；
     */
    private Boolean orderNeedVerify;
    /**
     * 预约提示
     */
    private String appointmentNotice;
    /**
     * 预约结果提示
     */
    private String appointmentResultNotice;
    /**
     * 预约后联系人
     */
    private String appointmentNotifyContact;

    /**
     * 预约提醒
     */
    private String appointmentReminder;
    /**
     * 预约成功通知模板ID
     */
    private String noticeTemplateId;
    /**
     * 预约规则
     */
    private String appointmentRule;
    /**
     * 预约指南
     */
    private String appointmentGuide;

    /**
     * 挂号提示
     */
    private String registrationReminder;

    /**
     * 已选支付方式
     */
    private String selectedPayments;

    /**
     * 科室下医生列表：0，显示科室下所有医生；1，显示有排班的医生；
     */
    private Integer displayDoctorUnderDepartment;

    /**
     * 是否需要支付,0:不需要,1:需要
     */
    private Boolean needPayment;

    /**
     * 是否支持锁号,false:不支持,true:支持
     */
    private Boolean supportLockingAppointmentNo;

    private Boolean supportCancelAppointment;

    /**
     * 是否需要分时间段,0:不需要,1:需要
     */
    private Boolean needPartitionTime;

    /**
     * 是否需要分时段,0:不需要,1:需要
     */
    private Boolean displayDepartmentAddress;

    /**
     * 显示就诊时间：0，不显示；1，显示；
     */
    private Boolean displayVisitTime;

    /**
     * 是否需要签到,0:不需要,1:需要
     */
    private Boolean needSignIn;

    /**
     * 全科室背景色
     */
    private String allDepartmentBackgroundColor;

    /**
     * 依赖 HIS 的预约挂号服务，不更新科室，true:更新，false：不更新
     */
    private Boolean updateDepartmentDependHis;
    /**
     * 科室是否可用 0: 禁用，1:启用
     */
    private Boolean hospitalDependHisEnabledDepartment;

    private String updateDepartmentExcludeColumns;
    private String updateDoctorExcludeColumns;
    private Boolean createDoctorFromHis;

    /**
     * 停止生成排班开始时间
     */
    private Date stopGenerateScheduleBeginDate;

    /**
     * 停止生成排班结束时间
     */
    private Date stopGenerateScheduleEndDate;

    /**
     * 排班日期显示方式，0-不显示 1-显示全部日期 2-仅显示有排班的日期
     */
    private Integer timePeriodDisplayType;

    /**
     * 医生职称展示类型。1-平台职称，2-医院HIS职称，3-荣誉
     */
    private Integer doctorTitleShowType;
}
