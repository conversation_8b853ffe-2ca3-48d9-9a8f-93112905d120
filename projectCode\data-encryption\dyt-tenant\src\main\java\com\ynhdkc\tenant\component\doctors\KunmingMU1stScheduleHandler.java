package com.ynhdkc.tenant.component.doctors;

import backend.common.domain.tenant.constant.AppointmentSystemDepends;
import backend.common.exception.BizException;
import backend.common.util.JsonUtil;
import backend.common.util.MessageUtil;
import backend.common.util.ObjectsUtils;
import com.ynhdkc.tenant.client.HisGatewayClient;
import com.ynhdkc.tenant.client.model.DepartmentDoctorListEnvRequest;
import com.ynhdkc.tenant.client.model.DepartmentDoctorListEnvResponse;
import com.ynhdkc.tenant.dao.DepartmentQuery;
import com.ynhdkc.tenant.dao.DoctorQuery;
import com.ynhdkc.tenant.dao.DoctorRepository;
import com.ynhdkc.tenant.dto.ScheduleResponseDto;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.DictLabel;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.entity.model.DoctorScheduleModel;
import com.ynhdkc.tenant.entity.model.ExecDepartmentModel;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import com.ynhdkc.tenant.service.backend.DictLabelService;
import com.ynhdkc.tenant.service.customer.impl.HisGatewayServiceImpl;
import com.ynhdkc.tenant.tool.DoctorHeadImageHandler;
import com.ynhdkc.tenant.util.CommonUtil;
import com.ynhdkc.tenant.util.HospitalUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.ynhdkc.tenant.util.DateUtil.convertDateFromMMDD;

@Slf4j
public abstract class KunmingMU1stScheduleHandler extends AbstractDoctorListHandler {

    private static final String DOCTOR_RANK_DICT_TYPE = "doctor_title";

    public static final String CONSULTATION_DEPARTMENT_CODE = "5268";

    @Resource
    protected HisGatewayClient hisGatewayClient;

    @Resource
    protected DepartmentQuery departmentQuery;

    @Resource
    protected DoctorQuery doctorQuery;

    @Resource
    protected DictLabelService dictLabelService;

    @Resource
    protected HisGatewayServiceImpl hisGatewayService;

    @Resource
    protected DoctorRepository doctorRepository;

    @Resource
    protected DoctorHeadImageHandler doctorHeadImageHandler;

    protected DoctorScheduleModel processScheduleInfo(Department department, List<Doctor> doctors, AppointmentRuleSetting appointmentRuleSetting) {
        DoctorScheduleModel doctorScheduleModel = new DoctorScheduleModel();
        doctorScheduleModel.setDoctors(doctors);

        DepartmentDoctorListEnvResponse response = makeRequest(department);
        if (response == null || response.getPayload() == null) {
            return doctorScheduleModel;
        }
        List<Doctor> doctorsFromHis = response.getPayload().getResult().stream().map(result -> {
            hisGatewayService.assembleDoctorInfo(result);
            Doctor doctor = result.getDoctor();
            doctor.setRegistrationLevel(result.getRegistrationLevelDesc());
            return doctor;
        }).collect(Collectors.toList());

        Map<String, Department> execDepartmentMap = queryExecDepartmentMap(doctorsFromHis, department);

        createOrUpdateDoctor(doctorsFromHis, department, execDepartmentMap);
        List<Doctor> processedDoctors = queryDoctorListByDeptId(department);
        doctorScheduleModel.setDoctors(processedDoctors);

        List<ScheduleResponseDto.ScheduleInfo> scheduleInfoList = toScheduleInfo(response, processedDoctors, execDepartmentMap);
        doctorScheduleModel.setScheduleInfoList(scheduleInfoList);

        if (!ObjectsUtils.isEmpty(scheduleInfoList) && !ObjectsUtils.isEmpty(execDepartmentMap)) {
            execDepartmentMap.forEach((key, value) -> {
                hisGatewayService.cacheDepartmentDoctorScheduleInfo(scheduleInfoList, value, appointmentRuleSetting.getDoctorScheduleCacheExpTime());
                hisGatewayService.cacheDoctorScheduleInfo(scheduleInfoList, value);
            });
        }
        return doctorScheduleModel;
    }

    protected List<Doctor> queryDoctorList(Department department) {
        List<Doctor> doctorList = queryDoctorByJoinDepartments(department);
        log.debug("before_filter doctors: {}", MessageUtil.object2JSONString(doctorList));
        return doctorList.stream().filter(q -> q.getStatus() == 0).collect(Collectors.toList());
    }

    protected List<Doctor> queryDoctorListByDeptId(Department department) {
        if (department.getThrdpartDepCode().equals(HospitalUtils.YUN_DA_SD_DEPARTMENT_CODE) ||
                HospitalUtils.YUN_DA_MDT_DEPARTMENT_CODE.equals(department.getThrdpartDepCode())) {
            List<Doctor> doctorList = doctorQuery.findByDepartmentId(department.getId());
            if (ObjectUtils.isEmpty(doctorList)) {
                return Collections.emptyList();
            }
            doctorList.forEach(y -> y.setDisplayDepartmentName(department.getName()));
            return doctorList.stream().filter(q -> q.getStatus() == 0).collect(Collectors.toList());
        } else {
            return queryDoctorList(department);
        }
    }

    private void createOrUpdateDoctor(List<Doctor> doctorsFromHis, Department department, Map<String, Department> execDepartmentMap) {
        if (CollectionUtils.isEmpty(doctorsFromHis)) {
            return;
        }
        List<DictLabel> dictLabels = dictLabelService.getDictLabelBy(DOCTOR_RANK_DICT_TYPE);
        doctorsFromHis.forEach(item -> {
            Optional<DictLabel> dictLabelOptional = dictLabels.stream().filter(q -> q.getDictLabel().equals(item.getRankDictLabel())).findFirst();
            if (dictLabelOptional.isPresent()) {
                DictLabel dictLabel = dictLabelOptional.get();
                item.setRankDictType(dictLabel.getDictType());
                item.setRankDictValue(dictLabel.getDictValue());
                item.setRankDictLabel(dictLabel.getDictLabel());
            }
        });
        saveDepartmentDoctors(doctorsFromHis, department, execDepartmentMap);
    }

    protected List<ScheduleResponseDto.ScheduleInfo> toScheduleInfo(DepartmentDoctorListEnvResponse response,
                                                                    List<Doctor> doctors,
                                                                    Map<String, Department> execDepartmentMap) {
//        log.debug("response_is_doctor 5: {}", MessageUtil.object2JSONString(response));
//        log.debug("response_is_doctor 6: {}", MessageUtil.object2JSONString(department));
        log.debug("response_is_doctor doctors: {}", MessageUtil.object2JSONString(doctors));
        log.debug("response_is_doctor exec dpt: {}", MessageUtil.object2JSONString(execDepartmentMap));

        List<ScheduleResponseDto.ScheduleInfo> schedules = new ArrayList<>();
        if (response.getPayload() == null) {
            return schedules;
        }
        Map<String, Doctor> doctorMap = doctors.stream().collect(Collectors.toMap(y -> getDoctorKey(y.getDepartmentId(), y.getThrdpartDoctorCode()), y -> y));
        for (DepartmentDoctorListEnvResponse.Payload.Result result : response.getPayload().getResult()) {
            Department execDepartment = execDepartmentMap.get(result.getExecDepartmentCode());
            if (execDepartment == null) {
                continue;
            }
            String doctorKey = getDoctorKey(execDepartment.getId(), result.getDoctorCode());
            Doctor doctor = doctorMap.get(doctorKey);
            if (doctor == null) {
                continue;
            }
            result.getSrcDateMap().forEach((dateStr, number) -> schedules.add(createScheduleInfo(doctor, result, dateStr, number)));
        }
        log.debug("schedules is {}", MessageUtil.object2JSONString(schedules));
        return schedules;
    }

    protected DepartmentDoctorListEnvResponse makeRequest(Department department) {
        DepartmentDoctorListEnvRequest request = hisGatewayService.buildbuildDepartmentDoctorListEnvRequest(department);
        if (StringUtils.hasText(department.getThrdpartDepCode())) {
            ResponseEntity<DepartmentDoctorListEnvResponse> temp = hisGatewayClient.getDoctors(department.getHospitalCode(), department.getThrdpartDepCode(), request);
            return handleDepartmentDoctorListEnvResponse(temp, request);
        } else {
            return null;
        }
    }

    private DepartmentDoctorListEnvResponse handleDepartmentDoctorListEnvResponse(ResponseEntity<DepartmentDoctorListEnvResponse> responseEntity, DepartmentDoctorListEnvRequest request) {
        if (responseEntity.getStatusCode() != HttpStatus.OK) {
            log.error("Failed to get doctors. Request: {}. Response Status: {}", JsonUtil.serializeObject(request), responseEntity.getStatusCode());
            throw new BizException(responseEntity.getStatusCode(), "获取排班周期失败");
        }

        DepartmentDoctorListEnvResponse response = responseEntity.getBody();
        if (response == null) {
            log.error("Response body is null. Request: {}", JsonUtil.serializeObject(request));
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "获取排班周期失败");
        }

        if (response.getPayload() == null) {
            DepartmentDoctorListEnvResponse.Payload payload = new DepartmentDoctorListEnvResponse.Payload();
            payload.setResult(new ArrayList<>());
            response.setPayload(payload);
        }

        hisGatewayService.filterDoctorScheduleList(request.getPayLoad().getStartDate(), request.getPayLoad().getEndDate(), response.getPayload().getResult());
        log.debug("schedule info response: {}", JsonUtil.serializeObject(response));
        return response;
    }


    private void saveDepartmentDoctors(List<Doctor> doctorsFromHis, Department department, Map<String, Department> execDepartmentMap) {
        Map<String, Set<String>> execDptDoctorMap = queryExecDepartmentDoctors(execDepartmentMap, department);
        List<Doctor> saveDoctorList = new ArrayList<>();
        for (Doctor doctor : doctorsFromHis) {
            String execDepartmentCode = doctor.getDepartmentCode();
            Department execDepartment = execDepartmentMap.get(execDepartmentCode);
            if (execDepartment == null) {
                continue;
            }
            doctor.setDepartmentId(execDepartment.getId());
            Set<String> doctorCodeSet = execDptDoctorMap.get(execDepartmentCode);
            if (doctorCodeSet.contains(doctor.getThrdpartDoctorCode())) {
                continue;
            }
            saveDoctorList.add(doctor);
        }

        log.debug("create_db_doctor: {}", MessageUtil.object2JSONString(saveDoctorList));
        Map<String, Doctor> existDoctorMap = assembleExistDoctorMap(doctorsFromHis);
        for (Doctor hisDoctor : saveDoctorList) {
            createDoctorFromDepartmentDoctorItem(hisDoctor, department, existDoctorMap);
        }
    }

    public Map<String, Doctor> assembleExistDoctorMap(List<Doctor> doctorsFromHis) {
        Map<String, Doctor> existDoctorMap = new HashMap<>();
        List<String> existDoctorCodeList = doctorsFromHis.stream().map(Doctor::getThrdpartDoctorCode).collect(Collectors.toList());
        if (ObjectsUtils.isEmpty(existDoctorCodeList)) {
            return existDoctorMap;
        }
        List<Doctor> existDoctorList = assembleExistDbDoctors(existDoctorCodeList);
        if (ObjectsUtils.isEmpty(existDoctorList)) {
            return existDoctorMap;
        }
        for (Doctor doctor : existDoctorList) {
            if (ObjectsUtils.isEmpty(doctor.getIntroduction()) || ObjectsUtils.isEmpty(doctor.getSpeciality())) {
                continue;
            }
            existDoctorMap.put(doctor.getThrdpartDoctorCode(), doctor);
        }
        return existDoctorMap;
    }

    private List<Doctor> assembleExistDbDoctors(List<String> existDoctorCodeList) {
        List<Doctor> existDoctorList = new ArrayList<>();
        for (String hospitalCode : realTimeSortHospitalCodeList) {
            List<Doctor> tmpExistDoctorList = doctorQuery.queryList(hospitalCode, existDoctorCodeList);
            if (ObjectsUtils.isEmpty(tmpExistDoctorList)) {
                continue;
            }
            existDoctorList.addAll(tmpExistDoctorList);
        }
        return existDoctorList;
    }

    public void createDoctorFromDepartmentDoctorItem(Doctor doctorInfoFromHis, Department department, Map<String, Doctor> existDoctorMap) {
        Doctor doctor = new Doctor();
        BeanUtils.copyProperties(doctorInfoFromHis, doctor);

        Doctor existDbDoctor = existDoctorMap.get(doctor.getThrdpartDoctorCode());
        if (existDbDoctor != null) {
            doctor.setIntroduction(existDbDoctor.getIntroduction());
            doctor.setSpeciality(existDbDoctor.getSpeciality());
            doctor.setHeadImgUrl(existDbDoctor.getHeadImgUrl());
            doctor.setHonor(existDbDoctor.getHonor());
        }
        doctor.setHospitalId(department.getHospitalId());
        doctor.setHospitalCode(department.getHospitalCode());
        doctor.setTenantId(department.getTenantId());
        doctor.setHospitalAreaId(department.getHospitalAreaId());
        doctor.setDisplay(true);
        doctor.setSystemDepends(AppointmentSystemDepends.HIS.getCode());
        doctorHeadImageHandler.setDoctorImageUrl(doctor, doctorInfoFromHis.getHeadImgUrl());

        doctorRepository.create(doctor);
    }

    private Map<String, Set<String>> queryExecDepartmentDoctors(Map<String, Department> departmentMap, Department department) {
        Map<String, Set<String>> departmentDoctorMap = new HashMap<>();
        if (ObjectsUtils.isEmpty(departmentMap)) {
            return departmentDoctorMap;
        }
        for (Map.Entry<String, Department> entry : departmentMap.entrySet()) {
            Long execDepartmentId = entry.getValue().getId();
            List<Doctor> doctorList = doctorQuery.queryBy(department.getHospitalAreaId(), execDepartmentId);
            if (ObjectsUtils.isEmpty(doctorList)) {
                departmentDoctorMap.put(entry.getKey(), new HashSet<>());
                continue;
            }
            Set<String> doctorCodeSet = doctorList.stream().map(Doctor::getThrdpartDoctorCode).collect(Collectors.toSet());
            departmentDoctorMap.put(entry.getKey(), doctorCodeSet);
        }
        return departmentDoctorMap;
    }

    protected Map<String, Department> queryExecDepartmentMap(List<Doctor> doctorsFromHis, Department department) {
        if (ObjectsUtils.isEmpty(doctorsFromHis)) {
            return new HashMap<>();
        }
        Set<String> departmentCodeSet = doctorsFromHis.stream().map(Doctor::getDepartmentCode).collect(Collectors.toSet());
        return queryExecDepartmentMap(departmentCodeSet, department);
    }

    protected Map<String, Department> queryExecDepartmentMap(Set<String> departmentCodeSet, Department department) {
        Map<String, Department> departmentMap = new HashMap<>();
        if (ObjectsUtils.isEmpty(departmentCodeSet)) {
            return departmentMap;
        }
        if (HospitalUtils.YUN_DA_SD_DEPARTMENT_CODE.equals(department.getThrdpartDepCode()) ||
                HospitalUtils.YUN_DA_MDT_DEPARTMENT_CODE.equals(department.getThrdpartDepCode())) {
            departmentMap.put(department.getThrdpartDepCode(), department);
            return departmentMap;
        }
        for (String execDepartmentCode : departmentCodeSet) {
            Department execDepartment = CommonUtil.filterDepartment(departmentQuery.queryList(department.getHospitalCode(), execDepartmentCode));
            if (execDepartment == null) {
                continue;
            }
            departmentMap.put(execDepartment.getThrdpartDepCode(), execDepartment);
        }
        return departmentMap;
    }

    private ScheduleResponseDto.ScheduleInfo createScheduleInfo(Doctor doctor, DepartmentDoctorListEnvResponse.Payload.Result item, String dateStr, Integer number) {
        ScheduleResponseDto.ScheduleInfo scheduleInfo = new ScheduleResponseDto.ScheduleInfo();
        scheduleInfo.setDoctorId(doctor.getId());
        scheduleInfo.setDoctorCode(doctor.getThrdpartDoctorCode());
        scheduleInfo.setHospitalId(doctor.getHospitalId());
        scheduleInfo.setHospitalAreaId(doctor.getHospitalAreaId());
        scheduleInfo.setDepartmentId(doctor.getDepartmentId());
        scheduleInfo.setDepartmentName(item.getDepartmentName());
        scheduleInfo.setDepartmentCode(item.getDepartmentCode());
        scheduleInfo.setExecDepartmentCode(doctor.getDepartmentCode());
        scheduleInfo.setShowSchDate(1);
        scheduleInfo.setSrcNum(number);
        scheduleInfo.setSchDate(convertDateFromMMDD(dateStr));
        scheduleInfo.setRegistrationLevelDesc(item.getRegistrationLevelDesc());
        return scheduleInfo;
    }

    private List<Doctor> queryDoctorByJoinDepartments(Department department) {
        List<Doctor> doctorList = new ArrayList<>();
        String[] departmentCodeList = department.getThrdpartDepCode().split("\\|");
        List<ExecDepartmentModel> departmentIdList = queryExecDepartmentIdList(departmentCodeList, department);

        for (ExecDepartmentModel execDepartmentModel : departmentIdList) {
            List<Doctor> subCategoryDoctors = doctorQuery.findByDepartmentId(execDepartmentModel.getId());
            if (ObjectUtils.isEmpty(subCategoryDoctors)) {
                continue;
            }
            subCategoryDoctors.forEach(y -> y.setDisplayDepartmentName(execDepartmentModel.getName()));
            doctorList.addAll(subCategoryDoctors);
        }
        doctorList = doctorList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                new TreeSet<>(Comparator.comparing(y -> y.getDepartmentId() + "_" + y.getThrdpartDoctorCode()))), ArrayList::new));

        log.debug("queryDoctorByJoinDepartments doctors: {}", MessageUtil.object2JSONString(doctorList));
        return doctorList;
    }

    private List<ExecDepartmentModel> queryExecDepartmentIdList(String[] departmentCodeList, Department department) {
        if (ObjectUtils.isEmpty(departmentCodeList)) {
            return Collections.emptyList();
        }
        List<ExecDepartmentModel> departmentIdList = new ArrayList<>();
        for (String execDepartmentCode : departmentCodeList) {
            Department execDepartment = CommonUtil.filterDepartment(departmentQuery.queryList(department.getHospitalCode(), execDepartmentCode));
            if (execDepartment == null) {
                continue;
            }
            ExecDepartmentModel execDepartmentModel = new ExecDepartmentModel();
            execDepartmentModel.setId(execDepartment.getId());
            execDepartmentModel.setName(execDepartment.getName());

            departmentIdList.add(execDepartmentModel);
        }
        return departmentIdList;
    }
}
