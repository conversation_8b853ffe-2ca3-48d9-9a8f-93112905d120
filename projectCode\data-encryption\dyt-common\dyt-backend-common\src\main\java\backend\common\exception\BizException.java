package backend.common.exception;

import com.google.common.base.Strings;
import org.springframework.http.HttpStatus;

public class BizException extends RuntimeException {

    private final HttpStatus httpStatus;
    private final String bizCode;
    private final Object[] args;

    public BizException(String message) {
        super(message);
        this.httpStatus = null;
        this.bizCode = "";
        this.args = null;
    }

    public BizException(String bizCode, String message) {
        super(message);
        this.httpStatus = null;
        this.bizCode = bizCode;
        this.args = null;
    }

    public BizException(HttpStatus httpStatus, String bizCode, Object... args) {
        super(Strings.nullToEmpty(bizCode));
        this.httpStatus = httpStatus;
        this.bizCode = bizCode;
        this.args = args;
    }

    public BizException(String bizCode, HttpStatus httpStatus, Object... args) {
        super(Strings.nullToEmpty(bizCode));
        this.httpStatus = httpStatus;
        this.bizCode = bizCode;
        this.args = args;
    }

    @Override
    public String getMessage() {
        return super.getMessage();
    }

    public HttpStatus getHttpStatus() {
        return httpStatus;
    }

    public String getBizCode() {
        return bizCode;
    }

    public Object[] getArgs() {
        return args;
    }
}
