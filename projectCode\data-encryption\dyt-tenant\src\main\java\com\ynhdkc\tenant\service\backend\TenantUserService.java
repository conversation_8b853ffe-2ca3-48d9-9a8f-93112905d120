package com.ynhdkc.tenant.service.backend;

import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.entity.TenantUser;
import com.ynhdkc.tenant.model.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-25 22:42
 */
public interface TenantUserService {
    static TenantUser toTenantUser(TenantUserCreateReqDto dto) {
        TenantUser entity = new TenantUser();
        entity.setName(dto.getName());
        entity.setNickname(dto.getNickname());
        entity.setPassword(dto.getPassword());
        entity.setPhoneNumber(dto.getPhoneNumber());
        entity.setGender(dto.getGender());
        entity.setIdCardNo(dto.getIdCardNo());
        entity.setEmail(dto.getEmail());
        entity.setStatus(dto.getStatus());
        entity.setHeadImageUrl(dto.getHeadImageUrl());
        return entity;
    }

    static TenantUserVo toTenantUserVo(TenantUser entity) {
        TenantUserVo vo = new TenantUserVo();
        vo.setId(entity.getId());
        vo.setNickname(entity.getNickname());
        vo.setName(entity.getName());
        vo.setEmail(entity.getEmail());
        vo.setStatus(entity.getStatus());
        vo.setCreateTime(entity.getCreateTime());
        vo.setHeadImageUrl(entity.getHeadImageUrl());
        return vo;
    }

    static TenantUserDetailVo toTenantUserDetailVo(TenantUser entity) {
        TenantUserDetailVo vo = new TenantUserDetailVo();
        vo.setId(entity.getId());
        vo.setName(entity.getName());
        vo.setNickname(entity.getNickname());
        vo.setEmail(entity.getEmail());
        vo.setStatus(entity.getStatus());
        vo.setPhoneNumber(entity.getPhoneNumber());
        vo.setAdmin(entity.getAdmin());
        vo.setCreateTime(entity.getCreateTime());
        vo.setHeadImageUrl(entity.getHeadImageUrl());
        return vo;
    }

    TenantUserDetailVo create(TenantUserCreateReqDto createTenantUserDto);

    BaseOperationResponse delete(Long userId);

    TenantUserDetailVo update(Long tenantUserId, TenantUserUpdateReqDto updateTenantUserDto);

    TenantUserDetailVo getDetail(Long userId, Long tenantId);

    TenantUserPageVo query(TenantUserQueryReqDto request);

    List<TenantUserVo> getTenantBoundUser(Long tenantId);

    List<TenantVo> getUserBoundTenants(Long userId, String tenantName);

    BaseOperationResponse bindTenant(Long tenantUserId, List<UserTenantPrivilegeConfig> userTenantPrivilegeConfigList);

    BaseOperationResponse unbindTenant(Long userId, List<Long> tenantIdList);

    BaseOperationResponse sendMsg(TenantUserSendMegReqDto tenantUserSendMegReqDto);

    TenantUserDetailVo getUserDetail(Long userId);

    BaseOperationResponse modify(Long userId, TenantUserModifyReqDto request);

    CheckTenantUserPasswordResultVo checkPassword(Long id);

    public BaseOperationResponse sendDesensitizationMsg();

    public BaseOperationResponse desensitizationSmsVerify(String smsCode);
}
