package com.ynhdkc.tenant.api.rpc;

import com.ynhdkc.tenant.handler.RpcHospitalAreaApi;
import com.ynhdkc.tenant.model.HospitalAreaVo;
import com.ynhdkc.tenant.model.HospitalDependOnHisResponse;
import com.ynhdkc.tenant.model.RpcBatchGetByIdReqDto;
import com.ynhdkc.tenant.model.RpcStopScheduleDataMap;
import com.ynhdkc.tenant.service.backend.HospitalAreaService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-23 10:48
 */
@Api(tags = "RpcHospitalArea")
@RestController
@RequiredArgsConstructor
public class RpcHospitalAreaController implements RpcHospitalAreaApi {
    private final HospitalAreaService hospitalAreaService;

    @Override
    public ResponseEntity<List<HospitalAreaVo>> batchGetHospitalAreaDetail(RpcBatchGetByIdReqDto request) {
        return ResponseEntity.ok(hospitalAreaService.rpcBatchGetDetail(request));
    }

    @Override
    public ResponseEntity<HospitalAreaVo> getHospitalAreaDetail(Long id) {
        return ResponseEntity.ok(hospitalAreaService.rpcGetDetail(id));
    }

    @Override
    public ResponseEntity<HospitalAreaVo> getHospitalAreaDetailByCode(String code) {
        return ResponseEntity.ok(hospitalAreaService.rpcGetDetailByCode(code));
    }

    @Override
    public ResponseEntity<RpcStopScheduleDataMap> stopScheduleDataMap() {
        return ResponseEntity.ok(hospitalAreaService.rpcStopScheduleDataMap());
    }

    @Override
    public ResponseEntity<List<HospitalDependOnHisResponse>> getHospitalDependOnHis(Long id, String hospitalName) {
        return ResponseEntity.ok(hospitalAreaService.rpcGetHospitalDependOnHis(id, hospitalName));
    }
}
