package com.ynhdkc.tenant.dao.redis.template;

import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericToStringSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;

public class IntegerRedisTemplate extends RedisTemplate<String, Integer> {

    private static IntegerRedisTemplate integerRedisTemplate;

    private IntegerRedisTemplate(RedisConnectionFactory connectionFactory) {
        setKeySerializer(RedisSerializer.string());
        setValueSerializer(new GenericToStringSerializer<>(Integer.class));
        setHashKeySerializer(RedisSerializer.string());
        setHashValueSerializer(new GenericToStringSerializer<>(Integer.class));

        setConnectionFactory(connectionFactory);
        afterPropertiesSet();
    }

    public static synchronized IntegerRedisTemplate getInstance(RedisConnectionFactory connectionFactory) {
        if (integerRedisTemplate == null) {
            integerRedisTemplate = new IntegerRedisTemplate(connectionFactory);
        }
        return integerRedisTemplate;
    }
}
