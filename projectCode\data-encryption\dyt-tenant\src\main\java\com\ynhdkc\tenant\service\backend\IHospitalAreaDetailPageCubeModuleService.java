package com.ynhdkc.tenant.service.backend;

import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.entity.detailpage.HospitalAreaDetailPageCubeModule;
import com.ynhdkc.tenant.model.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/24 18:51:56
 */
public interface IHospitalAreaDetailPageCubeModuleService {
    HospitalAreaDetailPageCubeModuleVo createHospitalAreaDetailPageCubeModule(CreateHospitalAreaDetailPageCubeModuleReqDto dto);

    BaseOperationResponse deleteHospitalAreaDetailPageCubeModule(Long id);

    HospitalAreaDetailPageCubeModuleVo getHospitalAreaDetailPageCubeModule(Long id);

    HospitalAreaDetailPageCubeModulePageVo searchHospitalAreaDetailPageCubeModule(SearchHospitalAreaDetailPageCubeModuleReqDto dto);

    HospitalAreaDetailPageCubeModuleVo updateHospitalAreaDetailPageCubeModule(Long id, UpdateHospitalAreaDetailPageCubeModuleReqDto dto);

    void setCubeModule(HospitalAreaLayoutVo vo, Long hospitalAreaId);

    List<HospitalAreaDetailPageCubeModuleVo> getHospitalAreaDetailPageCubeModuleByHospitalAreaId(Long hospitalAreaId);

    static HospitalAreaDetailPageCubeModuleVo toVo(HospitalAreaDetailPageCubeModule entity) {
        HospitalAreaDetailPageCubeModuleVo vo = new HospitalAreaDetailPageCubeModuleVo();
        vo.setId(entity.getId());
        vo.setTenantId(entity.getTenantId());
        vo.setHospitalId(entity.getHospitalId());
        vo.setHospitalAreaId(entity.getHospitalAreaId());
        vo.setTitle(entity.getTitle());
        vo.setTitleStatus(entity.getTitleStatus());
        vo.setCubeDisplayType(entity.getCubeDisplayType());
        vo.setSort(entity.getSort());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdateTime(entity.getUpdateTime());
        return vo;
    }

    default void toEntity(UpdateHospitalAreaDetailPageCubeModuleReqDto dto, HospitalAreaDetailPageCubeModule entity) {
        entity.setTenantId(dto.getTenantId());
        entity.setHospitalId(dto.getHospitalId());
        entity.setHospitalAreaId(dto.getHospitalAreaId());
        entity.setTitle(dto.getTitle());
        entity.setTitleStatus(dto.getTitleStatus());
        entity.setCubeDisplayType(dto.getCubeDisplayType());
        entity.setSort(dto.getSort());
        entity.setChannels("0,1");
    }

    static HospitalAreaDetailPageCubeModule toEntity(CreateHospitalAreaDetailPageCubeModuleReqDto dto) {
        HospitalAreaDetailPageCubeModule entity = new HospitalAreaDetailPageCubeModule();
        entity.setTenantId(dto.getTenantId());
        entity.setHospitalId(dto.getHospitalId());
        entity.setHospitalAreaId(dto.getHospitalAreaId());
        entity.setChannels("0,1");
        return entity;
    }
}
