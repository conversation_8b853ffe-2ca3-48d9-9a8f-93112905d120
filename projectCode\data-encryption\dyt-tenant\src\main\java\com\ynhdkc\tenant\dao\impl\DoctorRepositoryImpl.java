package com.ynhdkc.tenant.dao.impl;

import backend.common.util.ObjectsUtils;
import com.ynhdkc.tenant.dao.DoctorRepository;
import com.ynhdkc.tenant.dao.mapper.DoctorGroupRelationMapper;
import com.ynhdkc.tenant.dao.mapper.DoctorMapper;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.entity.DoctorGroupRelation;
import com.ynhdkc.tenant.tool.DoctorUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-03 16:41
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class DoctorRepositoryImpl implements DoctorRepository {
    private final DoctorMapper doctorMapper;
    private final DoctorGroupRelationMapper doctorGroupRelationMapper;

    @Override
    public void create(Doctor doctor) {
        processDoctorName(doctor);
        doctorMapper.insertSelective(doctor);
        if (!StringUtils.hasText(doctor.getThrdpartDoctorCode())) {
            doctor.setThrdpartDoctorCode(doctor.getId().toString());
            doctorMapper.updateByPrimaryKeySelective(doctor);
        }
    }

    private void processDoctorName(Doctor doctor) {
        if (ObjectsUtils.isEmpty(doctor.getName())) {
            return;
        }
        doctor.setShortening(DoctorUtils.getFirstOne(doctor.getName()));
    }

    @Override
    public void update(Doctor doctor) {
        doctorMapper.updateByPrimaryKeySelective(doctor);
    }

    @Override
    public void delete(Long doctorId) {
        doctorMapper.deleteByPrimaryKey(doctorId);
    }

    @Override
    public void createDoctorGroupRelation(DoctorGroupRelation doctorGroupRelation) {
        doctorGroupRelationMapper.deleteByExample2(DoctorGroupRelation.class, sql -> sql.andEqualTo(DoctorGroupRelation::getDoctorId, doctorGroupRelation.getDoctorId()));
        doctorGroupRelationMapper.insertSelective(doctorGroupRelation);
    }

    @Override
    public void deleteDoctorGroupRelation(Long doctorId, Integer doctorGroupSource, Long doctorGroupId) {
        doctorGroupRelationMapper.deleteByExample2(DoctorGroupRelation.class, consumer -> {
            consumer.andEqualTo(DoctorGroupRelation::getDoctorId, doctorId);
            consumer.andEqualTo(DoctorGroupRelation::getDoctorGroupSource, doctorGroupSource);
            consumer.andEqualTo(DoctorGroupRelation::getDoctorGroupId, doctorGroupId);
        });
    }

    @Override
    public List<Doctor> queryEnabledDoctors(String code) {
        return doctorMapper.selectByExample2(Doctor.class, query -> {
            query.andEqualTo(Doctor::getHospitalCode, code);
            query.andEqualTo(Doctor::getStatus, 1);
        });
    }
}
