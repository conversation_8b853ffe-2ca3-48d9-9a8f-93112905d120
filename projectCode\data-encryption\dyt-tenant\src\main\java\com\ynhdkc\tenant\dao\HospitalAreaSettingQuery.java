package com.ynhdkc.tenant.dao;

import com.ynhdkc.tenant.entity.setting.*;
import com.ynhdkc.tenant.model.HospitalDependOnHisResponse;
import org.springframework.lang.NonNull;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-09 13:30
 */
public interface HospitalAreaSettingQuery {
    Optional<PatientCardSetting> queryPatientCardSettingById(Long tenantId, Long hospitalId, Long hospitalAreaId);

    Optional<AppointmentRuleSetting> queryAppointmentRuleSettingById(Long tenantId, Long hospitalId, Long hospitalAreaId);

    Optional<AppointmentRuleSetting> queryAppointmentRuleSettingBy(@NonNull String hospitalCode);

    Optional<DiagnosisPaymentSetting> queryDiagnosisPaymentSettingById(Long tenantId, Long hospitalId, Long hospitalAreaId);

    Optional<PatientReportSetting> queryPatientReportSettingById(Long tenantId, Long hospitalId, Long hospitalAreaId);

    Optional<HospitalizationSetting> queryHospitalizationSettingById(Long tenantId, Long hospitalId, Long hospitalAreaId);

    Optional<CustomBusinessSetting> queryCustomBusinessSettingById(Long tenantId, Long hospitalId, Long hospitalAreaId, Long settingId);

    Optional<CustomBusinessSetting> queryCustomBusinessSettingByName(Long tenantId, Long hospitalId, Long hospitalAreaId, String name);

    List<CustomBusinessSetting> queryCustomBusinessSettingById(Long tenantId, Long hospitalId, Long hospitalAreaId);

    Optional<AppointmentRuleSetting> queryAppointmentRuleSettingBy(Long hospitalAreaId);

    List<AppointmentRuleSetting> queryAppointmentRuleSettingInId(List<Long> hospitalAreaIds);

    List<AppointmentRuleSetting> queryAppointmentRuleSettingByHospitalCodes(List<String> hospitalCodes);

    List<AppointmentRuleSetting> All();

    List<AppointmentRuleSetting> queryAppointmentRuleSettingBy(Set<String> hospitalCodeSet);

    Optional<List<HospitalDependOnHisResponse>> queryDependOnHIs(Long id, String hospitalName);
}
