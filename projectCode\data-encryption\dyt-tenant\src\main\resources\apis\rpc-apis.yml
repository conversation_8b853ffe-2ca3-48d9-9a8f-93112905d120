swagger: "2.0"
info:
  title: Tenant RPC API
  version: "1.0"
host: localhost:9001
basePath: /rpc/v1/tenant
schemes:
  - http
  - https

paths:
  /tenants/{id}:
    get:
      summary: 获取租户详情
      description: 获取租户详情
      operationId: getTenantDetail
      tags:
        - RpcTenant
      parameters:
        - name: id
          in: path
          description: 租户ID
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "apis.yml#/definitions/TenantVo"

  /tenants/{id}/users/ids/all:
    get:
      summary: 拉取租户全部用户
      description: 一次性拉取全部租户下用户ID
      operationId: getTenantAllUserIds
      tags:
        - RpcTenant
      parameters:
        - name: id
          in: path
          description: 租户ID
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/GetTenantAllUserIdsVo"

  /users:
    get:
      summary: 获取租户用户详情
      description: 获取租户用户详情
      operationId: getTenantUserDetailByNameOrPhone
      tags:
        - RpcTenantUser
      parameters:
        - name: user_name
          in: query
          description: 用户名
          required: false
          type: string
          minLength: 1
          maxLength: 20
        - name: user_phone
          in: query
          description: 手机号
          required: false
          type: string
          minLength: 11
          maxLength: 13
      responses:
        200:
          description: 成功
          schema:
            $ref: "apis.yml#/definitions/TenantUserVo"

  /users/{id}:
    get:
      summary: 获取租户用户详情
      description: 获取租户用户详情
      operationId: getTenantUserDetail
      tags:
        - RpcTenantUser
      parameters:
        - name: id
          in: path
          description: 租户用户ID
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "apis.yml#/definitions/TenantUserVo"

  /users/password/verify:
    post:
      summary: 校验租户密码
      description: 校验租户密码
      operationId: passwordVerify
      tags:
        - RpcTenantUser
      parameters:
        - name: request
          in: body
          schema:
            $ref: "apis.yml#/definitions/VerifyTenantUserPasswordDto"
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
  /users/tenant-user/login/verify:
    post:
      summary: 校验租户密码和短信验证码
      description: 校验租户密码和短信验证码
      operationId: passwordAndCodeVerify
      tags:
        - RpcTenantUser
      parameters:
        - name: request
          in: body
          schema:
            $ref: "apis.yml#/definitions/VerifyTenantUserPasswordDto"
      responses:
        200:
          description: 成功
          schema:
            # noinspection SwYamlUnresolvedReferencesInspection
            $ref: "#/definitions/BaseOperationResponse"
  /departments/crond:
    get:
      tags:
        - CronInfo
      summary: 定时任务
      description: 从数据库读取科室,写kafka请求同步科室信息
      operationId: syncDepartmentsReq
      responses:
        201:
          description: 成功
          schema:
            $ref: "#/definitions/BaseResp"

  /departments/get-by-code:
    get:
      tags:
        - RpcDepartment
      summary: 根据科室编码获取科室详情
      description: 根据科室编码获取科室详情
      operationId: getDepartmentDetailByCode
      parameters:
        - name: hospital_area_code
          in: query
          description: 院区编码
          required: true
          type: string
        - name: department_code
          in: query
          description: 科室编码
          required: true
          type: string
      responses:
        200:
          description: 成功
          schema:
            $ref: "apis.yml#/definitions/DepartmentVo"

  /hospitals/{id}:
    get:
      summary: 获取医院详情
      description: 获取医院详情
      operationId: getHospitalDetail
      tags:
        - RpcHospital
      parameters:
        - name: id
          in: path
          description: 医院id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "apis.yml#/definitions/HospitalVo"

  /hospital-areas/{id}:
    get:
      summary: 获取院区详情
      description: 获取院区详情
      operationId: getHospitalAreaDetail
      tags:
        - RpcHospitalArea
      parameters:
        - name: id
          in: path
          description: 院区id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "apis.yml#/definitions/HospitalAreaVo"

  /hospital-areas/his-hospital:
    get:
      summary: 获取依赖his的启用中状态院区
      description: 获取依赖his的启用中状态院区
      operationId: getHospitalDependOnHis
      tags:
        - RpcHospitalArea
      parameters:
        - name: id
          in: query
          description: 院区id
          required: false
          type: integer
          format: int64
        - name: hospitalName
          in: query
          description: 医院名称
          required: false
          type: string
      responses:
        200:
          description: 成功
          schema:
            type: array
            items:
              $ref: "apis.yml#/definitions/HospitalDependOnHisResponse"

  /hospital-areas/by-code:
    get:
      summary: 获取院区详情
      description: 获取院区详情
      operationId: getHospitalAreaDetailByCode
      tags:
        - RpcHospitalArea
      parameters:
        - name: code
          in: query
          description: 院区编码
          required: true
          type: string
      responses:
        200:
          description: 成功
          schema:
            $ref: "apis.yml#/definitions/HospitalAreaVo"

  /departments/{id}:
    get:
      summary: 获取科室详情
      description: 获取科室详情
      operationId: getDepartmentDetail
      tags:
        - RpcDepartment
      parameters:
        - name: id
          in: path
          description: 科室id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "apis.yml#/definitions/DepartmentVo"

  /doctors/{id}:
    get:
      summary: 获取医生详情
      description: 获取医生详情
      operationId: getDoctorDetail
      tags:
        - RpcDoctor
      parameters:
        - name: id
          in: path
          description: 医生id
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "apis.yml#/definitions/DoctorDetailVo"

  /hospitals/batch/get-by-id:
    post:
      tags:
        - RpcHospital
      summary: 批量获取医院详情
      description: 批量获取医院详情
      operationId: batchGetHospitalDetail
      parameters:
        - name: request
          in: body
          schema:
            $ref: "#/definitions/RpcBatchGetByIdReqDto"
      responses:
        200:
          description: 成功
          schema:
            type: array
            items:
              $ref: "apis.yml#/definitions/HospitalVo"

  /hospital-areas/stop-schedule/data-map:
    get:
      tags:
        - RpcHospitalArea
      summary: 停止院区排班数据映射
      description: 停止院区排班数据映射
      operationId: stopScheduleDataMap
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/RpcStopScheduleDataMap"

  /hospitals-areas/batch/get-by-id:
    post:
      tags:
        - RpcHospitalArea
      summary: 批量获取院区详情
      description: 批量获取院区详情
      operationId: batchGetHospitalAreaDetail
      parameters:
        - name: request
          in: body
          schema:
            $ref: "#/definitions/RpcBatchGetByIdReqDto"
      responses:
        200:
          description: 成功
          schema:
            type: array
            items:
              $ref: "apis.yml#/definitions/HospitalAreaVo"
  /departments/batch/get-by-id:
    post:
      tags:
        - RpcDepartment
      summary: 批量获取科室详情
      description: 批量获取科室详情
      operationId: batchGetDepartmentDetail
      parameters:
        - name: request
          in: body
          schema:
            $ref: "#/definitions/RpcBatchGetByIdReqDto"
      responses:
        200:
          description: 成功
          schema:
            type: array
            items:
              $ref: "apis.yml#/definitions/DepartmentVo"
  /doctors/batch/get-by-id:
    post:
      tags:
        - RpcDoctor
      summary: 批量获取医生详情
      description: 批量获取医生详情
      operationId: batchGetDoctorDetail
      parameters:
        - name: request
          in: body
          schema:
            $ref: "#/definitions/RpcBatchGetByIdReqDto"
      responses:
        200:
          description: 成功
          schema:
            type: array
            items:
              $ref: "apis.yml#/definitions/DoctorDetailVo"
  /doctors/batch/by-area-id:
    get:
      tags:
        - RpcDoctor
      summary: 根据院区id批量获取医生详情
      description: 根据院区id批量获取医生详情
      operationId: batchGetDoctorDetailByAreaId
      parameters:
        - name: area_id
          in: query
          description: 院区id
          required: true
          type: integer
          format: int64
        - name: department_id
          in: query
          description: 科室id
          required: false
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            type: array
            items:
              $ref: "apis.yml#/definitions/DoctorDetailVo"
  /doctor-id/batch/get-like-name:
    get:
      tags:
        - RpcDoctor
      summary: 批量获取医生id
      description: 批量获取医生id
      operationId: batchGetDoctorIdLikeName
      parameters:
        - name: name
          in: query
          description: 医生姓名
          required: true
          type: string
      responses:
        200:
          description: 成功
          schema:
            type: array
            items:
              type: integer
              format: int64
  /recommend-config/page:
    post:
      tags:
        - RpcRecommendConfig
      summary: 分页查询推荐配置
      description: 分页查询推荐配置
      operationId: getRecommendConfigPage
      produces:
        - application/json
      parameters:
        - name: request
          in: body
          description: getRecommendConfigPageReqDto
          required: true
          schema:
            $ref: 'apis.yml#/definitions/GetRecommendConfigPageReqDto'
      responses:
        200:
          description: successful operation
          schema:
            $ref: 'apis.yml#/definitions/RecommendConfigPageVo'
        404:
          description: Not found
definitions:
  BaseResp:
    type: object
    properties:
      message:
        type: string
    title: BaseResp
  GetTenantAllUserIdsVo:
    type: object
    properties:
      list:
        type: array
        items:
          $ref: "apis.yml#/definitions/TenantUserVo"
  RpcUserBoundRolesQueryVo:
    type: object
    properties:
      roles:
        type: array
        items:
          $ref: "apis.yml#/definitions/RoleVo"
  RpcUserBatchProcessRolesReqDto:
    type: object
    properties:
      list:
        type: array
        items:
          $ref: "#/definitions/RpcUserBatchProcessRolesItem"
  RpcUserBatchProcessRolesItem:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
      bind_roles:
        type: array
        items:
          type: integer
          format: int64
      unbind_roles:
        type: array
        items:
          type: integer
          format: int64

  RpcBatchGetByIdReqDto:
    type: object
    properties:
      ids:
        type: array
        items:
          type: integer
          format: int64

  RpcSettlementRuleVo:
    type: object
    properties:
      id:
        type: integer
        description: ID
        format: int64
      name:
        type: string
        description: 规则名称
      type:
        type: integer
        description: 规则类型：0,按比例;1,按金额;
      rate:
        type: number
        description: 分账百分比
      amount:
        type: number
        description: 分账金额
      lower_limit:
        type: number
        description: 最大分账金额
      upper_limit:
        type: number
        description: 最小分账金额
      remark:
        type: string
        description: 备注
      status:
        type: integer
        description: 状态：0,启用;1,禁用;2,删除;
      create_time:
        type: string
        description: 新增时间
        format: date-time
      update_time:
        type: string
        description: 更新时间
        format: date-time

  RpcWechatPayServiceProviderSecondMerchantVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
      tenant_payment_id:
        type: integer
        description: 租户支付配置ID
        format: int64
      status:
        type: integer
        description: 状态:0,资料未全部上传;1,未提交申请;2,资料校验中;3,等待账户验证;4,审核中;5,已驳回;6,待签约;7,完成;8,已冻结;9,已作废
      remark:
        type: string
        description: 备注

  RpcStopScheduleDataMap:
    type: object
    properties:
      map:
        type: object
        additionalProperties:
          type: object
          properties:
            start_date:
              type: string
              format: date-time
            end_date:
              type: string
              format: date-time

