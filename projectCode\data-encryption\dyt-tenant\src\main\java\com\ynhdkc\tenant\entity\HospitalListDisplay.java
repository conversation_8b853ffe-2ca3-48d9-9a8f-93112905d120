package com.ynhdkc.tenant.entity;

import backend.common.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Table;

/**
 * <p>
 * 医院显示配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@Getter
@Setter
@Accessors(chain = true)
@Table(name = "t_hospital_list_display")
@ApiModel(value = "HospitalListDisplay对象", description = "医院显示配置")
public class HospitalListDisplay extends BaseEntity {

    private Long hospitalId;

    private String hospitalCode;

    @ApiModelProperty("医院名称颜色类型")
    private Integer hospitalNameColorType;

    @ApiModelProperty("医院名称16进制颜色码")
    private String hospitalNameHexColorCode;

    @ApiModelProperty("logo类型 0-默认 1-自定义")
    private Integer logoType;

    @ApiModelProperty("是否显示角标 0-不显示 1-显示")
    private Integer displayCornerMark;

    @ApiModelProperty("角标样式")
    private String cornerMarkStyle;

    @ApiModelProperty("是否显示标签 0-不显示 1-显示")
    private Integer displayTag;

    @ApiModelProperty("标签")
    private String tags;

    @ApiModelProperty("推荐医生id")
    private Long recommendedDoctorId;

}
