package backend.common.entity.dto.hisgateway.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum ProcessCode {

	REGIST("010101", "挂号"), DIAGNOSIS("010102", "诊断"), MEDICINE("010103", "取药"), INSPECT("010104", "检查"),
	PAY("010105", "收费"), PRESCRIBE("010106", "开方"), SURGERY("010107", "手术"), OTHER("000000", "其他"),
	UNKNOWN("-999999", "未知的诊疗环节");

	private final String code;

	private final String name;

	ProcessCode(String code, String name) {
		this.code = code;
		this.name = name;
	}

	@JsonCreator
	public static ProcessCode getFromCode(String code) {
		for (ProcessCode t : ProcessCode.values()) {
			if (t.getCode().equals(code)) {
				return t;
			}
		}
		return UNKNOWN;
	}

	public static ProcessCode getFromName(String name) {
		for (ProcessCode t : ProcessCode.values()) {
			if (t.getName().equals(name)) {
				return t;
			}
		}
		return UNKNOWN;
	}

	@JsonValue
	public String getCode() {
		return code;
	}

	public String getName() {
		return name;
	}

}
