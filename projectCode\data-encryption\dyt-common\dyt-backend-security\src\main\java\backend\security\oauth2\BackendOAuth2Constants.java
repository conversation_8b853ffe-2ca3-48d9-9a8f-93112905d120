package backend.security.oauth2;

public interface BackendOAuth2Constants {
    String SCOPE_RPC = "rpc";
    String SCOPE_GLOBAL = "global";
    String SCOPE_TENANT = "tenant";
    String SCOPE_USER = "user";
    String NICKNAME = "nickname";

    String AUTHORITY_SCOPE_GLOBAL = "SCOPE_global";
    String AUTHORITY_SCOPE_TENANT = "SCOPE_tenant";
    String AUTHORITY_SCOPE_RPC = "SCOPE_rpc";
    String AUTHORITY_SCOPE_USER = "SCOPE_user";

    String PREFIX_SCOPE = "SCOPE_";
    String PREFIX_AUTH = "AUTH_";

    String AUTHORITIES_CLAIM_NAME = "auth";
    String SCOPE_CLAIM_NAME = "scope";
    String  SESSION_ID = "session_id";
}
