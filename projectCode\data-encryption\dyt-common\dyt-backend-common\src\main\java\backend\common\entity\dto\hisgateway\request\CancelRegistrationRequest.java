package backend.common.entity.dto.hisgateway.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class CancelRegistrationRequest {

    @JsonProperty("visiting_serial_number")
    private String visitingSerialNumber;

    @JsonProperty("medical_record_number")
    private String medicalRecordNumber;

    @JsonProperty("medical_card_expansion")
    private String medicalCardExpansion;

    @JsonProperty("schedule_id")
    private String scheduleId;

    @JsonProperty("appointment_serial_number")
    private String appointmentSerialNumber;

    @JsonProperty("lock_registration_number")
    private String lockRegistrationNumber;

    @JsonProperty("order_number")
    private String orderNumber;

    @JsonProperty("pay_channel_order_number")
    private String payChannelOrderNumber;

    @JsonProperty("trade_serial_number")
    private String tradeSerialNumber;

    private String amt;

    @JsonProperty("hospital_code")
    private String hospitalCode;

    @JsonProperty("visiting_date")
    private Long visitingDate;

    @JsonProperty("start_time")
    private Long startTime;

    @JsonProperty("end_time")
    private Long endTime;

    @JsonProperty("start_time_text")
    private String startTimeText;

    @JsonProperty("end_time_text")
    private String endTimeText;

    // 科室编码
    @JsonProperty("department_code")
    private String departmentCode;

    @JsonProperty("doctor_code")
    private String doctorCode;
}
