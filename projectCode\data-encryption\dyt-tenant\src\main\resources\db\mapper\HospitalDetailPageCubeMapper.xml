<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ynhdkc.tenant.dao.mapper.HospitalDetailPageCubeMapper">
    <update id="updateModuleIdByModuleId">
        update t_hospital_detail_page_cube
        set cube_module_id = #{newModuleId}
        where hospital_area_id = #{hospitalAreaId}
          and cube_module_id = #{moduleId}
    </update>
</mapper>