package backend.common.entity.dto.hisgateway.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class DoctorDataSyncResponse {

    @JsonProperty("department_name")
    private String departmentName;

    @JsonProperty("department_code")
    private String departmentCode;

    private String name;

    private String code;

    private String type;

    private String level;

    private String avatar;

    @JsonProperty("good_at")
    private String goodAt;

    private String information;

}
