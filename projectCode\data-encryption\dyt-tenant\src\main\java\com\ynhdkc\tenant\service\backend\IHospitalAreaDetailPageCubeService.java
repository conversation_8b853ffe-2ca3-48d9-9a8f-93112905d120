package com.ynhdkc.tenant.service.backend;

import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.entity.detailpage.HospitalAreaDetailPageCube;
import com.ynhdkc.tenant.model.*;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/24 19:15:45
 */
public interface IHospitalAreaDetailPageCubeService {
    HospitalAreaDetailPageCubeVo createHospitalAreaDetailPageCube(CreateHospitalAreaDetailPageCubeReqDto dto);

    BaseOperationResponse deleteHospitalAreaDetailPageCube(Long id);

    HospitalAreaDetailPageCubeVo getHospitalAreaDetailPageCube(Long id);

    HospitalAreaDetailPageCubePageVo searchHospitalAreaDetailPageCube(SearchHospitalAreaDetailPageCubeReqDto searchHospitalDetailPageCubeReqDto);

    HospitalAreaDetailPageCubeVo updateHospitalAreaDetailPageCube(Long id, UpdateHospitalAreaDetailPageCubeReqDto dto);

    List<HospitalAreaDetailPageCubeVo> getCubeVosByIds(List<Long> cubeModuleIds);

    List<HospitalAreaDetailPageCubeVo> getCubeVosByCubeModuleId(Long cubeModuleId);

    static HospitalAreaDetailPageCubeVo toVo(HospitalAreaDetailPageCube entity) {
        HospitalAreaDetailPageCubeVo vo = new HospitalAreaDetailPageCubeVo();
        vo.setId(entity.getId());
        vo.setTenantId(entity.getTenantId());
        vo.setHospitalId(entity.getHospitalId());
        vo.setHospitalAreaId(entity.getHospitalAreaId());
        vo.setCubeModuleId(entity.getCubeModuleId());
        vo.setTitle(entity.getTitle());
        vo.setPicture(entity.getPicture());
        vo.setUrl(entity.getUrl());
        vo.setSort(entity.getSort());
        if (StringUtils.hasText(entity.getChannels())) {
            vo.setChannels((Arrays.stream(entity.getChannels().split(",")).
                    map(Integer::parseInt)
                    .collect(Collectors.toList()))
            );
        }
        vo.setStatus(entity.getStatus());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdateTime(entity.getUpdateTime());
        return vo;
    }

    default HospitalAreaDetailPageCube toEntity(CreateHospitalAreaDetailPageCubeReqDto dto) {
        HospitalAreaDetailPageCube entity = new HospitalAreaDetailPageCube();
        entity.setTenantId(dto.getTenantId());
        entity.setHospitalId(dto.getHospitalId());
        entity.setHospitalAreaId(dto.getHospitalAreaId());
        entity.setCubeModuleId(dto.getCubeModuleId());
        entity.setTitle(dto.getTitle());
        entity.setPicture(dto.getPicture());
        entity.setUrl(dto.getUrl());
        entity.setSort(dto.getSort());
        if (!CollectionUtils.isEmpty(dto.getChannels())) {
            entity.setChannels(dto.getChannels().stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        entity.setStatus(dto.getStatus());
        return entity;
    }

    default void toEntity(UpdateHospitalAreaDetailPageCubeReqDto dto, HospitalAreaDetailPageCube entity) {
        entity.setTenantId(dto.getTenantId());
        entity.setHospitalId(dto.getHospitalId());
        entity.setHospitalAreaId(dto.getHospitalAreaId());
        entity.setCubeModuleId(dto.getCubeModuleId());
        entity.setTitle(dto.getTitle());
        entity.setPicture(dto.getPicture());
        entity.setUrl(dto.getUrl());
        entity.setSort(dto.getSort());
        if (!CollectionUtils.isEmpty(dto.getChannels())) {
            entity.setChannels(dto.getChannels().stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        entity.setStatus(dto.getStatus());
    }

}
