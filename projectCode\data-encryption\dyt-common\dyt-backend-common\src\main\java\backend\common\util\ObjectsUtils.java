package backend.common.util;

import java.util.Collection;
import java.util.Map;

public class ObjectsUtils {

    private ObjectsUtils() {
    }

    public static boolean isEmpty(Collection<?> records) {
        return records == null || records.isEmpty();
    }

    public static boolean isEmpty(String value) {
        return "".equals(value) || value == null;
    }

    public static boolean isEmpty(Map<?, ?> value) {
        return value == null || value.isEmpty();
    }

    public static boolean isEmpty(Object value) {
        return value == null;
    }

    public static boolean isEmpty(Object[] value) {
        return value == null || value.length == 0;
    }

    public static Integer parseInt(Object value) {
        return (int) value;
    }

    public static String getStringValue(Map<?, ?> parameters, String name) {
        Object value = parameters.get(name);
        if (value == null) {
            return null;
        }
        return String.valueOf(value);
    }

    public static int getIntegerValue(Map<?, ?> parameters, String name) {
        Object value = parameters.get(name);
        if (value == null) {
            return 0;
        }
        return Integer.parseInt(String.valueOf(value));
    }

    public static long getLongValue(Map<String, Object> parameters, String name) {
        Object value = parameters.get(name);
        if (value == null) {
            return 0;
        }
        return Long.parseLong(String.valueOf(value));
    }

    public static double getDoubleValue(Map<String, Object> parameters, String name) {
        Object value = parameters.get(name);
        if (value == null) {
            return 0;
        }
        return Double.parseDouble(String.valueOf(value));
    }

    public static boolean getBooleanValue(Map<String, Object> parameters, String name) {
        Object value = parameters.get(name);
        if (value == null) {
            return false;
        }
        return Boolean.parseBoolean(String.valueOf(value));
    }
}
