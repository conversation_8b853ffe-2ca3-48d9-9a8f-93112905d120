create table t_address
(
    id          bigint auto_increment comment '全局唯一标识'
        primary key,
    country     varchar(30) default '中国'               null comment '所在国家',
    province    varchar(30)                              null comment '所在省',
    city        varchar(30)                              null comment '所在城市',
    county      varchar(30)                              null comment '所在区/县',
    detail      varchar(1000)                             null comment '详细地址',
    longitude   decimal(9, 6)                            null comment '经度',
    latitude    decimal(9, 6)                            null comment '纬度',
    create_time datetime(3) default CURRENT_TIMESTAMP(3) not null,
    update_time datetime(3)                              null on update CURRENT_TIMESTAMP(3)
)
    comment 'Address';

insert into t_address
    (id, country, province, city, county, detail)
VALUES (1, '中国', '云南省', '昆明市', '西山区', '云南省昆明市西山区金碧路157号'),
       (2, '中国', '云南省', '昆明市', '安宁市', '云南省第一人民医院新昆华医院位于安宁市太平镇'),
       (3, '中国', '云南省', '昆明市', '五华区', '云南省昆明市五华区青年路176号'),
       (4, '中国', '云南省', '昆明市', '五华区', '云南省昆明市五华区青年路176号'),
       (5, '中国', '云南省', '昆明市', '五华区', '云南省昆明市五华区青年路176号');