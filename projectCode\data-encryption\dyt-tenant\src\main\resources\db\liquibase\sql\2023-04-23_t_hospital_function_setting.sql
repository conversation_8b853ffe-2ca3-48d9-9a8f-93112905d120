create table t_hospital_function_setting
(
    id                bigint auto_increment comment '全局唯一标识'
        primary key,
    function_id       bigint                                   null comment '功能 ID',
    tenant_id         bigint                                   not null comment '租户 ID',
    hospital_id       bigint                                   not null comment '医院 ID',
    hospital_area_id  bigint                                   not null comment '院区 ID',
    hospital_code     varchar(20)                              null comment '医院编码',
    name              varchar(50)                              not null comment '功能名称',
    type              varchar(50)                              not null comment '功能类型',
    logo              varchar(255)                             null comment '功能图标',
    tab_display_style varchar(100)                             null comment 'tab展示样式',
    sort              int         default 255                  not null comment '排序',
    status            int         default 0                    not null comment '状态 0:开启 1:维护 2:关闭',
    create_time       datetime(3) default CURRENT_TIMESTAMP(3) not null,
    update_time       datetime(3)                              null on update CURRENT_TIMESTAMP(3),
    constraint uidx_limit_function_name
        unique (tenant_id, hospital_id, hospital_area_id, name)
)
    comment '医院功能设置';

insert into t_hospital_function_setting (tenant_id, hospital_id, hospital_area_id, name, type)
values (1, 1, 2, '预约挂号', 'appointment_registration'),
       (1, 1, 3, '预约挂号', 'appointment_registration'),
       (2, 4, 5, '预约挂号', 'appointment_registration'),
       (2, 4, 6, '预约挂号', 'appointment_registration'),
       (2, 4, 7, '预约挂号', 'appointment_registration');

insert into t_hospital_function_setting (tenant_id, hospital_id, hospital_area_id, name, type)
values (1, 1, 2, '门诊缴费', 'diagnosis_payment'),
       (1, 1, 3, '门诊缴费', 'diagnosis_payment'),
       (2, 4, 5, '门诊缴费', 'diagnosis_payment'),
       (2, 4, 6, '门诊缴费', 'diagnosis_payment'),
       (2, 4, 7, '门诊缴费', 'diagnosis_payment');

insert into t_hospital_function_setting (tenant_id, hospital_id, hospital_area_id, name, type)
values (1, 1, 2, '报告查询', 'patient_report'),
       (1, 1, 3, '报告查询', 'patient_report'),
       (2, 4, 5, '报告查询', 'patient_report'),
       (2, 4, 6, '报告查询', 'patient_report'),
       (2, 4, 7, '报告查询', 'patient_report');

insert into t_hospital_function_setting (tenant_id, hospital_id, hospital_area_id, name, type)
values (1, 1, 2, '住院', 'hospitalization'),
       (1, 1, 3, '住院', 'hospitalization'),
       (2, 4, 5, '住院', 'hospitalization'),
       (2, 4, 6, '住院', 'hospitalization'),
       (2, 4, 7, '住院', 'hospitalization');

insert into t_hospital_function_setting (tenant_id, hospital_id, hospital_area_id, name, type)
values (1, 1, 2, '就诊卡', 'patient_card'),
       (1, 1, 3, '就诊卡', 'patient_card'),
       (2, 4, 5, '就诊卡', 'patient_card'),
       (2, 4, 6, '就诊卡', 'patient_card'),
       (2, 4, 7, '就诊卡', 'patient_card');

insert into t_hospital_function_setting (tenant_id, hospital_id, hospital_area_id, name, type)
values (1, 1, 2, '排队签到', 'queue_sign_in'),
       (1, 1, 3, '排队签到', 'queue_sign_in'),
       (2, 4, 5, '排队签到', 'queue_sign_in'),
       (2, 4, 6, '排队签到', 'queue_sign_in'),
       (2, 4, 7, '排队签到', 'queue_sign_in');

insert into t_hospital_function_setting (tenant_id, hospital_id, hospital_area_id, name, type)
values (1, 1, 2, '候诊查询', 'waiting_inquiry'),
       (1, 1, 3, '候诊查询', 'waiting_inquiry'),
       (2, 4, 5, '候诊查询', 'waiting_inquiry'),
       (2, 4, 6, '候诊查询', 'waiting_inquiry'),
       (2, 4, 7, '候诊查询', 'waiting_inquiry');

insert into t_hospital_function_setting (tenant_id, hospital_id, hospital_area_id, name, type)
values (1, 1, 2, '陪诊', 'medical_escort'),
       (1, 1, 3, '陪诊', 'medical_escort'),
       (2, 4, 5, '陪诊', 'medical_escort'),
       (2, 4, 6, '陪诊', 'medical_escort'),
       (2, 4, 7, '陪诊', 'medical_escort');

insert into t_hospital_function_setting (tenant_id, hospital_id, hospital_area_id, name, type)
values (1, 1, 2, '门诊病历查询', 'outpatient_medical_record_inquiry'),
       (1, 1, 3, '门诊病历查询', 'outpatient_medical_record_inquiry'),
       (2, 4, 5, '门诊病历查询', 'outpatient_medical_record_inquiry'),
       (2, 4, 6, '门诊病历查询', 'outpatient_medical_record_inquiry'),
       (2, 4, 7, '门诊病历查询', 'outpatient_medical_record_inquiry');

insert into t_hospital_function_setting (tenant_id, hospital_id, hospital_area_id, name, type)
values (1, 1, 2, '特需门诊', 'special_needs_clinic'),
       (1, 1, 3, '特需门诊', 'special_needs_clinic'),
       (2, 4, 5, '特需门诊', 'special_needs_clinic'),
       (2, 4, 6, '特需门诊', 'special_needs_clinic'),
       (2, 4, 7, '特需门诊', 'special_needs_clinic');

insert into t_hospital_function_setting (tenant_id, hospital_id, hospital_area_id, name, type)
values (1, 1, 2, '多学科门诊', 'multidisciplinary_clinic'),
       (1, 1, 3, '多学科门诊', 'multidisciplinary_clinic'),
       (2, 4, 5, '多学科门诊', 'multidisciplinary_clinic'),
       (2, 4, 6, '多学科门诊', 'multidisciplinary_clinic'),
       (2, 4, 7, '多学科门诊', 'multidisciplinary_clinic');
