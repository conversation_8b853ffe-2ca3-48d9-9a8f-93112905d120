package com.ynhdkc.tenant.tool;

import com.ynhdkc.tenant.dao.DepartmentQuery;
import com.ynhdkc.tenant.entity.*;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.tool.convert.HospitalAreaPositionConverter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/9/14 14:47
 */
@Component
@RequiredArgsConstructor
public class HospitalAreaPositionTreeUtil {
    private final DepartmentQuery departmentQuery;

    public List<BuildingPositionVo> buildTree(List<Building> buildingList, List<Floor> floorList, List<Area> areaList, List<HospitalAreaPosition> hospitalAreaPositionList) {
        Map<Long, BuildingPositionVo> buildingPositionVoMap = buildingList.stream()
                .map(building -> {
                    BuildingPositionVo buildingPositionVo = new BuildingPositionVo();
                    buildingPositionVo.setBuildingId(building.getId());
                    buildingPositionVo.setBuildingName(building.getName());
                    buildingPositionVo.setStatus(building.getStatus());
                    buildingPositionVo.setSort(building.getSort());
                    buildingPositionVo.setCreateTime(building.getCreateTime());
                    buildingPositionVo.setUpdateTime(building.getUpdateTime());
                    buildingPositionVo.setChildren(new ArrayList<>());
                    buildingPositionVo.setLocationList(new ArrayList<>());
                    return buildingPositionVo;
                })
                .collect(Collectors.toMap(BuildingPositionVo::getBuildingId, a -> a, (a, b) -> a));

        Map<Long, FloorPositionVo> floorPositionVoMap = new HashMap<>();
        for (Floor floor : floorList) {
            BuildingPositionVo buildingPositionVo = buildingPositionVoMap.get(floor.getBuildingId());
            if (buildingPositionVo == null) {
                continue;
            }
            FloorPositionVo floorPositionVo = new FloorPositionVo();
            floorPositionVo.setBuildingId(floor.getBuildingId());
            floorPositionVo.setBuildingName(buildingPositionVo.getBuildingName());
            floorPositionVo.setFloorId(floor.getId());
            floorPositionVo.setFloorName(floor.getName());
            floorPositionVo.setStatus(floor.getStatus());
            floorPositionVo.setSort(floor.getSort());
            floorPositionVo.setCreateTime(floor.getCreateTime());
            floorPositionVo.setUpdateTime(floor.getUpdateTime());
            floorPositionVo.setChildren(new ArrayList<>());
            floorPositionVo.setLocationList(new ArrayList<>());

            buildingPositionVo.getChildren().add(floorPositionVo);
            floorPositionVoMap.put(floor.getId(), floorPositionVo);
        }

        Map<Long, AreaPositionVo> areaPositionVoHashMap = new HashMap<>();
        for (Area area : areaList) {
            FloorPositionVo floorPositionVo = floorPositionVoMap.get(area.getFloorId());
            if (floorPositionVo == null) {
                continue;
            }

            AreaPositionVo areaPositionVo = new AreaPositionVo();
            areaPositionVo.setBuildingId(floorPositionVo.getBuildingId());
            areaPositionVo.setBuildingName(floorPositionVo.getBuildingName());
            areaPositionVo.setFloorId(area.getFloorId());
            areaPositionVo.setFloorName(floorPositionVo.getFloorName());
            areaPositionVo.setAreaId(area.getId());
            areaPositionVo.setAreaName(area.getName());
            areaPositionVo.setStatus(area.getStatus());
            areaPositionVo.setSort(area.getSort());
            areaPositionVo.setCreateTime(area.getCreateTime());
            areaPositionVo.setUpdateTime(area.getUpdateTime());
            areaPositionVo.setLocationList(new ArrayList<>());

            floorPositionVo.getChildren().add(areaPositionVo);
            areaPositionVoHashMap.put(area.getId(), areaPositionVo);
        }

        hospitalAreaPositionList.forEach(hospitalAreaPosition -> {
            if (null != hospitalAreaPosition.getAreaId()) {
                AreaPositionVo areaPositionVo = areaPositionVoHashMap.get(hospitalAreaPosition.getAreaId());
                if (null == areaPositionVo) {
                    return;
                }
                HospitalAreaPositionVo hospitalAreaPositionVo = HospitalAreaPositionConverter.toVo(hospitalAreaPosition);

                areaPositionVo.getLocationList().add(hospitalAreaPositionVo);
                return;
            }

            if (null != hospitalAreaPosition.getFloorId()) {
                FloorPositionVo floorPositionVo = floorPositionVoMap.get(hospitalAreaPosition.getFloorId());
                if (null == floorPositionVo) {
                    return;
                }
                HospitalAreaPositionVo hospitalAreaPositionVo = HospitalAreaPositionConverter.toVo(hospitalAreaPosition);

                floorPositionVo.getLocationList().add(hospitalAreaPositionVo);
                return;
            }

            if (null != hospitalAreaPosition.getBuildingId()) {
                BuildingPositionVo buildingPositionVo = buildingPositionVoMap.get(hospitalAreaPosition.getBuildingId());
                if (null == buildingPositionVo) {
                    return;
                }
                HospitalAreaPositionVo hospitalAreaPositionVo = HospitalAreaPositionConverter.toVo(hospitalAreaPosition);

                buildingPositionVo.getLocationList().add(hospitalAreaPositionVo);
            }
        });

        return buildingPositionVoMap.values()
                .stream()
                .peek(building -> {
                    building.setLocationList(sortHospitalAreaPositionVo(building.getLocationList()));

                    List<FloorPositionVo> sortedFloorVos = building.getChildren().stream()
                            .sorted(Comparator.comparing(FloorPositionVo::getSort).reversed())
                            .collect(Collectors.toList());
                    building.setChildren(sortedFloorVos);

                    building.getChildren().forEach(floor -> {
                        floor.setLocationList(sortHospitalAreaPositionVo(floor.getLocationList()));

                        List<AreaPositionVo> sortedAreaVos = floor.getChildren().stream()
                                .sorted(Comparator.comparing(AreaPositionVo::getSort).reversed())
                                .collect(Collectors.toList());
                        floor.setChildren(sortedAreaVos);

                        floor.getChildren().forEach(area -> area.setLocationList(sortHospitalAreaPositionVo(area.getLocationList())));
                    });
                })
                .sorted(Comparator.comparing(BuildingPositionVo::getSort).reversed())
                .collect(Collectors.toList());
    }

    public List<CustomerHospitalAreaBuildingVo> buildCustomerTree(List<Building> buildingList, List<Floor> floorList, List<Area> areaList, List<HospitalAreaPosition> hospitalAreaPositionList) {
        Map<Long, CustomerHospitalAreaBuildingVo> buildingPositionVoMap = buildingList.stream()
                .filter(building -> building.getStatus() == 0)
                .map(building -> {
                    CustomerHospitalAreaBuildingVo buildingPositionVo = new CustomerHospitalAreaBuildingVo();
                    buildingPositionVo.setId(building.getId());
                    buildingPositionVo.setName(building.getName());
                    buildingPositionVo.setSort(building.getSort());
                    buildingPositionVo.setChildren(new ArrayList<>());
                    buildingPositionVo.setLocationList(new ArrayList<>());
                    return buildingPositionVo;
                })
                .collect(Collectors.toMap(CustomerHospitalAreaBuildingVo::getId, a -> a, (a, b) -> a));

        Map<Long, CustomerHospitalAreaFloorVo> floorPositionVoMap = new HashMap<>();
        for (Floor floor : floorList) {
            CustomerHospitalAreaBuildingVo buildingPositionVo = buildingPositionVoMap.get(floor.getBuildingId());
            if (buildingPositionVo == null || floor.getStatus() == 1) {
                continue;
            }
            CustomerHospitalAreaFloorVo floorPositionVo = new CustomerHospitalAreaFloorVo();
            floorPositionVo.setId(floor.getId());
            floorPositionVo.setName(floor.getName());
            floorPositionVo.setSort(floor.getSort());
            floorPositionVo.setChildren(new ArrayList<>());
            floorPositionVo.setLocationList(new ArrayList<>());

            buildingPositionVo.getChildren().add(floorPositionVo);
            floorPositionVoMap.put(floor.getId(), floorPositionVo);
        }

        Map<Long, CustomerHospitalAreaAreaVo> areaPositionVoHashMap = new HashMap<>();
        for (Area area : areaList) {
            CustomerHospitalAreaFloorVo floorPositionVo = floorPositionVoMap.get(area.getFloorId());
            if (floorPositionVo == null || area.getStatus() == 1) {
                continue;
            }

            CustomerHospitalAreaAreaVo areaPositionVo = new CustomerHospitalAreaAreaVo();
            areaPositionVo.setId(area.getId());
            areaPositionVo.setName(area.getName());
            areaPositionVo.setSort(area.getSort());
            areaPositionVo.setLocationList(new ArrayList<>());

            floorPositionVo.getChildren().add(areaPositionVo);
            areaPositionVoHashMap.put(area.getId(), areaPositionVo);
        }

        Set<Long> departmentIds = hospitalAreaPositionList.stream()
                .map(HospitalAreaPosition::getDepartmentId)
                .collect(Collectors.toSet());

        Set<Long> enabledDepartmentIds = departmentQuery.pageQueryDepartment(new DepartmentQuery.DepartmentQueryOption(1, Integer.MAX_VALUE)
                        .setIncludingIds(departmentIds))
                .stream()
                .filter(Department::getEnabled)
                .map(Department::getId)
                .collect(Collectors.toSet());

        hospitalAreaPositionList.forEach(hospitalAreaPosition -> {
            if (hospitalAreaPosition.getType() == 0 && !enabledDepartmentIds.contains(hospitalAreaPosition.getDepartmentId())) {
                return;
            }
            if (null != hospitalAreaPosition.getAreaId()) {
                CustomerHospitalAreaAreaVo areaPositionVo = areaPositionVoHashMap.get(hospitalAreaPosition.getAreaId());
                if (null == areaPositionVo) {
                    return;
                }
                CustomerHospitalAreaLocationVo hospitalAreaLocationVo = toCustomerHospitalAreaLocationVo(hospitalAreaPosition);

                areaPositionVo.getLocationList().add(hospitalAreaLocationVo);
                return;
            }

            if (null != hospitalAreaPosition.getFloorId()) {
                CustomerHospitalAreaFloorVo floorPositionVo = floorPositionVoMap.get(hospitalAreaPosition.getFloorId());
                if (null == floorPositionVo) {
                    return;
                }
                CustomerHospitalAreaLocationVo hospitalAreaLocationVo = toCustomerHospitalAreaLocationVo(hospitalAreaPosition);

                floorPositionVo.getLocationList().add(hospitalAreaLocationVo);
                return;
            }

            if (null != hospitalAreaPosition.getBuildingId()) {
                CustomerHospitalAreaBuildingVo buildingPositionVo = buildingPositionVoMap.get(hospitalAreaPosition.getBuildingId());
                if (null == buildingPositionVo) {
                    return;
                }
                CustomerHospitalAreaLocationVo hospitalAreaLocationVo = toCustomerHospitalAreaLocationVo(hospitalAreaPosition);

                buildingPositionVo.getLocationList().add(hospitalAreaLocationVo);
            }
        });


        return buildingPositionVoMap.values()
                .stream()
                .peek(building -> {
                    building.setLocationList(sortCustomerHospitalAreaLocationVo(building.getLocationList()));

                    List<CustomerHospitalAreaFloorVo> sortedFloorVos = building.getChildren().stream()
                            .sorted(Comparator.comparing(CustomerHospitalAreaFloorVo::getSort).reversed())
                            .collect(Collectors.toList());
                    building.setChildren(sortedFloorVos);

                    building.getChildren().forEach(floor -> {
                        floor.setLocationList(sortCustomerHospitalAreaLocationVo(floor.getLocationList()));

                        List<CustomerHospitalAreaAreaVo> sortedAreaVos = floor.getChildren().stream()
                                .sorted(Comparator.comparing(CustomerHospitalAreaAreaVo::getSort).reversed())
                                .collect(Collectors.toList());
                        floor.setChildren(sortedAreaVos);

                        floor.getChildren().forEach(area -> area.setLocationList(sortCustomerHospitalAreaLocationVo(area.getLocationList())));
                    });
                })
                .sorted(Comparator.comparing(CustomerHospitalAreaBuildingVo::getSort).reversed())
                .collect(Collectors.toList());
    }

    private static List<HospitalAreaPositionVo> sortHospitalAreaPositionVo(List<HospitalAreaPositionVo> vos) {
        if (CollectionUtils.isEmpty(vos)) {
            return vos;
        }
        return vos.stream()
                .sorted(Comparator.comparing(HospitalAreaPositionVo::getSort).reversed())
                .collect(Collectors.toList());
    }

    private static List<CustomerHospitalAreaLocationVo> sortCustomerHospitalAreaLocationVo(List<CustomerHospitalAreaLocationVo> vos) {
        if (CollectionUtils.isEmpty(vos)) {
            return vos;
        }
        return vos.stream()
                .sorted(Comparator.comparing(CustomerHospitalAreaLocationVo::getSort).reversed())
                .collect(Collectors.toList());
    }

    private static CustomerHospitalAreaLocationVo toCustomerHospitalAreaLocationVo(HospitalAreaPosition entity) {
        CustomerHospitalAreaLocationVo vo = new CustomerHospitalAreaLocationVo();
        vo.setId(entity.getId());
        vo.setType(entity.getType());
        vo.setDepartmentId(entity.getDepartmentId());
        vo.setName(entity.getName());
        vo.setSort(entity.getSort());
        return vo;
    }
}
