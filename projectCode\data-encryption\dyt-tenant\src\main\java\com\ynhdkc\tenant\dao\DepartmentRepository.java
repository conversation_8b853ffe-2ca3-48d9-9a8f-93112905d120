package com.ynhdkc.tenant.dao;

import com.ynhdkc.tenant.entity.Department;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-29 21:47
 */
public interface DepartmentRepository {
    Long create(Department department);

    void update(Department department);

    void delete(Long departmentId);

    Optional<Department> queryById(Long departmentId);

    List<Department> listByHospitalCodeAndDepartmentCodeIn(List<String> hospitalCodes, List<String> departmentCodes);

    Department queryByHospitalCodeAndThrdpartDepCode(String hospitalCode, String departmentCode);

    void disableAll(String hospitalCode);

    void disableBy(Long id);
}
