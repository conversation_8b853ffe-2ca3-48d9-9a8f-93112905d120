package com.ynhdkc.tenant.api.rpc;

import com.ynhdkc.tenant.handler.RpcDepartmentApi;
import com.ynhdkc.tenant.model.DepartmentVo;
import com.ynhdkc.tenant.model.RpcBatchGetByIdReqDto;
import com.ynhdkc.tenant.service.backend.DepartmentService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-23 10:48
 */
@Api(tags = "RpcDepartment")
@RestController
public class RpcDepartmentController implements RpcDepartmentApi {
    @Autowired
    private DepartmentService departmentService;

    @Override
    public ResponseEntity<List<DepartmentVo>> batchGetDepartmentDetail(RpcBatchGetByIdReqDto request) {
        return ResponseEntity.ok(departmentService.rpcBatchGetDetail(request));
    }

    @Override
    public ResponseEntity<DepartmentVo> getDepartmentDetail(Long id) {
        return ResponseEntity.ok(departmentService.rpcGetDetail(id));
    }

    @Override
    public ResponseEntity<DepartmentVo> getDepartmentDetailByCode(String hospitalAreaCode, String departmentCode) {
        return ResponseEntity.ok(departmentService.rpcGetDetailByCode(hospitalAreaCode, departmentCode));
    }
}
