package com.ynhdkc.tenant.dao.mapper;

import com.ynhdkc.tenant.entity.UserDoctorSubscription;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

@org.apache.ibatis.annotations.Mapper
public interface UserDoctorSubscriptionMapper extends Mapper<UserDoctorSubscription> {
    @Results(id = "subscriptionMap", value = {
            @Result(property = "hospitalAreaId", column = "hospital_area_id"),
            @Result(property = "hospitalAreaName", column = "hospital_area_name"),
            @Result(property = "hospitalAreaCode", column = "hospital_area_code")
    })
    @Select("select  hospital_area_id,hospital_area_name,hospital_area_code from  t_user_doctor_subscription where  status in(0,1) GROUP BY hospital_area_name,hospital_area_code,hospital_area_id")
    List<UserDoctorSubscription> getSubscriptionHospitalArea();
}
