package com.ynhdkc.tenant.service.backend;

import com.ynhdkc.tenant.entity.HospitalListIndex;
import com.ynhdkc.tenant.model.HospitalListIndexVo;
import com.ynhdkc.tenant.model.HospitalVo;
import com.ynhdkc.tenant.model.SubmitHospitalListIndexReqDto;

import java.util.List;

/**
 * <p>
 * 医院列表序列 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
public interface IHospitalListIndexService {

    List<HospitalListIndexVo> getHospitalListIndex();

    List<HospitalListIndexVo> submitHospitalListIndex(List<SubmitHospitalListIndexReqDto> hospitalListIndexDtoList);

    static HospitalListIndexVo toVo(HospitalListIndex entity) {
        if (entity == null) {
            return null;
        }
        HospitalListIndexVo vo = new HospitalListIndexVo();
        vo.setIndexOrder(entity.getIndexOrder());
        vo.setDescription(entity.getDescription());
        vo.setRuleId(entity.getRuleId());
        vo.setHospitalCount(entity.getHospitalCount());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdateTime(entity.getUpdateTime());
        return vo;
    }

    static HospitalListIndex toEntity(SubmitHospitalListIndexReqDto dto) {
        if (dto == null) {
            return null;
        }
        HospitalListIndex entity = new HospitalListIndex();
        entity.setIndexOrder(dto.getIndexOrder());
        entity.setDescription(dto.getDescription());
        entity.setRuleId(dto.getRuleId());
        entity.setHospitalCount(dto.getHospitalCount());
        return entity;
    }
}
