CREATE TABLE t_hospital_detail_page_tab
(
    id               BIGINT       NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    tenant_id        BIGINT       NOT NULL DEFAULT 0 COMMENT '租户id',
    hospital_id      BIGINT       NOT NULL DEFAULT 0 COMMENT '医院id',
    hospital_area_id bigint       not null comment '院区id',
    hospital_code    varchar(20)  null comment '医院编码',
    title            VARCHAR(20)  NOT NULL DEFAULT '' COMMENT '标题',
    component_type   INT          NOT NULL DEFAULT 0 COMMENT '组件类型',
    recommend_id     bigint       null comment '推荐ID',
    sort             INT          NOT NULL DEFAULT 0 COMMENT '排序',
    channels         varchar(100) NOT NULL DEFAULT '' COMMENT '渠道',
    status           INT          NOT NULL DEFAULT 0 COMMENT '状态：0，启动；1，禁用；',
    create_time      DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time      DATETIME     NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT '医院详情页tab组件表';

create index idx_tenant_id on t_hospital_detail_page_tab (tenant_id);
create index idx_hospital_id on t_hospital_detail_page_tab (hospital_id);
create index idx_status on t_hospital_detail_page_tab (status);

