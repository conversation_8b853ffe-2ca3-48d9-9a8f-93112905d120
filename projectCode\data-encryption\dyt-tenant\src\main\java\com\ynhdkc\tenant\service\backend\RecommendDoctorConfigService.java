package com.ynhdkc.tenant.service.backend;


import com.ynhdkc.tenant.model.*;

import java.util.List;

public interface RecommendDoctorConfigService {

    RecommendDoctorConfigVo addRecommendDoctorConfig(List<RecommendDoctorConfigCreateDto> recommendDoctorConfigCreateDtoList);

    void deleteRecommendDoctorConfig(Long id);

    RecommendDoctorConfigVo getRecommendDoctorConfigById(Long id);

    RecommendDoctorConfigPageVo getRecommendDoctorConfigsPage(RecommendDoctorConfigQueryDto queryDto);

    void updateRecommendDoctorConfig(Long id, RecommendDoctorConfigUpdateDto updateDto);
}