swagger: "2.0"
info:
  title: tenant
  version: "1.0"
  description: 租户中心2025年新增接口
host: localhost:9001
basePath: /apis/v1/tenant/backend
schemes:
  - http
  - https

paths:
  /recommend-doctor-configs:
    post:
      summary: 添加推荐医生配置
      description: 创建新的推荐医生配置
      operationId: addRecommendDoctorConfig
      tags:
        - RecommendDoctorConfig
      parameters:
        - name: body
          in: body
          description: 推荐医生配置对象
          required: true
          schema:
            type: array
            description: 推荐医生配置对象
            items:
              $ref: "#/definitions/RecommendDoctorConfigCreateDto"
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/RecommendDoctorConfigVo"


  /recommend-doctor-configs/{id}:
    get:
      summary: 根据ID查询推荐医生配置详情
      description: 根据指定ID获取推荐医生配置详情
      operationId: getRecommendDoctorConfigById
      tags:
        - RecommendDoctorConfig
      parameters:
        - name: id
          in: path
          description: 推荐医生配置ID
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/RecommendDoctorConfigVo"

    put:
      summary: 更新推荐医生配置
      description: 更新指定ID的推荐医生配置信息
      operationId: updateRecommendDoctorConfig
      tags:
        - RecommendDoctorConfig
      parameters:
        - name: id
          in: path
          description: 推荐医生配置ID
          required: true
          type: integer
          format: int64
        - name: body
          in: body
          description: 更新后的推荐医生配置对象
          required: true
          schema:
            $ref: "#/definitions/RecommendDoctorConfigUpdateDto"
      responses:
        200:
          description: 成功

    delete:
      summary: 删除推荐医生配置
      description: 删除指定ID的推荐医生配置
      operationId: deleteRecommendDoctorConfig
      tags:
        - RecommendDoctorConfig
      parameters:
        - name: id
          in: path
          description: 推荐医生配置ID
          required: true
          type: integer
          format: int64
      responses:
        200:
          description: 成功
  /recommend-doctor-configs/search:
    post:
      summary: 分页查询推荐医生配置
      description: 根据分页参数获取推荐医生配置列表
      operationId: getRecommendDoctorConfigsPage
      tags:
        - RecommendDoctorConfig
      parameters:
        - name: request
          in: body
          schema:
            $ref: "#/definitions/RecommendDoctorConfigQueryDto"
          required: true
      responses:
        200:
          description: 成功
          schema:
            $ref: "#/definitions/RecommendDoctorConfigPageVo"



definitions:
  base_page:
    type: object
    properties:
      total_size:
        type: integer
        description: 总条数
        format: int64
      current_page:
        type: integer
        description: 当前页码
      page_size:
        type: integer
        description: 每页条数

  RecommendDoctorConfigPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/base_page"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/RecommendDoctorConfigVo"
  RecommendDoctorConfigVo:
    type: object
    properties:
      id:
        type: integer
        description: 主键ID
        format: int64
      recommend_id:
        type: integer
        description: 推荐配置ID
        format: int64
      doctor_id:
        type: integer
        description: 医生ID
        format: int64
      doctor_name:
        type: string
        description: 医生名称
      doctor_code:
        type: string
        description: 医生编码
      department_id:
        type: integer
        description: 所属科室ID
        format: int64
      department_code:
        type: string
        description: 所属科室名称
      department_name:
        type: string
        description: 所属科室名称
      hospital_area_id:
        type: integer
        description: 所属院区ID
        format: int64
      hospital_area_code:
        type: string
        description: 所属院区编码
      hospital_area_name:
        type: string
        description: 所属院区名称
      hospital_id:
        type: integer
        description: 所属医院编码
        format: int64
      sort:
        type: integer
        description: 排序号，默认为0
        default: 0
      hospital_name:
        type: string
        description: 所属医院名称
      data_tag:
        type: string
        description: 数据标签，当在相同的 biz_type、data_type 下，通过 data_tag，可以同一 data_type 设置多次
      data_id:
        type: string
        description: 数据全局唯一标识
      data_name:
        type: string
        description: 数据名称
      enabled:
        type: boolean
        description: 是否已启用，默认为true
        default: true
      create_time:
        type: string
        description: 创建时间，默认为当前时间戳
        format: date-time
      update_time:
        type: string
        description: 更新时间，默认为null，更新时自动设置为当前时间戳
        format: date-time
      data_type:
        type: string
        description: 数据类型(2:科室，5：院区)

  RecommendDoctorConfigCreateDto:
    type: object
    properties:
      recommend_id:
        type: integer
        format: int64
        description: 推荐配置ID
      doctor_id:
        type: integer
        description: 医生ID
        format: int64
      doctor_code:
        type: string
        description: 医生编码
      department_id:
        type: integer
        description: 所属科室ID
        format: int64
      department_code:
        type: string
        description: 所属科室编码
      hospital_area_id:
        type: integer
        description: 所属院区ID
        format: int64
      hospital_area_code:
        type: string
        description: 所属院区编码
      hospital_area_name:
        type: string
        description: 所属院区名称
      hospital_id:
        type: integer
        description: 所属医院编码
        format: int64
      sort:
        type: integer
        description: 排序号，默认为0
        default: 0
      hospital_name:
        type: string
        description: 所属医院名称
      data_tag:
        type: string
        description: 数据标签
      enabled:
        type: boolean
        description: 是否已启用，默认为false
        default: false
      data_type:
        type: string
        description: 数据类型(2:科室，5：院区)
  RecommendDoctorConfigUpdateDto:
    type: object
    properties:
      recommend_id:
        type: integer
        description: 推荐配置ID
        format: int64
      doctor_id:
        type: integer
        description: 医生ID
        format: int64
      department_id:
        type: integer
        description: 所属科室ID
        format: int64
      hospital_area_id:
        type: integer
        description: 所属院区ID
        format: int64
      hospital_area_code:
        type: string
        description: 所属院区编码
      hospital_area_name:
        type: string
        description: 所属院区名称
      hospital_id:
        type: integer
        description: 所属医院编码
        format: int64
      sort:
        type: integer
        description: 排序号，默认为0
        default: 0
      hospital_name:
        type: string
        description: 所属医院名称
      data_tag:
        type: string
        description: 数据标签
      data_type:
        type: string
        description: 数据类型(2:科室，5：院区)
      enabled:
        type: boolean
        description: 是否已启用，默认为true
        default: true
  RecommendDoctorConfigQueryDto:
    type: object
    properties:
      page_num:
        type: integer
        format: int32
        description: 页码
      page_size:
        type: integer
        format: int32
        description: 每页显示数量
      recommend_id:
        type: integer
        format: int64
        description: 推荐配置ID（可选）
      doctor_id:
        type: integer
        format: int64
        description: 医生ID（可选）
      doctor_code:
        type: string
        description: 医生编码（可选）
      doctor_name:
        type: string
        description: 医生名称（可选）
      hospital_id:
        type: integer
        format: int64
        description: 医院ID（可选）
      department_id:
        type: integer
        format: int64
        description: 科室ID（可选）
      hospital_area_id:
        type: integer
        format: int64
        description: 医院区域ID（可选）
      hospital_area_code:
        type: string
        description: 医院区域代码（可选）
      hospital_area_name:
        type: string
        description: 医院区域名称（可选）
      hospital_name:
        type: string
        description: 医院名称（可选）
      data_tag:
        type: string
        description: 数据标签（可选）
      enabled:
        type: boolean
        description: 是否启用（可选）
      data_type:
        type: string
        description: 数据类型(2:科室，5：院区)
