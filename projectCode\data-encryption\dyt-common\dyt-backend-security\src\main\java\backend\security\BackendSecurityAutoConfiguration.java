package backend.security;

import backend.security.method.MethodSecurityConfiguration;
import backend.security.oauth2.LoadBalanceJwtDecoderFactoryBean;
import backend.security.oauth2.SecurityConfiguration;
import backend.security.repository.BackendPrivilegeRepository;
import backend.security.repository.impl.BackendPrivilegeRepositoryImpl;
import backend.security.service.BackendClientUserService;
import backend.security.service.BackendTenantUserService;
import backend.security.service.impl.BackendClientUserServiceImpl;
import backend.security.service.impl.BackendTenantUserServiceImpl;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.security.oauth2.jwt.JwtDecoder;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-11 12:07
 */
@Import({SecurityConfiguration.class, MethodSecurityConfiguration.class})
@RequiredArgsConstructor
public class BackendSecurityAutoConfiguration {

    @Bean
    public BackendPrivilegeRepository backendPrivilegeRepository(RedisTemplate<String, Object> redisTemplate) {
        return new BackendPrivilegeRepositoryImpl(redisTemplate);
    }

    @Bean
    LoadBalanceJwtDecoderFactoryBean loadBalanceJwtDecoderFactoryBean(RedisTemplate<String, Object> redisTemplate) {
        return new LoadBalanceJwtDecoderFactoryBean(redisTemplate);
    }

    @Bean
    public BackendTenantUserService backendTenantUserService(BackendPrivilegeRepository backendPrivilegeRepository) {
        return new BackendTenantUserServiceImpl(backendPrivilegeRepository);
    }

    @Bean
    public BackendClientUserService backendClientUserService(JwtDecoder jwtDecoder) {
        return new BackendClientUserServiceImpl(jwtDecoder);
    }

    @Bean
    @ConditionalOnMissingBean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisConnectionFactory);
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance
                , ObjectMapper.DefaultTyping.NON_FINAL
                , JsonTypeInfo.As.WRAPPER_ARRAY);
        jackson2JsonRedisSerializer.setObjectMapper(objectMapper);
        RedisSerializer<String> stringRedisSerializer = new StringRedisSerializer();
        redisTemplate.setKeySerializer(stringRedisSerializer);
        redisTemplate.setValueSerializer(jackson2JsonRedisSerializer);
        redisTemplate.setHashKeySerializer(stringRedisSerializer);
        redisTemplate.setHashValueSerializer(stringRedisSerializer);
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }
}
