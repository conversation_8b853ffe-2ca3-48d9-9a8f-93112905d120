package backend.common.util;

import java.text.SimpleDateFormat;
import java.util.Date;

public class CacheKeyUtils {

    private CacheKeyUtils() {
    }

    public static String getCacheKey(String hisOperationValue, long hospitalAreaId) {
        return String.format("his:cache-mgmt:his-operation:%s:hospital-id:%s", hisOperationValue, hospitalAreaId);
    }

    public static String getCacheKey(String hisOperationValue, long hospitalAreaId, long departmentId) {
        return String.format("his:cache-mgmt:his-operation:%s:hospital-id:%s:department-id:%s",
                hisOperationValue, hospitalAreaId, departmentId);
    }

    public static String getCacheKey(String hisOperationValue, long hospitalAreaId, long departmentId, long doctorId,
                                     String startDate) {
        return String.format("his:cache-mgmt:his-operation:%s:hospital-id:%s:department-id:%s:doctor-id:%s:start-date:%s",
                hisOperationValue, hospitalAreaId, departmentId, doctorId, startDate);
    }


    public static String getCacheKey(String hisOperationValue, String hospitalCode) {
        return String.format("his:cache-mgmt:his-operation:%s:hospital-code:%s", hisOperationValue, hospitalCode);
    }

    public static String getCacheKey(String hisOperationValue, String hospitalCode, String departmentCode) {
        return String.format("his:cache-mgmt:his-operation:%s:hospital-code:%s:department-code:%s",
                hisOperationValue, hospitalCode, departmentCode);
    }

    public static String getCacheKey(String hisOperationValue, String hospitalCode, String departmentCode, long startDate) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return String.format("his:cache-mgmt:his-operation:%s:hospital-code:%s:department-code:%s:start-date:%s",
                hisOperationValue, hospitalCode, departmentCode, format.format(new Date(startDate)));
    }

    public static String getCacheKey(String hisOperationValue, String hospitalCode, String departmentCode,
                                     String doctorCode, long startDate) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return String.format("his:cache-mgmt:his-operation:%s:hospital-code:%s:department-code:%s:doctor-code:%s:start-date:%s",
                hisOperationValue, hospitalCode, departmentCode, doctorCode, format.format(new Date(startDate)));
    }
}
