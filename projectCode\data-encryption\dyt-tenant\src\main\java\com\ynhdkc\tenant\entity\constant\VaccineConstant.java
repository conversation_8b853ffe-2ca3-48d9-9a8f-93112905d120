package com.ynhdkc.tenant.entity.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface VaccineConstant {
    @AllArgsConstructor
    @Getter
    enum Status {
        NORMAL(0, "正常"),
        DISABLE(1, "禁用"),
        ;
        final int code;
        final String desc;
    }

    @AllArgsConstructor
    @Getter
    enum Sort {
        DEFAULT(0, "默认排序"),
        DISTANCE(1, "距离排序"),
        ;
        final int code;
        final String desc;
    }

    @AllArgsConstructor
    @Getter
    enum SourceChannel {
        HIS(0, "HIS"),
        INNER(1, "小系统"),
        ;
        final int code;
        final String desc;
    }

    @AllArgsConstructor
    @Getter
    enum AppointmentType {
        APPOINTMENT(1, "可预约"),
        REGISTER(2, "可登记"),
        NO_STOCK(3, "暂无号"),
        ;
        final int code;
        final String desc;
    }
}
