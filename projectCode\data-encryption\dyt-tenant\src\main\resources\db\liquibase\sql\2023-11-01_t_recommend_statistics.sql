create table t_recommend_statistics
(
    id           bigint auto_increment primary key,
    recommend_id bigint                                   not null comment '推荐id',
    quantity     bigint      default 0                    not null comment '数量',
    total        bigint      default 0                    not null comment '总数',
    create_time  datetime(3) default CURRENT_TIMESTAMP(3) not null,
    update_time  datetime(3)                              null on update CURRENT_TIMESTAMP(3)

) comment '推荐统计表';

create index idx_id_with_time_asc on t_recommend_statistics (recommend_id, create_time asc);