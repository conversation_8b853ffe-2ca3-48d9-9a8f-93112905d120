package com.ynhdkc.tenant.service.backend.impl;

import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import backend.common.util.JsonUtil;
import backend.common.util.MybatisUtil;
import backend.security.service.BackendTenantUserService;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.ynhdkc.oldsystem.integration.api.request.GetExtensionAdvertisementReqDto;
import com.ynhdkc.oldsystem.integration.api.response.GetExtensionAdvertisementRespDto;
import com.ynhdkc.oldsystem.integration.client.AdvertisementClient;
import com.ynhdkc.tenant.client.AppointmentOrderClient;
import com.ynhdkc.tenant.client.model.ApiOrderAppointment;
import com.ynhdkc.tenant.dao.HospitalAreaQuery;
import com.ynhdkc.tenant.dao.HospitalAreaSettingQuery;
import com.ynhdkc.tenant.dao.HospitalQuery;
import com.ynhdkc.tenant.dao.HospitalRepository;
import com.ynhdkc.tenant.dao.mapper.*;
import com.ynhdkc.tenant.entity.*;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.AddressService;
import com.ynhdkc.tenant.service.backend.*;
import com.ynhdkc.tenant.tool.SetDictInfoHelper;
import com.ynhdkc.tenant.util.CommonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import sdk.common.util.JsonUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ynhdkc.tenant.tool.convert.AppointmentSettingConverter.toAppointmentRuleSettingVo;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-26 14:25
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HospitalServiceImpl implements HospitalService {
    private static final Function<Hospital, CustomerHospitalVo> customerHospitalToVo = hospital -> {
        CustomerHospitalVo vo = new CustomerHospitalVo();
        vo.setId(hospital.getId());
        vo.setName(hospital.getName());
        vo.setLogo(hospital.getLogo());
        vo.setAddressId(hospital.getAddressId());
        vo.setStatus(hospital.getStatus());
        return vo;
    };
    private final HospitalQuery hospitalQuery;
    private final HospitalAreaQuery hospitalAreaQuery;
    private final HospitalRepository hospitalRepository;
    private final HospitalAreaSettingQuery hospitalAreaSettingQuery;
    private final TenantService tenantService;
    private final AddressService addressService;
    private final BackendTenantUserService backendTenantUserService;
    private final SetDictInfoHelper setDictInfoHelper;
    private final HospitalMapper hospitalMapper;
    private final HospitalListGroupRelationMapper hospitalListGroupRelationMapper;
    private final AddressMapper addressMapper;
    private final HospitalListIndexMapper hospitalListIndexMapper;
    private final HospitalListRuleMapper hospitalListRuleMapper;
    private final HospitalListDisplayMapper hospitalListDisplayMapper;
    private final DoctorMapper doctorMapper;
    private final DepartmentMapper departmentMapper;
    private final AppointmentOrderClient appointmentOrderClient;
    private final AdvertisementClient advertisementClient;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public HospitalDetailVo create(HospitalCreateReqDto request) {
        /* 校验租户是否存在 */
        tenantService.getDetail(request.getTenantId());
        Hospital hospital = toHospitalEntity(request);
        /* 存储地址 */
        if (request.getAddress() != null) {
            hospital.setAddressId(addressService.create(request.getAddress()));
        }
        /* 补充字典值 */
        setDictInfoHelper.setDictInfo(request, hospital);

        hospitalRepository.createHospital(hospital);

        HospitalDetailVo hospitalVo = HospitalService.toHospitalDetailVo(hospital);
        setDictInfoHelper.setDictInfo(hospital, hospitalVo);
        importHospitalAddress(hospital.getAddressId(), hospitalVo);
        return hospitalVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public HospitalDetailVo update(Long hospitalId, HospitalUpdateReqDto request) {
        Hospital hospital = hospitalQuery.queryHospitalById(hospitalId);
        if (null == hospital) {
            throw new BizException(HttpStatus.NOT_FOUND, "医院不存在");
        }
        updateIfNotNull(hospital, request);
        /* 存储地址 */
        if (request.getAddress() != null) {
            hospital.setAddressId(addressService.create(request.getAddress()));
        }
        /* 补充字典值 */
        if (null != request.getLevelDictLabel() && !request.getLevelDictLabel().isEmpty()) {
            setDictInfoHelper.setDictInfo(request, hospital);
        }

        hospitalRepository.updateHospital(hospital);

        HospitalDetailVo hospitalVo = HospitalService.toHospitalDetailVo(hospital);
        setDictInfoHelper.setDictInfo(hospital, hospitalVo);
        importHospitalAddress(hospital.getAddressId(), hospitalVo);
        return hospitalVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseOperationResponse delete(Long hospitalId) {
        Hospital hospital = hospitalQuery.queryHospitalById(hospitalId);
        if (null == hospital) {
            throw new BizException(HttpStatus.NOT_FOUND, "医院不存在");
        }
        hospitalRepository.deleteHospital(hospitalId);
        return new BaseOperationResponse("删除成功");
    }

    @Override
    public HospitalDetailVo getDetail(Long hospitalId) {
        Hospital hospital = hospitalQuery.queryHospitalById(hospitalId);
        if (null == hospital) {
            throw new BizException(HttpStatus.NOT_FOUND, "医院不存在");
        }

        /* 权限校验 */
        if (!backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.HospitalId(hospitalId))) {
            throw new BizException(HttpStatus.FORBIDDEN, "用户无访问该医院权限");
        }

        HospitalDetailVo hospitalVo = HospitalService.toHospitalDetailVo(hospital);
        setDictInfoHelper.setDictInfo(hospital, hospitalVo);
        importHospitalAddress(hospital.getAddressId(), hospitalVo);
        return hospitalVo;
    }

    @Override
    public HospitalDetailVo rpcGetDetail(Long hospitalId) {
        Hospital hospital = hospitalQuery.queryHospitalById(hospitalId);
        if (null == hospital) {
            return null;
        }

        HospitalDetailVo hospitalVo = HospitalService.toHospitalDetailVo(hospital);
        setDictInfoHelper.setDictInfo(hospital, hospitalVo);
        importHospitalAddress(hospital.getAddressId(), hospitalVo);
        return hospitalVo;
    }

    @Override
    public HospitalKafkaVo getDetailForKafka(Long hospitalId) {
        Hospital hospital = hospitalQuery.queryHospitalById(hospitalId);
        if (null == hospital) {
            log.error("The method -> getDetailForKafka -> 医院不存在，hospitalId:{}", hospitalId);
            throw new BizException(HttpStatus.NOT_FOUND, "医院不存在");
        }
        HospitalKafkaVo kafkaVo = HospitalService.toHospitalKafkaVo(hospital);
        setDictInfoHelper.setDictInfo(hospital, kafkaVo);
        importHospitalAreas(kafkaVo);

        return kafkaVo;
    }

    @Override
    public HospitalPageVo query(HospitalQueryReqDto request) {
        /* 未指定租户ID，需要判断是否为管理员 */
        boolean isSuperAdmin = backendTenantUserService.isSuperAdmin();
        if (null == request.getTenantId() && !isSuperAdmin) {
            throw new BizException(HttpStatus.FORBIDDEN, "必须指定租户ID");
        }
        /* 查询范围管控 */
        if (!isSuperAdmin && !backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.TenantId(request.getTenantId()))) {
            throw new BizException(HttpStatus.FORBIDDEN, "用户无访问该租户权限");
        }
        HospitalQuery.HospitalQueryOption option = new HospitalQuery.HospitalQueryOption(request.getCurrentPage(), request.getPageSize());
        option.setHospitalCode(request.getHospitalCode()).setTenantId(request.getTenantId()).setName(request.getHospitalName()).setLevelDictValue(request.getLevelDictValue()).setCategory(request.getCategory()).setExcludeCategory(request.getExcludeCategory()).setStatus(request.getStatus()).setStartCreateTime(request.getStartCreateTime()).setEndCreateTime(request.getEndCreateTime());
        if (!isSuperAdmin) {
            option.setIncludeIds(backendTenantUserService.getCurrentUserHospitalReadRange(true));
        }
        try (Page<Hospital> hospitalPage = hospitalQuery.pageQueryHospitalWithCategory(option)) {
            List<HospitalVo> hospitalVos = hospitalPage.stream().map(hospital -> {
                HospitalVo hospitalVo = HospitalService.toHospitalVo(hospital);
                setDictInfoHelper.setDictInfo(hospital, hospitalVo);
                return hospitalVo;
            }).collect(Collectors.toList());
            HospitalPageVo pageVo = new HospitalPageVo();
            pageVo.totalSize(hospitalPage.getTotal());
            pageVo.currentPage(hospitalPage.getPageNum());
            pageVo.pageSize(hospitalPage.getPageSize());
            pageVo.setList(hospitalVos);
            return pageVo;
        }
    }

    @Override
    public List<HospitalKafkaVo> queryAllForKafka() {
        try (Page<Hospital> hospitalPage = hospitalQuery.pageQueryHospital(new HospitalQuery.HospitalQueryOption(1, Integer.MAX_VALUE))) {
            if (hospitalPage.isEmpty()) {
                return Collections.emptyList();
            }
            return hospitalPage.stream().map(HospitalService::toHospitalKafkaVo).peek(this::importHospitalAreas).collect(Collectors.toList());
        }
    }

    @Override
    public CustomerHospitalPageVo customerQueryHospitalList(Long userId, QueryCustomerHospitalPageReqDto request) {
        ApiOrderAppointment latestOrder = null;
        if (null != userId) {
            try {
                latestOrder = appointmentOrderClient.getOrderAppointmentByUserId(userId).getBody();
            } catch (Exception e) {
                log.error("查询用户最近预约订单失败", e);
            }
        }
        log.info("查询上次挂号订单，userId:{}", userId);
        log.info("查询上次挂号订单，latestOrder:{}", JsonUtil.serializeObject(latestOrder));

        // 查询所有医院及院区
        List<Hospital> allHospitals = hospitalMapper.selectByExample2(Hospital.class,
                sql -> sql.andIn(Hospital::getStatus, Arrays.asList(0, 2)));
        if (latestOrder != null && !ObjectUtils.isEmpty(latestOrder.getHospitalAreaCode())) {
            String hospitalCodeFromOrder = latestOrder.getHospitalAreaCode();
            AtomicLong hospitalIdFromOrderInNewSystem = new AtomicLong();
            allHospitals.stream()
                    .filter(h -> h != null && hospitalCodeFromOrder.equals(h.getHospitalCode()))
                    .findFirst()
                    .ifPresent(hospital -> hospitalIdFromOrderInNewSystem.set(hospital.getParentId()));
            latestOrder.setHospitalId(hospitalIdFromOrderInNewSystem.get() == 0L ? null : hospitalIdFromOrderInNewSystem.get());
        }

        List<Hospital> hospitalList = allHospitals.stream().filter(h -> h.getHospitalTypeTag() == 0).collect(Collectors.toList());
        Integer platform = request.getPlatform() == null ? 0 : request.getPlatform();
        if (request.getPlatform() != 0) {
            // 微信不做筛选
            List<HospitalListGroupRelation> relationsInPlatform = hospitalListGroupRelationMapper.selectByExample(HospitalListGroupRelation.class,
                    help -> help.defGroup(
                            sql -> sql.andLike(HospitalListGroupRelation::getPlatform, MybatisUtil.likeBoth(String.valueOf(platform)))));
            List<Long> platformHospitalIds = relationsInPlatform
                    .stream()
                    .map(HospitalListGroupRelation::getHospitalId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            hospitalList = hospitalList.stream().filter(hos -> platformHospitalIds.contains(hos.getId())).collect(Collectors.toList());

            if (!platformHospitalIds.contains(getLatestHospitalId(latestOrder))) {
                log.info("用户上次挂号的医院不在当前平台，userId:{}, platform:{}, latestOrder:{}", userId, platform, JsonUtil.serializeObject(latestOrder));
                latestOrder = null;
            }
        }
        List<Hospital> hospitalAreaList = allHospitals.stream()
                .filter(h -> h.getHospitalTypeTag() == 1 && h.getDisplay())
                .collect(Collectors.toList());
        List<Address> allAddress = addressMapper.selectAll();

        // 按条件过滤
        hospitalList = filterHospitalList(request, hospitalList, hospitalAreaList, allAddress);
        // 判断排序类型
        if (request.getSortType() != 0) {
            // 非综合排序
            return getCustomerHospitalPageVoWithCondition(request, latestOrder, hospitalList, hospitalAreaList, allAddress);
        }
        // 综合排序走医院列表规则
        return getCustomerHospitalPageVoWithDefaultSortRule(request, allAddress, hospitalList, hospitalAreaList, allHospitals, latestOrder);
    }

    private Long getLatestHospitalId(ApiOrderAppointment latestOrder) {
        if (latestOrder == null) {
            return null;
        }
        if (latestOrder.getHospitalId() == null) {
            return null;
        }
        return latestOrder.getHospitalId();
    }

    private boolean hasCondition(Object... objects) {
        for (Object object : objects) {
            if (object != null) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<HospitalVo> selectHospitalListByRuleId(Long id) {
        HospitalListRule rule = hospitalListRuleMapper.selectByPrimaryKey(id);
        if (rule == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "规则不存在");
        }
        if (rule.getType() == 4) {
            return new ArrayList<>();
        }
        List<HospitalVo> result = new ArrayList<>();
        if (rule.getType() == 5) {
            HospitalListRuleVo vo = IHospitalListRuleService.toVo(rule);
            List<GroupScaleDto> groupScale = vo.getGroupScale();
            if (groupScale != null && !groupScale.isEmpty()) {
                List<Long> groupIds = groupScale.stream().map(GroupScaleDto::getGroupId).collect(Collectors.toList());
                List<HospitalListGroupRelation> relationList = hospitalListGroupRelationMapper.selectByExample2(HospitalListGroupRelation.class, sql -> sql.andIn(HospitalListGroupRelation::getGroupId, groupIds));
                result = getHospitalListByRelations(relationList);
            }
        } else {
            // 通过rule的groupId查询医院组和医院的关系
            List<HospitalListGroupRelation> relationList = hospitalListGroupRelationMapper.selectByExample2(HospitalListGroupRelation.class, sql -> sql.andEqualTo(HospitalListGroupRelation::getGroupId, rule.getHospitalGroupId()));
            result = getHospitalListByRelations(relationList);
        }
        return result;
    }

    @Override
    public Hospital getByHospitalAreaId(Long hospitalAreaId) {
        Hospital hospital = hospitalMapper.selectOneByExample2(Hospital.class, sql -> sql.andEqualTo(Hospital::getId, hospitalAreaId));
        if (hospital == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "医院不存在");
        }
        return hospital;
    }

    @Override
    public HospitalAreaQueryLabelsVo queryHospitalAreaQueryLabels(HospitalAreaQueryLabelsReqDto request) {
        /* 未指定租户ID，需要判断是否为管理员 */
        boolean isSuperAdmin = backendTenantUserService.isSuperAdmin();
        if (null == request.getTenantId() && !isSuperAdmin) {
            throw new BizException(HttpStatus.FORBIDDEN, "必须指定租户ID");
        }
        HospitalQuery.HospitalQueryOption option = new HospitalQuery.HospitalQueryOption(1, Integer.MAX_VALUE);
        option.setTenantId(request.getTenantId())
                .setName(request.getHospitalName());
        if (!isSuperAdmin) {
            option.setIncludeIds(backendTenantUserService.getCurrentUserHospitalReadRange(true));
        }
        try (Page<Hospital> hospitalPage = hospitalQuery.pageQueryHospital(option)) {
            Set<Long> hospitalIds = hospitalPage.stream().map(Hospital::getId).collect(Collectors.toSet());
            if (hospitalIds.isEmpty()) {
                return new HospitalAreaQueryLabelsVo()
                        .hospitalList(Collections.emptyList());
            }
            HospitalAreaQuery.HospitalAreaQueryOption hospitalAreaQueryOption = new HospitalAreaQuery.HospitalAreaQueryOption(1, Integer.MAX_VALUE)
                    .setCategory(request.getCategory())
                    .setIncludeHospitalIds(hospitalIds);
            if (!isSuperAdmin) {
                hospitalAreaQueryOption.setIncludeIds(backendTenantUserService.getCurrentUserHospitalAreaReadRange(true));
            }
            Page<Hospital> hospitalAreaPage = hospitalAreaQuery.pageQueryHospitalArea(hospitalAreaQueryOption);
            Map<Long, List<Hospital>> groupedHospitalArea = hospitalAreaPage.stream().collect(Collectors.groupingBy(Hospital::getParentId, Collectors.toList()));
            List<HospitalAreaQueryLabelsHospitalVo> vos = hospitalPage.stream()
                    .map(hospital -> {
                        HospitalAreaQueryLabelsHospitalVo vo = new HospitalAreaQueryLabelsHospitalVo();
                        vo.setId(hospital.getId());
                        vo.setDisplayName(hospital.getName());
                        vo.setTenantId(hospital.getTenantId());
                        List<Hospital> hospitalAreaList = groupedHospitalArea.get(hospital.getId());
                        if (hospitalAreaList != null) {
                            vo.setHospitalAreaList(hospitalAreaList.stream()
                                    .map(hospitalArea -> {
                                        HospitalAreaQueryLabelsHospitalAreaVo hospitalAreaVo = new HospitalAreaQueryLabelsHospitalAreaVo();
                                        hospitalAreaVo.setId(hospitalArea.getId());
                                        hospitalAreaVo.setName(hospitalArea.getName());
                                        hospitalAreaVo.setHospitalAreaCode(hospitalArea.getHospitalCode());
                                        hospitalAreaVo.setHospitalId(hospitalArea.getParentId());
                                        hospitalAreaVo.setTenantId(hospitalArea.getTenantId());
                                        hospitalAreaVo.setDisplayName(hospitalArea.getName() + "-" + hospitalArea.getHospitalCode());
                                        hospitalAreaVo.setStatus(hospitalArea.getStatus());
                                        return hospitalAreaVo;
                                    })
                                    .collect(Collectors.toList()));
                        }
                        return vo;
                    })
                    .filter(vo -> !CollectionUtils.isEmpty(vo.getHospitalAreaList()))
                    .collect(Collectors.toList());
            return new HospitalAreaQueryLabelsVo()
                    .hospitalList(vos);
        }
    }

    @Override
    public List<HospitalVo> rpcBatchGetDetail(RpcBatchGetByIdReqDto request) {
        try (Page<Hospital> hospitalPage = hospitalQuery.pageQueryHospital(new HospitalQuery.HospitalQueryOption(1, request.getIds().size())
                .setIncludeIds(request.getIds()))) {
            return hospitalPage.stream().map(HospitalService::toHospitalVo).collect(Collectors.toList());
        }
    }

    private List<HospitalVo> getHospitalListByRelations(List<HospitalListGroupRelation> relationList) {
        List<HospitalVo> result = new ArrayList<>();
        if (relationList != null && !relationList.isEmpty()) {
            List<Long> hospitalIds = relationList.stream().map(HospitalListGroupRelation::getHospitalId).collect(Collectors.toList());
            if (!hospitalIds.isEmpty()) {
                List<Hospital> hospitals = hospitalMapper.selectByIds(StrUtil.join(",", hospitalIds));
                if (hospitals != null && !hospitals.isEmpty()) {
                    hospitals.stream().map(HospitalService::toHospitalVo).forEach(result::add);
                }
            }
        }
        return result;
    }

    private void importHospitalAreas(HospitalKafkaVo hospitalKafkaVo) {
        HospitalAreaQuery.HospitalAreaQueryOption option = new HospitalAreaQuery.HospitalAreaQueryOption(1, Integer.MAX_VALUE).setHospitalId(hospitalKafkaVo.getId());
        try (Page<Hospital> hospitalAreaPage = hospitalAreaQuery.pageQueryHospitalArea(option)) {
            if (hospitalAreaPage.isEmpty()) {
                return;
            }
            List<HospitalAreaVo> hospitalAreaVos = hospitalAreaPage.stream().map(HospitalAreaService::toHospitalAreaVo).peek(hospitalAreaVo -> {
                Optional<AppointmentRuleSetting> appointmentRuleSettingOptional = hospitalAreaSettingQuery.queryAppointmentRuleSettingById(hospitalAreaVo.getTenantId(), hospitalAreaVo.getHospitalId(), hospitalAreaVo.getId());
                appointmentRuleSettingOptional.ifPresent(appointmentRuleSetting -> hospitalAreaVo.setAppointmentSetting(toAppointmentRuleSettingVo(appointmentRuleSetting)));
            }).collect(Collectors.toList());
            hospitalKafkaVo.setHospitalAreaList(hospitalAreaVos);
        }
    }

    private void updateIfNotNull(Hospital hospital, HospitalUpdateReqDto dto) {
        if (dto.getName() != null) {
            hospital.setName(dto.getName());
        }
        if (dto.getLogo() != null) {
            hospital.setLogo(dto.getLogo());
        }
        if (dto.getLevelDictType() != null) {
            hospital.setLevelDictType(dto.getLevelDictType());
        }
        if (dto.getCategory() != null) {
            hospital.setCategories(dto.getCategory());
        }
        if (dto.getStatus() != null) {
            hospital.setStatus(dto.getStatus());
        }
        if (dto.getProperty() != null) {
            hospital.setProperty(dto.getProperty());
        }
        if (dto.getDisplaySort() != null) {
            hospital.setDisplaySort(dto.getDisplaySort());
        }
    }

    private void importHospitalAddress(Long addressId, HospitalDetailVo vo) {
        if (null != addressId) {
            vo.setAddress(addressService.getAddressVoById(addressId));
        }
    }

    private CustomerHospitalPageVo getCustomerHospitalPageVoWithCondition(QueryCustomerHospitalPageReqDto request,
                                                                          ApiOrderAppointment latestOrder,
                                                                          List<Hospital> hospitalList,
                                                                          List<Hospital> hospitalAreaList,
                                                                          List<Address> allAddress) {
        if (request.getSortType() == 1) {
            // 按照距离排序
            Comparator<Address> distanceComparator = Comparator.nullsLast(Comparator.comparing(o -> {
                Double distance = CommonUtil.getDistance(request.getLongitude(), request.getLatitude(), o.getLongitude(), o.getLatitude());
                return distance != null ? distance : Double.POSITIVE_INFINITY;
            }));
            if (request.getSort() != null && request.getSort() == 1) {
                distanceComparator.reversed();
            }
            List<Long> addIdsSorted = allAddress.stream().sorted(distanceComparator).map(Address::getId).collect(Collectors.toList());
            List<Hospital> areaSorted = hospitalAreaList.stream().sorted(Comparator.comparingLong(o -> addIdsSorted.indexOf(o.getAddressId()))).collect(Collectors.toList());
            List<Long> hospitalIds = areaSorted.stream().map(Hospital::getParentId).collect(Collectors.toList());
            hospitalList.sort(Comparator.comparing(o -> hospitalIds.indexOf(o.getId())));
        } else if (request.getSortType() == 2) {
            Comparator<Hospital> comparing = Comparator.nullsLast(Comparator.comparing(h -> {
                String levelDictValue = h.getLevelDictValue();
                try {
                    return Integer.parseInt(levelDictValue);
                } catch (NumberFormatException e) {
                    return -1;
                }
            }));
            if (request.getSort() == 1) {
                comparing.reversed();
            }
            hospitalList.sort(comparing);
        }
        return buildCustomerHospitalPageVo(request, hospitalList, hospitalAreaList, allAddress, request.getLongitude(), request.getLatitude(), latestOrder);
    }

    private CustomerHospitalPageVo getCustomerHospitalPageVoWithDefaultSortRule(QueryCustomerHospitalPageReqDto request,
                                                                                List<Address> allAddress,
                                                                                List<Hospital> hospitalList,
                                                                                List<Hospital> hospitalAreaList,
                                                                                List<Hospital> allHospitals,
                                                                                ApiOrderAppointment latestOrder) {
        List<HospitalListIndex> indexList = hospitalListIndexMapper.selectAll()
                .stream()
                .sorted(Comparator.comparing(HospitalListIndex::getIndexOrder))
                .collect(Collectors.toList());
        if (indexList.isEmpty()) {
            return new CustomerHospitalPageVo();
        }

        List<Long> ruleIds = indexList.stream()
                .map(HospitalListIndex::getRuleId)
                .collect(Collectors.toList());
        if (ruleIds.isEmpty()) {
            return new CustomerHospitalPageVo();
        }
        List<HospitalListRule> rules = hospitalListRuleMapper.selectByIds(StrUtil.join(",", ruleIds));
        List<Hospital> resultHospitals = getHospitalListByOrderRule(indexList,
                rules,
                request.getLongitude(),
                request.getLatitude(),
                allAddress,
                hospitalList,
                hospitalAreaList,
                allHospitals,
                latestOrder);

        CustomerHospitalPageVo customerHospitalPageVo = buildCustomerHospitalPageVo(request, resultHospitals, hospitalAreaList, allAddress, request.getLongitude(), request.getLatitude(), latestOrder);

        List<CustomerHospitalVo> advertisementHospitals = getAdvertisementHospitals(latestOrder, rules);
        List<CustomerHospitalVo> records = customerHospitalPageVo.getRecords();
        // 根据adv_position将广告医院插入到对应位置，若长度不够则直接添加到最后
        for (CustomerHospitalVo advertisementHospital : advertisementHospitals) {
            if (advertisementHospital.getAdvPosition() == null) {
                records.add(advertisementHospital);
            } else {
                int index = advertisementHospital.getAdvPosition() - 1;
                if (index < records.size()) {
                    records.add(index, advertisementHospital);
                } else {
                    records.add(advertisementHospital);
                }
            }
        }
        customerHospitalPageVo.setRecords(records);

        return customerHospitalPageVo;
    }

    private List<Hospital> filterHospitalList(QueryCustomerHospitalPageReqDto request, List<Hospital> hospitalList, List<Hospital> hospitalAreaList, List<Address> allAddress) {
        // 按行政区域筛选
        if (hasCondition(request.getProvince(), request.getCity(), request.getCounty())) {
            List<Address> addressList = allAddress;
            if (request.getProvince() != null && !"".equals(request.getProvince())) {
                addressList = addressList.stream().filter(a -> {
                    if (a != null && a.getProvince() != null) {
                        return a.getProvince().contains(request.getProvince());
                    } else {
                        return false;
                    }
                }).collect(Collectors.toList());
            }
            if (request.getCity() != null && !"".equals(request.getCity())) {
                addressList = addressList.stream().filter(a -> {
                    if (a != null && a.getCity() != null) {
                        return a.getCity().contains(request.getCity());
                    } else {
                        return false;
                    }
                }).collect(Collectors.toList());
            }
            if (request.getCounty() != null && !"".equals(request.getCounty())) {
                addressList = addressList.stream().filter(a -> {
                    if (a != null && a.getCounty() != null) {
                        return a.getCounty().contains(request.getCounty());
                    } else {
                        return false;
                    }
                }).collect(Collectors.toList());
            }
            List<Long> filterAddIds = addressList.stream().map(Address::getId).collect(Collectors.toList());
            List<Hospital> areaFilter = hospitalAreaList.stream().filter(h -> filterAddIds.contains(h.getAddressId())).collect(Collectors.toList());
            List<Long> hospitalIds = areaFilter.stream().map(Hospital::getParentId).distinct().collect(Collectors.toList());
            hospitalList = hospitalList.stream().filter(h -> hospitalIds.contains(h.getId())).collect(Collectors.toList());
        }

        // 按照名称模糊筛选
        if (request.getName() != null && !"".equals(request.getName())) {
            List<Hospital> areaFilter = hospitalAreaList.stream().filter(h -> h.getName().contains(request.getName())).collect(Collectors.toList());
            List<Long> hospitalIds = areaFilter.stream().map(Hospital::getParentId).distinct().collect(Collectors.toList());
            hospitalList = hospitalList.stream().filter(h -> h.getName().contains(request.getName()) || hospitalIds.contains(h.getId())).collect(Collectors.toList());
        }

        // 按医院等级筛选
        if (request.getLevel() != null && !"".equals(request.getLevel())) {
            hospitalList = hospitalList.stream().filter(h -> h.getLevelDictValue() != null && h.getLevelDictValue().equals(request.getLevel())).collect(Collectors.toList());
        }
        return hospitalList;
    }

    private CustomerHospitalPageVo buildCustomerHospitalPageVo(QueryCustomerHospitalPageReqDto request,
                                                               List<Hospital> hospitalList,
                                                               List<Hospital> hospitalAreaList,
                                                               List<Address> adds,
                                                               Double longitude,
                                                               Double latitude,
                                                               ApiOrderAppointment latestOrder) {
        CustomerHospitalPageVo pageVo = new CustomerHospitalPageVo();
        pageVo.setRecords(hospitalList.stream().map(customerHospitalToVo).collect(Collectors.toList()));

        List<Long> hospitalIds = hospitalList.stream().map(Hospital::getId).collect(Collectors.toList());
        Map<Long, HospitalListDisplay> displayMap;
        Map<Long, Doctor> doctorMap = new HashMap<>();
        Map<Long, Department> departmentMap = new HashMap<>();
        if (!hospitalIds.isEmpty()) {
            List<HospitalListDisplay> displays = hospitalListDisplayMapper.selectByExample2(HospitalListDisplay.class, sql -> sql.andIn(HospitalListDisplay::getHospitalId, hospitalIds));
            displayMap = displays.stream().collect(Collectors.toMap(HospitalListDisplay::getHospitalId, Function.identity()));
            if (!displays.isEmpty()) {
                List<Long> doctorIds = displays.stream().map(HospitalListDisplay::getRecommendedDoctorId).collect(Collectors.toList());
                if (!doctorIds.isEmpty()) {
                    List<Doctor> doctors = doctorMapper.selectByIds(StrUtil.join(",", doctorIds));
                    doctors.forEach(d -> doctorMap.put(d.getId(), d));
                    List<Long> departmentIds = doctors.stream().map(Doctor::getDepartmentId).collect(Collectors.toList());
                    if (!departmentIds.isEmpty()) {
                        List<Department> departments = departmentMapper.selectByIds(StrUtil.join(",", departmentIds));
                        departments.forEach(d -> departmentMap.put(d.getId(), d));
                    }
                }
            }
        } else {
            displayMap = new HashMap<>();
        }
        boolean hasDistance = hasCondition(request.getLongitude(), request.getLatitude());
        Map<Long, List<Hospital>> areaMap = hospitalAreaList.stream().collect(Collectors.groupingBy(Hospital::getParentId));


        List<AppointmentRuleSetting> appointmentRuleSettings = hospitalAreaSettingQuery.All();
        pageVo.getRecords().forEach(r -> {
            List<Hospital> areas = areaMap.get(r.getId());
            if (areas != null) {
                List<CustomerHospitalVoHospitalAreaList> areaList = new ArrayList<>();
                areas.stream().map(a -> {

                    CustomerHospitalVoHospitalAreaList area = new CustomerHospitalVoHospitalAreaList();
                    area.setId(a.getId());
                    area.setCode(a.getHospitalCode());
                    area.setStatus(a.getStatus());
                    Optional<AppointmentRuleSetting> appointmentRuleSetting = Optional.of(appointmentRuleSettings.stream().filter(appointmentRuleSetting1 -> appointmentRuleSetting1.getHospitalAreaId().equals(a.getId())).findFirst()).get();
                    area.setCurrentSystemType(appointmentRuleSetting.map(AppointmentRuleSetting::getCurrentSystemType).orElse(null));
                    return area;
                }).forEach(areaList::add);
                r.setHospitalAreaList(areaList);
            }

            Address address = adds.stream().filter(a -> a.getId().equals(r.getAddressId())).findFirst().orElse(null);
            if (address != null) {
                r.setRegion(address.getCounty());
                if (hasDistance) {
                    r.setDistance(CommonUtil.getDistance(longitude, latitude, address.getLongitude(), address.getLatitude()));
                }
            }
            HospitalListDisplay display = displayMap.get(r.getId());
            if (display != null) {
                CustomerHospitalListDisplayVo displayVo = IHospitalListDisplayService.toCustomerHospitalDisplayVo(display);
                Doctor doctor = doctorMap.get(display.getRecommendedDoctorId());
                if (doctor != null) {
                    RecommendedDoctorVo recommendedDoctorVo = new RecommendedDoctorVo();
                    recommendedDoctorVo.setName(doctor.getName());
                    recommendedDoctorVo.setAvatar(doctor.getHeadImgUrl());
                    recommendedDoctorVo.setRank(doctor.getRankDictLabel());
                    recommendedDoctorVo.setDescription(doctor.getIntroduction());
                    recommendedDoctorVo.setSpeciality(doctor.getSpeciality());
                    Department department = departmentMap.get(doctor.getDepartmentId());
                    if (department != null) {
                        recommendedDoctorVo.setDepartmentName(department.getName());
                    }
                    displayVo.setRecommendedDoctors(Collections.singletonList(recommendedDoctorVo));
                }
                r.setDisplay(displayVo);
            } else {
                CustomerHospitalListDisplayVo defaultDisplay = new CustomerHospitalListDisplayVo();
                defaultDisplay.setHospitalId(r.getId());
                defaultDisplay.setHospitalNameColorType(0);
                defaultDisplay.setHospitalNameHexColorCode("#000000");
                defaultDisplay.setLogoType(0);
                defaultDisplay.setDisplayCornerMark(0);
                defaultDisplay.setCornerMarkStyle("");
                defaultDisplay.setDisplayTag(0);
                defaultDisplay.setTags("");
                defaultDisplay.setRecommendedDoctors(new ArrayList<>());
                r.setDisplay(defaultDisplay);
            }
        });
        Long lastHospitalId = getLatestHospitalId(latestOrder);
        if (!CollectionUtils.isEmpty(pageVo.getRecords())
                && pageVo.getRecords().get(0) != null
                && lastHospitalId != null
                && lastHospitalId.equals(pageVo.getRecords().get(0).getId())) {
            pageVo.getRecords().get(0).getDisplay().setIsLastHospital(1);
        }

        return pageVo;
    }

    private List<CustomerHospitalVo> getAdvertisementHospitals(ApiOrderAppointment latestOrder, List<HospitalListRule> rules) {
        if (latestOrder == null) {
            return new ArrayList<>();
        }
        List<HospitalListRule> advRules = rules.stream().filter(rule -> rule.getType() == 4).collect(Collectors.toList());
        List<CustomerHospitalVo> advHospitalVos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(advRules)) {
            GetExtensionAdvertisementReqDto extensionAdvertisementReqDto = new GetExtensionAdvertisementReqDto();
            List<Long> advIds = advRules.stream().map(HospitalListRule::getAdvertisementId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(advIds)) {
                extensionAdvertisementReqDto.setIds(advIds)
                        .setEnabled(true)
                        .setIsShow(1);
//                log.info("查询医院列表广告请求体:{}", JsonUtil.serializeObject(extensionAdvertisementReqDto));
                List<GetExtensionAdvertisementRespDto> extensionAdvertisementList = advertisementClient.getExtensionAdvertisementList(extensionAdvertisementReqDto);
//                log.info("查询医院列表广告，extensionAdvertisementList:{}", JsonUtil.serializeObject(extensionAdvertisementList));
                if (!CollectionUtils.isEmpty(extensionAdvertisementList)) {
                    advHospitalVos = extensionAdvertisementList.stream()
                            .filter(adv -> {
                                String expression = adv.getExtensionCondition().getExpression();
                                String[] split = expression.split("\"");
                                if (split.length < 2) {
                                    return false;
                                }
                                String keyWord = split[1];
                                return !ObjectUtils.isEmpty(latestOrder.getDepartmentName()) && latestOrder.getDepartmentName().contains(keyWord);
                            })
                            .map(exAdv -> {
                                CustomerHospitalVo vo = new CustomerHospitalVo();
                                GetExtensionAdvertisementRespDto.ExtensionCondition condition = exAdv.getExtensionCondition();
                                vo.setAdvReferer(condition.getRefer());
                                vo.setConditionExpression(condition.getExpression());
                                vo.setResourceUrl(condition.getResourcesUrl());
                                GetExtensionAdvertisementRespDto.RecommendAdvertisementInfo advInfo = exAdv.getRecommendAdvertisementInfo();
                                vo.setName(advInfo.getTargetName());
                                vo.setLogo(advInfo.getPicture());
                                vo.setIsAdvertisement(true);
                                vo.advTags(Arrays.asList(advInfo.getTag1(), advInfo.getTag2(), advInfo.getTag3()));
                                vo.setAdvPosition(advInfo.getPosition());
                                vo.setAdvRemark(advInfo.getRemark());
                                vo.setRedirectUrl(advInfo.getTargetUrl());
                                return vo;
                            }).collect(Collectors.toList());
                }
            }
        }
        return advHospitalVos;
    }

    private List<Hospital> getHospitalListByOrderRule(List<HospitalListIndex> indexList,
                                                      List<HospitalListRule> rules,
                                                      Double longitude,
                                                      Double latitude,
                                                      List<Address> addressList,
                                                      List<Hospital> hospitalList,
                                                      List<Hospital> hospitalAreaList,
                                                      List<Hospital> allHospitals,
                                                      ApiOrderAppointment latestOrder) {
        Map<Long, HospitalListRule> ruleMap = rules.stream().collect(Collectors.toMap(HospitalListRule::getId, rule -> rule));
        List<Long> allHospitalGroupIds = rules.stream().map(HospitalListRule::getHospitalGroupId).filter(Objects::nonNull).collect(Collectors.toList());
        List<String> scaleList = rules.stream().map(HospitalListRule::getGroupScale).filter(s -> !ObjectUtils.isEmpty(s)).collect(Collectors.toList());

        scaleList.forEach(scale -> {
            try {
                List<HospitalListRule.GroupScale> scales = JsonUtils.json2list(scale, HospitalListRule.GroupScale.class);
                scales.forEach(s -> allHospitalGroupIds.add(s.getGroupId()));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        Map<Long, List<Hospital>> groupHospitalMap = getGroupMapByHospitalGroupIds(allHospitalGroupIds.stream().distinct().collect(Collectors.toList()), hospitalList);

        List<HospitalListGroupRelation> relationList = hospitalListGroupRelationMapper.selectByExample2(HospitalListGroupRelation.class, sql -> sql.andIn(HospitalListGroupRelation::getGroupId, allHospitalGroupIds));
        // 以医院组id为key，关系为value
        Map<Long, List<HospitalListGroupRelation>> relationMap = relationList.stream().collect(Collectors.toMap(HospitalListGroupRelation::getGroupId, relation -> {
            List<HospitalListGroupRelation> relations = new ArrayList<>();
            relations.add(relation);
            return relations;
        }, (oldValue, newValue) -> {
            oldValue.addAll(newValue);
            return oldValue;
        }));

        Map<Long, Address> addressMap = addressList.stream().collect(Collectors.toMap(Address::getId, address -> address));

        List<Hospital> resultHospitals = new ArrayList<>();
        indexList.forEach(index -> {
            HospitalListRule rule = ruleMap.get(index.getRuleId());
            if (rule != null) {
                List<Hospital> hospitals = new ArrayList<>();
                Long groupId = rule.getHospitalGroupId();
                switch (rule.getType()) {
                    case 1:
                        // 1.单个医院
                        hospitals = groupHospitalMap.get(groupId);
                        if (!CollectionUtils.isEmpty(hospitals)) {
                            Hospital hospital = hospitals.get(0);
                            if (index.getHospitalCount() != null && index.getHospitalCount() > 1) {
                                resultHospitals.add(hospital);
                            }
                        }
                        break;
                    case 2:
                        // 2.分组距离排序
                        hospitals = groupHospitalMap.get(groupId);
                        sortByDistance(hospitals, hospitalAreaList, addressMap, longitude, latitude, rule.getDistanceOrderBy());
                        Integer hospitalCount = index.getHospitalCount();
                        resultHospitals.addAll(hospitals.subList(0, hospitalCount > hospitals.size() ? hospitals.size() : hospitalCount));
                        break;
                    case 3:
                        // 3.分组权重排序
                        hospitals = groupHospitalMap.get(groupId);
                        if (hospitals != null && !hospitals.isEmpty()) {
                            List<HospitalListGroupRelation> relations = relationMap.get(groupId);
                            if (relations != null && !relations.isEmpty()) {
                                Comparator<HospitalListGroupRelation> comparing = Comparator.comparing(HospitalListGroupRelation::getWeight);
                                if (rule.getWeightOrderBy() == 2) {
                                    comparing = comparing.reversed();
                                }
                                List<Long> sortList = relations.stream().sorted(comparing).map(HospitalListGroupRelation::getHospitalId).collect(Collectors.toList());
                                hospitals = hospitals.stream().sorted(Comparator.comparing(h -> sortList.indexOf(h.getId()))).collect(Collectors.toList());
                                resultHospitals.addAll(hospitals.subList(0, index.getHospitalCount() > hospitals.size() ? hospitals.size() : index.getHospitalCount()));
                            }
                        }
                        break;
                    case 5:
                        // 5.分组混合距离排序
                        String scale = rule.getGroupScale();
                        List<HospitalListRule.GroupScale> scales;
                        try {
                            scales = JsonUtils.json2list(scale, HospitalListRule.GroupScale.class);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                        if (scales != null && !scales.isEmpty()) {
                            // 各分组按距离排序
                            for (HospitalListRule.GroupScale groupScale : scales) {
                                List<Hospital> groupHospitals = groupHospitalMap.get(groupScale.getGroupId());
                                sortByDistance(groupHospitals, hospitalAreaList, addressMap, longitude, latitude, rule.getDistanceOrderBy());
                                // 取规则中配置的数量
                                hospitals.addAll(groupHospitals.subList(0, groupScale.getCount() > groupHospitals.size() ? groupHospitals.size() : groupScale.getCount()));
                            }
                            // 整体再次按距离排序
                            sortByDistance(hospitals, hospitalAreaList, addressMap, longitude, latitude, rule.getDistanceOrderBy());
                        }
                        resultHospitals.addAll(hospitals.subList(0, index.getHospitalCount() > hospitals.size() ? hospitals.size() : index.getHospitalCount()));
                }
            }
        });

        pushLastHospitalToFirst(latestOrder, resultHospitals, allHospitals);
        // 使用LinkedHashSet去重，保留靠前的元素
        LinkedHashSet<Hospital> set = new LinkedHashSet<>(resultHospitals);

        return new ArrayList<>(set);
    }

    private void pushLastHospitalToFirst(ApiOrderAppointment latestOrder, List<Hospital> resultHospitals, List<Hospital> allHospitals) {
        Long lastestHospitalId = getLatestHospitalId(latestOrder);
        if (lastestHospitalId == null) return;
        if (!resultHospitals.stream().map(Hospital::getId).collect(Collectors.toList()).contains(lastestHospitalId)) {
            allHospitals.stream().filter(h -> h.getId().equals(lastestHospitalId)).findFirst().ifPresent(resultHospitals::add);
        }
        if (!CollectionUtils.isEmpty(resultHospitals)) {
            Hospital lastestHospital = resultHospitals.stream().filter(h -> lastestHospitalId.equals(h.getId())).findFirst().orElse(null);
            if (lastestHospital != null) {
                // 将最后一次挂号的医院放到第一个
                resultHospitals.remove(lastestHospital);
                resultHospitals.add(0, lastestHospital);
            }
        }
    }

    private Map<Long, List<Hospital>> getGroupMapByHospitalGroupIds(List<Long> groupIds, List<Hospital> hospitalList) {
        Map<Long, List<Hospital>> result = new HashMap<>();
        List<HospitalListGroupRelation> relationList = hospitalListGroupRelationMapper.selectByExample2(HospitalListGroupRelation.class,
                sql -> sql.andIn(HospitalListGroupRelation::getGroupId, groupIds));
        Map<Long, List<Long>> relationMap = relationList.stream().collect(Collectors.toMap(HospitalListGroupRelation::getGroupId, re -> {
            List<Long> hospitalIds = new ArrayList<>();
            hospitalIds.add(re.getHospitalId());
            return hospitalIds;
        }, (oldValue, newValue) -> {
            oldValue.addAll(newValue);
            return oldValue;
        }));
        List<Long> hospitalIds = relationList.stream().map(HospitalListGroupRelation::getHospitalId).collect(Collectors.toList());
        List<Hospital> hospitals = hospitalList.stream().filter(hospital -> hospitalIds.contains(hospital.getId())).collect(Collectors.toList());
        if (groupIds.size() == 1) {
            result.put(groupIds.get(0), hospitals);
        } else {
            groupIds.forEach(groupId -> {
                List<Long> groupHospitalIds = relationMap.get(groupId);
                if (!CollectionUtils.isEmpty(groupHospitalIds)) {
                    List<Hospital> groupHospitals = hospitals.stream().filter(hospital -> groupHospitalIds.contains(hospital.getId())).collect(Collectors.toList());
                    result.put(groupId, groupHospitals);
                }
            });
        }
        return result;
    }

    private void sortByDistance(List<Hospital> hospitals,
                                List<Hospital> hospitalAreaList,
                                Map<Long, Address> addressMap,
                                Double longitude,
                                Double latitude,
                                Integer orderType) {
        List<Long> hospitalIds = hospitalAreaList.stream()
                // 院区根据距离排序，若距离为空，则排在最后
                .sorted(Comparator.comparing(h -> {
                    Address address = addressMap.get(h.getAddressId());
                    if (address != null) {
                        Double distance = CommonUtil.getDistance(longitude, latitude, address.getLongitude(), address.getLatitude());
                        return distance != null ? distance : Double.POSITIVE_INFINITY;
                    } else {
                        return Double.POSITIVE_INFINITY;
                    }
                }))
                .map(Hospital::getParentId)
                .distinct()
                .collect(Collectors.toList());
        hospitals.sort((o1, o2) -> {
            if (!hospitalIds.contains(o1.getId())) {
                return 1;
            }
            if (!hospitalIds.contains(o2.getId())) {
                return -1;
            }
            // orderType 1:升序 2:降序
            if (orderType == 1) {
                return Integer.compare(hospitalIds.indexOf(o1.getId()), hospitalIds.indexOf(o2.getId()));
            } else {
                return Integer.compare(hospitalIds.indexOf(o2.getId()), hospitalIds.indexOf(o1.getId()));
            }
        });
    }
}
