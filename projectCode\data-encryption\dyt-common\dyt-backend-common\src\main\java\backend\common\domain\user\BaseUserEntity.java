package backend.common.domain.user;


import backend.common.domain.BaseEntity;
import backend.encryption.annotation.EncryptField;
import backend.encryption.converter.EncryptConverter;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Convert;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public abstract class BaseUserEntity extends BaseEntity {

    /**
     * 用户名（重要数据）
     * 使用影子字段迁移策略
     */
    @EncryptField(
        description = "租户用户名",
        shadowField = "name_encrypted",
        migrationStrategy = EncryptField.MigrationStrategy.SHADOW_PRIORITY,
        strategyKey = "tenant-user-name",
        algorithm = EncryptField.AlgorithmType.AES_GCM
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "name", length = 30)
    private String name;

    /**
     * 用户名加密字段（影子字段）
     * 注意：这个字段不在业务代码中直接使用，由加密框架自动管理
     */
    @Column(name = "name_encrypted", length = 1000)
    private String nameEncrypted;

    /**
     * 手机号码（重要数据）
     * 使用影子字段迁移策略
     */
    @EncryptField(
        description = "租户用户手机号码",
        shadowField = "phone_number_encrypted",
        migrationStrategy = EncryptField.MigrationStrategy.SHADOW_PRIORITY,
        strategyKey = "tenant-user-phone",
        algorithm = EncryptField.AlgorithmType.AES_GCM
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "phone_number", length = 13)
    private String phoneNumber;

    /**
     * 手机号码加密字段（影子字段）
     */
    @Column(name = "phone_number_encrypted", length = 1000)
    private String phoneNumberEncrypted;

    /**
     * 性别（不敏感数据，不加密）
     */
    @Column(name = "gender")
    private Integer gender;

    /**
     * 身份证号（注意：根据要求已移除加密）
     */
    @Column(name = "id_card_no", length = 20)
    private String idCardNo;

    /**
     * 密码（不使用加密模块，有专门的密码加密机制）
     */
    @Column(name = "password", length = 60)
    private String password;

    /**
     * 登录IP（不敏感数据，不加密）
     */
    @Column(name = "login_ip")
    private Integer loginIp;

    /**
     * 每日登录重试次数（不敏感数据，不加密）
     */
    @Column(name = "daily_login_retries")
    private Integer dailyLoginRetries;

    /**
     * 状态（不敏感数据，不加密）
     */
    @Column(name = "status")
    private Integer status;
}
