package com.ynhdkc.tenant.api.backend;

import backend.common.response.BaseOperationResponse;
import backend.security.oplog.DytSecurityRequired;
import com.ynhdkc.tenant.handler.HospitalListRuleApi;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.HospitalService;
import com.ynhdkc.tenant.service.backend.IHospitalListRuleService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
@Api(tags = "HospitalListRule")
public class HospitalListRuleController implements HospitalListRuleApi {

    private final IHospitalListRuleService hospitalListRuleService;
    private final HospitalService hospitalService;

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:list:rule:create")
    public ResponseEntity<HospitalListRuleVo> createHospitalListRule(CreateHospitalListRuleReqDto hospitalListRuleDto) {
        return ResponseEntity.ok(hospitalListRuleService.createHospitalListRule(hospitalListRuleDto));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:list:rule:delete")
    public ResponseEntity<BaseOperationResponse> deleteHospitalListRule(Long id) {
        return ResponseEntity.ok(hospitalListRuleService.deleteHospitalListRule(id));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:list:rule:get:list")
    public ResponseEntity<List<HospitalVo>> getHospitalListByRuleId(Long id) {
        return ResponseEntity.ok(hospitalService.selectHospitalListByRuleId(id));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:list:rule:get:detail")
    public ResponseEntity<HospitalListRuleVo> getHospitalListRuleDetail(Long id) {
        return ResponseEntity.ok(hospitalListRuleService.getHospitalListRuleDetail(id));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:list:rule:get:page")
    public ResponseEntity<HospitalListRulePageVo> getHospitalListRulePage(Integer currentPage, Integer pageSize) {
        return ResponseEntity.ok(hospitalListRuleService.getHospitalListRulePage(currentPage, pageSize));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:list:rule:update")
    public ResponseEntity<HospitalListRuleVo> updateHospitalListRule(Long id, UpdateHospitalListRuleReqDto hospitalListRuleDto) {
        return ResponseEntity.ok(hospitalListRuleService.updateHospitalListRule(id, hospitalListRuleDto));
    }
}
