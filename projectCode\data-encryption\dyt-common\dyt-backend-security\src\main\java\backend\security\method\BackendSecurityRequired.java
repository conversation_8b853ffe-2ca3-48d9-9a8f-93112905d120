package backend.security.method;

import java.lang.annotation.*;

/**
 * <ul>
 * <li>If tenantIdExpr attribute is not empty, evaluate it and compare with current user's tenantId. Reject when they are not equal.</li>
 * <li>If tenantManager attribute is true and current user is tenantManager, pass.</li>
 * <li>If value(authorities) attribute is not empty and current user have one of the authority, pass.</li>
 * <li>None of the above, reject.</li>
 * </ul>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface BackendSecurityRequired {
    /**
     * required authorities
     */
    String[] value() default {};

    /**
     * required tenant manager
     */
    boolean tenantManager() default false;

    /**
     * tenant_id location SpringEL
     */
    String tenantIdExpr() default "";

    /**
     * hospital_id location SpringEL
     */
    String hospitalIdExpr() default "";

    /**
     * hospital_area_id location SpringEL
     */
    String hospitalAreaIdExpr() default "";

    /**
     * department_id location SpringEL
     */
    String departmentIdExpr() default "";

    /**
     * need write op log
     */
    boolean needOpLog() default false;

    /**
     * op log only record
     */
    boolean opLogOnly() default false;
}
