package com.ynhdkc.tenant.service.customer;

import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.model.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/7/21 10:25
 */
public interface CustomerQuickCheckService {
    List<CustomerQuickCheckDepartmentVo> queryGroupedDepartsAndDoctors(CustomerQuickCheckQueryDepartmentReqDto request);

    List<Department> queryDepartmentList(Long hospitalAreaId, String quickCheckType);


    List<Doctor> queryDoctorList(Long hospitalAreaId, String quickCheckType, List<Integer> tags);

    List<CustomerQuickCheckDoctorVo> queryDoctors(CustomerQuickCheckQueryDoctorsPageReqDto request);

    ExpertAppointmentDoctorPageVo queryExpertAppointmentDoctors(List<String> displayTags, Integer current, Integer pageSize);

    List<CustomerQuickCheckAllHospitalVo> getAllHospital(Double longitude, Double latitude,Boolean hot);
}