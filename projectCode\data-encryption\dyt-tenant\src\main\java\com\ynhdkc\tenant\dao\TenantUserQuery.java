package com.ynhdkc.tenant.dao;

import backend.common.util.BaseQueryOption;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.entity.TenantUser;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-25 22:28
 */
public interface TenantUserQuery {
    Page<TenantUser> query(TenantUserQueryOption option);

    TenantUser queryByUserId(Long userId);

    List<TenantUser> queryByUserIds(Collection<Long> userIds);

    Integer countByPhoneNumber(String phoneNumber);

    int countByName(String userName);

    int countByEmail(String email);

    public List<TenantUser> queryByName(String userName);

    public List<TenantUser> queryByNameOrPhone(String userName);

    List<TenantUser> queryPasswordErrorTimesMoreThanOne();

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Accessors(chain = true)
    class TenantUserQueryOption extends BaseQueryOption {
        private Long id;
        private String nameEqual;
        private String nameLike;
        private String nicknameLike;
        private String phoneNumber;
        private String idCardNo;
        private Boolean admin;
        private Integer status;
        private Date startCreateTime;
        private Date endCreateTime;
        private Collection<Long> includingIds;
        private Collection<Long> excludingIds;
        private Collection<Long> includingRoleUserIds;
        private Collection<Long> excludingRoleUserIds;

        public TenantUserQueryOption(Integer currentPage, Integer pageSize) {
            super(currentPage, pageSize);
        }
    }
}
