package backend.common.kafka.streams;

import backend.common.exception.BizException;
import backend.common.kafka.constant.KafkaProperties;
import com.ynhdkc.tenant.model.AppointmentRuleSettingVo;
import com.ynhdkc.tenant.model.HospitalAreaVo;
import com.ynhdkc.tenant.model.HospitalKafkaVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.serialization.Serde;
import org.apache.kafka.common.serialization.Serdes;
import org.apache.kafka.streams.KeyValue;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.kafka.support.serializer.JsonSerde;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 * @since 2023/2/28 13:50:11
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "backend.kafka.global-store.info.hospital-info", havingValue = "true")
public class HospitalInfoModelGlobalStore extends GlobalStoreKafkaStreamsCallback<String, HospitalKafkaVo> {
    public static final JsonSerde<HospitalKafkaVo> VALUE_SERDE = new JsonSerde<>(HospitalKafkaVo.class).noTypeInfo();
    private final ConcurrentMap<String, HospitalKafkaVo> hospitalId2value = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, HospitalAreaVo> hospitalAreaId2value = new ConcurrentHashMap<>();

    @Override
    public void processUpdate(String key, HospitalKafkaVo update, HospitalKafkaVo old) {
        log.info("HospitalInfoModelGlobalStore processUpdate key:{}, update:{}, old:{}", key, update, old);

        if (update == null && old == null) {
            return;
        }

        if (update == null) {
            hospitalId2value.remove(key);

            if (!CollectionUtils.isEmpty(old.getHospitalAreaList())) {
                for (HospitalAreaVo hospitalAreaVo : old.getHospitalAreaList()) {
                    hospitalAreaId2value.remove(hospitalAreaVo.getId().toString());
                }
            }

            return;
        }

        hospitalId2value.put(key, update);
        if (!CollectionUtils.isEmpty(update.getHospitalAreaList())) {
            for (HospitalAreaVo hospitalAreaVo : update.getHospitalAreaList()) {
                hospitalAreaId2value.put(hospitalAreaVo.getId().toString(), hospitalAreaVo);
            }
        }
    }

    @Override
    public String storeName() {
        return "hospital-info-model";
    }

    @Override
    public String sourceTopic() {
        return KafkaProperties.HOSPITAL_TOPIC_NAME;
    }

    @Override
    public Serde<HospitalKafkaVo> valueSerde() {
        return VALUE_SERDE;
    }

    @Override
    public Serde<String> keySerde() {
        return Serdes.String();
    }

    @Override
    protected void doInitialize(KeyValue<String, HospitalKafkaVo> next) {
        log.info("HospitalInfoModelGlobalStore doInitialize key:{}, value:{}", next.key, next.value);
        if (next.value == null) {
            return;
        }
        hospitalId2value.put(next.key, next.value);

        if (!CollectionUtils.isEmpty(next.value.getHospitalAreaList())) {
            for (HospitalAreaVo hospitalAreaVo : next.value.getHospitalAreaList()) {
                hospitalAreaId2value.put(hospitalAreaVo.getId().toString(), hospitalAreaVo);
            }
        }
    }

    public @Nullable HospitalKafkaVo getHospitalInfo(@NonNull Long hospitalId) {
        return getHospitalInfo(String.valueOf(hospitalId));
    }

    public @Nullable HospitalKafkaVo getHospitalInfo(@NonNull String hospitalId) {
        return hospitalId2value.get(hospitalId);
    }

    public @Nullable HospitalAreaVo getHospitalAreaInfo(@NonNull String hospitalAreaId) {
        return hospitalAreaId2value.get(hospitalAreaId);
    }

    public @Nullable HospitalAreaVo getHospitalAreaInfoFromCode(@NonNull String hospitalAreaCode) {
        return hospitalAreaId2value.values().stream()
                .filter(hospitalAreaVo -> hospitalAreaCode.equals(hospitalAreaVo.getHospitalAreaCode())).findFirst().orElse(null);
    }

    public @NonNull Boolean canCancelAppointment(@NonNull Long hospitalAreaId) {
        HospitalAreaVo hospitalAreaVo = hospitalAreaId2value.get(hospitalAreaId.toString());
        if (hospitalAreaVo == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospitalAreaId not found");
        }
        if (hospitalAreaVo.getAppointmentSetting() == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospital area not set appointment setting");
        }
//        log.info("获取 {} 院区是否允许取消,院区配置 {}", hospitalAreaVo.getHospitalAreaCode(), hospitalAreaVo.getAppointmentSetting());
        return hospitalAreaVo.getAppointmentSetting().isSupportCancelAppointment();
    }

    /**
     * 判断这家医院院区依不依赖 his 系统
     *
     * @param hospitalAreaId 医院院区 id
     * @return true 依赖 false 不依赖
     */
    public @NonNull Boolean judgementHospitalIsDependOnHisGatewayByHospitalId(@NonNull Long hospitalAreaId) {
        HospitalAreaVo hospitalAreaVo = hospitalAreaId2value.get(hospitalAreaId.toString());
        if (hospitalAreaVo == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospitalInfoModelGlobalStore 中无法找到该院区信息");
        }
        if (hospitalAreaVo.getAppointmentSetting() == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospital area not set appointment setting");
        }
        return hospitalAreaVo.getAppointmentSetting().getSystemDepends() == 0;
    }


    /**
     * 判断这家系统在重构有没有上线
     */
    public @NonNull Boolean isUseRefactorSystem(@NonNull Long hospitalAreaId) {
        final HospitalAreaVo hospitalAreaVo = hospitalAreaId2value.get(hospitalAreaId.toString());
        if (hospitalAreaVo == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospitalInfoModelGlobalStore 中无法找到该院区信息" + hospitalAreaId);
        }
        if (hospitalAreaVo.getAppointmentSetting() == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, hospitalAreaVo.getHospitalAreaCode() + "未设置预约配置信息");
        }
        return hospitalAreaVo.getAppointmentSetting().getCurrentSystemType().equals(2);
    }


    /**
     * 是否允许当天预约
     *
     * @param hospitalAreaId 院区id
     * @return true 允许 false 不允许
     */
    public @NonNull Boolean allowAppointmentToday(@NonNull Long hospitalAreaId) {
        HospitalAreaVo hospitalAreaVo = hospitalAreaId2value.get(hospitalAreaId.toString());
        if (hospitalAreaVo == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospitalInfoModelGlobalStore 中无法找到该院区信息");
        }
        if (hospitalAreaVo.getAppointmentSetting() == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospital area not set appointment setting");
        }
//        log.info("获取 {} 院区是否允许预约当天号,院区配置 {}", hospitalAreaVo.getHospitalAreaCode(), hospitalAreaVo.getAppointmentSetting());
        return hospitalAreaVo.getAppointmentSetting().isAppointToday();
    }

    public @NonNull String getAppointRule(@NonNull Long hospitalAreaId) {
        HospitalAreaVo hospitalAreaVo = hospitalAreaId2value.get(hospitalAreaId.toString());
        if (hospitalAreaVo == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospitalInfoModelGlobalStore 中无法找到该院区信息");
        }
        if (hospitalAreaVo.getAppointmentSetting() == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospital area not set appointment setting");
        }
        return hospitalAreaVo.getAppointmentSetting().getAppointmentRule();
    }

    /**
     * 获取可提前预约的天数
     *
     * @param hospitalAreaId 院区id
     * @return 可提前预约的天数
     */
    public @NonNull Integer getAdvanceDay(@NonNull Long hospitalAreaId) {
        HospitalAreaVo hospitalAreaVo = hospitalAreaId2value.get(hospitalAreaId.toString());
        if (hospitalAreaVo == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospitalInfoModelGlobalStore 中无法找到该院区信息");
        }
        if (hospitalAreaVo.getAppointmentSetting() == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospital area not set appointment setting");
        }
//        log.info("获取 {} 院区提前几天预约,院区配置 {}", hospitalAreaVo.getHospitalAreaCode(), hospitalAreaVo.getAppointmentSetting());
        return hospitalAreaVo.getAppointmentSetting().getAdvanceDay();
    }


    // 获取停止预约的时间
    public @NonNull String getStopAppointTime(@NonNull Long hospitalAreaId) {
        HospitalAreaVo hospitalAreaVo = hospitalAreaId2value.get(hospitalAreaId.toString());
        if (hospitalAreaVo == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospitalInfoModelGlobalStore 中无法找到该院区信息");
        }
        if (hospitalAreaVo.getAppointmentSetting() == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospital area not set appointment setting");
        }
//        log.info("获取 {} 院区停止预约的时间,院区配置 {}", hospitalAreaVo.getHospitalAreaCode(), hospitalAreaVo.getAppointmentSetting());
        return hospitalAreaVo.getAppointmentSetting().getStopAppointTime();
    }

    // 获取院区配置
    public @NonNull AppointmentRuleSettingVo getAppointmentSetting(@NonNull Long hospitalAreaId) {
        HospitalAreaVo hospitalAreaVo = hospitalAreaId2value.get(hospitalAreaId.toString());
        if (hospitalAreaVo == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospitalInfoModelGlobalStore 中无法找到该院区信息");
        }
        if (hospitalAreaVo.getAppointmentSetting() == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospital area not set appointment setting");
        }
//        log.info("获取 {} 院区停止预约的时间,院区配置 {}", hospitalAreaVo.getHospitalAreaCode(), hospitalAreaVo.getAppointmentSetting());
        return hospitalAreaVo.getAppointmentSetting();
    }

    // 每天什么时候开始显示号源
    public @NonNull String getShowScheduleTime(@NonNull Long hospitalAreaId) {
        HospitalAreaVo hospitalAreaVo = hospitalAreaId2value.get(hospitalAreaId.toString());
        if (hospitalAreaVo == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospitalInfoModelGlobalStore 中无法找到该院区信息");
        }
        if (hospitalAreaVo.getAppointmentSetting() == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospital area not set appointment setting");
        }
//        log.info("获取 {} 院区每天什么时候开始显示号源,院区配置 {}", hospitalAreaVo.getHospitalAreaCode(), hospitalAreaVo.getAppointmentSetting());
        return hospitalAreaVo.getAppointmentSetting().getDisplayTime();
    }


    public @NonNull Boolean getDisplayQueueNumber(@NonNull Long hospitalAreaId) {
        HospitalAreaVo hospitalAreaVo = hospitalAreaId2value.get(hospitalAreaId.toString());
        if (hospitalAreaVo == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospitalInfoModelGlobalStore 中无法找到该院区信息");
        }
        if (hospitalAreaVo.getAppointmentSetting() == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospital area not set appointment setting");
        }
//        log.info("获取 {} 院区是否显示序号,院区配置 {}", hospitalAreaVo.getHospitalAreaCode(), hospitalAreaVo.getAppointmentSetting());
        return hospitalAreaVo.getAppointmentSetting().isDisplayNo();
    }

    public @NonNull Integer getHospitalAreaDepends(@NonNull Long hospitalAreaId) {
        final HospitalAreaVo hospitalAreaVo = hospitalAreaId2value.get(hospitalAreaId.toString());
        if (hospitalAreaVo == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospitalInfoModelGlobalStore 中无法找到该院区信息");
        }
        if (hospitalAreaVo.getAppointmentSetting() == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospital area not set appointment setting");
        }
        return hospitalAreaVo.getAppointmentSetting().getSystemDepends();
    }

    public @NonNull Boolean needPayment(@NonNull Long hospitalAreaId) {
        final HospitalAreaVo hospitalAreaVo = hospitalAreaId2value.get(hospitalAreaId.toString());
        if (hospitalAreaVo == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospitalInfoModelGlobalStore 中无法找到该院区信息");
        }
        if (hospitalAreaVo.getAppointmentSetting() == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospital area not set appointment setting");
        }
//        log.info("获取 {} 院区是否需要支付,院区配置 {}", hospitalAreaVo.getHospitalAreaCode(), hospitalAreaVo.getAppointmentSetting());
        return hospitalAreaVo.getAppointmentSetting().isNeedPayment();
    }

    public @NonNull Boolean isSupplyLockNumber(@NonNull Long hospitalAreaId) {
        final HospitalAreaVo hospitalAreaVo = hospitalAreaId2value.get(hospitalAreaId.toString());
        if (hospitalAreaVo == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospitalInfoModelGlobalStore 中无法找到该院区信息");
        }
        if (hospitalAreaVo.getAppointmentSetting() == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospital area not set appointment setting");
        }
        return hospitalAreaVo.getAppointmentSetting().isSupportLockingAppointmentNo();
    }

    public int getPaymentCloseDuration(@NonNull Long hospitalAreaId) {
        final HospitalAreaVo hospitalAreaVo = hospitalAreaId2value.get(hospitalAreaId.toString());
        if (hospitalAreaVo == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospitalInfoModelGlobalStore 中无法找到该院区信息");
        }
        if (hospitalAreaVo.getAppointmentSetting() == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospital area not set appointment setting");
        }
//        log.info("获取 {} 院区支付超时时间,院区配置 {}", hospitalAreaVo.getHospitalAreaCode(), hospitalAreaVo.getAppointmentSetting());
        return hospitalAreaVo.getAppointmentSetting().getPaymentCloseDuration().intValue();
    }

    public @Nullable Boolean isNeedPartitionTime(@NonNull Long hospitalAreaId) {
        final HospitalAreaVo hospitalAreaVo = hospitalAreaId2value.get(hospitalAreaId.toString());
        if (hospitalAreaVo == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospitalInfoModelGlobalStore 中无法找到该院区信息");
        }
        if (hospitalAreaVo.getAppointmentSetting() == null) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "hospital area not set appointment setting");
        }
//        log.info("获取 {} 院区是否分时段,院区配置 {}", hospitalAreaVo.getHospitalAreaCode(), hospitalAreaVo.getAppointmentSetting());
        return hospitalAreaVo.getAppointmentSetting().isNeedPartitionTime();
    }

}
