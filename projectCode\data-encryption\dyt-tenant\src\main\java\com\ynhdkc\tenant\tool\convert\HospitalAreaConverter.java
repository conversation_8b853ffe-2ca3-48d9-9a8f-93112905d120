package com.ynhdkc.tenant.tool.convert;

import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.model.CustomerHospitalAreaDetailVo;
import com.ynhdkc.tenant.model.CustomerHospitalAreaVo;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/8/30 9:52
 */
public class HospitalAreaConverter {
    private HospitalAreaConverter() {
    }

    public static CustomerHospitalAreaDetailVo toCustomerHospitalAreaDetailVo(Hospital entity) {
        CustomerHospitalAreaDetailVo vo = new CustomerHospitalAreaDetailVo();
        setCustomerHospitalAreaVo(vo, entity);
        vo.setPicture(entity.getPictures());
        vo.setContactPhoneNumber(entity.getContactPhoneNumber());
        vo.setAppointmentSchedulingTime(entity.getAppointmentSchedulingTime());
        vo.setAnnouncement(entity.getAnnouncement());
        vo.setIntroduction(entity.getIntroduction());
        vo.setEnvironment(entity.getEnvironment());
        vo.setDisplayGuide(entity.getDisplayGuide());
        vo.setMapKeyword(entity.getMapKeyword());
        vo.setDisplayFloor(entity.getDisplayFloor());
        vo.setStopServiceBeginTime(entity.getStopServiceBeginTime());
        vo.setStopServiceEndTime(entity.getStopServiceEndTime());
        return vo;
    }

    private static <T extends CustomerHospitalAreaVo> void setCustomerHospitalAreaVo(T vo, Hospital entity) {
        vo.setId(entity.getId());
        vo.setHospitalAreaCode(entity.getHospitalCode());
        vo.setName(entity.getName());
        vo.setHospitalId(entity.getParentId());
        vo.setStatus(entity.getStatus());
        vo.setDisplay(entity.getDisplay());
        vo.setCategory(entity.getCategories());
    }
}
