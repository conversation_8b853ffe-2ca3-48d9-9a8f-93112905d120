package com.ynhdkc.tenant.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class UserInfoDtoRespFeign {
    @JsonProperty("id")
    private Long id = null;

    @JsonProperty("name")
    private String name = null;

    @JsonProperty("phone_number")
    private String phoneNumber = null;

    @JsonProperty("gender")
    private Integer gender = null;

    @JsonProperty("id_card_no")
    private String idCardNo = null;

    @JsonProperty("we_chat_profile")
    private UserWeChatProfileVo weChatProfile = null;

    @JsonProperty("old_we_chat_profile")
    private UserWeChatProfileVo oldWeChatProfile = null;


    @JsonProperty("dyt_union_key")
    private Long dytUnionKey = null;
}
