package backend.common.util;

import backend.common.enums.HospitalCode;

public class HospitalUtils {

    private HospitalUtils() {
    }

    public static boolean isKunmingMU1stHospital(String hospitalCode) {
        return HospitalCode.KUNMING_MU_FIRST_AFFILIATED_HOSPITAL.getCode().equals(hospitalCode)
                || HospitalCode.KUNMING_MU_FIRST_AFFILIATED_CG_CAMPUS_HOSPITAL.getCode().equals(hospitalCode);
    }


    public static boolean isYunnan1stPeopleHospital(String hospitalCode) {
        return HospitalCode.YUNNAN_FIRST_PEOPLE_HOSPITAL.getCode().equals(hospitalCode)
                || HospitalCode.YUNNAN_FIRST_PEOPLE_NEW_KUN_HUA_HOSPITAL.getCode().equals(hospitalCode);
    }

}
