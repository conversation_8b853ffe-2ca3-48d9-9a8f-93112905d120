package com.ynhdkc.tenant.doctor.sort.impl;

import com.ynhdkc.tenant.doctor.sort.strategy.AbstractDoctorSortStrategy;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.model.CustomerGroupedDoctorDetailVo;
import com.ynhdkc.tenant.tool.DoctorSortUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Component
public class KunKua871899SortStrategy extends AbstractDoctorSortStrategy {

    private static final Set<String> HOSPITAL_CODES = Collections.unmodifiableSet(new HashSet<>(Collections.singletonList("871899")));

    public KunKua871899SortStrategy() {
        super(HOSPITAL_CODES);
    }

    @Override
    public void sort(List<Doctor> doctors) {
        DoctorSortUtils.levelDescAndSortDescAndNameAsc(doctors);
    }

    @Override
    public void sortGroupVo(List<CustomerGroupedDoctorDetailVo> doctorGroupVos) {
        DoctorSortUtils.levelDescAndSortDescAndNameAscGroupDoc(doctorGroupVos);
    }
}
