package backend.common.util;

import java.util.Random;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-29 14:43
 */
public class RandomUtil {
    private RandomUtil() {
    }

    private static final Random RANDOM = new Random();

    /**
     * 生成随机6位的短信验证码，范围在100000-999999之间
     *
     * @return 6位验证码
     */
    public static Integer createVerifyCode() {
        return 100000 + Math.abs(RANDOM.nextInt(899999));
    }
}
