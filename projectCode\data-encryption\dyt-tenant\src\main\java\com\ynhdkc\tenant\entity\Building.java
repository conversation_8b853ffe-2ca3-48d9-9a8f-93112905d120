package com.ynhdkc.tenant.entity;

import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @since 2023/2/24 09:21:23
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "t_hospital_building")
public final class Building extends BaseEntity {

    private Long tenantId;

    private Long hospitalId;

    private Long hospitalAreaId;

    private String hospitalCode;

    private Long addressId;

    private String name;

    private Integer sort;

    private Integer status;
}
