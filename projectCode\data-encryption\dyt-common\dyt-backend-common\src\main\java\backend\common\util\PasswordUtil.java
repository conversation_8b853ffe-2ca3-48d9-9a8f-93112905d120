package backend.common.util;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-02-20 16:32
 */
public class PasswordUtil {
    private static final BCryptPasswordEncoder PASSWORD_ENCODER = new BCryptPasswordEncoder();

    private PasswordUtil() {
    }

    public static String newPassword(PasswordEncryptType type, String origin) {
        switch (type) {
            case None:
                return origin;
            case MD5:
                return calculateMD5(origin);
            case BCrypt:
                return PASSWORD_ENCODER.encode(origin);
            default:
                throw new IllegalArgumentException("Unknown password encrypt type: " + type);
        }
    }

    public static boolean isMatch(PasswordEncryptType type, String origin, String encrypted) {
        switch (type) {
            case None:
                return origin.equals(encrypted);
            case MD5:
                return isMatchMD5(origin, encrypted);
            case BCrypt:
                return PASSWORD_ENCODER.matches(origin, encrypted);
            default:
                throw new IllegalArgumentException("Unknown password encrypt type: " + type);
        }
    }

    private static boolean isMatchMD5(String origin, String encrypted) {
        return Objects.equals(calculateMD5(origin), encrypted);
    }

    private static String calculateMD5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(input.getBytes());
            byte[] digest = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b & 0xff));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        }
    }

    public enum PasswordEncryptType {
        /**
         * 不加密
         */
        None(0),
        /**
         * MD5
         */
        MD5(1),
        /**
         * BCrypt
         */
        BCrypt(2);

        PasswordEncryptType(int val) {
            this.val = val;
        }

        private final Integer val;

        public int val() {
            return val;
        }

        public static PasswordEncryptType findType(Integer val) {
            for (PasswordEncryptType type : PasswordEncryptType.values()) {
                if (type.val.equals(val)) {
                    return type;
                }
            }
            return null;
        }
    }
}
