package com.example.encryption.mybatis.service;

import com.example.encryption.mybatis.entity.MybatisUser;
import com.example.encryption.mybatis.handler.EncryptTypeHandler;
import com.example.encryption.mybatis.mapper.MybatisUserMapper;
import com.example.encryption.util.AESGCMUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * MyBatis用户业务服务类
 * 演示MyBatis环境下的加密字段业务操作
 */
@Service
@Transactional
public class MybatisUserService {

    private static final Logger logger = LoggerFactory.getLogger(MybatisUserService.class);

    @Autowired
    private MybatisUserMapper userMapper;

    /**
     * 创建用户
     */
    public MybatisUser createUser(MybatisUser user) {
        logger.info("Creating MyBatis user with username: {}", user.getUsername());

        // 检查用户名是否已存在
        if (userMapper.existsByUsername(user.getUsername())) {
            throw new RuntimeException("用户名已存在: " + user.getUsername());
        }

        // 设置时间戳
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());

        // 设置字段上下文，帮助TypeHandler识别需要加密的字段
        try {
            EncryptTypeHandler.setFieldContext(MybatisUser.class.getName(), "phone");
            EncryptTypeHandler.setFieldContext(MybatisUser.class.getName(), "email");
            EncryptTypeHandler.setFieldContext(MybatisUser.class.getName(), "idCard");
            EncryptTypeHandler.setFieldContext(MybatisUser.class.getName(), "realName");

            // 保存用户（敏感字段会自动加密）
            int result = userMapper.insertUser(user);
            if (result > 0) {
                logger.info("Successfully created MyBatis user with ID: {}", user.getId());
                return user;
            } else {
                throw new RuntimeException("Failed to create user");
            }
        } finally {
            EncryptTypeHandler.clearFieldContext();
        }
    }

    /**
     * 根据ID获取用户
     */
    @Transactional(readOnly = true)
    public Optional<MybatisUser> getUserById(Long id) {
        logger.debug("Getting MyBatis user by ID: {}", id);

        try {
            EncryptTypeHandler.setFieldContext(MybatisUser.class.getName(), "phone");
            MybatisUser user = userMapper.selectUserById(id);
            return Optional.ofNullable(user);
        } finally {
            EncryptTypeHandler.clearFieldContext();
        }
    }

    /**
     * 根据用户名获取用户
     */
    @Transactional(readOnly = true)
    public Optional<MybatisUser> getUserByUsername(String username) {
        logger.debug("Getting MyBatis user by username: {}", username);

        try {
            EncryptTypeHandler.setFieldContext(MybatisUser.class.getName(), "phone");
            MybatisUser user = userMapper.selectUserByUsername(username);
            return Optional.ofNullable(user);
        } finally {
            EncryptTypeHandler.clearFieldContext();
        }
    }

    /**
     * 获取所有用户
     */
    @Transactional(readOnly = true)
    public List<MybatisUser> getAllUsers() {
        logger.debug("Getting all MyBatis users");

        try {
            EncryptTypeHandler.setFieldContext(MybatisUser.class.getName(), "phone");
            return userMapper.selectAllUsers();
        } finally {
            EncryptTypeHandler.clearFieldContext();
        }
    }

    /**
     * 根据手机号查找用户
     * 注意：由于手机号是加密存储的，需要特殊处理
     */
    @Transactional(readOnly = true)
    public Optional<MybatisUser> getUserByPhone(String phone) {
        if (!StringUtils.hasText(phone)) {
            return Optional.empty();
        }

        logger.debug("Searching MyBatis user by phone (encrypted search)");

        // 查询所有用户，在应用层解密后匹配
        List<MybatisUser> allUsers = getAllUsers();

        for (MybatisUser user : allUsers) {
            if (phone.equals(user.getPhone())) {
                logger.debug("Found MyBatis user by phone");
                return Optional.of(user);
            }
        }

        logger.debug("No MyBatis user found with the given phone");
        return Optional.empty();
    }

    /**
     * 根据邮箱查找用户
     */
    @Transactional(readOnly = true)
    public Optional<MybatisUser> getUserByEmail(String email) {
        if (!StringUtils.hasText(email)) {
            return Optional.empty();
        }

        logger.debug("Searching MyBatis user by email (encrypted search)");

        List<MybatisUser> allUsers = getAllUsers();

        for (MybatisUser user : allUsers) {
            if (email.equals(user.getEmail())) {
                logger.debug("Found MyBatis user by email");
                return Optional.of(user);
            }
        }

        logger.debug("No MyBatis user found with the given email");
        return Optional.empty();
    }

    /**
     * 更新用户信息
     */
    public MybatisUser updateUser(MybatisUser user) {
        logger.info("Updating MyBatis user with ID: {}", user.getId());

        if (!getUserById(user.getId()).isPresent()) {
            throw new RuntimeException("用户不存在: " + user.getId());
        }

        // 设置更新时间
        user.setUpdatedAt(LocalDateTime.now());

        try {
            EncryptTypeHandler.setFieldContext(MybatisUser.class.getName(), "phone");
            EncryptTypeHandler.setFieldContext(MybatisUser.class.getName(), "email");
            EncryptTypeHandler.setFieldContext(MybatisUser.class.getName(), "idCard");
            EncryptTypeHandler.setFieldContext(MybatisUser.class.getName(), "realName");

            // 更新用户（敏感字段会自动加密）
            int result = userMapper.updateUser(user);
            if (result > 0) {
                logger.info("Successfully updated MyBatis user with ID: {}", user.getId());
                return user;
            } else {
                throw new RuntimeException("Failed to update user");
            }
        } finally {
            EncryptTypeHandler.clearFieldContext();
        }
    }

    /**
     * 删除用户
     */
    public void deleteUser(Long id) {
        logger.info("Deleting MyBatis user with ID: {}", id);

        if (!getUserById(id).isPresent()) {
            throw new RuntimeException("用户不存在: " + id);
        }

        int result = userMapper.deleteUser(id);
        if (result > 0) {
            logger.info("Successfully deleted MyBatis user with ID: {}", id);
        } else {
            throw new RuntimeException("Failed to delete user");
        }
    }

    /**
     * 根据状态获取用户列表
     */
    @Transactional(readOnly = true)
    public List<MybatisUser> getUsersByStatus(MybatisUser.UserStatus status) {
        logger.debug("Getting MyBatis users by status: {}", status);

        try {
            EncryptTypeHandler.setFieldContext(MybatisUser.class.getName(), "phone");
            return userMapper.selectUsersByStatus(status);
        } finally {
            EncryptTypeHandler.clearFieldContext();
        }
    }

    /**
     * 获取用户总数
     */
    @Transactional(readOnly = true)
    public long getUserCount() {
        return userMapper.countUsers();
    }
}
