package com.ynhdkc.tenant.service.customer.impl;


import backend.common.constants.ChannelConstant;
import backend.common.entity.dto.notification.constant.KafkaConstant;
import backend.common.entity.dto.notification.constant.MessageTypeTagEnum;
import backend.common.entity.dto.notification.request.NotificationSendRequest;
import backend.common.entity.dto.notification.request.Recipient;
import backend.common.exception.BizException;
import backend.common.util.JsonUtil;
import backend.common.util.MessageUtil;
import com.github.pagehelper.PageHelper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.ynhdkc.tenant.client.UserClient;
import com.ynhdkc.tenant.client.model.UserInfoDtoRespFeign;
import com.ynhdkc.tenant.config.HospitalConfig;
import com.ynhdkc.tenant.dao.*;
import com.ynhdkc.tenant.dao.mapper.UserDoctorSubscriptionMapper;
import com.ynhdkc.tenant.dto.UserSubscriptionHospitalAreaInfoDto;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.entity.UserDoctorSubscription;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import com.ynhdkc.tenant.enums.SubscriptionStatus;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.customer.CustomerDepartmentService;
import com.ynhdkc.tenant.service.customer.UserDoctorSubscriptionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserDoctorSubscriptionServiceImpl implements UserDoctorSubscriptionService {
    private static final String SUBSCRIPTION_HOSPITAL_AREA_KEY_PREFIX = "dyt-tenant:doctor:subscription:hospitalArea";
    private static final String DYT_OFFICIAL_ACCOUNT = "dyt-official-account";
    private static final String SUBSCRIPTION_HOSPITAL_AREA_INFO_KEY_PREFIX = "dyt-tenant:doctor:subscription:hospitalArea:info";
    private static final ZoneId BEIJING_ZONE = ZoneId.of("Asia/Shanghai");
    private static final List<String> YUNNAN_FIRST_HOSPITAL_AREA_CODE = Arrays.asList("871044", "871023");
    private final RedisTemplate<String, String> redisTemplate;
    private final HospitalAreaSettingQuery hospitalAreaSettingQuery;
    private final UserDoctorSubscriptionMapper subscriptionMapper;
    private final HospitalAreaQuery hospitalAreaQuery;
    private final DoctorQuery doctorQuery;
    private final DepartmentQuery departmentQuery;
    private final HospitalQuery hospitalQuery;
    private final CustomerDepartmentService customerDepartmentService;
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final UserClient userFeignClient;
    private final HospitalConfig hospitalConfig;
    private final Integer REDIS_LOCK_DEFAULT_TIME = 10;
    private final Integer SUBSCRIPTION_SEND_TIMES = 1;
    private final String DYT_MIN_APP = "dyt-min-app";

    private static HashMap<String, String> buildQueryMap(UserDoctorSubscription subscription, String scheduleDate) {
        final HashMap<String, String> hashMap = new HashMap<>(15);

        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("MM-dd");
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate now = LocalDate.now();

        try {
            // 验证输入日期格式
            if (!scheduleDate.matches("\\d{2}-\\d{2}")) {
                throw new IllegalArgumentException("Invalid date format: " + scheduleDate);
            }

            // 解析日期为 MonthDay 并设置当前年份
            MonthDay monthDay = MonthDay.parse(scheduleDate, inputFormatter);
            LocalDate parsedDate = monthDay.atYear(now.getYear());

            // 如果解析后的日期早于当前日期，则将年份加1
            if (parsedDate.isBefore(now)) {
                parsedDate = parsedDate.plusYears(1);
            }

            String formattedDate = parsedDate.format(outputFormatter);

            hashMap.put("hospital_area_name", subscription.getHospitalAreaName());
            hashMap.put("doctor_name", subscription.getDoctorName());
            hashMap.put("doctor_code", subscription.getDoctorCode());
            hashMap.put("department_name", subscription.getDepartmentName());
            hashMap.put("schedule_date", formattedDate);
            hashMap.put("hospital_id", subscription.getHospitalId().toString());
            hashMap.put("hospital_area_id", subscription.getHospitalAreaId().toString());
            hashMap.put("department_id", subscription.getDepartmentId().toString());
            hashMap.put("doctor_id", subscription.getDoctorId().toString());
            hashMap.put("hospital_name", subscription.getHospitalAreaName());
            if (YUNNAN_FIRST_HOSPITAL_AREA_CODE.contains(subscription.getHospitalAreaCode())) {
                hashMap.put("hospital_name", subscription.getHospitalName() + "-" + subscription.getHospitalAreaName());
            }
            hashMap.put("hospital_name", subscription.getHospitalName());
            hashMap.put("department_code", subscription.getDepartmentCode());
            hashMap.put("hospital_area_code", subscription.getHospitalAreaCode());
            hashMap.put("remark", "如有需要，点击预约!");
            return hashMap;
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid date format: " + scheduleDate, e);
        }
    }

    private static boolean checkDateTime(UserSubscriptionHospitalAreaInfoDto dto, ZonedDateTime beijingNow) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss").withZone(BEIJING_ZONE);
            LocalTime displayTime = LocalTime.parse(dto.getDisplayTime(), formatter);
            return !beijingNow.toLocalTime().isBefore(displayTime);
        } catch (Exception e) {
            log.error("北京时区时间解析失败 displayTime={}, 异常: {}",
                    dto.getDisplayTime(), e.getMessage());
            return false;
        }
    }

    @XxlJob(value = "syncSubscriptionHospitalAreaInfo")
    public void syncSubscriptionHospitalAreaInfo() {
        List<UserDoctorSubscription> latestSubscriptions = subscriptionMapper.getSubscriptionHospitalArea();
        if (latestSubscriptions == null) {
            latestSubscriptions = Collections.emptyList();
        }

        String cachedSubscriptions = redisTemplate.opsForValue().get(SUBSCRIPTION_HOSPITAL_AREA_KEY_PREFIX);
        List<UserSubscriptionHospitalAreaInfoDto> cachedSubscriptionList = new ArrayList<>();
        if (cachedSubscriptions != null) {
            cachedSubscriptionList = JsonUtil.deserializeList(cachedSubscriptions, UserSubscriptionHospitalAreaInfoDto.class);
        }

        Set<String> hospitalCodes = latestSubscriptions.stream()
                .map(UserDoctorSubscription::getHospitalAreaCode)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(hospitalCodes)) {
            return;
        }
        List<AppointmentRuleSetting> appointmentRuleSettings = hospitalAreaSettingQuery.queryAppointmentRuleSettingBy(hospitalCodes);
        Map<Long, AppointmentRuleSetting> ruleSettingMap = appointmentRuleSettings.stream()
                .collect(Collectors.toMap(AppointmentRuleSetting::getHospitalAreaId, setting -> setting));

        Map<String, UserSubscriptionHospitalAreaInfoDto> cachedSubscriptionMap = cachedSubscriptionList.stream()
                .collect(Collectors.toMap(UserSubscriptionHospitalAreaInfoDto::getHospitalAreaCode, dto -> dto));


        List<UserSubscriptionHospitalAreaInfoDto> updatedSubscriptions = new ArrayList<>();

        for (UserDoctorSubscription subscription : latestSubscriptions) {
            String hospitalAreaCode = subscription.getHospitalAreaCode();
            UserSubscriptionHospitalAreaInfoDto existingDto = cachedSubscriptionMap.get(hospitalAreaCode);

            UserSubscriptionHospitalAreaInfoDto newDto = new UserSubscriptionHospitalAreaInfoDto();
            newDto.setHospitalAreaId(subscription.getHospitalAreaId());
            newDto.setHospitalAreaName(subscription.getHospitalAreaName());
            newDto.setHospitalAreaCode(hospitalAreaCode);

            AppointmentRuleSetting ruleSetting = ruleSettingMap.getOrDefault(
                    subscription.getHospitalAreaId(),
                    new AppointmentRuleSetting()
            );

            newDto.setSourceActivateTime(ruleSetting.getSourceActivateTime());
            String displayTime = hospitalConfig.getHospitalAreaTimeList().get(subscription.getHospitalAreaCode());
            if (!StringUtils.hasText(displayTime)) {
                displayTime = ruleSetting.getDisplayTime();
            }
            newDto.setDisplayTime(displayTime);

            if (existingDto != null &&
                    Objects.equals(existingDto.getSourceActivateTime(), newDto.getSourceActivateTime()) &&
                    Objects.equals(existingDto.getDisplayTime(), newDto.getDisplayTime())) {
                newDto.setSourceActivateTime(existingDto.getSourceActivateTime());
                newDto.setDisplayTime(existingDto.getDisplayTime());
                newDto.setLastCheckTime(existingDto.getLastCheckTime());
            }

            updatedSubscriptions.add(newDto);
        }
        Set<String> removedHospitalCodes = cachedSubscriptionMap.keySet().stream()
                .filter(code -> !hospitalCodes.contains(code))
                .collect(Collectors.toSet());


        updatedSubscriptions.removeIf(dto -> removedHospitalCodes.contains(dto.getHospitalAreaCode()));

        if (!updatedSubscriptions.isEmpty()) {
            redisTemplate.opsForValue().set(SUBSCRIPTION_HOSPITAL_AREA_KEY_PREFIX, JsonUtil.serializeObject(updatedSubscriptions));
        } else if (cachedSubscriptionList.isEmpty()) {
            redisTemplate.delete(SUBSCRIPTION_HOSPITAL_AREA_KEY_PREFIX);
        }
    }

    @XxlJob(value = "checkSubscriptionDisplayTimes")
    public void checkSubscriptionDisplayTimes() {
        Boolean isNeedSendMessage = Boolean.parseBoolean(XxlJobHelper.getJobParam());

        String cachedSubscriptions = redisTemplate.opsForValue().get(SUBSCRIPTION_HOSPITAL_AREA_KEY_PREFIX);
        List<UserSubscriptionHospitalAreaInfoDto> cachedSubscriptionList = new ArrayList<>();
        if (cachedSubscriptions != null) {
            cachedSubscriptionList = JsonUtil.deserializeList(cachedSubscriptions, UserSubscriptionHospitalAreaInfoDto.class);
        }
        List<UserSubscriptionHospitalAreaInfoDto> checkedSubscriptionList = cachedSubscriptionList.stream()
                .filter(dto -> {
                            ZonedDateTime beijingNow = ZonedDateTime.now().withZoneSameInstant(BEIJING_ZONE);
                            if (dto.getLastCheckTime() != null) {
                                ZonedDateTime beijingDate = dto.getLastCheckTime().toInstant()
                                        .atZone(ZoneId.systemDefault())
                                        .withZoneSameInstant(BEIJING_ZONE);
                                log.debug("检查医院号源: {},展示时间：{}，最后一次检查时间：{},当前系统时间：{}，检查结果：{}", dto.getHospitalAreaCode(), dto.getDisplayTime(), beijingDate, beijingNow.toLocalTime(), shouldCheckHospital(dto));
                            } else {
                                log.debug("检查医院号源: {},展示时间：{}，最后一次检查时间：{},当前系统时间：{},检查结果：{}", dto.getHospitalAreaCode(), dto.getDisplayTime(), "wu", beijingNow.toLocalTime(), shouldCheckHospital(dto));
                            }

                            return shouldCheckHospital(dto);
                        }
                ).collect(Collectors.toList());
        log.info("需要检查的医院：{}", MessageUtil.object2JSONString(checkedSubscriptionList));
        if (!CollectionUtils.isEmpty(checkedSubscriptionList)) {
            for (UserSubscriptionHospitalAreaInfoDto userSubscriptionHospitalAreaInfoDto : checkedSubscriptionList) {
                try {
                    log.info("调用检查排班方法：" + userSubscriptionHospitalAreaInfoDto.getHospitalAreaCode());
                    checkDoctorSchedule(userSubscriptionHospitalAreaInfoDto.getHospitalAreaCode(), isNeedSendMessage);
                    cachedSubscriptionList.removeIf(dto -> dto.getHospitalAreaCode().equals(userSubscriptionHospitalAreaInfoDto.getHospitalAreaCode()));
                    userSubscriptionHospitalAreaInfoDto.setLastCheckTime(new Date());
                    cachedSubscriptionList.add(userSubscriptionHospitalAreaInfoDto);
                } catch (Exception e) {
                    log.error("Failed to check schedule for hospital: " + userSubscriptionHospitalAreaInfoDto.getHospitalAreaCode() + ". Error: " + e.getMessage());
                }
            }

            updateRedisWithUpdatedSubscriptions(cachedSubscriptionList);
        } else {
            log.info("没有需要检查的医院号源");
        }
    }

    private void checkDoctorSchedule(String hospitalCode, Boolean isNeedSendMessage) {
        Example subscription = new Example(UserDoctorSubscription.class);
        subscription.createCriteria().andEqualTo("hospitalAreaCode", hospitalCode).andEqualTo("status", 0);
        List<UserDoctorSubscription> subscriptions = subscriptionMapper.selectByExample(subscription);
        AtomicInteger smsMessageCount = new AtomicInteger(0);
        if (!CollectionUtils.isEmpty(subscriptions)) {
            subscriptions.forEach(subscriptionItem -> {
                try {
                    CustomerDoctorScheduleInfoVo customerDoctorScheduleInfoVo = customerDepartmentService.queryGroupedDoctorList(subscriptionItem.getDepartmentId(), null);
                    if (!CollectionUtils.isEmpty(customerDoctorScheduleInfoVo.getAllDoctors())) {
                        CustomerAllScheduleDoctorDetailVo vo = customerDoctorScheduleInfoVo.getAllDoctors().stream().filter(doctor -> doctor.getId().equals(subscriptionItem.getDoctorId())).findFirst().orElse(null);
                        if (vo != null) {
                            if (!CollectionUtils.isEmpty(vo.getDateInWeeks())) {
                                CustomerDateInWeekDto customerDateInWeekDto = vo.getDateInWeeks().get(vo.getDateInWeeks().size() - 1);
                                if (customerDateInWeekDto.isCanOrder() && isAfterDay(customerDateInWeekDto.getDate(), subscriptionItem.getLastNotifiedTime())) {
                                    final ResponseEntity<UserInfoDtoRespFeign> userInfo = userFeignClient.getUserInfo(subscriptionItem.getUserId());
                                    if (userInfo != null && userInfo.getBody() != null && isNeedSendMessage) {
                                        final UserInfoDtoRespFeign userInfoRes = userInfo.getBody();

                                        userDoctorSubscriptionNotify(subscriptionItem, userInfoRes, customerDateInWeekDto.getDate());
                                        LocalDate now = LocalDate.now();
                                        String fullDateStr = now.getYear() + "-" + customerDateInWeekDto.getDate();

                                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                                        LocalDate localDate = LocalDate.parse(fullDateStr, formatter);

                                        java.sql.Date sqlDate = java.sql.Date.valueOf(localDate);
                                        subscriptionItem.setLastNotifiedTime(sqlDate);
                                        subscriptionItem.setNoticeCount(subscriptionItem.getNoticeCount() + 1);
                                        if (subscriptionItem.getNoticeCount() >= SUBSCRIPTION_SEND_TIMES) {
                                            subscriptionItem.setStatus(SubscriptionStatus.NOTIFICATION_COMPLETED.getCode());
                                        }
                                        smsMessageCount.incrementAndGet();
                                        subscriptionMapper.updateByPrimaryKeySelective(subscriptionItem);
                                    }
                                    log.info("订阅通知发送成功");
                                }
                            }
                        }
                    } else {
                        log.info("该医院没有排班");
                    }
                } catch (Exception e) {
                    log.error("Failed to check schedule for doctor: " + subscriptionItem.getDoctorId() + ". Error: " + e.getMessage());
                }
            });

        }
        XxlJobHelper.log("本次查询医院[{}]，共计发送消息：{}条", hospitalCode, smsMessageCount.get());
    }

    public void userDoctorSubscriptionNotify(UserDoctorSubscription subscription, UserInfoDtoRespFeign userInfoRes, String scheduleDate) {
        final NotificationSendRequest notificationSendRequest = new NotificationSendRequest();
        final Recipient recipient = new Recipient();

        if (DYT_MIN_APP.equals(subscription.getChannelTag())) {
            notificationSendRequest.setMessageTypeTag(MessageTypeTagEnum.NEW_REGISTRATION_RESOURCE_MIN_NOTIFICATION);
            recipient.setWxMpOpenId(userInfoRes.getWeChatProfile().getMiniOpenid());
        } else {
            notificationSendRequest.setMessageTypeTag(MessageTypeTagEnum.NEW_REGISTRATION_RESOURCE_NOTIFICATION);
            String openId = userInfoRes.getWeChatProfile().getOpenId();
            recipient.setWxMpOpenId(openId);
        }
        recipient.setWxMpTag(ChannelConstant.WechatMpChannel.DYT);
        final List<Recipient> recipientList = new ArrayList<>();
        recipientList.add(recipient);
        notificationSendRequest.setRecipients(recipientList);
        final HashMap<String, String> hashMap = buildQueryMap(subscription, scheduleDate);
        notificationSendRequest.setPayload(hashMap);
        notificationSendRequest.setFromApp("dyt-tenant");
        String message = MessageUtil.object2JSONString(notificationSendRequest);
        kafkaTemplate.send(KafkaConstant.SEND_TOPIC, message);
        log.info("发送订阅通知消息：{}", message);
    }

    private boolean shouldCheckHospital(UserSubscriptionHospitalAreaInfoDto dto) {
        ZonedDateTime beijingNow = ZonedDateTime.now().withZoneSameInstant(BEIJING_ZONE);
        if (dto.getLastCheckTime() == null) {
            return checkDateTime(dto, beijingNow);
        }

        boolean isSameDayResult = isSameDay(dto.getLastCheckTime(), beijingNow);
        if (!isSameDayResult) {
            return checkDateTime(dto, beijingNow);
        } else {
            return false;
        }
    }

    private boolean isSameDay(Date date, ZonedDateTime beijingNow) {
        ZonedDateTime beijingDate = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .withZoneSameInstant(BEIJING_ZONE);

        return beijingDate.toLocalDate().equals(beijingNow.toLocalDate());
    }

    private boolean isAfterDay(String date, Date lastDate) {
        LocalDate now = LocalDate.now(BEIJING_ZONE);
        String fullDateStr = now.getYear() + "-" + date;

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate localDate = LocalDate.parse(fullDateStr, formatter);

        java.sql.Date sqlDate = java.sql.Date.valueOf(localDate);

        // 将原始的Date类型转换为Instant，再转换为北京时区的时间
        Instant instant = lastDate.toInstant();
        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.systemDefault()).withZoneSameInstant(BEIJING_ZONE);
        LocalDate lastLocalDate = zonedDateTime.toLocalDate();
        java.sql.Date sqlDate2 = java.sql.Date.valueOf(lastLocalDate);

        if (sqlDate.equals(sqlDate2)) {
            return false;
        }
        return !sqlDate.before(sqlDate2);
    }

    private void updateRedisWithUpdatedSubscriptions(List<UserSubscriptionHospitalAreaInfoDto> subscriptions) {
        String serializedSubscriptions = JsonUtil.serializeObject(subscriptions);
        redisTemplate.opsForValue().set(SUBSCRIPTION_HOSPITAL_AREA_KEY_PREFIX, serializedSubscriptions);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserDoctorSubscriptionVo addSubscription(UserDoctorSubscriptionCreateDto subscriptionDto) {
        String lockKey = "subscription_lock_" + subscriptionDto.getDoctorId() + "_" + subscriptionDto.getUserId();
        try {
            if (!tryLock(lockKey, REDIS_LOCK_DEFAULT_TIME, TimeUnit.SECONDS)) {
                throw new BizException(HttpStatus.TOO_MANY_REQUESTS, "请求过于频繁，请稍后重试");
            }
            Example example = new Example(UserDoctorSubscription.class);
            example.createCriteria().andEqualTo("userId", subscriptionDto.getUserId()).andEqualTo("doctorId", subscriptionDto.getDoctorId()).andIn("status", Arrays.asList(0, 1));
            UserDoctorSubscription existUserDoctorSubscription = subscriptionMapper.selectOneByExample(example);
            if (existUserDoctorSubscription != null) {
                throw new BizException(HttpStatus.BAD_REQUEST, "该医生已订阅，禁止重复订阅");
            }
            UserDoctorSubscription entity = new UserDoctorSubscription();
            BeanUtils.copyProperties(subscriptionDto, entity);
            Doctor doctor = doctorQuery.queryDoctorById(entity.getDoctorId());
            if (doctor == null) {
                throw new BizException(HttpStatus.NOT_FOUND, "医生不存在");
            }
            entity.setDoctorCode(doctor.getThrdpartDoctorCode());
            entity.setDoctorName(doctor.getName());
            Department department = departmentQuery.queryDepartmentById(doctor.getDepartmentId());

            entity.setDepartmentCode(department.getThrdpartDepCode());
            entity.setDepartmentId(department.getId());
            entity.setDepartmentName(department.getName());
            Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaById(department.getHospitalAreaId());
            entity.setHospitalAreaName(hospitalArea.getName());
            entity.setHospitalAreaId(hospitalArea.getId());
            entity.setHospitalAreaCode(hospitalArea.getHospitalCode());
            entity.setStatus(0);
            entity.setNoticeCount(0);
            Hospital hospital = hospitalQuery.queryHospitalById(hospitalArea.getParentId());
            entity.setHospitalName(hospital.getName());
            entity.setHospitalId(hospital.getId());
            CustomerDoctorScheduleInfoVo customerDoctorScheduleInfoVo = customerDepartmentService.queryGroupedDoctorList(department.getId(), null);
            if (!CollectionUtils.isEmpty(customerDoctorScheduleInfoVo.getAllDoctors())) {
                CustomerAllScheduleDoctorDetailVo vo = customerDoctorScheduleInfoVo.getAllDoctors().stream().filter(doctorItem -> doctorItem.getId().equals(doctor.getId())).findFirst().orElse(null);
                if (vo != null) {
                    if (!CollectionUtils.isEmpty(vo.getDateInWeeks())) {
                        CustomerDateInWeekDto customerDateInWeekDto = vo.getDateInWeeks().get(vo.getDateInWeeks().size() - 1);
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                        LocalDate now = LocalDate.now();
                        String fullDateStr = now.getYear() + "-" + customerDateInWeekDto.getDate();
                        LocalDate localDate = LocalDate.parse(fullDateStr, formatter);
                        entity.setLastNotifiedTime(java.sql.Timestamp.valueOf(localDate.atStartOfDay()));
                    }
                }
            } else {
                entity.setLastNotifiedTime(new Date());
            }
            subscriptionMapper.insertSelective(entity);
            return entityConvertToVo(entity);
        } catch (Exception e) {
            log.error("订阅失败：" + e.getMessage());
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, e.getMessage());
        } finally {
            unlock(lockKey);
        }

    }

    @Override
    public void cancelUserDoctorSubscription(Long id, long userId) {
        Example example = new Example(UserDoctorSubscription.class);
        example.createCriteria().andEqualTo("doctorId", id).andEqualTo("userId", userId).andIn("status", Arrays.asList(0, 1));
        UserDoctorSubscription entity = subscriptionMapper.selectOneByExample(example);
        entity.setStatus(-1);
        entity.setCancelTime(new java.util.Date());
        subscriptionMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public List<UserDoctorSubscriptionVo> getSubscriptions(Long userId, Integer status) {
        Example example = new Example(UserDoctorSubscription.class);
        Example.Criteria criteria = example.createCriteria();

        if (userId != null) {
            criteria.andEqualTo("userId", userId);
        }
        if (status != null) {
            if (status == 0) {
                criteria.andIn("status", Arrays.asList(0, 1));
            }
            if (status == 1) {
                criteria.andIn("status", Arrays.asList(2, 3, -1));
                PageHelper.startPage(1, 10);

            }

        }
        example.orderBy("createTime").desc();

        List<UserDoctorSubscription> entities = subscriptionMapper.selectByExample(example);
        List<UserDoctorSubscriptionVo> result = new ArrayList<>();
        List<Long> departmentIds = entities.stream().map(UserDoctorSubscription::getDepartmentId).collect(Collectors.toList());
        List<Department> departments = departmentQuery.queryBy(departmentIds);
        List<Long> doctorIds = entities.stream().map(UserDoctorSubscription::getDoctorId).collect(Collectors.toList());
        List<Doctor> doctors = doctorQuery.queryBy(doctorIds);
        for (UserDoctorSubscription entity : entities) {
            UserDoctorSubscriptionVo vo = new UserDoctorSubscriptionVo();
            BeanUtils.copyProperties(entity, vo);
            Department department = departments.stream().filter(d -> d.getId().equals(entity.getDepartmentId())).findFirst().orElse(null);
            if (null != department) {
                vo.setDepartmentCode(department.getThrdpartDepCode());
                vo.setDepartmentName(department.getName());
            }
            Doctor doctor = doctors.stream().filter(d -> d.getId().equals(entity.getDoctorId())).findFirst().orElse(null);
            if (null != doctor) {
                vo.setDoctorCode(doctor.getThrdpartDoctorCode());
                vo.setDoctorName(doctor.getName());
                vo.setDoctorTitle(doctor.getRankDictLabel());
                vo.setHeadImgUrl(doctor.getHeadImgUrl());
            }
            vo.setRemainNoticeCount(3 - entity.getNoticeCount());
            result.add(vo);
        }
        return result;
    }

    @Override
    public Boolean checkUserDoctorSubscription(Long userId, Long doctorId) {
        Example example = new Example(UserDoctorSubscription.class);
        example.createCriteria().andEqualTo("userId", userId).andEqualTo("doctorId", doctorId).andIn("status", Arrays.asList(0, 1));
        return subscriptionMapper.selectCountByExample(example) > 0;
    }

    private Map<Long, List<UserDoctorSubscription>> groupByDoctorId(List<UserDoctorSubscription> subscriptions) {
        return subscriptions.stream().collect(Collectors.groupingBy(UserDoctorSubscription::getDoctorId));
    }

    private UserDoctorSubscriptionVo entityConvertToVo(UserDoctorSubscription entity) {
        UserDoctorSubscriptionVo vo = new UserDoctorSubscriptionVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    private UserDoctorSubscription dtoConvertToEntity(UserDoctorSubscriptionCreateDto vo) {
        UserDoctorSubscription entity = new UserDoctorSubscription();
        BeanUtils.copyProperties(vo, entity);
        return entity;
    }

    public boolean tryLock(String key, long timeout, TimeUnit unit) {
        String value = UUID.randomUUID().toString();
        Boolean locked = redisTemplate.opsForValue().setIfAbsent(key, value, timeout, unit);
        return locked != null && locked;
    }

    public void unlock(String key) {
        redisTemplate.delete(key);
    }

}
