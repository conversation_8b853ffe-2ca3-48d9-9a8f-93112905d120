package backend.common.util;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/11/1 11:38
 */
public class ListUtil {
    private ListUtil() {
    }

    public static <T> List<List<T>> divideList(List<T> list, int size) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        if (size <= 0) {
            throw new IllegalArgumentException("size must be greater than 0");
        }

        int count = (list.size() + size - 1) / size;
        List<List<T>> result = new ArrayList<>(count);
        for (int i = 0; i < count; i++) {
            int start = i * size;
            int end = Math.min(list.size(), (i + 1) * size);
            result.add(list.subList(start, end));
        }
        return result;
    }
}
