package backend.common.kafka;

import backend.common.exception.BizException;
import backend.common.util.JsonUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.lang.Nullable;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2023/2/28 10:37:13
 */
@Configuration
@ConditionalOnProperty(name = "backend.kafka.his.change-log.enabled", havingValue = "true")
@RequiredArgsConstructor
public class KafkaPublisher {
    private final KafkaTemplate<String, String> kafkaTemplate;

    @Transactional(rollbackFor = Exception.class)
    public void publishMsg(String topic, String key, @Nullable Object value) {
        try {
            String valueStr = null;
            if (value != null) {
                valueStr = JsonUtil.serializeObject(value);
            }
            kafkaTemplate.send(topic, key, valueStr);
        } catch (Throwable e) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void publishMsg(String topic, @Nullable Object value) {
        try {
            String valueStr = null;
            if (value != null) {
                valueStr = JsonUtil.serializeObject(value);
            }
            kafkaTemplate.send(topic, valueStr);
        } catch (Throwable e) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, e.getMessage());
        }
    }
}
