package com.ynhdkc.tenant.entity.constant;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-10 9:30
 */
public enum ReportSupportSearchTime {
    ALL(0),
    LAST_WEEK(1),
    LAST_MONTH(2),
    LAST_3_MONTH(3),
    LAST_6_MONTH(4),
    LAST_YEAR(5);

    private final int value;

    ReportSupportSearchTime(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static ReportSupportSearchTime of(Integer input) {
        if (null == input) {
            return null;
        }
        for (ReportSupportSearchTime value : ReportSupportSearchTime.values()) {
            if (value.getValue() == input) {
                return value;
            }
        }
        return null;
    }

    public static boolean contains(Integer input) {
        if (null == input) {
            return false;
        }
        for (ReportSupportSearchTime value : ReportSupportSearchTime.values()) {
            if (value.getValue() == input) {
                return true;
            }
        }
        return false;
    }
}
