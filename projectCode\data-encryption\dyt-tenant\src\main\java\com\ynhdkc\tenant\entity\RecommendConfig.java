package com.ynhdkc.tenant.entity;

import backend.common.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Table;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "t_recommend_config")
@ApiModel(value = "RecommendConfig", description = "推荐配置")
@Accessors(chain = true)
public class RecommendConfig extends BaseEntity {

    @ApiModelProperty(value = "业务类型，例如：专病专诊、特色中医，来自字典配置")
    private String bizType;

    @ApiModelProperty(value = "数据类型，例如：医院、科室、医生，来自字典配置")
    private String dataType;

    @ApiModelProperty(value = "数据标签，当在相同的 biz_type、data_type 下，通过 data_tag，可以同一 data_type 设置多次")
    private String dataTag;

    @ApiModelProperty(value = "跳转链接")
    private String redirectUrl;

    @ApiModelProperty(value = "数据全局唯一标识")
    private String dataId;

    @ApiModelProperty(value = "排序，由大到小")
    private Integer sort;

    @ApiModelProperty(value = "已启用")
    private Integer enabled;

    @ApiModelProperty(value = "展示标签")
    private String displayTags;

    @ApiModelProperty(value = "图片地址")
    private String imgUrl;

    @ApiModelProperty(value = "展示名称")
    private String displayName;
    @ApiModelProperty(value = "关键词")
    private String keyWords;
    @ApiModelProperty(value = "推荐指数")
    private Double recommendScore;


    @ApiModelProperty(value = "推荐理由")
    private String recommendReason;

    @ApiModelProperty(value = "擅长")
    private String specially;

}
