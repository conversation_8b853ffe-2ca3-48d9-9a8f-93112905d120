package com.ynhdkc.tenant.dao.impl;

import com.ynhdkc.tenant.dao.TenantRepository;
import com.ynhdkc.tenant.dao.mapper.*;
import com.ynhdkc.tenant.entity.*;
import com.ynhdkc.tenant.entity.setting.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-27 15:27
 */
@Repository
@RequiredArgsConstructor
public class TenantRepositoryImpl implements TenantRepository {
    private final TenantMapper tenantMapper;
    private final AddressMapper addressMapper;
    private final BuildingMapper buildingMapper;
    private final AreaMapper areaMapper;
    private final FloorMapper floorMapper;
    private final HospitalMapper hospitalMapper;
    private final DepartmentMapper departmentMapper;
    private final DoctorMapper doctorMapper;
    private final TenantUserStructureMapper tenantUserStructureMapper;

    private final AppointmentRuleSettingMapper appointmentRuleSettingMapper;
    private final DiagnosisPaymentSettingMapper diagnosisPaymentSettingMapper;
    private final HospitalizationSettingMapper hospitalizationSettingMapper;
    private final PatientReportSettingMapper patientReportSettingMapper;
    private final PatientCardSettingMapper patientCardSettingMapper;
    private final CustomBusinessSettingMapper customBusinessSettingMapper;
    private final FunctionSettingMapper functionSettingMapper;

    @Override
    public void create(Tenant tenant) {
        tenantMapper.insertSelective(tenant);
    }

    @Override
    public void update(Tenant tenant) {
        tenantMapper.updateByPrimaryKeySelective(tenant);
    }

    @Override
    public void delete(Long tenantId) {
        /* 获取所有需要删除的地址id */
        Set<Long> readyToDeleteAddress = new HashSet<>();
        Tenant tenant = tenantMapper.selectByPrimaryKey(tenantId);
        if (null != tenant.getAddressId()) {
            readyToDeleteAddress.add(tenant.getAddressId());
        }
        if (null != tenant.getContactAddressId()) {
            readyToDeleteAddress.add(tenant.getContactAddressId());
        }
        List<Hospital> hospitals = hospitalMapper.selectByExample2(Hospital.class, sql ->
                sql.andEqualTo(Hospital::getTenantId, tenantId));
        hospitals.stream().map(Hospital::getAddressId).forEach(readyToDeleteAddress::add);
        hospitals.forEach(hospital -> buildingMapper.selectByExample2(Building.class, sql ->
                        sql.andEqualTo(Building::getHospitalId, hospital.getId()))
                .stream()
                .map(Building::getAddressId).forEach(readyToDeleteAddress::add));

        /* 删除租户 */
        tenantMapper.deleteByPrimaryKey(tenantId);

        /* 级联删除 */
        tenantUserStructureMapper.deleteByExample2(TenantUserStructure.class, sql -> sql.andEqualTo(TenantUserStructure::getTenantId, tenantId));
        hospitalMapper.deleteByExample2(Hospital.class, sql -> sql.andEqualTo(Hospital::getTenantId, tenantId));
        buildingMapper.deleteByExample2(Building.class, sql -> sql.andEqualTo(Building::getTenantId, tenantId));
        areaMapper.deleteByExample2(Area.class, sql -> sql.andEqualTo(Area::getTenantId, tenantId));
        floorMapper.deleteByExample2(Floor.class, sql -> sql.andEqualTo(Floor::getTenantId, tenantId));
        departmentMapper.deleteByExample2(Department.class, sql -> sql.andEqualTo(Department::getTenantId, tenantId));
        doctorMapper.deleteByExample2(Doctor.class, sql -> sql.andEqualTo(Doctor::getTenantId, tenantId));
        readyToDeleteAddress.forEach(addressMapper::deleteByPrimaryKey);

        appointmentRuleSettingMapper.deleteByExample2(AppointmentRuleSetting.class, sql -> sql.andEqualTo(AppointmentRuleSetting::getTenantId, tenantId));
        diagnosisPaymentSettingMapper.deleteByExample2(DiagnosisPaymentSetting.class, sql -> sql.andEqualTo(DiagnosisPaymentSetting::getTenantId, tenantId));
        hospitalizationSettingMapper.deleteByExample2(HospitalizationSetting.class, sql -> sql.andEqualTo(HospitalizationSetting::getTenantId, tenantId));
        patientReportSettingMapper.deleteByExample2(PatientReportSetting.class, sql -> sql.andEqualTo(PatientReportSetting::getTenantId, tenantId));
        patientCardSettingMapper.deleteByExample2(PatientCardSetting.class, sql -> sql.andEqualTo(PatientCardSetting::getTenantId, tenantId));
        customBusinessSettingMapper.deleteByExample2(CustomBusinessSetting.class, sql -> sql.andEqualTo(CustomBusinessSetting::getTenantId, tenantId));
        functionSettingMapper.deleteByExample2(FunctionSetting.class, sql -> sql.andEqualTo(FunctionSetting::getTenantId, tenantId));
    }
}
