# 数据加解密POC项目

这是一个数据加解密的概念验证（POC）项目，演示了如何在Spring Boot应用中实现敏感数据的加密存储。项目提供了三种数据加密迁移方案，支持统一的注解驱动加密机制。

## 🎯 项目特性

- **统一注解驱动**：使用@EncryptField注解标注需要加密的字段，ORM框架自动处理加密
- **三种迁移方案**：备份数据库增量迁移、新表替换、渐进式影子字段
- **透明加密**：使用JPA AttributeConverter和MyBatis TypeHandler实现透明加密/解密
- **配置驱动**：通过配置文件管理加密策略，支持运行时动态调整
- **零停机迁移**：支持渐进式数据迁移，无需停服
- **共享RDS友好**：控制并发度，避免长事务，适合共享数据库环境
- **可视化管理**：集成Web管理界面，支持实时监控和操作
- **明文字段清理**：基于xxl-job的分布式清理任务，支持分表分批处理

## 🏗️ 技术栈

- **框架**：Spring Boot 2.7.18, Spring Data JPA
- **数据库**：MySQL 8.0
- **加密算法**：AES-256-GCM
- **ORM支持**：JPA + MyBatis
- **Java版本**：Java 11+

## 📁 项目结构

```
src/
├── main/java/com/example/encryption/
│   ├── annotation/          # 加密注解（@EncryptField）
│   ├── config/             # 配置类和策略管理
│   ├── controller/         # REST控制器
│   ├── entity/             # 实体类（演示加密字段配置）
│   ├── enums/              # 迁移策略枚举
│   ├── migration/          # 迁移服务和管理器
│   ├── repository/         # 数据访问层
│   ├── service/            # 业务逻辑层
│   └── util/               # 加密工具类
└── test/                   # 测试代码
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 确保环境
Java 11+
MySQL 8.0+
Maven 3.6+
```

### 2. 数据库配置

```sql
CREATE DATABASE dyt_patient;
```

修改 `src/main/resources/application.yml` 中的数据库连接信息：

```yaml
spring:
  datasource:
    url: ***************************************
    username: root
    password: Hwc132465
```

### 3. 运行项目

```bash
mvn clean compile
mvn spring-boot:run
```

### 4. 访问管理界面

启动成功后，在浏览器中访问：

- **可视化管理界面**：http://localhost:8080/
- **API文档**：http://localhost:8080/swagger-ui.html（如果配置了Swagger）

### 5. 测试API

#### 通过Web界面操作（推荐）
访问 http://localhost:8080 使用可视化界面进行操作：
- 系统状态监控
- 迁移策略切换
- 数据迁移管理
- 明文字段清理
- 实时日志查看

#### 通过命令行测试
```bash
# 创建用户（自动加密）
curl -X POST http://localhost:8080/api/migration/users \
  -H "Content-Type: application/json" \
  -d '{"username":"test","name":"张三","phone":"13900139000"}'

# 查询用户（自动解密）
curl http://localhost:8080/api/migration/users/1

# 批量迁移数据
curl -X POST http://localhost:8080/api/migration/migrate/2

# 切换策略
curl -X POST http://localhost:8080/api/strategy/global \
  -H "Content-Type: application/json" \
  -d '{"strategy":"SHADOW_PRIORITY","enableOverride":true}'

# 启动清理任务
curl -X POST http://localhost:8080/api/migration/cleanup/start?tableName=ych_patient

# 查看清理状态
curl http://localhost:8080/api/migration/cleanup/status
```

## 🔧 核心功能

### 1. 统一注解加密

```java
@Entity
public class Patient {
    // 方案一、二：直接加密模式
    @EncryptField(
        description = "患者姓名",
        migrationStrategy = MigrationStrategy.DIRECT_ENCRYPT,
        strategyKey = "name"
    )
    @Convert(converter = EncryptConverter.class)
    private String name;

    // 方案三：影子字段模式
    @EncryptField(
        shadowField = "name_encrypted",
        migrationStrategy = MigrationStrategy.PLAINTEXT_PRIORITY,
        strategyKey = "name"
    )
    private String name;

    @Column(name = "name_encrypted")
    private String nameEncrypted; // 影子字段
}
```

### 2. 四种迁移策略

- `DIRECT_ENCRYPT`：直接加密模式（方案一、二使用）
- `PLAINTEXT_PRIORITY`：明文字段优先（渐进式迁移第一阶段）
- `SHADOW_PRIORITY`：影子字段优先（渐进式迁移第二阶段）
- `SHADOW_ONLY`：仅影子字段（最终状态）

### 3. 配置驱动管理

```yaml
encryption:
  migration:
    default-strategy: PLAINTEXT_PRIORITY
    enable-global-override: false
    strategies:
      name: PLAINTEXT_PRIORITY
      phone: PLAINTEXT_PRIORITY
      idCardNo: PLAINTEXT_PRIORITY
```

## 📋 三种迁移方案

### 方案一：备份数据库增量迁移
- **停服时间**：2-4小时
- **适用场景**：可接受停服的场景
- **技术特点**：使用@EncryptField注解，JPA自动处理加密

### 方案二：新表替换
- **停服时间**：5-15分钟
- **适用场景**：要求快速切换的场景
- **技术特点**：原表和新表都使用统一注解，自动加密

### 方案三：渐进式影子字段（推荐）
- **停服时间**：0分钟
- **适用场景**：零停机要求的核心业务
- **技术特点**：完整的统一加密组件实现

## 🛡️ 安全特性

- **AES-256-GCM加密**：业界标准加密算法，提供加密和认证
- **透明化处理**：业务代码无需关心加密逻辑
- **密钥管理**：支持外部密钥配置
- **共享RDS友好**：小批次处理，避免长事务
- **数据完整性**：GCM模式防止数据被篡改

## 🖥️ Web管理界面

项目集成了完整的Web管理界面，提供可视化的操作和监控功能：

### 功能模块
- **系统状态概览**：实时显示迁移状态、清理进度、加密字段数量等关键指标
- **迁移策略管理**：可视化切换迁移策略，支持一键切换和紧急回滚
- **数据迁移管理**：启动迁移任务、验证迁移结果、查看迁移统计
- **明文字段清理管理**：启动/暂停/恢复清理任务，实时监控清理进度
- **操作日志**：记录所有操作和系统事件，便于审计和排错

### 技术特性
- **自动刷新**：每5秒自动刷新数据，实时监控系统状态
- **响应式设计**：适配不同屏幕尺寸，支持移动端访问
- **实时进度条**：直观显示迁移和清理进度
- **操作确认**：重要操作需要确认，防止误操作

### 访问方式
启动应用后访问：http://localhost:8080

## 📖 文档说明

项目包含完整的技术文档：

- `肿瘤医院公众后台安全整改专项方案.md` - 完整的安全整改方案
- `数据加解密方案完整技术文档.md` - 详细的技术实现文档
- `三种数据加密方案对比总结.md` - 方案对比分析
- `统一注解加密方案技术要点.md` - 核心技术要点
- `共享RDS环境数据加密方案技术要点.md` - 共享环境适配要点
- `明文字段清理方案技术文档.md` - 明文字段清理详细方案
- `前端集成说明.md` - Web界面集成和使用说明

## 🎯 核心优势

1. **开发简化**：无需手动调用加密算法，注解驱动自动处理
2. **维护便利**：加密逻辑集中管理，易于调试和维护
3. **扩展性强**：同时支持JPA和MyBatis，策略可扩展
4. **业务友好**：对现有业务代码影响最小
5. **运维友好**：配置驱动，支持运行时动态调整

## 📄 许可证

MIT License
