package com.example.encryption;

import com.example.encryption.annotation.EncryptField;
import com.example.encryption.entity.EncryptionStrategy;
import com.example.encryption.service.DynamicStrategyService;
import com.example.encryption.util.EncryptionUtil;
import com.example.encryption.util.SM2Util;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 动态策略和SM2算法测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class DynamicStrategyAndSM2Test {

    @Autowired
    private DynamicStrategyService dynamicStrategyService;

    @Test
    public void testSM2Encryption() {
        System.out.println("=== 测试SM2加密算法 ===");
        
        String originalText = "这是一个测试身份证号：110101199001011234";
        System.out.println("原文: " + originalText);
        
        // 测试SM2加密
        String encrypted = SM2Util.encrypt(originalText);
        System.out.println("SM2加密后: " + encrypted);
        assertNotNull(encrypted);
        assertNotEquals(originalText, encrypted);
        
        // 测试SM2解密
        String decrypted = SM2Util.decrypt(encrypted);
        System.out.println("SM2解密后: " + decrypted);
        assertEquals(originalText, decrypted);
        
        System.out.println("SM2加密测试通过！");
    }

    @Test
    public void testEncryptionUtilWithDifferentAlgorithms() {
        System.out.println("=== 测试统一加密工具类 ===");
        
        String testData = "测试数据：13812345678";
        
        // 测试AES算法
        String aesEncrypted = EncryptionUtil.encrypt(testData, EncryptionUtil.AlgorithmType.AES);
        String aesDecrypted = EncryptionUtil.decrypt(aesEncrypted, EncryptionUtil.AlgorithmType.AES);
        System.out.println("AES: " + testData + " -> " + aesEncrypted + " -> " + aesDecrypted);
        assertEquals(testData, aesDecrypted);
        
        // 测试SM2算法
        String sm2Encrypted = EncryptionUtil.encrypt(testData, EncryptionUtil.AlgorithmType.SM2);
        String sm2Decrypted = EncryptionUtil.decrypt(sm2Encrypted, EncryptionUtil.AlgorithmType.SM2);
        System.out.println("SM2: " + testData + " -> " + sm2Encrypted + " -> " + sm2Decrypted);
        assertEquals(testData, sm2Decrypted);
        
        // 验证不同算法产生不同的密文
        assertNotEquals(aesEncrypted, sm2Encrypted);
        
        System.out.println("统一加密工具类测试通过！");
    }

    @Test
    public void testDynamicStrategyService() {
        System.out.println("=== 测试动态策略服务 ===");
        
        // 获取所有策略
        Map<String, EncryptionStrategy> strategies = dynamicStrategyService.getAllStrategies();
        System.out.println("当前策略数量: " + strategies.size());
        assertTrue(strategies.size() > 0);
        
        // 检查预设策略
        assertTrue(strategies.containsKey("phone"));
        assertTrue(strategies.containsKey("email"));
        assertTrue(strategies.containsKey("idCard"));
        assertTrue(strategies.containsKey("realName"));
        
        // 验证身份证策略使用SM2算法
        EncryptionStrategy idCardStrategy = strategies.get("idCard");
        assertEquals("SM2", idCardStrategy.getAlgorithmType());
        assertEquals(EncryptField.MigrationStrategy.SHADOW_PRIORITY, idCardStrategy.getMigrationStrategy());
        
        System.out.println("身份证策略: " + idCardStrategy.getSummary());
        System.out.println("动态策略服务测试通过！");
    }

    @Test
    public void testDynamicStrategyUpdate() {
        System.out.println("=== 测试动态策略更新 ===");
        
        // 创建新策略
        String strategyKey = "testField";
        EncryptionStrategy newStrategy = dynamicStrategyService.updateStrategy(
            strategyKey,
            EncryptField.MigrationStrategy.DIRECT_ENCRYPT,
            "SM2",
            "测试字段策略",
            "test"
        );
        
        assertNotNull(newStrategy);
        assertEquals(strategyKey, newStrategy.getStrategyKey());
        assertEquals("SM2", newStrategy.getAlgorithmType());
        assertEquals(EncryptField.MigrationStrategy.DIRECT_ENCRYPT, newStrategy.getMigrationStrategy());
        
        // 验证策略已加载到缓存
        String algorithmType = dynamicStrategyService.getAlgorithmType(strategyKey);
        assertEquals("SM2", algorithmType);
        
        // 更新策略
        EncryptionStrategy updatedStrategy = dynamicStrategyService.updateStrategy(
            strategyKey,
            EncryptField.MigrationStrategy.SHADOW_ONLY,
            "AES",
            "更新后的测试字段策略",
            "test"
        );
        
        assertEquals("AES", updatedStrategy.getAlgorithmType());
        assertEquals(EncryptField.MigrationStrategy.SHADOW_ONLY, updatedStrategy.getMigrationStrategy());
        
        System.out.println("动态策略更新测试通过！");
    }

    @Test
    public void testStrategyBasedEncryption() {
        System.out.println("=== 测试基于策略的加密 ===");
        
        String testData = "敏感数据测试";
        
        // 测试不同策略的加密
        String phoneEncrypted = dynamicStrategyService.encryptData(testData, "phone");
        String idCardEncrypted = dynamicStrategyService.encryptData(testData, "idCard");
        
        System.out.println("手机号策略加密: " + phoneEncrypted);
        System.out.println("身份证策略加密: " + idCardEncrypted);
        
        // 验证不同策略产生不同密文（因为使用不同算法）
        assertNotEquals(phoneEncrypted, idCardEncrypted);
        
        // 测试解密
        String phoneDecrypted = dynamicStrategyService.decryptData(phoneEncrypted, "phone");
        String idCardDecrypted = dynamicStrategyService.decryptData(idCardEncrypted, "idCard");
        
        assertEquals(testData, phoneDecrypted);
        assertEquals(testData, idCardDecrypted);
        
        System.out.println("基于策略的加密测试通过！");
    }

    @Test
    public void testAlgorithmTesting() {
        System.out.println("=== 测试算法功能验证 ===");
        
        // 测试所有支持的算法
        EncryptionUtil.AlgorithmInfo[] algorithms = dynamicStrategyService.getSupportedAlgorithms();
        System.out.println("支持的算法数量: " + algorithms.length);
        
        for (EncryptionUtil.AlgorithmInfo algorithm : algorithms) {
            System.out.println("算法: " + algorithm.getName() + 
                             ", 显示名: " + algorithm.getDisplayName() + 
                             ", 支持: " + algorithm.isSupported() + 
                             ", 说明: " + algorithm.getNote());
        }
        
        // 测试策略算法
        boolean phoneTest = dynamicStrategyService.testStrategyAlgorithm("phone");
        boolean idCardTest = dynamicStrategyService.testStrategyAlgorithm("idCard");
        
        System.out.println("手机号策略算法测试: " + phoneTest);
        System.out.println("身份证策略算法测试: " + idCardTest);
        
        assertTrue(phoneTest);
        assertTrue(idCardTest);
        
        // 批量测试
        Map<String, Boolean> allTests = dynamicStrategyService.testAllStrategiesAlgorithms();
        System.out.println("批量测试结果: " + allTests);
        
        // 验证所有策略都测试通过
        for (Boolean result : allTests.values()) {
            assertTrue(result);
        }
        
        System.out.println("算法功能验证测试通过！");
    }

    @Test
    public void testStrategyStatistics() {
        System.out.println("=== 测试策略统计信息 ===");
        
        Map<String, Object> stats = dynamicStrategyService.getStrategyStatistics();
        
        System.out.println("策略统计信息:");
        stats.forEach((key, value) -> {
            System.out.println("  " + key + ": " + value);
        });
        
        assertTrue((Integer) stats.get("totalStrategies") > 0);
        assertTrue((Long) stats.get("enabledStrategies") > 0);
        assertNotNull(stats.get("globalStrategy"));
        assertNotNull(stats.get("algorithmDistribution"));
        
        @SuppressWarnings("unchecked")
        Map<String, Long> algorithmDistribution = (Map<String, Long>) stats.get("algorithmDistribution");
        assertTrue(algorithmDistribution.containsKey("AES"));
        assertTrue(algorithmDistribution.containsKey("SM2"));
        
        System.out.println("策略统计信息测试通过！");
    }

    @Test
    public void testGlobalStrategyOverride() {
        System.out.println("=== 测试全局策略覆盖 ===");
        
        // 测试当前策略
        EncryptField.MigrationStrategy currentStrategy = dynamicStrategyService.getStrategy("phone", EncryptField.MigrationStrategy.PLAINTEXT_PRIORITY);
        System.out.println("当前手机号策略: " + currentStrategy);
        
        // 启用全局策略覆盖
        dynamicStrategyService.updateGlobalStrategy(EncryptField.MigrationStrategy.SHADOW_ONLY, true, "test");
        
        // 验证全局策略生效
        EncryptField.MigrationStrategy overriddenStrategy = dynamicStrategyService.getStrategy("phone", EncryptField.MigrationStrategy.PLAINTEXT_PRIORITY);
        assertEquals(EncryptField.MigrationStrategy.SHADOW_ONLY, overriddenStrategy);
        
        // 恢复原设置
        dynamicStrategyService.updateGlobalStrategy(EncryptField.MigrationStrategy.PLAINTEXT_PRIORITY, false, "test");
        
        System.out.println("全局策略覆盖测试通过！");
    }
}
