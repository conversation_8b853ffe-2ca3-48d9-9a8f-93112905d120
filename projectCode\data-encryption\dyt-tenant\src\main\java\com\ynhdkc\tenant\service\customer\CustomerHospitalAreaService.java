package com.ynhdkc.tenant.service.customer;

import backend.common.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.entity.setting.DiagnosisPaymentSetting;
import com.ynhdkc.tenant.entity.setting.HospitalizationSetting;
import com.ynhdkc.tenant.entity.setting.PatientCardSetting;
import com.ynhdkc.tenant.entity.setting.PatientReportSetting;
import com.ynhdkc.tenant.model.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/7/19 16:36
 */
public interface CustomerHospitalAreaService {
    static CustomerHospitalAreaVo toCustomerHospitalAreaVo(Hospital entity) {
        CustomerHospitalAreaVo vo = new CustomerHospitalAreaVo();
        vo.setId(entity.getId());
        vo.setName(entity.getName());
        vo.setHospitalId(entity.getParentId());
        vo.setHospitalAreaCode(entity.getHospitalCode());
        vo.setCategory(entity.getCategories());
        vo.setSort(entity.getDisplaySort());
        vo.setStatus(entity.getStatus());
        return vo;
    }

    static CustomerDiagnosisPaymentSettingVo toDiagnosisPaymentSettingVo(DiagnosisPaymentSetting entity) {
        CustomerDiagnosisPaymentSettingVo vo = new CustomerDiagnosisPaymentSettingVo();
        vo.setSupportMergerPayment(entity.getSupportMergerPayment());
        vo.setSupportOnlineRefund(entity.getSupportOnlineRefund());
        vo.setRefundToday(entity.getRefundToday());
        vo.setStopRefundTime(entity.getStopRefundTime());
        vo.setSupportInvoice(entity.getSupportInvoice());
        if (null != entity.getSelectedPayments()) {
            vo.setSelectedPayments(JsonUtil.deserializeObject(entity.getSelectedPayments(), new TypeReference<List<String>>() {
            }));
        }
        vo.setPaymentInformation(entity.getPaymentInformation());
        return vo;
    }

    static CustomerPatientReportSettingVo toPatientReportSettingVo(PatientReportSetting entity) {
        CustomerPatientReportSettingVo vo = new CustomerPatientReportSettingVo();
        vo.setSupportReportType(entity.getSupportReportType());
        vo.setSupportSearchDateRange(entity.getSupportSearchDateRange());
        vo.setNotice(entity.getNotice());
        if (null != entity.getSupportSearchTime()) {
            vo.setSupportSearchTime(JsonUtil.deserializeObject(entity.getSupportSearchTime(), new TypeReference<List<Integer>>() {
            }));
        }
        return vo;
    }

    static CustomerPatientCardSettingVo toPatientCardSettingVo(PatientCardSetting entity) {
        CustomerPatientCardSettingVo vo = new CustomerPatientCardSettingVo();
        vo.setCardName(entity.getCardName());
        vo.setNeedPatientCard(entity.getNeedPatientCard());
        vo.setNeedRecharge(entity.getNeedRecharge());
        vo.setBindType(entity.getBindType());
        vo.setSupportPatientType(entity.getSupportPatientType());
        vo.setNeedElectronCard(entity.getNeedElectronCard());
        return vo;
    }

    static CustomerHospitalizationSettingVo toHospitalizationSettingVo(HospitalizationSetting entity) {
        CustomerHospitalizationSettingVo vo = new CustomerHospitalizationSettingVo();
        vo.setEnablePayment(entity.getEnablePayment());
        vo.setSupportOnlineRefund(entity.getSupportOnlineRefund());
        vo.setEnableInfoQuery(entity.getEnableInfoQuery());
        if (null != entity.getSelectedPayments()) {
            vo.setSelectedPayments(JsonUtil.deserializeObject(entity.getSelectedPayments(), new TypeReference<List<String>>() {
            }));
        }
        vo.setPaymentInformation(entity.getPaymentInformation());
        return vo;
    }

    CustomerHospitalAreaDetailVo getDetail(Long hospitalAreaId, String hospitalAreaCode);

    CustomerHospitalAreaDetailVo getDetailAndHospitalInfo(Long hospitalAreaId, String hospitalAreaCode);

    CustomerHospitalAreaLayoutVo getLayout(Long hospitalAreaId, String hospitalAreaCode, Integer channel);

    CustomerHospitalAreaSettingDetailVo getSettingDetail(Long hospitalAreaId, String hospitalAreaCode);

    CustomerHospitalAreaPageVo queryHospitalAreaPage(CustomerQueryHospitalAreaPageReqDto request);

    CustomerHospitalAreaPositionVo getHospitalAreaPosition(CustomerHospitalAreaPositionReqDto request);

    CustomAppointmentRuleSettingVo getAppointmentRuleSettingDetail(String hospitalAreaCode);
}
