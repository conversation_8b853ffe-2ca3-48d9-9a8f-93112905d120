package com.ynhdkc.tenant.dao.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ynhdkc.tenant.dao.DesensitizationWhiteListQuery;
import com.ynhdkc.tenant.dao.mapper.DesensitizationWhiteListMapper;
import com.ynhdkc.tenant.entity.DesensitizationWhiteList;
import com.ynhdkc.tenant.model.WhiteListPageReqDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-25 21:49
 */
@Repository
@RequiredArgsConstructor
public class DesensitizationWhiteListQueryImpl implements DesensitizationWhiteListQuery {
    private final DesensitizationWhiteListMapper DesensitizationWhiteListMapper;

    @Override
    public DesensitizationWhiteList queryDesensitizationWhiteListById(Long desensitizationWhiteListId) {
        return DesensitizationWhiteListMapper.selectByPrimaryKey(desensitizationWhiteListId);
    }

    @Override
    public Page<DesensitizationWhiteList> pageQueryDesensitizationWhiteList(WhiteListPageReqDto whiteListPageReqDto) {
        try (final Page<DesensitizationWhiteList> page = PageHelper.startPage(whiteListPageReqDto.getCurrentPage(), whiteListPageReqDto.getPageSize())) {
            return page.doSelectPage(() -> DesensitizationWhiteListMapper.selectByExample(DesensitizationWhiteList.class, sql -> {
                sql.defGroup(condition -> {
                    if (whiteListPageReqDto.getTenantName() != null) {
                        condition.andLike(DesensitizationWhiteList::getTenantName, whiteListPageReqDto.getTenantName());
                    }
                    if (whiteListPageReqDto.getTenantUserName() != null) {
                        condition.andLike(DesensitizationWhiteList::getTenantUserName, whiteListPageReqDto.getTenantUserName());
                    }
                });
                sql.builder(builder -> builder.orderByDesc(DesensitizationWhiteList::getId));
            }));
        }
    }

    @Override
    public Collection<DesensitizationWhiteList> queryByIds(Set<Long> desensitizationWhiteLists) {
        return DesensitizationWhiteListMapper.selectByExample2(DesensitizationWhiteList.class, sql -> sql.andIn(DesensitizationWhiteList::getId, desensitizationWhiteLists));
    }

    @Override
    public List<DesensitizationWhiteList> queryAll() {
        return DesensitizationWhiteListMapper.selectAll();
    }

}
