package com.ynhdkc.tenant.service.backend;

import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.entity.HospitalListGroup;
import com.ynhdkc.tenant.model.*;

import java.util.List;

/**
 * <p>
 * 医院组 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
public interface IHospitalListGroupService {
    BaseOperationResponse bindHospitals(Long id, List<GroupBindHospitalReqDto> dto);

    HospitalListGroupVo createHospitalListGroup(CreateHospitalListGroupReqDto hospitalListGroup);

    BaseOperationResponse deleteHospitalListGroup(Long id);

    HospitalListGroupDetailVo getHospitalListGroupDetail(Long id);

    HospitalListGroupPageVo getHospitalListGroupPage(Integer currentPage, Integer pageSize, Integer hospitalCount);

    HospitalListGroupVo updateHospitalListGroup(Long id, UpdateHospitalListGroupReqDto hospitalListGroupDto);

    static HospitalListGroupVo toVo(HospitalListGroup entity) {
        if (entity == null) {
            return null;
        }
        HospitalListGroupVo vo = new HospitalListGroupVo();
        vo.setId(entity.getId());
        vo.setName(entity.getName());
        vo.setDescription(entity.getDescription());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdateTime(entity.getUpdateTime());
        return vo;
    }

    static HospitalListGroupDetailVo toDetailVo(HospitalListGroup entity) {
        if (entity == null) {
            return null;
        }
        HospitalListGroupDetailVo vo = new HospitalListGroupDetailVo();
        vo.setId(entity.getId());
        vo.setName(entity.getName());
        vo.setDescription(entity.getDescription());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdateTime(entity.getUpdateTime());
        return vo;
    }

    static <T extends UpdateHospitalListGroupReqDto> HospitalListGroup toEntity(T dto) {
        if (dto == null) {
            return null;
        }
        HospitalListGroup entity = new HospitalListGroup();
        entity.setName(dto.getName());
        entity.setDescription(dto.getDescription());
        return entity;
    }

}
