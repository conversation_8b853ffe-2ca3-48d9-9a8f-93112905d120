package com.ynhdkc.tenant.api.backend;

import backend.security.oplog.DytSecurityRequired;
import com.ynhdkc.tenant.handler.BulletinConfigApi;
import com.ynhdkc.tenant.model.BulletinConfigDto;
import com.ynhdkc.tenant.model.BulletinConfigPageVo;
import com.ynhdkc.tenant.model.BulletinConfigReqDto;
import com.ynhdkc.tenant.model.BulletinConfigVo;
import com.ynhdkc.tenant.service.backend.BulletinConfigService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Api(tags = "BulletinConfig")
@RestController
@RequiredArgsConstructor
public class BulletinConfigController implements BulletinConfigApi {
    private final BulletinConfigService bulletinConfigService;

    @Override
    @DytSecurityRequired(needOpLog = true, value = "bulletin:config:add")
    public ResponseEntity<BulletinConfigVo> addBulletinConfig(BulletinConfigDto dto) {
        return ResponseEntity.ok(bulletinConfigService.create(dto));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "bulletin:config:get")
    public ResponseEntity<BulletinConfigVo> getBulletinConfigDetail(Long id) {
        return ResponseEntity.ok(bulletinConfigService.get(id));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "bulletin:config:query")
    public ResponseEntity<BulletinConfigPageVo> queryBulletinConfig(BulletinConfigReqDto request) {
        return ResponseEntity.ok(bulletinConfigService.page(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "bulletin:config:update")
    public ResponseEntity<BulletinConfigVo> updateBulletinConfig(BulletinConfigDto dto) {
        return ResponseEntity.ok(bulletinConfigService.update(dto));
    }
}
