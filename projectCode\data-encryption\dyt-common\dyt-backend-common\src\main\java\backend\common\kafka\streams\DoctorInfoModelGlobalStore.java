package backend.common.kafka.streams;

import backend.common.kafka.constant.KafkaProperties;
import com.ynhdkc.tenant.model.DoctorKafkaVo;
import com.ynhdkc.tenant.model.DoctorVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.serialization.Serde;
import org.apache.kafka.common.serialization.Serdes;
import org.apache.kafka.streams.KeyValue;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.support.serializer.JsonSerde;
import org.springframework.lang.Nullable;
import org.springframework.util.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/3/1 13:55:43
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "backend.kafka.global-store.info.doctor-info", havingValue = "true")
public class DoctorInfoModelGlobalStore extends GlobalStoreKafkaStreamsCallback<String, DoctorKafkaVo> {
    public static final JsonSerde<DoctorKafkaVo> VALUE_SERDE = new JsonSerde<>(DoctorKafkaVo.class).noTypeInfo();
    private final ConcurrentMap<String, DoctorKafkaVo> id2value = new ConcurrentHashMap<>();

    private final ConcurrentMap<String, DoctorKafkaVo> doctorMap = new ConcurrentHashMap<>();

    @Override
    public void processUpdate(String key, DoctorKafkaVo update, DoctorKafkaVo old) {
        log.info("DoctorInfoModelGlobalStore processUpdate key:{}, update:{}, old:{}", key, update, old);
        if (update == null && old == null) {
            return;
        }
        if (update == null) {
            id2value.remove(key);
            String doctorCodeKey = getDoctorCodeKey(old);
            doctorMap.remove(doctorCodeKey);
        } else {
            id2value.put(key, update);

            String doctorCodeKey = getDoctorCodeKey(update);
            doctorMap.put(doctorCodeKey, update);
        }
    }

    @Override
    public String storeName() {
        return "doctor-info-model";
    }

    @Override
    public String sourceTopic() {
        return KafkaProperties.DOCTOR_TOPIC_NAME;
    }

    @Override
    public Serde<DoctorKafkaVo> valueSerde() {
        return VALUE_SERDE;
    }

    @Override
    public Serde<String> keySerde() {
        return Serdes.String();
    }

    @Override
    protected void doInitialize(KeyValue<String, DoctorKafkaVo> next) {
        id2value.put(next.key, next.value);
        DoctorKafkaVo doctorVo = next.value;
        doctorMap.put(getDoctorCodeKey(doctorVo), doctorVo);
    }

    private String getDoctorCodeKey(DoctorKafkaVo doctorKafkaVo) {
        return getDoctorCodeKey(doctorKafkaVo.getHospitalId(), doctorKafkaVo.getThrdpartDoctorCode());
    }

    private String getDoctorCodeKey(Long hospitalId, String doctorCode) {
        return String.format("%s_%s", hospitalId, doctorCode);
    }

    public @Nullable DoctorKafkaVo getDoctorInfo(String doctorId) {
        return id2value.get(doctorId);
    }

    public Set<Long> getDoctorIds(String doctorName) {
        return id2value.values().stream()
                .filter(doctorVo -> !StringUtils.isEmpty(doctorVo.getName()) && doctorVo.getName().contains(doctorName))
                .map(DoctorVo::getId)
                .collect(Collectors.toSet());
    }

    public DoctorKafkaVo getDoctor(Long hospitalId, String doctorCode) {
        return doctorMap.get(getDoctorCodeKey(hospitalId, doctorCode));
    }

    /**
     * 获取所有的医生信息
     *
     * @return 所有医生信息
     */
    public Set<DoctorKafkaVo> getAllDoctorInfo() {
        return new HashSet<>(id2value.values());
    }

    public List<DoctorKafkaVo> getDoctorInfoList(Long hospitalAreaId) {
        return id2value.values().stream()
                .filter(doctorVo -> doctorVo.getHospitalAreaId().equals(hospitalAreaId))
                .collect(Collectors.toList());
    }

    public List<DoctorKafkaVo> getDoctorInfoList(Long hospitalAreaId, Long departmentId) {
        return id2value.values().stream()
                .filter(doctorVo -> doctorVo.getHospitalAreaId().equals(hospitalAreaId) && doctorVo.getDepartmentId().equals(departmentId))
                .collect(Collectors.toList());
    }

    public List<DoctorKafkaVo> getDoctorsBy(Long hospitalId, Long departmentId) {
        return id2value.values().stream()
                .filter(doctorVo -> doctorVo.getHospitalId().equals(hospitalId) && doctorVo.getDepartmentId().equals(departmentId))
                .collect(Collectors.toList());
    }

    public List<DoctorKafkaVo> getDoctorsBy(String hospitalCode, Long departmentId) {
        return id2value.values().stream()
                .filter(doctorVo -> doctorVo.getHospitalCode().equals(hospitalCode) && doctorVo.getDepartmentId().equals(departmentId))
                .collect(Collectors.toList());
    }
}
