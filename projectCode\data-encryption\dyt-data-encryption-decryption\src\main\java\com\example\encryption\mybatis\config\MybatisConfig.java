package com.example.encryption.mybatis.config;

import com.example.encryption.mybatis.handler.EncryptTypeHandler;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;

/**
 * MyBatis配置类
 * 配置TypeHandler和其他MyBatis相关设置
 */
@Configuration
@MapperScan("com.example.encryption.mybatis.mapper")
public class MybatisConfig {
    
    @Autowired
    private DataSource dataSource;
    
    /**
     * 配置SqlSessionFactory
     */
    @Bean
    public SqlSessionFactory sqlSessionFactory() throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        
        // 配置MyBatis设置
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        
        // 注册自定义TypeHandler
        TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
        typeHandlerRegistry.register(String.class, EncryptTypeHandler.class);
        
        // 其他配置
        configuration.setMapUnderscoreToCamelCase(true); // 下划线转驼峰
        configuration.setUseGeneratedKeys(true); // 使用生成的主键
        configuration.setDefaultExecutorType(org.apache.ibatis.session.ExecutorType.REUSE); // 重用执行器
        
        sessionFactory.setConfiguration(configuration);
        
        return sessionFactory.getObject();
    }
}
