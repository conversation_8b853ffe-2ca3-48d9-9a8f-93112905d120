package com.ynhdkc.tenant.tool;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/11/22 14:07
 */
@Slf4j
public class ColumnUpdateTool {
    public ColumnUpdateTool(String columns) {
        this.columnSet = splitColumns(columns);
    }

    private final Set<String> columnSet;

    public boolean hasColumn(String column) {
        return columnSet.contains(column);
    }

    private static Set<String> splitColumns(String columns) {
        if (!StringUtils.hasText(columns)) {
            return Collections.emptySet();
        }
        return Arrays.stream(columns.split(",")).collect(Collectors.toSet());
    }

    public static String mergeColumns(List<String> columns) {
        if (CollectionUtils.isEmpty(columns)) {
            return "";
        }
        return String.join(",", columns);
    }
}
