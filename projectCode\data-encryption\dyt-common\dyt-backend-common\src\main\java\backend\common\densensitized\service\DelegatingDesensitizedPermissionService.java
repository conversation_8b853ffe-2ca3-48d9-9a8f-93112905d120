package backend.common.densensitized.service;

import backend.common.densensitized.service.AbstractDesensitizedPermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class DelegatingDesensitizedPermissionService {

    private final AbstractDesensitizedPermissionService desensitizedPermissionService;

    @Autowired
    public DelegatingDesensitizedPermissionService(AbstractDesensitizedPermissionService desensitizedPermissionService) {
        this.desensitizedPermissionService = desensitizedPermissionService;
    }

    public boolean evaluate(String payload, String description) {
        if (desensitizedPermissionService == null) {
            log.warn("Has no desensitizedPermissionService, defaulting to un-desensitizing");
            return false;
        }
        return desensitizedPermissionService.evaluate(payload, description);
    }
}