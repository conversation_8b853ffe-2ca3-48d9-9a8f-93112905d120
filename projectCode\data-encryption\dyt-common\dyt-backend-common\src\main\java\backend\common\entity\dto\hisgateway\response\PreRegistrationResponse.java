package backend.common.entity.dto.hisgateway.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

@Data
public class PreRegistrationResponse {

    @JsonProperty("hospital_code")
    private String hospitalCode;

    @JsonProperty("medical_record_number")
    private String medicalRecordNumber;

    // 这个是排班id 返回了就会更新
    @JsonProperty("schedule_id")
    private String scheduleId;

    // 这个是看诊的序号，返回了就会更新
    @JsonProperty("visiting_serial_number")
    private String visitingSerialNumber;

    @JsonProperty("appointment_date")
    private Long appointmentDate;

    @JsonProperty("visiting_date")
    private Long visitingDate;

    @JsonProperty("expire_time")
    private Long expireTime;

    @JsonProperty("time_type")
    private Integer timeType;

    @JsonProperty("time_type_desc")
    private String timeTypeDesc;

    @JsonProperty("order_number")
    private String orderNumber;

    private Boolean result = false;

    // 别用错了，这个是his返回的锁号单号
    @JsonProperty("lock_registration_number")
    private String lockRegistrationNumber;

    // 这个是需要支付的金额
    private BigDecimal fee;

    @JsonProperty("show_schedule_date")
    private String showScheduleDate;

    @JsonProperty("visiting_address")
    private String visitingAddress;

    @JsonProperty("visiting_desc")
    private String visitingDesc;

    private String reason;

    @JsonProperty("start_date")
    private Long startDate;

    @JsonProperty("end_date")
    private Long endDate;

    @JsonProperty("other_info") // 如果设置了该字段，会放入到 order_appointment 的 mark 字段里面
    private String otherInfo;

    private Map<String, Object> rawParameters;
}
