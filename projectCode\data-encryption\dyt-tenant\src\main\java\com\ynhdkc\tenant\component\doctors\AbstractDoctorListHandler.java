package com.ynhdkc.tenant.component.doctors;

import backend.common.util.ObjectsUtils;
import com.ynhdkc.tenant.doctor.sort.context.DoctorSortContext;
import com.ynhdkc.tenant.dto.DoctorAdvanceAndForbiddenDay;
import com.ynhdkc.tenant.dto.ScheduleResponseDto;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.entity.DoctorGroupRelation;
import com.ynhdkc.tenant.entity.constant.TimePeriodDisplayType;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import com.ynhdkc.tenant.model.CustomerAllScheduleDoctorDetailVo;
import com.ynhdkc.tenant.model.CustomerDateInWeekDto;
import com.ynhdkc.tenant.model.CustomerDoctorScheduleGroupedByDateVo;
import com.ynhdkc.tenant.model.CustomerGroupedDoctorDetailVo;
import com.ynhdkc.tenant.tool.DoctorListTool;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.ynhdkc.tenant.tool.convert.DoctorConvert.toGroupedDoctorDetailVo;
import static com.ynhdkc.tenant.util.DateUtil.*;
import static java.util.Comparator.comparing;

public abstract class AbstractDoctorListHandler {

    protected static final List<String> realTimeSortHospitalCodeList = Arrays.asList("871044", "871023");

    private static final String TODAY = "今天";
    private static final String TOMORROW = "明天";

    @Resource
    protected DoctorListTool doctorListTool;

    @Resource
    protected DoctorSortContext doctorSortContext;

    // type one
    protected List<CustomerDoctorScheduleGroupedByDateVo> compositeCustomDoctorInGroup(Set<LocalDate> scheduleDates,
                                                                                       List<CustomerAllScheduleDoctorDetailVo> doctors,
                                                                                       List<ScheduleResponseDto.ScheduleInfo> rawScheduleInfoList,
                                                                                       List<DoctorGroupRelation> doctorGroupRelations,
                                                                                       AppointmentRuleSetting ruleSetting, String hospitalCode) {
        List<ScheduleResponseDto.ScheduleInfo> scheduleInfoList = rawScheduleInfoList.stream().filter(scheduleInfo -> scheduleDates.contains(convertDateToLocalDate(scheduleInfo.getSchDate()))).collect(Collectors.toList());

        Map<LocalDate, List<ScheduleResponseDto.ScheduleInfo>> doctorScheduleGroupedByDate = scheduleInfoList.stream().collect(Collectors.groupingBy(scheduleInfo -> convertDateToLocalDate(scheduleInfo.getSchDate())));
        List<CustomerDoctorScheduleGroupedByDateVo> groupedByDateVoList = doctorScheduleGroupedByDate.entrySet().stream()
                .map(dateScheduleEntry -> {
                    LocalDate date = dateScheduleEntry.getKey();
                    List<ScheduleResponseDto.ScheduleInfo> schedulesForDate = dateScheduleEntry.getValue();

                    if (date == null || CollectionUtils.isEmpty(schedulesForDate)) {
                        return null;
                    }

                    CustomerDoctorScheduleGroupedByDateVo doctorScheduleGroupedByDateVo = new CustomerDoctorScheduleGroupedByDateVo();
                    String dateInWeek = convertLocalDateToWeekday(date);
                    doctorScheduleGroupedByDateVo.setDateInWeek(dateInWeek);

                    String dateString = convertLocalDateToMMDD(date);
                    doctorScheduleGroupedByDateVo.setDoctorGroupId(dateString);
                    doctorScheduleGroupedByDateVo.setScheduleDate(dateString);

                    Map<String, List<ScheduleResponseDto.ScheduleInfo>> doctorScheduleGroupedByDoctorCode = schedulesForDate.stream().collect(Collectors.groupingBy(this::getDoctorKey));
                    List<CustomerGroupedDoctorDetailVo> doctorVos = doctorScheduleGroupedByDoctorCode.entrySet()
                            .stream()
                            .map(doctorScheduleEntry -> createCustomerGroupedDoctorDetailVo(doctorScheduleEntry, doctors, doctorGroupRelations)).filter(Objects::nonNull).collect(Collectors.toList());

                    doctorSortContext.sortGroupVo(doctorVos, hospitalCode);
                    doctorScheduleGroupedByDateVo.setDoctors(doctorVos);
                    doctorScheduleGroupedByDateVo.setCanOrder(doctorVos.stream().anyMatch(CustomerGroupedDoctorDetailVo::isCanOrder));
                    doctorScheduleGroupedByDateVo.setScheduleLocalDate(convertLocalDateToDate(date));
                    return doctorScheduleGroupedByDateVo;
                }).filter(Objects::nonNull).collect(Collectors.toList());

        if (ruleSetting.getTimePeriodDisplayType().equals(TimePeriodDisplayType.DISPLAY_ALL.getCode())) {
            scheduleDates.forEach(scheduleDate -> {
                List<CustomerDoctorScheduleGroupedByDateVo> temp = groupedByDateVoList.stream().filter(q -> q.getScheduleLocalDate().equals(convertLocalDateToDate(scheduleDate))).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(temp)) {
                    CustomerDoctorScheduleGroupedByDateVo emptyVo = new CustomerDoctorScheduleGroupedByDateVo();
                    emptyVo.setCanOrder(false);
                    emptyVo.setScheduleLocalDate(convertLocalDateToDate(scheduleDate));
                    emptyVo.setDateInWeek(convertLocalDateToWeekday(scheduleDate));
                    String dateString = convertLocalDateToMMDD(scheduleDate);
                    emptyVo.setDoctorGroupId(dateString);
                    emptyVo.setScheduleDate(dateString);
                    groupedByDateVoList.add(emptyVo);
                }
            });
        }

        groupedByDateVoList.sort(Comparator.comparing(CustomerDoctorScheduleGroupedByDateVo::getScheduleLocalDate));
        return groupedByDateVoList;
    }

    private CustomerGroupedDoctorDetailVo createCustomerGroupedDoctorDetailVo(Map.Entry<String, List<ScheduleResponseDto.ScheduleInfo>> doctorScheduleEntry,
                                                                              List<CustomerAllScheduleDoctorDetailVo> doctors,
                                                                              List<DoctorGroupRelation> doctorGroupRelations) {
        String doctorKey = doctorScheduleEntry.getKey();
        List<ScheduleResponseDto.ScheduleInfo> schedulesForDoctor = doctorScheduleEntry.getValue();
        if (doctorKey == null || CollectionUtils.isEmpty(schedulesForDoctor)) {
            return null;
        }

        CustomerAllScheduleDoctorDetailVo doctor = doctors.stream().filter(d -> getDoctorKey(d).equals(doctorKey)).findFirst().orElse(null);
        if (doctor == null) {
            return null;
        }

        CustomerGroupedDoctorDetailVo groupedDoctorDetailVo = toGroupedDoctorDetailVo(doctor);
        groupedDoctorDetailVo.setId(doctor.getId());
        groupedDoctorDetailVo.setCanOrder(schedulesForDoctor.stream().anyMatch(schedule -> null != schedule.getSrcNum() && schedule.getSrcNum() > 0));
        groupedDoctorDetailVo.setDepartmentName(doctor.getDepartmentName());

        groupedDoctorDetailVo.setDateInWeeks(schedulesForDoctor.stream().map(this::scheduleInfoToDateInWeek).collect(Collectors.toSet()).stream().sorted(comparing(CustomerDateInWeekDto::getLocalDate)).collect(Collectors.toList()));

        schedulesForDoctor.stream().findFirst().ifPresent(schedule -> groupedDoctorDetailVo.setShowSchDate(null != schedule.getShowSchDate() && schedule.getShowSchDate() == 1));

        boolean isDoctorInGroup = doctorGroupRelations.stream().anyMatch(q -> q.getDoctorId().equals(doctor.getId()));
        groupedDoctorDetailVo.setBelongDoctorGroups(isDoctorInGroup);
        return groupedDoctorDetailVo;
    }

    //================================================= forbidden days =============================================================================================
    protected List<DoctorAdvanceAndForbiddenDay> getDoctorAdvanceAndForbiddenDays(List<Doctor> doctors, Department department, AppointmentRuleSetting ruleSetting) {
        List<DoctorAdvanceAndForbiddenDay> doctorAdvanceAndForbiddenDays = new ArrayList<>();
        doctors.forEach(doctor -> {
            Pair<Integer, Integer> defaultAdvanceAndForbiddenDays = doctorListTool.defaultAdvanceAndForbiddenDays(doctor, department, ruleSetting);
            doctorAdvanceAndForbiddenDays.add(DoctorAdvanceAndForbiddenDay.builder()
                    .doctorId(doctor.getId())
                    .advanceDay(defaultAdvanceAndForbiddenDays.getLeft())
                    .forbiddenDay(defaultAdvanceAndForbiddenDays.getRight())
                    .build());
        });
        return doctorAdvanceAndForbiddenDays;
    }

    protected Pair<Integer, Integer> getMaxAdvanceAndMinForbiddenDays(List<DoctorAdvanceAndForbiddenDay> doctorAdvanceAndForbiddenDays) {
        Integer maxAdvanceDay = doctorAdvanceAndForbiddenDays.stream().map(DoctorAdvanceAndForbiddenDay::getAdvanceDay).max(Integer::compareTo).orElse(0);
        Integer minForbiddenDay = doctorAdvanceAndForbiddenDays.stream().map(DoctorAdvanceAndForbiddenDay::getForbiddenDay).min(Integer::compareTo).orElse(0);
        return Pair.of(maxAdvanceDay, minForbiddenDay);
    }

    protected CustomerDateInWeekDto scheduleInfoToDateInWeek(ScheduleResponseDto.ScheduleInfo schedule) {
        CustomerDateInWeekDto customerDateInWeek = new CustomerDateInWeekDto();
        String dateInWeek = convertDateToWeekday(schedule.getSchDate());
        if (TODAY.equals(dateInWeek)) {
            customerDateInWeek.setDateInWeek(dateInWeek);
        } else if (TOMORROW.equals(dateInWeek)) {
            customerDateInWeek.setDateInWeek(dateInWeek);
        } else {
            customerDateInWeek.setDateInWeek(convertDateToMMDD(schedule.getSchDate()));
        }

        customerDateInWeek.setDate(convertDateToMMDD(schedule.getSchDate()));
        customerDateInWeek.setLocalDate(schedule.getSchDate());
        customerDateInWeek.setCanOrder(null != schedule.getSrcNum() && schedule.getSrcNum() > 0);
        return customerDateInWeek;
    }

    protected Set<LocalDate> getScheduleDate(List<Doctor> doctors, Department department, AppointmentRuleSetting ruleSetting) {
        List<DoctorAdvanceAndForbiddenDay> doctorAdvanceAndForbiddenDays = getDoctorAdvanceAndForbiddenDays(doctors, department, ruleSetting);
        Pair<Integer, Integer> maxAdvanceAndMinForbiddenDays = getMaxAdvanceAndMinForbiddenDays(doctorAdvanceAndForbiddenDays);
        Integer maxAdvanceDay = maxAdvanceAndMinForbiddenDays.getLeft();
        Integer minForbiddenDay = maxAdvanceAndMinForbiddenDays.getRight();

        LocalDate now = LocalDate.now();
        LocalDate start = now.plusDays(minForbiddenDay);
        LocalDate end = now.plusDays(maxAdvanceDay);
        return splitLocalDateByDay(start, end);
    }


    protected boolean filterScheduleDoctor(ScheduleResponseDto.ScheduleInfo scheduleInfo, CustomerAllScheduleDoctorDetailVo doctor) {
        if (ObjectsUtils.isEmpty(scheduleInfo.getDepartmentCode())) {
            return scheduleInfo.getDoctorCode().equals(doctor.getThrdpartDoctorCode());
        }
        if (!ObjectsUtils.isEmpty(scheduleInfo.getExecDepartmentCode())) {
            return (scheduleInfo.getExecDepartmentCode().equals(doctor.getDepartmentCode()) ||
                    scheduleInfo.getDepartmentCode().equals(doctor.getDepartmentCode())) && scheduleInfo.getDoctorCode().equals(doctor.getThrdpartDoctorCode());
        }
        return scheduleInfo.getDepartmentCode().equals(doctor.getDepartmentCode()) && scheduleInfo.getDoctorCode().equals(doctor.getThrdpartDoctorCode());
    }

    private String getDoctorKey(ScheduleResponseDto.ScheduleInfo scheduleInfo) {
        return getDoctorKey(scheduleInfo.getDepartmentId(), scheduleInfo.getDoctorCode());
    }

    private String getDoctorKey(CustomerAllScheduleDoctorDetailVo customerAllScheduleDoctorDetailVo) {
        return getDoctorKey(customerAllScheduleDoctorDetailVo.getDepartmentId(), customerAllScheduleDoctorDetailVo.getThrdpartDoctorCode());
    }

    protected String getDoctorKey(Long departmentId, String doctorCode) {
        return String.format("%s_%s", departmentId, doctorCode);
    }
}
