package backend.common.cdc.listener;

import backend.common.cdc.CDCListener;
import backend.common.cdc.CDCTopics;
import backend.common.cdc.ResourceChangeCapture;
import backend.common.cdc.dto.CDCHospitalBuilding;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;

public class CDCH<PERSON>pitalBuildingListener extends CDCListener<CDCHospitalBuilding> {
    public CDCHospitalBuildingListener(ResourceChangeCapture<CDCHospitalBuilding> capture) {
        super(capture);
    }


    @KafkaListener(topics = CDCTopics.BACKEND_HOSPITAL_BUILDING, groupId = "${spring.application.name}")
    public void kafkaListener(ConsumerRecord<String, byte[]> msg) throws Exception {
        this.doWork(msg, CDCHospitalBuilding.class);
    }

}
