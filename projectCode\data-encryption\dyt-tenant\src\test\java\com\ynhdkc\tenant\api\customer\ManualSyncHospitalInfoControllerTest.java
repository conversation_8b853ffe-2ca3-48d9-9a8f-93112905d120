package com.ynhdkc.tenant.api.customer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ynhdkc.tenant.DytTenantApplication;
import com.ynhdkc.tenant.dao.DepartmentQuery;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.model.CustomerDoctorScheduleInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@SpringBootTest(classes = {DytTenantApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@Slf4j
class ManualSyncHospitalInfoControllerTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private CustomerDepartmentController customerDepartmentController;

    @Autowired
    private DepartmentQuery departmentQuery;

    @Test
    public void syncDoctorByDepartment(){

        List<Department> departments = departmentQuery.queryBy("871023");
        Set<Long> departmentIds = departments.stream().map(Department::getId).collect(Collectors.toSet());

        departmentIds.forEach(x-> {
            ResponseEntity<CustomerDoctorScheduleInfoVo> customerDoctorScheduleInfoVoResponseEntity = customerDepartmentController.queryGroupedDoctorListByDepartmentId(x, null, null,null);
            log.info("result: {}", customerDoctorScheduleInfoVoResponseEntity);
        });

    }
}
