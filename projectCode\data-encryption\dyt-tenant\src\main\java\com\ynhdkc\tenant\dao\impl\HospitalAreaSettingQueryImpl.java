package com.ynhdkc.tenant.dao.impl;

import com.ynhdkc.tenant.dao.HospitalAreaSettingQuery;
import com.ynhdkc.tenant.dao.mapper.*;
import com.ynhdkc.tenant.entity.setting.*;
import com.ynhdkc.tenant.model.HospitalDependOnHisResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-09 14:11
 */
@Repository
@RequiredArgsConstructor
public class HospitalAreaSettingQueryImpl implements HospitalAreaSettingQuery {
    private final PatientCardSettingMapper patientCardSettingMapper;
    private final AppointmentRuleSettingMapper appointmentRuleSettingMapper;
    private final DiagnosisPaymentSettingMapper diagnosisPaymentSettingMapper;
    private final PatientReportSettingMapper patientReportSettingMapper;
    private final HospitalizationSettingMapper hospitalizationSettingMapper;
    private final CustomBusinessSettingMapper customBusinessSettingMapper;

    @Override
    public Optional<PatientCardSetting> queryPatientCardSettingById(@Nullable Long tenantId, @Nullable Long hospitalId, @Nullable Long hospitalAreaId) {
        List<PatientCardSetting> patientCardSettings = patientCardSettingMapper.selectByExample(PatientCardSetting.class,
                helper -> helper.defGroup(q -> {
                    if (tenantId != null) {
                        q.andEqualTo(PatientCardSetting::getTenantId, tenantId);
                    }
                    if (hospitalId != null) {
                        q.andEqualTo(PatientCardSetting::getHospitalId, hospitalId);
                    }
                    if (hospitalAreaId != null) {
                        q.andEqualTo(PatientCardSetting::getHospitalAreaId, hospitalAreaId);
                    }
                }));
        return Optional.ofNullable(CollectionUtils.isEmpty(patientCardSettings) ? null : patientCardSettings.get(0));
    }

    @Override
    public Optional<AppointmentRuleSetting> queryAppointmentRuleSettingById(@Nullable Long tenantId, @Nullable Long hospitalId, @Nullable Long hospitalAreaId) {
        List<AppointmentRuleSetting> appointmentRuleSettings = appointmentRuleSettingMapper.selectByExample(AppointmentRuleSetting.class,
                helper -> helper.defGroup(q -> {
                    if (tenantId != null) {
                        q.andEqualTo(AppointmentRuleSetting::getTenantId, tenantId);
                    }
                    if (hospitalId != null) {
                        q.andEqualTo(AppointmentRuleSetting::getHospitalId, hospitalId);
                    }
                    if (hospitalAreaId != null) {
                        q.andEqualTo(AppointmentRuleSetting::getHospitalAreaId, hospitalAreaId);
                    }
                }));
        return Optional.ofNullable(CollectionUtils.isEmpty(appointmentRuleSettings) ? null : appointmentRuleSettings.get(0));
    }

    @Override
    public Optional<AppointmentRuleSetting> queryAppointmentRuleSettingBy(@Nullable String hospitalCode) {
        List<AppointmentRuleSetting> appointmentRuleSettings = appointmentRuleSettingMapper.selectByExample(AppointmentRuleSetting.class,
                helper -> helper.defGroup(q -> q.andEqualTo(AppointmentRuleSetting::getHospitalCode, hospitalCode)));
        return Optional.ofNullable(CollectionUtils.isEmpty(appointmentRuleSettings) ? null : appointmentRuleSettings.get(0));
    }

    @Override
    public Optional<DiagnosisPaymentSetting> queryDiagnosisPaymentSettingById(@Nullable Long tenantId, @Nullable Long hospitalId, @Nullable Long hospitalAreaId) {
        List<DiagnosisPaymentSetting> diagnosisPaymentSettings = diagnosisPaymentSettingMapper.selectByExample(DiagnosisPaymentSetting.class,
                helper -> helper.defGroup(q -> {
                    if (tenantId != null) {
                        q.andEqualTo(DiagnosisPaymentSetting::getTenantId, tenantId);
                    }
                    if (hospitalId != null) {
                        q.andEqualTo(DiagnosisPaymentSetting::getHospitalId, hospitalId);
                    }
                    if (hospitalAreaId != null) {
                        q.andEqualTo(DiagnosisPaymentSetting::getHospitalAreaId, hospitalAreaId);
                    }
                }));
        return Optional.ofNullable(CollectionUtils.isEmpty(diagnosisPaymentSettings) ? null : diagnosisPaymentSettings.get(0));
    }

    @Override
    public Optional<PatientReportSetting> queryPatientReportSettingById(@Nullable Long tenantId, @Nullable Long hospitalId, @Nullable Long hospitalAreaId) {
        List<PatientReportSetting> patientReportSettings = patientReportSettingMapper.selectByExample(PatientReportSetting.class,
                helper -> helper.defGroup(q -> {
                    if (tenantId != null) {
                        q.andEqualTo(PatientReportSetting::getTenantId, tenantId);
                    }
                    if (hospitalId != null) {
                        q.andEqualTo(PatientReportSetting::getHospitalId, hospitalId);
                    }
                    if (hospitalAreaId != null) {
                        q.andEqualTo(PatientReportSetting::getHospitalAreaId, hospitalAreaId);
                    }
                }));
        return Optional.ofNullable(CollectionUtils.isEmpty(patientReportSettings) ? null : patientReportSettings.get(0));
    }

    @Override
    public Optional<HospitalizationSetting> queryHospitalizationSettingById(@Nullable Long tenantId, @Nullable Long hospitalId, @Nullable Long hospitalAreaId) {
        List<HospitalizationSetting> hospitalizationSettings = hospitalizationSettingMapper.selectByExample(HospitalizationSetting.class,
                helper -> helper.defGroup(q -> {
                    if (tenantId != null) {
                        q.andEqualTo(HospitalizationSetting::getTenantId, tenantId);
                    }
                    if (hospitalId != null) {
                        q.andEqualTo(HospitalizationSetting::getHospitalId, hospitalId);
                    }
                    if (hospitalAreaId != null) {
                        q.andEqualTo(HospitalizationSetting::getHospitalAreaId, hospitalAreaId);
                    }
                }));
        return Optional.ofNullable(CollectionUtils.isEmpty(hospitalizationSettings) ? null : hospitalizationSettings.get(0));
    }

    @Override
    public Optional<CustomBusinessSetting> queryCustomBusinessSettingById(@Nullable Long tenantId, @Nullable Long hospitalId, @Nullable Long hospitalAreaId, @Nullable Long settingId) {
        List<CustomBusinessSetting> customBusinessSettings = customBusinessSettingMapper.selectByExample(CustomBusinessSetting.class,
                helper -> helper.defGroup(q -> {
                    if (tenantId != null) {
                        q.andEqualTo(CustomBusinessSetting::getTenantId, tenantId);
                    }
                    if (hospitalId != null) {
                        q.andEqualTo(CustomBusinessSetting::getHospitalId, hospitalId);
                    }
                    if (hospitalAreaId != null) {
                        q.andEqualTo(CustomBusinessSetting::getHospitalAreaId, hospitalAreaId);
                    }
                    if (settingId != null) {
                        q.andEqualTo(CustomBusinessSetting::getId, settingId);
                    }
                }));
        return Optional.ofNullable(CollectionUtils.isEmpty(customBusinessSettings) ? null : customBusinessSettings.get(0));
    }

    @Override
    public Optional<CustomBusinessSetting> queryCustomBusinessSettingByName(@Nullable Long tenantId, @Nullable Long hospitalId, @Nullable Long hospitalAreaId, @Nullable String name) {
        List<CustomBusinessSetting> customBusinessSettings = customBusinessSettingMapper.selectByExample(CustomBusinessSetting.class,
                helper -> helper.defGroup(q -> {
                    if (tenantId != null) {
                        q.andEqualTo(CustomBusinessSetting::getTenantId, tenantId);
                    }
                    if (hospitalId != null) {
                        q.andEqualTo(CustomBusinessSetting::getHospitalId, hospitalId);
                    }
                    if (hospitalAreaId != null) {
                        q.andEqualTo(CustomBusinessSetting::getHospitalAreaId, hospitalAreaId);
                    }
                    if (name != null) {
                        q.andEqualTo(CustomBusinessSetting::getName, name);
                    }
                }));
        return Optional.ofNullable(CollectionUtils.isEmpty(customBusinessSettings) ? null : customBusinessSettings.get(0));
    }

    @Override
    public List<CustomBusinessSetting> queryCustomBusinessSettingById(@Nullable Long tenantId, @Nullable Long hospitalId, @Nullable Long hospitalAreaId) {
        return customBusinessSettingMapper.selectByExample(CustomBusinessSetting.class,
                helper -> helper.defGroup(q -> {
                    if (tenantId != null) {
                        q.andEqualTo(CustomBusinessSetting::getTenantId, tenantId);
                    }
                    if (hospitalId != null) {
                        q.andEqualTo(CustomBusinessSetting::getHospitalId, hospitalId);
                    }
                    if (hospitalAreaId != null) {
                        q.andEqualTo(CustomBusinessSetting::getHospitalAreaId, hospitalAreaId);
                    }
                }));
    }

    @Override
    public Optional<AppointmentRuleSetting> queryAppointmentRuleSettingBy(Long hospitalAreaId) {
        List<AppointmentRuleSetting> appointmentRuleSettings = appointmentRuleSettingMapper.selectByExample(AppointmentRuleSetting.class,
                helper -> helper.defGroup(q -> q.andEqualTo(AppointmentRuleSetting::getHospitalAreaId, hospitalAreaId)));
        return Optional.ofNullable(CollectionUtils.isEmpty(appointmentRuleSettings) ? null : appointmentRuleSettings.get(0));
    }

    @Override
    public List<AppointmentRuleSetting> queryAppointmentRuleSettingInId(List<Long> hospitalAreaIds) {
        if (CollectionUtils.isEmpty(hospitalAreaIds)) {
            return null;
        }
        return appointmentRuleSettingMapper.selectByExample2(AppointmentRuleSetting.class, q -> q.andIn(AppointmentRuleSetting::getHospitalAreaId, hospitalAreaIds));
    }

    @Override
    public List<AppointmentRuleSetting> queryAppointmentRuleSettingByHospitalCodes(List<String> hospitalCodes) {
        if (CollectionUtils.isEmpty(hospitalCodes)) {
            return null;
        }
        return appointmentRuleSettingMapper.selectByExample2(AppointmentRuleSetting.class, q -> q.andIn(AppointmentRuleSetting::getHospitalCode, hospitalCodes));
    }

    @Override
    public List<AppointmentRuleSetting> All() {
        return appointmentRuleSettingMapper.selectAll();
    }

    @Override
    public List<AppointmentRuleSetting> queryAppointmentRuleSettingBy(Set<String> hospitalCodeSet) {
        if (CollectionUtils.isEmpty(hospitalCodeSet)) {
            return null;
        }
        return appointmentRuleSettingMapper.selectByExample2(AppointmentRuleSetting.class, q -> q.andIn(AppointmentRuleSetting::getHospitalCode, hospitalCodeSet));
    }

    @Override
    public Optional<List<HospitalDependOnHisResponse>> queryDependOnHIs(Long id, String hospitalName) {
        List<HospitalDependOnHisResponse> appointmentRuleSettings = appointmentRuleSettingMapper.selectDependOnHis(id, hospitalName);
        return Optional.ofNullable(CollectionUtils.isEmpty(appointmentRuleSettings) ? null : appointmentRuleSettings);
    }
}
