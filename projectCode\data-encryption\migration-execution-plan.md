# 数据加密迁移执行计划

## 📋 概述

根据《数据安全法》《个人信息保护法》整改要求，对租户服务中的敏感数据进行加密存储改造。采用影子字段渐进式迁移策略，确保零停机迁移。

## 🎯 涉及的敏感数据

### 核心数据（最高安全级别）
- `t_tenant_user.id_card_no` - 用户身份证号
- `t_recharge_record.jz_card` - 就诊卡号

### 重要数据
- `t_tenant_user.phone_number` - 用户手机号码
- `t_tenant_user.name` - 用户名
- `t_recharge_record.patient_name` - 就诊人姓名
- `t_tenant.contact_phone_number` - 租户联系人手机号
- `t_tenant.contact_email` - 租户联系人邮箱

## 🚀 迁移执行步骤

### 第一阶段：准备阶段（预计1天）

#### 1.1 环境准备
```bash
# 1. 备份相关表结构和数据
mysqldump -u username -p database_name t_tenant_user t_recharge_record t_tenant > backup_$(date +%Y%m%d).sql

# 2. 检查表空间和磁盘空间
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS "Size in MB"
FROM information_schema.tables 
WHERE table_schema = 'your_database_name' 
  AND table_name IN ('t_tenant_user', 't_recharge_record', 't_tenant');
```

#### 1.2 执行DDL操作（业务低峰期）
```bash
# 执行影子字段新增SQL（简化版）
mysql -u username -p database_name < shadow_fields_migration_simple.sql
```

#### 1.3 验证DDL结果
```sql
-- 验证影子字段创建成功
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    COLUMN_TYPE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME IN ('t_tenant_user', 't_recharge_record', 't_tenant')
  AND COLUMN_NAME LIKE '%_encrypted'
ORDER BY TABLE_NAME, ORDINAL_POSITION;
```

### 第二阶段：代码部署阶段（预计1天）

#### 2.1 集成加密模块
```xml
<!-- 在项目pom.xml中添加依赖 -->
<dependency>
    <groupId>com.ynhdkc</groupId>
    <artifactId>dyt-backend-encryption</artifactId>
    <version>1.1-SNAPSHOT</version>
</dependency>
```

#### 2.2 配置加密策略
```yaml
# 初期配置：PLAINTEXT_PRIORITY策略
backend:
  encryption:
    enabled: true
    strategies:
      tenant-user-id-card:
        strategy: PLAINTEXT_PRIORITY  # 明文优先
        shadow-field: "id_card_no_encrypted"
      # ... 其他字段配置
```

#### 2.3 更新实体类
- 添加`@EncryptField`注解
- 添加`@Convert(converter = EncryptConverter.class)`
- 添加影子字段属性

#### 2.4 部署和验证
```bash
# 1. 部署应用
# 2. 验证加密功能
curl -X POST /api/test/encryption -d '{"data":"test"}'

# 3. 检查日志
tail -f logs/encryption.log
```

### 第三阶段：数据迁移阶段（预计2-3天）

#### 3.1 启动数据迁移
```sql
-- 检查待迁移数据量
SELECT 
    't_tenant_user' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN id_card_no IS NOT NULL THEN 1 END) as id_card_records,
    COUNT(CASE WHEN phone_number IS NOT NULL THEN 1 END) as phone_records
FROM t_tenant_user
UNION ALL
SELECT 
    't_recharge_record',
    COUNT(*),
    COUNT(CASE WHEN patient_name IS NOT NULL THEN 1 END),
    COUNT(CASE WHEN jz_card IS NOT NULL THEN 1 END)
FROM t_recharge_record
UNION ALL
SELECT 
    't_tenant',
    COUNT(*),
    COUNT(CASE WHEN contact_phone_number IS NOT NULL THEN 1 END),
    COUNT(CASE WHEN contact_email IS NOT NULL THEN 1 END)
FROM t_tenant;
```

#### 3.2 批量数据迁移脚本
```java
// 示例迁移代码
@Service
public class DataMigrationService {
    
    @Autowired
    private TenantUserRepository tenantUserRepository;
    
    @Transactional
    public void migrateTenantUserData(int batchSize) {
        int offset = 0;
        List<TenantUser> users;
        
        do {
            users = tenantUserRepository.findUsersForMigration(offset, batchSize);
            
            for (TenantUser user : users) {
                // 触发加密逻辑，自动填充影子字段
                tenantUserRepository.save(user);
            }
            
            offset += batchSize;
            
            // 记录迁移进度
            log.info("Migrated {} users, total processed: {}", users.size(), offset);
            
        } while (users.size() == batchSize);
    }
}
```

#### 3.3 监控迁移进度
```sql
-- 检查影子字段填充情况
SELECT
    COUNT(*) as total,
    COUNT(id_card_no_encrypted) as encrypted_id_card,
    COUNT(phone_number_encrypted) as encrypted_phone,
    COUNT(name_encrypted) as encrypted_name
FROM t_tenant_user;

-- 检查其他表的迁移进度
SELECT
    COUNT(*) as total,
    COUNT(patient_name_encrypted) as encrypted_patient_name,
    COUNT(jz_card_encrypted) as encrypted_jz_card
FROM t_recharge_record;

SELECT
    COUNT(*) as total,
    COUNT(contact_phone_number_encrypted) as encrypted_contact_phone,
    COUNT(contact_email_encrypted) as encrypted_contact_email
FROM t_tenant;
```

### 第四阶段：策略切换阶段（预计1天）

#### 4.1 切换到SHADOW_PRIORITY策略
```yaml
backend:
  encryption:
    strategies:
      tenant-user-id-card:
        strategy: SHADOW_PRIORITY  # 影子字段优先
        shadow-field: "id_card_no_encrypted"
```

#### 4.2 验证切换效果
```java
// 测试读取数据
@Test
public void testDataReadAfterSwitch() {
    TenantUser user = tenantUserRepository.findById(1L);
    assertNotNull(user.getIdCardNo());  // 应该能正常读取解密数据
}
```

#### 4.3 性能监控
```bash
# 监控应用性能
curl /actuator/metrics/encryption.operation.duration

# 检查数据库连接池
curl /actuator/metrics/hikaricp.connections
```

### 第五阶段：完成迁移阶段（预计1天）

#### 5.1 切换到SHADOW_ONLY策略
```yaml
backend:
  encryption:
    strategies:
      tenant-user-id-card:
        strategy: SHADOW_ONLY  # 仅使用影子字段
```

#### 5.2 数据验证
```sql
-- 验证数据完整性
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN id_card_no_encrypted IS NOT NULL THEN 1 END) as encrypted_records,
    ROUND(COUNT(CASE WHEN id_card_no_encrypted IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2) as encryption_rate
FROM t_tenant_user;
```

#### 5.3 清理原字段（可选，建议保留一段时间）
```sql
-- 可选：清空原字段数据（谨慎操作）
-- UPDATE t_tenant_user SET id_card_no = NULL WHERE id_card_no_encrypted IS NOT NULL;
```

## 📊 监控和告警

### 关键指标监控
1. **加密成功率**：> 99.9%
2. **加密性能**：平均响应时间 < 10ms
3. **数据完整性**：影子字段填充率 = 100%
4. **错误率**：< 0.1%

### 告警配置
```yaml
# 示例告警规则
alerts:
  - name: encryption_failure_rate
    condition: encryption_failure_rate > 0.1%
    action: send_notification
  
  - name: migration_progress
    condition: migration_stuck_for > 1hour
    action: escalate_to_team
```

## 🔧 回滚计划

### 紧急回滚步骤
1. **配置回滚**：切换回PLAINTEXT_PRIORITY策略
2. **代码回滚**：移除加密相关代码
3. **数据回滚**：使用备份数据恢复

### 回滚SQL
```sql
-- 如需完全回滚，删除影子字段
-- ALTER TABLE t_tenant_user DROP COLUMN id_card_no_encrypted;
-- ALTER TABLE t_tenant_user DROP COLUMN phone_number_encrypted;
-- ALTER TABLE t_tenant_user DROP COLUMN name_encrypted;
```

## ✅ 验收标准

1. **功能验收**
   - [ ] 所有敏感字段成功加密存储
   - [ ] 业务功能正常运行
   - [ ] 查询性能无明显下降

2. **安全验收**
   - [ ] 数据库中敏感数据已加密
   - [ ] 加密算法符合安全标准
   - [ ] 密钥管理安全可控

3. **性能验收**
   - [ ] 响应时间增加 < 20%
   - [ ] 数据库存储增加 < 50%
   - [ ] 系统稳定性良好

## 📞 应急联系

- **技术负责人**：[姓名] [电话]
- **DBA**：[姓名] [电话]
- **运维负责人**：[姓名] [电话]

## 📝 执行记录

| 阶段 | 计划时间 | 实际时间 | 执行人 | 状态 | 备注 |
|------|----------|----------|--------|------|------|
| 准备阶段 | 2024-06-25 | | | 待执行 | |
| 代码部署 | 2024-06-26 | | | 待执行 | |
| 数据迁移 | 2024-06-27~29 | | | 待执行 | |
| 策略切换 | 2024-06-30 | | | 待执行 | |
| 完成迁移 | 2024-07-01 | | | 待执行 | |
