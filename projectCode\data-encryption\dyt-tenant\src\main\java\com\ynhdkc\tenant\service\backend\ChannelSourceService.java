package com.ynhdkc.tenant.service.backend;

import com.ynhdkc.tenant.entity.constant.ChannelSourceType;

import java.util.List;
import java.util.Map;

public interface ChannelSourceService {

    /**
     * 提交渠道资源关联，如果已存在则更新，不存在则插入
     * 用于更新关联关系
     *
     * @param sourceIds   资源id
     * @param sourceType 资源类型
     * @param channels   渠道列表
     * @return 更新条数
     */
    Integer submitChannelSource(List<Long> sourceIds,
                                ChannelSourceType sourceType,
                                List<Integer> channels);

    /**
     * 根据渠道查询资源id列表
     *
     * @param sourceType  资源类型
     * @param channelCodes 渠道
     * @return 资源id列表
     */
    List<Long> selectSourceIdByChannel(ChannelSourceType sourceType,
                                       List<Integer> channelCodes);

    /**
     * 根据资源id查询渠道列表，key为资源id，value为渠道列表
     * 用于vo的组装
     *
     * @param sourceType 资源类型
     * @param sourceId   资源id列表
     * @return 资源id和渠道列表的map
     */
    Map<Long, List<Integer>> selectChannelMap(ChannelSourceType sourceType,
                                              List<Long> sourceId);

    /**
     * 根据资源id和资源类型删除关联关系
     * 用于当资源被删除时，需要删除关联关系
     *
     * @param sourceId   资源id
     * @param sourceType 资源类型
     * @return 删除条数
     */
    Integer deleteChannelSource(List<Long> sourceId,
                                ChannelSourceType sourceType);
}
