package backend.common.entity.dto.hisgateway.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Data
public class DoctorScheduleListItem {

    @JsonProperty("hospital_id")
    private Long hospitalId;

    @JsonProperty("hospital_area_id")
    private Long hospitalAreaId;

    @JsonProperty("department_id")
    private Long departmentId;

    @JsonProperty("doctor_id")
    private Long doctorId;

    @JsonProperty("hospital_code")
    private String hospitalCode;

    @JsonProperty("schedule_id")
    private String scheduleId;

    // 就诊时间
    @JsonProperty("registration_date")
    private Long registrationDate;

    @JsonProperty("registration_level")
    private String registrationLevel;

    @JsonProperty("registration_level_desc")
    private String registrationLevelDesc;

    @JsonProperty("resource_type")
    private String resourceType;

    @JsonProperty("resource_type_desc")
    private String resourceTypeDesc;

    private String resource;

    @JsonProperty("department_name")
    private String departmentName;

    @JsonProperty("department_code")
    private String departmentCode;

    @JsonProperty("doctor_name")
    private String doctorName;

    @JsonProperty("doctor_code")
    private String doctorCode;

    @JsonProperty("doctor_type")
    private String doctorType;

    @JsonProperty("doctor_type_desc")
    private String doctorTypeDesc;

    @JsonProperty("visiting_room")
    private String visitingRoom;

    @JsonProperty("begin_time")
    private Long beginTime;

    @JsonProperty("end_time")
    private Long endTime;

    @JsonProperty("time_type")
    private Integer timeType;

    @JsonProperty("time_type_desc")
    private String timeTypeDesc;

    @JsonProperty("src_sum")
    private Integer srcSum;

    @JsonProperty("src_number")
    private Integer srcNumber;

    @JsonProperty("registration_amt")
    private BigDecimal registrationAmt;

    @JsonProperty("treat_amt")
    private BigDecimal treatAmt;

    @JsonProperty("inspection_fees")
    private BigDecimal inspectionFees;

    @JsonProperty("expert_amt")
    private BigDecimal expertAmt;

    @JsonProperty("total_cost")
    private BigDecimal totalCost;

    @JsonProperty("schedule_status")
    private Integer scheduleStatus;

    @JsonProperty("is_time_part")
    private Integer isTimePart;

    @JsonProperty("show_sch_date")
    private Integer showSchDate;

    @JsonProperty("status")
    private Integer status;

    private Map<String, Object> rawParameters = new HashMap<>();

    public void setScheduleStatus(Integer scheduleStatus) {
        this.status = scheduleStatus;
        this.scheduleStatus = scheduleStatus;
    }

    public void setStatus(Integer status) {
        this.status = status;
        this.scheduleStatus = status;
    }

    public String getKey(String hospitalCode) {
        return String.format("%s_%s_%s", hospitalCode, departmentCode, doctorCode);
    }

}
