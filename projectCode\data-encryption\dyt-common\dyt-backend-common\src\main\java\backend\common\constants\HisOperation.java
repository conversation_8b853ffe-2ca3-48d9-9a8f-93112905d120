package backend.common.constants;

import backend.common.delegate.CacheKeyDelegate;
import backend.common.delegate.impl.DateCacheKeyImpl;
import backend.common.delegate.impl.DepartmentCacheKeyImpl;
import backend.common.delegate.impl.DoctorCacheKeyImpl;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
public enum HisOperation {

    DEPARTMENT_LIST("department_list", "获取科室列表",
            KafkaTopicConfig.DepartmentList.REQUEST_TOPIC_NAME,
            KafkaTopicConfig.DepartmentList.RESPONSE_TOPIC_NAME),

    DEPARTMENT_LIST_4_ADMIN("department_list", "获取科室列表(行政归口)",
            KafkaTopicConfig.DepartmentList.REQUEST_TOPIC_NAME,
            KafkaTopicConfig.DepartmentList.RESPONSE_TOPIC_NAME, Expiration.HALF_HOUR.getTime(), new DepartmentCacheKeyImpl("department_list")),
    DEPARTMENT_LIST_4_VIP("department_list_4_vip", "获取科室列表(行政归口)",
            KafkaTopicConfig.DepartmentList.REQUEST_TOPIC_NAME,
            KafkaTopicConfig.DepartmentList.RESPONSE_TOPIC_NAME, Expiration.HALF_HOUR.getTime(), new DepartmentCacheKeyImpl("department_list_4_vip")),
    DEPARTMENT_LIST_4_MULTI_LEVEL("department_list_4_multi_level", "获取科室列表多级",
            KafkaTopicConfig.DepartmentList.REQUEST_TOPIC_NAME,
            KafkaTopicConfig.DepartmentList.RESPONSE_TOPIC_NAME, Expiration.HALF_HOUR.getTime(), new DepartmentCacheKeyImpl("department_list_4_multi_level")),

    DEPARTMENT_DOCTOR_LIST("department_doctor_list", "获取科室医生列表",
            KafkaTopicConfig.DptDoctorList.REQUEST_TOPIC_NAME,
            KafkaTopicConfig.DptDoctorList.RESPONSE_TOPIC_NAME, Expiration.THREE_SECOND.getTime(), new DoctorCacheKeyImpl("department_doctor_list")),

    DOCTORS_DETAIL("doctors_detail", "获取医生详情", null, null),


    DOCTOR_SCHEDULE_LIST("doctor_schedule_list", "获取医生排班列表",
            KafkaTopicConfig.DoctorScheduleList.REQUEST_TOPIC_NAME,
            KafkaTopicConfig.DoctorScheduleList.RESPONSE_TOPIC_NAME, Expiration.TWO_SECOND.getTime(), new DateCacheKeyImpl("doctor_schedule_list")),

    TIME_SCHEDULE("time_schedule", "获取分时段排班信息",
            KafkaTopicConfig.TimeSchedule.REQUEST_TOPIC_NAME,
            KafkaTopicConfig.TimeSchedule.RESPONSE_TOPIC_NAME, Expiration.TWO_SECOND.getTime(), null),


    PRE_REGISTRATION("pre_registration", "锁号",
            KafkaTopicConfig.LockNumber.REQUEST_TOPIC_NAME,
            KafkaTopicConfig.LockNumber.RESPONSE_TOPIC_NAME),
    CANCEL_PRE_REGISTRATION("cancel_pre_registration", "取消锁号",
            KafkaTopicConfig.CancelPreRegistration.REQUEST_TOPIC_NAME,
            KafkaTopicConfig.CancelPreRegistration.RESPONSE_TOPIC_NAME),


    REGISTERED("registered", "挂号（下单）",
            KafkaTopicConfig.Registered.REQUEST_TOPIC_NAME,
            KafkaTopicConfig.Registered.RESPONSE_TOPIC_NAME),

    MEDICAL_INSURANCE_REGISTERED("medical_insurance_registered", "医保挂号", null, null),
    CANCEL_MEDICAL_INSURANCE_REGISTERED("cancel_medical_insurance_registered", "取消医保挂号", null, null),
    MEDICAL_INSURANCE_ORDER("medical_insurance_order", "医保订单", null, null),
    CANCEL_MEDICAL_INSURANCE_ORDER("cancel_medical_insurance_order", "取消医保订单", null, null),
    RELEASE_MEDICAL_INSURANCE_ORDER("release_medical_insurance_order", "释放医保订单", null, null),


//    YUNNAN_1ST_REGISTERED("registered", "挂号（下单）",
//            KafkaTopicConfig.Yunnan1stRegistered.REQUEST_TOPIC_NAME,
//            KafkaTopicConfig.Yunnan1stRegistered.RESPONSE_TOPIC_NAME),


    CANCEL_REGISTERED("cancel_registered", "取消挂号",
            KafkaTopicConfig.CancelRegistered.REQUEST_TOPIC_NAME,
            KafkaTopicConfig.CancelRegistered.RESPONSE_TOPIC_NAME),

    QUERY_PATIENT_ARCHIVES("query_patient_archives", "查询患者档案消息",
            KafkaTopicConfig.QueryPatientArchives.REQUEST_TOPIC_NAME,
            KafkaTopicConfig.QueryPatientArchives.RESPONSE_TOPIC_NAME),
    CREATE_ARCHIVES("create_archives", "建档",
            KafkaTopicConfig.CreateArchives.REQUEST_TOPIC_NAME,
            KafkaTopicConfig.CreateArchives.RESPONSE_TOPIC_NAME),
    UPDATE_PATIENT_ARCHIVES("update_patient_archives", "更新患者档案消息",
            KafkaTopicConfig.UpdatePatientArchives.REQUEST_TOPIC_NAME,
            KafkaTopicConfig.UpdatePatientArchives.RESPONSE_TOPIC_NAME),

    QUERY_PATIENT_CARD_BALANCE("query_patient_card_balance", "获取接诊卡余额", null, null),
    PATIENT_CARD_PRE_RECHARGE("patient_card_pre_recharge", "预充值", null, null),
    FREEZE_BALANCE("freeze_balance", "冻结余额", null, null),
    PATIENT_CARD_REFUND("patient_card_refund", "退费", null, null),
    UNFREEZE_BALANCE("unfreeze_balance", "解冻", null, null),
    HEALTH_CARD_REPORT_LOSS("health_card_report_loss", "就诊卡挂失", null, null),

    RECHARGE_RECORD_QUERY("recharge_record_query", "充值/预存 记录查询 （用于使用预存卡的医院）", null, null),
    RECHARGE_RESULT_SUCCESSFUL_QUERY("recharge_result_successful_query", "查询充值/预存 是否成功 （用于使用预存卡的医院）", null, null),
    RECHARGE_BALANCE_QUERY("recharge_balance_query", "查询充值/预存 余额 （用于使用预存卡的医院）", null, null),


    DOCTOR_OUT_SERVICE("doctor_out_service", "获取停诊信息(查询粒度到科室)",
            KafkaTopicConfig.DoctorOutService.REQUEST_TOPIC_NAME,
            KafkaTopicConfig.DoctorOutService.RESPONSE_TOPIC_NAME, Expiration.TEN_MINUTE.getTime(), new DoctorCacheKeyImpl("doctor_out_service")),

    HOSPITAL_OUT_SERVICE("hospital_out_service", "获取停诊信息(查询粒度到院区，适合通过hospital_code来查)",
            KafkaTopicConfig.HospitalOutService.REQUEST_TOPIC_NAME,
            KafkaTopicConfig.HospitalOutService.RESPONSE_TOPIC_NAME, Expiration.TEN_MINUTE.getTime(), new DepartmentCacheKeyImpl("hospital_out_service")),

    QUERY_REGISTRATION_FEES("query_registration_fees", "查询挂号费用", null, null),

    NOTIFY_HIS("notify_his", "通知HIS系统", KafkaTopicConfig.NotifyHis.REQUEST_TOPIC_NAME, KafkaTopicConfig.NotifyHis.RESPONSE_TOPIC_NAME),

    //"挂号（下单）结果核验
    REGISTRATION_RESULT_CHECK("registration_result_check", "挂号（下单）结果核验",
            KafkaTopicConfig.RegistrationResultCheck.REQUEST_TOPIC_NAME,
            KafkaTopicConfig.RegistrationResultCheck.RESPONSE_TOPIC_NAME),
    //检查支付订单状态，用于运维
    CHECK_PAYMENT_ORDER("check_payment_order", "检查订单状态", null, null),

    REGISTRATION_BEFORE_CHECK("registration_before_check", "锁号/挂号（下单）前置检查", null, null),

    CANCEL_REGISTRATION_BEFORE_CHECK("cancel_registration_before_check", "取消挂号前置检查", null, null),

    DOCTOR_DATA_SYNC("doctor_data_sync", "医师资料同步",
            KafkaTopicConfig.DoctorDataSync.REQUEST_TOPIC_NAME,
            KafkaTopicConfig.DoctorDataSync.RESPONSE_TOPIC_NAME),


    HOSPITAL_ACCEPT("hospital_accept", "查询医院是否愿意接受", null, null),

    AUTHENTICATE_INFO("authenticate_info", "鉴权服务", null, null),

    BACK_UP("back_up", "备份操作，用于支持医院新旧接口并行的情况", null, null),
    CHECK_IN("check_in", "签到", null, null),


    CREATE_HEALTH_CARD("create_health_card", "创建电子健康卡", null, null),
    QUERY_HEALTH_CARD("query_health_card", "查询电子健康卡", null, null),
    QUERY_HEALTH_CARD_QR_CODE("query_health_card_qr_code", "查询电子健康卡二维码", null, null),
    UPDATE_HEALTH_CARD("update_health_card", "更新电子健康卡", null, null),
    QUERY_HEALTH_CARD_USAGE("query_health_card_usage", "查询电子健康卡使用记录", null, null),


    //提供给医院窗口时候
    JUST_REFUND("just_refund", "只退款，不退号(提供给医院窗口时候)", KafkaTopicConfig.JustRefund.REQUEST_TOPIC_NAME, KafkaTopicConfig.JustRefund.RESPONSE_TOPIC_NAME),

    //排队签到
    QUEUE_4_CHECK_IN("queue_4_check_in", "就诊排队", null, null),

    //排队签到
    UPLOAD_BILL("upload_bill", "上传对账单", null, null),
    DELETE_BILL("delete_bill", "删除对账单", null, null),


    DOWNLOAD_HIS_BILL("download_his_bill", "下载医院对账单", null, null),

    ELECTRONIC_INVOICE("electronic_invoice", "电子发票", KafkaTopicConfig.ElectronicInvoice.REQUEST_TOPIC_NAME,
            KafkaTopicConfig.ElectronicInvoice.RESPONSE_TOPIC_NAME),

    QUERY_TIME_TYPE("query_time_type", "查询排班时间类型", null, null),

    UNKNOWN("", "未知", "", "");

    private final String desc;

    private final String value;

    private final String requestTopicName;

    private final String responseTopicName;

    //缓存过期时间，<=0 则不缓存(单位是毫秒)
    private final long expiration;

    private final CacheKeyDelegate cacheKeyDelegate;

    HisOperation(String value, String desc, String sendTopicName, String responseTopicName, long expiration, CacheKeyDelegate cacheKeyDelegate) {
        this.desc = desc;
        this.value = value;
        this.requestTopicName = sendTopicName;
        this.responseTopicName = responseTopicName;
        this.expiration = expiration;
        this.cacheKeyDelegate = cacheKeyDelegate;
    }

    HisOperation(String value, String desc, String sendTopicName, String responseTopicName) {
        this.desc = desc;
        this.value = value;
        this.requestTopicName = sendTopicName;
        this.responseTopicName = responseTopicName;
        this.expiration = 0;
        this.cacheKeyDelegate = null;
    }

    public static HisOperation parse(String value) {
        for (HisOperation e : HisOperation.values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        return UNKNOWN;
    }

    public static List<HisOperation> getAvailableList() {
        List<HisOperation> hisOperationList = new ArrayList<>();
        HisOperation[] hisOperations = HisOperation.values();
        for (HisOperation hisOperation : hisOperations) {
            if (hisOperation.equals(UNKNOWN)) {
                continue;
            }
            hisOperationList.add(hisOperation);
        }
        return hisOperationList;
    }

}
