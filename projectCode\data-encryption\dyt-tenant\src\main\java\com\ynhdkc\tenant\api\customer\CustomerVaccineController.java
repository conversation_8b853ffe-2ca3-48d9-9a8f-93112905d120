package com.ynhdkc.tenant.api.customer;

import com.ynhdkc.tenant.handler.CustomerVaccineApi;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.customer.CustomerVaccineService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "CustomerVaccine")
@RestController
@RequiredArgsConstructor
public class CustomerVaccineController implements CustomerVaccineApi {
    private final CustomerVaccineService customerVaccineService;

    @Override
    public ResponseEntity<CustomerVaccineCategoryPageVo> getVaccineCategoryPage(CustomerVaccineCategoryPageReqDto request) {
        CustomerVaccineCategoryPageVo result = customerVaccineService.getVaccineCategoryPage(request);
        return ResponseEntity.ok(result);
    }

    @Override
    public ResponseEntity<List<CustomerVaccineOrganizationVo>> getVaccineOrganizationList(CustomerVaccineOrganizationListReqDto request) {
        List<CustomerVaccineOrganizationVo> result = customerVaccineService.getVaccineOrganizationList(request);
        return ResponseEntity.ok(result);
    }

    @Override
    public ResponseEntity<List<CustomerVaccineVo>> getVaccineList(CustomerVaccineListReqDto request) {
        List<CustomerVaccineVo> result = customerVaccineService.getVaccineList(request);
        return ResponseEntity.ok(result);
    }

    @Override
    public ResponseEntity<CustomerVaccineSearchRespVo> vaccineSearch(String keyword, Double longitude, Double latitude) {
        CustomerVaccineSearchRespVo result = customerVaccineService.vaccineSearch(keyword, longitude, latitude);
        return ResponseEntity.ok(result);
    }

}
