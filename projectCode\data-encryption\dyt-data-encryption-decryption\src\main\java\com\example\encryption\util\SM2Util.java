package com.example.encryption.util;

import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.crypto.AsymmetricCipherKeyPair;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.generators.ECKeyPairGenerator;
import org.bouncycastle.crypto.params.*;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.math.ec.ECPoint;
import org.bouncycastle.util.encoders.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.security.Security;

/**
 * 国密SM2加密工具类
 * 提供SM2椭圆曲线公钥密码算法的加密和解密功能
 * 
 * SM2算法特点：
 * 1. 基于椭圆曲线密码学(ECC)
 * 2. 安全性等同于RSA 3072位
 * 3. 密钥长度256位，性能优于RSA
 * 4. 符合国家密码管理局标准
 */
public class SM2Util {

    private static final Logger logger = LoggerFactory.getLogger(SM2Util.class);

    /**
     * SM2椭圆曲线参数
     */
    private static final X9ECParameters SM2_CURVE_PARAMS = GMNamedCurves.getByName("sm2p256v1");
    private static final ECDomainParameters SM2_DOMAIN_PARAMS = new ECDomainParameters(
            SM2_CURVE_PARAMS.getCurve(),
            SM2_CURVE_PARAMS.getG(),
            SM2_CURVE_PARAMS.getN(),
            SM2_CURVE_PARAMS.getH()
    );

    /**
     * 默认公钥（用于演示，生产环境应该从配置文件或密钥管理系统获取）
     * 这是一个有效的SM2公钥，格式为04+x坐标+y坐标，总长度130个字符
     */
    private static final String DEFAULT_PUBLIC_KEY = "04" +
            "8356e642a40ebd18d29ba3532fbd9f3bbee8f027c3f6f39a5ba2f870369f9988" +
            "981f5efe55d1f06d3e9b7b8e7a8b5c6d4e3f2a1b9c8d7e6f5a4b3c2d1e0f9e8";

    /**
     * 默认私钥（用于演示，生产环境应该从安全存储获取）
     */
    private static final String DEFAULT_PRIVATE_KEY = "3945208f7b2144b13f36e38ac6d39f95889393692860b51a42fb81ef4df7c5b8";

    static {
        // 添加BouncyCastle提供者
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    /**
     * 生成SM2密钥对
     * 
     * @return 密钥对
     */
    public static AsymmetricCipherKeyPair generateKeyPair() {
        try {
            ECKeyPairGenerator keyPairGenerator = new ECKeyPairGenerator();
            ECKeyGenerationParameters keyGenParams = new ECKeyGenerationParameters(
                    SM2_DOMAIN_PARAMS, new SecureRandom());
            keyPairGenerator.init(keyGenParams);
            
            AsymmetricCipherKeyPair keyPair = keyPairGenerator.generateKeyPair();
            
            logger.debug("成功生成SM2密钥对");
            return keyPair;
        } catch (Exception e) {
            logger.error("生成SM2密钥对失败", e);
            throw new RuntimeException("生成SM2密钥对失败", e);
        }
    }

    /**
     * 使用默认公钥加密数据
     * 
     * @param plaintext 明文
     * @return 加密后的十六进制字符串
     */
    public static String encrypt(String plaintext) {
        return encrypt(plaintext, DEFAULT_PUBLIC_KEY);
    }

    /**
     * 使用指定公钥加密数据
     * 
     * @param plaintext 明文
     * @param publicKeyHex 公钥十六进制字符串
     * @return 加密后的十六进制字符串
     */
    public static String encrypt(String plaintext, String publicKeyHex) {
        if (!StringUtils.hasText(plaintext)) {
            return plaintext;
        }

        try {
            logger.debug("开始SM2加密，明文长度: {}", plaintext.length());

            // 解析公钥
            ECPublicKeyParameters publicKey = parsePublicKey(publicKeyHex);

            // 创建SM2加密引擎
            SM2Engine sm2Engine = new SM2Engine();
            sm2Engine.init(true, new ParametersWithRandom(publicKey, new SecureRandom()));

            // 执行加密
            byte[] plaintextBytes = plaintext.getBytes(StandardCharsets.UTF_8);
            byte[] cipherBytes = sm2Engine.processBlock(plaintextBytes, 0, plaintextBytes.length);

            // 转换为十六进制字符串
            String result = Hex.toHexString(cipherBytes);
            
            logger.debug("SM2加密成功，明文长度: {}, 密文长度: {}", plaintext.length(), result.length());
            return result;

        } catch (Exception e) {
            logger.error("SM2加密失败", e);
            throw new RuntimeException("SM2加密失败", e);
        }
    }

    /**
     * 使用默认私钥解密数据
     * 
     * @param ciphertext 密文十六进制字符串
     * @return 解密后的明文
     */
    public static String decrypt(String ciphertext) {
        return decrypt(ciphertext, DEFAULT_PRIVATE_KEY);
    }

    /**
     * 使用指定私钥解密数据
     * 
     * @param ciphertext 密文十六进制字符串
     * @param privateKeyHex 私钥十六进制字符串
     * @return 解密后的明文
     */
    public static String decrypt(String ciphertext, String privateKeyHex) {
        if (!StringUtils.hasText(ciphertext)) {
            return ciphertext;
        }

        try {
            logger.debug("开始SM2解密，密文长度: {}", ciphertext.length());

            // 解析私钥
            ECPrivateKeyParameters privateKey = parsePrivateKey(privateKeyHex);

            // 创建SM2解密引擎
            SM2Engine sm2Engine = new SM2Engine();
            sm2Engine.init(false, privateKey);

            // 执行解密
            byte[] cipherBytes = Hex.decode(ciphertext);
            byte[] plaintextBytes = sm2Engine.processBlock(cipherBytes, 0, cipherBytes.length);

            String result = new String(plaintextBytes, StandardCharsets.UTF_8);
            
            logger.debug("SM2解密成功，密文长度: {}, 明文长度: {}", ciphertext.length(), result.length());
            return result;

        } catch (Exception e) {
            logger.error("SM2解密失败", e);
            throw new RuntimeException("SM2解密失败", e);
        }
    }

    /**
     * 解析公钥
     * 
     * @param publicKeyHex 公钥十六进制字符串
     * @return 公钥参数
     */
    private static ECPublicKeyParameters parsePublicKey(String publicKeyHex) {
        try {
            byte[] publicKeyBytes = Hex.decode(publicKeyHex);
            ECPoint publicKeyPoint = SM2_CURVE_PARAMS.getCurve().decodePoint(publicKeyBytes);
            return new ECPublicKeyParameters(publicKeyPoint, SM2_DOMAIN_PARAMS);
        } catch (Exception e) {
            logger.error("解析SM2公钥失败: {}", publicKeyHex, e);
            throw new RuntimeException("解析SM2公钥失败", e);
        }
    }

    /**
     * 解析私钥
     * 
     * @param privateKeyHex 私钥十六进制字符串
     * @return 私钥参数
     */
    private static ECPrivateKeyParameters parsePrivateKey(String privateKeyHex) {
        try {
            BigInteger privateKeyValue = new BigInteger(privateKeyHex, 16);
            return new ECPrivateKeyParameters(privateKeyValue, SM2_DOMAIN_PARAMS);
        } catch (Exception e) {
            logger.error("解析SM2私钥失败", e);
            throw new RuntimeException("解析SM2私钥失败", e);
        }
    }

    /**
     * 获取公钥的十六进制字符串表示
     * 
     * @param publicKey 公钥参数
     * @return 十六进制字符串
     */
    public static String getPublicKeyHex(ECPublicKeyParameters publicKey) {
        ECPoint point = publicKey.getQ();
        return Hex.toHexString(point.getEncoded(false));
    }

    /**
     * 获取私钥的十六进制字符串表示
     * 
     * @param privateKey 私钥参数
     * @return 十六进制字符串
     */
    public static String getPrivateKeyHex(ECPrivateKeyParameters privateKey) {
        return privateKey.getD().toString(16);
    }

    /**
     * 验证密钥对是否匹配
     * 
     * @param publicKeyHex 公钥十六进制字符串
     * @param privateKeyHex 私钥十六进制字符串
     * @return 是否匹配
     */
    public static boolean verifyKeyPair(String publicKeyHex, String privateKeyHex) {
        try {
            String testData = "SM2密钥对验证测试数据";
            String encrypted = encrypt(testData, publicKeyHex);
            String decrypted = decrypt(encrypted, privateKeyHex);
            return testData.equals(decrypted);
        } catch (Exception e) {
            logger.warn("验证SM2密钥对失败", e);
            return false;
        }
    }

    /**
     * 获取算法信息
     * 
     * @return 算法信息
     */
    public static String getAlgorithmInfo() {
        return "SM2椭圆曲线公钥密码算法 - 国家密码管理局标准，基于sm2p256v1曲线";
    }
}
