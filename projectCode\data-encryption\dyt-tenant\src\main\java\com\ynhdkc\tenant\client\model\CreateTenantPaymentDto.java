package com.ynhdkc.tenant.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * CreateTenantPaymentDto
 */
@Validated
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2023-04-10T22:27:16.318+08:00")


public class CreateTenantPaymentDto {
    @JsonProperty("tenant_id")
    private Long tenantId = null;

    @JsonProperty("tenant_name")
    private String tenantName = null;

    @JsonProperty("hospital_id")
    private Long hospitalId = null;

    @JsonProperty("hospital_name")
    private String hospitalName = null;

    @JsonProperty("hospital_code")
    private String hospitalCode = null;

    @JsonProperty("payment_channel")
    @Valid
    private List<Integer> paymentChannel = new ArrayList<Integer>();

    @JsonProperty("enabled")
    private Boolean enabled = true;

    public CreateTenantPaymentDto tenantId(Long tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    /**
     * 租户ID
     *
     * @return tenantId
     **/
    @ApiModelProperty(required = true, value = "租户ID")
    @NotNull


    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public CreateTenantPaymentDto tenantName(String tenantName) {
        this.tenantName = tenantName;
        return this;
    }

    /**
     * 租户名称
     *
     * @return tenantName
     **/
    @ApiModelProperty(value = "租户名称")

    @Size(max = 30)
    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public CreateTenantPaymentDto hospitalId(Long hospitalId) {
        this.hospitalId = hospitalId;
        return this;
    }

    /**
     * 医院ID
     *
     * @return hospitalId
     **/
    @ApiModelProperty(required = true, value = "医院ID")
    @NotNull


    public Long getHospitalId() {
        return hospitalId;
    }

    public void setHospitalId(Long hospitalId) {
        this.hospitalId = hospitalId;
    }

    public CreateTenantPaymentDto hospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
        return this;
    }

    /**
     * 医院名称
     *
     * @return hospitalName
     **/
    @ApiModelProperty(required = true, value = "医院名称")
    @NotNull

    @Size(max = 100)
    public String getHospitalName() {
        return hospitalName;
    }

    public void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
    }

    public CreateTenantPaymentDto hospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
        return this;
    }

    /**
     * 医院编码
     *
     * @return hospitalCode
     **/
    @ApiModelProperty(required = true, value = "医院编码")
    @NotNull

    @Size(max = 100)
    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public CreateTenantPaymentDto paymentChannel(List<Integer> paymentChannel) {
        this.paymentChannel = paymentChannel;
        return this;
    }

    public CreateTenantPaymentDto addPaymentChannelItem(Integer paymentChannelItem) {
        this.paymentChannel.add(paymentChannelItem);
        return this;
    }

    /**
     * 支付渠道,0:微信直连商户支付；1:微信服务商支付；2:微信医保服务商模式；3:微信医保独立模式；4:龙支付
     *
     * @return paymentChannel
     **/
    @ApiModelProperty(required = true, value = "支付渠道,0:微信直连商户支付；1:微信服务商支付；2:微信医保服务商模式；3:微信医保独立模式；4:龙支付")
    @NotNull


    public List<Integer> getPaymentChannel() {
        return paymentChannel;
    }

    public void setPaymentChannel(List<Integer> paymentChannel) {
        this.paymentChannel = paymentChannel;
    }

    public CreateTenantPaymentDto enabled(Boolean enabled) {
        this.enabled = enabled;
        return this;
    }

    /**
     * 是否启用
     *
     * @return enabled
     **/
    @ApiModelProperty(value = "是否启用")


    public Boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }


    @Override
    public boolean equals(java.lang.Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CreateTenantPaymentDto createTenantPaymentDto = (CreateTenantPaymentDto) o;
        return Objects.equals(this.tenantId, createTenantPaymentDto.tenantId) &&
                Objects.equals(this.tenantName, createTenantPaymentDto.tenantName) &&
                Objects.equals(this.hospitalId, createTenantPaymentDto.hospitalId) &&
                Objects.equals(this.hospitalName, createTenantPaymentDto.hospitalName) &&
                Objects.equals(this.hospitalCode, createTenantPaymentDto.hospitalCode) &&
                Objects.equals(this.paymentChannel, createTenantPaymentDto.paymentChannel) &&
                Objects.equals(this.enabled, createTenantPaymentDto.enabled);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tenantId, tenantName, hospitalId, hospitalName, hospitalCode, paymentChannel, enabled);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class CreateTenantPaymentDto {\n");

        sb.append("    tenantId: ").append(toIndentedString(tenantId)).append("\n");
        sb.append("    tenantName: ").append(toIndentedString(tenantName)).append("\n");
        sb.append("    hospitalId: ").append(toIndentedString(hospitalId)).append("\n");
        sb.append("    hospitalName: ").append(toIndentedString(hospitalName)).append("\n");
        sb.append("    hospitalCode: ").append(toIndentedString(hospitalCode)).append("\n");
        sb.append("    paymentChannel: ").append(toIndentedString(paymentChannel)).append("\n");
        sb.append("    enabled: ").append(toIndentedString(enabled)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(java.lang.Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}

