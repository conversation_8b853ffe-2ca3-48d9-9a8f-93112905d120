package backend.common.entity.dto.hisgateway.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class DepartmentListRequest {

    @JsonProperty("hospital_code")
    private String hospitalCode;

    @JsonProperty("department_type")
    private Integer departmentType;

    @JsonProperty("start_date")
    private Long startDate;

    @JsonProperty("end_date")
    private Long endDate;

    @JsonProperty("parent_department_code")
    private String parentDepartmentCode;

    @JsonProperty("parent_department_name")
    private String parentDepartmentName;

}
