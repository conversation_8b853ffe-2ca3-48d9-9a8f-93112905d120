package com.ynhdkc.tenant.api;

import backend.common.exception.ErrResp;
import com.ynhdkc.tenant.DytTenantApplication;
import com.ynhdkc.tenant.config.H2Config;
import com.ynhdkc.tenant.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.context.annotation.Import;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.HashMap;
import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;


/**
 * <AUTHOR>
 * @since 2023/2/10 10:13:41
 */
@ExtendWith(SpringExtension.class)
@Import(H2Config.class)
@SpringBootTest(classes = DytTenantApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("dev")
class DictControllerIntegrationTest {

    private static final Integer PAGE_SIZE = 10;
    private static final Integer CURRENT_PAGE = 1;
    private String dictTypePath;
    private String dictLabelPath;
    @LocalServerPort
    private int port;
    @Autowired
    private TestRestTemplate restTemplate;

    @BeforeEach
    public void setUp() {
        String baseUrl = "http://localhost:".concat(port + "/apis/v1/tenant");
        dictTypePath = baseUrl.concat("/dicts/type");
        dictLabelPath = baseUrl.concat("/dicts/label");
    }

    @Test
    @Order(1)
    void createDictType_NullType_ShouldReturn400AndErrorMessage() {
        ResponseEntity<ErrResp> response = restTemplate.postForEntity(dictTypePath, new CreateDictTypeDto(), ErrResp.class);

        assertEquals(400, response.getStatusCodeValue());
        assertNotEquals(null, response.getBody());
        assertNotEquals(null, Objects.requireNonNull(response.getBody()).getMessage());

        assert response.getBody().getMessage() instanceof HashMap;
        assertNotEquals(null, ((HashMap<?, ?>) response.getBody().getMessage()).get("type"));
    }

    @Test
    @Order(2)
    @Sql(statements = "DELETE FROM t_dict_type WHERE type = 'test1'", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
    void createDictType_ShouldPass() {
        CreateDictTypeDto createDictTypeDto = new CreateDictTypeDto();
        createDictTypeDto.setType("test1");
        ResponseEntity<Long> response = restTemplate.postForEntity(dictTypePath, createDictTypeDto, Long.class);

        assertEquals(200, response.getStatusCodeValue());
        assertNotEquals(null, response.getBody());

        assert response.getBody() != null;
        assertEquals(1, response.getBody());
    }

    @Test
    @Order(3)
    @Sql(statements = "INSERT INTO t_dict_type (id,type) VALUES (1,'test1')", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    @Sql(statements = "DELETE FROM t_dict_type WHERE type = 'test1'", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
    void getDictTypeList_ShouldReturn1() {
        final String pagePath = dictTypePath + "?current_page=" + CURRENT_PAGE + "&page_size=" + PAGE_SIZE + "&name=test1";
        ResponseEntity<DictTypePageVo> response = restTemplate.getForEntity(pagePath, DictTypePageVo.class);

        assertEquals(200, response.getStatusCodeValue());
        assertNotEquals(null, response.getBody());
        assertEquals(1, Objects.requireNonNull(response.getBody()).getTotalSize());
    }

    @Test
    @Order(4)
    @Sql(statements = "INSERT INTO t_dict_type (id,type) VALUES (2,'test2')", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    @Sql(statements = "DELETE FROM t_dict_type WHERE type = 'test2'", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
    void updateDictType_ShouldPass() {
        UpdateDictTypeDto updateDictTypeDto = new UpdateDictTypeDto();
        updateDictTypeDto.setType("test2");
        restTemplate.put(dictTypePath + "/2", updateDictTypeDto);

        ResponseEntity<DictTypeVo> response = restTemplate.getForEntity(dictTypePath + "/2", DictTypeVo.class);
        assertEquals(2L, Objects.requireNonNull(response.getBody()).getId());
    }

    @Test
    @Order(5)
    @Sql(statements = "INSERT INTO t_dict_type (id,type) VALUES (3,'test3')", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    @Sql(statements = "DELETE FROM t_dict_type WHERE type = 'test3'", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
    void getDictTypeDetail_ShouldPass() {
        ResponseEntity<DictTypeVo> response = restTemplate.getForEntity(dictTypePath + "/3", DictTypeVo.class);
        assertEquals(3L, Objects.requireNonNull(response.getBody()).getId());
    }

    @Test
    @Order(6)
    @Sql(statements = "INSERT INTO t_dict_type (id,type) VALUES (4,'test4')", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    void deleteDictType_ShouldPass() {
        restTemplate.delete(dictTypePath + "/4");

        ResponseEntity<DictTypeVo> response = restTemplate.getForEntity(dictTypePath + "/4", DictTypeVo.class);
        assertEquals(404, response.getStatusCodeValue());
    }

    @Test
    @Order(7)
    @Sql(statements = "INSERT INTO t_dict_type (id,type) VALUES (5,'test5')", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    @Sql(statements = "DELETE FROM t_dict_type WHERE type = 'test5'", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
    void createDictLabel_ShouldReturn1() {
        CreateDictLabelDto createDictLabelDto = new CreateDictLabelDto();
        createDictLabelDto.setDictType("test5");
        createDictLabelDto.setDictLabel("A");
        createDictLabelDto.setDictValue("a");
        createDictLabelDto.setSort(1);
        createDictLabelDto.setDescription("a test for dict label");
        ResponseEntity<Long> createResponse = restTemplate.postForEntity(dictLabelPath, createDictLabelDto, Long.class);

        assertEquals(200, createResponse.getStatusCodeValue());
        assertNotEquals(null, createResponse.getBody());

        assert createResponse.getBody() != null;
        ResponseEntity<DictLabelVo> retrieveResponse = restTemplate.getForEntity(dictLabelPath + "/" + createResponse.getBody(), DictLabelVo.class);
        assertEquals(200, retrieveResponse.getStatusCodeValue());
        assertNotEquals(null, retrieveResponse.getBody());
        assertEquals("A", Objects.requireNonNull(retrieveResponse.getBody()).getDictLabel());
    }

    @Test
    @Order(8)
    @Sql(statements = "INSERT INTO t_dict_type (id,type) VALUES (6,'test6')", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    @Sql(statements = "INSERT INTO t_dict_label (id,dict_type,dict_label,dict_value,description) VALUES (1,'test6','a','A','some test')", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    @Sql(statements = "DELETE FROM t_dict_type WHERE type = 'test6'", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
    void deleteDictLabel_ShouldPass() {
        restTemplate.delete(dictLabelPath + "/1");

        ResponseEntity<DictLabelVo> response = restTemplate.getForEntity(dictLabelPath + "/1", DictLabelVo.class);
        assertEquals(404, response.getStatusCodeValue());
    }

    @Test
    @Order(9)
    @Sql(statements = "INSERT INTO t_dict_type (id,type) VALUES (7,'test7')", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    @Sql(statements = "INSERT INTO t_dict_label (id,dict_type,dict_label,dict_value,description) VALUES (2,'test7','a','A','some test')", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    @Sql(statements = "DELETE FROM t_dict_type WHERE type = 'test7'", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
    @Sql(statements = "DELETE FROM t_dict_label WHERE id = 2", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
    void updateDictLabel_ShouldPass() {
        UpdateDictLabelDto updateDictLabelDto = new UpdateDictLabelDto();
        updateDictLabelDto.setDictType("test7");
        updateDictLabelDto.setDictLabel("b");
        updateDictLabelDto.setDictValue("B");
        updateDictLabelDto.setDescription("some test");
        restTemplate.put(dictLabelPath + "/2", updateDictLabelDto);

        ResponseEntity<DictLabelVo> response = restTemplate.getForEntity(dictLabelPath + "/2", DictLabelVo.class);
        assertEquals("b", Objects.requireNonNull(response.getBody()).getDictLabel());
    }

    @Test
    @Order(10)
    @Sql(statements = "INSERT INTO t_dict_type (id,type) VALUES (8,'test8')", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    @Sql(statements = "INSERT INTO t_dict_label (id,dict_type,dict_label,dict_value,description) VALUES (4,'test8','a','A','some test')", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    @Sql(statements = "DELETE FROM t_dict_type WHERE type = 'test8'", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
    @Sql(statements = "DELETE FROM t_dict_label WHERE id = 4", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
    void getDictLabelDetail_ShouldPass() {
        ResponseEntity<DictLabelVo> response = restTemplate.getForEntity(dictLabelPath + "/4", DictLabelVo.class);
        assertEquals(4L, Objects.requireNonNull(response.getBody()).getId());
    }

    @Test
    @Order(11)
    @Sql(statements = "INSERT INTO t_dict_type (id,type) VALUES (9,'test9')", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    @Sql(statements = "INSERT INTO t_dict_label (id,dict_type,dict_label,dict_value,description) VALUES (5,'test9','a','A','some test')", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
    @Sql(statements = "DELETE FROM t_dict_type WHERE type = 'test9'", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
    @Sql(statements = "DELETE FROM t_dict_label WHERE id = 5", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
    void getDictLabelList_ShouldPass() {
        ResponseEntity<DictLabelPageVo> response = restTemplate.getForEntity(dictLabelPath + "?dict_type_name=test9&current_page=1&page_size=10", DictLabelPageVo.class);
        assertEquals(1, Objects.requireNonNull(response.getBody()).getTotalSize());
    }
}