package com.ynhdkc.tenant.api.customer;

import backend.security.service.BackendClientUserService;
import com.ynhdkc.tenant.handler.CustomerRecommendApi;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.customer.CustomerRecommendService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Api(tags = "CustomerRecommend")
@RequiredArgsConstructor
public class CustomerRecommendController implements CustomerRecommendApi {
    private final CustomerRecommendService customerRecommendService;
    private final BackendClientUserService backendClientUserService;

    @Override
    public ResponseEntity<RecentlyRegistrationDepartmentVo> getRecentRegistrationDepartment() {
        Long userId = backendClientUserService.getCurrentUserId();
        return ResponseEntity.ok(customerRecommendService.getRecentRegistrationDepartment(userId));
    }

    @Override
    public ResponseEntity<List<RecommendConfigDepartmentDto>> getRecommendDepartment(String bizType) {
        return ResponseEntity.ok(customerRecommendService.getRecommendDepartment(bizType));
    }

    @Override
    public ResponseEntity<CustomRecommendDoctorPageVo> getRecommendDepartmentDetail(CustomRecommendDoctorQueryDto request) {
        return ResponseEntity.ok(customerRecommendService.getRecommendDepartmentDetail(request));
    }

    @Override
    public ResponseEntity<RecommendRespVo> getRecommendDetail(Long recommendId, Double longitude, Double latitude) {
        return ResponseEntity.ok(customerRecommendService.getRecommendById(recommendId, longitude, latitude));
    }

    @Override
    public ResponseEntity<RecommendRespVo> getRecommendList(String bizType, Integer channel, String dataType, String dataTag, Double longitude, Double latitude) {
        return ResponseEntity.ok(customerRecommendService.getRecommendList(bizType, channel, dataType, dataTag, longitude, latitude));
    }

    @Override
    public ResponseEntity<CustomerDoctorScheduleInfoVo> queryGroupedDoctorListByDepartmentId(Long departmentId, String hospitalAreaCode, String departmentCode, Integer timeType, Long recommendId, String dataTag) {
        return ResponseEntity.ok(customerRecommendService.queryGroupedDoctorListByDepartmentId(departmentId, hospitalAreaCode, departmentCode, timeType, recommendId, dataTag));
    }


}
