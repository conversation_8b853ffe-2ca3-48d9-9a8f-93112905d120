package com.ynhdkc.tenant.entity.constant;

/**
 * <AUTHOR>
 * @since 2023/5/24 18:15:43
 */
public enum NavigatorTypeEnum {

    MAIN_NAVIGATOR(0, "主导航"),

    SUB_NAVIGATOR(1, "子导航");

    private final Integer code;

    private final String desc;

    NavigatorTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

}
