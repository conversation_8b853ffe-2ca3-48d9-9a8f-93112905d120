package com.ynhdkc.tenant.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * RoleBoundUsersQueryReqDto
 */
@Validated
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2023-04-14T13:35:23.401+08:00")


public class RoleBoundUsersQueryReqDto {
    @JsonProperty("role_id")
    private Long roleId = null;

    public RoleBoundUsersQueryReqDto roleId(Long roleId) {
        this.roleId = roleId;
        return this;
    }

    /**
     * Get roleId
     *
     * @return roleId
     **/
    @ApiModelProperty(required = true, value = "")
    @NotNull


    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }


    @Override
    public boolean equals(java.lang.Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        RoleBoundUsersQueryReqDto roleBoundUsersQueryReqDto = (RoleBoundUsersQueryReqDto) o;
        return Objects.equals(this.roleId, roleBoundUsersQueryReqDto.roleId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(roleId);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class RoleBoundUsersQueryReqDto {\n");

        sb.append("    roleId: ").append(toIndentedString(roleId)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(java.lang.Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}

