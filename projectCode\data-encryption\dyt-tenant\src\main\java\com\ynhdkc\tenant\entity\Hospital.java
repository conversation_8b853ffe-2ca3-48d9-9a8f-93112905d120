package com.ynhdkc.tenant.entity;

import backend.common.domain.tenant.HospitalEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.util.StringUtils;

import javax.persistence.Table;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/9 11:35:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(name = "t_hospital")
public final class Hospital extends HospitalEntity {
    public static final Long NO_PARENT = 0L;

    private Integer hospitalTypeTag;
    private String hospitalCode;
    private String name;
    private String levelDictType;
    private String levelDictValue;
    private String levelDictLabel;
    private String category;
    private Integer property;
    private String introduction;

    /**
     * 公告通知
     */
    private String announcement;
    private String environment;
    private Long addressId;
    private String appointmentCautionDictType;
    private String appointmentCautionDictLabel;
    private String contactPerson;
    private String contactPhoneNumber;
    private Integer status;
    private String tagDictType;
    private String tagDictLabel;
    private String tagDictValue;
    private Boolean display;
    private Boolean displayGuide;
    private Boolean displayFloor;
    private Integer displaySort;
    private String mapKeyword;
    private String stopServiceBeginTime;
    private String stopServiceEndTime;
    private Integer departmentLayer;
    private String logo;
    private String pictures;

    /**
     * 放号时间
     */
    private String appointmentSchedulingTime;

    public List<Integer> getCategories() {
        if (!StringUtils.hasText(category)) {
            return Collections.emptyList();
        }
        return Arrays.stream(category.split(","))
                .map(Integer::valueOf)
                .sorted()
                .collect(Collectors.toList());
    }

    public void setCategories(List<Integer> categoryList) {
        if (categoryList == null) {
            this.category = null;
            return;
        }
        this.category = categoryList.stream()
                .sorted()
                .map(String::valueOf)
                .collect(Collectors.joining(","));
    }

    public void setTagDictLabels(List<String> labels) {
        if (labels == null) {
            this.tagDictLabel = null;
            return;
        }
        this.tagDictLabel = labels.stream()
                .sorted()
                .collect(Collectors.joining(","));
    }

    public List<String> getTagDictLabels() {
        if (!StringUtils.hasText(tagDictLabel)) {
            return Collections.emptyList();
        }
        return Arrays.stream(tagDictLabel.split(","))
                .sorted()
                .collect(Collectors.toList());
    }
}
