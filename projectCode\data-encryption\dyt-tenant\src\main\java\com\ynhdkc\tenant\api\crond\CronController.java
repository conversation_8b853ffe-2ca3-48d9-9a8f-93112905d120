package com.ynhdkc.tenant.api.crond;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.ynhdkc.tenant.config.HospitalConfig;
import com.ynhdkc.tenant.handler.CrondApi;
import com.ynhdkc.tenant.service.xxl.CronTask;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@Api(tags = "Crond")
public class CronController implements CrondApi {

    @Resource
    private CronTask cronTask;

    @Resource
    private HospitalConfig hospitalConfig;


    @Override
    @XxlJob("stopDepartmentSchedule")
    public ResponseEntity<Void> stopDepartmentSchedule() {

        final List<String> departmentOutList = hospitalConfig.getDepartmentOutList();
        if (departmentOutList != null && !departmentOutList.isEmpty()) {
            departmentOutList.forEach(hospitalArea -> cronTask.getDepartmentStopSchedule(hospitalArea));
        }
        return ResponseEntity.ok().build();
    }

    @Override
    @XxlJob("stopHospitalAreaSchedule")
    public ResponseEntity<Void> stopHospitalAreaSchedule() {
        final List<String> hospitalAreaOutList = hospitalConfig.getHospitalAreaOutList();
        if (CollectionUtils.isEmpty(hospitalAreaOutList)) {
            return ResponseEntity.ok().build();
        }

        hospitalAreaOutList.forEach(hospitalArea -> cronTask.getHospitalAreaStopSchedule(hospitalArea));
        return ResponseEntity.ok().build();
    }


}
