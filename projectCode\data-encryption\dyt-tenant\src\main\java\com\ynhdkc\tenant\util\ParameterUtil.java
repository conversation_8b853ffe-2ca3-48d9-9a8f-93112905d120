package com.ynhdkc.tenant.util;

import cn.hutool.core.util.ReUtil;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-09 15:21
 */
public class ParameterUtil {
    private ParameterUtil() {
    }

    private static final Pattern TIME_REGEX = Pattern.compile("^([01]\\d|2[0-3]):[0-5][0-9]:[0-5][0-9]$");

    private static final Pattern CATEGORY_REGEX = Pattern.compile("^[0-9A-Za-z]$");

    @SuppressWarnings("BooleanMethodIsAlwaysInverted")
    public static boolean isTime(String time) {
        return ReUtil.isMatch(TIME_REGEX, time);
    }

    public static boolean isCategory(String category) {
        return ReUtil.isMatch(CATEGORY_REGEX, category);
    }

    public static boolean isCategory(Integer category) {
        return isCategory(String.valueOf(category));
    }
}
