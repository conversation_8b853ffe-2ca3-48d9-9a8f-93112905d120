package backend.common.kafka.streams;

import backend.common.kafka.constant.KafkaProperties;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.TopicBuilder;

/**
 * <AUTHOR>
 * @since 2023/2/27 16:34:54
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "backend.kafka.his.change-log")
@Slf4j
public class KafkaChangeLogTopicAutoConfiguration {
    private static final Integer DEFAULT_PARTITIONS = 1;
    private static final Short DEFAULT_REPLICATION_FACTOR = 1;
    private Integer partitions;
    private Short replicationFactor;

    @Bean
    @ConditionalOnProperty(name = "backend.kafka.his.change-log.enabled", havingValue = "true")
    public NewTopic hospitalInfoChangeLogTopic() {
        return TopicBuilder.name(KafkaProperties.HOSPITAL_TOPIC_NAME)
                .partitions(this.partitions == null ? DEFAULT_PARTITIONS : this.partitions)
                .replicas(this.replicationFactor == null ? DEFAULT_REPLICATION_FACTOR : this.replicationFactor)
                .compact()
                .build();
    }

    @Bean
    @ConditionalOnProperty(name = "backend.kafka.his.change-log.enabled", havingValue = "true")
    public NewTopic doctorInfoChangeLogTopic() {
        return TopicBuilder.name(KafkaProperties.DOCTOR_TOPIC_NAME)
                .partitions(partitions == null ? DEFAULT_PARTITIONS : partitions)
                .replicas(replicationFactor == null ? DEFAULT_REPLICATION_FACTOR : replicationFactor)
                .compact()
                .build();
    }

    @Bean
    @ConditionalOnProperty(name = "backend.kafka.his.change-log.enabled", havingValue = "true")
    public NewTopic syncScheduleRequest() {
        return TopicBuilder.name(KafkaProperties.SYNC_SCHEDULE_REQUEST)
                .partitions(partitions == null ? DEFAULT_PARTITIONS : partitions)
                .replicas(replicationFactor == null ? DEFAULT_REPLICATION_FACTOR : replicationFactor)
                .compact()
                .build();
    }
}
