package com.ynhdkc.tenant.service.backend.impl;

import backend.common.constants.KafkaTopicConfig;
import backend.common.dao.respository.HisGatewayMessageRepository;
import backend.common.entity.dto.hisgateway.request.DepartmentDoctorListRequest;
import backend.common.entity.dto.hisgateway.request.DepartmentListRequest;
import backend.common.entity.dto.hisgateway.request.SyncDoctorDepartmentItem;
import backend.common.entity.dto.hisgateway.request.SyncDoctorScheduleRequest;
import backend.common.enums.HospitalCode;
import backend.common.kafka.KafkaPublisher;
import backend.common.kafka.constant.KafkaProperties;
import backend.common.util.ListUtil;
import backend.common.util.MessageUtil;
import backend.common.util.ObjectsUtils;
import backend.common.util.StringUtils;
import com.ynhdkc.tenant.client.HisGatewayClient;
import com.ynhdkc.tenant.client.ScheduleClient;
import com.ynhdkc.tenant.client.model.DepartmentList4MultiLevelResponse;
import com.ynhdkc.tenant.dao.DepartmentQuery;
import com.ynhdkc.tenant.dao.HospitalAreaQuery;
import com.ynhdkc.tenant.dao.HospitalAreaSettingQuery;
import com.ynhdkc.tenant.dao.HospitalQuery;
import com.ynhdkc.tenant.dto.DateRangeDto;
import com.ynhdkc.tenant.dto.RawMessageEnvelope;
import com.ynhdkc.tenant.dto.TimeSpanDto;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import com.ynhdkc.tenant.entity.setting.BaseSetting;
import com.ynhdkc.tenant.service.backend.DepartmentService;
import com.ynhdkc.tenant.service.backend.IDoctorService;
import com.ynhdkc.tenant.service.backend.IHisRequestService;
import com.ynhdkc.tenant.tool.CacheComponent;
import com.ynhdkc.tenant.tool.DateUtils;
import com.ynhdkc.tenant.tool.DoctorListTool;
import com.ynhdkc.tenant.util.HospitalUtils;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.ynhdkc.tenant.util.DateUtil.convertLocalDateToLong;

/**
 * <AUTHOR>
 * @since 2023/7/11 16:55:34
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HisRequestServiceImpl implements IHisRequestService {
    private static final int SYNC_PAGE_SIZE = 60;
    private static final long WAITING_TIME = 1000L;
    private static final List<String> MORNING_HOSPITAL_CODE_SET = new ArrayList<>();
    private static final Set<String> FORCE_REQUEST_HOSPITAL_SET = new HashSet<>();

    static {
        MORNING_HOSPITAL_CODE_SET.add(HospitalCode.KUNMING_MU_FIRST_AFFILIATED_CG_CAMPUS_HOSPITAL.getCode());
        MORNING_HOSPITAL_CODE_SET.add(HospitalCode.KUNMING_MU_FIRST_AFFILIATED_HOSPITAL.getCode());
        MORNING_HOSPITAL_CODE_SET.add(HospitalCode.YAN_AN_HOSPITAL.getCode());

        FORCE_REQUEST_HOSPITAL_SET.add(HospitalCode.YUNNAN_FIRST_PEOPLE_HOSPITAL.getCode());
    }

    private final HospitalAreaQuery hospitalAreaQuery;
    private final DepartmentQuery departmentQuery;
    private final HospitalAreaSettingQuery hospitalAreaSettingQuery;
    private final HisGatewayMessageRepository hisGatewayMessageRepository;
    private final ScheduleClient scheduleApi;
    private final KafkaPublisher kafkaPublisher;
    private final HospitalQuery hospitalQuery;
    private final DoctorListTool doctorListTool;
    private final CacheComponent cacheComponent;
    private final HisGatewayClient hisGatewayClient;
    private final DepartmentService departmentService;
    private final IDoctorService doctorService;

    @Override
    public void requestDepartmentDoctorsFromHis(@NonNull Department department) {
        Long hospitalId = department.getHospitalId();
        Long hospitalAreaId = department.getHospitalAreaId();
        Long departmentId = department.getId();
        String hospitalCode = department.getHospitalCode();
        String departmentCode = department.getThrdpartDepCode();
        TimeSpanDto timeSpan = scheduleApi.getTimeSpan(hospitalId, hospitalAreaId, departmentId);
        if (timeSpan == null) {
            log.info("requestDepartmentDoctorsFromHis 获取排班周期异常，医院 id：{}，院区 id：{}，科室 id：{}", hospitalId, hospitalAreaId, departmentId);
            return;
        }

        log.info("requestDepartmentDoctorsFromHis 获取排班周期，医院 id：{}，院区 id：{}，科室 id：{}，开始时间：{}，结束时间：{}", hospitalId, hospitalAreaId, departmentId, timeSpan.getStartTime(), timeSpan.getEndTime());
        DepartmentDoctorListRequest request = new DepartmentDoctorListRequest();
        request.setHospitalCode(hospitalCode);
        request.setDepartmentCode(departmentCode);
        request.setDepartmentId(departmentId);
        request.setStartDate(timeSpan.getStartTime().getTime());
        request.setEndDate(timeSpan.getEndTime().getTime());
        processDoctorList(hospitalCode, departmentId, request);
        hisGatewayMessageRepository.send(request, hospitalId, hospitalAreaId, departmentId);

        RawMessageEnvelope rawMessageEnvelope = new RawMessageEnvelope();
        rawMessageEnvelope.setHospitalId(hospitalId);
        rawMessageEnvelope.setHospitalAreaId(hospitalAreaId);
        rawMessageEnvelope.setDepartmentId(departmentId);
        RawMessageEnvelope.PayLoad payLoad = new RawMessageEnvelope.PayLoad();
        payLoad.setHospitalCode(hospitalCode);
        payLoad.setDepartmentCode(departmentCode);
        payLoad.setDepartmentId(departmentId);
        kafkaPublisher.publishMsg(KafkaProperties.SYNC_SCHEDULE_REQUEST, rawMessageEnvelope);
    }

    @Override
    public void requestDepartmentsFromHis() {
        List<Hospital> hospitals = hospitalAreaQuery.listHospitalsThatDependOnHis();
        if (CollectionUtils.isEmpty(hospitals)) {
            return;
        }
        hospitals.forEach(hospital -> {
            if (!hospital.getHospitalCode().equals(HospitalCode.KUNMING_MU_SECOND_AFFILIATED_HOSPITAL.getCode())) {
                hospitalAreaSendSyncDepartmentsRequest(hospital, FORCE_REQUEST_HOSPITAL_SET.contains(hospital.getHospitalCode()));
            }
        });
    }

    public void updateGongRenHospitalDepartmentInfo() {
        ResponseEntity<DepartmentList4MultiLevelResponse> departmentList4MultiLevel = hisGatewayClient.getDepartmentList4MultiLevel(HospitalCode.KUNMING_MU_SECOND_AFFILIATED_HOSPITAL.getCode());
        if (departmentList4MultiLevel == null || departmentList4MultiLevel.getBody() == null) {
            return;
        }

        List<DepartmentList4MultiLevelResponse.Result> result = departmentList4MultiLevel.getBody().getResult();
        if (result == null) {
            return;
        }

        log.info("updateGongRenHospitalDepartmentInfo: {}", MessageUtil.object2JSONString(result));

        departmentService.createOrUpdateGongRenHospitalDepartmentInfo(result);
        doctorService.updateGongRenDoctorDepartmentInfo();
    }

    @Override
    public void requestOneDepartment(String hospitalCode) {
        Hospital hospital = hospitalAreaQuery.queryHospitalAreaBy(hospitalCode);
        if (hospital == null) {
            log.error("query_hospital_is_not_found: {}", hospitalCode);
            return;
        }
        log.info("send_request_one_department: {}", hospital.getName());
        hospitalAreaSendSyncDepartmentsRequest(hospital, true);
    }

    @Override
    public void requestGongRenDepartmentsFromHis() {
        updateGongRenHospitalDepartmentInfo();
    }

    @Override
    public void requestDepartmentsFromHisBy(Long hospitalAreaId) {
        Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaBy(hospitalAreaId);
        if (hospitalArea == null) {
            log.info("requestDepartmentsFromHisBy 院区不存在，医院院区 id：{}", hospitalAreaId);
            return;
        }
        hospitalAreaSendSyncDepartmentsRequest(hospitalArea, FORCE_REQUEST_HOSPITAL_SET.contains(hospitalArea.getHospitalCode()));
    }

    @Override
    public void syncDoctorSchedule() {
        log.info("sync_doctor_schedule_begin");

        //过滤云大医院，因为凌晨查号异常
        List<Hospital> morningHospitalList = hospitalAreaQuery.queryHospitalList(MORNING_HOSPITAL_CODE_SET);
        Set<Long> morningHospitalIds = morningHospitalList.stream().map(Hospital::getId).collect(Collectors.toSet());
        List<Long> hospitalAreaIds = hospitalQuery.queryHisHospital();
        if (hospitalAreaIds == null) {
            return;
        }

        log.info("sync_doctor_schedule_area_is {}", MessageUtil.object2JSONString(hospitalAreaIds));

        hospitalAreaIds = hospitalAreaIds.stream().filter(y -> !morningHospitalIds.contains(y)).collect(Collectors.toList());
        List<AppointmentRuleSetting> appointmentRuleSettings = hospitalAreaSettingQuery.queryAppointmentRuleSettingInId(hospitalAreaIds);
        Map<Long, AppointmentRuleSetting> appointmentRuleSettingMap = appointmentRuleSettings.stream().collect(Collectors.toMap(BaseSetting::getHospitalAreaId, y -> y));

        DateRangeDto dateRangeDto = DateUtils.getDateRange(7);


        log.info("sync_doctor_schedule_area_2_is {}", MessageUtil.object2JSONString(hospitalAreaIds));


        Set<Long> hisHospitalIdSet = new HashSet<>(hospitalAreaIds);
        for (int i = 'A'; i <= 'Z'; i++) {
            List<Department> departmentList = departmentQuery.queryList((char) i);

            log.info("sync_doctor_department_list_is {}", MessageUtil.object2JSONString(departmentList));

            processDoctorRecords(departmentList, hisHospitalIdSet, appointmentRuleSettingMap,
                    dateRangeDto.getStartDate(), dateRangeDto.getEndDate());
        }
    }

    @Override
    public void syncDoctorSchedule(String hospitalCode) {
        List<Long> hospitalAreaIds = hospitalQuery.queryHisHospital(hospitalCode);
        if (hospitalAreaIds == null) {
            return;
        }
        List<String> hospitalCodeList = new ArrayList<>();
        hospitalCodeList.add(hospitalCode);

        List<AppointmentRuleSetting> appointmentRuleSettings = hospitalAreaSettingQuery.queryAppointmentRuleSettingInId(hospitalAreaIds);
        Map<Long, AppointmentRuleSetting> appointmentRuleSettingMap = appointmentRuleSettings.stream().collect(Collectors.toMap(BaseSetting::getHospitalAreaId, y -> y));

        DateRangeDto dateRangeDto = DateUtils.getDateRange(7);

        log.info("sync_hospital_info_is {}", MessageUtil.object2JSONString(hospitalAreaIds));

        Set<Long> hisHospitalIdSet = new HashSet<>(hospitalAreaIds);
        for (int i = 'A'; i <= 'Z'; i++) {
            List<Department> departmentList = departmentQuery.queryList((char) i, hospitalCodeList);
            log.info("department_list_is: {}", departmentList.size());

            processDoctorRecords(departmentList, hisHospitalIdSet, appointmentRuleSettingMap, dateRangeDto.getStartDate(),
                    dateRangeDto.getEndDate());
        }
    }

    @Override
    public void syncHospitalDepartmentDoctorInMorning() {
        List<Hospital> hospitalList = hospitalAreaQuery.queryHospitalList(MORNING_HOSPITAL_CODE_SET);
        Set<Long> hospitalIdSet = hospitalList.stream().map(Hospital::getId).collect(Collectors.toSet());
        List<String> hospitalCodeList = hospitalList.stream().map(Hospital::getHospitalCode).collect(Collectors.toList());

        List<Long> hospitalAreaIds = new ArrayList<>(hospitalIdSet);
        DateRangeDto dateRangeDto = DateUtils.getDateRange(7);

        log.info("hospital_ids: {}", MessageUtil.object2JSONString(hospitalAreaIds));

        List<AppointmentRuleSetting> appointmentRuleSettings = hospitalAreaSettingQuery.queryAppointmentRuleSettingInId(hospitalAreaIds);
        Map<Long, AppointmentRuleSetting> appointmentRuleSettingMap = appointmentRuleSettings.stream().collect(Collectors.toMap(BaseSetting::getHospitalAreaId, y -> y));
        for (int i = 'A'; i <= 'Z'; i++) {
            processDoctorRecords(departmentQuery.queryList((char) i, hospitalCodeList), hospitalIdSet, appointmentRuleSettingMap,
                    dateRangeDto.getStartDate(), dateRangeDto.getEndDate());
        }
    }

    private void processDoctorRecords(List<Department> departmentList, Set<Long> hisHospitalIdSet,
                                      Map<Long, AppointmentRuleSetting> appointmentRuleSettingMap,
                                      long startDate, long endDate) {
        if (departmentList == null || departmentList.isEmpty()) {
            return;
        }
        departmentList = departmentList.stream().filter(y -> hisHospitalIdSet.contains(y.getHospitalAreaId())).collect(Collectors.toList());
        Collections.shuffle(departmentList);


        log.info("send_department_list_is {}", departmentList.size());


        if (departmentList.size() <= SYNC_PAGE_SIZE) {
            sendSyncRequestMessage(departmentList, appointmentRuleSettingMap, startDate, endDate);
            try {
                Thread.sleep(WAITING_TIME);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.info("processDoctorRecords_error", e);
            }
        } else {
            List<List<Department>> multiResult = ListUtil.divideList(departmentList, SYNC_PAGE_SIZE);
            for (List<Department> result : multiResult) {
                sendSyncRequestMessage(result, appointmentRuleSettingMap, startDate, endDate);
                try {
                    Thread.sleep(WAITING_TIME);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.info("processDoctorRecords_error", e);
                }
            }
        }
    }

    private void sendSyncRequestMessage(List<Department> departments, Map<Long, AppointmentRuleSetting> appointmentRuleSettingMap,
                                        long startDate, long endDate) {
        log.info("sendSyncRequestMessage_begin {}", departments.size());

        SyncDoctorScheduleRequest request = new SyncDoctorScheduleRequest();
        request.setDepartments(assembleItemlist(departments, appointmentRuleSettingMap, startDate, endDate));

        log.info("sendSyncRequestMessage_request_is {}", MessageUtil.object2JSONString(request));

        batchRequestDepartmentDoctorSchedule(request);
    }

    private List<SyncDoctorDepartmentItem> assembleItemlist(List<Department> departments,
                                                            Map<Long, AppointmentRuleSetting> appointmentRuleSettingMap,
                                                            long startDate, long endDate) {
        return departments.stream().map(department -> {
            SyncDoctorDepartmentItem item = new SyncDoctorDepartmentItem();
            item.setHospitalId(department.getHospitalId());
            item.setHospitalAreaId(department.getHospitalAreaId());
            item.setDepartmentId(department.getId());
            item.setHospitalCode(department.getHospitalCode());
            item.setDepartmentCode(department.getThrdpartDepCode());
            AppointmentRuleSetting appointmentRuleSetting = appointmentRuleSettingMap.get(department.getHospitalAreaId());
            boolean needPartitionTime = null != appointmentRuleSetting && Boolean.TRUE.equals(appointmentRuleSetting.getNeedPartitionTime());
            item.setNeedTimeSchedule(needPartitionTime);
            item.setStartDate(startDate);
            item.setEndDate(endDate);
            item.setMessageTraceId(StringUtils.getUUID(32));
            return item;
        }).collect(Collectors.toList());
    }

    private void hospitalAreaSendSyncDepartmentsRequest(Hospital hospitalArea, boolean forceRequest) {
        if (!forceRequest && Boolean.TRUE.equals(cacheComponent.isRequestHospitalDepartmentStatus(hospitalArea.getId()))) {
            return;
        }

        Pair<Integer, Integer> advanceAndForbiddenDays = doctorListTool.defaultAdvanceAndForbiddenDays(hospitalArea.getId());
        Integer advanceDay = advanceAndForbiddenDays.getLeft();
        Integer forbiddenDay = advanceAndForbiddenDays.getRight();

        LocalDate now = LocalDate.now();
        LocalDate start = now.plusDays(forbiddenDay);
        LocalDate end = now.plusDays(advanceDay);

        log.info("hospitalAreaSendSyncDepartmentsRequest 获取排班周期，医院 id：{}，院区 id：{}，开始时间：{}，结束时间：{}", hospitalArea.getParentId(), hospitalArea.getId(), start, end);

        DepartmentListRequest request = new DepartmentListRequest();
        request.setHospitalCode(hospitalArea.getHospitalCode());
        request.setDepartmentType(hospitalArea.getDepartmentLayer());
        request.setStartDate(convertLocalDateToLong(start));
        request.setEndDate(convertLocalDateToLong(end));
        hisGatewayMessageRepository.send(request, hospitalArea.getParentId(), hospitalArea.getId());
        cacheComponent.cacheRequestHospitalDepartmentStatus(hospitalArea.getId());
    }


    private void batchRequestDepartmentDoctorSchedule(SyncDoctorScheduleRequest request) {
        if (null == request || CollectionUtils.isEmpty(request.getDepartments()) || request.getDepartments().size() > 60) {
            return;
        }
        log.info("batchRequestDepartmentDoctorSchedule_begin_2");
        for (SyncDoctorDepartmentItem departmentItem : request.getDepartments()) {
            kafkaPublisher.publishMsg(KafkaTopicConfig.SyncDoctorSchedule.REQUEST_TOPIC_NAME, departmentItem);
        }
    }

    private void processDoctorList(String hospitalCode, long departmentId, DepartmentDoctorListRequest request) {
        if (!HospitalUtils.isKunmingMU1stHospital(hospitalCode)) {
            return;
        }
        Department department = departmentQuery.queryDepartmentById(departmentId);
        if (department == null || ObjectsUtils.isEmpty(department.getDoctors())) {
            return;
        }
        request.setDoctors(department.getDoctors());
    }

}
