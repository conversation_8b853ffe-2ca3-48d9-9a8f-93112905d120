package backend.common.entity.dto.hisgateway.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class DoctorScheduleListRequest {

    @JsonProperty("start_date")
    private Long startDate;

    @JsonProperty("end_date")
    private Long endDate;

    // 滇医通自定义唯一标识
    @JsonProperty("hospital_code")
    private String hospitalCode;

    // code 是 his 的唯一标识
    @JsonProperty("department_code")
    private String departmentCode;

    @JsonProperty("department_name")
    private String departmentName;

    @JsonProperty("parent_department_code")
    private String parentDepartmentCode;

    private Integer type;

    // code 是 his 的唯一标识
    @JsonProperty("doctor_code")
    private String doctorCode;
}
