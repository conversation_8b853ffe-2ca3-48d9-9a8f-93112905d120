package com.ynhdkc.tenant.entity;

import backend.common.domain.BaseEntity;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;
import java.util.List;
import java.util.stream.Collectors;

@EqualsAndHashCode(callSuper = true)
@Table(name = "t_channel_source")
@Data
public class ChannelSource extends BaseEntity {
    private Long sourceId;
    private String channelCode;
    private Integer sourceType;

    public List<Integer> getChannelCodeList() {
        List<String> split = StrUtil.split(channelCode, ',');
        return split.stream().map(Integer::parseInt).collect(Collectors.toList());
    }

    public void setChannelCodeStr(List<Integer> channelCodes) {
        this.channelCode = channelCodes.stream().sorted().map(String::valueOf).collect(Collectors.joining(","));
    }
}
