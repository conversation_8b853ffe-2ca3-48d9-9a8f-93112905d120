package backend.common.entity.dto.hisgateway.request;

import backend.common.entity.dto.hisgateway.enums.SyncLevel;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class SyncDoctorDepartmentItem {

    @JsonProperty("hospital_id")
    private Long hospitalId;

    @JsonProperty("hospital_area_id")
    private Long hospitalAreaId;

    @JsonProperty("department_id")
    private Long departmentId;

    @JsonProperty("doctor_id")
    private Long doctorId;

    @JsonProperty("hospital_code")
    private String hospitalCode;

    @JsonProperty("department_code")
    private String departmentCode;

    @JsonProperty("start_date")
    private Long startDate;

    @JsonProperty("end_date")
    private Long endDate;

    @JsonProperty("need_time_schedule")
    private boolean needTimeSchedule;

    @JsonProperty("message_trace_id")
    private String messageTraceId;

    @JsonProperty("smart_process")
    private boolean smartProcess;

    @JsonProperty("sync_level")
    private SyncLevel syncLevel;
}
