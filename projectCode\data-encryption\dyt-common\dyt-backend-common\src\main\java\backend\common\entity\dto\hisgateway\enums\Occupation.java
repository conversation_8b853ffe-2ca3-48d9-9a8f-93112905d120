package backend.common.entity.dto.hisgateway.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum Occupation {

	GOV("国家机关党群组织、企事业单位负责人", "0"), TECHNICAL_STAFF("各类专业技术人员", "1"), STAFF("办事人员和有关人员", "3"), CLERK("商业、服务业人员", "4"),
	WORKER("农、林、牧、渔、水利业生产人员", "5"), OPERATOR("生产、运输设备操作人员及有关人员", "6"), SOLDIER("军人", "X"), OTHER("不便分类的其他从业人员", "Y");

	private final String value;

	private final String code;

	Occupation(String value, String code) {
		this.value = value;
		this.code = code;
	}

	@JsonCreator
	public static Occupation getFromCode(String code) {
		for (Occupation t : Occupation.values()) {
			if (t.getCode().equals(code)) {
				return t;
			}
		}
		throw new IllegalArgumentException("职业分类不存在");
	}

	public static Occupation getFromValue(String value) {
		for (Occupation t : Occupation.values()) {
			if (t.getValue().equals(value)) {
				return t;
			}
		}
		throw new IllegalArgumentException("职业分类不存在");
	}

	public String getValue() {
		return value;
	}

	public String getCode() {
		return code;
	}

}
