package com.ynhdkc.tenant.api.backend;

import backend.common.response.BaseOperationResponse;
import backend.security.oplog.DytSecurityRequired;
import com.ynhdkc.tenant.handler.SearchManagementApi;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.SearchManagementService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/9/12 15:48
 */
@Api(tags = "SearchManagement")
@RestController
public class SearchManagementController implements SearchManagementApi {
    private final SearchManagementService searchManagementService;

    public SearchManagementController(@Qualifier("localSearchManagementServiceImpl") SearchManagementService searchManagementService) {
        this.searchManagementService = searchManagementService;
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "search:management:create:dict")
    public ResponseEntity<DictFileVo> createDict(DictFileCreateReqDto request) {
        return ResponseEntity.ok(searchManagementService.createDict(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "search:management:delete:dict")
    public ResponseEntity<BaseOperationResponse> deleteDict(Long dictId) {
        return ResponseEntity.ok(searchManagementService.deleteDict(dictId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "search:management:get:dict")
    public ResponseEntity<DictFileVo> getDictDetail(Long dictId) {
        return ResponseEntity.ok(searchManagementService.getDictDetail(dictId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "search:management:query:dict")
    public ResponseEntity<DictFileQueryRespDto> queryDict(DictFileQueryReqDto request) {
        return ResponseEntity.ok(searchManagementService.queryDict(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "search:management:sync:dict")
    public ResponseEntity<BaseOperationResponse> syncDict() {
        return ResponseEntity.ok(searchManagementService.syncDict());
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "search:management:update:dict")
    public ResponseEntity<DictFileVo> updateDict(DictFileUpdateReqDto request) {
        return ResponseEntity.ok(searchManagementService.updateDict(request));
    }
}
