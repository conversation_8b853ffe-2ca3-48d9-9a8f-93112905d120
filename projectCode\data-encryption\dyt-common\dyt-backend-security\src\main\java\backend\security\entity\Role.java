package backend.security.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.Map;

@Data
@Accessors(chain = true)
public class Role {
    public Role() {
        this.endpoints = new HashMap<>();
    }

    @JsonProperty("id")
    private Long id;
    @JsonProperty("name")
    private String name;
    @JsonProperty("endpoints")
    private Map<Long, Endpoint> endpoints;

    public void putEndpoint(Long endpointId, String path, String method) {
        this.endpoints.put(endpointId, new Endpoint()
                .setId(endpointId)
                .setPath(path)
                .setMethod(method));
    }
}

