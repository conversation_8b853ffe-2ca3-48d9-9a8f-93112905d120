package com.example.encryption.repository;

import com.example.encryption.entity.MigrationUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 迁移用户数据访问层
 * 提供影子字段迁移相关的数据操作方法
 */
@Repository
public interface MigrationUserRepository extends JpaRepository<MigrationUser, Long> {
    
    /**
     * 根据用户名查找用户
     */
    Optional<MigrationUser> findByUsername(String username);
    
    /**
     * 根据迁移状态查找用户
     */
    List<MigrationUser> findByMigrationStatus(MigrationUser.MigrationStatus migrationStatus);
    
    /**
     * 根据数据版本查找用户
     */
    List<MigrationUser> findByDataVersion(Integer dataVersion);
    
    /**
     * 查找需要迁移的用户（版本号小于指定值）
     */
    @Query("SELECT u FROM MigrationUser u WHERE u.dataVersion < :targetVersion")
    List<MigrationUser> findUsersNeedingMigration(@Param("targetVersion") Integer targetVersion);
    
    /**
     * 统计各迁移状态的用户数量
     */
    @Query("SELECT u.migrationStatus, COUNT(u) FROM MigrationUser u GROUP BY u.migrationStatus")
    List<Object[]> countByMigrationStatus();
    
    /**
     * 统计各数据版本的用户数量
     */
    @Query("SELECT u.dataVersion, COUNT(u) FROM MigrationUser u GROUP BY u.dataVersion")
    List<Object[]> countByDataVersion();
    
    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);
}
