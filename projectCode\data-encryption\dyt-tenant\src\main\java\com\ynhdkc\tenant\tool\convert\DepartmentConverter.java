package com.ynhdkc.tenant.tool.convert;

import com.google.common.collect.Lists;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.tool.DoctorListTool;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Collections;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/8/7 10:19
 */
public class DepartmentConverter {
    private DepartmentConverter() {
    }

    public static Department toDepartment(DepartmentCreateReqDto request) {
        Department entity = new Department();
        entity.setTenantId(request.getTenantId());
        entity.setHospitalId(request.getHospitalId());
        entity.setHospitalAreaId(request.getHospitalAreaId());
        entity.setParentId(request.getParentId());
        entity.setThrdpartDepCode(request.getThrdpartDepCode());
        entity.setName(request.getName());
        entity.setShortName(request.getShortName());
        entity.setRecommended(request.isRecommended());
        entity.setCategoryStr(request.getCategory());
        entity.setFirstLetter(request.getFirstLetter());
        entity.setIntroduction(request.getIntroduction());
        entity.setCaution(request.getCaution());
        entity.setAddressIntro(request.getAddressIntro());
        entity.setSort(request.getSort());
        entity.setSystemDepends(request.getSystemDepends());
        entity.setEnabled(request.isEnabled());
        entity.setDisplayBgColor(request.getDisplayBgColor());
        entity.setEnableDepartmentDetail(request.isEnableDepartmentDetail());
        entity.setUri(request.getUri());
        if (null == request.getForbiddenDay() || request.getForbiddenDay() < 0) {
            entity.setForbiddenDay(-1);
        } else {
            entity.setForbiddenDay(request.getForbiddenDay());
        }
        if (null == request.getAdvanceDay() || request.getAdvanceDay() < 0) {
            entity.setAdvanceDay(-1);
        } else {
            entity.setAdvanceDay(request.getAdvanceDay());
        }
        if (StringUtils.hasText(request.getSourceActivateTime())) {
            entity.setSourceActivateTime(request.getSourceActivateTime());
        } else {
            entity.setSourceActivateTime(DoctorListTool.DEFAULT_SOURCE_ACTIVATE_TIME);
        }
        entity.setRemark(request.getRemark());
        entity.setTriageDeskAddress(request.getTriageDeskAddress());
        entity.setLevelTag(request.getLevelTag());
        if (!CollectionUtils.isEmpty(request.getDoctors())) {
            entity.setDoctors(String.join("|", request.getDoctors()));
        }
        if (StringUtils.hasText(request.getDisplayDepartmentName())) {
            entity.setDisplayDepartmentName(request.getDisplayDepartmentName());
        }
        entity.setDisplayFields(request.getDisplayFields());
        return entity;
    }

    public static DepartmentVo toDepartmentVo(Department entity) {
        DepartmentVo vo = new DepartmentVo();
        setDepartmentVo(vo, entity);
        return vo;
    }

    public static DepartmentDetailVo toDepartmentDetailVo(Department entity) {
        DepartmentDetailVo vo = new DepartmentDetailVo();
        setDepartmentVo(vo, entity);
        vo.setShortName(entity.getShortName());
        vo.setIntroduction(entity.getIntroduction());
        vo.setRecommended(entity.getRecommended());
        vo.setCaution(entity.getCaution());
        vo.setFirstLetter(entity.getFirstLetter());
        vo.setTriageDeskAddress(entity.getTriageDeskAddress());
        String doctors = entity.getDoctors();
        if (doctors != null) {
            String[] split = doctors.split("\\|");
            vo.setDoctors(Arrays.asList(split));
        }
        vo.setLevelTag(entity.getLevelTag());
        vo.setDisplayFields(entity.getDisplayFields());
        vo.setDisplayDepartmentName(entity.getDisplayDepartmentName());
        vo.setAppointmentNotifyContact(entity.getAppointmentNotifyContact());
        vo.setNoticeTemplateId(entity.getNoticeTemplateId());
        return vo;
    }

    public static DepartmentsTreeSearchVo toDepartmentsTreeSearchVo(Department entity) {
        DepartmentsTreeSearchVo vo = new DepartmentsTreeSearchVo();
        vo.setId(entity.getId());
        vo.setName(entity.getName());
        vo.setFirstLetter(entity.getFirstLetter());
        vo.setDisplayBgColor(entity.getDisplayBgColor());
        vo.setUri(entity.getUri());
        vo.setSort(entity.getSort());
        vo.setCategory(entity.getCategoryStr());
        vo.setParentId(entity.getParentId());
        vo.setThrdpartDepCode(entity.getThrdpartDepCode());
        vo.setAddressIntro(entity.getAddressIntro());
        vo.setEnabled(entity.getEnabled());
        vo.setCreateTime(entity.getCreateTime());
        vo.setHospitalCode(entity.getHospitalCode());
        vo.setRemark(entity.getRemark());
        vo.setDisplayFields(entity.getDisplayFields());
        vo.setDisplayDepartmentName(entity.getDisplayDepartmentName());
        return vo;
    }

    private static <T extends DepartmentVo> void setDepartmentVo(T vo, Department entity) {
        vo.id(entity.getId());
        vo.tenantId(entity.getTenantId());
        vo.hospitalId(entity.getHospitalId());
        vo.hospitalAreaId(entity.getHospitalAreaId());
        vo.parentId(entity.getParentId());
        vo.thrdpartDepCode(entity.getThrdpartDepCode());
        vo.name(entity.getName());
        vo.sort(entity.getSort());
        vo.systemDepends(entity.getSystemDepends());
        vo.enabled(entity.getEnabled());
        vo.category(entity.getCategoryStr());
        vo.setDisplayBgColor(entity.getDisplayBgColor());
        vo.setEnableDepartmentDetail(entity.getEnableDepartmentDetail());
        vo.setUri(entity.getUri());
        vo.setAddressIntro(entity.getAddressIntro());
        vo.setRemark(entity.getRemark());
        vo.setForbiddenDay(entity.getForbiddenDay());
        vo.setAdvanceDay(entity.getAdvanceDay());
        vo.setSourceActivateTime(entity.getSourceActivateTime());
        vo.setCreateTime(entity.getCreateTime());
        vo.setAppointmentNotifyContact(entity.getAppointmentNotifyContact());
        vo.setNoticeTemplateId(entity.getNoticeTemplateId());
    }

    public static CustomerDepartmentVo toCustomerDepartmentVo(Department entity) {
        CustomerDepartmentVo vo = new CustomerDepartmentVo();
        vo.setId(entity.getId());
        vo.setName(entity.getName());
        vo.setParentId(entity.getParentId());
        vo.setHospitalId(entity.getHospitalId());
        vo.setHospitalAreaId(entity.getHospitalAreaId());
        vo.setThrdpartDepCode(entity.getThrdpartDepCode());
        vo.setHospitalAreaCode(entity.getHospitalCode());
        vo.setDisplayBgColor(entity.getDisplayBgColor());
        vo.setUri(entity.getUri());
        vo.setFirstLetter(entity.getFirstLetter());
        vo.setAddressIntro(entity.getAddressIntro());
        vo.setCategory(entity.getCategoryStr());
        vo.setSort(entity.getSort());
        vo.setEnableDepartmentDetail(entity.getEnableDepartmentDetail());
        return vo;
    }

    public static CustomerDepartmentDetailVo toCustomerDepartmentDetailVo(Department entity) {
        CustomerDepartmentDetailVo vo = new CustomerDepartmentDetailVo();
        vo.setId(entity.getId());
        vo.setName(entity.getName());
        vo.setParentId(entity.getParentId());
        vo.setHospitalId(entity.getHospitalId());
        vo.setHospitalAreaId(entity.getHospitalAreaId());
        vo.setThrdpartDepCode(entity.getThrdpartDepCode());
        vo.setHospitalAreaCode(entity.getHospitalCode());
        vo.setDisplayBgColor(entity.getDisplayBgColor());
        vo.setUri(entity.getUri());
        vo.setFirstLetter(entity.getFirstLetter());
        vo.setShortName(entity.getShortName());
        vo.setIntroduction(entity.getIntroduction());
        vo.setRecommended(entity.getRecommended());
        vo.setSort(entity.getSort());
        vo.setCaution(entity.getCaution());
        vo.setEnableDepartmentDetail(entity.getEnableDepartmentDetail());
        vo.setTriageDeskAddress(entity.getTriageDeskAddress());
        vo.setCategory(entity.getCategoryStr());
        return vo;
    }

    public static CustomerQuickCheckDepartmentVo toCustomerQuickCheckDepartmentVo(Department customerDepartmentVo) {
        CustomerQuickCheckDepartmentVo vo = new CustomerQuickCheckDepartmentVo();
        vo.setId(customerDepartmentVo.getId());
        vo.setName(customerDepartmentVo.getName());
        vo.setParentId(customerDepartmentVo.getParentId());
//        vo.setParentName(customerDepartmentVo.getParentName());
        vo.setHospitalId(customerDepartmentVo.getHospitalId());
        vo.setHospitalAreaId(customerDepartmentVo.getHospitalAreaId());
        vo.setHospitalAreaCode(customerDepartmentVo.getHospitalCode());
        vo.setThrdpartDepCode(customerDepartmentVo.getThrdpartDepCode());
        vo.setDisplayBgColor(customerDepartmentVo.getDisplayBgColor());
        vo.setEnableDepartmentDetail(customerDepartmentVo.getEnableDepartmentDetail());
        vo.setCategory(Lists.newArrayList(Integer.valueOf(customerDepartmentVo.getCategory())));
        vo.setUri(customerDepartmentVo.getUri());
        vo.setFirstLetter(customerDepartmentVo.getFirstLetter());
        vo.setAddressIntro(customerDepartmentVo.getAddressIntro());
        vo.setSort(customerDepartmentVo.getSort());
        vo.setRemark(customerDepartmentVo.getRemark());
        vo.setDisplayFields(customerDepartmentVo.getDisplayFields());
        vo.setDisplayDepartmentName(customerDepartmentVo.getDisplayDepartmentName());
//        vo.setParentDepartmentCode(customerDepartmentVo.getParentDepartmentCode());
        return vo;
    }

    public static CustomerDepartmentTreeVo toCustomerDepartmentTreeVo(Department entity) {
        CustomerDepartmentTreeVo vo = new CustomerDepartmentTreeVo();
        vo.setId(entity.getId());
        vo.setName(entity.getName());
        vo.setParentId(entity.getParentId());
        vo.setHospitalId(entity.getHospitalId());
        vo.setHospitalAreaId(entity.getHospitalAreaId());
        vo.setThrdpartDepCode(entity.getThrdpartDepCode());
        vo.setHospitalAreaCode(entity.getHospitalCode());
        vo.setDisplayBgColor(entity.getDisplayBgColor());
        vo.setUri(entity.getUri());
        vo.setFirstLetter(entity.getFirstLetter());
        vo.setAddressIntro(entity.getAddressIntro());
        vo.setCategory(entity.getCategoryStr());
        vo.setSort(entity.getSort());
        vo.setEnableDepartmentDetail(entity.getEnableDepartmentDetail());
        vo.setChildrens(Collections.emptyList());
        return vo;
    }

    public static void updateDepartment(Department entity, DepartmentUpdateReqDto dto) {
        if (null != dto.getParentId()) {
            entity.setParentId(dto.getParentId());
        }
        if (null != dto.getThrdpartDepCode()) {
            entity.setThrdpartDepCode(dto.getThrdpartDepCode());
        }
        if (null != dto.getName()) {
            entity.setName(dto.getName());
        }
        if (null != dto.getShortName()) {
            entity.setShortName(dto.getShortName());
        }
        if (null != dto.isRecommended()) {
            entity.setRecommended(dto.isRecommended());
        }
        if (null != dto.getCategory()) {
            entity.setCategoryStr(dto.getCategory());
        }
        if (null != dto.getFirstLetter()) {
            entity.setFirstLetter(dto.getFirstLetter());
        }
        if (null != dto.getIntroduction()) {
            entity.setIntroduction(dto.getIntroduction());
        }
        if (null != dto.getAddressIntro()) {
            entity.setAddressIntro(dto.getAddressIntro());
        }
        if (null != dto.getCaution()) {
            entity.setCaution(dto.getCaution());
        }
        if (null != dto.getSort()) {
            entity.setSort(dto.getSort());
        }
        if (null != dto.getSystemDepends()) {
            entity.setSystemDepends(dto.getSystemDepends());
        }
        if (null != dto.isEnabled()) {
            entity.setEnabled(dto.isEnabled());
        }
        if (null != dto.getUri()) {
            entity.setUri(dto.getUri());
        }
        if (null != dto.getDisplayBgColor()) {
            entity.setDisplayBgColor(dto.getDisplayBgColor());
        }
        if (null != dto.getRemark()) {
            entity.setRemark(dto.getRemark());
        }
        if (null != dto.isEnableDepartmentDetail()) {
            entity.setEnableDepartmentDetail(dto.isEnableDepartmentDetail());
        }
        if (null != dto.getForbiddenDay()) {
            entity.setForbiddenDay(dto.getForbiddenDay());
        }
        if (null != dto.getAdvanceDay()) {
            entity.setAdvanceDay(dto.getAdvanceDay());
        }
        if (StringUtils.hasText(dto.getSourceActivateTime())) {
            entity.setSourceActivateTime(dto.getSourceActivateTime());
        }
        if (null != dto.getLevelTag()) {
            entity.setLevelTag(dto.getLevelTag());
        }
        if (!CollectionUtils.isEmpty(dto.getDoctors())) {
            entity.setDoctors(String.join("|", dto.getDoctors()));
        }
        if (null != dto.getTriageDeskAddress()) {
            entity.setTriageDeskAddress(dto.getTriageDeskAddress());
        }
        if (null != dto.getDisplayFields()) {
            entity.setDisplayFields(dto.getDisplayFields());
        }
        if (null != dto.getAppointmentNotifyContact()) {
            entity.setAppointmentNotifyContact(dto.getAppointmentNotifyContact());
        }
        if (null != dto.getNoticeTemplateId()) {
            entity.setNoticeTemplateId(dto.getNoticeTemplateId());
        }

    }
}
