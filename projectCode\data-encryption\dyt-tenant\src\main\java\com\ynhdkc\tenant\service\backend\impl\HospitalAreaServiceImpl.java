package com.ynhdkc.tenant.service.backend.impl;

import backend.common.entity.dto.notification.constant.ServiceConstant;
import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import backend.common.util.JsonUtil;
import backend.common.util.ResponseEntityUtil;
import backend.security.service.BackendTenantUserService;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.client.PaymentCenterClient;
import com.ynhdkc.tenant.client.UserClient;
import com.ynhdkc.tenant.client.model.TenantPaymentFullListVo;
import com.ynhdkc.tenant.client.model.TenantPaymentVo;
import com.ynhdkc.tenant.client.model.UpdateTenantPaymentDto;
import com.ynhdkc.tenant.client.model.UserVo;
import com.ynhdkc.tenant.dao.*;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.entity.constant.HospitalAreaFunctionStatus;
import com.ynhdkc.tenant.entity.constant.HospitalAreaFunctionType;
import com.ynhdkc.tenant.entity.setting.*;
import com.ynhdkc.tenant.enums.DoctorTitleShowType;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.AddressService;
import com.ynhdkc.tenant.service.backend.*;
import com.ynhdkc.tenant.tool.CacheComponent;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.util.Pair;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.ynhdkc.tenant.tool.convert.AppointmentSettingConverter.toAppointmentRuleSetting;
import static com.ynhdkc.tenant.tool.convert.AppointmentSettingConverter.toAppointmentRuleSettingVo;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-27 10:56
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HospitalAreaServiceImpl implements HospitalAreaService {
    private final HospitalAreaQuery hospitalAreaQuery;
    private final HospitalQuery hospitalQuery;
    private final DepartmentQuery departmentQuery;
    private final HospitalAreaRepository hospitalAreaRepository;
    private final HospitalAreaSettingQuery hospitalAreaSettingQuery;
    private final HospitalAreaSettingRepository hospitalAreaSettingRepository;
    private final HospitalAreaFunctionQuery hospitalAreaFunctionQuery;
    private final HospitalAreaFunctionRepository hospitalAreaFunctionRepository;

    private final TenantService tenantService;
    private final HospitalService hospitalService;
    private final AddressService addressService;

    private final BackendTenantUserService backendTenantUserService;
    private final RedisTemplate<String, Object> redisTemplate;

    private final PaymentCenterClient paymentCenterClient;
    private final UserClient userClient;

    private final PageVoConvert pageVoConvert;
    private final CacheComponent cacheComponent;
    private final IHospitalAreaDetailPageConfigService hospitalDetailPageConfigService;
    private final IHospitalAreaDetailPageNavigatorService hospitalDetailPageNavigatorService;
    private final IHospitalAreaDetailPageSubNaviModuleService hospitalDetailPageSubNaviModuleService;
    private final IHospitalAreaDetailPageCubeModuleService hospitalDetailPageCubeModuleService;

    static HospitalAreaDetailVo toHospitalAreaDetailVo(Hospital entity) {
        HospitalAreaDetailVo vo = new HospitalAreaDetailVo();
        vo.id(entity.getId())
                .tenantId(entity.getTenantId())
                .hospitalAreaCode(entity.getHospitalCode())
                .name(entity.getName())
                .hospitalId(entity.getParentId())
                .status(entity.getStatus())
                .display(entity.getDisplay())
                .category(entity.getCategories())
                .displaySort(entity.getDisplaySort())
                .createTime(entity.getCreateTime());
        vo.announcement(entity.getAnnouncement())
                .picture(entity.getPictures())
                .contactPhoneNumber(entity.getContactPhoneNumber())
                .introduction(entity.getIntroduction())
                .environment(entity.getEnvironment())
                .displayGuide(entity.getDisplayGuide())
                .mapKeyword(entity.getMapKeyword())
                .displayFloor(entity.getDisplayFloor())
                .setAppointmentSchedulingTime(entity.getAppointmentSchedulingTime());
        vo.setStopServiceBeginTime(entity.getStopServiceBeginTime());
        vo.setStopServiceEndTime(entity.getStopServiceEndTime());
        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public HospitalAreaDetailVo create(HospitalAreaCreateReqDto request) {
        tenantService.getDetail(request.getTenantId());
        hospitalService.getDetail(request.getHospitalId());
        Hospital hospitalArea = HospitalAreaService.toHospitalArea(request);
        if (null != request.getAddress()) {
            hospitalArea.setAddressId(addressService.create(request.getAddress()));
        }
        hospitalAreaRepository.create(hospitalArea);
        /* 创建医院默认配置 */
        createDefaultSetting(hospitalArea);

        HospitalAreaDetailVo vo = toHospitalAreaDetailVo(hospitalArea);
        importHospitalAreaAddress(hospitalArea.getAddressId(), vo);
        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public HospitalAreaDetailVo update(Long hospitalAreaId, HospitalAreaUpdateReqDto request) {
        Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaById(hospitalAreaId);
        if (null == hospitalArea) {
            throw new BizException(HttpStatus.NOT_FOUND, "院区不存在");
        }
        if (null != request.getAddress()) {
            hospitalArea.setAddressId(addressService.create(request.getAddress()));
        }
        updateIfNotNull(hospitalArea, request);
        hospitalAreaRepository.update(hospitalArea);

        HospitalAreaDetailVo vo = toHospitalAreaDetailVo(hospitalArea);
        importHospitalAreaAddress(hospitalArea.getAddressId(), vo);
        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Pair<BaseOperationResponse, Long> delete(Long hospitalAreaId) {
        Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaById(hospitalAreaId);
        if (null == hospitalArea) {
            throw new BizException(HttpStatus.NOT_FOUND, "院区不存在");
        }
        hospitalAreaRepository.delete(hospitalAreaId);
        return Pair.of(new BaseOperationResponse("删除成功"), hospitalArea.getTenantId());
    }

    @Override
    public HospitalAreaDetailVo getDetail(Long hospitalAreaId) {
        Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaById(hospitalAreaId);
        if (null == hospitalArea) {
            throw new BizException(HttpStatus.NOT_FOUND, "院区不存在");
        }

        /* 校验权限 */
        if (!backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.HospitalAreaId(hospitalAreaId))) {
            throw new BizException(HttpStatus.FORBIDDEN, "用户无访问该院区权限");
        }

        HospitalAreaDetailVo vo = toHospitalAreaDetailVo(hospitalArea);
        importHospitalAreaAddress(hospitalArea.getAddressId(), vo);
        vo.setHospitalAreaSettings(queryHospitalAreaSettings(hospitalAreaId));
        return vo;
    }

    @Override
    public HospitalAreaDetailVo rpcGetDetail(Long hospitalAreaId) {
        Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaById(hospitalAreaId);
        if (null == hospitalArea) {
            return null;
        }

        HospitalAreaDetailVo vo = toHospitalAreaDetailVo(hospitalArea);
        importHospitalAreaAddress(hospitalArea.getAddressId(), vo);
        vo.setHospitalAreaSettings(queryHospitalAreaSettings(hospitalAreaId));
        return vo;
    }

    @Override
    public HospitalAreaPageVo query(HospitalAreaQueryReqDto request) {
        /* 未指定租户ID，需要判断是否为管理员 */
        boolean isSuperAdmin = backendTenantUserService.isSuperAdmin();
        if (null == request.getTenantId() && !isSuperAdmin) {
            throw new BizException(HttpStatus.FORBIDDEN, "必须指定租户ID");
        }

        HospitalAreaQuery.HospitalAreaQueryOption option = new HospitalAreaQuery.HospitalAreaQueryOption(request.getCurrentPage(), request.getPageSize())
                .setHospitalAreaCode(request.getHospitalAreaCode())
                .setTenantId(request.getTenantId())
                .setHospitalId(request.getHospitalId())
                .setHospitalAreaId(request.getHospitalAreaId())
                .setName(request.getHospitalAreaName())
                .setCategory(request.getCategory())
                .setExcludeCategory(request.getExcludeCategory())
                .setStatus(request.getStatus())
                .setStartCreateTime(request.getStartCreateTime())
                .setEndCreateTime(request.getEndCreateTime());
        if (null != request.getPaymentStatus()) {
            option.setIncludeActivePaymentIds(getHospitalPaymentByStatus(request.getPaymentStatus()));
        }

        /* 根据医院名称模糊搜索 */
        if (StringUtils.hasText(request.getHospitalName())) {
            HospitalQuery.HospitalQueryOption hospitalQueryOption = new HospitalQuery.HospitalQueryOption(1, Integer.MAX_VALUE)
                    .setCategory(request.getCategory())
                    .setExcludeCategory(request.getExcludeCategory())
                    .setName(request.getHospitalName());
            if (!isSuperAdmin) {
                hospitalQueryOption.setIncludeIds(backendTenantUserService.getCurrentUserHospitalReadRange(true));
            }
            Set<Long> hospitalIdSet;
            try (Page<Hospital> hospitalPage = hospitalQuery.pageQueryHospital(hospitalQueryOption)) {
                hospitalIdSet = hospitalPage.stream()
                        .map(Hospital::getId)
                        .collect(Collectors.toSet());
            }
            if (hospitalIdSet.isEmpty()) {
                HospitalAreaPageVo hospitalAreaPageVo = new HospitalAreaPageVo();
                hospitalAreaPageVo.setList(Collections.emptyList());
                hospitalAreaPageVo.setCurrentPage(request.getCurrentPage());
                hospitalAreaPageVo.setPageSize(request.getPageSize());
                return hospitalAreaPageVo;
            }
            option.setIncludeHospitalIds(hospitalIdSet);
        }

        List<AppointmentRuleSetting> appointmentRuleSettings = hospitalAreaSettingQuery.All();
        /* 非超管约束查询范围 */
        List<Long> hospitalAreaIds = new ArrayList<>(backendTenantUserService.getCurrentUserHospitalReadRange(true));
        if (!isSuperAdmin) {
            if (!CollectionUtils.isEmpty(appointmentRuleSettings)) {
                List<Long> filteredHospitalAreaIds = appointmentRuleSettings.stream()
                        .filter(appointmentRuleSetting -> appointmentRuleSetting.getSystemDepends().equals(request.getSystemDepends()) && hospitalAreaIds.contains(appointmentRuleSetting.getHospitalAreaId()))
                        .map(AppointmentRuleSetting::getHospitalAreaId)
                        .collect(Collectors.toList());
                option.setIncludeIds(filteredHospitalAreaIds);
            }
        } else {
            if (!CollectionUtils.isEmpty(appointmentRuleSettings) && (null != request.getSystemDepends())) {
                List<Long> filteredHospitalAreaIds = appointmentRuleSettings.stream()
                        .filter(appointmentRuleSetting -> appointmentRuleSetting.getSystemDepends().equals(request.getSystemDepends()))
                        .map(AppointmentRuleSetting::getHospitalAreaId)
                        .collect(Collectors.toList());
                option.setIncludeIds(filteredHospitalAreaIds);
            }
        }

        try (Page<Hospital> hospitalAreaPage = hospitalAreaQuery.pageQueryHospitalAreaWithCategory(option)) {
            return pageVoConvert.toPageVo(hospitalAreaPage, HospitalAreaPageVo.class, entity -> {
                HospitalAreaVo vo = HospitalAreaService.toHospitalAreaVo(entity);
                this.importHospitalAreaAddress(entity.getAddressId(), vo);
                this.importHospitalInfo(vo);
                hospitalAreaSettingQuery.queryAppointmentRuleSettingById(entity.getTenantId(), entity.getParentId(), entity.getId())
                        .ifPresent(appointmentRuleSetting -> vo.setAppointmentSetting(toAppointmentRuleSettingVo(appointmentRuleSetting)));
                return vo;
            });
        }

    }

    @Override
    public HospitalAreaDepartmentsInfoPageVo queryHospitalAreaDepartmentInfo(HospitalAreaDepartmentsInfoQueryReqDto request) {
        HospitalAreaQuery.HospitalAreaQueryOption option = new HospitalAreaQuery.HospitalAreaQueryOption(request.getCurrentPage(), request.getPageSize())
                .setHospitalAreaCode(request.getHospitalAreaCode())
                .setTenantId(request.getTenantId())
                .setHospitalId(request.getHospitalId())
                .setDepartmentLayer(request.getDepartmentLayer())
                .setName(request.getHospitalAreaName())
                .setCategory(request.getCategory())
                .setExcludeCategory(request.getExcludeCategory());

        Set<Long> hospitalAreaIds = new HashSet<>();
        boolean superAdmin = backendTenantUserService.isSuperAdmin();
        if (!superAdmin) {
            hospitalAreaIds = backendTenantUserService.getCurrentUserHospitalAreaReadRange(true);
        }
        if (null != request.getHospitalAreaId()) {
            if (!superAdmin && !hospitalAreaIds.contains(request.getHospitalAreaId())) {
                return new HospitalAreaDepartmentsInfoPageVo();
            }
            hospitalAreaIds = Collections.singleton(request.getHospitalAreaId());
        }
        option.setIncludeIds(hospitalAreaIds);

        Set<Long> hospitalIdSet;
        try (Page<Hospital> hospitals = hospitalQuery.pageQueryHospital(new HospitalQuery.HospitalQueryOption(1, Integer.MAX_VALUE)
                .setName(request.getHospitalName()))) {
            hospitalIdSet = hospitals
                    .stream()
                    .map(Hospital::getId)
                    .collect(Collectors.toSet());
        }
        if (hospitalIdSet.isEmpty()) {
            HospitalAreaDepartmentsInfoPageVo pageVo = new HospitalAreaDepartmentsInfoPageVo();
            pageVo._list(Collections.emptyList());
            pageVo.currentPage(request.getCurrentPage());
            pageVo.pageSize(request.getPageSize());
            pageVo.setTotalSize(0L);
            return pageVo;
        }
        option.setIncludeHospitalIds(hospitalIdSet);

        try (Page<Hospital> hospitalAreaPage = hospitalAreaQuery.pageQueryHospitalAreaWithCategory(option)) {
            List<HospitalAreaDepartmentInfoVo> infoVos = hospitalAreaPage.stream().map(hospitalArea -> {
                Hospital hospital = hospitalQuery.queryHospitalById(hospitalArea.getParentId());
                HospitalAreaDepartmentInfoVo infoVo = new HospitalAreaDepartmentInfoVo();
                infoVo.setTenantId(hospitalArea.getTenantId());
                infoVo.setHospitalCode(hospital.getHospitalCode());
                infoVo.setHospitalId(hospital.getId());
                infoVo.setHospitalName(hospital.getName());
                infoVo.setHospitalAreaId(hospitalArea.getId());
                infoVo.setHospitalAreaCode(hospitalArea.getHospitalCode());
                infoVo.setHospitalAreaName(hospitalArea.getName());
                infoVo.setDisplayLayer(hospitalArea.getDepartmentLayer());
                infoVo.setStatus(hospitalArea.getStatus());
                infoVo.setTotalCount(departmentQuery.countByHospitalAreaId(hospitalArea.getId()));
                hospitalAreaSettingQuery.queryAppointmentRuleSettingById(hospitalArea.getTenantId(), hospitalArea.getParentId(), hospitalArea.getId())
                        .ifPresent(appointmentRuleSetting -> infoVo.setSystemDepends(appointmentRuleSetting.getSystemDepends()));
                return infoVo;
            }).collect(Collectors.toList());

            HospitalAreaDepartmentsInfoPageVo pageVo = new HospitalAreaDepartmentsInfoPageVo();
            pageVo._list(infoVos);
            pageVo.currentPage(hospitalAreaPage.getPageNum());
            pageVo.pageSize(hospitalAreaPage.getPageSize());
            pageVo.setTotalSize(hospitalAreaPage.getTotal());
            return pageVo;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public HospitalAreaSettingDetailVo createSetting(Long hospitalAreaId, HospitalAreaSettingCreateReqDto request) {
        Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaById(hospitalAreaId);
        if (null == hospitalArea) {
            throw new BizException(HttpStatus.NOT_FOUND, "院区不存在");
        }
        CustomBusinessSettingDto customBusinessSetting = request.getCustomBusinessSetting();
        Optional<CustomBusinessSetting> customBusinessSettingOptional = hospitalAreaSettingQuery.queryCustomBusinessSettingByName(
                request.getTenantId(),
                request.getHospitalId(),
                hospitalAreaId,
                customBusinessSetting.getName());
        if (customBusinessSettingOptional.isPresent()) {
            throw new BizException(HttpStatus.BAD_REQUEST, "自定义业务名称不能重复");
        }

        /* 创建自定义业务规则 */
        CustomBusinessSetting customBusinessSettingNew = HospitalAreaService.toCustomBusinessSetting(customBusinessSetting);
        customBusinessSettingNew.setTenantId(request.getTenantId());
        customBusinessSettingNew.setHospitalId(request.getHospitalId());
        customBusinessSettingNew.setHospitalAreaId(hospitalAreaId);
        hospitalAreaSettingRepository.createCustomBusinessSetting(customBusinessSettingNew);

        /* 录入功能清单 */
        try (Page<FunctionSetting> functionPage = hospitalAreaFunctionQuery.pageQuery(new HospitalAreaFunctionQuery.HospitalAreaFunctionQueryOption(1, Integer.MAX_VALUE)
                .setTenantId(request.getTenantId())
                .setHospitalId(request.getHospitalId())
                .setHospitalAreaId(hospitalAreaId)
                .setName(customBusinessSetting.getName()))) {
            FunctionSetting functionSetting = HospitalAreaDefaultSettingTool.createFunctionSetting(
                    hospitalArea,
                    HospitalAreaFunctionType.custom,
                    customBusinessSetting.getName(),
                    null);
            if (!functionPage.isEmpty()) {
                throw new BizException(HttpStatus.BAD_REQUEST, "已存在同名功能或与系统功能名称重复");
            }
            hospitalAreaFunctionRepository.create(functionSetting);
            customBusinessSettingNew.setFunctionId(functionSetting.getId());
            hospitalAreaSettingRepository.updateCustomBusinessSetting(customBusinessSettingNew);
        }

        return new HospitalAreaSettingDetailVo().addCustomBusinessSettingsItem(HospitalAreaService.toCustomBusinessSettingVo(customBusinessSettingNew));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public HospitalAreaSettingDetailVo updateSetting(Long hospitalAreaId, HospitalAreaSettingUpdateReqDto request, HospitalAreaFunctionType type) {
        Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaById(hospitalAreaId);
        if (null == hospitalArea) {
            throw new BizException(HttpStatus.NOT_FOUND, "院区不存在");
        }
        HospitalAreaSettingDetailVo vo;
        switch (type) {
            case appointment_registration:
                vo = updateAppointmentRuleSetting(hospitalArea, request);
                break;
            case diagnosis_payment:
                vo = updateDiagnosisPaymentSetting(hospitalArea, request);
                break;
            case hospitalization:
                vo = updateHospitalizationSetting(hospitalArea, request);
                break;
            case patient_report:
                vo = updatePatientReportSetting(hospitalArea, request);
                break;
            case patient_card:
                vo = updatePatientCardSetting(hospitalArea, request);
                break;
            case custom:
                vo = updateCustomBusinessSetting(hospitalArea, request);
                break;
            default:
                throw new BizException(HttpStatus.BAD_REQUEST, "不支持的业务类型");
        }
        vo.setHospitalId(hospitalArea.getParentId());
        return vo;
    }

    @Override
    public HospitalAreaSettingDetailVo getSettingDetail(Long tenantId, Long hospitalId, Long hospitalAreaId) {
        Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaById(hospitalAreaId);
        if (null == hospitalArea) {
            throw new BizException(HttpStatus.NOT_FOUND, "院区不存在");
        }

        Optional<DiagnosisPaymentSetting> diagnosisPaymentSettingOptional = hospitalAreaSettingQuery.queryDiagnosisPaymentSettingById(tenantId, hospitalId, hospitalAreaId);
        Optional<AppointmentRuleSetting> appointmentRuleSettingOptional = hospitalAreaSettingQuery.queryAppointmentRuleSettingById(tenantId, hospitalId, hospitalAreaId);
        Optional<PatientReportSetting> patientReportSettingOptional = hospitalAreaSettingQuery.queryPatientReportSettingById(tenantId, hospitalId, hospitalAreaId);
        Optional<PatientCardSetting> patientCardSettingOptional = hospitalAreaSettingQuery.queryPatientCardSettingById(tenantId, hospitalId, hospitalAreaId);
        Optional<HospitalizationSetting> hospitalizationSettingOptional = hospitalAreaSettingQuery.queryHospitalizationSettingById(tenantId, hospitalId, hospitalAreaId);
        List<CustomBusinessSetting> customBusinessSettings = hospitalAreaSettingQuery.queryCustomBusinessSettingById(tenantId, hospitalId, hospitalAreaId);

        HospitalAreaSettingDetailVo vo = new HospitalAreaSettingDetailVo();
        vo.setHospitalAreaId(hospitalAreaId);
        vo.setHospitalAreaCode(hospitalArea.getHospitalCode());
        diagnosisPaymentSettingOptional.ifPresent(setting -> vo.diagnosisPaymentSetting(HospitalAreaService.toDiagnosisPaymentSettingVo(setting)));
        appointmentRuleSettingOptional.ifPresent(ruleSetting -> vo.appointmentRuleSetting(toAppointmentRuleSettingVo(ruleSetting)));
        patientReportSettingOptional.ifPresent(setting -> vo.patientReportSetting(HospitalAreaService.toPatientReportSettingVo(setting)));
        patientCardSettingOptional.ifPresent(cardSetting -> vo.patientCardSetting(HospitalAreaService.toPatientCardSettingVo(cardSetting)));
        hospitalizationSettingOptional.ifPresent(setting -> vo.hospitalizationSetting(HospitalAreaService.toHospitalizationSettingVo(setting)));
        if (!CollectionUtils.isEmpty(customBusinessSettings)) {
            vo.customBusinessSettings(customBusinessSettings.stream().map(HospitalAreaService::toCustomBusinessSettingVo).collect(Collectors.toList()));
        }
        return vo;
    }

    @Override
    public HospitalAreaFunctionDetailVo getFunctionDetail(Long hospitalAreaId) {
        HospitalAreaFunctionQuery.HospitalAreaFunctionQueryOption option = new HospitalAreaFunctionQuery.HospitalAreaFunctionQueryOption(1, Integer.MAX_VALUE)
                .setHospitalAreaId(hospitalAreaId);
        if (!backendTenantUserService.isSuperAdmin()) {
            option.setIncludeHospitalAreaIds(backendTenantUserService.getCurrentUserHospitalAreaReadRange(true));
        }
        try (Page<FunctionSetting> functionSettingPage = hospitalAreaFunctionQuery.pageQuery(option)) {
            HospitalAreaFunctionDetailVo vo = new HospitalAreaFunctionDetailVo();
            vo.setFunctionList(new ArrayList<>());
            functionSettingPage.forEach(function -> vo.addFunctionListItem(HospitalAreaService.toHospitalAreaFunctionItem(function)));
            return vo;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public HospitalAreaFunctionDetailVo updateFunction(Long hospitalAreaId, HospitalAreaFunctionUpdateReqDto request) {
        Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaById(hospitalAreaId);
        if (null == hospitalArea) {
            throw new BizException(HttpStatus.NOT_FOUND, "院区不存在");
        }
        FunctionSetting hospitalFunctionSetting = hospitalAreaFunctionQuery.queryFunctionById(hospitalAreaId, request.getId());
        if (null == hospitalFunctionSetting) {
            throw new BizException(HttpStatus.NOT_FOUND, "功能不存在");
        }

        HospitalAreaDefaultSettingTool.updateFunctionSetting(hospitalFunctionSetting, request);
        hospitalAreaFunctionRepository.update(hospitalFunctionSetting);

        HospitalAreaFunctionDetailVo vo = new HospitalAreaFunctionDetailVo();
        vo.setFunctionList(new ArrayList<>());
        vo.addFunctionListItem(HospitalAreaService.toHospitalAreaFunctionItem(hospitalFunctionSetting));
        return vo;
    }

    @Override
    public HospitalAreaLayoutVo getLayout(Long hospitalAreaId) {
        Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaById(hospitalAreaId);
        if (null == hospitalArea) {
            throw new BizException(HttpStatus.NOT_FOUND, "院区不存在");
        }

        HospitalAreaLayoutVo vo = new HospitalAreaLayoutVo();
        vo.setMapKeyword(vo.getMapKeyword());
        vo.setIntroduction(hospitalArea.getIntroduction());
        vo.setHospitalAreaId(hospitalAreaId);
        vo.setHospitalAreaCode(hospitalArea.getHospitalCode());
        setHospitalName(hospitalArea.getParentId(), vo);
        vo.setHospitalAreaName(hospitalArea.getName());
        vo.setHospitalAreaImage(hospitalArea.getPictures());
        setHospitalAreas(hospitalArea.getParentId(), vo);
        setHospitalAreaAddress(hospitalArea, vo);
        vo.setAppointmentScheduleTime(hospitalArea.getAppointmentSchedulingTime());

        Optional<AppointmentRuleSetting> appointmentRuleSettingOptional = hospitalAreaSettingQuery.queryAppointmentRuleSettingById(hospitalArea.getTenantId(), hospitalArea.getParentId(), hospitalAreaId);
        appointmentRuleSettingOptional.ifPresent(appointmentRuleSetting -> vo.setAppointmentRule(appointmentRuleSetting.getAppointmentRule()));

        vo.setHospitalAreaPhone(hospitalArea.getContactPhoneNumber());
        vo.setHospitalAreaLevel(hospitalArea.getLevelDictValue());
        vo.setAnnouncement(hospitalArea.getAnnouncement());
        hospitalDetailPageConfigService.setHospitalAreaDetailPageConfig(vo, hospitalAreaId);
        hospitalDetailPageNavigatorService.setPageNavigator(vo, hospitalAreaId);
        hospitalDetailPageSubNaviModuleService.setSubNaviModule(vo, hospitalAreaId);
        hospitalDetailPageCubeModuleService.setCubeModule(vo, hospitalAreaId);
        return vo;
    }

    private void setHospitalName(Long parentId, HospitalAreaLayoutVo vo) {
        Hospital hospital = hospitalQuery.queryHospitalById(parentId);
        if (null != hospital) {
            vo.setHospitalName(hospital.getName());
        }
    }

    private void setHospitalAreas(Long parentId, HospitalAreaLayoutVo vo) {
        List<Hospital> hospitalAreas = hospitalAreaQuery.queryHospitalAreaByParentId(parentId);
        if (!CollectionUtils.isEmpty(hospitalAreas)) {
            List<HospitalAreaLayoutVoHospitalAreas> hospitalAreaLayoutVoHospitalAreas = hospitalAreas.stream().map(hospital -> {
                HospitalAreaLayoutVoHospitalAreas hospitalAreaLayoutVoHospitalArea = new HospitalAreaLayoutVoHospitalAreas();
                hospitalAreaLayoutVoHospitalArea.setHospitalAreaId(hospital.getId());
                hospitalAreaLayoutVoHospitalArea.setHospitalAreaName(hospital.getName());
                return hospitalAreaLayoutVoHospitalArea;
            }).collect(Collectors.toList());
            vo.setHospitalAreas(hospitalAreaLayoutVoHospitalAreas);
        }
    }

    private void setHospitalAreaAddress(Hospital hospitalArea, HospitalAreaLayoutVo vo) {
        AddressVo hospitalAreaAddress = addressService.getAddressVoById(hospitalArea.getAddressId());
        if (null != hospitalAreaAddress) {
            vo.setHospitalAreaAddress(hospitalAreaAddress);
        }
    }

    @Override
    public Boolean isHospitalAreaDependOnHis(Long hospitalAreaId) {
        AppointmentRuleSetting appointmentRuleSetting = hospitalAreaSettingQuery.queryAppointmentRuleSettingBy(hospitalAreaId).orElse(null);
        if (appointmentRuleSetting == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "院区预约挂号配置不存在");
        }
        return appointmentRuleSetting.getSystemDepends() == 0;
    }

    @Override
    public List<HospitalAreaVo> rpcBatchGetDetail(RpcBatchGetByIdReqDto request) {
        try (Page<Hospital> hospitalPage = hospitalAreaQuery.pageQueryHospitalArea(new HospitalAreaQuery.HospitalAreaQueryOption(1, request.getIds().size())
                .setIncludeIds(request.getIds()))) {
            return hospitalPage.stream().map(HospitalAreaService::toHospitalAreaVo).collect(Collectors.toList());
        }
    }

    @Override
    public HospitalAreaVo rpcGetDetailByCode(String code) {
        Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaBy(code);
        if (null == hospitalArea) {
            throw new BizException(HttpStatus.NOT_FOUND, "院区不存在");
        }

        HospitalAreaDetailVo vo = toHospitalAreaDetailVo(hospitalArea);
        importHospitalAreaAddress(hospitalArea.getAddressId(), vo);
        vo.setHospitalAreaSettings(queryHospitalAreaSettings(hospitalArea.getId()));
        return vo;
    }

    @Override
    public Hospital getById(Long hospitalAreaId) {
        return hospitalAreaQuery.queryHospitalAreaById(hospitalAreaId);
    }

    @Override
    public void batchSetStopGenerateSchedule(HospitalAreaBatchSetStopGenerateScheduleReqDto request) {
        if (CollectionUtils.isEmpty(request.getHospitalCodes())) {
            return;
        }

        if (request.getStopStartDate().after(request.getStopEndDate())) {
            throw new BizException(HttpStatus.BAD_REQUEST, "停止生成排班开始时间不能大于停止生成排班结束时间");
        }

        hospitalAreaSettingQuery.queryAppointmentRuleSettingByHospitalCodes(request.getHospitalCodes())
                .forEach(appointmentRuleSetting -> {
                    appointmentRuleSetting.setStopGenerateScheduleBeginDate(request.getStopStartDate());
                    appointmentRuleSetting.setStopGenerateScheduleEndDate(request.getStopEndDate());
                    hospitalAreaSettingRepository.updateAppointmentRuleSetting(appointmentRuleSetting);
                });
    }

    @Override
    public RpcStopScheduleDataMap rpcStopScheduleDataMap() {
        RpcStopScheduleDataMap rpcStopScheduleDataMap = new RpcStopScheduleDataMap();
        HospitalAreaQuery.HospitalAreaQueryOption hospitalAreaQueryOption = new HospitalAreaQuery.HospitalAreaQueryOption(1, Integer.MAX_VALUE)
                .setStatus(0);
        Map<Long, Hospital> hospitalAreaMap = hospitalAreaQuery.queryHospitalArea(hospitalAreaQueryOption)
                .stream()
                .collect(Collectors.toMap(Hospital::getId, v -> v));
        hospitalAreaSettingQuery.All()
                .forEach(appointmentRuleSetting -> {
                    if (!hospitalAreaMap.containsKey(appointmentRuleSetting.getHospitalAreaId())) {
                        return;
                    }
                    RpcStopScheduleDataMapMap rpcStopScheduleDataMapMap = new RpcStopScheduleDataMapMap();
                    rpcStopScheduleDataMapMap.setStartDate(appointmentRuleSetting.getStopGenerateScheduleBeginDate());
                    rpcStopScheduleDataMapMap.setEndDate(appointmentRuleSetting.getStopGenerateScheduleEndDate());
                    rpcStopScheduleDataMap.putMapItem(hospitalAreaMap.get(appointmentRuleSetting.getHospitalAreaId()).getHospitalCode(), rpcStopScheduleDataMapMap);
                });
        return rpcStopScheduleDataMap;
    }

    @Override
    public AppointNotifyConfigVo getAppointNotifyConfig(String hospitalCode) {
        Object recipientObj = redisTemplate.opsForHash().get(ServiceConstant.HOSPITAL_ADMIN_REDIS_KEY, hospitalCode);
        if (recipientObj != null) {
            List<HospitalAdminRecipientVo> list = JSONUtil.toList(recipientObj.toString(), HospitalAdminRecipientVo.class);
            return new AppointNotifyConfigVo().adminList(list);
        }
        return null;
    }

    @Override
    public SetAppointNotifyConfigResponse setAppointNotifyConfig(String hospitalCode, SetAppointNotifyConfigReqDto reqDto) {
        List<String> adminPhoneNumbers = reqDto.getAdminPhoneNumbers();
        Map<String, String> setResultMap = new HashMap<>();
        List<UserVo> userVos = adminPhoneNumbers.stream().map(phoneNumber -> {
            UserVo body = userClient.queryUserByPhoneNumber(phoneNumber).getBody();
            if (body != null) {
                setResultMap.put(phoneNumber, "设置成功");
            } else {
                setResultMap.put(phoneNumber, "未找到该用户");
            }
            return body;
        }).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(userVos)) {
            throw new BizException(HttpStatus.NOT_FOUND, "未找到该手机号码的用户");
        }
        List<HospitalAdminRecipientVo> recipientVos = userVos.stream().map(user -> {
                    HospitalAdminRecipientVo recipient = new HospitalAdminRecipientVo();
                    recipient.setUserId(user.getId());
                    recipient.setPhoneNumber(user.getPhoneNumber());
                    recipient.setWxMpOpenId(user.getWeChatProfile().getOpenId());

                    return recipient;
                })
                .collect(Collectors.toList());
        redisTemplate.opsForHash().put(ServiceConstant.HOSPITAL_ADMIN_REDIS_KEY, hospitalCode, JSONUtil.toJsonStr(recipientVos));
        return new SetAppointNotifyConfigResponse().resultMap(setResultMap);
    }

    @Override
    public Boolean deleteDepartmentScheduleCache(Long hospitalAreaId) {
        List<Department> departments = departmentQuery.queryDepartmentsByHospitalAreaId(hospitalAreaId);
        if (!CollectionUtils.isEmpty(departments)) {
            departments.forEach(department -> {
                String key = cacheComponent.getDoctorScheduleKey(department);
                redisTemplate.delete(key);
            });
        }
        return true;
    }

    @Override
    public List<HospitalDependOnHisResponse> rpcGetHospitalDependOnHis(Long id, String hospitalName) {
        Optional<List<HospitalDependOnHisResponse>> appointmentRuleSettingOptional = hospitalAreaSettingQuery.queryDependOnHIs(id, hospitalName);
        return appointmentRuleSettingOptional.orElse(null);
    }


    private HospitalAreaSettingDetailVo updateAppointmentRuleSetting(Hospital hospitalArea, HospitalAreaSettingUpdateReqDto request) {
        AppointmentRuleSetting newSetting = toAppointmentRuleSetting(request.getAppointmentRuleSetting());
        Optional<AppointmentRuleSetting> appointmentRuleSettingOptional = hospitalAreaSettingQuery.queryAppointmentRuleSettingById(hospitalArea.getTenantId(), hospitalArea.getParentId(), hospitalArea.getId());

        if (appointmentRuleSettingOptional.isPresent()) {
            newSetting.setId(appointmentRuleSettingOptional.get().getId());
            hospitalAreaSettingRepository.updateAppointmentRuleSetting(newSetting);
        } else {
            newSetting.setTenantId(hospitalArea.getTenantId());
            newSetting.setHospitalId(hospitalArea.getParentId());
            newSetting.setHospitalAreaId(hospitalArea.getId());
            hospitalAreaSettingRepository.createAppointmentRuleSetting(newSetting);
        }

        return new HospitalAreaSettingDetailVo().appointmentRuleSetting(toAppointmentRuleSettingVo(newSetting));
    }

    private HospitalAreaSettingDetailVo updateDiagnosisPaymentSetting(Hospital hospitalArea, HospitalAreaSettingUpdateReqDto request) {
        DiagnosisPaymentSetting newSetting = HospitalAreaService.toDiagnosisPaymentSetting(request.getDiagnosisPaymentSetting());
        Optional<DiagnosisPaymentSetting> diagnosisPaymentSettingOptional = hospitalAreaSettingQuery.queryDiagnosisPaymentSettingById(hospitalArea.getTenantId(), hospitalArea.getParentId(), hospitalArea.getId());
        if (diagnosisPaymentSettingOptional.isPresent()) {
            newSetting.setId(diagnosisPaymentSettingOptional.get().getId());
            hospitalAreaSettingRepository.updateDiagnosisPaymentSetting(newSetting);
        } else {
            newSetting.setTenantId(hospitalArea.getTenantId());
            newSetting.setHospitalId(hospitalArea.getParentId());
            newSetting.setHospitalAreaId(hospitalArea.getId());
            hospitalAreaSettingRepository.createDiagnosisPaymentSetting(newSetting);
        }
        return new HospitalAreaSettingDetailVo()
                .diagnosisPaymentSetting(HospitalAreaService.toDiagnosisPaymentSettingVo(newSetting));
    }

    private HospitalAreaSettingDetailVo updateHospitalizationSetting(Hospital hospitalArea, HospitalAreaSettingUpdateReqDto request) {
        HospitalizationSetting newSetting = HospitalAreaService.toHospitalizationSetting(request.getHospitalizationSetting());
        Optional<HospitalizationSetting> hospitalizationSettingOptional = hospitalAreaSettingQuery.queryHospitalizationSettingById(hospitalArea.getTenantId(), hospitalArea.getParentId(), hospitalArea.getId());
        if (hospitalizationSettingOptional.isPresent()) {
            newSetting.setId(hospitalizationSettingOptional.get().getId());
            hospitalAreaSettingRepository.updateHospitalizationSetting(newSetting);
        } else {
            newSetting.setTenantId(hospitalArea.getTenantId());
            newSetting.setHospitalId(hospitalArea.getParentId());
            newSetting.setHospitalAreaId(hospitalArea.getId());
            hospitalAreaSettingRepository.createHospitalizationSetting(newSetting);
        }

        return new HospitalAreaSettingDetailVo().hospitalizationSetting(HospitalAreaService.toHospitalizationSettingVo(newSetting));
    }

    private HospitalAreaSettingDetailVo updatePatientReportSetting(Hospital hospitalArea, HospitalAreaSettingUpdateReqDto request) {
        PatientReportSetting newSetting = HospitalAreaService.toPatientReportSetting(request.getPatientReportSetting());
        Optional<PatientReportSetting> patientReportSettingOptional = hospitalAreaSettingQuery.queryPatientReportSettingById(hospitalArea.getTenantId(), hospitalArea.getParentId(), hospitalArea.getId());
        if (patientReportSettingOptional.isPresent()) {
            newSetting.setId(patientReportSettingOptional.get().getId());
            hospitalAreaSettingRepository.updatePatientReportSetting(newSetting);
        } else {
            newSetting.setTenantId(hospitalArea.getTenantId());
            newSetting.setHospitalId(hospitalArea.getParentId());
            newSetting.setHospitalAreaId(hospitalArea.getId());
            hospitalAreaSettingRepository.createPatientReportSetting(newSetting);
        }

        return new HospitalAreaSettingDetailVo()
                .patientReportSetting(HospitalAreaService.toPatientReportSettingVo(newSetting));
    }

    private HospitalAreaSettingDetailVo updatePatientCardSetting(Hospital hospitalArea, HospitalAreaSettingUpdateReqDto request) {
        PatientCardSetting newSetting = HospitalAreaService.toPatientCardSetting(request.getPatientCardSetting());
        Optional<PatientCardSetting> patientCardSettingOptional = hospitalAreaSettingQuery.queryPatientCardSettingById(hospitalArea.getTenantId(), hospitalArea.getParentId(), hospitalArea.getId());
        /* 一般不会出现，会生成默认规则 */
        if (patientCardSettingOptional.isPresent()) {
            newSetting.setId(patientCardSettingOptional.get().getId());
            hospitalAreaSettingRepository.updatePatientCardSetting(newSetting);
        } else {
            newSetting.setTenantId(hospitalArea.getTenantId());
            newSetting.setHospitalId(hospitalArea.getParentId());
            newSetting.setHospitalAreaId(hospitalArea.getId());
            hospitalAreaSettingRepository.createPatientCardSetting(newSetting);
        }
        return new HospitalAreaSettingDetailVo()
                .patientCardSetting(HospitalAreaService.toPatientCardSettingVo(newSetting));
    }

    private HospitalAreaSettingDetailVo updateCustomBusinessSetting(Hospital hospitalArea, HospitalAreaSettingUpdateReqDto request) {
        Optional<CustomBusinessSetting> customBusinessSettingOptional = hospitalAreaSettingQuery.queryCustomBusinessSettingById(hospitalArea.getTenantId(),
                hospitalArea.getParentId(),
                hospitalArea.getId(),
                request.getSettingId());
        if (!customBusinessSettingOptional.isPresent()) {
            throw new BizException(HttpStatus.NOT_FOUND, "未找到对应的自定义业务设置");
        }

        CustomBusinessSetting newSetting = HospitalAreaService.toCustomBusinessSetting(request.getCustomBusinessSetting());
        newSetting.setId(customBusinessSettingOptional.get().getId());
        hospitalAreaSettingRepository.updateCustomBusinessSetting(newSetting);
        return new HospitalAreaSettingDetailVo().addCustomBusinessSettingsItem(HospitalAreaService.toCustomBusinessSettingVo(newSetting));
    }

    private Set<Long> getHospitalPaymentByStatus(Integer paymentStatus) {
        ResponseEntity<TenantPaymentFullListVo> payments = paymentCenterClient.getAllTenantPayments();
        TenantPaymentFullListVo vos = ResponseEntityUtil.assertAndGetBody(payments, "远程调用异常");
        Set<Long> ids;
        switch (paymentStatus) {
            case 1:
                ids = vos.getList().stream().filter(UpdateTenantPaymentDto::isEnabled).map(TenantPaymentVo::getHospitalId).collect(Collectors.toSet());
                break;
            case 2:
                ids = vos.getList().stream().filter(tenantPaymentVo -> !tenantPaymentVo.isEnabled()).map(TenantPaymentVo::getHospitalId).collect(Collectors.toSet());
                break;
            default:
                ids = vos.getList().stream().map(TenantPaymentVo::getHospitalId).collect(Collectors.toSet());
                break;
        }
        /* 没有目标医院，让返回结果为空 */
        if (ids.isEmpty()) {
            ids.add(-1L);
        }
        return ids;
    }

    private void importHospitalInfo(HospitalAreaVo vo) {
        Hospital hospital = hospitalQuery.queryHospitalById(vo.getHospitalId());
        vo.setHospitalName(hospital.getName());
    }

    private HospitalAreaSettingDetailVo queryHospitalAreaSettings(Long hospitalAreaId) {
        HospitalAreaSettingDetailVo hospitalAreaSettingDetailVo = new HospitalAreaSettingDetailVo();

        List<FunctionSetting> functionSettings = hospitalAreaFunctionRepository.queryByHospitalAreaId(hospitalAreaId);
        if (functionSettings.isEmpty()) {
            return hospitalAreaSettingDetailVo;
        }

        functionSettings.forEach(functionSetting -> {
            switch (functionSetting.getType()) {
                case "预约挂号":
                    hospitalAreaSettingQuery.queryAppointmentRuleSettingById(null, null, hospitalAreaId)
                            .ifPresent(q -> hospitalAreaSettingDetailVo.setAppointmentRuleSetting(toAppointmentRuleSettingVo(q)));
                    break;
                case "门诊缴费":
                    hospitalAreaSettingQuery.queryDiagnosisPaymentSettingById(null, null, hospitalAreaId)
                            .ifPresent(q -> hospitalAreaSettingDetailVo.setDiagnosisPaymentSetting(HospitalAreaService.toDiagnosisPaymentSettingVo(q)));
                    break;
                case "报告查询":
                    hospitalAreaSettingQuery.queryPatientReportSettingById(null, null, hospitalAreaId)
                            .ifPresent(q -> hospitalAreaSettingDetailVo.setPatientReportSetting(HospitalAreaService.toPatientReportSettingVo(q)));
                    break;
                case "住院":
                    hospitalAreaSettingQuery.queryHospitalizationSettingById(null, null, hospitalAreaId)
                            .ifPresent(q -> hospitalAreaSettingDetailVo.setHospitalizationSetting(HospitalAreaService.toHospitalizationSettingVo(q)));
                    break;
                case "就诊卡":
                    hospitalAreaSettingQuery.queryPatientCardSettingById(null, null, hospitalAreaId)
                            .ifPresent(q -> hospitalAreaSettingDetailVo.setPatientCardSetting(HospitalAreaService.toPatientCardSettingVo(q)));
                    break;
                default:
                    break;
            }
        });

        List<CustomBusinessSetting> customBusinessSettings = hospitalAreaSettingQuery.queryCustomBusinessSettingById(null, null, hospitalAreaId);
        if (!CollectionUtils.isEmpty(customBusinessSettings)) {
            hospitalAreaSettingDetailVo.setCustomBusinessSettings(customBusinessSettings.stream().map(HospitalAreaService::toCustomBusinessSettingVo).collect(Collectors.toList()));
        }
        return hospitalAreaSettingDetailVo;
    }

    private void updateIfNotNull(Hospital hospitalArea, HospitalAreaUpdateReqDto dto) {
        if (null != dto.getHospitalAreaCode()) {
            hospitalArea.setHospitalCode(dto.getHospitalAreaCode());
        }
        if (null != dto.getName()) {
            hospitalArea.setName(dto.getName());
        }
        if (null != dto.getPicture()) {
            hospitalArea.setPictures(dto.getPicture());
        }
        if (null != dto.getCategory()) {
            hospitalArea.setCategories(dto.getCategory());
        }
        if (null != dto.getContactPhoneNumber()) {
            hospitalArea.setContactPhoneNumber(dto.getContactPhoneNumber());
        }
        if (null != dto.getDisplaySort()) {
            hospitalArea.setDisplaySort(dto.getDisplaySort());
        }
        if (null != dto.getAnnouncement()) {
            hospitalArea.setAnnouncement(dto.getAnnouncement());
        }
        if (null != dto.getIntroduction()) {
            hospitalArea.setIntroduction(dto.getIntroduction());
        }
        if (null != dto.getEnvironment()) {
            hospitalArea.setEnvironment(dto.getEnvironment());
        }
        if (null != dto.isDisplay()) {
            hospitalArea.setDisplay(dto.isDisplay());
        }
        if (null != dto.isDisplayGuide()) {
            hospitalArea.setDisplayGuide(dto.isDisplayGuide());
        }
        if (null != dto.getMapKeyword()) {
            hospitalArea.setMapKeyword(dto.getMapKeyword());
        }
        if (null != dto.isDisplayFloor()) {
            hospitalArea.setDisplayFloor(dto.isDisplayFloor());
        }
        if (null != dto.getStopServiceBeginTime()) {
            hospitalArea.setStopServiceBeginTime(dto.getStopServiceBeginTime());
        }
        if (null != dto.getStopServiceEndTime()) {
            hospitalArea.setStopServiceEndTime(dto.getStopServiceEndTime());
        }
        if (null != dto.getStatus()) {
            hospitalArea.setStatus(dto.getStatus());
        }
        if (null != dto.getAppointmentSchedulingTime()) {
            hospitalArea.setAppointmentSchedulingTime(dto.getAppointmentSchedulingTime());
        }
        if (!CollectionUtils.isEmpty(dto.getTagDictLabel())) {
            hospitalArea.setTagDictLabels(dto.getTagDictLabel());
        }
    }

    /**
     * 创建院区默认配置
     *
     * @param hospitalArea 院区实体
     */
    private void createDefaultSetting(Hospital hospitalArea) {
        HospitalizationSetting hospitalizationSetting = HospitalAreaDefaultSettingTool.createDefaultHospitalizationSetting(hospitalArea);
        AppointmentRuleSetting appointmentRuleSetting = HospitalAreaDefaultSettingTool.createDefaultAppointmentRuleSetting(hospitalArea);
        DiagnosisPaymentSetting diagnosisPaymentSetting = HospitalAreaDefaultSettingTool.createDefaultDiagnosisPaymentSetting(hospitalArea);
        PatientReportSetting patientReportSetting = HospitalAreaDefaultSettingTool.createDefaultPatientReportSetting(hospitalArea);
        PatientCardSetting patientCardSetting = HospitalAreaDefaultSettingTool.createDefaultPatientCardSetting(hospitalArea);

        Set<HospitalAreaFunctionType> skipTypes = new HashSet<>();

        hospitalAreaSettingRepository.createHospitalizationSetting(hospitalizationSetting);
        FunctionSetting hospitalizationFunction = HospitalAreaDefaultSettingTool.createDefaultFunctionSetting(hospitalArea, HospitalAreaFunctionType.hospitalization);
        hospitalAreaFunctionRepository.create(hospitalizationFunction);
        skipTypes.add(HospitalAreaFunctionType.hospitalization);
        hospitalizationSetting.setFunctionId(hospitalizationFunction.getId());
        hospitalAreaSettingRepository.updateHospitalizationSetting(hospitalizationSetting);

        hospitalAreaSettingRepository.createAppointmentRuleSetting(appointmentRuleSetting);
        FunctionSetting appointmentFunction = HospitalAreaDefaultSettingTool.createDefaultFunctionSetting(hospitalArea, HospitalAreaFunctionType.appointment_registration);
        hospitalAreaFunctionRepository.create(appointmentFunction);
        skipTypes.add(HospitalAreaFunctionType.appointment_registration);
        appointmentRuleSetting.setFunctionId(appointmentFunction.getId());
        hospitalAreaSettingRepository.updateAppointmentRuleSetting(appointmentRuleSetting);

        hospitalAreaSettingRepository.createDiagnosisPaymentSetting(diagnosisPaymentSetting);
        FunctionSetting diagnosisFunction = HospitalAreaDefaultSettingTool.createDefaultFunctionSetting(hospitalArea, HospitalAreaFunctionType.diagnosis_payment);
        hospitalAreaFunctionRepository.create(diagnosisFunction);
        skipTypes.add(HospitalAreaFunctionType.diagnosis_payment);
        diagnosisPaymentSetting.setFunctionId(diagnosisFunction.getId());
        hospitalAreaSettingRepository.updateDiagnosisPaymentSetting(diagnosisPaymentSetting);

        hospitalAreaSettingRepository.createPatientReportSetting(patientReportSetting);
        FunctionSetting patientReportFunction = HospitalAreaDefaultSettingTool.createDefaultFunctionSetting(hospitalArea, HospitalAreaFunctionType.patient_report);
        hospitalAreaFunctionRepository.create(patientReportFunction);
        skipTypes.add(HospitalAreaFunctionType.patient_report);
        patientReportSetting.setFunctionId(patientReportFunction.getId());
        hospitalAreaSettingRepository.updatePatientReportSetting(patientReportSetting);

        hospitalAreaSettingRepository.createPatientCardSetting(patientCardSetting);
        FunctionSetting patientCardFunction = HospitalAreaDefaultSettingTool.createDefaultFunctionSetting(hospitalArea, HospitalAreaFunctionType.patient_card);
        hospitalAreaFunctionRepository.create(patientCardFunction);
        skipTypes.add(HospitalAreaFunctionType.patient_card);
        patientCardSetting.setFunctionId(patientCardFunction.getId());
        hospitalAreaSettingRepository.updatePatientCardSetting(patientCardSetting);

        skipTypes.add(HospitalAreaFunctionType.custom);
        Arrays.stream(HospitalAreaFunctionType.values()).filter(type -> !skipTypes.contains(type)).forEach(type -> {
            FunctionSetting functionSetting = HospitalAreaDefaultSettingTool.createDefaultFunctionSetting(hospitalArea, type);
            hospitalAreaFunctionRepository.create(functionSetting);
        });
    }

    private void importHospitalAreaAddress(Long addressId, HospitalAreaVo vo) {
        if(null == addressId) {
            return;
        }
        vo.setAddress(addressService.getAddressVoById(addressId));
    }
}

class HospitalAreaDefaultSettingTool {
    private HospitalAreaDefaultSettingTool() {
    }

    protected static FunctionSetting createDefaultFunctionSetting(Hospital hospitalArea, HospitalAreaFunctionType type) {
        return createFunctionSetting(hospitalArea, type, type.getDesc(), 0);
    }

    protected static FunctionSetting createFunctionSetting(Hospital hospitalArea, HospitalAreaFunctionType type, String name, Integer sort) {
        if (type != HospitalAreaFunctionType.custom) {
            name = type.getDesc();
        }
        return new FunctionSetting()
                .setTenantId(hospitalArea.getTenantId())
                .setHospitalId(hospitalArea.getParentId())
                .setHospitalAreaId(hospitalArea.getId())
                .setName(name)
                .setType(type.name())
                .setLogo(null)
                .setTabDisplayStyle(null)
                .setSort(sort)
                .setStatus(HospitalAreaFunctionStatus.CLOSE.getCode());
    }

    protected static void updateFunctionSetting(FunctionSetting functionSetting, HospitalAreaFunctionUpdateReqDto dto) {
        if (null != dto.getLogo()) {
            functionSetting.setLogo(dto.getLogo());
        }
        if (null != dto.getTabDisplayStyle()) {
            functionSetting.setTabDisplayStyle(dto.getTabDisplayStyle());
        }
        if (null != dto.getStatus()) {
            functionSetting.setStatus(dto.getStatus());
        }
    }

    protected static AppointmentRuleSetting createDefaultAppointmentRuleSetting(Hospital hospitalArea) {
        AppointmentRuleSetting setting = new AppointmentRuleSetting();
        setting.setTenantId(hospitalArea.getTenantId());
        setting.setHospitalId(hospitalArea.getParentId());
        setting.setHospitalCode(hospitalArea.getHospitalCode());
        setting.setHospitalAreaId(hospitalArea.getId());
        setting.setDepartmentLevel(1);
        setting.setSystemDepends(0);
        setting.setAppointToday(false);
        setting.setDisplayNo(false);
        setting.setStopAppointTime("16:00");
        setting.setAdvanceDay(-1);
        setting.setDisplayTime("06:00");
        setting.setPaymentCloseDuration(30L);
        setting.setRefundToday(false);
        setting.setStopRefundTime("23:59");
        setting.setConfirmMedicalInsuranceCard(false);
        setting.setOrderNeedVerify(false);
        setting.setSelectedPayments(JsonUtil.serializeObject(Collections.emptyList()));
        setting.setDisplayDoctorUnderDepartment(1);
        setting.setDoctorTitleShowType(DoctorTitleShowType.FROM_HIS.getValue());
        return setting;
    }

    protected static PatientCardSetting createDefaultPatientCardSetting(Hospital hospitalArea) {
        PatientCardSetting setting = new PatientCardSetting();
        setting.setTenantId(hospitalArea.getTenantId());
        setting.setHospitalId(hospitalArea.getParentId());
        setting.setHospitalAreaId(hospitalArea.getId());
        setting.setNeedPatientCard(false);
        setting.setBindType(0);
        setting.setSupportPatientType(0);
        setting.setNeedElectronCard(false);
        return setting;
    }

    protected static DiagnosisPaymentSetting createDefaultDiagnosisPaymentSetting(Hospital hospitalArea) {
        DiagnosisPaymentSetting setting = new DiagnosisPaymentSetting();
        setting.setTenantId(hospitalArea.getTenantId());
        setting.setHospitalId(hospitalArea.getParentId());
        setting.setHospitalAreaId(hospitalArea.getId());
        setting.setSupportMergerPayment(false);
        setting.setSupportOnlineRefund(false);
        setting.setRefundToday(false);
        setting.setStopRefundTime("23:59");
        setting.setSupportInvoice(false);
        setting.setSelectedPayments(JsonUtil.serializeObject(Collections.emptyList()));
        return setting;
    }

    protected static PatientReportSetting createDefaultPatientReportSetting(Hospital hospitalArea) {
        PatientReportSetting setting = new PatientReportSetting();
        setting.setTenantId(hospitalArea.getTenantId());
        setting.setHospitalId(hospitalArea.getParentId());
        setting.setHospitalAreaId(hospitalArea.getId());
        setting.setSupportReportType(0);
        setting.setSupportSearchDateRange(0);
        setting.setSupportSearchTime(JsonUtil.serializeObject(Collections.emptyList()));
        return setting;
    }

    protected static HospitalizationSetting createDefaultHospitalizationSetting(Hospital hospitalArea) {
        HospitalizationSetting setting = new HospitalizationSetting();
        setting.setTenantId(hospitalArea.getTenantId());
        setting.setHospitalId(hospitalArea.getParentId());
        setting.setHospitalAreaId(hospitalArea.getId());
        setting.setEnablePayment(false);
        setting.setSupportOnlineRefund(false);
        setting.setEnableInfoQuery(false);
        setting.setSelectedPayments(JsonUtil.serializeObject(Collections.emptyList()));
        return setting;
    }

}
