package com.ynhdkc.tenant.entity.constant;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-02-08 11:53
 */
public enum HospitalCategory {
    /**
     * 常规
     */
    NORMAL(0),
    /**
     * 疫苗预约
     */
    VACCINE(1),
    /**
     * 特需门诊
     */
    SPECIAL(2),
    /**
     * 多学科
     */
    MDT(3),
    /**
     * 中医
     */
    TCM(4),
    /**
     * 其他
     */
    OTHER(9);

    HospitalCategory(int value) {
        this.value = value;
    }

    private final int value;

    public final int val() {
        return this.value;
    }
}
