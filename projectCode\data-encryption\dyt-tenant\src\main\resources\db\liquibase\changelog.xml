<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">
    <changeSet id="1" author="Quasar">
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-04-20_t_address.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-04-20_t_building_floor.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-04-20_t_china_region.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-04-20_t_dict_label.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-04-20_t_dict_type.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-04-20_t_doctor.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-04-20_t_floor_area.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-04-20_t_tenant.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-04-21_t_hospital.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-04-21_t_hospital_building.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-04-21_t_hospital_department.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-04-21_t_tenant_user.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-04-22_t_hospital_patient_card_setting.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-04-23_t_hospital_appointment_rule_setting.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-04-23_t_hospital_custom_business_setting.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-04-23_t_hospital_diagnosis_payment_setting.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-04-23_t_hospital_function_setting.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-04-23_t_hospital_hospitalization_setting.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-04-23_t_hospital_patient_report_setting.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-04-25_t_tenant_user_structure.sql"
                 relativeToChangelogFile="true"/>
    </changeSet>
    <changeSet id="2" author="dzh">
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-05-04_t_hospital_list_display.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-05-04_t_hospital_list_group.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-05-04_t_hospital_list_group_relation.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-05-04_t_hospital_list_index.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-05-04_t_hospital_list_rule.sql"
                 relativeToChangelogFile="true"/>
    </changeSet>
    <changeSet id="3" author="Zhang_Xiang">
        <sqlFile stripComments="true" path="./sql/2023-05-23_t_hospital_detail_page_config.sql"
                 relativeToChangelogFile="true"/>
    </changeSet>
    <changeSet id="4" author="Zhang_Xiang">
        <sqlFile stripComments="true" path="./sql/2023-05-23_t_hospital_detail_page_sub_navi_module.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile stripComments="true" path="./sql/2023-05-23_t_hospital_detail_page_navigator.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile stripComments="true" path="./sql/2023-05-23_t_hospital_detail_page_cube_module.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile stripComments="true" path="./sql/2023-05-23_t_hospital_detail_page_cube.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile stripComments="true" path="./sql/2023-05-23_t_hospital_detail_page_tab.sql"
                 relativeToChangelogFile="true"/>
    </changeSet>
    <changeSet id="5" author="Zhang_Xiang">
        <sql dbms="mysql">
            alter table `t_doctor`
                modify introduction text null comment '简介';
            alter table t_doctor
                add appointment_rule text null comment '预约规则，如果没有配置字典，则从此字段中读取预约规则' after appointment_rule_dict_label;
        </sql>
    </changeSet>
    <changeSet id="6" author="Zhang_Xiang">
        <sql dbms="mysql">
            alter table t_doctor
                modify speciality text null comment '擅长';
        </sql>
    </changeSet>
    <changeSet id="7" author="Zhang_Xiang">
        <sql dbms="mysql">
            alter table t_doctor
                modify statement text null comment '简介';
        </sql>
    </changeSet>
    <changeSet id="8" author="Zhang_Xiang">
        <sql dbms="mysql">
            alter table t_doctor
                add gender int default 0 comment '0:未知，1:男，2:女' after statement;
        </sql>
    </changeSet>
    <changeSet id="9" author="Zhang_Xiang">
        <sql dbms="mysql">
            alter table t_doctor
                add department_code varchar(300) null comment '科室编码' after department_id;

            alter table t_doctor
                modify head_img_url text null comment '头像 URL 或者 Base64 以后的医生头像';
        </sql>
    </changeSet>
    <changeSet id="10" author="Zhang_Xiang">
        <sql dbms="mysql">
            create index t_code_index
                on t_doctor (hospital_code, department_code, thrdpart_doctor_code);
        </sql>
    </changeSet>
    <changeSet id="11" author="Zhang_Xiang">
        <sql dbms="mysql">
            alter table t_hospital_appointment_rule_setting
                add display_doctor_under_department int default 0 not null comment '科室下医生列表：0，显示科室下所有医生；1，显示有排班的医生；' after selected_payments;
        </sql>
    </changeSet>
    <changeSet id="12" author="Zhang_Xiang">
        <sql dbms="mysql">
            alter table t_hospital_appointment_rule_setting
                add need_payment tinyint(1) default 0 not null comment '预约挂号需要支付，0:不需要；1:需要' after display_doctor_under_department;
        </sql>
    </changeSet>
    <changeSet id="13" author="ZhangXiang">
        <sql dbms="mysql">
            alter table t_hospital_appointment_rule_setting
                add support_locking_appointment_no tinyint(1) default 0 not null comment '是否支持锁号,false:不支持,true:支持' after need_payment;
        </sql>
    </changeSet>
    <changeSet id="14" author="Zhang_Xiang">
        <sql dbms="mysql">
            alter table t_hospital_detail_page_config
                collate = utf8mb4_unicode_ci;
            alter table t_hospital_detail_page_cube
                collate = utf8mb4_unicode_ci;
            alter table t_hospital_detail_page_cube_module
                collate = utf8mb4_unicode_ci;
            alter table t_hospital_detail_page_navigator
                collate = utf8mb4_unicode_ci;
            alter table t_hospital_detail_page_sub_navi_module
                collate = utf8mb4_unicode_ci;
            alter table t_hospital_detail_page_tab
                collate = utf8mb4_unicode_ci;
        </sql>
    </changeSet>
    <changeSet id="15" author="Zhang_Xiang">
        <sql dbms="mysql">
            alter table t_hospital_detail_page_config
                modify hospital_code varchar (20) null comment '医院编码';
            alter table t_hospital_detail_page_cube
                modify hospital_code varchar (20) null comment '医院编码';
            alter table t_hospital_detail_page_cube_module
                modify hospital_code varchar (20) null comment '医院编码';
            alter table t_hospital_detail_page_navigator
                modify hospital_code varchar (20) null comment '医院编码';
            alter table t_hospital_detail_page_sub_navi_module
                modify hospital_code varchar (20) null comment '医院编码';
            alter table t_hospital_detail_page_tab
                modify hospital_code varchar (20) null comment '医院编码';
            alter table t_hospital_diagnosis_payment_setting
                modify hospital_code varchar (20) null comment '医院编码';
            alter table t_hospital_function_setting
                modify hospital_code varchar (20) null comment '医院编码';
            alter table t_hospital_hospitalization_setting
                modify hospital_code varchar (20) null comment '医院编码';
            alter table t_hospital_patient_card_setting
                modify hospital_code varchar (20) null comment '医院编码';
            alter table t_hospital_patient_report_setting
                modify hospital_code varchar (20) null comment '医院编码';
        </sql>
    </changeSet>
    <changeSet id="16" author="Zhang_Xiang">
        <sql dbms="mysql">
            alter table t_hospital_appointment_rule_setting
                add need_partition_time tinyint(1) default 0 not null comment '是否需要分时段,0:不需要,1:需要' after support_locking_appointment_no;
        </sql>
    </changeSet>
    <changeSet id="17" author="Zhang_Xiang">
        <sql dbms="mysql">
            alter table t_hospital_appointment_rule_setting
                add need_sign_in tinyint(1) default 0 not null comment '是否需要签到,0:不需要，1:需要' after need_partition_time;
        </sql>
    </changeSet>
    <changeSet id="18" author="dzh">
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-08-03_t_vaccine_category.sql"
                 relativeToChangelogFile="true"/>
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-08-03_t_vaccine.sql"
                 relativeToChangelogFile="true"/>
    </changeSet>
    <changeSet id="19" author="ZhangXiang">
        <sql dbms="mysql">
            alter table t_doctor
                add honor text null comment '荣誉' after rank_dict_label;
        </sql>
    </changeSet>
    <changeSet id="20" author="dzh">
        <sql dbms="mysql">
            alter table `t_doctor`
                add `category` varchar(100) default '0' not null comment '医生类别：0，预约挂号；1，疫苗；' after `statement`;
        </sql>
    </changeSet>
    <changeSet id="21" author="Zhang_Xiang">
        <sql dbms="mysql">
            alter table t_hospital_department
                add source int default 0 not null comment '科室来源，0:默认，1:HIS' after remark;
        </sql>
    </changeSet>

    <changeSet id="22" author="Zhang_Xiang">
        <sql dbms="mysql">
            alter table t_dict_label
                add iconUrl varchar(200) null comment '图标地址' after sort;
        </sql>
    </changeSet>

    <changeSet id="23" author="Zhang_Xiang">
        <sql dbms="mysql">
            alter table t_dict_label
                change iconUrl icon_url varchar (200) null comment '图标地址';
        </sql>
    </changeSet>

    <changeSet id="24" author="Zhang_Xiang">
        <sql dbms="mysql">
            alter table t_dict_label
                add tag text null comment '标签' after icon_url;

            alter table t_dict_label
                add redirect_path varchar(200) null comment '重定向地址' after tag;
        </sql>
    </changeSet>

    <changeSet id="25" author="dzh">
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-08-15_t_recommend_config.sql"
                 relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="26" author="Quasar">
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-09-05_t_doctor_group_relation.sql"
                 relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="27" author="Quasar">
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-09-12_t_dict_file.sql"
                 relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="28" author="Quasar">
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-09-14_t_hospital_area_position.sql"
                 relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="29" author="Zhang_Xiang">
        <sql dbms="mysql">
            alter table t_hospital_appointment_rule_setting
                add update_department_depend_his tinyint(1) default 1 not null comment '依赖 HIS 的预约挂号服务，不更新科室，true:更新，false：不更新，默认更新' after need_sign_in;
        </sql>
    </changeSet>

    <changeSet id="30" author="dzh">
        <sql dbms="mysql">
            alter table t_recommend_config
                add img_url varchar(200) null comment '图片地址' after `display_tags`;
        </sql>
    </changeSet>

    <changeSet id="31" author="Quasar">
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-11-01_t_recommend_statistics.sql"
                 relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="32" author="dzh">
        <sql dbms="mysql">
            alter table t_hospital_list_group_relation
                add `platform` varchar(200) default 0 comment '平台 0-微信，1-支付宝' after `weight`;
        </sql>
    </changeSet>

    <changeSet id="33" author="dzh">
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-12-07_create_t_channel_source.sql"
                 relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="33" author="james">
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-12-20_add_doctors.sql"
                 relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="34" author="james">
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-12-20_b_add_department_doctor.sql"
                 relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="35" author="james">
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2023-12-22_add_department.sql"
                 relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="36" author="gxz">
        <sql dbms="mysql">
            alter table t_hospital_appointment_rule_setting
                add department_order_type int not null comment '科室排序类型：0，倒序；1，正序；' after department_level;
        </sql>
    </changeSet>

    <changeSet id="37" author="Zhang_Xiang">
        <sql dbms="mysql">
            alter table t_hospital_appointment_rule_setting
                add stop_generate_schedule_begin_date date null comment '停止生成排班开始日期' after all_department_background_color;
            alter table t_hospital_appointment_rule_setting
                add stop_generate_schedule_end_date date null comment '停止生成排班结束日期' after stop_generate_schedule_begin_date;
        </sql>
    </changeSet>

    <changeSet id="38" author="gxz">
        <sql dbms="mysql">
            alter table t_hospital_appointment_rule_setting
                alter column department_order_type set default 0;
        </sql>
    </changeSet>

    <changeSet id="39" author="dzh">
        <sql dbms="mysql">
            alter table t_hospital_appointment_rule_setting
                add time_period_display_type int default 1 not null comment 'C端排班时间段展示方式，0-不显示 1-显示示全部 2-仅显示示有排班的时间' after department_order_type;
        </sql>
    </changeSet>

    <changeSet id="40" author="gxz">
        <sql dbms="mysql">
            ALTER TABLE t_doctor
                MODIFY COLUMN forbidden_day INT DEFAULT -1;

            ALTER TABLE t_doctor
                MODIFY COLUMN advance_day INT DEFAULT -1;
        </sql>
    </changeSet>

    <changeSet id="41" author="Zhang_Xiang">
        <sql dbms="mysql">
            alter table t_doctor
                add display_department_name nvarchar(100) null comment '科室展示名称';
        </sql>
    </changeSet>

    <changeSet id="42" author="gxz">
        <sql dbms="mysql">
            alter table t_hospital_department
                MODIFY column forbidden_day INT DEFAULT -1;

            alter table t_hospital_department
                MODIFY column advance_day INT DEFAULT -1;
        </sql>
    </changeSet>

    <changeSet id="43" author="james">
        <sqlFile dbms="mysql" stripComments="true" path="./sql/20240122_add_shortening.sql"
                 relativeToChangelogFile="true"/>
    </changeSet>
    <changeSet id="44" author="airvip">
        <sql dbms="mysql">
            ALTER TABLE `t_hospital_appointment_rule_setting`
                MODIFY COLUMN `appointment_result_notice` varchar (1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '预约结果提示' AFTER `appointment_notify_contact`;
        </sql>
    </changeSet>
    <changeSet id="45" author="gxz">
        <sql dbms="mysql">
            ALTER TABLE `t_doctor`
                ADD INDEX `idx_hospital_code`(`hospital_code`) USING BTREE COMMENT 'idx_hospital_code索引';
            ALTER TABLE `t_doctor`
                ADD INDEX `idx_sort`(`sort`) USING BTREE COMMENT 'idx_sort索引';
            ALTER TABLE `t_doctor`
                ADD INDEX `idx_department_id`(`department_id`) USING BTREE COMMENT 'idx_department_id索引';
            ALTER TABLE `t_doctor`
                ADD INDEX `idx_hospital_area_id_department_id`(`hospital_area_id`,`department_id`) USING BTREE COMMENT 'idx_hospital_area_id_department_id索引';
            ALTER TABLE `t_doctor`
                ADD INDEX `idx_hospital_code_department_code_thrdpart_doctor_code`(`hospital_code`,`department_code`,`thrdpart_doctor_code`) USING BTREE COMMENT 'idx_hospital_code_department_code_thrdpart_doctor_code索引';
            ALTER TABLE `t_doctor`
                ADD INDEX `idx_department_code_hospital_code_status`(`department_code`,`hospital_code`,`status`) USING BTREE COMMENT 'idx_department_code_hospital_code_status索引';
            ALTER TABLE `t_doctor`
                ADD INDEX `idx_department_id_status`(`department_id`,`status`) USING BTREE COMMENT 'idx_department_id_status索引';
            ALTER TABLE `t_doctor`
                ADD INDEX `idx_hospital_code_department_code`(`hospital_code`,`department_code`) USING BTREE COMMENT 'idx_hospital_code_department_code索引';
            ALTER TABLE `t_doctor`
                ADD INDEX `idx_hospital_code_thrdpart_doctor_code`(`hospital_code`,`thrdpart_doctor_code`) USING BTREE COMMENT 'idx_hospital_code_thrdpart_doctor_code索引';
            ALTER TABLE `t_doctor`
                ADD INDEX `idx_name`(`name`) USING BTREE COMMENT 'idx_name索引';

            ALTER TABLE `t_doctor_group_relation`
            DROP INDEX `uidx_doctor_id_doctor_doctor_group_source_group_id`;
            ALTER TABLE `t_doctor_group_relation`
                ADD INDEX `idx_doctor_id_name_group_source_group_id_group_name`(`doctor_id`, `doctor_name`, `doctor_group_source`, `doctor_group_id`, `doctor_group_name`) USING BTREE COMMENT 'idx_doctor_id_name_group_source_group_id_group_name索引';
            ALTER TABLE `t_doctor_group_relation`
                ADD INDEX `idx_doctor_id`(`doctor_id`) USING BTREE COMMENT 'idx_doctor_id索引';

            ALTER TABLE `t_hospital`
                ADD INDEX `idx_hospital_type_tag`(`hospital_type_tag`) USING BTREE COMMENT 'idx_hospital_type_tag索引';

            ALTER TABLE `t_hospital_department`
                ADD INDEX `idx_hospital_area_id`(`hospital_area_id`) USING BTREE COMMENT 'idx_hospital_area_id索引';
            ALTER TABLE `t_hospital_department`
                ADD INDEX `idx_hospital_area_id_thrdpart_dep_code`(`hospital_area_id`,`thrdpart_dep_code`) USING BTREE COMMENT 'idx_hospital_area_id_thrdpart_dep_code索引';
            ALTER TABLE `t_hospital_department`
                ADD INDEX `idx_hospital_id`(`hospital_id`) USING BTREE COMMENT 'idx_hospital_id索引';
            ALTER TABLE `t_hospital_department`
                ADD INDEX `idx_hospital_code_thrdpart_dep_code`(`hospital_code`,`thrdpart_dep_code`) USING BTREE COMMENT 'idx_hospital_code_thrdpart_dep_code索引';
            ALTER TABLE `t_hospital_department`
                ADD INDEX `idx_first_letter_level_tag_thrdpart_dep_code`(`first_letter`,`level_tag`,`thrdpart_dep_code`) USING BTREE COMMENT 'idx_first_letter_level_tag_thrdpart_dep_code索引';
            ALTER TABLE `t_hospital_department`
                ADD INDEX `idx_first_letter_hospital_code`(`first_letter`,`hospital_code`) USING BTREE COMMENT 'idx_first_letter_hospital_code索引';
            ALTER TABLE `t_hospital_department`
                ADD INDEX `idx_hospital_code`(`hospital_code`) USING BTREE COMMENT 'idx_hospital_code索引';
            ALTER TABLE `t_hospital_department`
                ADD INDEX `idx_sort`(`sort`) USING BTREE COMMENT 'idx_sort索引';
            ALTER TABLE `t_hospital_department`
                ADD INDEX `idx_name`(`name`) USING BTREE COMMENT 'idx_name索引';
            ALTER TABLE `t_hospital_department`
                ADD INDEX `idx_short_name`(`short_name`) USING BTREE COMMENT 'idx_short_name索引';
            ALTER TABLE `t_hospital_department`
                ADD INDEX `idx_first_letter`(`first_letter`) USING BTREE COMMENT 'idx_first_letter索引';
            ALTER TABLE `t_hospital_department`
                ADD INDEX `idx_category`(`category`) USING BTREE COMMENT 'idx_category索引';
        </sql>
    </changeSet>
    <changeSet id="46" author="gxz">
        <sql dbms="mysql">
            ALTER TABLE `t_hospital_department`
                ADD INDEX `idx_tenant_id_hospital_id_hospital_area_id_name`(`tenant_id`,`hospital_id`,`hospital_area_id`,`name`) USING BTREE COMMENT 'idx_tenant_id_hospital_id_hospital_area_id_name索引';
        </sql>
    </changeSet>
    <changeSet id="47" author="hwc">
        <sql dbms="mysql">
            ALTER TABLE `t_hospital_appointment_rule_setting`
                ADD COLUMN `current_system_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '当前使用系統类型: 1,老系统;2,新系统;3,新老系统共用;' AFTER `system_depends`;
        </sql>
    </changeSet>
    <changeSet id="48" author="hwc">
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2024-03-04_t_bulletin_config.sql"
                 relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="49" author="hwc">
        <sql dbms="mysql">
            ALTER TABLE `t_bulletin_config`
                ADD COLUMN `white_list` varchar(1000) NULL COMMENT '白名单列表' AFTER `status`;
        </sql>
    </changeSet>
    <changeSet id="50" author="hwc">
        <sql dbms="mysql">
            ALTER TABLE `t_hospital_appointment_rule_setting`
                ADD COLUMN `doctor_schedule_cache_exp_time` bigint NOT NULL DEFAULT 10 COMMENT '医生排班缓存过期时间（单位:秒/s）' AFTER `stop_refund_time`;
        </sql>
    </changeSet>
    <changeSet id="51" author="xiehaibo">
        <sql dbms="mysql">
            alter table t_hospital_appointment_rule_setting
                add doctor_title_show_type int default 1 not null comment '医生职称展示类型。1-平台职称，2-医院HIS职称，3-荣誉' after department_order_type;
        </sql>
    </changeSet>
    <changeSet id="52" author="xiehaibo">
        <sql dbms="mysql">
            alter table t_doctor
                add registration_level varchar(30) comment 'his医生职称' after rank_dict_label;
        </sql>
    </changeSet>
    <changeSet id="53" author="xiehaibo">
        <sql dbms="mysql">
            update t_hospital_appointment_rule_setting
            set doctor_title_show_type = (case when system_depends = 0 then 2 else 3 end)
        </sql>
    </changeSet>
    <changeSet id="54" author="gxz">
        <sql dbms="mysql">
            ALTER TABLE `t_hospital_appointment_rule_setting`
                ADD COLUMN `source_activate_time` varchar(8) NULL DEFAULT '00:00:00' COMMENT '号源激活时间' AFTER `advance_day`;
            ALTER TABLE `t_hospital_department`
                ADD COLUMN `source_activate_time` varchar(8) NULL DEFAULT '00:00:00' COMMENT '号源激活时间' AFTER `advance_day`;
            ALTER TABLE `t_doctor`
                ADD COLUMN `source_activate_time` varchar(8) NULL DEFAULT '00:00:00' COMMENT '号源激活时间' AFTER `advance_day`;
        </sql>
    </changeSet>

    <changeSet id="55" author="gxz">
        <sql dbms="mysql">
            ALTER TABLE `t_hospital_appointment_rule_setting`
                ADD COLUMN `appointment_reminder` text NULL COMMENT '预约提醒' AFTER `appointment_guide`;
        </sql>
    </changeSet>
    <changeSet id="56" author="hwc">
        <sql dbms="mysql">
            ALTER TABLE `t_hospital_area_position`
                ADD COLUMN `triage_desk_address` varchar(255) NULL COMMENT '分诊台地址' AFTER `area_name`;
        </sql>
    </changeSet>

    <changeSet id="57" author="ZhangXiang">
        <sqlFile dbms="mysql" stripComments="true" path="./sql/20240629_ti_jian_promoter_statistics.sql"
                 relativeToChangelogFile="true"/>
    </changeSet>

    <changeSet id="58" author="ZhangXiang">
        <sql dbms="mysql">
            drop table if exists t_promoter;
            drop table if exists t_promoter_performance;
            drop table if exists t_promoter_performance_statistics;
        </sql>
    </changeSet>

    <changeSet id="59" author="ZhangXiang">
        <sql dbms="mysql">
            CREATE TABLE t_promoter_url_generator
            (
                id             bigint(20) NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
                promoter_name  varchar(50)  NOT NULL COMMENT '推广员名称',
                promoter_code  varchar(50)  NOT NULL COMMENT '推广员编码',
                target_url     varchar(500) NOT NULL COMMENT '目标链接',
                promoter_url   varchar(500) NOT NULL COMMENT '推广链接',
                promoter_scene varchar(50)  NOT NULL COMMENT '推广场景',
                create_time    datetime     NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP,
                update_time    datetime     NOT NULL COMMENT '更新时间' ON UPDATE CURRENT_TIMESTAMP,
                unique uk_promoter_code_target_url_promoter_scene (promoter_code, target_url, promoter_scene)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='推广员链接生成器';
        </sql>
    </changeSet>
    <changeSet id="60" author="hwc">
        <sql dbms="mysql">
            CREATE TABLE `t_tenant_user_password_change_log`
            (
                `id`             bigint                                                       NOT NULL AUTO_INCREMENT COMMENT '变更记录唯一标识',
                `tenant_user_id` bigint                                                       NOT NULL COMMENT '用户ID',
                `old_password`   varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '旧密码',
                `create_time`    datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3),
                `update_time`    datetime(3) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP (3),
                PRIMARY KEY (`id`) USING BTREE,
                INDEX            `idx_user_id` (`tenant_user_id`) USING BTREE
            ) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户用户密码变更记录表' ROW_FORMAT=Dynamic;
            INSERT INTO `t_tenant_user_password_change_log` (`tenant_user_id`, `old_password`, `create_time`)
            SELECT `id`, `password`, `create_time`
            FROM `t_tenant_user`;
        </sql>
    </changeSet>
    <changeSet id="61" author="hwc">
        <sql dbms="mysql">
            CREATE TABLE `t_desensitization_white_list`
            (
                `id`               bigint NOT NULL AUTO_INCREMENT COMMENT '全局唯一标识',
                `tenant_id`        bigint NOT NULL COMMENT '租户 ID',
                `tenant_user_id`   bigint NOT NULL COMMENT '租户用户 ID',
                `tenant_name`      VARCHAR(255) DEFAULT NULL COMMENT '租户名称',
                `tenant_user_name` VARCHAR(255) DEFAULT NULL COMMENT '租户用户名称',
                `create_time`      datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3),
                `update_time`      datetime(3) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP (3),
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '数据脱敏白名单' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>
    <changeSet id="62" author="wzy">
        <sql dbms="mysql">
            ALTER TABLE `t_hospital_appointment_rule_setting`
                ADD COLUMN `hospital_depend_his_enabled_department` int default 0 not NULL COMMENT '0: 禁用，1:启用' AFTER `update_department_depend_his`;
        </sql>
    </changeSet>
    <changeSet id="63" author="ZhangXiang">
        <sql dbms="mssql">
            alter table t_hospital_appointment_rule_setting
                modify hospital_depend_his_enabled_department tinyint(1) default 0 not null comment '0: 禁用，1:启用';
        </sql>
    </changeSet>
    <changeSet id="64" author="hwc">
        <sql dbms="mysql">
            ALTER TABLE `t_hospital_department`
                ADD COLUMN `triage_desk_address` varchar(255) NULL DEFAULT NULL COMMENT '分诊台地址' AFTER `address_intro`;
        </sql>
    </changeSet>
    <changeSet id="65" author="wzy">
        <sql dbms="mysql">
            ALTER TABLE `t_hospital_appointment_rule_setting`
                ADD COLUMN `registration_reminder` text NULL COMMENT '挂号提示' AFTER `appointment_reminder`;
        </sql>
    </changeSet>
    <changeSet id="66" author="ZhangXiang">
        <sql dbms="mysql">
            ALTER TABLE `t_doctor`
                ADD COLUMN `tags` varchar(200) NUll COMMENT '标签' AFTER `shortening`;
        </sql>
    </changeSet>
    <changeSet id="67" author="wzy">
        <sql dbms="mysql">
            ALTER TABLE `t_hospital_department`
                ADD COLUMN `display_fields` int default 0 not NULL COMMENT '展示字段,' AFTER `level_tag`;
        </sql>
    </changeSet>
    <changeSet id="68" author="wzy">
        <sql dbms="mysql">
            ALTER TABLE `t_hospital_department`
                ADD COLUMN `display_department_name` varchar(200) NUll COMMENT '科室显示名称,' AFTER `name`;
        </sql>
    </changeSet>
    <changeSet id="69" author="xiehaibo">
        <sql dbms="mysql">
            ALTER TABLE `t_recommend_config`
                ADD COLUMN `display_name` varchar(500) NUll COMMENT '展示名称' AFTER `img_url`;
        </sql>
    </changeSet>
    <changeSet id="70" author="hwc">
        <sql dbms="mysql">
            CREATE TABLE `t_recommend_doctor_config`
            (
                `id`                 bigint                                                       NOT NULL AUTO_INCREMENT,
                `recommend_id`       bigint                                                       NOT NULL COMMENT '推荐配置id',
                `doctor_id`          bigint                                                       NOT NULL COMMENT '医生id',
                `department_id`      bigint                                                       NOT NULL COMMENT '所属科室id',
                `hospital_area_id`   bigint                                                       NOT NULL COMMENT '所属院区id',
                `hospital_area_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '所属院区编码',
                `hospital_area_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '所属院区名称',
                `hospital_id`        bigint                                                       NOT NULL COMMENT '所属医院编码',
                `sort`               int                                                          NOT NULL DEFAULT 0 COMMENT '排序号',
                `hospital_name`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '所属医院名称',
                `data_tag`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '数据标签',
                `enabled`            tinyint(1) NOT NULL DEFAULT 1 COMMENT '已启用',
                `create_time`        datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                `update_time`        datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                PRIMARY KEY (`id`) USING BTREE
            ) COMMENT = '推荐医生配置' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>
    <changeSet id="71" author="hwc">
        <sql dbms="mysql">
            ALTER TABLE `t_recommend_config`
                ADD COLUMN `key_words` varchar(255) NULL COMMENT '关键词' AFTER `display_name`,
                ADD COLUMN `recommend_score` float NULL COMMENT '推荐指数' AFTER `key_words`,
                ADD COLUMN `recommend_reason` varchar(500) NULL COMMENT '推荐原因' AFTER `recommend_score`,
                ADD COLUMN `specially` varchar(500) NULL COMMENT '擅长' AFTER `recommend_reason`;
        </sql>
    </changeSet>
    <changeSet id="72" author="hwc">
        <sqlFile dbms="mysql" stripComments="true" path="./sql/2025-01-14_t_user_doctor_subscription.sql"
                 relativeToChangelogFile="true"/>
    </changeSet>
    <changeSet id="73" author="hwc">
        <sql dbms="mysql">
            ALTER TABLE `dyt_tenant`.`t_user_doctor_subscription`
                ADD COLUMN `channel_tag` varchar(5) NULL COMMENT '订阅渠道标识' AFTER `hospital_name`;
        </sql>
    </changeSet>
    <changeSet id="xiehaibo-2025-03-31-1" author="xiehaibo">
        <sql dbms="mysql">
            ALTER TABLE `dyt_tenant`.`t_hospital_department`
                ADD COLUMN `appointment_notify_contact` varchar(300) NULL COMMENT '预约消息通知人' AFTER `doctors`;
            ALTER TABLE `dyt_tenant`.`t_hospital_department`
                ADD COLUMN `notice_template_id` varchar(300) NULL COMMENT '预约消息通知模版' AFTER `doctors`;
        </sql>
    </changeSet>

    <changeSet id="gxz-2025-04-25-1" author="gxz">
        <sql dbms="mysql">
            alter table t_doctor
                add update_from_his tinyint(1) default 1 null comment '是否从his更新属性，0：不，1：是';
        </sql>
    </changeSet>
    <!--    t_dict_label 添加字段 ： 角标-->
    <changeSet id="wzy-2025-06-17" author="wzy">
        <addColumn tableName="t_dict_label">
            <column name="badge" type="varchar(300)" remarks="角标">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
</databaseChangeLog>
