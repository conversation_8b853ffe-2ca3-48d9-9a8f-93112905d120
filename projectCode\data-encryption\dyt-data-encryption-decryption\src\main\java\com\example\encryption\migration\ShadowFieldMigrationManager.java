package com.example.encryption.migration;

import com.example.encryption.annotation.EncryptField;
import com.example.encryption.config.MigrationStrategyConfig;
import com.example.encryption.util.AESGCMUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 影子字段迁移管理器
 * 负责处理渐进式影子字段迁移的核心逻辑
 *
 * 功能特性：
 * 1. 支持多种迁移策略（明文优先、影子字段优先、仅影子字段）
 * 2. 提供数据版本管理
 * 3. 支持快速回滚机制
 * 4. 缓存字段元数据以提高性能
 */
@Component
public class ShadowFieldMigrationManager {

    private static final Logger logger = LoggerFactory.getLogger(ShadowFieldMigrationManager.class);

    @Autowired
    private MigrationStrategyConfig strategyConfig;

    /**
     * 字段元数据缓存
     * Key: 类名.字段名
     * Value: 字段迁移元数据
     */
    private final ConcurrentHashMap<String, FieldMigrationMetadata> fieldMetadataCache = new ConcurrentHashMap<>();

    /**
     * 读取字段值（根据迁移策略）
     *
     * @param entity 实体对象
     * @param fieldName 字段名
     * @return 字段值
     */
    public String readFieldValue(Object entity, String fieldName) {
        if (entity == null || !StringUtils.hasText(fieldName)) {
            return null;
        }

        try {
            FieldMigrationMetadata metadata = getFieldMetadata(entity.getClass(), fieldName);
            if (metadata == null || !metadata.isEncrypted()) {
                // 非加密字段，直接返回原值
                return getFieldValue(entity, fieldName);
            }

            return readEncryptedFieldValue(entity, metadata);
        } catch (Exception e) {
            logger.error("Failed to read field value for {}.{}", entity.getClass().getSimpleName(), fieldName, e);
            return null;
        }
    }

    /**
     * 写入字段值（根据迁移策略）
     *
     * @param entity 实体对象
     * @param fieldName 字段名
     * @param value 字段值
     */
    public void writeFieldValue(Object entity, String fieldName, String value) {
        if (entity == null || !StringUtils.hasText(fieldName)) {
            return;
        }

        try {
            FieldMigrationMetadata metadata = getFieldMetadata(entity.getClass(), fieldName);
            if (metadata == null || !metadata.isEncrypted()) {
                // 非加密字段，直接设置值
                setFieldValue(entity, fieldName, value);
                return;
            }

            writeEncryptedFieldValue(entity, metadata, value);
        } catch (Exception e) {
            logger.error("Failed to write field value for {}.{}", entity.getClass().getSimpleName(), fieldName, e);
        }
    }

    /**
     * 根据迁移策略读取加密字段值
     */
    private String readEncryptedFieldValue(Object entity, FieldMigrationMetadata metadata) throws Exception {
        EncryptField.MigrationStrategy strategy = metadata.getMigrationStrategy();
        String fieldName = metadata.getFieldName();
        String shadowFieldName = metadata.getShadowFieldName();

        switch (strategy) {
            case DIRECT_ENCRYPT:
                // 直接加密模式，从当前字段读取并解密
                String encryptedValue = getFieldValue(entity, fieldName);
                return StringUtils.hasText(encryptedValue) ? AESGCMUtil.decrypt(encryptedValue) : null;

            case PLAINTEXT_PRIORITY:
                // 明文优先模式，优先读取明文字段
                String plaintextValue = getFieldValue(entity, fieldName);
                if (StringUtils.hasText(plaintextValue)) {
                    return plaintextValue;
                }
                // 明文字段为空，尝试从影子字段读取并解密
                String shadowEncryptedValue = getFieldValue(entity, shadowFieldName);
                return StringUtils.hasText(shadowEncryptedValue) ? AESGCMUtil.decrypt(shadowEncryptedValue) : null;

            case SHADOW_PRIORITY:
            case SHADOW_ONLY:
                // 影子字段优先模式或仅影子字段模式，优先从影子字段读取
                String shadowValue = getFieldValue(entity, shadowFieldName);
                if (StringUtils.hasText(shadowValue)) {
                    return AESGCMUtil.decrypt(shadowValue);
                }
                // 影子字段为空且不是仅影子字段模式，尝试从明文字段读取
                if (strategy != EncryptField.MigrationStrategy.SHADOW_ONLY) {
                    return getFieldValue(entity, fieldName);
                }
                return null;

            default:
                logger.warn("Unknown migration strategy: {}", strategy);
                return getFieldValue(entity, fieldName);
        }
    }

    /**
     * 根据迁移策略写入加密字段值
     */
    private void writeEncryptedFieldValue(Object entity, FieldMigrationMetadata metadata, String value) throws Exception {
        EncryptField.MigrationStrategy strategy = metadata.getMigrationStrategy();
        String fieldName = metadata.getFieldName();
        String shadowFieldName = metadata.getShadowFieldName();

        switch (strategy) {
            case DIRECT_ENCRYPT:
                // 直接加密模式，加密后写入当前字段
                String encryptedValue = StringUtils.hasText(value) ? AESGCMUtil.encrypt(value) : null;
                setFieldValue(entity, fieldName, encryptedValue);
                break;

            case PLAINTEXT_PRIORITY:
                // 明文优先模式，同时写入明文字段和影子字段
                setFieldValue(entity, fieldName, value);
                String encryptedForShadow = StringUtils.hasText(value) ? AESGCMUtil.encrypt(value) : null;
                setFieldValue(entity, shadowFieldName, encryptedForShadow);
                break;

            case SHADOW_PRIORITY:
                // 影子字段优先模式，只写入影子字段，明文字段置空
                String encryptedShadowValue = StringUtils.hasText(value) ? AESGCMUtil.encrypt(value) : null;
                setFieldValue(entity, shadowFieldName, encryptedShadowValue);
                setFieldValue(entity, fieldName, null);
                break;

            case SHADOW_ONLY:
                // 仅影子字段模式，只写入影子字段
                String encryptedOnlyValue = StringUtils.hasText(value) ? AESGCMUtil.encrypt(value) : null;
                setFieldValue(entity, shadowFieldName, encryptedOnlyValue);
                break;

            default:
                logger.warn("Unknown migration strategy: {}", strategy);
                setFieldValue(entity, fieldName, value);
        }
    }

    /**
     * 获取字段迁移元数据
     */
    private FieldMigrationMetadata getFieldMetadata(Class<?> clazz, String fieldName) {
        String cacheKey = clazz.getName() + "." + fieldName;

        return fieldMetadataCache.computeIfAbsent(cacheKey, key -> {
            try {
                Field field = clazz.getDeclaredField(fieldName);
                EncryptField annotation = field.getAnnotation(EncryptField.class);

                if (annotation == null || !annotation.enabled()) {
                    return new FieldMigrationMetadata(fieldName, false, null, null, 0);
                }

                String shadowFieldName = StringUtils.hasText(annotation.shadowField())
                    ? annotation.shadowField()
                    : fieldName + "_encrypted";

                // 使用配置管理器获取最终的迁移策略
                EncryptField.MigrationStrategy finalStrategy = strategyConfig.getStrategy(
                    annotation.strategyKey(),
                    annotation.migrationStrategy()
                );

                return new FieldMigrationMetadata(
                    fieldName,
                    true,
                    shadowFieldName,
                    finalStrategy,
                    annotation.version(),
                    annotation.strategyKey()
                );
            } catch (Exception e) {
                logger.warn("Failed to get field metadata for {}.{}: {}", clazz.getSimpleName(), fieldName, e.getMessage());
                return new FieldMigrationMetadata(fieldName, false, null, null, 0);
            }
        });
    }

    /**
     * 获取字段值
     */
    private String getFieldValue(Object entity, String fieldName) throws Exception {
        Field field = entity.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        Object value = field.get(entity);
        return value != null ? value.toString() : null;
    }

    /**
     * 设置字段值
     */
    private void setFieldValue(Object entity, String fieldName, String value) throws Exception {
        try {
            Field field = entity.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(entity, value);
        } catch (NoSuchFieldException e) {
            // 影子字段可能不存在于实体类中，这是正常的
            logger.debug("Field {} not found in entity {}, this is normal for shadow fields",
                fieldName, entity.getClass().getSimpleName());
        }
    }

    /**
     * 字段迁移元数据
     */
    public static class FieldMigrationMetadata {
        private final String fieldName;
        private final boolean encrypted;
        private final String shadowFieldName;
        private final EncryptField.MigrationStrategy migrationStrategy;
        private final int version;
        private final String strategyKey;

        public FieldMigrationMetadata(String fieldName, boolean encrypted, String shadowFieldName,
                                    EncryptField.MigrationStrategy migrationStrategy, int version, String strategyKey) {
            this.fieldName = fieldName;
            this.encrypted = encrypted;
            this.shadowFieldName = shadowFieldName;
            this.migrationStrategy = migrationStrategy;
            this.version = version;
            this.strategyKey = strategyKey;
        }

        // 兼容性构造函数
        public FieldMigrationMetadata(String fieldName, boolean encrypted, String shadowFieldName,
                                    EncryptField.MigrationStrategy migrationStrategy, int version) {
            this(fieldName, encrypted, shadowFieldName, migrationStrategy, version, "");
        }

        // Getters
        public String getFieldName() { return fieldName; }
        public boolean isEncrypted() { return encrypted; }
        public String getShadowFieldName() { return shadowFieldName; }
        public EncryptField.MigrationStrategy getMigrationStrategy() { return migrationStrategy; }
        public int getVersion() { return version; }
        public String getStrategyKey() { return strategyKey; }
    }
}
