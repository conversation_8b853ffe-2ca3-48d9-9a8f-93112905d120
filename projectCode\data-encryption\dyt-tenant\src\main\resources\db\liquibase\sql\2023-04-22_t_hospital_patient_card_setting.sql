create table t_hospital_patient_card_setting
(
    id                   bigint auto_increment comment '全局唯一标识'
        primary key,
    function_id          bigint                                   null comment '功能 ID',
    tenant_id            bigint                                   not null comment '租户 ID',
    hospital_id          bigint                                   not null comment '医院 ID',
    hospital_area_id     bigint                                   not null comment '院区 ID',
    hospital_code        varchar(20)                              null comment '医院编码',
    card_name            varchar(20)                              null comment '就诊卡名称',
    need_patient_card    tinyint     default 0                    not null comment '是否需要患者就诊卡：0，不需要；1，需要',
    need_recharge        tinyint     default 0                    not null comment '是否需要充值：0，不需要；1，需要',
    bind_type            int         default 0                    not null comment '绑定类型：0，自动绑卡；1，手动填写自动开卡；2，手工录入',
    support_patient_type int         default 0                    not null comment '支持的患者类型：0，全部；1，成人；2，儿童',
    need_electron_card   tinyint     default 0                    not null comment '是否需要电子就诊卡：0，不需要；1，需要',
    create_time          datetime(3) default CURRENT_TIMESTAMP(3) not null,
    update_time          datetime(3)                              null on update CURRENT_TIMESTAMP(3),
    constraint uidx_limit_single_setting
        unique (tenant_id, hospital_id, hospital_area_id)
)
    comment '医院就诊卡配置';

insert into t_hospital_patient_card_setting
    (id, tenant_id, hospital_id, hospital_area_id)
values (1, 1, 1, 2),
       (2, 1, 1, 3),
       (3, 2, 4, 5),
       (4, 2, 4, 6),
       (5, 2, 4, 7);
