package com.ynhdkc.tenant.service.customer;

import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.client.model.DepartmentList4MultiLevelResponse;
import com.ynhdkc.tenant.dto.ScheduleResponseDto;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;

import java.util.List;

public interface IHisGatewayService {


    List<ScheduleResponseDto.ScheduleInfo> getDepartmentDoctorTimeTypeScheduleList(Department department, List<Doctor> doctors, AppointmentRuleSetting appointmentRuleSetting, Integer timeType);

    List<ScheduleResponseDto.ScheduleInfo> getDepartmentDoctorScheduleList(Department department, List<Doctor> doctors, AppointmentRuleSetting appointmentRuleSetting);

    BaseOperationResponse syncInfoByDepartmentId(Long hospitalAreaId);

    BaseOperationResponse syncInfoByDoctorId(Long departmentId);

    void updateDoctorInfoFromDepartmentDoctorItem(Doctor doctorIn<PERSON><PERSON><PERSON><PERSON><PERSON>, Doctor databaseDoctor, Department department, AppointmentRuleSetting setting);

    BaseOperationResponse syncInfoByHospitalAreaId(Long doctorId);

    void syncYunDaHospitalDoctorInfo();

    DepartmentList4MultiLevelResponse getDepartmentList4MultiLevel(String hospitalCode, String parentDepartmentCode);
}
