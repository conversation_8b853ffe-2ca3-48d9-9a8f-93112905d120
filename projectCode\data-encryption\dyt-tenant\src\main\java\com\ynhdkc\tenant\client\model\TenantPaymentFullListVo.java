package com.ynhdkc.tenant.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * TenantPaymentFullListVo
 */
@Validated
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2023-04-11T14:36:55.358+08:00")


public class TenantPaymentFullListVo {
    @JsonProperty("list")
    @Valid
    private List<TenantPaymentVo> _list = null;

    public TenantPaymentFullListVo _list(List<TenantPaymentVo> _list) {
        this._list = _list;
        return this;
    }

    public TenantPaymentFullListVo addListItem(TenantPaymentVo _listItem) {
        if (this._list == null) {
            this._list = new ArrayList<TenantPaymentVo>();
        }
        this._list.add(_listItem);
        return this;
    }

    /**
     * Get _list
     *
     * @return _list
     **/
    @ApiModelProperty(value = "")

    @Valid

    public List<TenantPaymentVo> getList() {
        return _list;
    }

    public void setList(List<TenantPaymentVo> _list) {
        this._list = _list;
    }


    @Override
    public boolean equals(java.lang.Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TenantPaymentFullListVo tenantPaymentFullListVo = (TenantPaymentFullListVo) o;
        return Objects.equals(this._list, tenantPaymentFullListVo._list);
    }

    @Override
    public int hashCode() {
        return Objects.hash(_list);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class TenantPaymentFullListVo {\n");

        sb.append("    _list: ").append(toIndentedString(_list)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(java.lang.Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}

