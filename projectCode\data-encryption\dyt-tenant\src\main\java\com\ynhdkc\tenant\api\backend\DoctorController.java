package com.ynhdkc.tenant.api.backend;

import backend.common.domain.tenant.constant.AppointmentSystemDepends;
import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import backend.security.method.BackendSecurityRequired;
import backend.security.oplog.DytSecurityRequired;
import backend.security.service.BackendTenantUserService;
import com.google.common.collect.Sets;
import com.ynhdkc.tenant.handler.DoctorApi;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.DoctorKafkaService;
import com.ynhdkc.tenant.service.backend.IDoctorService;
import com.ynhdkc.tenant.service.customer.IHisGatewayService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashSet;

/**
 * <AUTHOR>
 * @since 2023/2/22 17:41:14
 */
@Api(tags = "Doctor")
@RestController
@RequiredArgsConstructor
public class DoctorController implements DoctorApi {
    private final IDoctorService doctorService;
    private final DoctorKafkaService doctorKafkaService;
    private final IHisGatewayService hisGatewayService;

    private final BackendTenantUserService backendTenantUserService;

    @DytSecurityRequired(needOpLog = true, value = "doctor:batch:bind:group")
    @Override
    public ResponseEntity<BaseOperationResponse> batchBindDoctorGroup(DoctorBatchBindGroupReqDto request) {
        return ResponseEntity.ok(doctorService.batchBindDoctorGroup(request));
    }

    @Override
    public ResponseEntity<BaseOperationResponse> batchSyncDoctorInfoFromSpecifiedDoctor(BatchSyncDoctorInfoFromSpecifiedDoctorReqDto request) {
        return ResponseEntity.ok(doctorService.batchSyncDoctorInfoFromSpecifiedDoctor(request));
    }

    @DytSecurityRequired(needOpLog = true, value = "doctor:bind:group")
    @Override
    public ResponseEntity<DoctorGroupRelationVo> bindDoctorGroup(DoctorBindGroupReqDto request) {
        return ResponseEntity.ok(doctorService.bindDoctorGroup(request));
    }

    @DytSecurityRequired(needOpLog = true, value = "doctor:create")
    @BackendSecurityRequired(tenantIdExpr = "#request.tenantId", hospitalIdExpr = "#request.hospitalId", hospitalAreaIdExpr = "#request.hospitalAreaId", departmentIdExpr = "#request.departmentId")
    @Override
    public ResponseEntity<DoctorDetailVo> create(DoctorCreateReqDto request) {
        if (!CollectionUtils.isEmpty(request.getCategory()) && (new HashSet<>(request.getCategory()).containsAll(Sets.newHashSet(0, 1)))) {
            throw new BizException(HttpStatus.BAD_REQUEST, "医生类型错误，预约挂号与疫苗只能选择一个");
        }
        if (null != request.getSystemDepends()) {
            AppointmentSystemDepends dependsEnum = AppointmentSystemDepends.of(request.getSystemDepends());
            if (null == dependsEnum) {
                throw new BizException(HttpStatus.BAD_REQUEST, "系统依赖类型错误");
            }
        }

        DoctorDetailVo doctorVo = doctorService.create(request);
        doctorKafkaService.syncDoctor(doctorVo.getId());
        return ResponseEntity.ok(doctorVo);
    }

    @DytSecurityRequired(needOpLog = true, value = "doctor:delete")
    @BackendSecurityRequired(tenantManager = true)
    @Override
    public ResponseEntity<BaseOperationResponse> delete(Long doctorId, Long tenantId, Long hospitalId, Long hospitalAreaId) {
        BaseOperationResponse baseOperationResponse = doctorService.delete(doctorId);
        doctorKafkaService.deleteDoctor(doctorId);
        return ResponseEntity.ok(baseOperationResponse);
    }

    @DytSecurityRequired(needOpLog = true, value = "doctor:detail:get")
    @Override
    public ResponseEntity<DoctorDetailVo> getDetail(Long doctorId, Long tenantId, Long hospitalId, Long hospitalAreaId, Long departmentId) {
        if (!backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.DepartmentId(departmentId))) {
            throw new BizException(HttpStatus.FORBIDDEN, "用户没有该科室权限");
        }
        return ResponseEntity.ok(doctorService.getDetail(doctorId));
    }

    @DytSecurityRequired(needOpLog = true, value = "doctor:list:get")
    @Override
    public ResponseEntity<DoctorPageVo> query(DoctorQueryReqDto request) {
        return ResponseEntity.ok(doctorService.query(request));
    }

    @DytSecurityRequired(needOpLog = true, value = "doctor:group:query:list")
    @Override
    public ResponseEntity<DoctorGroupRelationQueryRespDto> queryDoctorGroup(DoctorGroupRelationQueryReqDto request) {
        return ResponseEntity.ok(doctorService.queryDoctorGroup(request));
    }

    @DytSecurityRequired(needOpLog = true, value = "doctor:reSort")
    @Override
    public ResponseEntity<BaseOperationResponse> reSortDoctor(Long hospitalAreaId, Long departmentId) {
        return ResponseEntity.ok(doctorService.reSortDoctor(hospitalAreaId, departmentId));

    }

    @DytSecurityRequired(needOpLog = true, value = "doctor:sync")
    @BackendSecurityRequired(tenantManager = true)
    @Override
    public ResponseEntity<BaseOperationResponse> sync() {
        doctorKafkaService.syncAllDoctor();
        return ResponseEntity.ok(new BaseOperationResponse("同步成功"));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "doctor:sync:byDepartmentId")
    public ResponseEntity<BaseOperationResponse> syncInfoByDepartmentId(Long departmentId) {
        return ResponseEntity.ok(hisGatewayService.syncInfoByDepartmentId(departmentId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "doctor:sync:byDoctorId")
    public ResponseEntity<BaseOperationResponse> syncInfoByDoctorId(Long doctorId) {
        return ResponseEntity.ok(hisGatewayService.syncInfoByDoctorId(doctorId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "doctor:sync:byHospitalAreaId")
    public ResponseEntity<BaseOperationResponse> syncInfoByHospitalAreaId(Long hospitalAreaId) {
        return ResponseEntity.ok(hisGatewayService.syncInfoByHospitalAreaId(hospitalAreaId));
    }

    @Override
    public ResponseEntity<BaseOperationResponse> syncYundaDoctorInfo() {
        hisGatewayService.syncYunDaHospitalDoctorInfo();
        return ResponseEntity.ok(new BaseOperationResponse("同步成功"));
    }

    @DytSecurityRequired(needOpLog = true, value = "doctor:unbind:group")
    @Override
    public ResponseEntity<BaseOperationResponse> unbindDoctorGroup(DoctorUnbindGroupReqDto request) {
        return ResponseEntity.ok(doctorService.unbindDoctorGroup(request));
    }

    @DytSecurityRequired(needOpLog = true, value = "doctor:update")
    @BackendSecurityRequired(tenantIdExpr = "#request.tenantId", hospitalIdExpr = "#request.hospitalId", hospitalAreaIdExpr = "#request.hospitalAreaId", departmentIdExpr = "#request.departmentId")
    @Override
    public ResponseEntity<DoctorDetailVo> update(Long doctorId, DoctorUpdateReqDto request) {
        if (!CollectionUtils.isEmpty(request.getCategory()) && (new HashSet<>(request.getCategory()).containsAll(Sets.newHashSet(0, 1)))) {
            throw new BizException(HttpStatus.BAD_REQUEST, "医生类型错误，预约挂号与疫苗只能选择一个");
        }
        if (null != request.getSystemDepends()) {
            AppointmentSystemDepends dependsEnum = AppointmentSystemDepends.of(request.getSystemDepends());
            if (null == dependsEnum) {
                throw new BizException(HttpStatus.BAD_REQUEST, "系统依赖类型错误");
            }
        }

        DoctorDetailVo doctorVo = doctorService.update(doctorId, request);
        doctorKafkaService.syncDoctor(doctorId);
        return ResponseEntity.ok(doctorVo);
    }


}
