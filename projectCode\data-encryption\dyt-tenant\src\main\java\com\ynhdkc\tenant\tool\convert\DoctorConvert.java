package com.ynhdkc.tenant.tool.convert;

import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.entity.DoctorGroupRelation;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.tool.DoctorListTool;
import org.springframework.util.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2023/7/10 16:09:27
 */
public class DoctorConvert {
    private DoctorConvert() {
    }

    public static Doctor toEntity(DoctorCreateReqDto dto) {
        Doctor entity = new Doctor();
        entity.setTenantId(dto.getTenantId());
        entity.setHospitalId(dto.getHospitalId());
        entity.setHospitalAreaId(dto.getHospitalAreaId());
        entity.setDepartmentId(dto.getDepartmentId());
        entity.setName(dto.getName());
        entity.setEmail(dto.getEmail());
        entity.setHeadImgUrl(dto.getHeadImgUrl());
        entity.setSort(dto.getSort());
        entity.setSystemDepends(dto.getSystemDepends());
        entity.setStatus(dto.getStatus());
        entity.setThrdpartDoctorCode(dto.getThrdpartDoctorCode());
        entity.setSpeciality(dto.getSpeciality());
        entity.setIntroduction(dto.getIntroduction());
        entity.setStatement(dto.getStatement());
        entity.setUserId(dto.getUserId());
        entity.setDepartmentCode(dto.getDepartmentCode());
        entity.setHonor(dto.getHonor());
        entity.setCategoryStr(dto.getCategory());
        entity.setDisplay(dto.isDisplay());
        if (null == dto.getForbiddenDay() || dto.getForbiddenDay() < 0) {
            entity.setForbiddenDay(-1);
        } else {
            entity.setForbiddenDay(dto.getForbiddenDay());
        }
        if (null == dto.getAdvanceDay() || dto.getAdvanceDay() < 0) {
            entity.setAdvanceDay(-1);
        } else {
            entity.setAdvanceDay(dto.getAdvanceDay());
        }
        if (StringUtils.hasText(dto.getSourceActivateTime())) {
            entity.setSourceActivateTime(dto.getSourceActivateTime());
        } else {
            entity.setSourceActivateTime(DoctorListTool.DEFAULT_SOURCE_ACTIVATE_TIME);
        }
        entity.setMultiDepartment(dto.isMultiDepartment());

        entity.setVisitingAddress(dto.getVisitingAddress());
        entity.setVisitingIntroduction(dto.getVisitingIntroduction());
        entity.setAppointmentNotice(dto.getAppointmentNotice());
        entity.setPaymentNotice(dto.getPaymentNotice());
        entity.setJudgeAppointmentCondition(dto.isJudgeAppointmentCondition());
        entity.setJudgeAppointmentRule(dto.getJudgeAppointmentRule());
        entity.setNeedUploadResource(dto.isNeedUploadResource());
        entity.setNeedVerifyResource(dto.isNeedVerifyResource());
        entity.setSuccessNoticePhones(dto.getSuccessNoticePhones());
        entity.setSuccessTemplateIds(dto.getSuccessTemplateIds());
        entity.setNeedDivideSettlement(dto.isNeedDivideSettlement());
        entity.setSecondMerchantId(dto.getSecondMerchantId());
        entity.setSettlementRuleId(dto.getSettlementRuleId());
        entity.setTags(dto.getTags());
        return entity;
    }

    public static DoctorDetailVo toDoctorDetailVo(Doctor entity) {
        DoctorDetailVo vo = new DoctorDetailVo();
        toDoctorVo(entity, vo);
        vo.setIntroduction(entity.getIntroduction());
        vo.setStatement(entity.getStatement());
        vo.setVisitingAddress(entity.getVisitingAddress());
        vo.setUserId(entity.getUserId());
        vo.setCategory(entity.getCategoryStr());
        vo.setDisplay(entity.getDisplay());

        vo.setVisitingAddress(entity.getVisitingAddress());
        vo.setVisitingIntroduction(entity.getVisitingIntroduction());
        vo.setAppointmentNotice(entity.getAppointmentNotice());
        vo.setPaymentNotice(entity.getPaymentNotice());
        vo.setJudgeAppointmentCondition(entity.getJudgeAppointmentCondition());
        vo.setJudgeAppointmentRule(entity.getJudgeAppointmentRule());
        vo.setNeedUploadResource(entity.getNeedUploadResource());
        vo.setNeedVerifyResource(entity.getNeedVerifyResource());
        vo.setDisplayDepartmentName(entity.getDisplayDepartmentName());
        vo.setSuccessNoticePhones(entity.getSuccessNoticePhones());
        vo.setSuccessTemplateIds(entity.getSuccessTemplateIds());
        vo.setSecondMerchantId(entity.getSecondMerchantId());
        vo.setSettlementRuleId(entity.getSettlementRuleId());
        vo.setThrdpartDepCode(entity.getDepartmentCode());
        vo.updateFromHis(entity.getUpdateFromHis());
        return vo;
    }

    private static <T extends DoctorVo> void toDoctorVo(Doctor entity, T vo) {
        vo.setId(entity.getId());
        vo.setTenantId(entity.getTenantId());
        vo.setHospitalId(entity.getHospitalId());
        vo.setHospitalAreaId(entity.getHospitalAreaId());
        vo.setHospitalCode(entity.getHospitalCode());
        vo.setDepartmentId(entity.getDepartmentId());
        vo.setThrdpartDepCode(entity.getDepartmentCode());
        vo.setName(entity.getName());
        vo.setEmail(entity.getEmail());
        vo.setHeadImgUrl(entity.getHeadImgUrl());
        vo.setSort(entity.getSort());
        vo.setSystemDepends(entity.getSystemDepends());
        vo.setStatus(entity.getStatus());
        vo.setCreateTime(entity.getCreateTime());
        vo.setThrdpartDoctorCode(entity.getThrdpartDoctorCode());
        vo.setSpeciality(entity.getSpeciality());
        vo.setHonor(entity.getHonor());
        vo.setCategory(entity.getCategoryStr());
        vo.setForbiddenDay(entity.getForbiddenDay());
        vo.setAdvanceDay(entity.getAdvanceDay());
        vo.setSourceActivateTime(entity.getSourceActivateTime());
        vo.setMultiDepartment(entity.getMultiDepartment());
        vo.setNeedDivideSettlement(entity.getNeedDivideSettlement());
        vo.setSecondMerchantId(entity.getSecondMerchantId());
        vo.setSettlementRuleId(entity.getSettlementRuleId());
        vo.setTags(entity.getTags());
    }

    public static CustomerGroupedDoctorDetailVo toGroupedDoctorDetailVo(Doctor entity) {
        CustomerGroupedDoctorDetailVo groupedDoctorDetailVo = new CustomerGroupedDoctorDetailVo();
        toCustomerDoctorVo(entity, groupedDoctorDetailVo);
        return groupedDoctorDetailVo;
    }

    public static CustomerGroupedDoctorDetailVo toGroupedDoctorDetailVo(CustomerAllScheduleDoctorDetailVo entity) {
        CustomerGroupedDoctorDetailVo groupedDoctorDetailVo = new CustomerGroupedDoctorDetailVo();
        toCustomerDoctorVo(entity, groupedDoctorDetailVo);
        return groupedDoctorDetailVo;
    }

    public static DoctorKafkaVo toDoctorKafkaVo(Doctor entity) {
        DoctorKafkaVo vo = new DoctorKafkaVo();
        toDoctorVo(entity, vo);
        vo.setVisitingAddress(entity.getVisitingAddress());
        return vo;
    }

    private static <T extends CustomerDoctorVo> void toCustomerDoctorVo(CustomerAllScheduleDoctorDetailVo entity, T vo) {
        vo.setId(entity.getId());
        vo.setHospitalId(entity.getHospitalId());
        vo.setHospitalAreaId(entity.getHospitalAreaId());
        vo.setHospitalAreaCode(entity.getHospitalAreaCode());
        vo.setDepartmentId(entity.getDepartmentId());
        vo.setDepartmentCode(entity.getDepartmentCode());
        vo.setName(entity.getName());
        vo.setHeadImgUrl(entity.getHeadImgUrl());
        vo.setStatus(entity.getStatus());
        vo.setThrdpartDoctorCode(entity.getThrdpartDoctorCode());
        vo.setSpeciality(entity.getSpeciality());
        vo.setHonor(entity.getHonor());
        vo.setIntroduction(entity.getIntroduction());
        vo.setRankDictType(entity.getRankDictType());
        vo.setRankDictLabel(entity.getRankDictLabel());
        vo.setRankDictValue(entity.getRankDictValue());
        vo.appointmentRuleList(entity.getAppointmentRuleList());
        vo.setRegistrationLevel(entity.getRegistrationLevel());
        vo.setEmail(entity.getEmail());
        vo.setSort(entity.getSort());
        vo.setDisplayDepartmentName(entity.getDisplayDepartmentName());
    }

    private static <T extends CustomerDoctorVo> void toCustomerDoctorVo(Doctor entity, T vo) {
        vo.setId(entity.getId());
        vo.setHospitalId(entity.getHospitalId());
        vo.setHospitalAreaId(entity.getHospitalAreaId());
        vo.setHospitalAreaCode(entity.getHospitalCode());
        vo.setDepartmentId(entity.getDepartmentId());
        vo.setDepartmentCode(entity.getDepartmentCode());
        vo.setName(entity.getName());
        vo.setHeadImgUrl(entity.getHeadImgUrl());
        vo.setStatus(entity.getStatus());
        vo.setThrdpartDoctorCode(entity.getThrdpartDoctorCode());
        vo.setSpeciality(entity.getSpeciality());
        vo.setHonor(entity.getHonor());
        vo.setIntroduction(entity.getIntroduction());
        vo.setRankDictType(entity.getRankDictType());
        if (StringUtils.hasText(entity.getRankDictLabel())) {
            vo.setRankDictLabel(Arrays.asList(entity.getRankDictLabel().split(",")));
        }
        if (StringUtils.hasText(entity.getRankDictValue())) {
            vo.setRankDictValue(Arrays.asList(entity.getRankDictValue().split(",")));
        }
        vo.setRegistrationLevel(entity.getRegistrationLevel());
        if (StringUtils.hasText(entity.getAppointmentRule())) {
            vo.appointmentRuleList(Arrays.asList(entity.getAppointmentRule().split("\\|")));
        }
        vo.setEmail(entity.getEmail());
        vo.setSort(entity.getSort());
        vo.setDisplayDepartmentName(entity.getDisplayDepartmentName());
        vo.setTags(entity.getTags());
        vo.setSettlementRuleId(entity.getSettlementRuleId());
        vo.setSecondMerchantId(entity.getSecondMerchantId());
        vo.setNeedDivideSettlement(entity.getNeedDivideSettlement());
    }

    public static CustomerAllScheduleDoctorDetailVo toDoctorAllScheduleInfoVo(Doctor entity) {
        CustomerAllScheduleDoctorDetailVo allScheduleDoctorDetailVo = new CustomerAllScheduleDoctorDetailVo();
        toCustomerDoctorVo(entity, allScheduleDoctorDetailVo);
        return allScheduleDoctorDetailVo;
    }

    public static CustomerQuickCheckAllScheduleDoctorDetailVo toQuickCheckDoctorAllScheduleInfoVo(Doctor entity) {
        CustomerQuickCheckAllScheduleDoctorDetailVo allScheduleDoctorDetailVo = new CustomerQuickCheckAllScheduleDoctorDetailVo();
        toCustomerDoctorVo(entity, allScheduleDoctorDetailVo);
        return allScheduleDoctorDetailVo;
    }

    public static CustomerDoctorDetailVo toCustomerDoctorDetailVo(Doctor entity) {
        CustomerDoctorDetailVo doctorDetailVo = new CustomerDoctorDetailVo();
        toCustomerDoctorVo(entity, doctorDetailVo);
        doctorDetailVo.setCategory(entity.getCategoryStr());
        doctorDetailVo.setIntroduction(entity.getIntroduction());
        doctorDetailVo.setStatement(entity.getStatement());
        doctorDetailVo.setVisitingIntroduction(entity.getVisitingIntroduction());
        doctorDetailVo.setAppointmentNotice(entity.getAppointmentNotice());
        doctorDetailVo.setPaymentNotice(entity.getPaymentNotice());
        doctorDetailVo.setVisitingAddress(entity.getVisitingAddress());
        doctorDetailVo.setUserId(entity.getUserId());
        doctorDetailVo.setJudgeAppointmentCondition(entity.getJudgeAppointmentCondition());
        doctorDetailVo.setJudgeAppointmentRule(entity.getJudgeAppointmentRule());
        doctorDetailVo.setNeedUploadResource(entity.getNeedUploadResource());
        doctorDetailVo.setNeedVerifyResource(entity.getNeedVerifyResource());
        doctorDetailVo.setSuccessNoticePhones(entity.getSuccessNoticePhones());
        doctorDetailVo.setSuccessTemplateIds(entity.getSuccessTemplateIds());
        return doctorDetailVo;
    }

    public static CustomerDoctorVo toCustomerDoctorVo(Doctor entity) {
        CustomerDoctorVo vo = new CustomerDoctorVo();
        toCustomerDoctorVo(entity, vo);
        return vo;
    }

    public static void toEntity(Doctor entity, DoctorUpdateReqDto dto) {
        if (null != dto.getDepartmentId()) {
            entity.setDepartmentId(dto.getDepartmentId());
        }
        if (null != dto.getName()) {
            entity.setName(dto.getName());
        }
        if (null != dto.getEmail()) {
            entity.setEmail(dto.getEmail());
        }
        if (null != dto.getThrdpartDoctorCode()) {
            entity.setThrdpartDoctorCode(dto.getThrdpartDoctorCode());
        }
        if (null != dto.getHeadImgUrl()) {
            entity.setHeadImgUrl(dto.getHeadImgUrl());
        }
        if (null != dto.getSpeciality()) {
            entity.setSpeciality(dto.getSpeciality());
        }
        if (null != dto.getIntroduction()) {
            entity.setIntroduction(dto.getIntroduction());
        }
        if (null != dto.getStatement()) {
            entity.setStatement(dto.getStatement());
        }
        if (null != dto.getSort()) {
            entity.setSort(dto.getSort());
        }
        if (null != dto.getSystemDepends()) {
            entity.setSystemDepends(dto.getSystemDepends());
        }
        if (null != dto.getStatus()) {
            entity.setStatus(dto.getStatus());
        }
        if (null != dto.getUserId()) {
            entity.setUserId(dto.getUserId());
        }
        if (null != dto.getHonor()) {
            entity.setHonor(dto.getHonor());
        }
        if (null != dto.getCategory()) {
            entity.setCategoryStr(dto.getCategory());
        }
        if (null != dto.isDisplay()) {
            entity.setDisplay(dto.isDisplay());
        }
        if (null != dto.getVisitingAddress()) {
            entity.setVisitingAddress(dto.getVisitingAddress());
        }
        if (null != dto.getVisitingIntroduction()) {
            entity.setVisitingIntroduction(dto.getVisitingIntroduction());
        }
        if (null != dto.getAppointmentNotice()) {
            entity.setAppointmentNotice(dto.getAppointmentNotice());
        }
        if (null != dto.getPaymentNotice()) {
            entity.setPaymentNotice(dto.getPaymentNotice());
        }
        if (null != dto.isJudgeAppointmentCondition()) {
            entity.setJudgeAppointmentCondition(dto.isJudgeAppointmentCondition());
        }
        if (null != dto.getJudgeAppointmentRule()) {
            entity.setJudgeAppointmentRule(dto.getJudgeAppointmentRule());
        }
        if (null != dto.isNeedUploadResource()) {
            entity.setNeedUploadResource(dto.isNeedUploadResource());
        }
        if (null != dto.isNeedVerifyResource()) {
            entity.setNeedVerifyResource(dto.isNeedVerifyResource());
        }
        if (null != dto.getSuccessNoticePhones()) {
            entity.setSuccessNoticePhones(dto.getSuccessNoticePhones());
        }
        if (null != dto.getSuccessTemplateIds()) {
            entity.setSuccessTemplateIds(dto.getSuccessTemplateIds());
        }
        if (null != dto.getForbiddenDay()) {
            entity.setForbiddenDay(dto.getForbiddenDay());
        }
        if (null != dto.getAdvanceDay()) {
            entity.setAdvanceDay(dto.getAdvanceDay());
        }
        if (StringUtils.hasText(dto.getSourceActivateTime())) {
            entity.setSourceActivateTime(dto.getSourceActivateTime());
        }
        if (null != dto.isMultiDepartment()) {
            entity.setMultiDepartment(dto.isMultiDepartment());
        }
        if (null != dto.isNeedDivideSettlement()) {
            entity.setNeedDivideSettlement(dto.isNeedDivideSettlement());
        }
        if (null != dto.getSecondMerchantId()) {
            entity.setSecondMerchantId(dto.getSecondMerchantId());
        }
        if (null != dto.getSettlementRuleId()) {
            entity.setSettlementRuleId(dto.getSettlementRuleId());
        }
        if (null != dto.getDepartmentCode()) {
            entity.setDepartmentCode(dto.getDepartmentCode());
        }
        if (StringUtils.hasText(dto.getTags())) {
            entity.setTags(dto.getTags());
        }
        if(null != dto.isUpdateFromHis()){
            entity.setUpdateFromHis(dto.isUpdateFromHis());
        }
    }

    public static DoctorGroupRelationVo toDoctorGroupRelationVo(DoctorGroupRelation relation) {
        DoctorGroupRelationVo vo = new DoctorGroupRelationVo();
        vo.setId(relation.getId());
        vo.setDoctorId(relation.getDoctorId());
        vo.setDoctorName(relation.getDoctorName());
        vo.setDoctorGroupName(relation.getDoctorGroupName());
        vo.setDoctorGroupId(relation.getDoctorGroupId());
        vo.setDoctorGroupSource(relation.getDoctorGroupSource());
        return vo;
    }

    public static CustomerQuickCheckDoctorVo toCustomerQuickCheckDoctorVo(Doctor doctor) {
        CustomerQuickCheckDoctorVo vo = new CustomerQuickCheckDoctorVo();
        vo.setId(doctor.getId());
        vo.setHospitalId(doctor.getHospitalId());
        vo.setHospitalAreaId(doctor.getHospitalAreaId());
        vo.setDepartmentId(doctor.getDepartmentId());
        vo.setName(doctor.getName());
        vo.setEmail(doctor.getEmail());
        vo.setHeadImgUrl(doctor.getHeadImgUrl());
        vo.setSort(doctor.getSort());
        vo.setStatus(doctor.getStatus());
        vo.setThrdpartDoctorCode(doctor.getThrdpartDoctorCode());
        vo.setSpeciality(doctor.getSpeciality());
        vo.setIntroduction(doctor.getIntroduction());
        vo.setDepartmentCode(doctor.getDepartmentCode());
        vo.setHonor(doctor.getHonor());
        vo.setNeedDivideSettlement(doctor.getNeedDivideSettlement());
        vo.setSecondMerchantId(doctor.getSecondMerchantId());
        vo.setSettlementRuleId(doctor.getSettlementRuleId());
        vo.setTags(doctor.getTags());
        return vo;
    }
}
