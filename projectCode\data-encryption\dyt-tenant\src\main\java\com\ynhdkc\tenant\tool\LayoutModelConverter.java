package com.ynhdkc.tenant.tool;

import com.ynhdkc.tenant.entity.detailpage.*;
import com.ynhdkc.tenant.model.*;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/7/19 16:44
 */
public class LayoutModelConverter {
    private LayoutModelConverter() {
    }

    public static CustomerHospitalAreaDetailPageConfigVo toVo(HospitalAreaDetailPageConfig entity) {
        CustomerHospitalAreaDetailPageConfigVo vo = new CustomerHospitalAreaDetailPageConfigVo();
        vo.setDisplayHospitalArea(entity.getDisplayHospitalArea());
        vo.setDisplayAddress(entity.getDisplayAddress());
        vo.setDisplayLevel(entity.getDisplayLevel());
        vo.setDisplayOpenSchedulingTime(entity.getDisplayOpenSchedulingTime());
        vo.setDisplayAppointNotice(entity.getDisplayAppointNotice());
        vo.setDisplayNotice(entity.getDisplayNotice());
        vo.setDisplayFloorDistribution(entity.getDisplayFloorDistribution());
        vo.setDisplayTab(entity.getDisplayTab());
        return vo;
    }

    public static CustomerHospitalAreaDetailPageNavigatorVo toVo(List<HospitalAreaDetailPageNavigator> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return null;
        }

        CustomerHospitalAreaDetailPageNavigatorVo vo = new CustomerHospitalAreaDetailPageNavigatorVo();
        vo.setType(entities.get(0).getType());
        vo.setNaviInfo(
                entities.stream()
                        .map(entity -> {
                            CustomerHospitalAreaDetailPageNavigatorVoNaviInfo naviInfo = new CustomerHospitalAreaDetailPageNavigatorVoNaviInfo();
                            naviInfo.setTitle(entity.getTitle());
                            naviInfo.setSubTitle(entity.getSubTitle());
                            naviInfo.setPicture(entity.getPicture());
                            naviInfo.setUrl(entity.getUrl());
                            naviInfo.setSort(entity.getSort());
                            return naviInfo;
                        })
                        .sorted(Comparator.comparing(CustomerHospitalAreaDetailPageNavigatorVoNaviInfo::getSort))
                        .collect(Collectors.toList())
        );
        return vo;
    }

    public static CustomerHospitalAreaDetailPageSubNaviModuleVo toVo(HospitalAreaDetailPageSubNaviModule entity) {
        CustomerHospitalAreaDetailPageSubNaviModuleVo vo = new CustomerHospitalAreaDetailPageSubNaviModuleVo();
        vo.setSubNaviType(entity.getSubNaviType());
        vo.setSubNaviDisplayLimit(entity.getSubNaviDisplayLimit());
        vo.setSort(entity.getSort());
        return vo;
    }

    public static CustomerHospitalAreaDetailPageCubeModuleVo toVo(HospitalAreaDetailPageCubeModule entity) {
        CustomerHospitalAreaDetailPageCubeModuleVo vo = new CustomerHospitalAreaDetailPageCubeModuleVo();
        vo.setId(entity.getId());
        vo.setTitle(entity.getTitle());
        vo.setTitleStatus(entity.getTitleStatus());
        vo.setCubeDisplayType(entity.getCubeDisplayType());
        vo.setSort(entity.getSort());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdateTime(entity.getUpdateTime());
        return vo;
    }

    public static CustomerHospitalAreaDetailPageCubeVo toVo(HospitalAreaDetailPageCube entity) {
        CustomerHospitalAreaDetailPageCubeVo vo = new CustomerHospitalAreaDetailPageCubeVo();
        vo.setId(entity.getId());
        vo.setCubeModuleId(entity.getCubeModuleId());
        vo.setTitle(entity.getTitle());
        vo.setPicture(entity.getPicture());
        vo.setUrl(entity.getUrl());
        vo.setSort(entity.getSort());
        vo.setStatus(entity.getStatus());
        return vo;
    }

    public static CustomerHospitalAreaDetailPageTabVo toVo(HospitalAreaDetailPageTab entity) {
        CustomerHospitalAreaDetailPageTabVo vo = new CustomerHospitalAreaDetailPageTabVo();
        vo.setTitle(entity.getTitle());
        vo.setComponentType(entity.getComponentType());
        vo.setRecommendId(entity.getRecommendId());
        vo.setSort(entity.getSort());
        vo.setStatus(entity.getStatus());
        return vo;
    }
}
