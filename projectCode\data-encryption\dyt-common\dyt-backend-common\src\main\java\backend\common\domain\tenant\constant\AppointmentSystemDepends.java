package backend.common.domain.tenant.constant;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-10 10:23
 */
@Getter
public enum AppointmentSystemDepends {
    /**
     * 继承上级
     */
    INHERIT(-1),
    /**
     * HIS
     */
    HIS(0),

    /**
     * 小系统
     */
    MINI_SYSTEM(1);

    private final Integer code;

    AppointmentSystemDepends(int code) {
        this.code = code;
    }

    public static AppointmentSystemDepends of(Integer input) {
        if (null == input) {
            return null;
        }
        for (AppointmentSystemDepends value : AppointmentSystemDepends.values()) {
            if (Objects.equals(value.getCode(), input)) {
                return value;
            }
        }
        return null;
    }

    public static boolean contains(Integer input) {
        if (null == input) {
            return false;
        }
        for (AppointmentSystemDepends value : AppointmentSystemDepends.values()) {
            if (Objects.equals(value.getCode(), input)) {
                return true;
            }
        }
        return false;
    }
}
