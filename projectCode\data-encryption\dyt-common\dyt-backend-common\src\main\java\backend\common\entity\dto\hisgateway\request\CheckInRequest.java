package backend.common.entity.dto.hisgateway.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class CheckInRequest {

    private String name;

    @JsonProperty("hospital_code")
    private String HospitalCode;

    @JsonProperty("medical_record_number")
    private String medicalRecordNumber;

    @JsonProperty("id_card_number")
    private String idCardNumber;

    private String telephone;

    @JsonProperty("visiting_serial_number")
    private String visitingSerialNumber;

    @JsonProperty("visiting_room")
    private String visitingRoom;

    @JsonProperty("appointment_serial_number")
    private String appointmentSerialNumber;

    @JsonProperty("lock_registration_number")
    private String lockRegistrationNumber;

    @JsonProperty("visiting_date")
    private Long visitingDate;

    @JsonProperty("order_number")
    private String orderNumber;

}
