package com.ynhdkc.tenant.api.backend;

import backend.common.response.BaseOperationResponse;
import backend.security.method.BackendSecurityRequired;
import backend.security.oplog.DytSecurityRequired;
import com.ynhdkc.tenant.handler.DictApi;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.DictLabelService;
import com.ynhdkc.tenant.service.backend.DictTypeService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2023/2/11 11:00:44
 */
@Api(tags = "Dict")
@RestController
@RequiredArgsConstructor
public class DictController implements DictApi {

    private final DictTypeService dictTypeService;
    private final DictLabelService dictLabelService;

    @BackendSecurityRequired(tenantManager = true)
    @DytSecurityRequired(needOpLog = true, value = "system:dict:label:create")
    @Override
    public ResponseEntity<DictLabelVo> createDictLabel(CreateDictLabelDto createDictLabelDto) {
        dictTypeService.dictTypeExist(createDictLabelDto.getDictType());
        return ResponseEntity.ok(dictLabelService.create(createDictLabelDto));
    }

    @BackendSecurityRequired(tenantManager = true)
    @DytSecurityRequired(needOpLog = true, value = "system:dict:type:create")
    @Override
    public ResponseEntity<DictTypeVo> createDictType(CreateDictTypeDto createDictTypeDto) {
        return ResponseEntity.ok(dictTypeService.create(createDictTypeDto));
    }

    @BackendSecurityRequired(tenantManager = true)
    @DytSecurityRequired(needOpLog = true, value = "system:dict:label:delete")
    @Override
    public ResponseEntity<BaseOperationResponse> deleteDictLabel(Long id) {
        return ResponseEntity.ok(BaseOperationResponse.builder().effectiveCount(dictLabelService.delete(id)).build());
    }

    @BackendSecurityRequired(tenantManager = true)
    @DytSecurityRequired(needOpLog = true, value = "system:dict:type:delete")
    @Override
    public ResponseEntity<BaseOperationResponse> deleteDictType(Long id) {
        return ResponseEntity.ok(BaseOperationResponse.builder().effectiveCount(dictTypeService.delete(id)).build());
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "system:dict:label:query")
    public ResponseEntity<DictLabelVo> getDictLabelDetail(Long id) {
        return ResponseEntity.ok(dictLabelService.getDictLabelDetail(id));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "system:dict:label:query:list")
    public ResponseEntity<DictLabelPageVo> getDictLabelList(String name, String dictTypeName, String sortBy, String description, Integer currentPage, Integer pageSize) {
        return ResponseEntity.ok(dictLabelService.getDictLabelList(name, dictTypeName, sortBy, description, currentPage, pageSize));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "system:dict:type:query:detail")
    public ResponseEntity<DictTypeVo> getDictTypeDetail(Long id) {
        return ResponseEntity.ok(dictTypeService.getDictTypeDetail(id));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "system:dict:type:query:list")
    public ResponseEntity<DictTypePageVo> getDictTypeList(String name, String description, Integer currentPage, Integer pageSize) {
        return ResponseEntity.ok(dictTypeService.getDictTypeList(name, description, currentPage, pageSize));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "system:dict:type:sync")
    public ResponseEntity<Void> syncDictInfo() {
        dictLabelService.syncAllDictInfo();
        return ResponseEntity.ok().build();
    }

    @BackendSecurityRequired(tenantManager = true)
    @DytSecurityRequired(needOpLog = true, value = "system:dict:label:update")
    @Override
    public ResponseEntity<DictLabelVo> updateDictLabel(Long dictLabelId, UpdateDictLabelDto updateDictLabelDto) {
        dictTypeService.dictTypeExist(updateDictLabelDto.getDictType());
        return ResponseEntity.ok(dictLabelService.update(dictLabelId, updateDictLabelDto));
    }

    @BackendSecurityRequired(tenantManager = true)
    @DytSecurityRequired(needOpLog = true, value = "system:dict:type:update")
    @Override
    public ResponseEntity<DictTypeVo> updateDictType(Long dictTypeId, UpdateDictTypeDto updateDictTypeDto) {
        return ResponseEntity.ok(dictTypeService.update(dictTypeId, updateDictTypeDto));
    }
}
