package com.ynhdkc.tenant.service.backend;

import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.model.TenantUserDetailVo;
import com.ynhdkc.tenant.model.VerifyTenantUserPasswordDto;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-04 14:39
 */
public interface RpcTenantUserService {
    TenantUserDetailVo getDetail(Long userId);

    TenantUserDetailVo getDetail(String userName, String userPhone);

    BaseOperationResponse passwordVerify(VerifyTenantUserPasswordDto request);

    BaseOperationResponse passwordAndCodeVerify(VerifyTenantUserPasswordDto request);

}
