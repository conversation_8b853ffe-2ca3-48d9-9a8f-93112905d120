package com.example.encryption.migration;

import com.example.encryption.entity.MigrationUser;
import com.example.encryption.repository.MigrationUserRepository;
import com.example.encryption.service.MigrationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 影子字段迁移功能测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class ShadowFieldMigrationTest {
    
    @Autowired
    private MigrationService migrationService;
    
    @Autowired
    private MigrationUserRepository userRepository;
    
    private MigrationUser testUser;
    
    @BeforeEach
    void setUp() {
        // 清理测试数据
        userRepository.deleteAll();
        
        // 创建测试用户
        testUser = new MigrationUser();
        testUser.setUsername("migration_test_user");
        testUser.setPhone("13900139000");
        testUser.setEmail("<EMAIL>");
        testUser.setIdCard("110101199001011234");
        testUser.setRealName("测试用户");
        testUser.setAge(30);
        testUser.setDataVersion(1);
        testUser.setMigrationStatus(MigrationUser.MigrationStatus.NOT_MIGRATED);
    }
    
    @Test
    void testCreateMigrationUser() {
        // 创建用户
        MigrationUser createdUser = migrationService.createUser(testUser);
        
        assertNotNull(createdUser.getId());
        assertEquals("migration_test_user", createdUser.getUsername());
        assertEquals("13900139000", createdUser.getPhone());
        assertEquals("<EMAIL>", createdUser.getEmail());
        assertEquals("测试用户", createdUser.getRealName());
        
        // 验证数据版本和迁移状态
        assertEquals(1, createdUser.getDataVersion());
        assertEquals(MigrationUser.MigrationStatus.NOT_MIGRATED, createdUser.getMigrationStatus());
    }
    
    @Test
    void testPlaintextPriorityStrategy() {
        // 创建用户（邮箱使用明文优先策略）
        MigrationUser createdUser = migrationService.createUser(testUser);
        
        // 从数据库重新加载用户
        Optional<MigrationUser> loadedUserOpt = migrationService.getUserById(createdUser.getId());
        assertTrue(loadedUserOpt.isPresent());
        
        MigrationUser loadedUser = loadedUserOpt.get();
        
        // 验证邮箱字段（明文优先策略）
        assertEquals("<EMAIL>", loadedUser.getEmail());
        
        // 验证影子字段也有数据（在数据库层面）
        Optional<MigrationUser> dbUser = userRepository.findById(createdUser.getId());
        assertTrue(dbUser.isPresent());
        assertNotNull(dbUser.get().getEmailEncrypted());
    }
    
    @Test
    void testShadowPriorityStrategy() {
        // 创建用户（身份证号使用影子字段优先策略）
        MigrationUser createdUser = migrationService.createUser(testUser);
        
        // 从数据库重新加载用户
        Optional<MigrationUser> loadedUserOpt = migrationService.getUserById(createdUser.getId());
        assertTrue(loadedUserOpt.isPresent());
        
        MigrationUser loadedUser = loadedUserOpt.get();
        
        // 验证身份证号字段（影子字段优先策略）
        assertEquals("110101199001011234", loadedUser.getIdCard());
        
        // 验证明文字段被清空，影子字段有数据
        Optional<MigrationUser> dbUser = userRepository.findById(createdUser.getId());
        assertTrue(dbUser.isPresent());
        assertNull(dbUser.get().getIdCard()); // 明文字段应该为空
        assertNotNull(dbUser.get().getIdCardEncrypted()); // 影子字段应该有数据
    }
    
    @Test
    void testShadowOnlyStrategy() {
        // 创建用户（真实姓名使用仅影子字段策略）
        MigrationUser createdUser = migrationService.createUser(testUser);
        
        // 从数据库重新加载用户
        Optional<MigrationUser> loadedUserOpt = migrationService.getUserById(createdUser.getId());
        assertTrue(loadedUserOpt.isPresent());
        
        MigrationUser loadedUser = loadedUserOpt.get();
        
        // 验证真实姓名字段（仅影子字段策略）
        assertEquals("测试用户", loadedUser.getRealName());
        
        // 验证明文字段为空，只有影子字段有数据
        Optional<MigrationUser> dbUser = userRepository.findById(createdUser.getId());
        assertTrue(dbUser.isPresent());
        assertNull(dbUser.get().getRealName()); // 明文字段应该为空
        assertNotNull(dbUser.get().getRealNameEncrypted()); // 影子字段应该有数据
    }
    
    @Test
    void testDirectEncryptStrategy() {
        // 创建用户（手机号使用直接加密策略）
        MigrationUser createdUser = migrationService.createUser(testUser);
        
        // 从数据库重新加载用户
        Optional<MigrationUser> loadedUserOpt = migrationService.getUserById(createdUser.getId());
        assertTrue(loadedUserOpt.isPresent());
        
        MigrationUser loadedUser = loadedUserOpt.get();
        
        // 验证手机号字段（直接加密策略）
        assertEquals("13900139000", loadedUser.getPhone());
        
        // 验证数据库中存储的是加密数据
        Optional<MigrationUser> dbUser = userRepository.findById(createdUser.getId());
        assertTrue(dbUser.isPresent());
        // 数据库中的手机号应该是加密的（不等于明文）
        assertNotEquals("13900139000", dbUser.get().getPhone());
    }
    
    @Test
    void testMigrationToVersion2() {
        // 创建版本1的用户
        MigrationUser createdUser = migrationService.createUser(testUser);
        assertEquals(1, createdUser.getDataVersion());
        
        // 迁移到版本2
        MigrationService.MigrationResult result = migrationService.migrateUsersToVersion(2);
        
        // 验证迁移结果
        assertEquals(2, result.getTargetVersion());
        assertEquals(1, result.getTotalUsers());
        assertEquals(1, result.getSuccessCount());
        assertEquals(0, result.getFailureCount());
        
        // 验证用户数据版本已更新
        Optional<MigrationUser> migratedUserOpt = migrationService.getUserById(createdUser.getId());
        assertTrue(migratedUserOpt.isPresent());
        
        MigrationUser migratedUser = migratedUserOpt.get();
        assertEquals(2, migratedUser.getDataVersion());
        assertEquals(MigrationUser.MigrationStatus.MIGRATED, migratedUser.getMigrationStatus());
    }
    
    @Test
    void testRollbackToVersion1() {
        // 创建并迁移用户到版本2
        MigrationUser createdUser = migrationService.createUser(testUser);
        migrationService.migrateUsersToVersion(2);
        
        // 回滚到版本1
        MigrationService.MigrationResult result = migrationService.rollbackUsersToVersion(1);
        
        // 验证回滚结果
        assertEquals(1, result.getTargetVersion());
        assertEquals(1, result.getTotalUsers());
        assertEquals(1, result.getSuccessCount());
        assertEquals(0, result.getFailureCount());
        
        // 验证用户数据版本已回滚
        Optional<MigrationUser> rolledBackUserOpt = migrationService.getUserById(createdUser.getId());
        assertTrue(rolledBackUserOpt.isPresent());
        
        MigrationUser rolledBackUser = rolledBackUserOpt.get();
        assertEquals(1, rolledBackUser.getDataVersion());
        assertEquals(MigrationUser.MigrationStatus.ROLLBACK, rolledBackUser.getMigrationStatus());
    }
    
    @Test
    void testMigrationStatistics() {
        // 创建多个不同状态的用户
        MigrationUser user1 = new MigrationUser();
        user1.setUsername("user1");
        user1.setPhone("13900139001");
        user1.setEmail("<EMAIL>");
        user1.setIdCard("110101199001011235");
        user1.setRealName("用户1");
        user1.setAge(25);
        migrationService.createUser(user1);
        
        MigrationUser user2 = new MigrationUser();
        user2.setUsername("user2");
        user2.setPhone("13900139002");
        user2.setEmail("<EMAIL>");
        user2.setIdCard("110101199001011236");
        user2.setRealName("用户2");
        user2.setAge(28);
        migrationService.createUser(user2);
        
        // 迁移一个用户
        migrationService.migrateUsersToVersion(2);
        
        // 获取统计信息
        MigrationService.MigrationStatistics stats = migrationService.getMigrationStatistics();
        
        assertNotNull(stats);
        assertNotNull(stats.getStatusCounts());
        assertNotNull(stats.getVersionCounts());
        
        // 验证统计数据
        assertTrue(stats.getStatusCounts().containsKey(MigrationUser.MigrationStatus.NOT_MIGRATED));
        assertTrue(stats.getStatusCounts().containsKey(MigrationUser.MigrationStatus.MIGRATED));
        assertTrue(stats.getVersionCounts().containsKey(1));
        assertTrue(stats.getVersionCounts().containsKey(2));
    }
    
    @Test
    void testUpdateMigrationUser() {
        // 创建用户
        MigrationUser createdUser = migrationService.createUser(testUser);
        
        // 更新用户信息
        createdUser.setEmail("<EMAIL>");
        createdUser.setAge(35);
        
        MigrationUser updatedUser = migrationService.updateUser(createdUser);
        
        // 验证更新结果
        assertEquals("<EMAIL>", updatedUser.getEmail());
        assertEquals(35, updatedUser.getAge());
        
        // 从数据库重新加载验证
        Optional<MigrationUser> loadedUserOpt = migrationService.getUserById(createdUser.getId());
        assertTrue(loadedUserOpt.isPresent());
        assertEquals("<EMAIL>", loadedUserOpt.get().getEmail());
        assertEquals(35, loadedUserOpt.get().getAge());
    }
}
