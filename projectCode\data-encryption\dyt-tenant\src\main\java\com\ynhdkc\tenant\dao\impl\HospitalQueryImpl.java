package com.ynhdkc.tenant.dao.impl;

import backend.common.util.MybatisUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.ynhdkc.tenant.dao.HospitalQuery;
import com.ynhdkc.tenant.dao.mapper.AppointmentRuleSettingMapper;
import com.ynhdkc.tenant.dao.mapper.HospitalMapper;
import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-25 21:47
 */
@Repository
@RequiredArgsConstructor
public class HospitalQueryImpl implements HospitalQuery {
    private final HospitalMapper hospitalMapper;

    private final AppointmentRuleSettingMapper appointmentRuleSettingMapper;

    @Override
    public Page<Hospital> pageQueryHospitalWithCategory(HospitalQueryOption option) {
        return PageMethod.startPage(option.getCurrentPage(), option.getPageSize())
                .doSelectPage(() -> hospitalMapper.selectHospitalWithCategory(option));
    }

    @Override
    public Page<Hospital> pageQueryHospital(HospitalQueryOption option) {
        try (final Page<Hospital> page = PageMethod.startPage(option.getCurrentPage(), option.getPageSize())) {
            return page.doSelectPage(() -> hospitalMapper.selectByExample(Hospital.class, sql -> {
                sql.defGroup(condition -> {
                    condition.andEqualTo(Hospital::getHospitalTypeTag, option.getHospitalTypeTag());
                    if (option.getId() != null) {
                        condition.andEqualTo(Hospital::getId, option.getId());
                    }
                    if (option.getTenantId() != null) {
                        condition.andEqualTo(Hospital::getTenantId, option.getTenantId());
                    }
                    if (null != option.getHospitalCode()) {
                        condition.andLike(Hospital::getHospitalCode, MybatisUtil.likeBoth(option.getHospitalCode()));
                    }
                    if (option.getName() != null) {
                        condition.andLike(Hospital::getName, MybatisUtil.likeBoth(option.getName()));
                    }
                    if (option.getLevelDictValue() != null) {
                        condition.andLike(Hospital::getLevelDictValue, MybatisUtil.likeBoth(option.getLevelDictValue()));
                    }
                    if (option.getStatus() != null) {
                        condition.andEqualTo(Hospital::getStatus, option.getStatus());
                    }
                    if (null != option.getStartCreateTime() && null != option.getEndCreateTime()) {
                        condition.andBetween(Hospital::getCreateTime, option.getStartCreateTime(), option.getEndCreateTime());
                    }

                    if (!CollectionUtils.isEmpty(option.getCategory())) {
                        List<Integer> sortedList = option.getCategory().stream().sorted().collect(Collectors.toList());
                        StringBuilder builder = new StringBuilder();
                        sortedList.forEach(category -> builder.append(category).append("%"));
                        String queryPattern = builder.toString();
                        condition.andLike(Hospital::getCategory, MybatisUtil.likeRight(queryPattern));
                    }

                    if (!CollectionUtils.isEmpty(option.getIncludeIds())) {
                        condition.andIn(Hospital::getId, option.getIncludeIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getExcludeIds())) {
                        condition.andNotIn(Hospital::getId, option.getExcludeIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getIncludeTenantIds())) {
                        condition.andIn(Hospital::getTenantId, option.getIncludeTenantIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getExcludeTenantIds())) {
                        condition.andNotIn(Hospital::getTenantId, option.getExcludeTenantIds());
                    }
                });
                sql.builder(builder ->
                        builder.orderByDesc(Hospital::getDisplaySort)
                                .orderByAsc(Hospital::getId));
            }));
        }
    }

    @Override
    public Hospital queryHospitalById(Long hospitalId) {
        List<Hospital> selected = hospitalMapper.selectByExample2(Hospital.class, sql ->
                sql.andEqualTo(Hospital::getId, hospitalId)
                        .andEqualTo(Hospital::getHospitalTypeTag, 0));
        return selected.isEmpty() ? null : selected.get(0);
    }

    @Override
    public Hospital queryHospitalById2(Long hospitalId) {
        List<Hospital> selected = hospitalMapper.selectByExample2(Hospital.class, sql ->
                sql.andEqualTo(Hospital::getId, hospitalId));
        return selected.isEmpty() ? null : selected.get(0);
    }

    @Override
    public List<Hospital> queryBy(Set<Long> hospitalIds) {
        if (CollectionUtils.isEmpty(hospitalIds)) {
            return null;
        }
        return hospitalMapper.selectByExample2(Hospital.class, sql ->
                sql.andIn(Hospital::getId, hospitalIds)
        );
    }

    @Override
    public List<Hospital> queryBy(List<Long> hospitalIds, List<Long> hospitalAreaIds) {
        List<Long> allIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(hospitalIds)) {
            allIds.addAll(hospitalIds);
        }
        if (!CollectionUtils.isEmpty(hospitalAreaIds)) {
            allIds.addAll(hospitalAreaIds);
        }
        if (CollectionUtils.isEmpty(allIds)) {
            return null;
        }

        return hospitalMapper.selectByExample2(Hospital.class, sql ->
                sql.andIn(Hospital::getId, allIds)
        );
    }

    @Override
    public List<Long> queryHisHospital() {
        List<AppointmentRuleSetting> appointmentRuleSettingsDependOnHis = appointmentRuleSettingMapper.selectByExample2(AppointmentRuleSetting.class, sql -> {
            sql.andEqualTo(AppointmentRuleSetting::getSystemDepends, 0);
        });
        if (appointmentRuleSettingsDependOnHis == null) {
            return Collections.emptyList();
        }
        return appointmentRuleSettingsDependOnHis.stream().map(AppointmentRuleSetting::getHospitalAreaId).collect(Collectors.toList());
    }

    @Override
    public List<Long> queryHisHospital(String hospitalCode) {
        List<AppointmentRuleSetting> appointmentRuleSettingsDependOnHis = appointmentRuleSettingMapper.selectByExample2(AppointmentRuleSetting.class, sql -> {
            sql.andEqualTo(AppointmentRuleSetting::getHospitalCode, hospitalCode).andEqualTo(AppointmentRuleSetting::getSystemDepends, 0);
        });
        if (appointmentRuleSettingsDependOnHis == null) {
            return Collections.emptyList();
        }
        return appointmentRuleSettingsDependOnHis.stream().map(AppointmentRuleSetting::getHospitalAreaId).collect(Collectors.toList());
    }

    @Override
    public Optional<Hospital> queryHospitalByCode(String hospitalCode) {
        List<Hospital> hospitals = hospitalMapper.selectByExample2(Hospital.class, sql -> sql.andEqualTo(Hospital::getHospitalCode, hospitalCode));
        if (CollectionUtils.isEmpty(hospitals)) {
            return Optional.empty();
        }
        return Optional.of(hospitals.get(0));
    }
}
