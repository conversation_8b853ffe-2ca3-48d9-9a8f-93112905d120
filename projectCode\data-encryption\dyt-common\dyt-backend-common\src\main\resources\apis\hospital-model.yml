swagger: "2.0"
info:
  title: tenant
  version: "1.0"
  description: 租户中心
host: localhost:9001
basePath: /apis/v1/tenant/backend
schemes:
  - http
  - https

paths:
  { }

definitions:
  BasePage:
    type: object
    properties:
      total_size:
        type: integer
        description: 总条数
        format: int64
      current_page:
        type: integer
        description: 当前页码
      page_size:
        type: integer
        description: 每页条数

  HospitalPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalVo"

  DepartmentPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/DepartmentVo"
  QueryBuildingListDto:
    type: object
    properties:
      name:
        type: string
        description: 楼宇名称
      hospitalId:
        type: integer
        description: 医院id
        format: int64
      address:
        $ref: "#/definitions/AddressVo"
      currentPage:
        type: integer
        description: 页码
      pageSize:
        type: integer
        description: 每页数量

  BuildingPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/BuildingVo"

  BuildingVo:
    type: object
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 医院区域id
        format: int64
      hospital_code:
        type: string
      id:
        type: integer
        description: 大楼id
        format: int64
      name:
        type: string
        description: 大楼名称
      address_id:
        type: integer
        description: 地址id
        format: int64
      address:
        $ref: "#/definitions/AddressVo"
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态
      create_time:
        type: string
        description: 新增时间
        format: date-time
  BuildingCreateReqDto:
    type: object
    required:
      - name
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      hospital_code:
        type: string
      name:
        type: string
        description: 楼宇名称
      address:
        $ref: "#/definitions/AddressDto"
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态
  BuildingUpdateReqDto:
    type: object
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      hospital_code:
        type: string
      name:
        type: string
        description: 楼宇名称
      address:
        $ref: "#/definitions/AddressVo"
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态
  FloorCreateReqDto:
    type: object
    required:
      - name
      - building_id
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      hospital_code:
        type: string
      name:
        type: string
        description: 楼层名称
        minLength: 1
        maxLength: 20
      building_id:
        type: integer
        description: 楼宇id
        format: int64
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态
      picture_urls:
        type: array
        items:
          type: string
          description: 图片

  FloorPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/FloorVo"

  FloorVo:
    type: object
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      hospital_code:
        type: string
      id:
        type: integer
        description: 楼层id
        format: int64
      name:
        type: string
        description: 楼层名称
      building_id:
        type: integer
        description: 楼宇id
        format: int64
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态
      picture_urls:
        type: array
        items:
          type: string
          description: 图片
      create_time:
        type: string
        description: 新增时间
        format: date-time

  FloorUpdateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      hospital_code:
        type: string
      name:
        type: string
        description: 楼层名称
        minLength: 1
        maxLength: 20
      building_id:
        type: integer
        description: 楼宇id
        format: int64
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态
      picture_urls:
        type: array
        items:
          type: string
          description: 图片

  AreaCreateReqDto:
    type: object
    required:
      - name
      - tenant_id
      - hospital_id
      - hospital_area_id
      - building_id
      - floor_id
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      hospital_code:
        type: string
      building_id:
        type: integer
        description: 大楼id
        format: int64
      floor_id:
        type: integer
        description: 楼层id
        format: int64
      name:
        type: string
        description: 区域名称
        minLength: 1
        maxLength: 20
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态
      picture_urls:
        type: array
        items:
          type: string
          description: 图片
  AreaUpdateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      hospital_code:
        type: string
      building_id:
        type: integer
        description: 大楼id
        format: int64
      floor_id:
        type: integer
        description: 楼层id
        format: int64
      name:
        type: string
        description: 区域名称
        minLength: 1
        maxLength: 20
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态
      picture_urls:
        type: array
        items:
          type: string
          description: 图片
  AreaVo:
    type: object
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      hospital_code:
        type: string
      building_id:
        type: integer
        description: 大楼id
        format: int64
      floor_id:
        type: integer
        description: 楼层id
        format: int64
      id:
        type: integer
        description: 区域id
        format: int64
      name:
        type: string
        description: 区域名称
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态
      picture_urls:
        type: array
        items:
          type: string
          description: 图片
      create_time:
        type: string
        description: 新增时间
        format: date-time
  AreaPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
    properties:
      list:
        type: array
        items:
          $ref: "#/definitions/AreaVo"

  DictTypePageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/DictTypeVo"
  DictTypeVo:
    type: object
    properties:
      id:
        type: integer
        description: 字典类型id
        format: int64
      type:
        type: string
        description: 字典类型
      description:
        type: string
        description: 字典类型描述
      create_time:
        type: string
        description: 新增时间
        format: date-time
      update_time:
        type: string
        description: 更新时间
        format: date-time
  CreateDictTypeDto:
    type: object
    required:
      - type
    properties:
      type:
        type: string
        description: 字典类型
        minLength: 1
        maxLength: 40
      description:
        type: string
        description: 字典类型描述

  UpdateDictTypeDto:
    type: object
    allOf:
      - $ref: "#/definitions/CreateDictTypeDto"
      - type: object

  DictLabelPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/DictLabelVo"

  DictLabelVo:
    type: object
    properties:
      id:
        type: integer
        description: 字典标签id
        format: int64
      dict_type:
        type: string
        description: 字典类型
      dict_label:
        type: string
        description: 字典标签
      dict_value:
        type: string
        description: 字典值
      sort:
        type: integer
        description: 排序
      description:
        type: string
        description: 字典标签描述
      icon_url:
        type: string
        description: 图标 URL
      tag:
        type: string
        description: 标签
      redirect_path:
        type: string
        description: 重定向地址
      create_time:
        type: string
        description: 新增时间
        format: date-time
      update_time:
        type: string
        description: 更新时间
        format: date-time

  CreateDictLabelDto:
    type: object
    required:
      - dict_type
      - dict_label
      - dict_value
    properties:
      dict_type:
        type: string
        description: 字典类型
        minLength: 1
        maxLength: 40
      dict_label:
        type: string
        description: 字典标签
        minLength: 1
        maxLength: 40
      dict_value:
        type: string
        description: 字典值
        minLength: 1
        maxLength: 40
      description:
        type: string
        description: 字典标签描述
      sort:
        type: integer
        description: 排序
      icon_url:
        type: string
        description: 图标 URL
      tag:
        type: string
        description: 标签
      redirect_path:
        type: string
        description: 重定向地址


  UpdateDictLabelDto:
    type: object
    allOf:
      - $ref: "#/definitions/CreateDictLabelDto"
      - type: object

  TenantUserCreateReqDto:
    type: object
    required:
      - name
      - status
    properties:
      name:
        type: string
        description: 帐号名
        minLength: 1
        maxLength: 20
      nickname:
        type: string
        description: 用户名
        minLength: 1
        maxLength: 20
      password:
        type: string
        description: 密码
        minLength: 1
        maxLength: 32
      phone_number:
        type: string
        description: 手机号
        minLength: 11
        maxLength: 13
      gender:
        type: integer
        description: 性别,0:未知,1:男,2:女
        format: int32
        default: 0
        minimum: 0
        maximum: 2
      id_card_no:
        type: string
        description: 身份证号
        minLength: 1
        maxLength: 20
      register_platform:
        type: integer
        description: 注册平台 0:未知 1:PC
        format: int32
      status:
        type: integer
        description: 状态 0:正常 1:冻结
        format: int32
        minimum: 0
        maximum: 2
      email:
        type: string
        description: 邮箱
        minLength: 1
        maxLength: 75
      head_image_url:
        type: string
        description: 头像
        minLength: 1
        maxLength: 300
      user_tenant_privilege_configs:
        type: array
        description: 用户权限配置，最基本单位:租户
        items:
          $ref: "#/definitions/UserTenantPrivilegeConfig"
  UserTenantPrivilegeConfig:
    type: object
    properties:
      tenant_id:
        type: integer
        description: 租户id，必填
        format: int64
      tenant_admin:
        type: boolean
        description: 是否租户管理员
      bind_roles:
        type: array
        description: 绑定租户角色id列表
        items:
          type: integer
          format: int64
      unbind_roles:
        type: array
        description: 解绑租户角色id列表
        items:
          type: integer
          format: int64
      hospital_privilege_configs:
        type: array
        description: 当前租户医院配置
        items:
          $ref: "#/definitions/UserHospitalPrivilegeConfig"
  UserHospitalPrivilegeConfig:
    type: object
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_admin:
        type: boolean
        description: 是否医院管理员
      hospital_area_privilege_configs:
        type: array
        description: 当前院区配置(只能是院区)
        items:
          $ref: "#/definitions/UserHospitalAreaPrivilegeConfig"
  UserHospitalAreaPrivilegeConfig:
    type: object
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      hospital_area_admin:
        type: boolean
        description: 是否院区管理员
      departments:
        type: array
        description: 当前租户科室id列表
        items:
          $ref: "#/definitions/UserDepartmentPrivilegeConfig"
  UserDepartmentPrivilegeConfig:
    type: object
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      department_id:
        type: integer
        description: 科室id
        format: int64
      department_admin:
        type: boolean
        description: 是否科室管理员
  TenantUserUpdateReqDto:
    type: object
    allOf:
      - $ref: "#/definitions/TenantUserCreateReqDto"
      - type: object
        properties:
          { }
  TenantUserPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/TenantUserVo"

  RegionQueryReqDto:
    type: object
    required:
      - current_page
      - page_size
    properties:
      current_page:
        type: integer
        description: 当前页码
        default: 1
        minimum: 1
      page_size:
        type: integer
        description: 每页条数
        default: 10
        minimum: 10
        maximum: 200
      id:
        type: integer
        format: int64
        minimum: 1
      pid:
        type: integer
        format: int64
        minimum: 0
      short_name:
        type: string
        maxLength: 100
      name:
        type: string
        maxLength: 100
      merger_name:
        type: string
        maxLength: 255
      level:
        type: integer
        minimum: 1
        maximum: 3
      pinyin:
        type: string
        maxLength: 100
      code:
        type: string
        maxLength: 100
      zip_code:
        type: string
        maxLength: 100
      first:
        type: string
        maxLength: 50
      fid:
        type: integer
      spell_simple:
        type: string
        maxLength: 50
      start_create_time:
        type: string
        format: date-time
      end_create_time:
        type: string
        format: date-time

  RegionCreateReqDto:
    type: object
    properties:
      pid:
        type: integer
        format: int64
        minimum: 0
      short_name:
        type: string
        maxLength: 100
      name:
        type: string
        maxLength: 100
      merger_name:
        type: string
        maxLength: 255
      level:
        type: integer
        minimum: 1
        maximum: 3
      pinyin:
        type: string
        maxLength: 100
      code:
        type: string
        maxLength: 100
      zip_code:
        type: string
        maxLength: 100
      first:
        type: string
        maxLength: 50
      lng:
        type: string
        maxLength: 100
      lat:
        type: string
        maxLength: 100
      fid:
        type: integer
      spell_simple:
        type: string
        maxLength: 50
    required:
      - short_name
      - name
      - merger_name
      - level
      - pid
      - code
      - zip_code
      - first
      - spell_simple

  RegionUpdateReqDto:
    type: object
    properties:
      pid:
        type: integer
        format: int64
        minimum: 0
      short_name:
        type: string
        maxLength: 100
      name:
        type: string
        maxLength: 100
      merger_name:
        type: string
        maxLength: 255
      level:
        type: integer
        minimum: 1
        maximum: 3
      pinyin:
        type: string
        maxLength: 100
      code:
        type: string
        maxLength: 100
      zip_code:
        type: string
        maxLength: 100
      first:
        type: string
        maxLength: 50
      lng:
        type: string
        maxLength: 100
      lat:
        type: string
        maxLength: 100
      fid:
        type: integer
      spell_simple:
        type: string
        maxLength: 50

  RegionVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
      pid:
        type: integer
        format: int64
      short_name:
        type: string
      name:
        type: string
      merger_name:
        type: string
      level:
        type: integer
      pinyin:
        type: string
      code:
        type: string
      zip_code:
        type: string
      first:
        type: string
      lng:
        type: string
      lat:
        type: string
      fid:
        type: integer
      spell_simple:
        type: string
      create_time:
        type: string
        format: date-time

  RegionPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/RegionVo"



  AddressVo:
    type: object
    allOf:
      - $ref: "#/definitions/AddressDto"
      - type: object
        properties:
          id:
            type: integer
            description: 地址id
            format: int64

  AddressDto:
    type: object
    properties:
      province:
        type: string
        description: 省
      city:
        type: string
        description: 市
      county:
        type: string
        description: 区/县
      detail:
        type: string
        description: 详细地址
      longitude:
        type: number
        description: 经度
        format: decimal
      latitude:
        type: number
        description: 纬度
        format: decimal

  VerifyTenantUserPasswordDto:
    type: object
    properties:
      name:
        type: string
        description: 用户名
        minLength: 1
        maxLength: 20
      phone_number:
        type: string
        description: 手机号
        minLength: 11
        maximum: 13
      password:
        type: string
        description: 密码
        minLength: 1
        maxLength: 32

  TenantUserBindingTenantVo:
    type: object
    properties:
      tenants:
        type: array
        items:
          $ref: "#/definitions/TenantVo"

  DepartmentUpdateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
        minimum: 1
      hospital_id:
        type: integer
        description: 医院 id
        format: int64
        minimum: 1
      hospital_area_id:
        type: integer
        description: 院区 id
        format: int64
        minimum: 1
      parent_id:
        type: integer
        description: 父节点科室 id
        format: int64
      thrdpart_dep_code:
        type: string
        description: 第三方科室编码
      name:
        type: string
        description: 科室名称
        minLength: 1
        maxLength: 300
      short_name:
        type: string
        description: 科室简称
      recommended:
        type: boolean
        description: 是否推荐
      category:
        description: 科室类型
        type: array
        items:
          type: integer
          format: int32
      first_letter:
        type: string
        description: 首字母
      introduction:
        type: string
        description: 科室简介
      address_intro:
        type: string
        description: 地址简介
      caution:
        type: string
        description: 科室提醒
      sort:
        type: integer
        description: 排序
      system_depends:
        type: integer
        description: 挂号系统依赖：0,HIS;1,小系统;
      enabled:
        type: boolean
        description: 是否启用
      display_bg_color:
        type: string
        description: 显示背景色
      enable_department_detail:
        type: boolean
        description: 是否启用科室详情
      uri:
        type: string
        description: 科室跳转地址
      forbidden_day:
        type: integer
        description: 禁止预约天数
        minimum: 0
      advance_day:
        type: integer
        description: 提前预约天数
        minimum: 0
      remark:
        type: string
        description: 备注

  TreeDepartmentVo:
    type: object
    allOf:
      - $ref: "#/definitions/DepartmentVo"
      - type: object
        properties:
          enabled:
            type: boolean
            description: 是否启用
          children:
            type: array
            description: 子科室
            items:
              $ref: "#/definitions/TreeDepartmentVo"
  TableDepartmentQueryReqDto:
    type: object
    required:
      - tenant_id
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户id

  TreeDepartmentQueryReqDto:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户id
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      name:
        type: string
        description: 科室名称

  TreeDepartmentQueryVo:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户id
      hospital_list:
        type: array
        items:
          $ref: "#/definitions/TreeHospitalVo"

  TreeHospitalVo:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户id
      id:
        type: integer
        description: 医院id
        format: int64
      name:
        type: string
        description: 医院名称
      status:
        type: integer
        description: 医院状态
      hospital_area_list:
        type: array
        description: 院区列表
        items:
          $ref: "#/definitions/TreeHospitalAreaVo"

  TreeHospitalAreaVo:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户id
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      id:
        type: integer
        description: 院区id
        format: int64
      name:
        type: string
        description: 院区名称
      status:
        type: integer
        description: 院区状态
      department_list:
        type: array
        description: 科室列表
        items:
          $ref: "#/definitions/TreeDepartmentVo"

  TableDepartmentQueryVo:
    type: object
    properties:
      list:
        type: array
        items:
          $ref: "#/definitions/TableHospitalVo"

  TableHospitalVo:
    type: object
    properties:
      id:
        type: integer
        description: 医院id
        format: int64
      name:
        type: string
        description: 医院名称
      hospital_area_list:
        type: array
        description: 院区列表
        items:
          $ref: "#/definitions/HospitalAreaVo"
      department_list:
        type: array
        description: 科室列表
        items:
          $ref: "#/definitions/DepartmentVo"

  HospitalDepartmentTreeVo:
    type: object
    properties:
      id:
        type: integer
        description: 医院id
        format: int64
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      name:
        type: string
        description: 医院名称
      department_list:
        type: array
        description: 科室列表
        items:
          $ref: "#/definitions/TreeDepartmentVo"

  TenantUserQueryReqDto:
    type: object
    required:
      - current_page
      - page_size
    properties:
      current_page:
        description: 当前页
        type: integer
        default: 1
        minimum: 1
      page_size:
        description: 每页条数
        type: integer
        default: 10
        minimum: 10
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
        minimum: 1
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
        minimum: 1
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
        minimum: 1
      department_id:
        type: integer
        format: int64
        description: 科室ID
        minimum: 1
      name:
        description: 用户名称
        type: string
      phone_number:
        description: 手机号
        type: string
      id_card_no:
        description: 身份证号
        type: string
      status:
        description: 状态
        type: integer
        format: int32
      start_create_time:
        type: string
        format: date-time
      end_create_time:
        type: string
        format: date-time
      role_id:
        type: integer
        format: int64
        description: 角色ID

  TenantCreateReqDto:
    type: object
    required:
      - name
      - contact
      - contact_phone_number
    properties:
      name:
        type: string
        description: 租户名称
        minLength: 1
        maxLength: 50
      contact:
        type: string
        description: 联系人
        minLength: 1
        maxLength: 20
      contact_phone_number:
        type: string
        description: 联系人电话
        minLength: 1
        maxLength: 100
      logo_url:
        type: string
        description: logo地址
      description:
        type: string
        description: 描述
        maximum: 300
      contact_email:
        type: string
        description: 联系人邮箱
        minLength: 1
        maxLength: 20
      contact_wechat:
        type: string
        description: 联系人微信
        minLength: 1
        maxLength: 30
      contact_address:
        $ref: "#/definitions/AddressDto"
      address:
        $ref: "#/definitions/AddressDto"
  TenantUpdateReqDto:
    type: object
    properties:
      name:
        type: string
        description: 租户名称
        minLength: 1
        maxLength: 50
      contact:
        type: string
        description: 联系人
        minLength: 1
        maxLength: 20
      contact_phone_number:
        type: string
        description: 联系人电话
        minLength: 1
        maxLength: 100
      logo_url:
        type: string
        description: logo地址
      description:
        type: string
        description: 描述
        maximum: 300
      contact_email:
        type: string
        description: 联系人邮箱
        minLength: 1
        maxLength: 20
      contact_wechat:
        type: string
        description: 联系人微信
        minLength: 1
        maxLength: 30
      address_id:
        type: integer
        description: 租户地址id
        format: int64
      address:
        $ref: "#/definitions/AddressDto"
      contact_address_id:
        type: integer
        description: 联系人地址id
        format: int64
      contact_address:
        $ref: "#/definitions/AddressDto"
  TenantVo:
    type: object
    properties:
      id:
        type: integer
        description: 租户id
        format: int64
      name:
        type: string
        description: 租户名称
      contact:
        type: string
        description: 联系人
      contact_phone_number:
        type: string
        description: 联系人电话
      address_id:
        type: integer
        description: 租户地址 id
        format: int64
      logo_url:
        type: string
        description: logo地址
      description:
        type: string
        description: 描述
      contact_email:
        type: string
        description: 联系人邮箱
      contact_wechat:
        type: string
        description: 联系人微信
      contact_address_id:
        type: integer
        description: 联系人地址 id
        format: int64
      create_time:
        type: string
        description: 新增时间
        format: date-time
      address:
        $ref: "#/definitions/AddressVo"
  TenantDetailVo:
    allOf:
      - $ref: "#/definitions/TenantVo"
      - type: object
        properties:
          contact_address:
            $ref: "#/definitions/AddressVo"
  TenantQueryReqDto:
    type: object
    properties:
      current_page:
        description: 当前页
        type: integer
        default: 1
      page_size:
        description: 每页条数
        type: integer
        default: 10
      name:
        description: 租户名称
        type: string
      contact_person:
        description: 联系人
        type: string
      contact_phone_number:
        description: 联系人电话
        type: string
  TenantPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/TenantVo"
  TenantUserVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 用户id
      name:
        type: string
        description: 账户名
      nickname:
        type: string
        description: 用户名
      email:
        type: string
        description: 邮箱
      roles:
        type: array
        description: 角色列表
        items:
          $ref: "#/definitions/RoleVo"
      status:
        type: integer
        description: 状态 0:正常 1:冻结
      head_image_url:
        type: string
        description: 头像
        minLength: 1
        maxLength: 300
      create_time:
        type: string
        format: date-time
        description: 新增时间
  TenantUserDetailVo:
    allOf:
      - $ref: "#/definitions/TenantUserVo"
      - type: object
        properties:
          phone_number:
            type: string
            description: 手机号
          admin:
            type: boolean
            description: 是否是超级管理员
          tenants:
            type: array
            description: 租户列表
            items:
              $ref: "#/definitions/TenantVo"
          user_tenant_privilege_configs:
            type: array
            description: 用户权限配置
            items:
              $ref: "#/definitions/UserTenantPrivilegeConfig"
  RoleVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
      tenant_id:
        type: integer
        format: int64
        default: 租户ID
      name:
        type: string
      description:
        type: string
      status:
        type: integer
      user_count:
        type: integer
        format: int64
        description: 用户总数
      create_time:
        type: string
        format: date-time
  HospitalCreateReqDto:
    type: object
    required:
      - tenant_id
      - name
      - logo
      - level_dict_label
      - category
    properties:
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
        minimum: 1
      name:
        type: string
        description: 医院名称
      logo:
        type: string
        description: 医院 logo
      level_dict_type:
        type: string
        description: 医院等级字典类型
        default: hospital_level
      level_dict_label:
        type: array
        description: 医院等级字典标签
        items:
          type: string
      property:
        type: integer
        description: 医院性质：0，公立；1，民营；10，其他；
      category:
        type: array
        items:
          type: integer
        description: 医院分类：0，常规；1，疫苗预约；2，特需门诊；3，多学科；9，其他；
      address:
        $ref: "#/definitions/AddressDto"
      status:
        type: integer
        description: 状态,0:启用,1:开发,2:维护,3:停用
        default: 0
        enum:
          - 0
          - 1
          - 2
          - 3

  HospitalVo:
    type: object
    properties:
      id:
        type: integer
        description: 医院id
        format: int64
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_code:
        type: string
        description: 医院编码
      logo:
        type: string
        description: 医院logo
      name:
        type: string
        description: 医院名称
      status:
        type: integer
        description: 状态,0:启用,1:开发,2:维护,3:停用
      category:
        type: array
        items:
          type: integer
        description: 医院类别：0，常规；1，疫苗预约；2，特需门诊；3，多学科；9，其他；
      property:
        type: integer
        description: 医院性质：0，公立；1，民营；10，其他；
      level_dict_type:
        type: string
        description: 医院等级字典类型
        default: hospital_level
      level_dict_label:
        type: array
        description: 医院等级字典标签
        items:
          type: string
      level_dict_value:
        type: array
        description: 医院级别
        items:
          type: string
      display_sort:
        type: integer
        description: 排序
      create_time:
        type: string
        description: 新增时间
        format: date-time

  HospitalKafkaVo:
    allOf:
      - $ref: "#/definitions/HospitalVo"
      - type: object
        properties:
          tenant:
            $ref: "#/definitions/TenantVo"
          hospital_area_list:
            type: array
            description: 院区列表
            items:
              $ref: "#/definitions/HospitalAreaVo"

  HospitalDetailVo:
    allOf:
      - $ref: "#/definitions/HospitalVo"
      - type: object
        properties:
          address:
            $ref: "#/definitions/AddressVo"

  HospitalQueryReqDto:
    type: object
    required:
      - current_page
      - page_size
    properties:
      current_page:
        description: 当前页
        type: integer
        default: 1
        minimum: 1
      page_size:
        description: 每页条数
        type: integer
        default: 10
        minimum: 10
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
        minimum: 1
      hospital_code:
        type: string
        description: 医院编码
      hospital_name:
        type: string
        description: 医院名称
      level_dict_value:
        description: 医院等级
        type: string
      category:
        type: array
        items:
          type: integer
        description: 院区类型,0，常规；1，疫苗预约；2，特需门诊；3，多学科；9，其他；
      status:
        description: 状态,0:启用,1:开发,2:维护,3:停用
        type: integer
        enum:
          - 0
          - 1
          - 2
          - 3
      start_create_time:
        description: 新增起始时间
        type: string
        format: date-time
      end_create_time:
        description: 新增结束时间
        type: string
        format: date-time
  HospitalUpdateReqDto:
    type: object
    required:
      - tenant_id
    properties:
      tenant_id:
        type: integer
        description: 租户 id，做识别，不会更新
        format: int64
        minimum: 1
      name:
        type: string
        description: 医院名称
      logo:
        type: string
        description: 医院 logo
      level_dict_type:
        type: string
        description: 医院等级字典类型
        default: hospital_level
      level_dict_label:
        type: array
        description: 医院等级字典标签
        items:
          type: string
      category:
        type: array
        items:
          type: integer
        description: 院区类型,0，常规；1，疫苗预约；2，特需门诊；3，多学科；9，其他；
      property:
        type: integer
        description: 医院类型,0:公立,1:民营,20:其他
        enum:
          - 0
          - 1
          - 20
      display_sort:
        type: integer
        description: 排序
        minimum: 0
      address:
        $ref: "#/definitions/AddressDto"
      status:
        type: integer
        description: 状态,0:启用,1:开发,2:维护,3:停用
        default: 0
        enum:
          - 0
          - 1
          - 2
          - 3
  HospitalAreaCreateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - name
      - picture
      - category
      - contact_phone_number
      - stop_service_begin_time
      - stop_service_end_time
    properties:
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
        minimum: 1
      hospital_id:
        type: integer
        description: 医院 id
        format: int64
        minimum: 1
      hospital_area_code:
        type: string
        description: 院区编码
      name:
        type: string
        description: 院区名称
      picture:
        type: string
        description: 院区图片
      category:
        type: array
        items:
          type: integer
        description: 院区类型,0，常规；1，疫苗预约；2，特需门诊；3，多学科；9，其他；
      contact_phone_number:
        type: string
        description: 联系电话
      display:
        type: boolean
        description: 是否显示
      display_sort:
        type: integer
        description: 排序
        default: 255
      appointment_scheduling_time:
        type: string
        description: 放号时间
      announcement:
        type: string
        description: 院区公告
      introduction:
        type: string
        description: 院区介绍
      environment:
        type: string
        description: 院区环境
      display_guide:
        type: boolean
        description: 是否显示去院导航
        default: true
      map_keyword:
        type: string
        description: 地图关键字
      display_floor:
        type: boolean
        description: 是否显示楼层
        default: true
      stop_service_begin_time:
        type: string
        description: 停止服务开始时间
      stop_service_end_time:
        type: string
        description: 停止服务结束时间
      address:
        $ref: "#/definitions/AddressDto"
      tag_dict_label:
        type: array
        description: 院区标签
        items:
          type: string
      status:
        type: integer
        description: 状态,0:启用,1:开发,2:维护,3:停用
        default: 0
        enum:
          - 0
          - 1
          - 2
          - 3

  HospitalAreaVo:
    type: object
    properties:
      id:
        type: integer
        description: 院区id
        format: int64
      hospital_area_code:
        type: string
        description: 院区编码
      name:
        type: string
        description: 院区名称
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 所属医院id
        format: int64
      hospital_name:
        type: string
        description: 所属医院名称
      status:
        type: integer
        description: 状态,0:启用,1:开发,2:维护,3:停用
      display:
        type: boolean
        description: 是否显示,0:显示,1:不显示
      category:
        type: array
        items:
          type: integer
        description: 院区类别：0，常规；1，疫苗预约；2，特需门诊；3，多学科；9，其他；
      display_sort:
        type: integer
        description: 排序
      address:
        $ref: "#/definitions/AddressDto"
      create_time:
        type: string
        description: 新增时间
        format: date-time
      appointment_setting:
        $ref: "#/definitions/AppointmentRuleSettingVo"
      stop_service_begin_time:
        type: string
        description: 停止服务开始时间
      stop_service_end_time:
        type: string
        description: 停止服务结束时间

  HospitalAreaDetailVo:
    allOf:
      - $ref: "#/definitions/HospitalAreaVo"
      - type: object
        properties:
          picture:
            type: string
            description: 院区图片
          contact_phone_number:
            type: string
            description: 联系电话
          appointment_scheduling_time:
            type: string
            description: 放号时间
          announcement:
            type: string
            description: 院区公告
          introduction:
            type: string
            description: 院区介绍
          environment:
            type: string
            description: 院区环境
          display_guide:
            type: boolean
            description: 是否显示去院导航
          map_keyword:
            type: string
            description: 地图关键字
          display_floor:
            type: boolean
            description: 是否显示楼层
          hospital_area_settings:
            $ref: "#/definitions/HospitalAreaSettingDetailVo"

  HospitalAreaQueryReqDto:
    type: object
    required:
      - current_page
      - page_size
    properties:
      current_page:
        type: integer
        description: 页码
        default: 1
        minimum: 1
      page_size:
        type: integer
        description: 每页数量
        default: 10
        minimum: 10
      hospital_area_code:
        type: string
        description: 院区编码
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
        minimum: 1
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
        minimum: 1
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
        minimum: 1
      hospital_name:
        type: string
        description: 医院名称
      hospital_area_name:
        type: string
        description: 院区名称
      category:
        type: array
        items:
          type: integer
        description: 院区类型,0，常规；1，疫苗预约；2，特需门诊；3，多学科；9，其他；
      status:
        type: integer
        description: 状态,0:启用,1:开发,2:维护,3:停用
        enum:
          - 0
          - 1
          - 2
          - 3
      start_create_time:
        type: string
        description: 开始时间
        format: date-time
      end_create_time:
        type: string
        description: 结束时间
        format: date-time
      payment_status:
        type: integer
        description: 支付启动状态，为空不做筛选，0：未配置；1，启动；2，停用；
        enum:
          - 0
          - 1
          - 2
  HospitalAreaPageVo:
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalAreaVo"

  HospitalAreaUpdateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
    properties:
      tenant_id:
        type: integer
        description: 租户 id,必填，不做更新
        format: int64
        minimum: 1
      hospital_id:
        type: integer
        format: int64
        description: 医院ID,必填，不做更新
        minimum: 1
      hospital_area_code:
        type: string
        description: 院区编码
      name:
        type: string
        description: 院区名称
      picture:
        type: string
        description: 院区图片
      category:
        type: array
        items:
          type: integer
        description: 院区类型,0，常规；1，疫苗预约；2，特需门诊；3，多学科；9，其他；
      contact_phone_number:
        type: string
        description: 联系电话
      display:
        type: boolean
        description: 是否显示院区列表
      display_sort:
        type: integer
        description: 排序
        default: 255
      announcement:
        type: string
        description: 院区公告
      introduction:
        type: string
        description: 院区介绍
      environment:
        type: string
        description: 院区环境
      display_guide:
        type: boolean
        description: 是否显示去院导航
        default: true
      map_keyword:
        type: string
        description: 地图关键字
      display_floor:
        type: boolean
        description: 是否显示楼层
        default: true
      stop_service_begin_time:
        type: string
        description: 停止服务开始时间
      stop_service_end_time:
        type: string
        description: 停止服务结束时间
      address:
        $ref: "#/definitions/AddressDto"
      status:
        type: integer
        description: 状态,0:启用,1:开发,2:维护,3:停用
        default: 0
        enum:
          - 0
          - 1
          - 2
          - 3
      appointment_scheduling_time:
        type: string
        description: 放号时间
      tag_dict_label:
        type: array
        description: 院区标签
        items:
          type: string

  HospitalAreaDepartmentsInfoQueryReqDto:
    type: object
    properties:
      current_page:
        type: integer
        description: 页码
        default: 1
        minimum: 1
      page_size:
        type: integer
        description: 每页数量
        default: 10
        minimum: 10
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
        minimum: 1
      hospital_area_code:
        type: string
        description: 院区编码
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
        minimum: 1
      hospital_name:
        type: string
        description: 医院名称
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
        minimum: 1
      hospital_area_name:
        type: string
        description: 院区名称
      department_layer:
        type: integer
        description: 展示层级
        minimum: 1
      category:
        type: array
        items:
          type: integer
        description: 科室类型
  HospitalAreaDepartmentsInfoPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalAreaDepartmentInfoVo"
  HospitalAreaDepartmentInfoVo:
    type: object
    properties:
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      hospital_code:
        type: string
        description: 医院编码
      hospital_name:
        type: string
        description: 医院名称
      hospital_area_code:
        type: string
        description: 院区编码
      hospital_area_name:
        type: string
        description: 院区名称
      display_layer:
        type: integer
        description: 展示层级
      total_count:
        type: integer
        description: 科室数量
      system_depends:
        type: integer
        description: 挂号系统依赖：0，HIS；1，小系统；
  HospitalAreaSettingCreateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - type
    properties:
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
        minimum: 1
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
        minimum: 1
      custom_business_setting:
        $ref: "#/definitions/CustomBusinessSettingDto"

  HospitalAreaSettingUpdateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - type
    properties:
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
        minimum: 1
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
        minimum: 1
      type:
        type: string
        description: 新增配置类型，
          appointment_registration:预约挂号设置;
          diagnosis_payment:门诊缴费设置;
          patient_report:就诊报告设置;
          hospitalization:住院配置;
          patient_card:就诊卡设置;
          custom_business:自定义业务设置;
      setting_id:
        type: integer
        format: int64
        description: 配置id,仅更新自定义业务时需要
        minimum: 1
      hospitalization_setting:
        $ref: "#/definitions/HospitalizationSettingDto"
      diagnosis_payment_setting:
        $ref: "#/definitions/DiagnosisPaymentSettingDto"
      custom_business_setting:
        $ref: "#/definitions/CustomBusinessSettingDto"
      appointment_rule_setting:
        $ref: "#/definitions/AppointmentRuleSettingDto"
      patient_card_setting:
        $ref: "#/definitions/PatientCardSettingDto"
      patient_report_setting:
        $ref: "#/definitions/PatientReportSettingDto"
  HospitalAreaSettingDetailVo:
    type: object
    properties:
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
        minimum: 1
      hospital_area_code:
        type: string
        description: 院区编码
      hospitalization_setting:
        $ref: "#/definitions/HospitalizationSettingVo"
      diagnosis_payment_setting:
        $ref: "#/definitions/DiagnosisPaymentSettingVo"
      custom_business_settings:
        type: array
        items:
          $ref: "#/definitions/CustomBusinessSettingVo"
      appointment_rule_setting:
        $ref: "#/definitions/AppointmentRuleSettingVo"
      patient_card_setting:
        $ref: "#/definitions/PatientCardSettingVo"
      patient_report_setting:
        $ref: "#/definitions/PatientReportSettingVo"

  HospitalizationSettingDto:
    type: object
    required:
      - enable_payment
      - support_online_refund
      - enable_info_query
    properties:
      enable_payment:
        type: boolean
        description: 是否开启支付
      support_online_refund:
        type: boolean
        description: 是否支持在线退款
      enable_info_query:
        type: boolean
        description: 是否开启住院信息查询
      selected_payments:
        type: array
        description: 支付方式
        items:
          type: string
      payment_information:
        type: string
        description: 缴费提示信息

  HospitalizationSettingUpdateDto:
    allOf:
      - $ref: "#/definitions/HospitalizationSettingDto"
      - type: object
        required:
          - id
        properties:
          id:
            type: integer
            format: int64
            description: 住院规则配置ID
            minimum: 1

  HospitalizationSettingVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 住院规则配置ID
        minimum: 1
      function_id:
        type: integer
        format: int64
        description: 功能ID
        minimum: 1
      enable_payment:
        type: boolean
        description: 是否开启支付
      support_online_refund:
        type: boolean
        description: 是否支持在线退款
      enable_info_query:
        type: boolean
        description: 是否开启住院信息查询
      selected_payments:
        type: array
        description: 支付方式
        items:
          type: string
      payment_information:
        type: string
        description: 缴费提示信息
      create_time:
        type: string
        description: 新增时间
        format: date-time

  DiagnosisPaymentSettingDto:
    type: object
    required:
      - support_merger_payment
      - support_online_refund
      - refund_today
      - stop_refund_time
      - support_invoice
    properties:
      support_merger_payment:
        type: boolean
        description: 是否支持合并支付
      support_online_refund:
        type: boolean
        description: 是否支持在线退款
      refund_today:
        type: boolean
        description: 是否支持当天退款
      stop_refund_time:
        type: string
        description: 停止退款时间
      support_invoice:
        type: boolean
        description: 是否支持开票
      selected_payments:
        type: array
        description: 支付方式
        items:
          type: string
      payment_information:
        type: string
        description: 缴费提示信息
  DiagnosisPaymentSettingUpdateDto:
    allOf:
      - $ref: "#/definitions/DiagnosisPaymentSettingDto"
      - type: object
        required:
          - id
        properties:
          id:
            type: integer
            format: int64
            description: 门诊缴费规则配置ID
            minimum: 1
  DiagnosisPaymentSettingVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 门诊缴费规则配置ID
        minimum: 1
      function_id:
        type: integer
        format: int64
        description: 功能ID
        minimum: 1
      support_merger_payment:
        type: boolean
        description: 是否支持合并支付
      support_online_refund:
        type: boolean
        description: 是否支持在线退款
      refund_today:
        type: boolean
        description: 是否支持当天退款
      stop_refund_time:
        type: string
        description: 停止退款时间
      support_invoice:
        type: boolean
        description: 是否支持开票
      selected_payments:
        type: array
        description: 支付方式
        items:
          type: string
      payment_information:
        type: string
        description: 缴费提示信息
      create_time:
        type: string
        description: 新增时间
        format: date-time
  CustomBusinessSettingDto:
    type: object
    required:
      - name
      - logo
    properties:
      name:
        type: string
        description: 自定义业务名称
        minLength: 1
        maxLength: 50
      logo:
        type: string
        description: 自定义业务logo
        minLength: 1
        maxLength: 250
      wechat_open_path:
        type: string
        description: 微信公众号路径
        minLength: 1
        maxLength: 250
      mini_program_app_id:
        type: string
        description: 小程序appId
        minLength: 1
        maxLength: 30
      mini_program_path:
        type: string
        description: 小程序路径
        minLength: 1
        maxLength: 250
  CustomBusinessSettingVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 自定义业务配置ID
      function_id:
        type: integer
        format: int64
        description: 功能ID
        minimum: 1
      name:
        type: string
        description: 自定义业务名称
      logo:
        type: string
        description: 自定义业务logo
      wechat_open_path:
        type: string
        description: 微信公众号路径
      mini_program_app_id:
        type: string
        description: 小程序appId
      mini_program_path:
        type: string
        description: 小程序路径
      create_time:
        type: string
        description: 新增时间
        format: date-time

  AppointmentRuleSettingDto:
    type: object
    required:
      - department_level
      - system_depends
      - appoint_today
      - display_no
      - stop_appoint_time
      - advance_day
      - display_time
      - payment_close_duration
      - refund_today
      - stop_refund_time
      - confirm_medical_insurance_card
      - order_need_verify
    properties:
      department_level:
        type: integer
        description: 科室级别，从字典取
      system_depends:
        type: integer
        description: 挂号系统依赖：0，HIS；1，小系统；
      appoint_today:
        type: boolean
        description: 是否支持当天预约
      display_no:
        type: boolean
        description: 是否显示序号
      stop_appoint_time:
        type: string
        description: 停止预约时间
      forbidden_day:
        type: integer
        description: 禁止预约天数
        minimum: -1
      advance_day:
        type: integer
        description: 提前预约天数；
        minimum: 0
      display_time:
        type: string
        description: 号源显示时间
      payment_close_duration:
        type: integer
        description: 支付关闭时间，单位分钟
        format: int64
        minimum: 0
      refund_today:
        type: boolean
        description: 是否支持当天退款
      stop_refund_time:
        type: string
        description: 停止退款时间
      confirm_medical_insurance_card:
        type: boolean
        description: 是否需要确认医保卡
      order_need_verify:
        type: boolean
        description: 是否需要核销
      appointment_result_notice:
        type: string
        description: 预约结果通知
      appointment_notify_contact:
        type: string
        description: 预约后联系人
      notice_template_id:
        type: string
        description: 通知模板id
      appointment_notice:
        type: string
        description: 预约提示
      appointment_rule:
        type: string
        description: 预约规则
      appointment_guide:
        type: string
        description: 预约指引
      selected_payments:
        type: array
        description: 支付方式
        items:
          type: string
      display_doctor_under_department:
        type: integer
        description: 科室下医生列表：0，显示科室下所有医生；1，显示有排班的医生；
      need_payment:
        type: boolean
        description: 是否需要支付， 0:不需要，1:需要
      support_locking_appointment_no:
        type: boolean
        description: 是否支持锁号,false:不支持,true:支持
      support_cancel_appointment:
        type: boolean
        description: 是否支持取消预约,false:不支持,true:支持
      need_partition_time:
        type: boolean
        description: 是否需要分时,false:不需要,true:需要
      display_department_address:
        type: boolean
        description: 是否显示科室地址
      display_visit_time:
        type: boolean
        description: 是否显示就诊时间
      need_sign_in:
        type: boolean
        description: 是否需要签到,false:不需要,true:需要
      all_department_background_color:
        type: string
        description: 全科室背景色
      update_department_exclude_columns:
        type: array
        description: 更新科室排班排除字段
        items:
          type: integer
      update_doctor_exclude_columns:
        type: array
        description: 更新医生排班排除字段
        items:
          type: integer

  AppointmentRuleSettingUpdateDto:
    allOf:
      - $ref: "#/definitions/AppointmentRuleSettingDto"
      - type: object
        required:
          - id
        properties:
          id:
            type: integer
            format: int64
            description: 预约规则配置ID
            minimum: 1

  AppointmentRuleSettingVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 预约规则配置ID
        minimum: 1
      function_id:
        type: integer
        format: int64
        description: 功能ID
        minimum: 1
      department_level:
        type: integer
        description: 科室级别，从字典取
      system_depends:
        type: integer
        description: 挂号系统依赖：0，HIS；1，小系统；
      current_system_type:
        type: integer
        description: 当前使用系統类型：1,老系统;2,新系统;3,新老系统共用;
      appoint_today:
        type: boolean
        description: 是否支持当天预约
      display_no:
        type: boolean
        description: 是否显示序号
      stop_appoint_time:
        type: string
        description: 停止预约时间
      forbidden_day:
        type: integer
        description: 禁止预约天数
        minimum: -1
      advance_day:
        type: integer
        description: 提前预约天数
        minimum: 0
      display_time:
        type: string
        description: 号源显示时间
      payment_close_duration:
        type: integer
        description: 支付关闭时间，单位分钟
        format: int64
        minimum: 0
      refund_today:
        type: boolean
        description: 是否支持当天退款
      stop_refund_time:
        type: string
        description: 停止退款时间
      confirm_medical_insurance_card:
        type: boolean
        description: 是否需要确认医保卡
      order_need_verify:
        type: boolean
        description: 是否需要核销
      appointment_result_notice:
        type: string
        description: 预约结果通知
      appointment_notify_contact:
        type: string
        description: 预约后联系人
      notice_template_id:
        type: string
        description: 通知模板id
      appointment_notice:
        type: string
        description: 预约提示
      appointment_rule:
        type: string
        description: 预约规则
      appointment_guide:
        type: string
        description: 预约指引
      appointment_reminder:
        type: string
        description: 预约提醒
      registration_reminder:
        type: string
        description: 挂号提示
      selected_payments:
        type: array
        description: 支付方式
        items:
          type: string
      display_doctor_under_department:
        type: integer
        description: 科室下医生列表：0，显示科室下所有医生；1，显示有排班的医生；
      all_department_background_color:
        type: string
        description: 全科室背景色
      need_payment:
        type: boolean
        description: 是否需要支付， 0:不需要，1:需要
      support_cancel_appointment:
        type: boolean
        description: 是否支持取消预约,false:不支持,true:支持
      support_locking_appointment_no:
        type: boolean
        description: 是否支持锁号,false:不支持,true:支持
      need_partition_time:
        type: boolean
        description: 是否需要分时,false:不需要,true:需要
      display_department_address:
        type: boolean
        description: 是否显示科室地址
      display_visit_time:
        type: boolean
        description: 是否显示就诊时间
      need_sign_in:
        type: boolean
        description: 是否需要签到,false:不需要,true:需要
      update_department_exclude_columns:
        type: array
        description: 更新科室排班排除字段
        items:
          type: integer
      update_doctor_exclude_columns:
        type: array
        description: 更新医生排班排除字段
        items:
          type: integer
      create_time:
        type: string
        description: 新增时间
        format: date-time
      update_time:
        type: string
        description: 更新时间
        format: date-time

  PatientCardSettingDto:
    type: object
    required:
      - need_patient_card
      - bind_type
      - support_patient_type
      - need_electron_card
    properties:
      card_name:
        type: string
        description: 就诊卡名称
      need_patient_card:
        type: boolean
        description: 是否需要就诊卡
      need_recharge:
        type: boolean
        description: 是否需要充值
      bind_type:
        type: integer
        description: 绑定类型：0，自动绑卡；1，手动填写自动开卡；2，手工录入
        enum:
          - 0
          - 1
          - 2
      support_patient_type:
        type: integer
        description: 支持的患者类型：0，全部；1，成人；2，儿童
        enum:
          - 0
          - 1
          - 2
      need_electron_card:
        type: boolean
        description: 是否需要电子就诊卡
  PatientCardSettingUpdateDto:
    allOf:
      - $ref: "#/definitions/PatientCardSettingDto"
      - type: object
        required:
          - id
        properties:
          id:
            type: integer
            format: int64
            description: 就诊卡配置ID
            minimum: 1
  PatientCardSettingVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 就诊卡配置ID
        minimum: 1
      function_id:
        type: integer
        format: int64
        description: 功能ID
        minimum: 1
      card_name:
        type: string
        description: 就诊卡名称
      need_patient_card:
        type: boolean
        description: 是否需要就诊卡
      need_recharge:
        type: boolean
        description: 是否需要充值
      bind_type:
        type: integer
        description: 绑定类型：0，自动绑卡；1，手动填写自动开卡；2，手工录入
        enum:
          - 0
          - 1
          - 2
      support_patient_type:
        type: integer
        description: 支持的患者类型：0，全部；1，成人；2，儿童
      need_electron_card:
        type: boolean
        description: 是否需要电子就诊卡
      create_time:
        type: string
        description: 新增时间
        format: date-time
  PatientReportSettingDto:
    type: object
    properties:
      support_report_type:
        type: integer
        description: 支持的报告类型，0：全部，1：检验报告，2：检查报告
      support_search_date_range:
        type: integer
        description: 支持的按钮刻度，0：无限制，1：30天内，2：90天内，3：180天内，4：一年内
      support_search_time:
        type: array
        description: 支持的查询时间
        items:
          type: integer
          description: 0：全部，1：最近一周，2：最近一月，3：最近三月，4：最近半年，5：最近一年
      notice:
        type: string
        description: 提示消息
  PatientReportSettingVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 报告配置ID
        minimum: 1
      function_id:
        type: integer
        format: int64
        description: 功能ID
        minimum: 1
      support_report_type:
        type: integer
        description: 支持的报告类型，0：全部，1：检验报告，2：检查报告
        enum:
          - 0
          - 1
          - 2
      support_search_date_range:
        type: integer
        description: 支持的按钮刻度，0：无限制，1：30天内，2：90天内，3：180天内，3：一年内
        enum:
          - 0
          - 1
          - 2
          - 3
      support_search_time:
        type: array
        description: 支持的查询时间
        items:
          type: integer
          description: 0：全部，1：最近一周，2：最近一月，3：最近三月，4：最近半年，5：最近一年
          enum:
            - 0
            - 1
            - 2
            - 3
            - 4
            - 5
      notice:
        type: string
        description: 提示消息
      create_time:
        type: string
        description: 新增时间
        format: date-time

  DepartmentCreateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
      - parent_id
      - name
      - first_letter
      - category
    properties:
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
        minimum: 1
      hospital_id:
        type: integer
        description: 医院 id
        format: int64
        minimum: 1
      hospital_area_id:
        type: integer
        description: 院区 id
        format: int64
        minimum: 1
      parent_id:
        type: integer
        description: 父节点科室 id
        format: int64
      thrdpart_dep_code:
        type: string
        description: 第三方科室编码
      name:
        type: string
        description: 科室名称
        minLength: 1
        maxLength: 300
      short_name:
        type: string
        description: 科室简称
      recommended:
        type: boolean
        description: 是否推荐
      category:
        type: array
        items:
          type: integer
          format: int32
          description: 科室类型
      first_letter:
        type: string
        description: 首字母
      introduction:
        type: string
        description: 科室简介
      address_intro:
        type: string
        description: 地址简介
      caution:
        type: string
        description: 科室提醒
      sort:
        type: integer
        description: 排序
      system_depends:
        type: integer
        description: 挂号系统依赖：0,HIS;1,小系统;99,继承院区配置;
      enabled:
        type: boolean
        description: 是否启用
      display_bg_color:
        type: string
        description: 显示背景色
      enable_department_detail:
        type: boolean
        description: 是否启用科室详情
      uri:
        type: string
        description: 科室跳转地址
      forbidden_day:
        type: integer
        description: 禁止预约天数
        minimum: 0
      advance_day:
        type: integer
        description: 提前预约天数
        minimum: 0
      remark:
        type: string
        description: 备注

  DepartmentQueryReqDto:
    type: object
    required:
      - current_page
      - page_size
    properties:
      current_page:
        description: 当前页
        type: integer
        default: 1
      page_size:
        description: 每页条数
        type: integer
        default: 10
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
        minimum: 1
      hospital_id:
        description: 医院id
        type: integer
        format: int64
        minimum: 1
      hospital_area_id:
        description: 院区id
        type: integer
        format: int64
        minimum: 1
      name:
        description: 科室名称
        type: string
      thrdpart_dep_code:
        description: 第三方科室编码
        type: string
      enabled:
        description: 是否启用
        type: boolean
      category:
        description: 科室类别
        type: array
        items:
          type: integer
          format: int32

  DepartmentVo:
    type: object
    properties:
      id:
        type: integer
        description: 科室 id
        format: int64
      parent_id:
        type: integer
        description: 父节点科室 id
        format: int64
      tenant_id:
        type: integer
        description: 租户 id
        format: int64
      hospital_id:
        type: integer
        description: 医院 id
        format: int64
      hospital_code:
        type: string
        description: 医院编码
      hospital_area_id:
        type: integer
        description: 院区 id
        format: int64
      thrdpart_dep_code:
        type: string
        description: 第三方科室编码
      name:
        type: string
        description: 科室名称
        minLength: 1
        maxLength: 20
      category:
        type: array
        description: 科室类型
        items:
          type: integer
          format: int32
      hospital_name:
        type: string
        description: 医院名称
      hospital_area_name:
        type: string
        description: 院区名称
      system_depends:
        type: integer
        description: 挂号系统依赖：0,HIS;1,小系统;99,继承院区配置;
      enabled:
        type: boolean
        description: 是否启用
      display_bg_color:
        type: string
        description: 显示背景色
      enable_department_detail:
        type: boolean
        description: 是否启用科室详情
      uri:
        type: string
        description: 科室跳转地址
      first_letter:
        type: string
        description: 首字母
      sort:
        type: integer
        description: 排序
      address_intro:
        type: string
        description: 地址简介
      forbidden_day:
        type: integer
        description: 禁止预约天数
        minimum: 0
      advance_day:
        type: integer
        description: 提前预约天数
        minimum: 0
      remark:
        type: string
        description: 备注
      layer:
        type: integer
        description: 层级
      create_time:
        type: string
        description: 新增时间
        format: date-time

  DepartmentDetailVo:
    allOf:
      - $ref: "#/definitions/DepartmentVo"
      - type: object
        properties:
          short_name:
            type: string
            description: 科室简称
          introduction:
            type: string
            description: 科室简介
          recommended:
            type: boolean
            description: 是否推荐
          caution:
            type: string
            description: 科室提醒
          first_letter:
            type: string
            description: 首字母

  FloorQueryReqDto:
    type: object
    required:
      - current_page
      - page_size
    properties:
      current_page:
        description: 当前页
        type: integer
        default: 1
      page_size:
        description: 每页条数
        type: integer
        default: 10
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      building_id:
        description: 医院大楼id
        type: integer
        format: int64
      name:
        description: 楼层名称
        type: string
      status:
        description: 楼层状态,0:正常,1:停用
        type: integer
        enum:
          - 0
          - 1

  DoctorPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/DoctorVo"
  DoctorVo:
    type: object
    properties:
      id:
        type: integer
        description: 医生id
        format: int64
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_code:
        type: string
        description: 院区编码
      hospital_name:
        type: string
        description: 医院名称
      thrdpart_doctor_code:
        type: string
        description: 第三方医生编码
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      hospital_area_name:
        type: string
        description: 院区名称
      department_id:
        type: integer
        description: 科室id
        format: int64
      thrdpart_dep_code:
        type: string
        description: 科室编码
      department_name:
        type: string
        description: 科室名称
      name:
        type: string
        description: 医生姓名
      email:
        type: string
        description: 邮箱
      head_img_url:
        type: string
        description: 头像 URL 地址
      rank_dict_type:
        type: string
        description: 职称字典类型
      rank_dict_label:
        type: array
        description: 职称
        items:
          type: string
      rank_dict_value:
        type: array
        description: 职称字典值
        items:
          type: string
      sort:
        type: integer
        description: 排序
      speciality:
        type: string
        description: 专长
      category:
        description: 医生类别
        type: array
        items:
          type: integer
          format: int32
      system_depends:
        type: integer
        description: 挂号系统依赖：0，HIS；1，小系统；
      status:
        type: integer
        description: 状态：0，正常；1，禁用
      honor:
        type: string
        description: 荣誉
      forbidden_day:
        type: integer
        description: 禁止预约天数
        minimum: 0
      advance_day:
        type: integer
        description: 提前预约天数
        minimum: 0
      multi_department:
        type: boolean
        description: 医生显示多个科室
      need_divide_settlement:
        type: boolean
        description: 是否需要分账
      second_merchant_id:
        type: integer
        description: 分账二级商户id
        format: int64
      settlement_rule_id:
        type: integer
        description: 结算规则id
        format: int64
      create_time:
        type: string
        description: 新增时间
        format: date-time

  DoctorDetailVo:
    allOf:
      - $ref: "#/definitions/DoctorVo"
      - type: object
        properties:
          introduction:
            type: string
            description: 简介
          appointment_rule_dict_type:
            type: string
            description: 预约规则字典类型
          appointment_rule_dict_label:
            type: array
            description: 预约规则
            items:
              type: string
          statement:
            type: string
            description: 特殊说明
          user_id:
            type: integer
            description: 与在线问诊医生关联的用户 id
            format: int64
            default: 0
          category:
            description: 医生类别
            type: array
            items:
              type: integer
              format: int32
          display:
            type: boolean
            description: C端是否展示该医生
          visiting_address:
            type: string
            description: 出诊地址
          visiting_introduction:
            type: string
            description: 出诊信息
          appointment_notice:
            type: string
            description: 预约提示
          payment_notice:
            type: string
            description: 缴费提示
          judge_appointment_condition:
            type: boolean
            description: 是否判断预约条件
          judge_appointment_rule:
            type: string
            description: 预约条件,|分割
          need_upload_resource:
            type: boolean
            description: 是否需要上传资源
          need_verify_resource:
            type: boolean
            description: 是否需要审核资源
          success_notice_phones:
            type: string
            description: 预约成功通知手机号,分割
          success_template_ids:
            type: string
            description: 预约成功通知模板id,分割

  DoctorCreateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
      - department_id
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      department_id:
        type: integer
        description: 科室id
        format: int64
      department_code:
        type: string
        description: 第三方科室编码
      name:
        type: string
        description: 医生姓名
      email:
        type: string
        description: 邮箱
      thrdpart_doctor_code:
        type: string
        description: 第三方医生编码
      head_img_url:
        type: string
        description: 头像 URL 地址
      speciality:
        type: string
        description: 专长
      rank_dict_type:
        type: string
        description: 职称字典类型
      rank_dict_label:
        type: array
        description: 职称
        items:
          type: string
      introduction:
        type: string
        description: 简介
      appointment_rule_dict_type:
        type: string
        description: 预约规则字典类型
      appointment_rule_dict_label:
        type: array
        description: 预约规则
        items:
          type: string
      statement:
        type: string
        description: 特殊说明
      category:
        description: 医生类别
        type: array
        items:
          type: integer
          format: int32
      display:
        type: boolean
        description: C端是否展示该医生
      system_depends:
        type: integer
        description: 挂号系统依赖：0，HIS；1，小系统；
      status:
        type: integer
        description: 状态：0，正常；1，禁用
      user_id:
        type: integer
        description: 与在线问诊医生关联的用户 id
        format: int64
        default: 0
      sort:
        type: integer
        description: 排序
      honor:
        type: string
        description: 荣誉
      visiting_address:
        type: string
        description: 出诊地址
      visiting_introduction:
        type: string
        description: 出诊信息
      appointment_notice:
        type: string
        description: 预约提示
      payment_notice:
        type: string
        description: 缴费提示
      judge_appointment_condition:
        type: boolean
        description: 是否判断预约条件
      judge_appointment_rule:
        type: string
        description: 预约条件,|分割
      need_upload_resource:
        type: boolean
        description: 是否需要上传资源
      need_verify_resource:
        type: boolean
        description: 是否需要审核资源
      success_notice_phones:
        type: string
        description: 预约成功通知手机号,分割
      success_template_ids:
        type: string
        description: 预约成功通知模板id,分割
      forbidden_day:
        type: integer
        description: 禁止预约天数
        minimum: 0
      advance_day:
        type: integer
        description: 提前预约天数
        minimum: 0
      multi_department:
        type: boolean
        description: 医生显示多个科室
      need_divide_settlement:
        type: boolean
        description: 是否需要分账
      second_merchant_id:
        type: integer
        description: 分账二级商户id
        format: int64
      settlement_rule_id:
        type: integer
        description: 结算规则id
        format: int64

  DoctorUpdateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
      - department_id
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      hospital_area_id:
        type: integer
        description: 院区id
        format: int64
      department_id:
        type: integer
        description: 科室id
        format: int64
      name:
        type: string
        description: 医生姓名
      email:
        type: string
        description: 邮箱
      thrdpart_doctor_code:
        type: string
        description: 第三方医生编码
      head_img_url:
        type: string
        description: 头像 URL 地址
      speciality:
        type: string
        description: 专长
      rank_dict_type:
        type: string
        description: 职称字典类型
      rank_dict_label:
        type: array
        description: 职称
        items:
          type: string
      introduction:
        type: string
        description: 简介
      appointment_rule_dict_type:
        type: string
        description: 预约规则字典类型
      appointment_rule_dict_label:
        type: array
        description: 预约规则
        items:
          type: string
      statement:
        type: string
        description: 特殊说明
      category:
        description: 医生类别
        type: array
        items:
          type: integer
          format: int32
      display:
        type: boolean
        description: C端是否展示该医生
      system_depends:
        type: integer
        description: 挂号系统依赖：0，HIS；1，小系统；
      status:
        type: integer
        description: 状态：0，正常；1，禁用
      user_id:
        type: integer
        description: 与在线问诊医生关联的用户 id
        format: int64
        default: 0
      sort:
        type: integer
        description: 排序
      honor:
        type: string
        description: 荣誉
      visiting_address:
        type: string
        description: 出诊地址
      visiting_introduction:
        type: string
        description: 出诊信息
      appointment_notice:
        type: string
        description: 预约提示
      payment_notice:
        type: string
        description: 缴费提示
      judge_appointment_condition:
        type: boolean
        description: 是否判断预约条件
      judge_appointment_rule:
        type: string
        description: 预约条件,|分割
      need_upload_resource:
        type: boolean
        description: 是否需要上传资源
      need_verify_resource:
        type: boolean
        description: 是否需要审核资源
      success_notice_phones:
        type: string
        description: 预约成功通知手机号,分割
      success_template_ids:
        type: string
        description: 预约成功通知模板id,分割
      forbidden_day:
        type: integer
        description: 禁止预约天数
        minimum: 0
      advance_day:
        type: integer
        description: 提前预约天数
        minimum: 0
      multi_department:
        type: boolean
        description: 医生显示多个科室
      need_divide_settlement:
        type: boolean
        description: 是否需要分账
      second_merchant_id:
        type: integer
        description: 分账二级商户id
        format: int64
      settlement_rule_id:
        type: integer
        description: 结算规则id
        format: int64

  DoctorKafkaVo:
    type: object
    allOf:
      - $ref: "#/definitions/DoctorVo"
      - type: object
        properties:
          visiting_address:
            type: string
            description: 就诊地址
  DoctorQueryReqDto:
    type: object
    properties:
      current_page:
        description: 当前页
        type: integer
        default: 1
      page_size:
        description: 每页条数
        type: integer
        default: 10
      tenant_id:
        description: 租户id
        type: integer
        format: int64
      hospital_id:
        description: 医院id
        type: integer
        format: int64
      hospital_area_id:
        description: 院区id
        type: integer
        format: int64
      department_id:
        description: 科室id
        type: integer
        format: int64
      name:
        description: 医生姓名
        type: string
      thrdpart_doctor_code:
        description: 第三方医生编码
        type: string
      status:
        type: integer
        description: 状态：0，正常；1，禁用
      start_create_time:
        type: string
        format: date-time
      end_create_time:
        type: string
        format: date-time
      category:
        description: 医生类别：0，预约挂号；1，疫苗；2，在线问诊
        type: array
        items:
          type: integer
          format: int32
  BuildingQueryReqDto:
    type: object
    required:
      - current_page
      - page_size
    properties:
      current_page:
        description: 当前页
        type: integer
        default: 1
      page_size:
        description: 每页条数
        type: integer
        default: 10
      tenant_id:
        description: 租户id
        type: integer
        format: int64
      hospital_id:
        description: 医院id
        type: integer
        format: int64
      hospital_area_id:
        description: 院区id
        type: integer
        format: int64
      name:
        description: 医院大楼名称
        type: string
      status:
        description: 医院大楼状态,0:正常,1:停用
        type: integer
        enum:
          - 0
          - 1
  AreaQueryReqDto:
    type: object
    properties:
      current_page:
        description: 当前页
        type: integer
        default: 1
      page_size:
        description: 每页条数
        type: integer
        default: 10
      tenant_id:
        description: 租户id
        type: integer
        format: int64
      hospital_id:
        description: 医院id
        type: integer
        format: int64
      hospital_area_id:
        description: 院区id
        type: integer
        format: int64
      building_id:
        description: 大楼id
        type: integer
        format: int64
      floor_id:
        description: 楼层id
        type: integer
        format: int64
      name:
        description: 区域名称
        type: string
      status:
        description: 区域状态,0:正常,1:停用
        type: integer
        enum:
          - 0
          - 1
  HospitalAreaFunctionUpdateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - function
    properties:
      tenant_id:
        type: integer
        description: 租户id
        format: int64
      hospital_id:
        type: integer
        description: 医院id
        format: int64
      id:
        type: integer
        description: 院区功能id
        format: int64
      logo:
        type: string
        description: 院区功能图标icon
      tab_display_style:
        type: string
        description: 院区功能tab展示样式
      status:
        type: integer
        description: 状态,0:开启 1:维护 2:关闭
        enum:
          - 0
          - 1
          - 2
  HospitalAreaFunctionDetailVo:
    type: object
    properties:
      function_list:
        type: array
        description: 院区功能列表
        items:
          $ref: "#/definitions/HospitalAreaFunctionItem"
  HospitalAreaFunctionItem:
    type: object
    required:
      - id
    properties:
      id:
        type: integer
        description: 院区功能id
        format: int64
      name:
        type: string
        description: 院区功能名称
      type:
        type: string
        description: 院区功能类型
      logo:
        type: string
        description: 院区功能图标icon
      tab_display_style:
        type: string
        description: 院区功能tab展示样式
      sort:
        type: integer
        description: 排序
      status:
        type: integer
        description: 状态,0:开启 1:维护 2:关闭
        enum:
          - 0
          - 1
          - 2
  # HospitalListIndex
  SubmitHospitalListIndexReqDto:
    type: object
    properties:
      index_order:
        type: integer
        description: 医院列表位置
      description:
        type: string
        description: 描述
      rule_id:
        type: integer
        description: 规则id
        format: int64
      hospital_count:
        type: integer
        description: 该位置取的医院数量

  HospitalListIndexVo:
    type: object
    allOf:
      - $ref: "#/definitions/SubmitHospitalListIndexReqDto"
      - type: object
    properties:
      rule_name:
        type: string
        description: 规则名称
      create_time:
        description: 新增时间
        type: string
        format: date-time
      update_time:
        description: 更新时间
        type: string
        format: date-time

  # HospitalListGroup
  CreateHospitalListGroupReqDto:
    type: object
    properties:
      hospitals:
        type: array
        items:
          $ref: "#/definitions/GroupBindHospitalReqDto"
    allOf:
      - $ref: "#/definitions/UpdateHospitalListGroupReqDto"
      - type: object

  HospitalListGroupVo:
    type: object
    allOf:
      - $ref: "#/definitions/UpdateHospitalListGroupReqDto"
      - type: object
    properties:
      id:
        type: integer
        format: int64
      hospital_count:
        type: integer
        description: 绑定医院数量
      create_time:
        description: 新增时间
        type: string
        format: date-time
      update_time:
        description: 更新时间
        type: string
        format: date-time

  HospitalListGroupPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalListGroupVo"

  UpdateHospitalListGroupReqDto:
    type: object
    properties:
      name:
        type: string
        description: 医院组名称
      description:
        type: string
        description: 医院组说明

  GroupBindHospitalReqDto:
    type: object
    properties:
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      weight:
        type: integer
        format: int32
        description: 权重
      platform:
        type: array
        items:
          type: integer
          format: int32
        description: 平台,0微信,1支付宝
  HospitalListGroupDetailVo:
    allOf:
      - $ref: "#/definitions/HospitalListGroupVo"
    properties:
      hospitals:
        type: array
        items:
          type: object
          $ref: "#/definitions/GroupHospitalVo"

  GroupHospitalVo:
    allOf:
      - $ref: "#/definitions/HospitalVo"
    properties:
      weight:
        type: integer
        format: int32
        description: 权重
      display_id:
        type: integer
        format: int64
        description: 医院显示id
      platform:
        type: array
        items:
          type: integer
          format: int32
        description: 平台,0微信,1支付宝

  # HospitalListRule
  CreateHospitalListRuleReqDto:
    type: object
    allOf:
      - $ref: "#/definitions/UpdateHospitalListRuleReqDto"
      - type: object

  HospitalListRuleVo:
    type: object
    allOf:
      - $ref: "#/definitions/UpdateHospitalListRuleReqDto"
      - type: object
    properties:
      id:
        type: integer
        format: int64
      hospital_group_name:
        type: string
        description: 医院组名称
      create_time:
        description: 新增时间
        type: string
        format: date-time
      update_time:
        description: 更新时间
        type: string
        format: date-time

  HospitalListRulePageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalListRuleVo"

  UpdateHospitalListRuleReqDto:
    type: object
    properties:
      name:
        type: string
        description: 规则名称
      description:
        type: string
        description: 规则描述
      type:
        type: integer
        description: 1-固定医院  2-分组距离  3-分组权重  4-广告推荐  5-分组混合距离
      weight_order_by:
        type: integer
        description: 排序方式：1-从低到高 2-从高到低
      distance_order_by:
        type: integer
        description: 距离排序方式：1-从近到远 2-从远到近
      hospital_group_id:
        type: integer
        format: int64
        description: 分组距离模式下对应医院分组ID，当type值为2、3时必填
      advertisement_id:
        type: integer
        format: int64
        description: 广告推荐模式下对应广告id，当type值为4时必填
      advertisement_type:
        type: integer
        format: int32
        description: 广告推荐模式下对应广告类型，当type值为4时必填，1-推荐条件广告
      group_scale:
        type: array
        items:
          $ref: "#/definitions/GroupScaleDto"
        description: 分组混合距离模式下对应混合规则，当type为5时必填，{分组id:取的医院数量}
  GroupScaleDto:
    type: object
    properties:
      group_id:
        type: integer
        format: int64
        description: 分组id
      count:
        type: integer
        description: 该位置取的医院数量

  # HospitalListDisplay
  CreateHospitalListDisplayReqDto:
    type: object
    allOf:
      - $ref: "#/definitions/UpdateHospitalListDisplayReqDto"
      - type: object

  HospitalListDisplayVo:
    type: object
    allOf:
      - $ref: "#/definitions/UpdateHospitalListDisplayReqDto"
      - type: object
    properties:
      id:
        type: integer
        format: int64
      create_time:
        description: 新增时间
        type: string
        format: date-time
      update_time:
        description: 更新时间
        type: string
        format: date-time

  HospitalListDisplayPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalListDisplayVo"

  UpdateHospitalListDisplayReqDto:
    type: object
    properties:
      hospital_id:
        type: integer
        format: int64
        description: 医院 ID
      hospital_name_color_type:
        type: integer
        description: 医院名称颜色，0:默认，1:自定义
      hospital_name_hex_color_code:
        type: string
        description: 设置医院名称颜色，16进制颜色编码
      logo_type:
        type: integer
        description: 医院logo
      display_corner_mark:
        type: integer
        description: 显示角标，0:不显示，1:显示
      corner_mark_style:
        type: string
        description: 角标样式
      display_tag:
        type: integer
        description: 显示标签，0:不显示，1:显示
      tags:
        type: string
        description: 医院标签，通过逗号分隔
      recommended_doctor_id:
        type: integer
        format: int64
        description: 推荐的医生ID

  CreateHospitalAreaDetailPageConfigReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      display_hospital_area:
        type: boolean
        description: 是否显示院区：0，不显示；1，显示；
      display_address:
        type: boolean
        description: 是否显示地址：0，不显示；1，显示；
      display_level:
        type: boolean
        description: 是否显示等级：0，不显示；1，显示；
      display_open_scheduling_time:
        type: boolean
        description: 是否显示医院放号时间：0，不显示；1，显示；
      display_appoint_notice:
        type: boolean
        description: 是否显示预约须知：0，不显示；1，显示；
      display_notice:
        type: boolean
        description: 是否显示医院温馨提示：0，不显示；1，显示；
      display_floor_distribution:
        type: boolean
        description: 是否显示楼层分布：0，不显示；1，显示；
      display_tab:
        type: boolean
        description: 是否显示tab：0，不显示；1，显示；

  HospitalAreaDetailPageConfigVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      display_hospital_area:
        type: boolean
        description: 是否显示院区：0，不显示；1，显示；
      display_address:
        type: boolean
        description: 是否显示地址：0，不显示；1，显示；
      display_level:
        type: boolean
        description: 是否显示等级：0，不显示；1，显示；
      display_open_scheduling_time:
        type: boolean
        description: 是否显示医院放号时间：0，不显示；1，显示；
      display_appoint_notice:
        type: boolean
        description: 是否显示预约须知：0，不显示；1，显示；
      display_notice:
        type: boolean
        description: 是否显示医院温馨提示：0，不显示；1，显示；
      display_floor_distribution:
        type: boolean
        description: 是否显示楼层分布：0，不显示；1，显示；
      display_tab:
        type: boolean
        description: 是否显示tab：0，不显示；1，显示；
      create_time:
        description: 新增时间
        type: string
        format: date-time
      update_time:
        description: 更新时间
        type: string
        format: date-time

  SearchHospitalAreaDetailPageConfigReqDto:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      current_page:
        type: integer
        description: 当前页
        default: 1
      page_size:
        type: integer
        description: 每页条数
        default: 10

  UpdateHospitalAreaDetailPageConfigReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      display_hospital_area:
        type: boolean
        description: 是否显示院区：0，不显示；1，显示；
      display_address:
        type: boolean
        description: 是否显示地址：0，不显示；1，显示；
      display_level:
        type: boolean
        description: 是否显示等级：0，不显示；1，显示；
      display_open_scheduling_time:
        type: boolean
        description: 是否显示医院放号时间：0，不显示；1，显示；
      display_appoint_notice:
        type: boolean
        description: 是否显示预约须知：0，不显示；1，显示；
      display_notice:
        type: boolean
        description: 是否显示医院温馨提示：0，不显示；1，显示；
      display_floor_distribution:
        type: boolean
        description: 是否显示楼层分布：0，不显示；1，显示；
      display_tab:
        type: boolean
        description: 是否显示tab：0，不显示；1，显示；


  HospitalAreaDetailPageConfigPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalAreaDetailPageConfigVo"

  CreateHospitalAreaDetailPageSubNaviModuleReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
      - sub_navi_type
      - sub_navi_display_limit
      - sort
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      sub_navi_type:
        type: integer
        description: 副标题样式：0，固定；1，横向滑动；
        default: 0
      sub_navi_display_limit:
        type: integer
        description: 副标题单行显示上限
        default: 4
      sort:
        type: integer
        description: 排序
        default: 0
      channels:
        type: array
        items:
          type: integer
        description: 渠道

  UpdateHospitalAreaDetailPageSubNaviModuleReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      sub_navi_type:
        type: integer
        description: 副标题样式：0，固定；1，横向滑动；
      sub_navi_display_limit:
        type: integer
        description: 副标题单行显示上限
      sort:
        type: integer
        description: 排序
      channels:
        type: array
        items:
          type: integer
        description: 渠道

  HospitalAreaDetailPageSubNaviModuleVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      sub_navi_type:
        type: integer
        description: 副标题样式：0，固定；1，横向滑动；
      sub_navi_display_limit:
        type: integer
        description: 副标题单行显示上限
      sort:
        type: integer
        description: 排序
      sub_navigation_list:
        type: array
        items:
          $ref: "#/definitions/NaviVo"
      create_time:
        description: 新增时间
        type: string
        format: date-time
      update_time:
        description: 更新时间
        type: string
        format: date-time

  SearchHospitalAreaDetailPageSubNaviModuleReqDto:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      current_page:
        type: integer
        description: 当前页
        default: 1
      page_size:
        type: integer
        description: 每页条数
        default: 10

  HospitalAreaDetailPageSubNaviModulePageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalAreaDetailPageSubNaviModuleVo"

  CreateHospitalAreaDetailPageNavigatorReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      id:
        type: integer
        format: int64
        description: 模块ID
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      type:
        type: integer
        description: 导航栏类型，0:主图文导航，1:副图文导航
        default: 0
      channels:
        type: array
        items:
          type: integer
        description: 渠道
      navi_info:
        type: array
        items:
          type: object
          required:
            - title
            - sub_title
            - picture
            - url
            - sort
          properties:
            title:
              type: string
              description: 主标题
            sub_title:
              type: string
              description: 副标题，副导航栏无效
            picture:
              type: string
              description: 导航图片
            url:
              type: string
              description: 导航链接
            sort:
              type: integer
              description: 排序
              default: 0
            channels:
              type: array
              items:
                type: integer
              description: 渠道

  UpdateHospitalAreaDetailPageNavigatorReqDto:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 模块ID
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      type:
        type: integer
        description: 导航栏类型，0:主图文导航，1:副图文导航
        default: 0
      navi_info:
        type: array
        items:
          $ref: "#/definitions/NaviVo"

  HospitalAreaDetailPageNavigatorVo:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      type:
        type: integer
        description: 导航栏类型，0:主图文导航，1:副图文导航
      channels:
        type: array
        items:
          type: integer
        description: 渠道
      navi_info:
        type: array
        items:
          type: object
          properties:
            id:
              type: integer
              format: int64
            title:
              type: string
              description: 主标题
            sub_title:
              type: string
              description: 副标题，副导航栏无效
            picture:
              type: string
              description: 导航图片
            url:
              type: string
              description: 导航链接
            sort:
              type: integer
              description: 排序
              default: 0
            channels:
              type: array
              items:
                type: integer
              description: 渠道
            create_time:
              description: 新增时间
              type: string
              format: date-time
            update_time:
              description: 更新时间
              type: string
              format: date-time
  NaviVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
      title:
        type: string
        description: 主标题
      sub_title:
        type: string
        description: 副标题，副导航栏无效
      picture:
        type: string
        description: 导航图片
      url:
        type: string
        description: 导航链接
      sort:
        type: integer
        description: 排序
        default: 0
      channels:
        type: array
        items:
          type: integer
        description: 渠道

  SearchHospitalAreaDetailPageNavigatorReqDto:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      navi_module_id:
        type: integer
        format: int64
        description: 副标题模块ID，主标题默认为0
      type:
        type: integer
        description: 导航栏类型，0:主图文导航，1:副图文导航
      current_page:
        type: integer
        description: 当前页
        default: 1
      page_size:
        type: integer
        description: 每页条数
        default: 10

  HospitalAreaDetailPageNavigatorPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalAreaDetailPageNavigatorVo"

  CreateHospitalAreaDetailPageCubeModuleReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID

  UpdateHospitalAreaDetailPageCubeModuleReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      title:
        type: string
        description: 标题
      title_status:
        type: integer
        description: 标题状态：0，展示；1，禁用；
      cube_display_type:
        type: integer
        description: 魔方类型：0，一行两个；1，一行三个；2，二左二右；3，一左二右；4，一左三右
      sort:
        type: integer
        description: 排序
      cube_list:
        type: array
        items:
          type: integer
          format: int64

  HospitalAreaDetailPageCubeModuleVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      title:
        type: string
        description: 标题
      title_status:
        type: integer
        description: 标题状态：0，展示；1，禁用；
      cube_display_type:
        type: integer
        description: 魔方类型：0，一行两个；1，一行三个；2，二左二右；3，一左二右；4，一左三右
      sort:
        type: integer
        description: 排序
      create_time:
        description: 新增时间
        type: string
        format: date-time
      update_time:
        description: 更新时间
        type: string
        format: date-time
      cube_vo:
        type: array
        items:
          $ref: "#/definitions/HospitalAreaDetailPageCubeVo"

  SearchHospitalAreaDetailPageCubeModuleReqDto:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      cube_display_type:
        type: integer
        description: 魔方类型：0，一行两个；1，一行三个；2，二左二右；3，一左二右；4，一左三右
      current_page:
        type: integer
        description: 当前页
        default: 1
      page_size:
        type: integer
        description: 每页条数
        default: 10

  HospitalAreaDetailPageCubeModulePageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalAreaDetailPageCubeModuleVo"

  CreateHospitalAreaDetailPageCubeReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
      - title
      - picture
      - url
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      cube_module_id:
        type: integer
        format: int64
        description: 魔方模块ID
      title:
        type: string
        description: 标题
      picture:
        type: string
        description: 图片
      url:
        type: string
        description: 链接
      sort:
        type: integer
        description: 排序
        default: 0
      channels:
        type: array
        items:
          type: integer
        description: 渠道
      status:
        type: integer
        description: 状态：0，启动；1，禁用；
        default: 0

  UpdateHospitalAreaDetailPageCubeReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      cube_module_id:
        type: integer
        format: int64
        description: 魔方模块ID
      title:
        type: string
        description: 标题
      picture:
        type: string
        description: 图片
      url:
        type: string
        description: 链接
      sort:
        type: integer
        description: 排序
      channels:
        type: array
        items:
          type: integer
        description: 渠道
      status:
        type: integer
        description: 状态：0，启动；1，禁用；
        default: 0

  HospitalAreaDetailPageCubeVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      cube_module_id:
        type: integer
        format: int64
        description: 魔方模块ID
      title:
        type: string
        description: 标题
      picture:
        type: string
        description: 图片
      url:
        type: string
        description: 链接
      sort:
        type: integer
        description: 排序
      channels:
        type: array
        items:
          type: integer
        description: 渠道
      status:
        type: integer
        description: 状态：0，启动；1，禁用；
        default: 0
      create_time:
        description: 新增时间
        type: string
        format: date-time
      update_time:
        description: 更新时间
        type: string
        format: date-time

  SearchHospitalAreaDetailPageCubeReqDto:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      cube_module_id:
        type: integer
        format: int64
        description: 魔方模块ID
      title:
        type: string
        description: 标题
      current_page:
        type: integer
        description: 当前页
        default: 1
      page_size:
        type: integer
        description: 每页条数
        default: 10


  HospitalAreaDetailPageCubePageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalAreaDetailPageCubeVo"

  CreateHospitalAreaDetailPageTabReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
      - title
      - sort
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      title:
        type: string
        description: 标题
      component_type:
        type: integer
        description: 组件类型
      recommend_id:
        type: integer
        format: int64
        description: 推荐ID
      sort:
        type: integer
        description: 排序
        default: 0
      channels:
        type: array
        items:
          type: integer
        description: 渠道
      status:
        type: integer
        description: 状态：0，启动；1，禁用；
        default: 0

  UpdateHospitalAreaDetailPageTabReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      title:
        type: string
        description: 标题
      component_type:
        type: integer
        description: 组件类型
      recommend_id:
        type: integer
        format: int64
        description: 推荐ID
      sort:
        type: integer
        description: 排序
      channels:
        type: array
        items:
          type: integer
        description: 渠道
      status:
        type: integer
        description: 状态：0，启动；1，禁用；
        default: 0

  HospitalAreaDetailPageTabVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      title:
        type: string
        description: 标题
      component_type:
        type: integer
        description: 组件类型
      recommend_id:
        type: integer
        format: int64
        description: 推荐ID
      sort:
        type: integer
        description: 排序
      channels:
        type: array
        items:
          type: integer
        description: 渠道
      status:
        type: integer
        description: 状态：0，启动；1，禁用；
        default: 0
      create_time:
        description: 新增时间
        type: string
        format: date-time
      update_time:
        description: 更新时间
        type: string
        format: date-time

  SearchHospitalAreaDetailPageTabReqDto:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      title:
        type: string
        description: 标题
      component_type:
        type: integer
        description: 组件类型
      current_page:
        type: integer
        description: 当前页
        default: 1
      page_size:
        type: integer
        description: 每页条数
        default: 10

  HospitalAreaDetailPageTabPageVo:
    type: object
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalAreaDetailPageTabVo"

  HospitalAreaLayoutVo:
    type: object
    properties:
      map_keyword:
        type: string
        description: 地图关键字
      page_config:
        $ref: "#/definitions/HospitalAreaDetailPageConfigVo"
      navigation_vo:
        $ref: "#/definitions/HospitalAreaDetailPageNavigatorVo"
      sub_navigation_vo:
        type: array
        items:
          description: 医院详情页子导航模块
          type: object
          $ref: "#/definitions/HospitalAreaDetailPageSubNaviModuleVo"
      cube_module_vo:
        type: array
        items:
          description: 医院详情页魔方模块
          $ref: "#/definitions/HospitalAreaDetailPageCubeModuleVo"
      tab_vo:
        type: array
        items:
          description: 医院详情页Tab模块
          $ref: "#/definitions/HospitalAreaDetailPageTabVo"
      hospital_area_id:
        type: integer
        format: int64
        description: 院区ID
      hospital_area_code:
        type: string
        description: 院区编码
      hospital_name:
        type: string
        description: 医院名称
      hospital_area_name:
        type: string
        description: 医院院区名称
      hospital_area_image:
        type: string
        description: 医院院区图片
      hospital_areas:
        type: array
        description: 医院院区集合
        items:
          type: object
          properties:
            hospital_area_id:
              type: integer
              format: int64
              description: 医院院区ID
            hospital_area_name:
              type: string
              description: 医院院区名称
      hospital_area_address:
        $ref: "#/definitions/AddressVo"
      appointment_schedule_time:
        type: string
        description: 放号时间
      appointment_rule:
        type: string
        description: 预约规则
      introduction:
        type: string
        description: 医院介绍
      announcement:
        type: string
        description: 公告，又名：温馨提示
      hospital_area_level:
        type: string
        description: 医院院区等级
      hospital_area_phone:
        type: string
        description: 医院电话

  DepartmentsTreeSearchReqDto:
    type: object
    properties:
      hospital_area_id:
        type: integer
        format: int64
        description: 医院院区ID
      department_name:
        type: string
        description: 科室名称
      thrdpart_dep_code:
        type: string
        description: 科室编码
      category:
        description: 科室类型
        type: array
        items:
          type: integer
          format: int32
      enabled:
        type: boolean
        description: 是否启用

  DepartmentsTreeSearchVo:
    type: object
    allOf:
      - $ref: "#/definitions/DepartmentVo"
      - type: object
        properties:
          childrens:
            type: array
            items:
              $ref: "#/definitions/DepartmentsTreeSearchVo"

  DepartmentsTreeSearchListVo:
    type: object
    properties:
      list:
        type: array
        items:
          $ref: "#/definitions/DepartmentsTreeSearchVo"
      level_count:
        type: integer
        description: 科室层级数


  DictInfoKafkaVo:
    type: object
    properties:
      dict_types:
        type: array
        items:
          $ref: "#/definitions/DictTypeVo"
      dict_labels:
        type: array
        items:
          $ref: "#/definitions/DictLabelVo"

  HospitalAreaQueryLabelsReqDto:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_name:
        type: string
        description: 医院名称
      category:
        type: array
        items:
          type: integer
        description: 院区类型
  HospitalAreaQueryLabelsVo:
    type: object
    properties:
      hospital_list:
        type: array
        items:
          $ref: "#/definitions/HospitalAreaQueryLabelsHospitalVo"
  HospitalAreaQueryLabelsHospitalVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 医院ID
      display_name:
        type: string
        description: 医院名称
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_area_list:
        type: array
        items:
          $ref: "#/definitions/HospitalAreaQueryLabelsHospitalAreaVo"
  HospitalAreaQueryLabelsHospitalAreaVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 院区ID
      name:
        type: string
        description: 院区名称
      display_name:
        type: string
        description: 标签展示名称
      hospital_area_code:
        type: string
        description: 院区编码
      tenant_id:
        type: integer
        format: int64
        description: 租户ID
      hospital_id:
        type: integer
        format: int64
        description: 医院ID
  QueryCustomerHospitalPageReqDto:
    type: object
    properties:
      current:
        type: integer
        format: int32
        default: 1
        description: 当前页
      size:
        type: integer
        format: int32
        default: 10
        description: 每页显示条数
      platform:
        type: integer
        format: int32
        description: 平台 0:微信 1:支付宝
        default: 0
      name:
        type: string
        description: 医院名称
      province:
        type: string
        description: 省
      city:
        type: string
        description: 市
      county:
        type: string
        description: 区
      level:
        type: string
        description: 等级
      sort_type:
        type: integer
        format: int32
        default: 0
        description: 0:综合排序 1:距离排序 2:等级排序
      sort:
        type: integer
        enum:
          - 0
          - 1
        description: 0:升序 1:降序
      longitude:
        type: number
        format: double
        description: 经度
      latitude:
        type: number
        format: double
        description: 纬度
      user_id:
        type: integer
        format: int64
        description: 用户id

  CustomerHospitalPageVo:
    type: object
    allOf:
      - $ref: '#/definitions/BasePage'
      - type: object
        properties:
          records:
            type: array
            items:
              $ref: '#/definitions/CustomerHospitalVo'
  CustomerHospitalVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 医院id
      name:
        type: string
        description: 医院名称
      logo:
        type: string
        description: 医院logo
      distance:
        type: number
        format: double
        description: 距离
      region:
        type: string
        description: 区域
      address_id:
        type: integer
        format: int64
        description: 医院地址id
      status:
        type: integer
        format: int32
        description: 状态 0:启用，1:开发中，2:维护，3:禁用
      is_advertisement:
        type: boolean
        description: 是否是广告
        default: false
      adv_referer:
        type: string
        description: 广告referer
      resource_url:
        type: string
        description: 资源url
      condition_expression:
        type: string
        description: 条件表达式
      redirect_url:
        type: string
        description: 跳转url
      adv_tags:
        type: array
        items:
          type: string
        description: 广告标签
      adv_position:
        type: integer
        description: 广告位置
      adv_remark:
        type: string
        description: 广告备注
      display:
        $ref: '#/definitions/CustomerHospitalListDisplayVo'
      hospital_area_list:
        type: array
        items:
          properties:
            id:
              type: integer
              format: int64
              description: 医院院区id
            code:
              type: string
              description: 医院院区编码
            status:
              type: integer
              format: int32
              description: 状态 0:启用，1:开发中，2:维护，3:禁用
  CustomerHospitalListDisplayVo:
    type: object
    description: 医院列表展示
    properties:
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_name_color_type:
        type: integer
        description: 医院名称颜色，0:默认，1:自定义
      hospital_name_hex_color_code:
        type: string
        description: 医院名称16进制颜色码
      logo_type:
        type: integer
        enum:
          - 0
          - 1
        description: logo类型 0:默认 1:自定义
      display_corner_mark:
        type: integer
        enum:
          - 0
          - 1
        description: 是否显示角标 0:不显示 1:显示
      corner_mark_style:
        type: string
        description: 角标样式
      display_tag:
        type: integer
        enum:
          - 0
          - 1
        description: 是否显示标签 0:不显示 1:显示
      tags:
        type: string
        description: 标签
      recommended_doctors:
        type: array
        items:
          $ref: '#/definitions/RecommendedDoctorVo'
        description: 推荐医生
      is_last_hospital:
        type: integer
        format: int32
        default: 0
        description: 是否是上次挂号的医院 0:否 1:是
  RecommendedDoctorVo:
    type: object
    properties:
      name:
        type: string
        description: 医生姓名
      avatar:
        type: string
        description: 医生头像
      rank:
        type: string
        description: 医生职称
      description:
        type: string
        description: 医生简介
      speciality:
        type: string
        description: 医生擅长
      department_name:
        type: string
        description: 科室名称

  # vaccine
  VaccineCategoryVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 苗种id
      name:
        type: string
        description: 苗种名称
      sort:
        type: integer
        format: int32
        description: 排序
      logo:
        type: string
        description: 苗种图标地址
      is_hot:
        type: integer
        format: int32
        description: 是否热门 0 - 否 1 - 是
      hot_sort:
        type: integer
        format: int32
        description: 热门排序
      description:
        type: string
        description: 苗种介绍说明
      tips:
        type: string
        description: 接种须知
      status:
        type: integer
        format: int32
        description: 状态 0 - 正常 1- 禁用
      create_time:
        type: string
        format: date-time
        description: 新增时间
      update_time:
        type: string
        format: date-time
        description: 更新时间
  CreateVaccineCategoryReqDto:
    type: object
    properties:
      name:
        type: string
        description: 苗种名称
      sort:
        type: integer
        format: int32
        description: 排序
      logo:
        type: string
        description: 苗种图标地址
      is_hot:
        type: integer
        format: int32
        description: 是否热门 0 - 否 1 - 是
      hot_sort:
        type: integer
        format: int32
        description: 热门排序
      description:
        type: string
        description: 苗种介绍说明
      tips:
        type: string
        description: 接种须知
      status:
        type: integer
        format: int32
        description: 状态 0 - 正常 1- 禁用
  UpdateVaccineCategoryReqDto:
    type: object
    allOf:
      - $ref: '#/definitions/CreateVaccineCategoryReqDto'
  GetVaccineCategoryPageReqDto:
    type: object
    properties:
      current:
        type: integer
        format: int32
        default: 1
        description: 当前页
      size:
        type: integer
        format: int32
        default: 10
        description: 每页显示条数
      name:
        type: string
        description: 苗种名称
      status:
        type: integer
        format: int32
        description: 状态 0 - 正常 1- 禁用
      is_hot:
        type: integer
        format: int32
        description: 是否热门 0 - 否 1 - 是
  VaccineCategoryPageVo:
    type: object
    allOf:
      - $ref: '#/definitions/BasePage'
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: '#/definitions/VaccineCategoryVo'
  VaccineVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 疫苗id
      vaccine_category_id:
        type: integer
        format: int64
        description: 苗种id
      vaccine_category_name:
        type: string
        description: 苗种名称
      tenant_id:
        type: integer
        format: int64
        description: 租户id
      tenant_name:
        type: string
        description: 租户名称
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_name:
        type: string
        description: 医院名称
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      hospital_area_name:
        type: string
        description: 院区名称
      department_id:
        type: integer
        format: int64
        description: 科室id
      department_name:
        type: string
        description: 科室名称
      doctor_id:
        type: integer
        format: int64
        description: 医生id
      doctor_name:
        type: string
        description: 医生名称
      sort:
        type: integer
        format: int32
        description: 排序
      tips:
        type: string
        description: 预约提示(弹窗等)
      remark:
        type: string
        description: 其他说明(放号时间等)
      status:
        type: integer
        format: int32
        description: 状态 0-正常，1-禁用
      create_time:
        type: string
        format: date-time
        description: 新增时间
      update_time:
        type: string
        format: date-time
        description: 更新时间
  CreateVaccineReqDto:
    type: object
    properties:
      vaccine_category_id:
        type: integer
        format: int64
        description: 苗种id
      doctor_id:
        type: integer
        format: int64
        description: 医生id
      sort:
        type: integer
        format: int32
        description: 排序
      tips:
        type: string
        description: 预约提示(弹窗等)
      remark:
        type: string
        description: 其他说明(放号时间等)
  UpdateVaccineReqDto:
    type: object
    allOf:
      - $ref: '#/definitions/CreateVaccineReqDto'
  GetVaccinePageReqDto:
    type: object
    properties:
      current:
        type: integer
        format: int32
        default: 1
        description: 当前页
      size:
        type: integer
        format: int32
        default: 10
        description: 每页显示条数
      vaccine_id:
        type: integer
        format: int64
        description: 疫苗id
      vaccine_category_id:
        type: integer
        format: int64
        description: 苗种id
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      department_id:
        type: integer
        format: int64
        description: 科室id
      doctor_id:
        type: integer
        format: int64
        description: 医生id
      doctor_name:
        type: string
        description: 医生名称
      status:
        type: integer
        format: int32
        description: 状态 0-正常，1-禁用
  VaccinePageVo:
    type: object
    allOf:
      - $ref: '#/definitions/BasePage'
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: '#/definitions/VaccineVo'

  # recommend-config
  RecommendConfigVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 主键id
      biz_type:
        type: string
        description: 业务类型，例如：专病专诊、特色中医，来自字典配置
      biz_type_desc:
        type: string
        description: 业务类型描述
      data_type:
        type: string
        description: 数据类型，例如：医院、科室、医生，来自字典配置
      data_type_desc:
        type: string
        description: 数据类型描述
      data_tag:
        type: string
        description: 数据标签，当在相同的 biz_type、data_type 下，通过 data_tag，可以同一 data_type 设置多次
      data_id:
        type: string
        description: 数据全局唯一标识
      data_name:
        type: string
        description: 数据名称
      redirect_url:
        type: string
        description: 跳转链接
      sort:
        type: integer
        format: int32
        description: 排序，由大到小
      enabled:
        type: integer
        format: int32
        description: 已启用
      display_tags:
        type: array
        items:
          type: string
        description: 展示标签
      img_url:
        type: string
        description: 图片地址
      reserved_json:
        type: string
        description: 预留字段, json 格式
      create_time:
        type: string
        format: date-time
        description: 新增时间
      update_time:
        type: string
        format: date-time
        description: 更新时间
  CreateRecommendConfigReqDto:
    type: object
    properties:
      biz_type:
        type: string
        description: 业务类型，例如：专病专诊、特色中医，来自字典配置
      data_type:
        type: string
        description: 数据类型，例如：医院、科室、医生，来自字典配置
      data_tag:
        type: string
        description: 数据标签，当在相同的 biz_type、data_type 下，通过 data_tag，可以同一 data_type 设置多次
      data_id:
        type: string
        description: 数据全局唯一标识
      redirect_url:
        type: string
        description: 跳转链接
      sort:
        type: integer
        format: int32
        description: 排序，由大到小
      display_tags:
        type: array
        items:
          type: string
        description: 展示标签
      img_url:
        type: string
        description: 图片地址
      enabled:
        type: integer
        format: int32
        description: 已启用
  UpdateRecommendConfigReqDto:
    type: object
    allOf:
      - $ref: '#/definitions/CreateRecommendConfigReqDto'
  GetRecommendConfigPageReqDto:
    type: object
    properties:
      current:
        type: integer
        format: int32
        default: 1
        description: 当前页
      size:
        type: integer
        format: int32
        default: 10
        description: 每页显示条数
      biz_type:
        type: string
        description: 业务类型，例如：专病专诊、特色中医，来自字典配置
      data_type:
        type: string
        description: 数据类型，例如：医院、科室、医生，来自字典配置
      data_tag:
        type: string
        description: 数据标签，当在相同的 biz_type、data_type 下，通过 data_tag，可以同一 data_type 设置多次
      data_id:
        type: string
        description: 数据全局唯一标识
      enabled:
        type: integer
        format: int32
        description: 已启用
  RecommendConfigPageVo:
    type: object
    allOf:
      - $ref: '#/definitions/BasePage'
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: '#/definitions/RecommendConfigVo'

  GetDoctorGroupPageReqDto:
    type: object
    properties:
      current:
        type: integer
        description: 当前页
        default: 1
      size:
        type: integer
        description: 页大小
        default: 10
      ids:
        type: array
        items:
          type: integer
          format: int64
          description: 医生组id
      name:
        type: string
        description: 医生组名称
      hospitalCodes:
        type: array
        items:
          type: string
          description: 医院编码
      departmentCodes:
        type: array
        items:
          type: string
          description: 科室编码
      doctorCodes:
        type: array
        items:
          type: string
          description: 医生编码
  DoctorGroupPageRespVo:
    type: object
    properties:
      current:
        type: integer
        format: int32
        description: 当前页
      size:
        type: integer
        format: int32
        description: 页大小
      total:
        type: integer
        format: int64
        description: 总条数
      list:
        type: array
        items:
          $ref: "#/definitions/DoctorGroupItemRespVo"
  DoctorGroupItemRespVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 医生id
      name:
        type: string
        description: 医生名称
      title:
        type: string
        description: 职称
      display_name:
        type: string
        description: 展示名称
      hospital_code:
        type: string
        description: 医院编码
      hospital_name:
        type: string
        description: 医院名称
      department_code:
        type: string
        description: 科室编码
      department_name:
        type: string
        description: 科室名称
      doctor_code:
        type: string
        description: 医生编码

  GetExtensionConditionAdvertisementReqDto:
    type: object
    properties:
      is_show:
        type: integer
        format: int32
        description: 是否展示 0-否 1-是
      enable:
        type: boolean
        description: 是否启用
  GetExtensionConditionAdvertisementRespVo:
    type: object
    properties:
      list:
        type: array
        items:
          $ref: "#/definitions/GetExtensionConditionAdvertisementItemVo"
  GetExtensionConditionAdvertisementItemVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 主键id
      extension_condition_name:
        type: string
        description: 推广条件名称
      advertisement_resource_name:
        type: string
        description: 广告资源名称

  DoctorBindGroupReqDto:
    type: object
    required:
      - doctor_id
      - doctor_group_source
      - doctor_group_id
    properties:
      doctor_id:
        type: integer
        format: int64
        description: 医生id
      doctor_group_source:
        type: integer
        format: int32
        description: 医生团队来源:0,在线问诊;
      doctor_group_id:
        type: integer
        format: int64
        description: 医生团队id
      doctor_group_name:
        type: string
        description: 医生团队名称
  DoctorBatchBindGroupReqDto:
    type: object
    required:
      - doctor_ids
      - doctor_group_source
      - doctor_group_id
    properties:
      doctor_ids:
        type: array
        items:
          type: integer
          format: int64
        description: 医生id
      doctor_group_source:
        type: integer
        format: int32
        description: 医生团队来源:0,在线问诊;
      doctor_group_id:
        type: integer
        format: int64
        description: 医生团队id
      doctor_group_name:
        type: string
        description: 医生团队名称
  DoctorUnbindGroupReqDto:
    type: object
    properties:
      relation_id:
        type: integer
        format: int64
        description: 医生团队关系id
      doctor_id:
        type: integer
        format: int64
        description: 医生id
      doctor_group_source:
        type: integer
        format: int32
        description: 医生团队来源:0,在线问诊;
      doctor_group_id:
        type: integer
        format: int64
        description: 医生团队id
  DoctorGroupRelationQueryReqDto:
    type: object
    required:
      - current_page
      - page_size
    properties:
      current_page:
        type: integer
        description: 当前页
        default: 1
      page_size:
        type: integer
        description: 每页条数
        default: 10
      doctor_id:
        type: integer
        format: int64
        description: 医生id
      doctor_name:
        type: string
        description: 医生名称
      doctor_group_source:
        type: integer
        format: int32
        description: 医生团队来源:0,在线问诊;
      doctor_group_id:
        type: integer
        format: int64
        description: 医生团队id
      doctor_group_name:
        type: string
        description: 医生团队名称
  DoctorGroupRelationQueryRespDto:
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/DoctorGroupRelationVo"
  DoctorGroupRelationVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 医生团队关系id
      doctor_id:
        type: integer
        format: int64
        description: 医生id
      doctor_name:
        type: string
        description: 医生名称
      doctor_code:
        type: string
        description: 医生编码
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_name:
        type: string
        description: 医院名称
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      hospital_area_name:
        type: string
        description: 院区名称
      department_id:
        type: integer
        format: int64
        description: 科室id
      department_name:
        type: string
        description: 科室名称
      status:
        type: integer
        format: int32
        description: 状态 0-正常，1-禁用
      doctor_group_source:
        type: integer
        format: int32
        description: 医生团队来源:0,在线问诊;
      doctor_group_id:
        type: integer
        format: int64
        description: 医生团队id
      doctor_group_name:
        type: string
        description: 医生团队名称

  DictFileCreateReqDto:
    type: object
    required:
      - name
      - type
      - words
    properties:
      name:
        type: string
        description: 字典名称
      type:
        type: integer
        description: 字典类型:0,关键词;1,屏蔽词;
      remark:
        type: string
        description: 备注
      words:
        type: array
        items:
          type: string
          description: 关键字
        description: 关键字集合
  DictFileUpdateReqDto:
    type: object
    required:
      - id
    properties:
      id:
        type: integer
        format: int64
        description: 字典id
      name:
        type: string
        description: 字典名称
      type:
        type: integer
        description: 字典类型:0,关键词;1,屏蔽词;
      words:
        type: array
        items:
          type: string
          description: 关键字
        description: 关键字集合
      remark:
        type: string
        description: 备注
      status:
        type: integer
        description: 状态:0,启用;1,禁用;
  DictFileQueryReqDto:
    type: object
    properties:
      current_page:
        type: integer
        description: 当前页
        default: 1
      page_size:
        type: integer
        description: 每页条数
        default: 10
      name:
        type: string
        description: 字典名称
      type:
        type: integer
        description: 字典类型:0,关键词;1,屏蔽词;
      status:
        type: integer
        description: 状态:0,启用;1,禁用;
  DictFileQueryRespDto:
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/DictFileVo"
  DictFileVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 字典id
      name:
        type: string
        description: 字典名称
      type:
        type: integer
        description: 字典类型:0,关键词;1,屏蔽词;
      words:
        type: array
        items:
          type: string
          description: 关键字
        description: 关键字集合
      remark:
        type: string
        description: 备注
      status:
        type: integer
        description: 状态:0,启用;1,禁用;
      cluster_status:
        type: integer
        description: 集群状态:0,未同步;1,已同步;
      create_time:
        type: string
        format: date-time
        description: 新增时间
      update_time:
        type: string
        format: date-time
        description: 更新时间
      sync_time:
        type: string
        format: date-time
        description: 同步时间

  LocationCreateReqDto:
    type: object
    required:
      - tenant_id
      - hospital_id
      - hospital_area_id
      - type
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户id
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      building_id:
        type: integer
        format: int64
        description: 大楼id
      floor_id:
        type: integer
        format: int64
        description: 楼层id
      area_id:
        type: integer
        format: int64
        description: 区域id
      type:
        type: integer
        description: 位置类型:0,科室;9,自定义;
        minimum: 0
        maximum: 9
      name:
        type: string
        description: 位置名称
      department_id:
        type: integer
        format: int64
        description: 科室id
      sort:
        type: integer
      status:
        type: integer
        format: int32
        description: 状态 0-启用，1-禁用
  LocationUpdateReqDto:
    type: object
    properties:
      tenant_id:
        type: integer
        format: int64
        description: 租户id
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      building_id:
        type: integer
        format: int64
        description: 大楼id
      floor_id:
        type: integer
        format: int64
        description: 楼层id
      area_id:
        type: integer
        format: int64
        description: 区域id
      type:
        type: integer
        description: 位置类型:0,科室;9,自定义;
        minimum: 0
        maximum: 9
      name:
        type: string
        description: 位置名称
      department_id:
        type: integer
        format: int64
        description: 科室id
      sort:
        type: integer
      status:
        type: integer
        format: int32
        description: 状态 0-启用，1-禁用
  HospitalAreaPositionTreeQueryReqDto:
    type: object
    required:
      - hospital_area_id
    properties:
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      building_id:
        type: integer
        format: int64
        description: 大楼id
      floor_id:
        type: integer
        format: int64
        description: 楼层id
      area_id:
        type: integer
        format: int64
        description: 区域id
      department_name:
        type: string
        description: 科室名称
  HospitalAreaPositionQueryReqDto:
    type: object
    properties:
      current_page:
        type: integer
        description: 当前页
        default: 1
      page_size:
        type: integer
        description: 每页条数
        default: 10
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      building_id:
        type: integer
        format: int64
        description: 大楼id
      floor_id:
        type: integer
        format: int64
        description: 楼层id
      area_id:
        type: integer
        format: int64
        description: 区域id
      type:
        type: integer
        description: 位置类型:0,科室;9,自定义;
        minimum: 0
        maximum: 9
      name:
        type: string
        description: 名称
  HospitalAreaPositionQueryRespDto:
    allOf:
      - $ref: "#/definitions/BasePage"
      - type: object
        properties:
          list:
            type: array
            items:
              $ref: "#/definitions/HospitalAreaPositionVo"
  HospitalAreaPositionTreeQueryRespDto:
    type: object
    properties:
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      children:
        type: array
        items:
          $ref: "#/definitions/BuildingPositionVo"
  BuildingPositionVo:
    type: object
    properties:
      building_id:
        type: integer
        format: int64
        description: 大楼id
      building_name:
        type: string
        description: 大楼名称
      status:
        type: integer
        format: int32
        description: 状态 0-启用，1-禁用
      create_time:
        type: string
        format: date-time
        description: 新增时间
      update_time:
        type: string
        format: date-time
        description: 更新时间
      sort:
        type: integer
      location_list:
        type: array
        items:
          $ref: "#/definitions/HospitalAreaPositionVo"
      children:
        type: array
        items:
          $ref: "#/definitions/FloorPositionVo"
  FloorPositionVo:
    type: object
    properties:
      building_id:
        type: integer
        format: int64
        description: 大楼id
      building_name:
        type: string
        description: 大楼名称
      floor_id:
        type: integer
        format: int64
        description: 楼层id
      floor_name:
        type: string
        description: 楼层名称
      status:
        type: integer
        format: int32
        description: 状态 0-启用，1-禁用
      create_time:
        type: string
        format: date-time
        description: 新增时间
      update_time:
        type: string
        format: date-time
        description: 更新时间
      sort:
        type: integer
      location_list:
        type: array
        items:
          $ref: "#/definitions/HospitalAreaPositionVo"
      children:
        type: array
        items:
          $ref: "#/definitions/AreaPositionVo"
  AreaPositionVo:
    type: object
    properties:
      building_id:
        type: integer
        format: int64
        description: 大楼id
      building_name:
        type: string
        description: 大楼名称
      floor_id:
        type: integer
        format: int64
        description: 楼层id
      floor_name:
        type: string
        description: 楼层名称
      area_id:
        type: integer
        format: int64
        description: 区域id
      area_name:
        type: string
        description: 区域名称
      status:
        type: integer
        format: int32
        description: 状态 0-启用，1-禁用
      sort:
        type: integer
      create_time:
        type: string
        format: date-time
        description: 新增时间
      update_time:
        type: string
        format: date-time
        description: 更新时间
      location_list:
        type: array
        items:
          $ref: "#/definitions/HospitalAreaPositionVo"
  HospitalAreaPositionVo:
    type: object
    properties:
      id:
        type: integer
        format: int64
        description: 绑定关系id
      tenant_id:
        type: integer
        format: int64
        description: 租户id
      hospital_id:
        type: integer
        format: int64
        description: 医院id
      hospital_area_id:
        type: integer
        format: int64
        description: 院区id
      type:
        type: integer
        description: 位置类型:0,科室;9,自定义;
        minimum: 0
        maximum: 9
      name:
        type: string
        description: 名称
      department_id:
        type: integer
        format: int64
        description: 科室id
      building_id:
        type: integer
        format: int64
        description: 大楼id
      building_name:
        type: string
        description: 大楼名称
      floor_id:
        type: integer
        format: int64
        description: 楼层id
      floor_name:
        type: string
        description: 楼层名称
      area_id:
        type: integer
        format: int64
        description: 区域id
      area_name:
        type: string
        description: 区域名称
      sort:
        type: integer
      status:
        type: integer
        format: int32
        description: 状态 0-启用，1-禁用
      create_time:
        type: string
        format: date-time
        description: 新增时间
      update_time:
        type: string
        format: date-time
        description: 更新时间

  TenantUserModifyReqDto:
    type: object
    properties:
      nickname:
        type: string
        description: 昵称
      phone_number:
        type: string
        description: 手机号
      head_image_url:
        type: string
        description: 头像
        minLength: 1
        maxLength: 300
      password:
        type: string
        description: 密码

  HospitalDependOnHisResponse:
    type: object
    properties:
      hospital_id:
        type: Long
        description: 医院id
      hospital_area_id:
        type: Long
        description: 院区id
      hospital_name:
        type: string
        description: 医院名称
      hospital_code:
        type: string
        description: 医院编码
  OrderInfoVO:
    type: object
    properties:
      order_id:
        type: integer
        format: int64
        description: 订单id
      order_no:
        type: string
        description: 订单号
      hospital_area_name:
        type: string
        description: 医院院区名称
      hospital_code:
        type: string
        description: 医院编码
      jz_card:
        type: string
        description: 就诊卡号
      patient_name:
        type: string
        description: 患者姓名
      patient_sex:
        type: integer
        description: 患者性别
      patient_birthday:
        type: string
        description: 患者生日
      patient_id:
        type: integer
        format: int64
        description: 患者id
