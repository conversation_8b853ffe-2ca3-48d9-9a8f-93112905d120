package backend.common.domain.payment.constant;

import org.springframework.lang.Nullable;

/**
 * <AUTHOR>
 * @since 2023/3/14 16:04:34
 */
public enum PaymentChannel {

    WECHAT_PAY_DIRECT("微信直连商户支付", 0, "微信支付"),
    WECHAT_PAY_SP("微信服务商支付", 1, "微信支付"),
    WECHAT_YIBAO_PAY_DIRECT("微信医保直连商户支付", 2, "微信医保支付"),
    WECHAT_YIBAO_PAY_SP("微信医保服务商支付", 3, "微信医保支付"),
    CCB_PAY("建行支付", 4, "微信支付"),

    WECHAT_PAY_SFT("微信收付通", 5, "微信收付通"),

    ALIPAY_DIRECT("支付宝直连商户支付", 6, "支付宝直连商户支付"),

    ALIPAY_ZFT("支付宝直付通", 7, "支付宝直付通"),

    DZ_KUNHUA("昆华东软支付", 8, "微信支付"),
    SHEN_SI_PAY("神思支付", 9, "微信支付"),
    WEIMAI_PAY_H5("微脉H5支付", 10, "微信支付"),
    WEIMAI_PAY("微脉原生支付(小程序收银台)", 11, "微信支付"),
    ;

    private final String name;
    private final Integer code;
    private final String group;


    PaymentChannel(String name, Integer code, String group) {
        this.name = name;
        this.code = code;
        this.group = group;
    }

    public static boolean contains(Integer payChannel) {
        for (PaymentChannel c : PaymentChannel.values()) {
            if (c.code.equals(payChannel)) {
                return true;
            }
        }
        return false;
    }

    public static @Nullable String getName(Integer code) {
        for (PaymentChannel c : PaymentChannel.values()) {
            if (c.code.equals(code)) {
                return c.name;
            }
        }
        return null;
    }

    public static @Nullable Integer getCode(String name) {
        for (PaymentChannel c : PaymentChannel.values()) {
            if (c.name.equals(name)) {
                return c.code;
            }
        }
        return null;
    }

    public static @Nullable PaymentChannel getPaymentChannel(Integer payChannel) {
        for (PaymentChannel c : PaymentChannel.values()) {
            if (c.code.equals(payChannel)) {
                return c;
            }
        }
        return null;
    }

    public static @Nullable PaymentChannel getPaymentChannel(String name) {
        for (PaymentChannel c : PaymentChannel.values()) {
            if (c.name.equals(name)) {
                return c;
            }
        }
        return null;
    }

    public static @Nullable String getGroup(Integer payChannel) {
        for (PaymentChannel c : PaymentChannel.values()) {
            if (c.code.equals(payChannel)) {
                return c.group;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getGroup() {
        return group;
    }
}
