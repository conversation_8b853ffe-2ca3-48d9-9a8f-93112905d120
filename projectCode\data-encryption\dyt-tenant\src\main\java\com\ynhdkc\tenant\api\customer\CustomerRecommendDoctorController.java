package com.ynhdkc.tenant.api.customer;

import backend.common.kafka.KafkaPublisher;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ynhdkc.tenant.entity.RecommendConfig;
import com.ynhdkc.tenant.handler.CustomerSourceRecommendConfigApi;
import com.ynhdkc.tenant.model.DictLabelVo;
import com.ynhdkc.tenant.model.DoctorDetailVo;
import com.ynhdkc.tenant.model.RecommendConfigDoctorDto;
import com.ynhdkc.tenant.service.backend.DictLabelService;
import com.ynhdkc.tenant.service.backend.IDoctorService;
import com.ynhdkc.tenant.service.backend.RecommendConfigService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@Api(tags = "CustomerSourceRecommendConfig")
@RequiredArgsConstructor
@Slf4j
public class CustomerRecommendDoctorController implements CustomerSourceRecommendConfigApi {
    public final static String dictType = "source_recommend";
    private final static String RecommendDoctorKey = "dyt-tenant:recommend-doctor:";
    // 分组返回医生最大值
    private final static int maxNum = 3;
    // 分组是否启用 1：禁用 0：启用
    private final static String unAble = "1";
    private final static ObjectMapper objectMapper = new ObjectMapper();
    private final RecommendConfigService recommendConfigService;
    private final DictLabelService dictLabelService;
    private final KafkaPublisher kafkaPublisher;
    private final IDoctorService doctorService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public ResponseEntity<List<RecommendConfigDoctorDto>> getRecommendDoctors(Long doctorId) {
        log.info("开始查询推荐医生:{}", doctorId);
        // 查询是否有推荐分组
        RecommendConfig config = recommendConfigService.selectRecommendGroup(doctorId, true);
        if (Objects.isNull(config)) {
            config = recommendConfigService.selectRecommendGroup(doctorId, false);
        }
        if (Objects.isNull(config)) {
            log.info("根据医生id：{}未查询到分组", doctorId);
            return ResponseEntity.ok(Collections.emptyList());
        }
        String group = StringUtils.isNotBlank(config.getDataTag()) ? config.getDataTag().split("-")[1] : config.getImgUrl();
        DictLabelVo detail = dictLabelService.getDictLabelDetail(Long.parseLong(group));
        if (!Objects.equals(detail.getRedirectPath(), unAble)) {
            log.info("分组未启用:{}", group);
            return ResponseEntity.ok(Collections.emptyList());
        }
        List<RecommendConfigDoctorDto> collect = new ArrayList<>();
        if (Boolean.TRUE.equals(redisTemplate.hasKey(RecommendDoctorKey + config.getDataType()))) {
            // 从缓存查询分组下的医生
            String o = (String) redisTemplate.opsForValue().get(RecommendDoctorKey + config.getDataType());
            log.info("从缓存key：{}查询到结果:{}", RecommendDoctorKey + config.getDataType(), o);
            List<RecommendConfigDoctorDto> jsonList = JSONUtil.toList(JSONUtil.parseArray(o), RecommendConfigDoctorDto.class);
            if (jsonList != null && !jsonList.isEmpty()) {
                collect = jsonList.stream()
                        .filter(dto -> !Objects.equals(dto.getDoctorId(), doctorId))
                        .sorted(Comparator.comparingInt(RecommendConfigDoctorDto::getSort))
                        .collect(Collectors.toList());
                int i = Math.min(collect.size(), maxNum);
                collect = collect.subList(0, i);
            } else {
                log.info("{} 分组下的缓存存在，但是无排班，查询推荐结束", config.getDataType());
            }

        } else {
            List<RecommendConfig> list = recommendConfigService.selectDoctorRecommondList(config.getDataType());
            int num = 0;
            for (RecommendConfig configVo : list) {
                log.info("缓存为空，使用数据库信息返回: {}", JSONUtil.toJsonStr(configVo));
                if (num >= maxNum) {
                    break;
                }
                if (StringUtils.equals(configVo.getRedirectUrl(), String.valueOf(doctorId))) {
                    continue;
                }
                objectMapper.clearProblemHandlers();
                RecommendConfigDoctorDto doctor;
                String doctorString = config.getDisplayName();
                try {
                    doctor = objectMapper.readValue(doctorString, RecommendConfigDoctorDto.class);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
                DoctorDetailVo docDetail = doctorService.getDetail(Long.parseLong(configVo.getRedirectUrl()));
                log.info("根据医生id: {}得到到结果{}", configVo.getRedirectUrl(), JSONUtil.toJsonStr(docDetail));
                doctor.setId(configVo.getId());
                doctor.setDoctorId(docDetail.getId());
                doctor.setDoctorName(docDetail.getName());
                doctor.setDoctorCode(docDetail.getThrdpartDoctorCode());
                if (!docDetail.getRankDictLabel().isEmpty()) {
                    doctor.setDoctorTitle(docDetail.getRankDictLabel().get(0));
                }
                doctor.setDoctorHeadImg(docDetail.getHeadImgUrl());
                doctor.setDepartmentId(docDetail.getDepartmentId());
                doctor.setDepartmentCode(docDetail.getThrdpartDepCode());
                doctor.setHospitalId(docDetail.getHospitalId());
                doctor.setHospitalName(docDetail.getHospitalName());
                doctor.setHospitalAreaId(docDetail.getHospitalAreaId());
                doctor.setHospitalAreaName(docDetail.getHospitalAreaName());
                doctor.setStatus(convertStatus(docDetail.getStatus()));
                doctor.setDepartmentName(docDetail.getDepartmentName());
                doctor.setSort(configVo.getSort());
                doctor.setDictLabel(configVo.getDataType());
                doctor.setDictLabelId(Long.parseLong(configVo.getImgUrl()));
                collect.add(doctor);
                num++;
            }
        }
        //发送kafka消息更新分组缓存信息
        String key = StringUtils.isNotBlank(config.getDataTag()) ? config.getDataTag().split("-")[0] : config.getDataType();
        log.info("号满推荐发送消息: {}", key);
        kafkaPublisher.publishMsg("RecommendDoctorGroupUpdate", key);
        return ResponseEntity.ok(collect);
    }

    private int convertStatus(Integer status) {
        // 当 status 为 null 时，默认返回 1，表示初始状态
        if (status == null) {
            return 1;
        }
        return status == 0 ? 1 : (status == 1 ? 0 : 1);
    }

}
