package com.ynhdkc.tenant.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/10/30 15:44:02
 */
@Data
public class ScheduleResponseDto {

    private Pagination pagination;
    private List<ScheduleInfo> list;

    @Data
    public static class Pagination {
        private Integer currentPage;
        private Integer pageSize;
        private Integer totalSize;
    }

    @Data
    public static class ScheduleInfo {
        private String createBy;
        private String createTime;

        @JsonProperty("department_id")
        private Long departmentId;

        @JsonProperty("department_name")
        private String departmentName;

        @JsonProperty("department_code")
        private String departmentCode;

        @JsonProperty("exec_department_code")
        private String execDepartmentCode;

        @JsonProperty("doctor_id")
        private Long doctorId;

        @JsonProperty("doctor_code")
        private String doctorCode;

        @JsonProperty("doctor_name")
        private String doctorName;

        private String editBy;
        private String endTime;
        private Integer ghf;

        @JsonProperty("hospital_area_id")
        private Long hospitalAreaId;

        @JsonProperty("hospital_area_name")
        private String hospitalAreaName;

        @JsonProperty("hospital_id")
        private Long hospitalId;

        @JsonProperty("hospital_name")
        private String hospitalName;
        private Long id;
        private Integer maxTreatNum;

        @JsonProperty("sch_date")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        private Date schDate;

        @JsonProperty("schedule_id")
        private String scheduleId;

        @JsonProperty("schedule_info")
        private String scheduleInfo;

        @JsonProperty("show_sch_date")
        private Integer showSchDate;

        @JsonProperty("src_number")
        private Integer srcNum;

        @JsonProperty("start_time")
        private String startTime;
        private Integer status;

        @JsonProperty("time_part")
        private Integer timePart;

        @JsonProperty("time_type")
        private Integer timeType;

        @JsonProperty("time_type_his")
        private String timeTypeHis;

        @JsonProperty("time_type_text")
        private String timeTypeText;

        @JsonProperty("total_fee")
        private BigDecimal totalFee;

        @JsonProperty("total_fee_format")
        private String totalFeeFormat;

        private String updateTime;

        @JsonProperty("used_num")
        private Integer usedNum;
        private Integer zjf;
        private Integer zlf;

        @JsonProperty("registration_date")
        private Long registrationDate;

        @JsonProperty("registration_level_desc")
        private String registrationLevelDesc;

    }

}
