# DYT-Tenant 数据加密集成指南

## 📋 概述

本文档描述了如何将dyt-backend-encryption模块集成到dyt-tenant项目中，实现敏感数据的加密存储。

## 🎯 涉及的敏感数据字段

根据《数据安全法》《个人信息保护法》整改要求，以下字段需要加密存储：

### t_tenant_user 表（租户用户表）
- `name` - 用户名（重要数据）
- `phone_number` - 手机号码（重要数据）
- ~~`id_card_no` - 身份证号（已移除加密要求）~~

### t_tenant 表（租户表）
- `contact_phone_number` - 联系人手机号（重要数据）
- `contact_email` - 联系人邮箱（重要数据）

## ✅ 已完成的集成工作

### 1. 依赖添加
已在 `dyt-tenant/pom.xml` 中添加加密模块依赖：

```xml
<dependency>
    <groupId>com.ynhdkc</groupId>
    <artifactId>dyt-backend-encryption</artifactId>
    <version>${backend.version}</version>
</dependency>
```

### 2. 配置文件更新
已更新 `application-encrypted.yml` 配置文件，包含：

```yaml
backend:
  encryption:
    enabled: true
    secret-key: "DYT-Backend-Encryption-Key-2024-Production"
    algorithm: AES_GCM
    strategies:
      tenant-user-phone:
        strategy: SHADOW_PRIORITY
        shadow-field: "phone_number_encrypted"
        algorithm: AES_GCM
        enabled: true
      tenant-user-name:
        strategy: SHADOW_PRIORITY
        shadow-field: "name_encrypted"
        algorithm: AES_GCM
        enabled: true
      tenant-contact-phone:
        strategy: SHADOW_PRIORITY
        shadow-field: "contact_phone_number_encrypted"
        algorithm: AES_GCM
        enabled: true
      tenant-contact-email:
        strategy: SHADOW_PRIORITY
        shadow-field: "contact_email_encrypted"
        algorithm: AES_GCM
        enabled: true
```

### 3. 实体类更新

#### BaseUserEntity 类
已添加加密注解到 `BaseUserEntity` 类中：

```java
@EncryptField(
    description = "租户用户名",
    shadowField = "name_encrypted",
    migrationStrategy = EncryptField.MigrationStrategy.SHADOW_PRIORITY,
    strategyKey = "tenant-user-name",
    algorithm = EncryptField.AlgorithmType.AES_GCM
)
@Convert(converter = EncryptConverter.class)
private String name;

@EncryptField(
    description = "租户用户手机号码",
    shadowField = "phone_number_encrypted",
    migrationStrategy = EncryptField.MigrationStrategy.SHADOW_PRIORITY,
    strategyKey = "tenant-user-phone",
    algorithm = EncryptField.AlgorithmType.AES_GCM
)
@Convert(converter = EncryptConverter.class)
private String phoneNumber;
```

#### Tenant 类
已添加加密注解到 `Tenant` 类中：

```java
@EncryptField(
    description = "租户联系人手机号",
    shadowField = "contact_phone_number_encrypted",
    migrationStrategy = EncryptField.MigrationStrategy.SHADOW_PRIORITY,
    strategyKey = "tenant-contact-phone",
    algorithm = EncryptField.AlgorithmType.AES_GCM
)
@Convert(converter = EncryptConverter.class)
private String contactPhoneNumber;

@EncryptField(
    description = "租户联系人邮箱",
    shadowField = "contact_email_encrypted",
    migrationStrategy = EncryptField.MigrationStrategy.SHADOW_PRIORITY,
    strategyKey = "tenant-contact-email",
    algorithm = EncryptField.AlgorithmType.AES_GCM
)
@Convert(converter = EncryptConverter.class)
private String contactEmail;
```

### 4. 数据库影子字段
已生成对应的DDL脚本 `shadow_fields_migration_simple.sql`：

```sql
-- t_tenant_user 表
ALTER TABLE t_tenant_user ADD COLUMN phone_number_encrypted VARCHAR(1000) COMMENT '手机号码加密字段（影子字段）' AFTER phone_number;
ALTER TABLE t_tenant_user ADD COLUMN name_encrypted VARCHAR(1000) COMMENT '用户名加密字段（影子字段）' AFTER name;

-- t_tenant 表
ALTER TABLE t_tenant ADD COLUMN contact_phone_number_encrypted VARCHAR(1000) COMMENT '联系人手机号加密字段（影子字段）' AFTER contact_phone_number;
ALTER TABLE t_tenant ADD COLUMN contact_email_encrypted VARCHAR(1000) COMMENT '联系人邮箱加密字段（影子字段）' AFTER contact_email;
```

## 🚀 部署步骤

### 1. 数据库迁移
```bash
# 执行影子字段新增SQL
mysql -u username -p database_name < shadow_fields_migration_simple.sql
```

### 2. 应用配置
确保应用启动时加载加密配置：

```yaml
spring:
  profiles:
    active: dev,encrypted  # 添加encrypted profile
```

### 3. 验证部署
```bash
# 编译项目
mvn clean compile

# 运行测试
mvn test

# 启动应用
mvn spring-boot:run
```

## 🔍 验证方法

### 1. 数据库验证
```sql
-- 检查影子字段是否创建成功
SELECT TABLE_NAME, COLUMN_NAME, COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND COLUMN_NAME LIKE '%_encrypted'
ORDER BY TABLE_NAME;
```

### 2. 功能验证
```java
// 创建租户用户测试
TenantUser user = new TenantUser();
user.setName("测试用户");
user.setPhoneNumber("13800138000");
tenantUserRepository.save(user);

// 查询验证
TenantUser savedUser = tenantUserRepository.findById(user.getId());
assertEquals("测试用户", savedUser.getName()); // 应该能正常读取解密数据
```

### 3. 数据库数据验证
```sql
-- 检查数据是否正确加密存储
SELECT 
    name, name_encrypted,
    phone_number, phone_number_encrypted
FROM t_tenant_user 
WHERE id = 1;
```

## ⚠️ 注意事项

1. **渐进式迁移**：当前使用SHADOW_PRIORITY策略，优先读取影子字段，原字段作为备份
2. **密钥管理**：生产环境请使用环境变量配置密钥
3. **性能监控**：启用了性能监控，可通过日志观察加密性能
4. **字段长度**：影子字段设置为1000字符，足够存储加密数据
5. **索引策略**：加密字段通常不适合创建索引，因为加密后数据是随机的

## 🔧 故障排查

### 常见问题

1. **加密失败**
   - 检查密钥配置是否正确
   - 确认影子字段是否存在
   - 查看应用日志中的错误信息

2. **数据读取异常**
   - 确认策略配置是否正确
   - 检查注解配置是否匹配
   - 验证数据库字段长度是否足够

3. **性能问题**
   - 监控加密操作耗时
   - 检查批量操作配置
   - 考虑调整加密策略

### 调试配置
```yaml
logging:
  level:
    backend.encryption: DEBUG
    
backend:
  encryption:
    debug-enabled: true
    performance-monitor-enabled: true
```

## 📈 后续计划

1. **监控数据迁移进度**：观察影子字段填充情况
2. **性能优化**：根据实际使用情况调整配置
3. **策略升级**：适时切换到SHADOW_ONLY策略
4. **安全审计**：定期检查加密效果和安全性

## 🎉 集成完成

dyt-tenant项目已成功集成数据加密功能，现在可以：
- ✅ 自动加密存储敏感数据
- ✅ 透明解密读取数据
- ✅ 支持渐进式迁移
- ✅ 符合法规合规要求

如有问题，请参考日志信息或联系技术支持团队。
