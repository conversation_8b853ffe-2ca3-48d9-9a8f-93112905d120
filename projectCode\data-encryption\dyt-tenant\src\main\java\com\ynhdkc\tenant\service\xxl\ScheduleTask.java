package com.ynhdkc.tenant.service.xxl;

import backend.common.enums.HospitalCode;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.ynhdkc.tenant.dao.DepartmentQuery;
import com.ynhdkc.tenant.dao.TenantUserQuery;
import com.ynhdkc.tenant.dao.TenantUserRepository;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.TenantUser;
import com.ynhdkc.tenant.service.backend.IHisRequestService;
import com.ynhdkc.tenant.service.backend.OrganizationStructureService;
import com.ynhdkc.tenant.service.backend.RecommendStatisticsService;
import com.ynhdkc.tenant.service.customer.CustomerDepartmentService;
import com.ynhdkc.tenant.service.customer.IHisGatewayService;
import com.ynhdkc.tenant.service.customer.SyncDoctorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/7/17 15:05:30
 */
@Slf4j
@Component
@EnableScheduling
@RequiredArgsConstructor
public class ScheduleTask {
    private final RecommendStatisticsService recommendStatisticsService;
    private final OrganizationStructureService organizationStructureService;
    private final IHisRequestService hisRequestService;
    private final CustomerDepartmentService customerDepartmentService;
    private final DepartmentQuery departmentQuery;

    private final TenantUserQuery tenantUserQuery;
    private final TenantUserRepository tenantUserRepository;
    private final IHisGatewayService hisGatewayService;

    private final SyncDoctorService syncDoctorService;

    @XxlJob("syncHospitalDepartment")
    public void syncHospitalDepartment() {
        hisRequestService.requestDepartmentsFromHis();
    }

    @XxlJob("syncGongRenHospitalDepartment")
    public void syncGongRenHospitalDepartment() {
        hisRequestService.requestGongRenDepartmentsFromHis();
    }

    @XxlJob("syncOneHospitalDepartment")
    public void syncOneHospitalDepartment() {
        String hospitalCode = XxlJobHelper.getJobParam();
        hisRequestService.requestOneDepartment(hospitalCode);
    }

    @XxlJob("syncHospitalDepartmentDoctor")
    public void syncHospitalDepartmentDoctor() {
        hisRequestService.syncDoctorSchedule();
    }

    @XxlJob("syncHospitalDepartmentDoctorInMorning")
    public void syncHospitalDepartmentDoctorInMorning() {
        hisRequestService.syncHospitalDepartmentDoctorInMorning();
    }

    @XxlJob("syncOrganizationStructure")
    public void syncOrganizationStructure() {
        organizationStructureService.syncOrganizationStructure();
        organizationStructureService.syncUserOrganizationStructure();
    }

    @XxlJob("syncOneHospital")
    public void syncOneHospital() {
        String parameter = XxlJobHelper.getJobParam();
        log.info("sync_one_hospital_parameters {}", parameter);
        if (ObjectUtils.isEmpty(parameter)) {
            return;
        }
        String[] hospitalRawCodes = parameter.split(",");
        for (String hospitalRawCode : hospitalRawCodes) {
            HospitalCode hospitalCode = HospitalCode.parse(hospitalRawCode);
            if (HospitalCode.UNKNOWN.equals(hospitalCode)) {
                log.error("sync_hospital_code_is_unknown");
                continue;
            }
            log.info("sync_one_hospital_hospital_code: {}", hospitalCode.getDesc());
            hisRequestService.syncDoctorSchedule(hospitalCode.getCode());
        }
    }

    @XxlJob("syncDepartDoctorByHospitalCode")
    public void syncDepartDoctor() {
        String parameter = XxlJobHelper.getJobParam();
        XxlJobHelper.log("获取到的参数数据为：[{}]", parameter);
        if (ObjectUtils.isEmpty(parameter)) {
            return;
        }
        String[] hospitalRawCodes = parameter.split(",");
        for (String hospitalRawCode : hospitalRawCodes) {
            List<Department> departments = departmentQuery.queryBy(hospitalRawCode);
            if (!CollectionUtils.isEmpty(departments)) {
                departments.forEach(department -> {
                    try {
                        Thread.sleep(200);
                        customerDepartmentService.queryGroupedDoctorListByDepartmentId(department.getId());
                    } catch (InterruptedException e) {
                        log.error("sync_depart_doctor_error", e);
                    }

                });
            }
        }
        XxlJobHelper.log("科室医生数据同步完成！");
    }

    @XxlJob("mergeStatistics")
    public void mergeStatistics() {
        recommendStatisticsService.mergeStatistics();
    }

    @XxlJob("resetPasswordErrorTimes")
    public ResponseEntity<Void> resetPasswordErrorTimes() {
        List<TenantUser> tenantUsers = tenantUserQuery.queryPasswordErrorTimesMoreThanOne();
        if (!CollectionUtils.isEmpty(tenantUsers)) {
            tenantUsers.forEach(tenantUser -> {
                tenantUser.setDailyLoginRetries(0);
                tenantUserRepository.update(tenantUser);
            });
        }
        return ResponseEntity.ok().build();
    }

    @XxlJob("syncYunDaHospitalDoctorInfo")
    public void syncYunDaHospitalDoctorInfo() {
        hisGatewayService.syncYunDaHospitalDoctorInfo();
    }

    @XxlJob("syncDoctorData4DoctorLevelTask")
    public void syncDoctorData4DoctorLevelTask() {
        syncDoctorService.syncDoctorData4DoctorLevelTask();
    }
}
