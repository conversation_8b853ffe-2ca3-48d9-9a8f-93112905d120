package backend.common.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

/**
 * Inherits this class for generally use.
 */
@Data
@Builder
public class BaseOperationResponse {
    public static final String SUCCESS_STATUS = "200";
    public static final String DEFAULT_SUCCESS_MESSAGE = "ok";
    /**
     * 操作记录的条数
     */
    @JsonProperty("effective_count")
    private Integer effectiveCount;

    /**
     * 操作状态
     */
    @JsonProperty("status")
    private String status;

    /**
     * 展示信息
     */
    @JsonProperty("message")
    private String message;

    public BaseOperationResponse() {
        this(null, SUCCESS_STATUS, DEFAULT_SUCCESS_MESSAGE);
    }

    public BaseOperationResponse(String message) {
        this(null, SUCCESS_STATUS, message);
    }

    public BaseOperationResponse(String status, String message) {
        this(null, status, message);
    }

    /**
     * Generally used for a success response that don't need object return.
     *
     * @param effectiveCount operation effective lines
     * @param status         operation status
     * @param message        help information
     */
    public BaseOperationResponse(Integer effectiveCount, String status, String message) {
        this.effectiveCount = effectiveCount;
        this.status = status;
        this.message = message;
    }

    public static BaseOperationResponse success(Integer effectiveCount, String message) {
        return new BaseOperationResponse(effectiveCount, SUCCESS_STATUS, message);
    }
}
