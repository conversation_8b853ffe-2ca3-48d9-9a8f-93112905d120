[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
[remote "origin"]
	url = https://gitlab.ynhdkc.com/dyt/backend/dyt-data-encryption-decryption.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "main"]
	remote = origin
	merge = refs/heads/main
	vscode-merge-base = origin/main
	vscode-merge-base = origin/main
[branch "feature/enhanced-encryption-with-sm2"]
	remote = origin
	merge = refs/heads/feature/enhanced-encryption-with-sm2
	vscode-merge-base = origin/feature/enhanced-encryption-with-sm2
