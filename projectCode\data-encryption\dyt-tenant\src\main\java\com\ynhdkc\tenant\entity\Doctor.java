package com.ynhdkc.tenant.entity;

import backend.common.domain.BaseEntity;
import cn.hutool.core.text.CharSequenceUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/22 17:45:18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "t_doctor")
public class Doctor extends BaseEntity {
    private Long tenantId;
    private Long hospitalId;
    private Long hospitalAreaId;
    private String hospitalCode;
    private Long departmentId;
    private String departmentCode;
    private Long userId;
    private String thrdpartDoctorCode;
    private String headImgUrl;
    private String name;
    private String email;
    private String rankDictType;
    private String rankDictValue;
    private String rankDictLabel;
    private String registrationLevel;
    private String speciality;
    private String introduction;
    private String appointmentRuleDictType;
    private String appointmentRuleDictLabel;
    private String appointmentRule;
    private String statement;
    private Integer gender;
    private Integer sort;
    private Integer systemDepends;
    private Integer status;
    private String honor;
    private String category;
    private Boolean display;
    private Integer forbiddenDay;
    private Integer advanceDay;
    private String sourceActivateTime;
    private Boolean multiDepartment;

    private String visitingAddress;
    private String visitingIntroduction;
    private String appointmentNotice;
    private String paymentNotice;
    private Boolean judgeAppointmentCondition;
    private String judgeAppointmentRule;
    private Boolean needUploadResource;
    private Boolean needVerifyResource;
    private String successNoticePhones;
    private String successTemplateIds;

    private Boolean needDivideSettlement;
    private Long secondMerchantId;
    private Long settlementRuleId;

    private String displayDepartmentName;
    private String shortening;
    private String tags;
    private Boolean updateFromHis;


    public List<Integer> getCategoryStr() {
        return CharSequenceUtil.splitTrim(this.category, ',').stream().map(Integer::valueOf).collect(Collectors.toList());
    }

    public void setCategoryStr(List<Integer> category) {
        this.category = CharSequenceUtil.join(",", category.stream().sorted().collect(Collectors.toList()));
    }
}
