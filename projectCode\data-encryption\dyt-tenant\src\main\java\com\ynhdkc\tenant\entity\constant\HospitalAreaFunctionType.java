package com.ynhdkc.tenant.entity.constant;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-09 11:11
 */
public enum HospitalAreaFunctionType {
    /**
     * 预约挂号
     */
    appointment_registration("预约挂号"),
    /**
     * 门诊缴费
     */
    diagnosis_payment("门诊缴费"),
    /**
     * 报告查询
     */
    patient_report("报告查询"),
    /**
     * 住院
     */
    hospitalization("住院"),
    /**
     * 就诊卡
     */
    patient_card("就诊卡"),
    /**
     * 排队签到
     */
    queue_sign_in("排队签到"),
    /**
     * 候诊查询
     */
    waiting_inquiry("候诊查询"),
    /**
     * 陪诊
     */
    medical_escort("陪诊"),
    /**
     * 门诊病历查询
     */
    outpatient_medical_record_inquiry("门诊病历查询"),
    /**
     * 特需门诊
     */
    special_needs_clinic("特需门诊"),
    /**
     * 多学科门诊
     */
    multidisciplinary_clinic("多学科门诊"),
    /**
     * 自定义
     */
    custom("自定义业务");

    private final String desc;

    HospitalAreaFunctionType(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 是否包含该功能
     */
    public static boolean contains(String name) {
        if (null == name) {
            return false;
        }
        for (HospitalAreaFunctionType type : HospitalAreaFunctionType.values()) {
            if (type.name().equals(name)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 通过名字查询枚举值
     */
    public static HospitalAreaFunctionType getByName(String name) {
        if (null == name) {
            return null;
        }
        for (HospitalAreaFunctionType type : HospitalAreaFunctionType.values()) {
            if (type.name().equals(name)) {
                return type;
            }
        }
        return null;
    }
}
