FROM megaease/easeimg-javabuild:latest AS builder

ARG      REPOSITORY1
ARG      REPOSITORY2
ARG      REPOSITORY3
ARG      MIRROR1
ARG      MIRROR2
ARG      MIRROR3
ARG      SERVER1
ARG      SERVER2
ARG      SERVER3
ARG      PROJECT_NAME

COPY     pom.xml /${PROJECT_NAME}/pom.xml
COPY     src /${PROJECT_NAME}/src
COPY     resources /${PROJECT_NAME}/resources

WORKDIR  /${PROJECT_NAME}

RUN      rewrite-settings.sh && cd /${PROJECT_NAME}/ && mkdir out \
         && /${PROJECT_NAME}/resources/scripts/build-app.sh

FROM openjdk:8u292-slim

ARG PROJECT_NAME

RUN mkdir /${PROJECT_NAME}

COPY --from=builder /${PROJECT_NAME}/target/${PROJECT_NAME}-1.0-SNAPSHOT.jar /${PROJECT_NAME}
COPY --from=builder /${PROJECT_NAME}/resources/scripts/bootstrap.sh  /

VOLUME /tmp

WORKDIR /${PROJECT_NAME}

EXPOSE 18090

CMD [ "/bootstrap.sh -j 31000" ]

