package com.ynhdkc.tenant.component;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "tenant-user-login-config")
@Data
/**
 * 租户用户登录配置类
 */
public class TenantUserLoginConfig {

    /**
     * 密码策略说明
     */
    private String passwordPolicy;

    /**
     * 最小密码长度
     */
    private Integer minPasswordLength;

    /**
     * 最大登录尝试次数
     */
    private Integer maxLoginAttempts;

    /**
     * 登录冷却时间（单位：分钟）
     */
    private Long loginCooloffTime;

    /**
     * 是否允许多设备登录
     */
    private Boolean multiDeviceLogin;

    /**
     * 账户锁定时长（单位：秒）
     */
    private Long accountLockDuration;

    /**
     * 密码有效期天数
     */
    private Short passwordExpiryDays;

    /**
     * 记录的密码历史数量
     */
    private Integer passwordHistoryCount;

    /**
     * 密码正则表达式
     */
    private String passwordRegex;
}
