package com.ynhdkc.tenant.service.backend.impl;

import backend.common.kafka.KafkaPublisher;
import backend.common.kafka.constant.KafkaProperties;
import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.service.backend.HospitalAreaService;
import com.ynhdkc.tenant.service.backend.HospitalKafkaService;
import com.ynhdkc.tenant.service.backend.HospitalService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-26 22:55
 */
@Service
@RequiredArgsConstructor
public class HospitalKafkaServiceImpl implements HospitalKafkaService {
    private final HospitalService hospitalService;
    private final HospitalAreaService hospitalAreaService;
    private final KafkaPublisher kafkaPublisher;

    @Override
    public void syncAllHospital() {
        hospitalService.queryAllForKafka().forEach(hospitalKafkaVo -> sendMessage(hospitalKafkaVo.getId().toString(), hospitalKafkaVo));
    }

    @Override
    public void syncHospital(Long hospitalId) {
        sendMessage(hospitalId.toString(), hospitalService.getDetailForKafka(hospitalId));
    }

    @Override
    public void deleteHospital(Long hospitalId) {
        sendMessage(hospitalId.toString(), null);
    }

    @Override
    public void syncHospitalBy(Long hospitalAreaId) {
        Hospital hospitalArea = hospitalAreaService.getById(hospitalAreaId);
        if (hospitalArea != null) {
            sendMessage(hospitalArea.getId().toString(), hospitalService.getDetailForKafka(hospitalArea.getParentId()));
        }
    }

    private void sendMessage(String key, Object message) {
        kafkaPublisher.publishMsg(KafkaProperties.HOSPITAL_TOPIC_NAME, key, message);
    }
}
