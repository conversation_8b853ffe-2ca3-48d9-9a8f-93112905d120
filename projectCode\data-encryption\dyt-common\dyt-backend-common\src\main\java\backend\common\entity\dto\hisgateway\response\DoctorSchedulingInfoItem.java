package backend.common.entity.dto.hisgateway.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class DoctorSchedulingInfoItem {

    @JsonProperty("hospital_code")
    private String hospitalCode;

    @JsonProperty("schedule_id")
    private String scheduleId;

    @JsonProperty("registration_date")
    private String registrationDate;

    @JsonProperty("registration_level")
    private String registrationLevel;

    @JsonProperty("registration_level_desc")
    private String registrationLevelDesc;

    private String resource;

    @JsonProperty("department_name")
    private String departmentName;

    @JsonProperty("department_code")
    private String departmentCode;

    @JsonProperty("doctor_name")
    private String doctorName;

    @JsonProperty("doctor_code")
    private String doctorCode;

    @JsonProperty("doctor_type")
    private String doctorType;

    @JsonProperty("doctor_type_desc")
    private String doctorTypeDesc;

    @JsonProperty("begin_time")
    private Long beginTime;

    @JsonProperty("end_time")
    private Long endTime;

    @JsonProperty("time_type")
    private Integer timeType;

    @JsonProperty("time_type_desc")
    private String timeTypeDesc;

    @JsonProperty("hospital_id")
    private Long hospitalId;

    @JsonProperty("hospital_area_id")
    private Long hospitalAreaId;

    @JsonProperty("department_id")
    private Long departmentId;

    @JsonProperty("doctor_id")
    private Long doctorId;

    @JsonProperty("total_fee")
    private BigDecimal totalFee;

    @JsonProperty("is_time_part")
    private Integer isTimePart;

    @JsonProperty("show_sch_date")
    private Integer showSchDate;

    public String getKey(String hospitalCode) {
        return String.format("%s_%s_%s", hospitalCode, departmentCode, doctorCode);
    }

}
