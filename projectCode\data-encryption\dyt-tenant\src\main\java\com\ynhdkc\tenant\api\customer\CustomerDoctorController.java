package com.ynhdkc.tenant.api.customer;

import com.ynhdkc.tenant.handler.CustomerDoctorApi;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.customer.CustomerDoctorService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/7/21 10:25
 */
@Api(tags = "CustomerDoctor")
@RestController
@RequiredArgsConstructor
public class CustomerDoctorController implements CustomerDoctorApi {

    private final CustomerDoctorService customerDoctorService;

    @Override
    public ResponseEntity<CustomerDoctorAskServiceVo> getAskServiceList(Long doctorId) {
        return ResponseEntity.ok(customerDoctorService.getAskServiceList(doctorId));
    }

    @Override
    public ResponseEntity<CustomerDoctorDetailVo> getDetail(Long doctorId) {
        return ResponseEntity.ok(customerDoctorService.getDetail(doctorId));
    }

    @Override
    public ResponseEntity<CustomerDoctorDetailVo> getDetailByCode(String hospitalCode, String departmentCode, String doctorCode) {
        return ResponseEntity.ok(customerDoctorService.getDetailByCode(hospitalCode, departmentCode, doctorCode));
    }

    @Override
    public ResponseEntity<CustomerDoctorPageVo> query(CustomerDoctorQueryReqDto request) {
        return ResponseEntity.ok(customerDoctorService.query(request));
    }

    @Override
    public ResponseEntity<CustomerDoctorScheduledDepartmentVo> queryDoctorScheduledDepartment(Long baseDoctorId) {
        return ResponseEntity.ok(customerDoctorService.queryDoctorScheduledDepartment(baseDoctorId));
    }
}
