package com.ynhdkc.tenant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/** 医生职称展示类型
 * <AUTHOR>
 * @date 2024/4/26 10:11
 */
@AllArgsConstructor
@NoArgsConstructor
public enum DoctorTitleShowType {
    FROM_SYSTEM("平台职称",1),
    FROM_HIS("his职称",2),
    FROM_HONOR("荣誉",3);

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }

    private String name;
    private Integer value;
}
