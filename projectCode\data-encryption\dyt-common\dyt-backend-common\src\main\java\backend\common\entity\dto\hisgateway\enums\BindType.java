package backend.common.entity.dto.hisgateway.enums;

import com.fasterxml.jackson.annotation.JsonGetter;

public enum BindType {

	BIND(1, "绑定"), CHANGING(2, "换绑"), UNBIND(3, "解绑");

	private final Integer code;

	private final String value;

	BindType(Integer code, String value) {
		this.code = code;
		this.value = value;
	}

	@JsonGetter
	public Integer getCode() {
		return code;
	}

	public String getValue() {
		return value;
	}

}
