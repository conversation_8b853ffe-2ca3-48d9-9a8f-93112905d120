package com.ynhdkc.tenant.api.backend;

import backend.common.domain.tenant.constant.AppointmentSystemDepends;
import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import backend.security.method.BackendSecurityRequired;
import backend.security.oplog.DytSecurityRequired;
import backend.security.service.BackendTenantUserService;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.handler.DepartmentApi;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.DepartmentService;
import com.ynhdkc.tenant.service.backend.HospitalKafkaService;
import com.ynhdkc.tenant.service.backend.IHisRequestService;
import com.ynhdkc.tenant.service.backend.OrganizationStructureService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2023/2/22 15:07:17
 */
@Api(tags = "Department")
@RestController
@RequiredArgsConstructor
public class DepartmentController implements DepartmentApi {

    private final DepartmentService departmentService;
    private final HospitalKafkaService hospitalKafkaService;
    private final BackendTenantUserService backendTenantUserService;
    private final IHisRequestService hisRequestService;
    private final OrganizationStructureService organizationStructureService;

    @BackendSecurityRequired(tenantIdExpr = "#request.tenantId", hospitalIdExpr = "#request.hospitalId", hospitalAreaIdExpr = "#request.hospitalAreaId")
    @DytSecurityRequired(needOpLog = true, value = "hospital:department:create")
    @Override
    public ResponseEntity<DepartmentDetailVo> create(DepartmentCreateReqDto request) {
        if (null != request.getSystemDepends()) {
            AppointmentSystemDepends dependsEnum = AppointmentSystemDepends.of(request.getSystemDepends());
            if (null == dependsEnum) {
                throw new BizException(HttpStatus.BAD_REQUEST, "系统依赖类型错误");
            }
        }

        DepartmentDetailVo vo = departmentService.create(request);
        hospitalKafkaService.syncHospital(request.getHospitalId());

        organizationStructureService.asyncUserOrganizationStructureAndUser();

        return ResponseEntity.ok(vo);
    }

    @BackendSecurityRequired(tenantManager = true)
    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:department:delete")
    public ResponseEntity<BaseOperationResponse> delete(Long departmentId, Long tenantId, Long hospitalId, Long hospitalAreaId) {
        Pair<BaseOperationResponse, Long> pair = departmentService.delete(departmentId);
        hospitalKafkaService.deleteHospital(pair.getSecond());

        organizationStructureService.asyncUserOrganizationStructureAndUser();

        return ResponseEntity.ok(pair.getFirst());
    }


    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:department:get")
    public ResponseEntity<DepartmentDetailVo> getDetail(Long departmentId) {
        if (!backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.DepartmentId(departmentId))) {
            throw new BizException(HttpStatus.FORBIDDEN, "用户没有该科室权限");
        }
        return ResponseEntity.ok(departmentService.getDetail(departmentId));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:department:query")
    public ResponseEntity<DepartmentPageVo> query(DepartmentQueryReqDto request) {
        return ResponseEntity.ok(departmentService.query(request));
    }

    @BackendSecurityRequired(tenantIdExpr = "#request.tenantId")
    @DytSecurityRequired(needOpLog = true, value = "hospital:department:queryByTable")
    @Override
    public ResponseEntity<TableDepartmentQueryVo> queryByTable(TableDepartmentQueryReqDto request) {
        return ResponseEntity.ok(departmentService.queryByTable(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:department:queryByTree")
    public ResponseEntity<TreeDepartmentQueryVo> queryByTree(TreeDepartmentQueryReqDto request) {
        if (!backendTenantUserService.currentUserHasReadPrivilege(new BackendTenantUserService.TenantId(request.getTenantId()))) {
            throw new BizException(HttpStatus.FORBIDDEN, "用户没有该租户权限");
        }
        return ResponseEntity.ok(departmentService.queryByTree(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:department:queryTree:search")
    public ResponseEntity<DepartmentsTreeSearchListVo> queryTree(DepartmentsTreeSearchReqDto request) {
        return ResponseEntity.ok(departmentService.queryTree(request));
    }

    @Override
    @DytSecurityRequired(needOpLog = true, value = "hospital:department:queryTree:search2")
    public ResponseEntity<DepartmentsTreeSearchListVo> queryTree2(DepartmentsTreeSearchReqDto request) {
        return ResponseEntity.ok(departmentService.queryTree2(request));
    }

    @Override
    public ResponseEntity<DepartmentsTreeSearchPageVo> queryPage(DepartmentsTreeSearchPageReqDto request) {
        return ResponseEntity.ok(departmentService.queryPage(request));
    }

    @Override
    public ResponseEntity<Void> syncDepartmentDoctorsFromHis(Long id) {
        Department department = departmentService.getById(id);
        if (department == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "科室不存在");
        }

        hisRequestService.requestDepartmentDoctorsFromHis(department);
        return ResponseEntity.ok().build();
    }


    @BackendSecurityRequired(tenantIdExpr = "#request.tenantId", hospitalIdExpr = "#request.hospitalId", hospitalAreaIdExpr = "#request.hospitalAreaId", departmentIdExpr = "#departmentId")
    @DytSecurityRequired(needOpLog = true, value = "hospital:department:update")
    @Override
    public ResponseEntity<DepartmentDetailVo> update(Long departmentId, DepartmentUpdateReqDto request) {
        if (null != request.getSystemDepends()) {
            AppointmentSystemDepends dependsEnum = AppointmentSystemDepends.of(request.getSystemDepends());
            if (null == dependsEnum) {
                throw new BizException(HttpStatus.BAD_REQUEST, "系统依赖类型错误");
            }
        }

        DepartmentDetailVo departmentVo = departmentService.update(departmentId, request);
        hospitalKafkaService.syncHospital(request.getHospitalId());

        organizationStructureService.asyncUserOrganizationStructureAndUser();

        return ResponseEntity.ok(departmentVo);
    }


    @Override
    public ResponseEntity<AppointNotifyConfigVo> getAppointNotifyConfig(String hospitalCode, Long departmentId) {
        return ResponseEntity.ok(departmentService.getAppointNotifyConfig(hospitalCode, departmentId));
    }

    @Override
    public ResponseEntity<SetAppointNotifyConfigResponse> setAppointNotifyConfig(String hospitalCode, Long departmentId, SetAppointNotifyConfigReqDto admins) {
        return ResponseEntity.ok(departmentService.setAppointNotifyConfig(hospitalCode, departmentId, admins));
    }
}
