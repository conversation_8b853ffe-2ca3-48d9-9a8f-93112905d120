package com.ynhdkc.tenant.service.backend;

import backend.common.exception.BizException;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.ynhdkc.tenant.dao.mapper.DictLabelMapper;
import com.ynhdkc.tenant.entity.DictLabel;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/14 09:34:28
 */
@Service
@RequiredArgsConstructor
public final class DictLabelService implements IDictInfoKafkaService {

    private final PageVoConvert pageVoConvert;
    private final DictLabelMapper dictLabelRepository;
    private final DictTypeService dictTypeService;

    private final Function<DictLabel, DictLabelVo> dictLabelVoFunction = q -> {
        DictLabelVo dictLabelVo = new DictLabelVo();
        dictLabelVo.setId(q.getId());
        dictLabelVo.setDictType(q.getDictType());
        dictLabelVo.setDictLabel(q.getDictLabel());
        dictLabelVo.setDictValue(q.getDictValue());
        dictLabelVo.setSort(q.getSort());
        dictLabelVo.setDescription(q.getDescription());
        dictLabelVo.setIconUrl(q.getIconUrl());
        dictLabelVo.setTag(q.getTag());
        dictLabelVo.setRedirectPath(q.getRedirectPath());
        dictLabelVo.setCreateTime(q.getCreateTime());
        dictLabelVo.setUpdateTime(q.getUpdateTime());
        dictLabelVo.setBadge(q.getBadge());
        return dictLabelVo;
    };

    private final BiFunction<CreateDictLabelDto, DictLabel, DictLabel> dictLabelFunction = (dto, dictLabel) -> {
        dictLabel.setDictType(dto.getDictType());
        dictLabel.setDictLabel(dto.getDictLabel());
        dictLabel.setDictValue(dto.getDictValue());
        dictLabel.setDescription(dto.getDescription());
        dictLabel.setSort(dto.getSort());
        dictLabel.setIconUrl(dto.getIconUrl());
        dictLabel.setBadge(dto.getBadge());
        dictLabel.setTag(dto.getTag());
        dictLabel.setRedirectPath(dto.getRedirectPath());
        return dictLabel;
    };


    public DictLabelVo create(CreateDictLabelDto createDictLabelDto) {
        if (dictLabelRepository.countByTypeAndLabel(createDictLabelDto.getDictType(), createDictLabelDto.getDictLabel()) > 0) {
            throw new BizException(HttpStatus.CONFLICT, "字典类型对应的字典标签已存在", createDictLabelDto.getDictType(), createDictLabelDto.getDictLabel());
        }

        DictLabel dictLabel = dictLabelFunction.apply(createDictLabelDto, new DictLabel());
        dictLabelRepository.create(dictLabel);
        return dictLabelVoFunction.apply(dictLabel);
    }

    public int delete(Long id) {
        int effectiveCount = dictLabelRepository.deleteByPrimaryKey(id);
        if (effectiveCount == 0) {
            throw new BizException(HttpStatus.NOT_FOUND, "字典标签不存在", id);
        }
        return effectiveCount;
    }

    public DictLabelVo update(Long dictLabelId, UpdateDictLabelDto updateDictLabelDto) {
        DictLabel dictLabel = dictLabelRepository.selectByPrimaryKey(dictLabelId);
        if (dictLabel == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "字典标签不存在", dictLabelId);
        }

        if (dictLabelRepository.countByTypeAndLabelAndIdNot(dictLabelId, updateDictLabelDto.getDictType(), updateDictLabelDto.getDictLabel()) > 0) {
            throw new BizException(HttpStatus.CONFLICT, "字典类型对应的字典标签已存在", updateDictLabelDto.getDictType(), updateDictLabelDto.getDictLabel());
        }
        dictLabelRepository.updateByPrimaryKey(dictLabelFunction.apply(updateDictLabelDto, dictLabel));
        return dictLabelVoFunction.apply(dictLabel);
    }

    public DictLabelVo getDictLabelDetail(Long id) {
        DictLabel dictLabel = dictLabelRepository.selectByPrimaryKey(id);
        if (dictLabel == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "字典标签不存在", id);
        }
        return dictLabelVoFunction.apply(dictLabel);
    }

    public DictLabelPageVo getDictLabelList(String name, String dictTypeName, String sortBy, String description, Integer currentPage, Integer pageSize) {
        Page<DictLabel> dictLabelPage;
        try (final Page<DictLabel> page = PageMethod.startPage(currentPage, pageSize)) {
            dictLabelPage = page.doSelectPage(() -> dictLabelRepository.selectByConditions(name, dictTypeName, description, sortBy));
        }
        return pageVoConvert.toPageVo(dictLabelPage, DictLabelPageVo.class, dictLabelVoFunction);
    }

    public List<DictLabel> testDictLabels(List<String> dictTypes, List<String> dictLabels) {
        return getDictLabelsByDictTypesAndDictLabels(dictTypes, dictLabels);
    }


    public List<DictLabel> getDictLabelsByDictTypesAndDictLabels(List<String> dictTypes, List<String> dictLabels) {
        return dictLabelRepository.selectByDictTypesAndDictLabels(dictTypes, dictLabels);
    }

    @Override
    public void syncAllDictInfo() {
        List<DictLabel> dictLabels = dictLabelRepository.selectAll();
        List<DictLabelVo> dictLabelVos = dictLabels.stream().map(dictLabelVoFunction).collect(Collectors.toList());

        List<DictTypeVo> dictTypeVos = dictTypeService.getAllDictTypeVos();

        DictInfoKafkaVo dictInfoKafkaVo = new DictInfoKafkaVo();
        dictInfoKafkaVo.setDictLabels(dictLabelVos);
        dictInfoKafkaVo.setDictTypes(dictTypeVos);
    }

    public List<DictLabel> getDictLabelBy(String dictType) {
        return dictLabelRepository.selectByExample(DictLabel.class, helper -> helper.defGroup(condition -> {
            condition.andEqualTo(DictLabel::getDictType, dictType);
        }));
    }

    public DictLabelPageVo getSourceRecommendList(String label, String dictType, String description, Integer status, Integer currentPage, Integer pageSize) {
        Page<DictLabel> dictLabelPage;
        try (final Page<DictLabel> page = PageMethod.startPage(currentPage, pageSize)) {
            dictLabelPage = page.doSelectPage(() -> dictLabelRepository.selectSourceRecommendList(label, dictType, status, description));
        }
        return pageVoConvert.toPageVo(dictLabelPage, DictLabelPageVo.class, dictLabelVoFunction);
    }

    public void updateSourceRecommend(Long id) {
        DictLabel dictLabel = dictLabelRepository.selectByPrimaryKey(id);
        if (dictLabel == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "字典标签不存在", id);
        }
        dictLabel.setUpdateTime(new Date());
        dictLabelRepository.updateByPrimaryKey(dictLabel);
    }
}
