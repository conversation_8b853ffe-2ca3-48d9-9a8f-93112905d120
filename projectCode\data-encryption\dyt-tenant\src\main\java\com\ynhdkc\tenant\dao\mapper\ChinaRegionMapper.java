package com.ynhdkc.tenant.dao.mapper;

import backend.common.util.BaseQueryOption;
import com.ynhdkc.tenant.entity.ChinaRegion;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/2/14 16:19:36
 */
@Mapper
public interface ChinaRegionMapper extends BaseMapper<ChinaRegion> {
    default void selectByConditions(RegionQueryOption option) {
        selectByExample(ChinaRegion.class, sql -> sql.defGroup(condition -> {
            if (null != option.id) {
                condition.andEqualTo(ChinaRegion::getId, option.id);
            }
            if (null != option.pid) {
                condition.andEqualTo(ChinaRegion::getPid, option.pid);
            }
            if (null != option.shortName) {
                condition.andEqualTo(ChinaRegion::getShortName, option.shortName);
            }
            if (null != option.name) {
                condition.andEqualTo(ChinaRegion::getName, option.name);
            }
            if (null != option.mergerName) {
                condition.andEqualTo(ChinaRegion::getMergerName, option.mergerName);
            }
            if (null != option.level) {
                condition.andEqualTo(ChinaRegion::getLevel, option.level);
            }
            if (null != option.pinyin) {
                condition.andEqualTo(ChinaRegion::getPinyin, option.pinyin);
            }
            if (null != option.code) {
                condition.andEqualTo(ChinaRegion::getCode, option.code);
            }
            if (null != option.zipCode) {
                condition.andEqualTo(ChinaRegion::getZipCode, option.zipCode);
            }
            if (null != option.first) {
                condition.andEqualTo(ChinaRegion::getFirst, option.first);
            }
            if (null != option.spellSimple) {
                condition.andEqualTo(ChinaRegion::getSpellSimple, option.spellSimple);
            }
            if (null != option.startCreateTime && null != option.endCreateTime) {
                condition.andBetween(ChinaRegion::getCreateTime, option.startCreateTime, option.endCreateTime);
            }
        }));
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Accessors(chain = true)
    class RegionQueryOption extends BaseQueryOption {
        private Long id;
        private Long pid;
        private String shortName;
        private String name;
        private String mergerName;
        private Integer level;
        private String pinyin;
        private String code;
        private String zipCode;
        private String first;
        private String spellSimple;
        private Date startCreateTime;
        private Date endCreateTime;
        public RegionQueryOption() {
            super();
        }
        public RegionQueryOption(Integer currentPage, Integer pageSize) {
            super(currentPage, pageSize);
        }
    }
}
