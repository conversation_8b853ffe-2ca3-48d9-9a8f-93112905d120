create table t_hospital_list_display
(
    id                           bigint auto_increment comment '全局唯一标识' primary key,
    hospital_id                  bigint                                   not null,
    hospital_code                varchar(20)                              null comment '医院编码',
    hospital_name_color_type     tinyint(1)  default 0                    null comment '医院名称颜色类型 0-默认 1-自定义',
    hospital_name_hex_color_code varchar(7)                               null comment '医院名称16进制颜色码',
    logo_type                    tinyint(1)  default 0                    null comment 'logo类型 0-默认 1-自定义',
    display_corner_mark          tinyint(1)  default 0                    null comment '是否显示角标 0-不显示 1-显示',
    corner_mark_style            varchar(20)                              null comment '角标样式',
    display_tag                  tinyint(1)  default 0                    null comment '是否显示标签 0-不显示 1-显示',
    tags                         varchar(40)                              null comment '标签',
    recommended_doctor_id        varchar(100)                             null comment '推荐医生id',
    create_time                  datetime(3) default CURRENT_TIMESTAMP(3) not null comment '创建时间',
    update_time                  datetime(3)                              null on update CURRENT_TIMESTAMP(3)
)
    comment '医院显示配置';
