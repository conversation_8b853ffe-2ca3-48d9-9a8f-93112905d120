package com.ynhdkc.tenant.entity.constant;

import lombok.Getter;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/11/22 13:54
 */
@Getter
public enum DepartmentColumn {
    PARENT_ID(0, "上级科室", "parent_id"),
    FIRST_LETTER(1, "首字母", "first_letter"),
    SORT(2, "排序", "sort"),
    INTRODUCTION(3, "简介", "introduction"),
    ADDRESS_INTRO(4, "地址简介", "address_intro"),
    NAME(5, "名称", "name");

    DepartmentColumn(Integer code, String desc, String column) {
        this.code = code;
        this.desc = desc;
        this.column = column;
    }

    private final Integer code;
    private final String desc;
    private final String column;

    public static boolean contains(Integer code) {
        if (code == null) {
            return false;
        }
        for (DepartmentColumn value : DepartmentColumn.values()) {
            if (value.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

    public static DepartmentColumn of(Integer code) {
        if (code == null) {
            return null;
        }
        for (DepartmentColumn value : DepartmentColumn.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static List<Integer> getCodesByColumns(String updateDoctorExcludeColumns) {
        if (!StringUtils.hasText(updateDoctorExcludeColumns)) {
            return Collections.emptyList();
        }
        Set<String> columns = Arrays.stream(updateDoctorExcludeColumns.split(",")).collect(Collectors.toSet());
        return Arrays.stream(DepartmentColumn.values())
                .filter(column -> columns.contains(column.getColumn()))
                .map(DepartmentColumn::getCode)
                .collect(Collectors.toList());
    }

    public static String getMergedColumnsByCodes(List<Integer> updateDoctorExcludeColumns) {
        if (CollectionUtils.isEmpty(updateDoctorExcludeColumns)) {
            return "";
        }
        return updateDoctorExcludeColumns.stream()
                .map(DepartmentColumn::of)
                .filter(Objects::nonNull)
                .map(DepartmentColumn::getColumn)
                .collect(Collectors.joining(","));
    }
}
