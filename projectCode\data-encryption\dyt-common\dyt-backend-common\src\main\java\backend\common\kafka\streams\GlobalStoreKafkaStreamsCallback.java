package backend.common.kafka.streams;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.header.Headers;
import org.apache.kafka.common.serialization.Deserializer;
import org.apache.kafka.common.serialization.Serde;
import org.apache.kafka.common.serialization.Serializer;
import org.apache.kafka.streams.*;
import org.apache.kafka.streams.kstream.Consumed;
import org.apache.kafka.streams.processor.Processor;
import org.apache.kafka.streams.processor.ProcessorContext;
import org.apache.kafka.streams.processor.ProcessorSupplier;
import org.apache.kafka.streams.processor.StateStore;
import org.apache.kafka.streams.state.*;

import java.util.Map;

@Slf4j
public abstract class GlobalStoreKafkaStreamsCallback<K, V> implements KafkaStreamsCallback {

    @Override
    public void build(StreamsBuilder streamsBuilder) {
        log.info("wrap key value serede");
        Serde<K> keySerde = wrapKeySerde(keySerde());
        Serde<V> valueSerde = wrapValueSerde(valueSerde());
        Consumed<K, V> consumed = Consumed.with(keySerde, valueSerde).
                withOffsetResetPolicy(Topology.AutoOffsetReset.EARLIEST);
        StoreBuilder<KeyValueStore<K, V>> storeBuilder = Stores.keyValueStoreBuilder(
                Stores.inMemoryKeyValueStore(storeName()),
                keySerde,
                valueSerde);
        streamsBuilder.addGlobalStore(storeBuilder, sourceTopic(), consumed, getProcessorSupplier());
        log.info("add GlobalStore: {}, topic: {}", storeName(), sourceTopic());
    }

    private ProcessorSupplier<K, V> getProcessorSupplier() {
        return () -> new Processor<K, V>() {

            private StateStore stateStore;

            @Override
            public void init(ProcessorContext context) {
                stateStore = context.getStateStore(storeName());
            }

            @SuppressWarnings("unchecked")
            @Override
            public void process(K key, V value) {
                final KeyValueStore<K, V> kv = (KeyValueStore<K, V>) this.stateStore;
                final V old = kv.get(key);
                kv.put(key, value);
                processUpdate(key, value, old);
            }


            @Override
            public void close() {

            }
        };
    }

    private Serde<V> wrapValueSerde(Serde<V> valueSerde) {
        return new WrapSerde<>(valueSerde);
    }

    private Serde<K> wrapKeySerde(Serde<K> keySerde) {
        return new WrapSerde<>(keySerde);
    }

    public abstract void processUpdate(K key, V update, V old);

    public abstract String storeName();

    public abstract String sourceTopic();

    public abstract Serde<V> valueSerde();

    public abstract Serde<K> keySerde();

    @Override
    public void onStreamsAdded(String id, KafkaStreams streams) {
        final ReadOnlyKeyValueStore<K, V> store = streams.store(
                StoreQueryParameters.fromNameAndType(storeName(),
                        QueryableStoreTypes.keyValueStore()));
        initialize(store);
    }

    protected final void initialize(ReadOnlyKeyValueStore<K, V> store) {
        log.warn("initialize total size: {} of the store, for topic:{}",
                store.approximateNumEntries(), sourceTopic());
        try (KeyValueIterator<K, V> iterator = store.all()) {
            while (iterator.hasNext()) {
                KeyValue<K, V> next = iterator.next();
                if (next.key == null) {
                    log.warn("ignored, iterator stream with null key, for topic:{}", sourceTopic());
                    continue;
                }
                try {
                    doInitialize(next);
                } catch (Exception e) {
                    log.warn("ignored, do store initialization with {} for topic {} got an error:",
                            next, sourceTopic(), e);
                }
            }
        }
    }

    protected abstract void doInitialize(KeyValue<K, V> next);

    @Override
    public void onStreamsRemoved(String id, KafkaStreams streams) {
    }

    private static class WrapSerde<T> implements Serde<T> {
        final Serde<T> serde;
        final WrapSerializer<T> serializer;
        final WrapDeserializer<T> deserializer;

        public WrapSerde(Serde<T> valueSerde) {
            this.serde = valueSerde;
            this.serializer = new WrapSerializer<>(valueSerde.serializer());
            this.deserializer = new WrapDeserializer<>(valueSerde.deserializer());
        }

        @Override
        public Serializer<T> serializer() {
            return serializer;
        }

        @Override
        public Deserializer<T> deserializer() {
            return deserializer;
        }
    }

    private static class WrapDeserializer<T> implements Deserializer<T> {
        final Deserializer<T> deserializer;

        public WrapDeserializer(Deserializer<T> deserializer) {
            this.deserializer = deserializer;
        }

        @Override
        public void configure(Map<String, ?> configs, boolean isKey) {
            this.deserializer.configure(configs, isKey);
        }

        @Override
        public T deserialize(String topic, byte[] data) {
            try {
                return this.deserializer.deserialize(topic, data);
            } catch (Exception e) {
                log.warn("ignore deserialize error for topic:{}", topic, e);
                return null;
            }
        }

        @Override
        public T deserialize(String topic, Headers headers, byte[] data) {
            try {
                return this.deserializer.deserialize(topic, headers, data);
            } catch (Exception e) {
                log.warn("ignore deserialize error for topic:{}, headers:{}", topic, headers, e);
                return null;
            }
        }

        @Override
        public void close() {
            this.deserializer.close();
        }
    }

    private static class WrapSerializer<T> implements Serializer<T> {
        final Serializer<T> serializer;

        public WrapSerializer(Serializer<T> serializer) {
            this.serializer = serializer;
        }

        @Override
        public void configure(Map<String, ?> configs, boolean isKey) {
            this.serializer.configure(configs, isKey);
        }

        @Override
        public byte[] serialize(String topic, T data) {
            try {
                return this.serializer.serialize(topic, data);
            } catch (Exception e) {
                log.warn("serializer topic:{} error:", topic, e);
                return null;
            }
        }

        @Override
        public byte[] serialize(String topic, Headers headers, T data) {
            try {
                return this.serializer.serialize(topic, headers, data);
            } catch (Exception e) {
                log.warn("serializer topic:{} , headers: {} error:", topic, headers, e);
                return null;
            }
        }

        @Override
        public void close() {
            this.serializer.close();
        }
    }
}