package com.ynhdkc.tenant.service.backend.impl;

import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.dao.*;
import com.ynhdkc.tenant.entity.*;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.HospitalAreaPositionService;
import com.ynhdkc.tenant.tool.HospitalAreaPositionTreeUtil;
import com.ynhdkc.tenant.tool.convert.HospitalAreaPositionConverter;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/9/25 17:19
 */
@Service
@RequiredArgsConstructor
public class HospitalAreaPositionServiceImpl implements HospitalAreaPositionService {
    private final DepartmentQuery departmentQuery;
    private final BuildingQuery buildingQuery;
    private final FloorQuery floorQuery;
    private final AreaQuery areaQuery;
    private final HospitalAreaPositionQuery hospitalAreaPositionQuery;
    private final HospitalAreaPositionRepository hospitalAreaPositionRepository;

    private final HospitalAreaPositionTreeUtil hospitalAreaPositionTreeUtil;
    private final PageVoConvert pageVoConvert;

    @Override
    public BaseOperationResponse createLocation(LocationCreateReqDto request) {
        HospitalAreaPosition hospitalAreaPosition = createHospitalAreaPosition(request);
        hospitalAreaPositionRepository.create(hospitalAreaPosition);
        return new BaseOperationResponse("创建成功");
    }

    private HospitalAreaPosition createHospitalAreaPosition(LocationCreateReqDto request) {
        HospitalAreaPosition position;
        switch (request.getType()) {
            case 0:
                position = hospitalAreaPositionQuery.queryByDepartmentId(request.getDepartmentId());
                if (null != position) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "科室已经绑定位置信息");
                }
                break;
            case 9:
                position = hospitalAreaPositionQuery.queryByUniqueKey(request.getBuildingId(), request.getFloorId(), request.getAreaId(), 9, request.getName());
                if (null != position) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "位置已经存在");
                }
                break;
            default:
                throw new BizException(HttpStatus.BAD_REQUEST, "类型错误");
        }
        position = new HospitalAreaPosition();
        position.setTenantId(request.getTenantId());
        position.setHospitalId(request.getHospitalId());
        position.setHospitalAreaId(request.getHospitalAreaId());
        position.setType(request.getType());
        position.setName(request.getName());
        position.setBuildingId(request.getBuildingId());
        position.setFloorId(request.getFloorId());
        position.setAreaId(request.getAreaId());
        position.setDepartmentId(request.getDepartmentId());
        position.setSort(request.getSort());
        position.setStatus(request.getStatus());
        position.setTriageDeskAddress(request.getTriageDeskAddress());
        importBoundResourceNames(position);
        return position;
    }

    @Override
    public BaseOperationResponse deleteLocation(Long locationId) {
        HospitalAreaPosition hospitalAreaPosition = hospitalAreaPositionQuery.queryById(locationId);
        if (null == hospitalAreaPosition) {
            throw new BizException(HttpStatus.NOT_FOUND, "位置不存在");
        }
        hospitalAreaPositionRepository.delete(locationId);
        return new BaseOperationResponse("删除成功");
    }

    @Override
    public HospitalAreaPositionQueryRespDto queryLocation(HospitalAreaPositionQueryReqDto request) {
        try (Page<HospitalAreaPosition> page = hospitalAreaPositionQuery.pageQueryPositions(new HospitalAreaPositionQuery.PositionQueryOption(request.getCurrentPage(), request.getPageSize())
                .setHospitalAreaId(request.getHospitalAreaId())
                .setBuildingId(request.getBuildingId())
                .setFloorId(request.getFloorId())
                .setAreaId(request.getAreaId())
                .setType(request.getType())
                .setName(request.getName())
        )) {
            return pageVoConvert.toPageVo(page, HospitalAreaPositionQueryRespDto.class, HospitalAreaPositionConverter::toVo);
        }
    }

    @Override
    public HospitalAreaPositionTreeQueryRespDto queryPositionTree(HospitalAreaPositionTreeQueryReqDto request) {
        List<Building> buildings = buildingQuery.queryBuildingsByHospitalAreaId(request.getHospitalAreaId());
        List<Floor> floors = floorQuery.queryFloorsByHospitalAreaId(request.getHospitalAreaId());
        List<Area> areas = areaQuery.queryAreasByHospitalAreaId(request.getHospitalAreaId());

        List<HospitalAreaPosition> departmentPositions = hospitalAreaPositionQuery.pageQueryPositions(new HospitalAreaPositionQuery.PositionQueryOption(1, Integer.MAX_VALUE)
                .setHospitalAreaId(request.getHospitalAreaId())
                .setBuildingId(request.getBuildingId())
                .setFloorId(request.getFloorId())
                .setAreaId(request.getAreaId())
                .setName(request.getDepartmentName())
        );

        List<BuildingPositionVo> buildingPositionVos = hospitalAreaPositionTreeUtil.buildTree(buildings, floors, areas, departmentPositions);

        return new HospitalAreaPositionTreeQueryRespDto()
                .hospitalAreaId(request.getHospitalAreaId())
                .children(buildingPositionVos);
    }

    @Override
    public BaseOperationResponse updateLocation(Long locationId, LocationUpdateReqDto request) {
        HospitalAreaPosition hospitalAreaPosition = hospitalAreaPositionQuery.queryById(locationId);
        if (null == hospitalAreaPosition) {
            throw new BizException(HttpStatus.NOT_FOUND, "位置不存在");
        }
        hospitalAreaPosition.setBuildingId(request.getBuildingId());
        hospitalAreaPosition.setFloorId(request.getFloorId());
        hospitalAreaPosition.setAreaId(request.getAreaId());
        hospitalAreaPosition.setType(request.getType());
        hospitalAreaPosition.setName(request.getName());
        hospitalAreaPosition.setDepartmentId(request.getDepartmentId());
        hospitalAreaPosition.setStatus(request.getStatus());
        hospitalAreaPosition.setSort(request.getSort());
        hospitalAreaPosition.setTriageDeskAddress(request.getTriageDeskAddress());
        importBoundResourceNames(hospitalAreaPosition);

        hospitalAreaPositionRepository.update(hospitalAreaPosition);
        return new BaseOperationResponse("更新成功");
    }

    private void importBoundResourceNames(HospitalAreaPosition hospitalAreaPosition) {
        if (hospitalAreaPosition.getType() == 0) {
            Department department = departmentQuery.queryDepartmentById(hospitalAreaPosition.getDepartmentId());
            if (null == department) {
                throw new BizException(HttpStatus.NOT_FOUND, "科室不存在");
            }
            if (null == hospitalAreaPosition.getName()) {
                hospitalAreaPosition.setName(department.getName());
            }
        }
        if (null != hospitalAreaPosition.getAreaId()) {
            Area area = areaQuery.queryAreaById(hospitalAreaPosition.getAreaId());
            if (null == area) {
                throw new BizException(HttpStatus.NOT_FOUND, "区域不存在");
            }
            Building building = buildingQuery.queryBuildingById(hospitalAreaPosition.getBuildingId());
            if (null == building) {
                throw new BizException(HttpStatus.NOT_FOUND, "大楼不存在");
            }
            Floor floor = floorQuery.queryFloorById(hospitalAreaPosition.getFloorId());
            if (null == floor) {
                throw new BizException(HttpStatus.NOT_FOUND, "楼层不存在");
            }
            if (!area.getBuildingId().equals(hospitalAreaPosition.getBuildingId()) || !area.getFloorId().equals(hospitalAreaPosition.getFloorId())) {
                throw new BizException(HttpStatus.BAD_REQUEST, "区域不属于该大楼或楼层");
            }
            hospitalAreaPosition.setBuildingName(building.getName());
            hospitalAreaPosition.setFloorName(floor.getName());
            hospitalAreaPosition.setAreaName(area.getName());
        }

        if (null != hospitalAreaPosition.getFloorId()) {
            Floor floor = floorQuery.queryFloorById(hospitalAreaPosition.getFloorId());
            if (null == floor) {
                throw new BizException(HttpStatus.NOT_FOUND, "楼层不存在");
            }
            Building building = buildingQuery.queryBuildingById(hospitalAreaPosition.getBuildingId());
            if (null == building) {
                throw new BizException(HttpStatus.NOT_FOUND, "大楼不存在");
            }
            if (!floor.getBuildingId().equals(hospitalAreaPosition.getBuildingId())) {
                throw new BizException(HttpStatus.BAD_REQUEST, "楼层不属于该大楼");
            }
            hospitalAreaPosition.setBuildingName(building.getName());
            hospitalAreaPosition.setFloorName(floor.getName());
            return;
        }

        if (null != hospitalAreaPosition.getBuildingId()) {
            Building building = buildingQuery.queryBuildingById(hospitalAreaPosition.getBuildingId());
            if (null == building) {
                throw new BizException(HttpStatus.NOT_FOUND, "大楼不存在");
            }
            hospitalAreaPosition.setBuildingName(building.getName());
        }
    }
}
