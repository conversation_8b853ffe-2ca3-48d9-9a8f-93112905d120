package com.ynhdkc.tenant.enums;/**
 * <AUTHOR>
 * @date 2024/06/28/10:13
 */

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/28 10:13
 **/
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum TenantUserStatus {
    /**
     * 租户状态
     */
    NORMAL(1, "正常"),
    DISABLE(2, "禁用"),
    LOCK(3, "锁定");

    private Integer value;
    private String desc;

}
