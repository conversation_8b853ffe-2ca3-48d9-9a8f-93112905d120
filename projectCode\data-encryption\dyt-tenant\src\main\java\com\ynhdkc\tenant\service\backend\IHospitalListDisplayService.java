package com.ynhdkc.tenant.service.backend;

import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.entity.HospitalListDisplay;
import com.ynhdkc.tenant.model.*;

/**
 * <p>
 * 医院显示配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
public interface IHospitalListDisplayService {

    static HospitalListDisplayVo toVo(HospitalListDisplay entity) {
        if (entity == null) {
            return null;
        }
        HospitalListDisplayVo vo = new HospitalListDisplayVo();
        vo.setId(entity.getId());
        vo.setHospitalId(entity.getHospitalId());
        vo.setHospitalNameHexColorCode(entity.getHospitalNameHexColorCode());
        vo.setHospitalNameColorType(entity.getHospitalNameColorType());
        vo.setLogoType(entity.getLogoType());
        vo.setDisplayCornerMark(entity.getDisplayCornerMark());
        vo.setCornerMarkStyle(entity.getCornerMarkStyle());
        vo.setDisplayTag(entity.getDisplayTag());
        vo.setTags(entity.getTags());
        vo.setRecommendedDoctorId(entity.getRecommendedDoctorId());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdateTime(entity.getUpdateTime());

        return vo;
    }

    static CustomerHospitalListDisplayVo toCustomerHospitalDisplayVo(HospitalListDisplay entity) {
        if (entity == null) {
            return null;
        }
        CustomerHospitalListDisplayVo vo = new CustomerHospitalListDisplayVo();
        vo.setHospitalId(entity.getHospitalId());
        vo.setHospitalNameHexColorCode(entity.getHospitalNameHexColorCode());
        vo.setHospitalNameColorType(entity.getHospitalNameColorType());
        vo.setLogoType(entity.getLogoType());
        vo.setDisplayCornerMark(entity.getDisplayCornerMark());
        vo.setCornerMarkStyle(entity.getCornerMarkStyle());
        vo.setDisplayTag(entity.getDisplayTag());
        vo.setTags(entity.getTags());

        return vo;
    }

    static <T extends UpdateHospitalListDisplayReqDto> HospitalListDisplay toEntity(T dto) {
        if (dto == null) {
            return null;
        }
        HospitalListDisplay entity = new HospitalListDisplay();

        entity.setHospitalId(dto.getHospitalId());
        entity.setHospitalNameHexColorCode(dto.getHospitalNameHexColorCode());
        entity.setHospitalNameColorType(dto.getHospitalNameColorType());
        entity.setDisplayCornerMark(dto.getDisplayCornerMark());
        entity.setLogoType(dto.getLogoType());
        entity.setCornerMarkStyle(dto.getCornerMarkStyle());
        entity.setDisplayTag(dto.getDisplayTag());
        entity.setTags(dto.getTags());
        entity.setRecommendedDoctorId(dto.getRecommendedDoctorId());

        return entity;
    }

    HospitalListDisplayVo createHospitalListDisplay(CreateHospitalListDisplayReqDto dto);

    BaseOperationResponse deleteHospitalListDisplay(Long id);

    HospitalListDisplayVo getHospitalListDisplayDetail(Long id);

    HospitalListDisplayPageVo getHospitalListDisplayPage(Integer currentPage, Integer pageSize);

    HospitalListDisplayVo updateHospitalListDisplay(Long id, UpdateHospitalListDisplayReqDto hospitalListDisplayDto);
}
