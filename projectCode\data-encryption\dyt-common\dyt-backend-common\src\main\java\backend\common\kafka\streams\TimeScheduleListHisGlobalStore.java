package backend.common.kafka.streams;

import backend.common.constants.KafkaTopicConfig;
import backend.common.entity.dto.hisgateway.response.TimeScheduleItem;
import backend.common.entity.dto.hisgateway.response.TimeScheduleResponse;
import backend.common.util.MessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.serialization.Serde;
import org.apache.kafka.common.serialization.Serdes;
import org.apache.kafka.streams.KeyValue;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.Nullable;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

@Slf4j
@Configuration
@ConditionalOnProperty(name = "backend.kafka.global-store.info.time-schedule-list-his", havingValue = "true")
public class TimeScheduleListHisGlobalStore extends GlobalStoreKafkaStreamsCallback<String, String> {

    //    public static final JsonSerde<String> VALUE_SERDE = new JsonSerde<>(String.class).noTypeInfo();
    public static final Serdes.StringSerde VALUE_SERDE = new Serdes.StringSerde();
    private final ConcurrentMap<String, TimeScheduleResponse> key2value = new ConcurrentHashMap<>();

    @Override
    public void processUpdate(String key, String update, String old) {
        log.info("分时段 global-store 有数据需要更新 key:{}, update:{}, old:{}", key, update, old);
        if (update == null && old == null) {
            return;
        }
        if (update == null) {
            key2value.remove(key);
        } else {
            key2value.put(key, parseResponse(update));
        }
    }

    @Override
    public String storeName() {
        return "time-schedule-list-his";
    }

    /*@Override
    public String sourceTopic() {
        return KafkaTopicConfig.DOCTOR_SCHEDULE_LIST;
    }*/
    @Override
    public String sourceTopic() {
        return KafkaTopicConfig.TimeSchedule.RESPONSE_TOPIC_NAME;
    }

    @Override
    public Serde<String> valueSerde() {
        return VALUE_SERDE;
    }

    @Override
    public Serde<String> keySerde() {
        return Serdes.String();
    }

    @Override
    protected void doInitialize(KeyValue<String, String> next) {
        String rawValue = next.value;
        if (ObjectUtils.isEmpty(rawValue)) {
            return;
        }
        key2value.put(next.key, parseResponse(rawValue));
    }

    /**
     * 获取医生排班列表
     *
     * @param key 组成 hospital_code_department_id_doctor_id_schedule_id
     */
    public @Nullable
    TimeScheduleResponse getTimeScheduleList(String key) {
        log.info("分时段 global-store 通过key:{},获取分时段数据:{}", key, key2value.get(key));
        return key2value.get(key);
    }

    private TimeScheduleResponse parseResponse(String rawValue) {
        try {
            final List<TimeScheduleItem> timeScheduleItems = MessageUtil.json2ListObj(rawValue, TimeScheduleItem.class);
            final TimeScheduleResponse timeScheduleResponse = new TimeScheduleResponse();
            timeScheduleResponse.setResult(timeScheduleItems);
            return timeScheduleResponse;
//            return MessageUtil.json2Obj(rawValue, TimeScheduleResponse.class);
        } catch (Exception e) {
            log.error("分时段 global-store json2obj 出现错误,data:{},error:{},stack{}", rawValue, e.getMessage(), e.getStackTrace());
            return null;
        }
    }

}
