package com.ynhdkc.tenant.dao;

import backend.common.util.BaseQueryOption;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.entity.Floor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-03 10:46
 */
public interface FloorQuery {
    Floor queryFloorById(Long floorId);

    Page<Floor> pageQuery(FloorQueryOption option);

    List<Floor> queryFloorsByHospitalAreaId(Long hospitalAreaId);

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Accessors(chain = true)
    class FloorQueryOption extends BaseQueryOption {
        public FloorQueryOption(Integer currentPage, Integer pageSize) {
            super(currentPage, pageSize);
        }

        private Long tenantId;
        private Long hospitalId;
        private Long hospitalAreaId;
        private Long buildingId;
        private String name;
        private Integer status;

        private Collection<Long> includeTenantIds;
        private Collection<Long> excludeTenantIds;
        private Collection<Long> includeHospitalIds;
        private Collection<Long> excludeHospitalIds;
        private Collection<Long> includeHospitalAreaIds;
        private Collection<Long> excludeHospitalAreaIds;
        private Collection<Long> includeBuildingIds;
    }
}
