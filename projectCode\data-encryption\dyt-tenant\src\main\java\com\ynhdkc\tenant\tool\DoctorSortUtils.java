package com.ynhdkc.tenant.tool;

import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.model.CustomerAllScheduleDoctorDetailVo;
import com.ynhdkc.tenant.model.CustomerGroupedDoctorDetailVo;
import net.sourceforge.pinyin4j.PinyinHelper;
import org.springframework.util.ObjectUtils;

import java.util.Comparator;
import java.util.List;

public class DoctorSortUtils {

    private DoctorSortUtils() {
    }

    /**
     * 医生职务级别-姓名拼音排序
     */
    public static void reSortDoctor(List<Doctor> doctors) {
        doctors.sort(Comparator.comparing((Doctor d) -> {
                    if (StringUtils.isEmpty(d.getRankDictValue())) {
                        return "\uffff";
                    }
                    return d.getRankDictValue();
                }).thenComparing((Doctor d) -> {

                    String pinyin = ObjectUtils.isEmpty(d.getName()) ? "" : getPinyin(d.getName());
                    return StringUtils.isEmpty(pinyin) ? "" : pinyin;
                })
                .thenComparing((Doctor d) -> {

                    if (d.getHonor() == null) {
                        return "";
                    }
                    if ("普通号".equals(d.getHonor()) || "vip号".equals(d.getHonor())) {
                        return "";
                    }
                    return d.getHonor();
                }));
    }

    /**
     * 医生职务级别-先按sort排序姓名拼音排序
     */
    public static void reSortAddSortDoctor(List<Doctor> doctors) {
        doctors.sort(Comparator.comparing((Doctor d) -> d.getSort() == null ? 0 : -d.getSort())
                .thenComparing((Doctor d) -> {
                    if (StringUtils.isEmpty(d.getRankDictValue())) {
                        return "\uffff";
                    }
                    return d.getRankDictValue();
                }).thenComparing((Doctor d) -> {

                    String pinyin = ObjectUtils.isEmpty(d.getName()) ? "" : getPinyin(d.getName());
                    return StringUtils.isEmpty(pinyin) ? "" : pinyin;
                })
                .thenComparing((Doctor d) -> {

                    if (d.getHonor() == null) {
                        return "";
                    }
                    if ("普通号".equals(d.getHonor()) || "vip号".equals(d.getHonor())) {
                        return "";
                    }
                    return d.getHonor();
                }));
    }


    public static void sortDoctorWithSort(List<Doctor> doctors) {
        doctors.sort(Comparator.comparing((Doctor d) -> {
                            if (StringUtils.isEmpty(d.getRankDictValue())) {
                                return "\uffff";
                            }
                            return d.getRankDictValue();
                        })
                        .thenComparing((Doctor d) -> d.getSort() == null ? 0 : -d.getSort())
                        .thenComparing((Doctor d) -> com.ynhdkc.tenant.util.DoctorUtils.getTitleValue(d.getHonor()))
                        .thenComparing((Doctor d) -> {
                            String pinyin = ObjectUtils.isEmpty(d.getName()) ? "" : getPinyin(d.getName());
                            return StringUtils.isEmpty(pinyin) ? "" : pinyin;
                        })
        );
    }

    public static void sortDoctorWithVO(List<CustomerAllScheduleDoctorDetailVo> doctors) {
        doctors.sort(Comparator.comparing((CustomerAllScheduleDoctorDetailVo d) -> {
                            if (ObjectUtils.isEmpty(d.getRankDictValue())) {
                                return "\uffff";
                            }
                            return d.getRankDictValue().get(0);
                        })
                        .thenComparing((CustomerAllScheduleDoctorDetailVo d) -> d.getSort() == null ? 0 : -d.getSort())
                        .thenComparing((CustomerAllScheduleDoctorDetailVo d) -> com.ynhdkc.tenant.util.DoctorUtils.getTitleValue(d.getHonor()))
                        .thenComparing((CustomerAllScheduleDoctorDetailVo d) -> {
                            String pinyin = ObjectUtils.isEmpty(d.getName()) ? "" : getPinyin(d.getName());
                            return StringUtils.isEmpty(pinyin) ? "" : pinyin;
                        })
        );
    }

    public static void sortDoctorWithGroupVO(List<CustomerGroupedDoctorDetailVo> doctors) {
        doctors.sort(Comparator.comparing((CustomerGroupedDoctorDetailVo d) -> {
                            if (ObjectUtils.isEmpty(d.getRankDictValue())) {
                                return "\uffff";
                            }
                            return d.getRankDictValue().get(0);
                        })
                        .thenComparing((CustomerGroupedDoctorDetailVo d) -> d.getSort() == null ? 0 : -d.getSort())
                        .thenComparing((CustomerGroupedDoctorDetailVo d) -> com.ynhdkc.tenant.util.DoctorUtils.getTitleValue(d.getHonor()))
                        .thenComparing((CustomerGroupedDoctorDetailVo d) -> {
                            String pinyin = ObjectUtils.isEmpty(d.getName()) ? "" : getPinyin(d.getName());
                            return StringUtils.isEmpty(pinyin) ? "" : pinyin;
                        })
        );
    }

    public static void sortDescAndNameAsc(List<Doctor> doctors) {
        doctors.sort(Comparator.comparing(Doctor::getSort).reversed()
                .thenComparing(x -> {
                    String pinyin = StringUtils.hasText(x.getName()) ? getPinyin(x.getName()) : "";
                    return StringUtils.hasText(pinyin) ? pinyin : "";
                }));
    }


    public static void levelDescAndSortDescAndNameAsc(List<Doctor> doctors) {
        doctors.sort(Comparator.comparing((Doctor d) -> com.ynhdkc.tenant.util.DoctorUtils.getTitleValue(d.getHonor()))
                .thenComparing((Doctor d) -> d.getSort() == null ? 0 : -d.getSort())
                .thenComparing(x -> {
                    String pinyin = StringUtils.hasText(x.getName()) ? getPinyin(x.getName()) : "";
                    return StringUtils.hasText(pinyin) ? pinyin : "";
                })
        );
    }

    public static void levelDescAndNameAsc(List<Doctor> doctors) {
        doctors.sort(Comparator.comparing((Doctor d) -> com.ynhdkc.tenant.util.DoctorUtils.getTitleValue(d.getHonor()))
                .thenComparing(x -> {
                    String pinyin = StringUtils.hasText(x.getName()) ? getPinyin(x.getName()) : "";
                    return StringUtils.hasText(pinyin) ? pinyin : "";
                })
        );
    }

    public static void levelDescAndSortDescAndNameAscGroupDoc(List<CustomerGroupedDoctorDetailVo> doctors) {
        doctors.sort(Comparator.comparing((CustomerGroupedDoctorDetailVo d) -> com.ynhdkc.tenant.util.DoctorUtils.getTitleValue(d.getHonor()))
                .thenComparing((CustomerGroupedDoctorDetailVo d) -> d.getSort() == null ? 0 : -d.getSort())
                .thenComparing(x -> {
                    String pinyin = StringUtils.hasText(x.getName()) ? getPinyin(x.getName()) : "";
                    return StringUtils.hasText(pinyin) ? pinyin : "";
                })
        );
    }

    public static void levelDescAndNameAscGroupDoc(List<CustomerGroupedDoctorDetailVo> doctors) {
        doctors.sort(Comparator.comparing((CustomerGroupedDoctorDetailVo d) -> com.ynhdkc.tenant.util.DoctorUtils.getTitleValue(d.getHonor()))
                .thenComparing(x -> {
                    String pinyin = StringUtils.hasText(x.getName()) ? getPinyin(x.getName()) : "";
                    return StringUtils.hasText(pinyin) ? pinyin : "";
                })
        );
    }

    private static String chineseWordsToPinyin(String chinese) {
        StringBuilder pinyin = new StringBuilder();
        for (char c : chinese.toCharArray()) {
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c);
            if (pinyinArray != null && pinyinArray.length > 0) {
                pinyin.append(pinyinArray[0]);
            } else {
                pinyin.append(c);
            }
        }
        return pinyin.toString();
    }

    public static String getPinyin(String chinese) {
        if (StringUtils.isEmpty(chinese)) {
            return "";
        }
        return chineseWordsToPinyin(chinese);
    }
}
