package backend.common.cdc.listener;

import backend.common.cdc.CDCListener;
import backend.common.cdc.CDCTopics;
import backend.common.cdc.ResourceChangeCapture;
import backend.common.cdc.dto.CDCDoctor;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;

public class CDCDoctorListener extends CDCListener<CDCDoctor> {

    public CDCDoctorListener(ResourceChangeCapture<CDCDoctor> capture) {
        super(capture);
    }

    @KafkaListener(topics = CDCTopics.BACKEND_DOCTOR, groupId = "${spring.application.name}")
    public void kafkaListener(ConsumerRecord<String, byte[]> msg) throws Exception {
        this.doWork(msg, CDCDoctor.class);
    }
}
