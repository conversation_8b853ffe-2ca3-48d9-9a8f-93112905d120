package com.ynhdkc.tenant.api.customer;

import com.ynhdkc.tenant.handler.CustomerDictApi;
import com.ynhdkc.tenant.model.DictLabelPageVo;
import com.ynhdkc.tenant.service.backend.DictLabelService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/7/19 15:41
 */
@Api(tags = "CustomerDict")
@RestController
@RequiredArgsConstructor
@Slf4j
public class CustomerDictController implements CustomerDictApi {
    private final DictLabelService dictLabelService;

    @Override
    @Cacheable(value = "dictLabelCache", key = "#name + '_' + #dictTypeName + '_' + #sortBy + '_' + #currentPage+'_'+#pageSize",
            unless = "#result == null", cacheManager = "cacheManager")
    public ResponseEntity<DictLabelPageVo> getDictLabelList(String name, String dictTypeName, String sortBy, Integer currentPage, Integer pageSize) {
        log.info("name:{},dictTypeName:{},sortBy:{},currentPage:{},pageSize:{}", name, dictTypeName, sortBy, currentPage, pageSize);
        return ResponseEntity.ok(dictLabelService.getDictLabelList(name, dictTypeName, sortBy, null, currentPage, pageSize));
    }
}
