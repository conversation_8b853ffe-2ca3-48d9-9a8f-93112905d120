package com.ynhdkc.tenant.service.backend.impl;

import backend.common.exception.BizException;
import cn.hutool.core.util.StrUtil;
import com.ynhdkc.tenant.dao.mapper.HospitalListIndexMapper;
import com.ynhdkc.tenant.dao.mapper.HospitalListRuleMapper;
import com.ynhdkc.tenant.entity.HospitalListIndex;
import com.ynhdkc.tenant.entity.HospitalListRule;
import com.ynhdkc.tenant.model.HospitalListIndexVo;
import com.ynhdkc.tenant.model.SubmitHospitalListIndexReqDto;
import com.ynhdkc.tenant.service.backend.IHospitalListIndexService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 医院列表序列 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@Service
@RequiredArgsConstructor
public class HospitalListIndexServiceImpl implements IHospitalListIndexService {

    private final HospitalListIndexMapper hospitalListIndexMapper;
    private final HospitalListRuleMapper hospitalListRuleMapper;

    @Override
    public List<HospitalListIndexVo> getHospitalListIndex() {
        List<HospitalListIndex> hospitalListIndexList = hospitalListIndexMapper.selectAll();
        List<HospitalListIndexVo> vos = hospitalListIndexList.stream().map(IHospitalListIndexService::toVo).collect(Collectors.toList());
        List<Long> ruleIds = vos.stream().map(HospitalListIndexVo::getRuleId).collect(Collectors.toList());
        if (!ruleIds.isEmpty()) {
            List<HospitalListRule> rules = hospitalListRuleMapper.selectByIds(StrUtil.join(",", ruleIds));
            vos.forEach(vo -> rules.stream()
                    .filter(r -> r.getId().equals(vo.getRuleId()))
                    .findFirst()
                    .ifPresent(rule -> vo.setRuleName(rule.getName())));
        }
        return vos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<HospitalListIndexVo> submitHospitalListIndex(List<SubmitHospitalListIndexReqDto> hospitalListIndexDtoList) {
        if (hospitalListIndexDtoList.stream().anyMatch(dto -> Objects.isNull(dto.getRuleId()))) {
            throw new BizException(HttpStatus.BAD_REQUEST, "规则id不能为空");
        }

        List<HospitalListIndex> oldIndex = hospitalListIndexMapper.selectAll();
        if (!CollectionUtils.isEmpty(oldIndex)) {
            List<Long> ids = oldIndex.stream().map(HospitalListIndex::getId).collect(Collectors.toList());
            int deleteEffectCount = hospitalListIndexMapper.deleteByIds(StrUtil.join(",", ids));
            if (deleteEffectCount == 0) {
                throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "更新医院列表序列失败");
            }
        }
        // 获取hospitalListIndexDtoList中最大的indexOrder
        Integer maxIndexOrder = hospitalListIndexDtoList.stream()
                .filter(dto -> Objects.nonNull(dto.getIndexOrder()))
                .map(SubmitHospitalListIndexReqDto::getIndexOrder)
                .max(Integer::compareTo)
                .orElse(0);
        AtomicInteger indexOrder = new AtomicInteger(maxIndexOrder);
        List<HospitalListIndex> newOrder = hospitalListIndexDtoList.stream()
                .map(dto -> {
                    HospitalListIndex entity = IHospitalListIndexService.toEntity(dto);
                    if (Objects.isNull(entity.getIndexOrder())) {
                        entity.setIndexOrder(indexOrder.incrementAndGet());
                    }
                    return entity;
                })
                .collect(Collectors.toList());
        if (!newOrder.isEmpty()) {
            int insertEffectCount = hospitalListIndexMapper.insertList(newOrder);
            if (insertEffectCount == 0) {
                throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "更新医院列表序列失败");
            }
        }
        return newOrder.stream().map(IHospitalListIndexService::toVo).collect(Collectors.toList());
    }
}
