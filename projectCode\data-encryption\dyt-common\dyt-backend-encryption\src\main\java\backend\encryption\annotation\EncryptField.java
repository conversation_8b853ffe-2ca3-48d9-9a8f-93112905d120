package backend.encryption.annotation;

import java.lang.annotation.*;

/**
 * 敏感字段加密注解
 * 用于标记需要在数据库中加密存储的字段
 * 支持渐进式影子字段迁移策略
 *
 * 使用示例：
 * <pre>
 * {@code
 * @Entity
 * public class User {
 *     // 传统加密方式
 *     @EncryptField
 *     private String phone;
 *
 *     // 影子字段迁移方式
 *     @EncryptField(shadowField = "phone_encrypted", migrationStrategy = MigrationStrategy.SHADOW_PRIORITY)
 *     private String email;
 *
 *     // 影子字段（数据库字段，不在实体中使用）
 *     private String phone_encrypted;
 * }
 * }
 * </pre>
 *
 * <AUTHOR> Backend Team
 * @version 1.1-SNAPSHOT
 * @since 2024-06-24
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface EncryptField {

    /**
     * 字段描述，用于日志记录和调试
     * @return 字段描述
     */
    String description() default "";

    /**
     * 是否启用加密，默认为true
     * 可以通过配置动态控制是否加密
     * @return 是否启用加密
     */
    boolean enabled() default true;

    /**
     * 影子字段名称
     * 用于渐进式迁移，指定对应的加密字段名
     * 如果为空，则使用传统的直接加密方式
     * @return 影子字段名称
     */
    String shadowField() default "";

    /**
     * 迁移策略
     * 如果配置了全局策略，则优先使用全局策略
     * @return 迁移策略
     */
    MigrationStrategy migrationStrategy() default MigrationStrategy.DIRECT_ENCRYPT;

    /**
     * 策略配置键
     * 用于从application.yml中读取策略配置
     * 格式：encryption.migration.strategies.{strategyKey}
     * @return 策略配置键
     */
    String strategyKey() default "";

    /**
     * 数据版本号
     * 用于标识数据的加密版本，便于后续升级和回滚
     * @return 版本号
     */
    int version() default 1;

    /**
     * 加密算法类型
     * @return 算法类型
     */
    AlgorithmType algorithm() default AlgorithmType.AES_GCM;

    /**
     * 迁移策略枚举
     * 定义了不同的数据迁移和加密策略
     */
    enum MigrationStrategy {
        /**
         * 直接加密策略
         * 新数据直接加密存储，适用于新系统或可接受停服的场景
         */
        DIRECT_ENCRYPT,

        /**
         * 明文优先策略
         * 当前字段存储明文，影子字段存储密文
         * 适用于迁移初期，保证系统稳定性
         */
        PLAINTEXT_PRIORITY,

        /**
         * 影子字段优先策略
         * 优先从影子字段读取加密数据，当前字段作为备份
         * 适用于迁移中期，逐步切换到加密存储
         */
        SHADOW_PRIORITY,

        /**
         * 仅影子字段策略
         * 只使用影子字段存储加密数据，当前字段不再使用
         * 适用于迁移完成后，完全使用加密存储
         */
        SHADOW_ONLY
    }

    /**
     * 加密算法类型枚举
     */
    enum AlgorithmType {
        /**
         * AES-GCM算法，默认选择
         * 提供认证加密，安全性高，性能好
         */
        AES_GCM,

        /**
         * 国密SM2算法
         * 符合国家密码标准，适用于有国密要求的场景
         */
        SM2,

        /**
         * 国密SM4算法
         * 对称加密算法，适用于大量数据加密
         */
        SM4
    }
}
