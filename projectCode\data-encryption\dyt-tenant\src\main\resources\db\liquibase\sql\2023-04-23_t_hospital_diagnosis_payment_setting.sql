create table t_hospital_diagnosis_payment_setting
(
    id                     bigint auto_increment comment '全局唯一标识'
        primary key,
    function_id            bigint                                   null comment '功能 ID',
    tenant_id              bigint                                   not null comment '租户 ID',
    hospital_id            bigint                                   not null comment '医院 ID',
    hospital_area_id       bigint                                   not null comment '院区 ID',
    hospital_code          varchar(20)                              null comment '医院编码',
    support_merger_payment tinyint     default 0                    not null comment '是否支持合并支付',
    support_online_refund  tinyint     default 0                    not null comment '是否支持在线退款',
    refund_today           tinyint     default 0                    not null comment '是否允许当天退款：0，不允许；1，允许；',
    stop_refund_time       time                                     not null comment '停止退款时间',
    support_invoice        tinyint     default 1                    not null comment '是否支持开发票',
    selected_payments      varchar(200)                             null comment '已选支付方式',
    payment_information    text                                     null comment '缴费信息',
    create_time            datetime(3) default CURRENT_TIMESTAMP(3) not null,
    update_time            datetime(3)                              null on update CURRENT_TIMESTAMP(3),
    constraint uidx_limit_single_setting
        unique (tenant_id, hospital_id, hospital_area_id)
)
    comment '医院门诊缴费设置';

insert into t_hospital_diagnosis_payment_setting
    (id, tenant_id, hospital_id, hospital_area_id, stop_refund_time)
VALUES (1, 1, 1, 2, '23:59:59'),
       (2, 1, 1, 3, '23:59:59'),
       (3, 2, 4, 5, '23:59:59'),
       (4, 2, 4, 6, '23:59:59'),
       (5, 2, 4, 7, '23:59:59');