package com.ynhdkc.tenant.dao.impl;

import backend.common.util.MybatisUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ynhdkc.tenant.dao.TenantQuery;
import com.ynhdkc.tenant.dao.mapper.TenantMapper;
import com.ynhdkc.tenant.entity.Tenant;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-25 21:49
 */
@Repository
@RequiredArgsConstructor
public class TenantQueryImpl implements TenantQuery {
    private final TenantMapper tenantMapper;

    @Override
    public Tenant queryTenantById(Long tenantId) {
        return tenantMapper.selectByPrimaryKey(tenantId);
    }

    @Override
    public Page<Tenant> pageQueryTenant(TenantQueryOption option) {
        try (final Page<Tenant> page = PageHelper.startPage(option.getCurrentPage(), option.getPageSize())) {
            return page.doSelectPage(() -> tenantMapper.selectByExample(Tenant.class, sql -> {
                sql.defGroup(condition -> {
                    if (null != option.getName()) {
                        condition.andLike(Tenant::getName, MybatisUtil.likeBoth(option.getName()));
                    }
                    if (null != option.getContactPerson()) {
                        condition.andLike(Tenant::getContact, MybatisUtil.likeBoth(option.getContactPerson()));
                    }
                    if (null != option.getContactPhoneNumber()) {
                        condition.andLike(Tenant::getContactPhoneNumber, MybatisUtil.likeLeft(option.getContactPhoneNumber()));
                    }

                    if (!CollectionUtils.isEmpty(option.getIncludeIds())) {
                        condition.andIn(Tenant::getId, option.getIncludeIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getExcludeIds())) {
                        condition.andNotIn(Tenant::getId, option.getExcludeIds());
                    }
                });
                sql.builder(builder -> builder.orderByDesc(Tenant::getId));
            }));
        }
    }

    @Override
    public Collection<Tenant> queryByIds(Set<Long> tenants) {
        return tenantMapper.selectByExample2(Tenant.class, sql -> sql.andIn(Tenant::getId, tenants));
    }

    @Override
    public Integer countByName(String name) {
        return tenantMapper.selectCountByExample2(Tenant.class, sql -> sql.andEqualTo(Tenant::getName, name));
    }
}
