package com.ynhdkc.tenant.service.backend.impl;

import backend.common.domain.BaseEntity;
import backend.common.enums.HospitalCode;
import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import backend.common.util.ObjectsUtils;
import backend.security.service.BackendTenantUserService;
import com.github.pagehelper.Page;
import com.ynhdkc.oldsystem.integration.api.request.GetDytkfDoctorGroupListReqDto;
import com.ynhdkc.oldsystem.integration.api.response.QueryDoctorGroupRespDto;
import com.ynhdkc.oldsystem.integration.client.DytkfDoctorGroupClient;
import com.ynhdkc.tenant.client.PaymentCenterClient;
import com.ynhdkc.tenant.component.doctors.KunmingMU1stScheduleHandler;
import com.ynhdkc.tenant.dao.*;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.entity.DoctorGroupRelation;
import com.ynhdkc.tenant.entity.Hospital;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.*;
import com.ynhdkc.tenant.tool.DoctorListTool;
import com.ynhdkc.tenant.tool.DoctorSortUtils;
import com.ynhdkc.tenant.tool.SetDictInfoHelper;
import com.ynhdkc.tenant.tool.convert.DoctorConvert;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import com.ynhdkc.tenant.util.HospitalUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-03 15:45
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DoctorServiceImpl extends KunmingMU1stScheduleHandler implements IDoctorService {
    private final DoctorQuery doctorQuery;
    private final DoctorRepository doctorRepository;
    private final HospitalQuery hospitalQuery;
    private final HospitalAreaQuery hospitalAreaQuery;
    private final DepartmentQuery departmentQuery;
    private final DepartmentRepository departmentRepository;
    private final TenantService tenantService;
    private final HospitalService hospitalService;
    private final HospitalAreaService hospitalAreaService;
    private final DepartmentService departmentService;
    private final HospitalAreaSettingQuery hospitalAreaSettingQuery;

    private final BackendTenantUserService backendTenantUserService;

    private final PageVoConvert pageVoConvert;
    private final SetDictInfoHelper setDictInfoHelper;

    private final DytkfDoctorGroupClient dytkfDoctorGroupClient;
    private final PaymentCenterClient paymentCenterClient;
    private final DoctorListTool doctorListTool;

    private static void asyncExecute(Runnable runnable) {
        new Thread(runnable).start();
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public DoctorDetailVo create(DoctorCreateReqDto request) {
        tenantService.getDetail(request.getTenantId());
        hospitalService.getDetail(request.getHospitalId());
        HospitalAreaDetailVo hospitalArea = hospitalAreaService.getDetail(request.getHospitalAreaId());
        DepartmentDetailVo department = departmentService.getDetail(request.getDepartmentId());

        Doctor doctor = DoctorConvert.toEntity(request);
        doctor.setHospitalCode(hospitalArea.getHospitalAreaCode());
        if (!StringUtils.hasText(doctor.getDepartmentCode())) {
            doctor.setDepartmentCode(department.getThrdpartDepCode());
        }
        if (null == doctor.getSystemDepends()) {
            doctor.setSystemDepends(department.getSystemDepends());
        }
        validDivideSettlementDoctor(doctor);

        setDictInfoHelper.setDictInfo(request, doctor);
        doctorRepository.create(doctor);

        DoctorDetailVo vo = DoctorConvert.toDoctorDetailVo(doctor);
        importDoctorInfo(vo);
        setDictInfoHelper.setDictInfo(doctor, vo);
        return vo;
    }

    private void validDivideSettlementDoctor(Doctor doctor) {
        if (Boolean.TRUE.equals(doctor.getNeedDivideSettlement())) {
            if (null == doctor.getSettlementRuleId()) {
                throw new BizException(HttpStatus.BAD_REQUEST, "需要分账的医生必须指定分账规则");
            }
            if (null == doctor.getSecondMerchantId()) {
                throw new BizException(HttpStatus.BAD_REQUEST, "需要分账的医生必须指定二级商户");
            }
            ResponseEntity<RpcSettlementRuleVo> rpcSettlementRuleVoResponse = paymentCenterClient.querySettlementRuleDetail(doctor.getSettlementRuleId());
            if (HttpStatus.OK.equals(rpcSettlementRuleVoResponse.getStatusCode())) {
                RpcSettlementRuleVo rpcSettlementRuleVo = rpcSettlementRuleVoResponse.getBody();
                if (null == rpcSettlementRuleVo || null == rpcSettlementRuleVo.getId() || rpcSettlementRuleVo.getStatus() != 0) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "分账规则不存在或禁用");
                }
            }
            ResponseEntity<RpcWechatPayServiceProviderSecondMerchantVo> rpcWechatPayServiceProviderSecondMerchantVoResponse = paymentCenterClient.querySecondMerchantDetail(doctor.getSecondMerchantId());
            if (HttpStatus.OK.equals(rpcWechatPayServiceProviderSecondMerchantVoResponse.getStatusCode())) {
                RpcWechatPayServiceProviderSecondMerchantVo rpcWechatPayServiceProviderSecondMerchantVo = rpcWechatPayServiceProviderSecondMerchantVoResponse.getBody();
                if (null == rpcWechatPayServiceProviderSecondMerchantVo || null == rpcWechatPayServiceProviderSecondMerchantVo.getId() || rpcWechatPayServiceProviderSecondMerchantVo.getStatus() != 7) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "二级商户不存在或不可用");
                }
            }
        } else {
            doctor.setSettlementRuleId(null);
            doctor.setSecondMerchantId(null);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DoctorDetailVo update(Long doctorId, DoctorUpdateReqDto request) {
        tenantService.getDetail(request.getTenantId());
        hospitalService.getDetail(request.getHospitalId());
        hospitalAreaService.getDetail(request.getHospitalAreaId());
        departmentService.getDetail(request.getDepartmentId());

        Doctor doctor = doctorQuery.queryDoctorById(doctorId);
        if (null == doctor) {
            throw new BizException(HttpStatus.NOT_FOUND, "医生不存在");
        }
        DoctorConvert.toEntity(doctor, request);

        doctor.setUpdateTime(new Date());
        validDivideSettlementDoctor(doctor);

        setDictInfoHelper.setDictInfo(request, doctor);
        doctorRepository.update(doctor);

        DoctorDetailVo vo = DoctorConvert.toDoctorDetailVo(doctor);
        importDoctorInfo(vo);
        setDictInfoHelper.setDictInfo(doctor, vo);
        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseOperationResponse delete(Long doctorId) {
        Doctor doctor = doctorQuery.queryDoctorById(doctorId);
        if (null == doctor) {
            throw new BizException(HttpStatus.NOT_FOUND, "医生不存在");
        }
        doctorRepository.delete(doctorId);
        return new BaseOperationResponse("删除成功");
    }

    @Override
    public DoctorDetailVo getDetail(Long doctorId) {
        Doctor doctor = doctorQuery.queryDoctorById(doctorId);
        if (null == doctor) {
            throw new BizException(HttpStatus.NOT_FOUND, "医生不存在");
        }

        DoctorDetailVo vo = DoctorConvert.toDoctorDetailVo(doctor);
        importDoctorInfo(vo);
        setDictInfoHelper.setDictInfo(doctor, vo);
        return vo;
    }

    @Override
    public DoctorDetailVo rpcGetDetail(Long doctorId) {
        Doctor doctor = doctorQuery.queryDoctorById(doctorId);
        if (null == doctor) {
            return null;
        }

        DoctorDetailVo vo = DoctorConvert.toDoctorDetailVo(doctor);
        Department department = departmentQuery.queryDepartmentById(doctor.getDepartmentId());
        Optional<AppointmentRuleSetting> ruleSetting = hospitalAreaSettingQuery.queryAppointmentRuleSettingById(null, null, department.getHospitalAreaId());
        if (!ruleSetting.isPresent()) {
            throw new BizException(HttpStatus.BAD_REQUEST, "医院区域预约规则不存在");
        }

        Pair<Integer, Integer> advanceAndForbiddenDays = doctorListTool.defaultAdvanceAndForbiddenDays(doctor, department, ruleSetting.get());
        vo.setAdvanceDay(advanceAndForbiddenDays.getLeft());
        vo.setForbiddenDay(advanceAndForbiddenDays.getRight());

        importDoctorInfo(vo);
        setDictInfoHelper.setDictInfo(doctor, vo);
        log.info("rpcGetDetail:{}", vo);
        return vo;
    }

    @Override
    public DoctorKafkaVo getDetailForKafka(Long doctorId) {
        Doctor doctor = doctorQuery.queryDoctorById(doctorId);
        if (null == doctor) {
            throw new BizException(HttpStatus.NOT_FOUND, "医生不存在");
        }
        DoctorKafkaVo vo = DoctorConvert.toDoctorKafkaVo(doctor);
        importDoctorInfo(vo);
        return vo;
    }

    @Override
    public DoctorPageVo query(DoctorQueryReqDto request) {
        boolean isSuperAdmin = backendTenantUserService.isSuperAdmin();
        if (null == request.getTenantId() && !isSuperAdmin) {
            throw new BizException(HttpStatus.FORBIDDEN, "必须指定租户ID");
        }
        Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaBy(request.getHospitalAreaId());
        DoctorQuery.DoctorQueryOption option = new DoctorQuery.DoctorQueryOption(request.getCurrentPage(), request.getPageSize())
                .setTenantId(request.getTenantId())
                .setHospitalId(request.getHospitalId())
                .setHospitalAreaId(request.getHospitalAreaId())
                .setDepartmentId(request.getDepartmentId())
                .setId(request.getId())
                .setName(request.getName())
                .setThrdpartDoctorCode(request.getThrdpartDoctorCode())
                .setStatus(request.getStatus())
                .setCategory(request.getCategory())
                .setExcludeCategory(request.getExcludeCategory())
                .setStartCreateTime(request.getStartCreateTime())
                .setEndCreateTime(request.getEndCreateTime());
        if (!isSuperAdmin) {
            option.setIncludeTenantIds(backendTenantUserService.getCurrentUserTenantReadRange(true));
            option.setIncludeHospitalIds(backendTenantUserService.getCurrentUserHospitalReadRange(true));
            option.setIncludeHospitalAreaIds(backendTenantUserService.getCurrentUserHospitalAreaReadRange(true));
            option.setIncludeDepartmentIds(backendTenantUserService.getCurrentUserDepartmentReadRange(true));
        }

        if (hospitalArea != null && HospitalUtils.isKunmingMU1stHospital(hospitalArea.getHospitalCode())) {
            processQueryOption4KunmingMU1st(request, option);
        }

        try (Page<Doctor> doctorPage = doctorQuery.pageQueryDoctorWithCategory(option)) {
            DoctorPageVo pageVo = pageVoConvert.toPageVo(doctorPage, DoctorPageVo.class, DoctorConvert::toDoctorDetailVo);
            if (CollectionUtils.isEmpty(pageVo.getList())) {
                return pageVo;
            }

            List<Long> hospitalIds = doctorPage.getResult().stream().map(Doctor::getHospitalId).collect(Collectors.toList());
            List<Long> hospitalAreaIds = doctorPage.getResult().stream().map(Doctor::getHospitalAreaId).collect(Collectors.toList());
            List<Hospital> hospitals = hospitalQuery.queryBy(hospitalIds, hospitalAreaIds);
            hospitals = hospitals == null ? new ArrayList<>() : hospitals;
            List<Department> departments = departmentQuery.queryByHospitalIds(hospitalIds);
            List<AppointmentRuleSetting> appointmentRuleSettings = hospitalAreaSettingQuery.queryAppointmentRuleSettingInId(hospitalAreaIds);

            Map<Long, Hospital> hospitalMap = hospitals.stream().collect(Collectors.toMap(Hospital::getId, Function.identity(), (a, b) -> a));
            Map<Long, Department> departmentMap = departments.stream().collect(Collectors.toMap(Department::getId, Function.identity(), (a, b) -> a));
            Map<Long, AppointmentRuleSetting> appointmentRuleSettingMap = appointmentRuleSettings.stream().collect(Collectors.toMap(AppointmentRuleSetting::getHospitalAreaId, Function.identity(), (a, b) -> a));
            if (!CollectionUtils.isEmpty(hospitals)) {
                pageVo.getList().forEach(q -> importHospitalAndDepartmentInfo(q, hospitalMap, departmentMap, appointmentRuleSettingMap));
            }

            Map<Long, Doctor> doctorMap = doctorPage.stream()
                    .collect(Collectors.toMap(Doctor::getId, entity -> entity));
            pageVo.getList().forEach(vo -> setDictInfoHelper.setDictInfo(doctorMap.get(vo.getId()), vo));
            return pageVo;
        }
    }

    private void processQueryOption4KunmingMU1st(DoctorQueryReqDto request, DoctorQuery.DoctorQueryOption option) {
        Department department = departmentQuery.queryDepartmentById(request.getDepartmentId());
        if (department == null) {
            return;
        }
        String departmentCode = department.getThrdpartDepCode();
        if (departmentCode == null || !departmentCode.contains("|")) {
            return;
        }
        String[] execDepartmentCode = departmentCode.split("\\|");
        Set<String> departmentCodeSet = Arrays.stream(execDepartmentCode).collect(Collectors.toSet());
        Map<String, Department> execDepartmentMap = queryExecDepartmentMap(departmentCodeSet, department);
        log.info("processQueryOption4KunmingMU1st is : {}", execDepartmentMap);
        if (ObjectsUtils.isEmpty(execDepartmentMap)) {
            return;
        }
        option.setDepartmentId(null);
        List<Long> execDepartmentIdList = execDepartmentMap.values().stream().map(Department::getId).collect(Collectors.toList());
        Collection<Long> departmentIds = option.getIncludeDepartmentIds();
        if (ObjectsUtils.isEmpty(departmentIds)) {
            option.setIncludeDepartmentIds(execDepartmentIdList);
        } else {
            departmentIds.addAll(execDepartmentIdList);
        }
    }

    @Override
    public List<DoctorKafkaVo> queryAllForKafka() {
        List<Doctor> doctors = doctorQuery.queryAll();
        if (CollectionUtils.isEmpty(doctors)) {
            return null;
        }

        return compositeDoctorKafkaVo(doctors);
    }

    private List<DoctorKafkaVo> compositeDoctorKafkaVo(List<Doctor> doctors) {
        Set<Long> hospitalIds = doctors.stream().map(Doctor::getHospitalId).collect(Collectors.toSet());
        Set<Long> hospitalAreaIds = doctors.stream().map(Doctor::getHospitalAreaId).collect(Collectors.toSet());
        Set<Long> allHospitalIds = new HashSet<>(hospitalIds);
        allHospitalIds.addAll(hospitalAreaIds);
        List<Hospital> hospitals = hospitalQuery.queryBy(allHospitalIds);
        Map<Long, Hospital> hospitalMap = hospitals.stream().filter(q -> q.getHospitalTypeTag() == 0).collect(Collectors.toMap(Hospital::getId, entity -> entity, (a, b) -> a));
        Map<Long, Hospital> hospitalAreaMap = hospitals.stream().filter(q -> q.getHospitalTypeTag() == 1).collect(Collectors.toMap(Hospital::getId, entity -> entity, (a, b) -> a));

        Map<Long, Department> departmentMap = departmentQuery.queryBy(doctors.stream().map(Doctor::getDepartmentId).collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(Department::getId, entity -> entity, (a, b) -> a));

        return doctors.stream()
                .map(DoctorConvert::toDoctorKafkaVo)
                .peek(doctorKafkaVo -> importDoctorInfo(doctorKafkaVo, hospitalMap, hospitalAreaMap, departmentMap))
                .collect(Collectors.toList());
    }

    private void importDoctorInfo(DoctorVo vo, Map<Long, Hospital> hospitalMap, Map<Long, Hospital> hospitalAreaMap, Map<Long, Department> departmentMap) {
        if (null != vo.getHospitalId()) {
            Hospital hospital = hospitalMap.get(vo.getHospitalId());
            if (null != hospital) {
                vo.setHospitalName(hospital.getName());
            }
        }
        if (null != vo.getHospitalAreaId()) {
            Hospital hospitalArea = hospitalAreaMap.get(vo.getHospitalAreaId());
            if (null != hospitalArea) {
                vo.setHospitalAreaName(hospitalArea.getName());
            }
        }
        if (null != vo.getDepartmentId()) {
            Department department = departmentMap.get(vo.getDepartmentId());
            if (null != department) {
                vo.setDepartmentName(department.getName());
            }
        }
    }

    @Override
    public List<DoctorKafkaVo> queryByDepartmentIdForKafkaVo(Long departmentId) {
        Optional<Department> departmentOptional = departmentRepository.queryById(departmentId);
        if (!departmentOptional.isPresent()) {
            throw new BizException(HttpStatus.NOT_FOUND, "科室不存在");
        }

        Department department = departmentOptional.get();
        List<Doctor> doctors = doctorQuery.findByDepartmentId(department.getId());
        if (doctors.isEmpty()) {
            return new ArrayList<>();
        }

        return compositeDoctorKafkaVo(doctors);
    }

    @Override
    public List<DoctorDetailVo> rpcBatchGetDetail(RpcBatchGetByIdReqDto request) {
        try (Page<Doctor> doctorPage = doctorQuery.pageQuery(new DoctorQuery.DoctorQueryOption(1, request.getIds().size())
                .setIncludeIds(request.getIds()))) {
            return getDoctorVos(doctorPage);
        }
    }

    @Override
    public List<DoctorDetailVo> rpcBatchGetDetailByAreaId(Long areaId, Long departmentId) {
        List<Doctor> doctors = doctorQuery.queryBy(areaId, departmentId);
        if (CollectionUtils.isEmpty(doctors)) {
            return Collections.emptyList();
        }
        return getDoctorVos(doctors);
    }

    @Override
    public List<Long> rpcBatchGetIdLikeName(String name) {
        List<Doctor> doctors = doctorQuery.queryLikeName(name);
        if (CollectionUtils.isEmpty(doctors)) {
            return Collections.emptyList();
        }
        return doctors.stream().map(Doctor::getId).collect(Collectors.toList());
    }

    @Override
    public DoctorGroupPageRespVo getDoctorGroupList(GetDoctorGroupPageReqDto request) {
        GetDytkfDoctorGroupListReqDto req = new GetDytkfDoctorGroupListReqDto();
        req.setCurrent(request.getCurrent());
        req.setSize(request.getSize());
        req.setIds(request.getIds());
        req.setName(request.getName());
        req.setHospitalCodes(request.getHospitalCodes());
        req.setDepartmentCodes(request.getDepartmentCodes());
        req.setDoctorCodes(request.getDoctorCodes());
        QueryDoctorGroupRespDto doctorGroupPage = dytkfDoctorGroupClient.getDoctorGroupList(req);

        List<DoctorGroupItemRespVo> vos = doctorGroupPage.getRecords().stream().map(dg -> {
            DoctorGroupItemRespVo vo = new DoctorGroupItemRespVo();
            vo.setId(dg.getId());
            vo.setName(dg.getName());
            vo.setTitle(dg.getTitle());
            vo.setHospitalCode(dg.getHospitalCode());
            vo.setHospitalName(dg.getHospitalName());
            vo.setDepartmentCode(dg.getDepartCode());
            vo.setDepartmentName(dg.getDepartName());
            vo.setDoctorCode(dg.getDoctorCode());
            vo.setDisplayName(dg.getDoctorName() + "-" + dg.getHospitalName() + "-" + dg.getDepartName());
            return vo;
        }).collect(Collectors.toList());

        DoctorGroupPageRespVo result = new DoctorGroupPageRespVo();
        result.setCurrent(doctorGroupPage.getCurrent());
        result.setSize(doctorGroupPage.getSize());
        result.setTotal(doctorGroupPage.getTotal());
        result.setList(vos);
        return result;
    }

    @Override
    public DoctorGroupRelationVo bindDoctorGroup(DoctorBindGroupReqDto request) {
        Doctor doctor = doctorQuery.queryDoctorById(request.getDoctorId());
        if (null == doctor) {
            throw new BizException(HttpStatus.NOT_FOUND, "医生不存在");
        }
        DoctorGroupRelation relation = doctorQuery.queryDoctorGroupRelation(request.getDoctorId(), request.getDoctorGroupSource(), request.getDoctorGroupId());
        if (null == relation) {
            relation = new DoctorGroupRelation();
            relation.setDoctorId(doctor.getId());
            relation.setDoctorName(doctor.getName());
            relation.setDoctorGroupSource(request.getDoctorGroupSource());
            relation.setDoctorGroupId(request.getDoctorGroupId());
            relation.setDoctorGroupName(request.getDoctorGroupName());
            doctorRepository.createDoctorGroupRelation(relation);
        }
        return DoctorConvert.toDoctorGroupRelationVo(relation);
    }

    @Override
    public BaseOperationResponse unbindDoctorGroup(DoctorUnbindGroupReqDto request) {
        Doctor doctor = doctorQuery.queryDoctorById(request.getDoctorId());
        if (null == doctor) {
            throw new BizException(HttpStatus.NOT_FOUND, "医生不存在");
        }
        DoctorGroupRelation relation;
        if (null == request.getRelationId()) {
            relation = doctorQuery.queryDoctorGroupRelation(request.getDoctorId(), request.getDoctorGroupSource(), request.getDoctorGroupId());
        } else {
            relation = doctorQuery.queryDoctorGroupRelationById(request.getRelationId());
        }
        if (null != relation) {
            doctorRepository.deleteDoctorGroupRelation(relation.getDoctorId(), request.getDoctorGroupSource(), request.getDoctorGroupId());
        }
        return new BaseOperationResponse("解绑成功");
    }

    @Override
    public DoctorGroupRelationQueryRespDto queryDoctorGroup(DoctorGroupRelationQueryReqDto request) {
        try (Page<DoctorGroupRelation> relations = doctorQuery.pageQueryDoctorGroupRelation(new DoctorQuery.DoctorGroupRelationQueryOption(request.getCurrentPage(), request.getPageSize())
                .setDoctorId(request.getDoctorId())
                .setDoctorName(request.getDoctorName())
                .setDoctorGroupSource(request.getDoctorGroupSource())
                .setDoctorGroupId(request.getDoctorGroupId())
                .setDoctorGroupName(request.getDoctorGroupName()))) {
            List<Doctor> doctors = doctorQuery.queryBy(relations.stream().map(DoctorGroupRelation::getDoctorId).collect(Collectors.toList()));
            Map<Long, Doctor> doctorMap = doctors.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
            return pageVoConvert.toPageVo(relations, DoctorGroupRelationQueryRespDto.class, relation -> {
                DoctorGroupRelationVo relationVo = DoctorConvert.toDoctorGroupRelationVo(relation);

                Doctor doctor = doctorMap.get(relation.getDoctorId());

                relationVo.setDoctorCode(doctor.getThrdpartDoctorCode());
                relationVo.setStatus(doctor.getStatus());

                Department department = departmentQuery.queryDepartmentById(doctor.getDepartmentId());
                if (null != department) {
                    relationVo.setDepartmentId(department.getId());
                    relationVo.setDepartmentName(department.getName());
                }

                Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaById(doctor.getHospitalAreaId());
                if (null != hospitalArea) {
                    relationVo.setHospitalAreaId(hospitalArea.getId());
                    relationVo.setHospitalAreaName(hospitalArea.getName());
                }

                Hospital hospital = hospitalQuery.queryHospitalById(doctor.getHospitalId());
                if (null != hospital) {
                    relationVo.setHospitalId(hospital.getId());
                    relationVo.setHospitalName(hospital.getName());
                }
                return relationVo;
            }, v -> doctorMap.containsKey(v.getDoctorId()));
        }
    }

    @Override
    public BaseOperationResponse batchBindDoctorGroup(DoctorBatchBindGroupReqDto request) {
        if (!CollectionUtils.isEmpty(request.getDoctorIds())) {
            List<Doctor> doctors = request.getDoctorIds()
                    .stream()
                    .map(doctorId -> {
                        Doctor doctor = doctorQuery.queryDoctorById(doctorId);
                        if (null == doctor) {
                            throw new BizException(HttpStatus.NOT_FOUND, "医生不存在");
                        }
                        return doctor;
                    })
                    .collect(Collectors.toList());
            doctors.forEach(doctor -> {
                DoctorGroupRelation relation = doctorQuery.queryDoctorGroupRelation(doctor.getId(), request.getDoctorGroupSource(), request.getDoctorGroupId());
                if (null == relation) {
                    relation = new DoctorGroupRelation();
                    relation.setDoctorId(doctor.getId());
                    relation.setDoctorName(doctor.getName());
                    relation.setDoctorGroupSource(request.getDoctorGroupSource());
                    relation.setDoctorGroupId(request.getDoctorGroupId());
                    relation.setDoctorGroupName(request.getDoctorGroupName());
                    doctorRepository.createDoctorGroupRelation(relation);
                }
            });
        }
        return new BaseOperationResponse("批量绑定成功");
    }

    @Override
    public BaseOperationResponse reSortDoctor(Long hospitalAreaId, Long departmentId) {
        DepartmentsTreeSearchReqDto departmentsTreeSearchReqDto = new DepartmentsTreeSearchReqDto();
        departmentsTreeSearchReqDto.setHospitalAreaId(hospitalAreaId);
        DepartmentsTreeSearchListVo departmentsTreeSearchListVo = departmentService.queryTree2(departmentsTreeSearchReqDto);
        asyncExecute(() -> {
            if (null != hospitalAreaId && null == departmentId) {
                List<Doctor> doctors = doctorQuery.queryBy(hospitalAreaId, null);
                List<DepartmentsTreeSearchVo> leafNodes = findLeafNodes(departmentsTreeSearchListVo.getList());
                if (!CollectionUtils.isEmpty(leafNodes)) {
                    leafNodes.stream().parallel().forEach(node -> reSortDoctorByDepartmentId(node.getId(), doctors));
                }
            }

            if (null != departmentId) {
                Department department = departmentQuery.queryDepartmentById(departmentId);
                if (null == department) {
                    log.warn("科室不存在，departmentId={}", departmentId);
                    return;
                }
                List<Doctor> doctors = doctorQuery.queryBy(department.getHospitalAreaId(), departmentId);
                reSortDoctorByDepartmentId(departmentId, doctors);
            }
            log.info("医生重排序成功");
        });

        return new BaseOperationResponse("医生重排序成功");

    }

    @Override
    public void updateGongRenDoctorDepartmentInfo() {
        List<Doctor> doctors = doctorRepository.queryEnabledDoctors(HospitalCode.KUNMING_MU_SECOND_AFFILIATED_HOSPITAL.getCode());
        if (CollectionUtils.isEmpty(doctors)) {
            return;
        }

        List<Department> departments = departmentQuery.queryBy(HospitalCode.KUNMING_MU_SECOND_AFFILIATED_HOSPITAL.getCode());
        doctors.forEach(doctor -> {
            Department department = departments.stream().filter(d -> d.getId().equals(doctor.getDepartmentId())).findFirst().orElse(null);
            if (null == department) {
                return;
            }

            doctor.setDepartmentCode(department.getThrdpartDepCode());
            doctorRepository.update(doctor);
        });

    }

    @Override
    public BaseOperationResponse batchSyncDoctorInfoFromSpecifiedDoctor(BatchSyncDoctorInfoFromSpecifiedDoctorReqDto request) {
        if (CollectionUtils.isEmpty(request.getPendingSyncDoctorIds())) {
            return new BaseOperationResponse("同步成功");
        }

        Doctor doctor = doctorQuery.queryDoctorById(request.getDoctorId());
        if (null == doctor) {
            throw new BizException(HttpStatus.NOT_FOUND, "医生不存在");
        }

        Set<Long> pendingSyncDoctorIds = new HashSet<>(request.getPendingSyncDoctorIds());
        List<Doctor> doctors = doctorQuery.queryBy(pendingSyncDoctorIds);
        if (CollectionUtils.isEmpty(doctors)) {
            return new BaseOperationResponse("同步成功");
        }

        syncDoctorInfo(doctor, doctors);
        return new BaseOperationResponse("同步成功");
    }

    /**
     * 同步擅长、简介、职称、头像
     */
    private void syncDoctorInfo(Doctor doctor, List<Doctor> doctors) {
        doctors.forEach(d -> {
            d.setSpeciality(doctor.getSpeciality());
            d.setIntroduction(doctor.getIntroduction());
            d.setRankDictLabel(doctor.getRankDictLabel());
            d.setRankDictType(doctor.getRankDictType());
            d.setRankDictValue(doctor.getRankDictValue());
            d.setHeadImgUrl(doctor.getHeadImgUrl());
            d.setHonor(doctor.getHonor());
            d.setUpdateTime(new Date());
            doctorRepository.update(d);
        });
    }


    private List<DepartmentsTreeSearchVo> findLeafNodes(List<DepartmentsTreeSearchVo> nodes) {
        List<DepartmentsTreeSearchVo> leafNodes = new ArrayList<>();
        for (DepartmentsTreeSearchVo node : nodes) {
            if (node.getChildrens() == null || node.getChildrens().isEmpty()) {
                leafNodes.add(node);
            } else {
                leafNodes.addAll(findLeafNodes(node.getChildrens()));
            }
        }
        return leafNodes;
    }


    private void reSortDoctorByDepartmentId(Long departmentId, List<Doctor> doctors) {
        Department department = departmentQuery.queryDepartmentById(departmentId);
        if (null == department) {
            log.warn("科室不存在，departmentId={}", departmentId);
            return;
        }
        if (!CollectionUtils.isEmpty(doctors)) {
            List<Doctor> doctorList = doctors.stream().filter(doctor -> doctor.getDepartmentId().equals(departmentId)).collect(Collectors.toList());
            DoctorSortUtils.reSortDoctor(doctorList);
            AtomicReference<Integer> maxIndex = new AtomicReference<>(doctorList.size());
            updateSort(doctorList, maxIndex);
        }
    }

    private void updateSort(List<Doctor> doctorList, AtomicReference<Integer> maxIndex) {
        doctorList.forEach(doctor -> {
            doctor.setSort(maxIndex.get());
            maxIndex.getAndSet(maxIndex.get() - 1);
            doctorRepository.update(doctor);
        });
    }

    private List<DoctorDetailVo> getDoctorVos(List<Doctor> doctors) {
        Map<Long, Doctor> doctorMap = doctors.stream().collect(Collectors.toMap(Doctor::getId, Function.identity(), (a, b) -> a));

        return doctors.stream()
                .map(DoctorConvert::toDoctorDetailVo)
                .peek(this::importDoctorInfo)
                .peek(vo -> setDictInfoHelper.setDictInfo(doctorMap.get(vo.getId()), vo))
                .collect(Collectors.toList());
    }

    private void importHospitalAndDepartmentInfo(DoctorVo doctorVo,
                                                 Map<Long, Hospital> hospitals,
                                                 Map<Long, Department> departments,
                                                 Map<Long, AppointmentRuleSetting> appointmentRuleSettings) {
        Hospital hospital = hospitals.get(doctorVo.getHospitalId());
        Hospital hospitalArea = hospitals.get(doctorVo.getHospitalAreaId());
        Department department = departments.get(doctorVo.getDepartmentId());
        AppointmentRuleSetting appointmentRuleSetting = appointmentRuleSettings.get(doctorVo.getHospitalAreaId());
        if (null != hospital) {
            doctorVo.setHospitalName(hospital.getName());
        }
        if (null != hospitalArea) {
            doctorVo.setHospitalAreaName(hospitalArea.getName());
        }
        if (null != department) {
            doctorVo.setDepartmentName(department.getName());
        }
        if (null != appointmentRuleSetting) {
            doctorVo.setSystemDepends(appointmentRuleSetting.getSystemDepends());
        }
    }

    private void importDoctorInfo(DoctorVo vo) {
        if (null != vo.getHospitalId()) {
            HospitalDetailVo hospitalDetailVo = hospitalService.rpcGetDetail(vo.getHospitalId());
            if (null != hospitalDetailVo) {
                vo.setHospitalName(hospitalDetailVo.getName());
            }
        }
        if (null != vo.getHospitalAreaId()) {
            HospitalAreaDetailVo hospitalAreaDetailVo = hospitalAreaService.rpcGetDetail(vo.getHospitalAreaId());
            if (null != hospitalAreaDetailVo) {
                vo.setHospitalAreaName(hospitalAreaDetailVo.getName());
            }
        }
        if (null != vo.getDepartmentId()) {
            DepartmentDetailVo departmentDetailVo = departmentService.rpcGetDetail(vo.getDepartmentId());
            if (null != departmentDetailVo) {
                vo.setDepartmentName(departmentDetailVo.getName());
            }
        }
    }


}
