package backend.common.entity.dto.hisgateway.enums;

import com.fasterxml.jackson.annotation.JsonGetter;

/**
 * GB/T 4761-2008 家庭关系代码(待补全)
 */
public enum FamilyRelationship {

    SELF("本人", "01"), SPOUSE("配偶", "10"), SON("儿子", "20"), DAUGHTER("女儿", "30"), GRAND_SON("孙子、孙女或外孙子、外孙女", "40"),
    PARENTS("父母", "50"), GRANDPARENTS("祖父母", "60"), BRO_SIS("兄弟姐妹", "70"), OTHER("其他", "80");

    private final String value;

    private final String code;

    FamilyRelationship(String value, String code) {
        this.value = value;
        this.code = code;
    }

    public static FamilyRelationship getFromCode(String code) {
        for (FamilyRelationship t : FamilyRelationship.values()) {
            if (t.getCode().equals(code)) {
                return t;
            }
        }
        throw new IllegalArgumentException("婚姻状况不存在");
    }

    public static FamilyRelationship getFromValue(String value) {
        for (FamilyRelationship t : FamilyRelationship.values()) {
            if (t.getValue().equals(value)) {
                return t;
            }
        }
        throw new IllegalArgumentException("婚姻状况不存在");
    }

    public String getValue() {
        return value;
    }

    public String getCode() {
        return code;
    }

    @JsonGetter("code")
    public String getRequestCode() {
        return code;
    }

}
