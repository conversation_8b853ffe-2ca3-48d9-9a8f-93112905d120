create table t_hospital_list_group_relation
(
    id            bigint auto_increment comment '全局唯一标识' primary key,
    group_id      bigint                                   not null comment '医院组id',
    hospital_id   bigint                                   not null comment '医院id',
    hospital_code varchar(20)                              null comment '医院编码',
    weight        int(9)      default 0                    not null comment '权重',
    create_time   datetime(3) default CURRENT_TIMESTAMP(3) not null,
    update_time   datetime(3)                              null on update CURRENT_TIMESTAMP(3)
)
    comment '医院组关系表';

