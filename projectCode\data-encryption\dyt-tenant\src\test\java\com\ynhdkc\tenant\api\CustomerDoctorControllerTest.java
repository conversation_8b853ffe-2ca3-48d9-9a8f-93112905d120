package com.ynhdkc.tenant.api;

import com.ynhdkc.oldsystem.integration.api.response.QueryBaseDoctorDepartmentRespDto;
import com.ynhdkc.oldsystem.integration.client.DytkfDoctorClient;
import com.ynhdkc.tenant.DytTenantApplication;
import com.ynhdkc.tenant.api.customer.CustomerDoctorController;
import com.ynhdkc.tenant.model.CustomerDoctorScheduledDepartmentVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;

import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = {DytTenantApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("dev")
public class CustomerDoctorControllerTest {

    @MockBean
    private DytkfDoctorClient dytkfDoctorClient;

    @Autowired
    private CustomerDoctorController customerDoctorController;

    @Test
    void testQueryDoctorScheduledDepartment() {
        when(dytkfDoctorClient.queryBaseDoctorDepartment(anyLong())).thenReturn(fromJson());
        ResponseEntity<CustomerDoctorScheduledDepartmentVo> responseEntity = customerDoctorController.queryDoctorScheduledDepartment(1310731324755017729L);
        System.out.println(responseEntity);
    }

    private QueryBaseDoctorDepartmentRespDto fromJson() {
        /**
         * {
         *   "doctor_info": {
         *     "id": 1556936955596374017,
         *     "name": "梁金龙",
         *     "level": "副主任医师",
         *     "avatar": "/uploads/avatar/20210310/66b8fb9556426e45f66ec3e996c20a93.jpg",
         *     "summary": "梁金龙，硕士研究生，副主任医师，从事骨科10余年，对脊柱、关节退变及创伤性疾病的诊治具有较深理论和临床实践，对骨科创伤救治具有丰富的临床经验。发表SCI及中文核心论文10余篇，获国家专利2项，参编医学著作1本。",
         *     "speciality": "对脊柱侧弯、腰椎间盘突出、腰椎管狭窄、腰椎滑脱和髋膝关节退变性疾病的诊治具有较深理论和临床实践，对骨科创伤救治具有丰富的临床经验。"
         *   },
         *   "department_info_list": [
         *     {
         *       "hospital_area_code": "871256",
         *       "department_code": "091106",
         *       "doctor_code": "7526",
         *       "enabled": true,
         *       "sort": 255
         *     }
         *   ]
         * }
         */
        QueryBaseDoctorDepartmentRespDto queryBaseDoctorDepartmentRespDto = new QueryBaseDoctorDepartmentRespDto();
        QueryBaseDoctorDepartmentRespDto.BaseDoctorInfoDto baseDoctorInfoDto = new QueryBaseDoctorDepartmentRespDto.BaseDoctorInfoDto();
        baseDoctorInfoDto.setId(1556936955596374017L);
        baseDoctorInfoDto.setName("梁金龙");
        baseDoctorInfoDto.setLevel("副主任医师");
        baseDoctorInfoDto.setAvatar("/uploads/avatar/20210310/66b8fb9556426e45f66ec3e996c20a93.jpg");
        baseDoctorInfoDto.setSummary("梁金龙，硕士研究生，副主任医师，从事骨科10余年，对脊柱、关节退变及创伤性疾病的诊治具有较深理论和临床实践，对骨科创伤救治具有丰富的临床经验。发表SCI及中文核心论文10余篇，获国家专利2项，参编医学著作1本。");
        baseDoctorInfoDto.setSpeciality("对脊柱侧弯、腰椎间盘突出、腰椎管狭窄、腰椎滑脱和髋膝关节退变性疾病的诊治具有较深理论和临床实践，对骨科创伤救治具有丰富的临床经验。");
        queryBaseDoctorDepartmentRespDto.setDoctorInfo(baseDoctorInfoDto);

        List<QueryBaseDoctorDepartmentRespDto.BaseDepartmentInfoDto> departmentInfoDtoList = Collections.singletonList(
                new QueryBaseDoctorDepartmentRespDto.BaseDepartmentInfoDto()
                        .setHospitalAreaCode("871256")
                        .setDepartmentCode("091106")
                        .setDoctorCode("7526")
                        .setEnabled(true)
                        .setSort(255)
        );
        queryBaseDoctorDepartmentRespDto.setDepartmentInfoList(departmentInfoDtoList);
        return queryBaseDoctorDepartmentRespDto;
    }

}
