package com.ynhdkc.tenant.dao.impl;

import backend.common.util.MybatisUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ynhdkc.tenant.dao.HospitalAreaFunctionQuery;
import com.ynhdkc.tenant.dao.mapper.FunctionSettingMapper;
import com.ynhdkc.tenant.entity.setting.FunctionSetting;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-09 10:23
 */
@Repository
@RequiredArgsConstructor
public class HospitalAreaFunctionQueryImpl implements HospitalAreaFunctionQuery {
    private final FunctionSettingMapper hospitalFunctionSettingMapper;

    @Override
    public FunctionSetting queryFunctionById(Long hospitalAreaId, Long functionId) {
        List<FunctionSetting> settings = hospitalFunctionSettingMapper.selectByExample2(FunctionSetting.class, sql ->
                sql.andEqualTo(FunctionSetting::getHospitalAreaId, hospitalAreaId)
                .andEqualTo(FunctionSetting::getId, functionId));
        if (CollectionUtils.isEmpty(settings)) {
            return null;
        }
        return settings.get(0);
    }

    @Override
    public Page<FunctionSetting> pageQuery(HospitalAreaFunctionQueryOption option) {
        try (final Page<FunctionSetting> page = PageHelper.startPage(option.getCurrentPage(), option.getPageSize())) {
            return page.doSelectPage(() -> hospitalFunctionSettingMapper.selectByExample(FunctionSetting.class, sql -> {
                sql.defGroup(condition -> {
                    if (null != option.getTenantId()) {
                        condition.andEqualTo(FunctionSetting::getTenantId, option.getTenantId());
                    }
                    if (null != option.getHospitalId()) {
                        condition.andEqualTo(FunctionSetting::getHospitalId, option.getHospitalId());
                    }
                    if (null != option.getHospitalAreaId()) {
                        condition.andEqualTo(FunctionSetting::getHospitalAreaId, option.getHospitalAreaId());
                    }
                    if (null != option.getName()) {
                        condition.andLike(FunctionSetting::getName, MybatisUtil.likeBoth(option.getName()));
                    }
                    if (null != option.getType()) {
                        condition.andEqualTo(FunctionSetting::getType, option.getType());
                    }
                    if (null != option.getStatus()) {
                        condition.andEqualTo(FunctionSetting::getStatus, option.getStatus());
                    }

                    if (!CollectionUtils.isEmpty(option.getIncludeHospitalAreaIds())) {
                        condition.andIn(FunctionSetting::getHospitalAreaId, option.getIncludeHospitalAreaIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getExcludeHospitalAreaIds())) {
                        condition.andNotIn(FunctionSetting::getHospitalAreaId, option.getExcludeHospitalAreaIds());
                    }
                });
                sql.builder(builder -> builder
                        .orderByAsc(FunctionSetting::getSort)
                        .orderByAsc(FunctionSetting::getId));
            }));
        }
    }
}
