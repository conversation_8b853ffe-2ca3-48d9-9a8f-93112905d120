package com.ynhdkc.tenant.tool;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import com.ynhdkc.tenant.dto.DateRangeDto;

import java.util.Calendar;

public class DateUtils {

    private DateUtils() {
    }

    public static DateRangeDto getDateRange(int deltaDays) {
        DateRangeDto dateRangeDto = new DateRangeDto();

        long startDate = System.currentTimeMillis();
        long endDate = new DateTime().offset(DateField.DAY_OF_YEAR, deltaDays).getTime();

        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(startDate);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        dateRangeDto.setStartDate(calendar.getTimeInMillis());

        calendar.setTimeInMillis(endDate);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 0);

        dateRangeDto.setEndDate(calendar.getTimeInMillis());

        return dateRangeDto;
    }
    
    public static int getDaysBetween(long startDate, long endDate) {
        return (int) ((endDate - startDate) / (1000 * 60 * 60 * 24));
    }

}
