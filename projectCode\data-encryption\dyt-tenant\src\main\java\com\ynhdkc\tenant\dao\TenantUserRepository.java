package com.ynhdkc.tenant.dao;

import com.ynhdkc.tenant.entity.TenantUser;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-25 22:50
 */
public interface TenantUserRepository {
    void save(TenantUser tenantUser);

    void update(TenantUser tenantUser);

    void delete(Long userId);

    /**
     * 删除用户绑定该租户所有资源
     *
     * @param userId   用户ID
     * @param tenantId 租户ID
     */
    void delete(Long userId, Long tenantId);
}
