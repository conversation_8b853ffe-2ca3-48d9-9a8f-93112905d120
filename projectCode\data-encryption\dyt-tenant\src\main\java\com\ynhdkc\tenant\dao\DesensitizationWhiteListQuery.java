package com.ynhdkc.tenant.dao;

import com.github.pagehelper.Page;
import com.ynhdkc.tenant.entity.DesensitizationWhiteList;
import com.ynhdkc.tenant.model.WhiteListPageReqDto;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-25 21:46
 */
public interface DesensitizationWhiteListQuery {
    DesensitizationWhiteList queryDesensitizationWhiteListById(Long DesensitizationWhiteListId);

    Page<DesensitizationWhiteList> pageQueryDesensitizationWhiteList(WhiteListPageReqDto whiteListPageReqDto);

    Collection<DesensitizationWhiteList> queryByIds(Set<Long> DesensitizationWhiteLists);

    List<DesensitizationWhiteList> queryAll();

}
