create table t_doctor_group_relation
(
    id                  bigint auto_increment                    not null primary key comment 'id',
    doctor_id           bigint                                   not null comment '医生id',
    doctor_name varchar(30) null comment '医生名称',
    doctor_group_source int                                      not null comment '医生团队来源:0,在线问诊;',
    doctor_group_id     bigint                                   not null comment '医生团队id',
    doctor_group_name   varchar(255)                             null comment '医生团队名称',
    create_time         datetime(3) default CURRENT_TIMESTAMP(3) not null,
    update_time         datetime(3)                              null on update CURRENT_TIMESTAMP(3),
    unique uidx_doctor_id_doctor_doctor_group_source_group_id (doctor_id, doctor_group_source, doctor_group_id)
) comment '医生团队关联表' ENGINE = InnoDB
                           DEFAULT CHARSET = utf8mb4
                           COLLATE = utf8mb4_unicode_ci;