package com.ynhdkc.tenant.service.backend.impl;

import backend.common.densensitized.enums.PrivacyType;
import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import backend.common.util.PasswordUtil;
import backend.common.util.ResponseEntityUtil;
import backend.security.service.BackendTenantUserService;
import backend.security.service.impl.DesensitizationWhiteListUtils;
import backend.security.until.DesensitizedUtils;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.client.PrivilegeClient;
import com.ynhdkc.tenant.client.UserClient;
import com.ynhdkc.tenant.client.model.RoleBoundUsersQueryReqDto;
import com.ynhdkc.tenant.client.model.RpcRoleBoundUsersQueryVo;
import com.ynhdkc.tenant.component.TenantUserLoginConfig;
import com.ynhdkc.tenant.dao.*;
import com.ynhdkc.tenant.entity.*;
import com.ynhdkc.tenant.entity.constant.TenantUserStatusEnum;
import com.ynhdkc.tenant.enums.TenantUserPassStatus;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.*;
import com.ynhdkc.tenant.tool.DateUtils;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2023/2/16 14:29:28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TenantUserServiceImpl implements TenantUserService {
    private static final String ALLOW_QUERY_UN_DESENSITIZATION_TENANT = "dyt-tenant:desensitization:allow-query-un-desensitization-tenant:";
    private final TenantUserQuery tenantUserQuery;
    private final TenantUserRepository tenantUserRepository;
    private final OrganizationStructureRepository organizationStructureRepository;
    private final TenantQuery tenantQuery;
    private final HospitalQuery hospitalQuery;
    private final HospitalAreaQuery hospitalAreaQuery;
    private final DepartmentQuery departmentQuery;
    private final OrganizationStructureQuery organizationStructureQuery;
    private final UserClient userClient;
    private final TenantService tenantService;
    private final HospitalService hospitalService;
    private final HospitalAreaService hospitalAreaService;
    private final DepartmentService departmentService;
    private final OrganizationStructureService organizationStructureService;
    private final BackendTenantUserService backendTenantUserService;
    private final PrivilegeClient privilegeClient;
    private final PageVoConvert pageVoConvert;
    private final TenantUserPasswordChangeLogMapper tenantUserPasswordChangeLogRepository;
    private final TenantUserLoginConfig tenantUserLoginConfig;
    private final TenantUserPasswordChangeLogQuery tenantUserPasswordChangeLogQuery;
    private final DesensitizationWhiteListUtils desensitizationWhiteListUtils;
    private final RedisTemplate<String, Object> redisTemplate;

    private void uniqueCheckIfExists(String userName, String phoneNumber, String email) {
        if (null != userName && tenantUserQuery.countByName(userName) > 0) {
            throw new BizException(HttpStatus.CONFLICT, "用户名已存在");
        }
        if (null != phoneNumber && tenantUserQuery.countByPhoneNumber(phoneNumber) > 0) {
            throw new BizException(HttpStatus.CONFLICT, "手机号已存在");
        }
        if (null != email && tenantUserQuery.countByEmail(email) > 0) {
            throw new BizException(HttpStatus.CONFLICT, "邮箱已存在");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TenantUserDetailVo create(TenantUserCreateReqDto request) {
        uniqueCheckIfExists(request.getName(), request.getPhoneNumber(), request.getEmail());

        boolean isSuperAdmin = backendTenantUserService.isSuperAdmin();
        /* 非超管校验是否拥有创建组织架构权限 */
        if (!isSuperAdmin) {
            checkUserTenantPrivilegeConfigs(request.getUserTenantPrivilegeConfigs(), true);
        }

        TenantUser tenantUser = TenantUserService.toTenantUser(request);
        String password = PasswordUtil.newPassword(PasswordUtil.PasswordEncryptType.BCrypt, request.getPassword());
        tenantUser.setPassword(password);
        if (null == request.getStatus()) {
            tenantUser.setStatus(TenantUserStatusEnum.NORMAL.getValue());
        }

        tenantUserRepository.save(tenantUser);
        TenantUserDetailVo vo = TenantUserService.toTenantUserDetailVo(tenantUser);
        addTenantUserPasswordChangeLog(tenantUser.getId(), password);
        organizationStructureService.createOrUpdateUserStructure(vo.getId(), request.getUserTenantPrivilegeConfigs());

        organizationStructureService.asyncUserOrganizationStructureAndUser(vo.getId());

        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseOperationResponse delete(Long userId) {
        checkTenantUserStructureByTenantId(userId);
        tenantUserRepository.delete(userId);
        organizationStructureService.asyncDeleteUserOrganizationStructure(userId);
        return new BaseOperationResponse("删除成功");
    }

    private void deleteTenantUserStructureByTenantId(Long userId, Long tenantId) {
        checkTenantUserStructureByTenantId(userId);
        tenantUserRepository.delete(userId, tenantId);
        organizationStructureService.asyncDeleteUserOrganizationStructure(userId);
    }


    private void checkTenantUserStructureByTenantId(Long userId) {
        TenantUser tenantUser = tenantUserQuery.queryByUserId(userId);
        if (tenantUser == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "用户不存在");
        }
        List<TenantUserStructure> userStructures = organizationStructureQuery.queryUserStructure(new OrganizationStructureQuery.OrganizationStructureQueryOption(1, Integer.MAX_VALUE)
                .setUserId(userId));
        if (CollectionUtils.isEmpty(userStructures)) {
            throw new BizException(HttpStatus.NOT_FOUND, "用户已删除所有该租户关系");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TenantUserDetailVo update(Long tenantUserId, TenantUserUpdateReqDto request) {
        TenantUser tenantUser = tenantUserQuery.queryByUserId(tenantUserId);
        if (tenantUser == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "用户不存在");
        }
        if (!backendTenantUserService.isSuperAdmin()) {
            /* 非超管不允许更新账户名 */
            request.setName(null);
            if (null != request.getPassword()) {
                throw new BizException(HttpStatus.FORBIDDEN, "非超管不允许重置密码");
            }
            /* 非超管校验是否拥有创建组织架构权限 */
            checkUserTenantPrivilegeConfigs(request.getUserTenantPrivilegeConfigs(), false);
        }
        updateIfNotNull(tenantUser, request);
        checkPassword(tenantUser, request.getPassword());
        tenantUserRepository.update(tenantUser);
        TenantUserDetailVo vo = TenantUserService.toTenantUserDetailVo(tenantUser);

        organizationStructureService.createOrUpdateUserStructure(vo.getId(), request.getUserTenantPrivilegeConfigs());

        organizationStructureService.asyncUserOrganizationStructureAndUser(vo.getId());

        /* 补充用户信息 */
        Set<Long> tenantIds = request.getUserTenantPrivilegeConfigs().stream().map(UserTenantPrivilegeConfig::getTenantId).collect(Collectors.toSet());
        importRolesSelective(vo, tenantIds);
        importUserPrivilegeConfigsSelective(vo, tenantIds);
        return vo;
    }

    @Override
    public TenantUserDetailVo getDetail(Long userId, Long tenantId) {
        boolean isSuperAdmin = backendTenantUserService.isSuperAdmin();
        /* 初始化查询选项 */
        TenantUserQuery.TenantUserQueryOption option = new TenantUserQuery.TenantUserQueryOption(1, Integer.MAX_VALUE)
                .setId(userId);
        if (!isSuperAdmin) {
            /* 根据根据组织架构查询 */
            OrganizationStructureQuery.OrganizationStructureQueryOption structQueryOption = new OrganizationStructureQuery.OrganizationStructureQueryOption(1, Integer.MAX_VALUE)
                    .setTenantId(tenantId);
            /* 用户组织架构约束 */
            structQueryOption.setIncludeTenantIds(backendTenantUserService.getCurrentUserTenantReadRange(true))
                    .setIncludeHospitalIds(backendTenantUserService.getCurrentUserHospitalReadRange(true))
                    .setIncludeHospitalAreaIds(backendTenantUserService.getCurrentUserHospitalAreaReadRange(true))
                    .setIncludeDepartmentIds(backendTenantUserService.getCurrentUserDepartmentReadRange(true));
            List<TenantUserStructure> tenantUserStructures = organizationStructureQuery.queryUserStructure(structQueryOption);
            Set<Long> structureUsers = tenantUserStructures.stream().map(TenantUserStructure::getUserId).collect(Collectors.toSet());
            if (structureUsers.isEmpty()) {
                structureUsers.add(-1L);
            }
            option.setIncludingIds(structureUsers);
        }
        try (Page<TenantUser> userPage = tenantUserQuery.query(option)) {
            if (userPage.isEmpty()) {
                throw new BizException(HttpStatus.NOT_FOUND, "用户不存在");
            }
            if (userPage.size() > 1) {
                throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "用户查询错误");
            }
            TenantUserDetailVo vo = TenantUserService.toTenantUserDetailVo(userPage.get(0));

            /* 补充用户信息 */
            if (null == tenantId) {
                Set<Long> currentUserTenantReadRange = backendTenantUserService.getCurrentUserTenantReadRange(true);
                importRolesSelective(vo, currentUserTenantReadRange);
                importUserPrivilegeConfigsSelective(vo, currentUserTenantReadRange);
            } else {
                importRolesSelective(vo, Collections.singleton(tenantId));
                importUserPrivilegeConfigsSelective(vo, Collections.singleton(tenantId));
            }
            return vo;
        }
    }

    @Override
    public TenantUserPageVo query(TenantUserQueryReqDto request) {
        boolean isSuperAdmin = backendTenantUserService.isSuperAdmin();
        checkStructure(isSuperAdmin, request.getTenantId(), request.getHospitalId(), request.getHospitalAreaId(), request.getDepartmentId());
        /* 初始化查询选项 */
        TenantUserQuery.TenantUserQueryOption option = new TenantUserQuery.TenantUserQueryOption(request.getCurrentPage(), request.getPageSize())
                .setNameLike(request.getName())
                .setNicknameLike(request.getNickname())
                .setPhoneNumber(request.getPhoneNumber())
                .setIdCardNo(request.getIdCardNo())
                .setStatus(request.getStatus())
                .setStartCreateTime(request.getStartCreateTime())
                .setEndCreateTime(request.getEndCreateTime());

        /* 如果需要根据角色ID查询 */
        if (null != request.getRoleId()) {
            ResponseEntity<RpcRoleBoundUsersQueryVo> rpcResp = privilegeClient.queryRoleBoundUsers(new RoleBoundUsersQueryReqDto()
                    .roleId(request.getRoleId()));
            RpcRoleBoundUsersQueryVo vo = ResponseEntityUtil.assertAndGetBody(rpcResp, "远程调用异常");
            /* 未查询到绑定该角色的用户 */
            if (vo.getBoundList().isEmpty()) {
                TenantUserPageVo emptyPage = new TenantUserPageVo()
                        ._list(Collections.emptyList());
                emptyPage.totalSize(0L)
                        .currentPage(request.getCurrentPage())
                        .pageSize(request.getPageSize());
                return emptyPage;
            }
            option.setIncludingRoleUserIds(vo.getBoundList());
        }

        /* 根据根据组织架构查询 */
        OrganizationStructureQuery.OrganizationStructureQueryOption structQueryOption = new OrganizationStructureQuery.OrganizationStructureQueryOption(1, Integer.MAX_VALUE)
                .setTenantId(request.getTenantId())
                .setHospitalId(request.getHospitalId())
                .setHospitalAreaId(request.getHospitalAreaId())
                .setDepartmentId(request.getDepartmentId());
        /* 如果是超管，不对范围进行约束 */
        if (!isSuperAdmin) {
            /* 用户组织架构约束 */
            structQueryOption.setIncludeTenantIds(backendTenantUserService.getCurrentUserTenantReadRange(true))
                    .setIncludeHospitalIds(backendTenantUserService.getCurrentUserHospitalReadRange(true))
                    .setIncludeHospitalAreaIds(backendTenantUserService.getCurrentUserHospitalAreaReadRange(true))
                    .setIncludeDepartmentIds(backendTenantUserService.getCurrentUserDepartmentReadRange(true))
                    .setExcludeUserIds(Collections.singleton(1L));
        }
        List<TenantUserStructure> tenantUserStructures = organizationStructureQuery.queryUserStructure(structQueryOption);
        Set<Long> structureUsers = tenantUserStructures.stream().map(TenantUserStructure::getUserId).collect(Collectors.toSet());
        if (structureUsers.isEmpty()) {
            structureUsers.add(-1L);
        }
        option.setIncludingIds(structureUsers);

        /* 查询用户列表 */
        try (Page<TenantUser> tenantUserPage = tenantUserQuery.query(option)) {
            TenantUserPageVo pageVo = pageVoConvert.toPageVo(tenantUserPage, TenantUserPageVo.class, TenantUserService::toTenantUserVo);
            /* 补充角色信息 */
            Boolean isNeedDesensitization = desensitizationWhiteListUtils.needDesensitization(backendTenantUserService.getCurrentUserId());
            pageVo.getList().forEach(vo -> {
                this.importRolesSelective(vo, null != request.getTenantId() ? Collections.singleton(request.getTenantId()) : null);
                if (isNeedDesensitization) {
                    vo.setEmail(DesensitizedUtils.desensitize(PrivacyType.EMAIL, vo.getEmail()));
                }
            });
            return pageVo;
        }
    }

    @Override
    public List<TenantUserVo> getTenantBoundUser(Long id) {
        // TODO: 可以改成从组织架构树查询用户
        List<TenantUserStructure> tenantUserStructures = organizationStructureQuery.queryUserStructure(new OrganizationStructureQuery.OrganizationStructureQueryOption(1, Integer.MAX_VALUE)
                .setTenantId(id));
        Set<Long> users = tenantUserStructures.stream().map(TenantUserStructure::getUserId).collect(Collectors.toSet());
        return tenantUserQuery.queryByUserIds(users).stream().map(TenantUserService::toTenantUserVo).collect(Collectors.toList());
    }

    @Override
    public List<TenantVo> getUserBoundTenants(Long userId, String tenantName) {
        Set<Long> tenants = backendTenantUserService.getUserTenantReadRange(userId, true);
        return tenantQuery.queryByIds(tenants).stream().filter(tenant -> null == tenantName || tenant.getName().contains(tenantName)).map(TenantService::toTenantVo).collect(Collectors.toList());
    }

    @Override
    public BaseOperationResponse bindTenant(Long tenantUserId, List<UserTenantPrivilegeConfig> userTenantPrivilegeConfigList) {

        TenantUser tenantUser = tenantUserQuery.queryByUserId(tenantUserId);
        if (tenantUser == null) {
            throw new BizException(HttpStatus.NOT_FOUND, "用户不存在");
        }

        if (CollectionUtils.isEmpty(userTenantPrivilegeConfigList)) {
            throw new BizException(HttpStatus.BAD_REQUEST, "租户用户配置不能为空");
        }

        TenantUserDetailVo vo = TenantUserService.toTenantUserDetailVo(tenantUser);

        organizationStructureService.createOrUpdateUserStructure(vo.getId(), userTenantPrivilegeConfigList);

        organizationStructureService.asyncUserOrganizationStructureAndUser(vo.getId());

        /* 补充用户信息 */
        Set<Long> tenantIds = userTenantPrivilegeConfigList.stream().map(UserTenantPrivilegeConfig::getTenantId).collect(Collectors.toSet());
        importRolesSelective(vo, tenantIds);
        importUserPrivilegeConfigsSelective(vo, tenantIds);
        return new BaseOperationResponse("绑定成功");

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseOperationResponse unbindTenant(Long userId, List<Long> tenantIdList) {
        if (CollectionUtils.isEmpty(tenantIdList)) {
            throw new BizException(HttpStatus.BAD_REQUEST, "用户ID不能为空");
        }
        tenantIdList.forEach(id -> deleteTenantUserStructureByTenantId(userId, id));
        List<TenantVo> tenantVos = this.getUserBoundTenants(userId, null);
        if (CollectionUtils.isEmpty(tenantVos)) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "最少要保留一条租户信息");
        }
        return new BaseOperationResponse("解绑成功");
    }

    @Override
    public BaseOperationResponse sendMsg(TenantUserSendMegReqDto tenantUserSendMegReqDto) {
        List<TenantUser> tenantUserList = tenantUserQuery.queryByNameOrPhone(tenantUserSendMegReqDto.getUserName());
        if (!CollectionUtils.isEmpty(tenantUserList)) {
            userClient.sendMessage(tenantUserList.get(0).getPhoneNumber(), 7, 4);
            return new BaseOperationResponse("发送成功，已发送到手机尾号" + tenantUserList.get(0).getPhoneNumber().substring(7, 11));
        }
        throw new BizException(HttpStatus.NOT_FOUND, "用户不存在");
    }

    @Override
    public BaseOperationResponse sendDesensitizationMsg() {
        Long tenantUserId = backendTenantUserService.getCurrentUserId();
        if (!desensitizationWhiteListUtils.whiteListIsContainTenantUser(tenantUserId)) {
            throw new BizException(HttpStatus.NOT_FOUND, "当前用户未被授权允许查看敏感数据，如有需要请联系系统管理员授权！");
        }
        TenantUser tenantUser = tenantUserQuery.queryByUserId(tenantUserId);
        if (null != tenantUser) {
            userClient.sendMessage(tenantUser.getPhoneNumber(), 8, 4);
            return new BaseOperationResponse("发送成功，已发送到手机尾号" + tenantUser.getPhoneNumber().substring(7, 11));
        }
        throw new BizException(HttpStatus.NOT_FOUND, "用户不存在");
    }

    @Override
    public BaseOperationResponse desensitizationSmsVerify(String smsCode) {
        TenantUser tenantUser = tenantUserQuery.queryByUserId(backendTenantUserService.getCurrentUserId());
        userClient.verifyMessage(tenantUser.getPhoneNumber(), 8, 4, smsCode);
        redisTemplate.opsForValue().set(ALLOW_QUERY_UN_DESENSITIZATION_TENANT + tenantUser.getId(), tenantUser.getId(), 60, TimeUnit.MINUTES);
        return new BaseOperationResponse("验证成功,授权有效期30分钟！");
    }

    @Override
    public TenantUserDetailVo getUserDetail(Long userId) {
        TenantUser tenantUser = tenantUserQuery.queryByUserId(userId);
        if (null == tenantUser) {
            throw new BizException(HttpStatus.NOT_FOUND, "用户不存在");
        }
        TenantUserDetailVo vo = new TenantUserDetailVo();
        vo.name(tenantUser.getName());
        vo.nickname(tenantUser.getNickname());
        vo.headImageUrl(tenantUser.getHeadImageUrl());
        vo.phoneNumber(tenantUser.getPhoneNumber());
        return vo;
    }

    @Override
    public BaseOperationResponse modify(Long userId, TenantUserModifyReqDto request) {
        TenantUser tenantUser = tenantUserQuery.queryByUserId(userId);
        if (null == tenantUser) {
            throw new BizException(HttpStatus.NOT_FOUND, "用户不存在");
        }
        if (null != request.getPhoneNumber()) {
            Integer countByPhoneNumber = tenantUserQuery.countByPhoneNumber(request.getPhoneNumber());
            if (countByPhoneNumber > 0 && !tenantUser.getPhoneNumber().equals(request.getPhoneNumber())) {
                throw new BizException(HttpStatus.CONFLICT, "手机号已存在");
            }
        }
        if (StringUtils.hasText(request.getPassword()) && null == request.getOldPassword()) {
            throw new BizException(HttpStatus.BAD_REQUEST, "旧密码不能为空");
        } else {
            if (!PasswordUtil.isMatch(PasswordUtil.PasswordEncryptType.BCrypt, request.getOldPassword(), tenantUser.getPassword())) {
                throw new BizException(HttpStatus.BAD_REQUEST, "旧密码错误，请核对！");
            }
        }

        if (null != request.getNickname()) {
            tenantUser.setNickname(request.getNickname());
        }
        if (null != request.getPhoneNumber()) {
            tenantUser.setPhoneNumber(request.getPhoneNumber());
        }
        if (null != request.getHeadImageUrl()) {
            tenantUser.setHeadImageUrl(request.getHeadImageUrl());
        }

        checkPassword(tenantUser, request.getPassword());
        tenantUserRepository.update(tenantUser);

        return new BaseOperationResponse("修改成功");
    }

    @Override
    public CheckTenantUserPasswordResultVo checkPassword(Long id) {
        if (null != id) {
            TenantUser tenantUser = tenantUserQuery.queryByUserId(id);
            CheckTenantUserPasswordResultVo checkTenantUserPasswordResultVo = new CheckTenantUserPasswordResultVo();
            if (null != tenantUser) {
                TenantUserPasswordChangeLog lastChangeLog = tenantUserPasswordChangeLogQuery.queryLastByUserId(tenantUser.getId());
                if (DateUtils.getDaysBetween(lastChangeLog.getCreateTime().getTime(), System.currentTimeMillis()) >= tenantUserLoginConfig.getPasswordExpiryDays()) {
                    checkTenantUserPasswordResultVo.setResult(TenantUserPassStatus.PASSWORD_EXPIRED.getCode());
                    checkTenantUserPasswordResultVo.setDesc(TenantUserPassStatus.PASSWORD_EXPIRED.getDesc());
                    return checkTenantUserPasswordResultVo;
                }
                checkTenantUserPasswordResultVo.setResult(TenantUserPassStatus.NORMAL.getCode());
                checkTenantUserPasswordResultVo.setDesc(TenantUserPassStatus.NORMAL.getDesc());
                return checkTenantUserPasswordResultVo;
            }
        }
        return null;
    }

    private void importRolesSelective(TenantUserVo user, Set<Long> tenantIds) {
        ResponseEntity<RpcUserBoundRolesQueryVo> rpcResp = privilegeClient.queryUserBoundRoles(user.getId(), null);
        RpcUserBoundRolesQueryVo vo = ResponseEntityUtil.assertAndGetBody(rpcResp, "远程调用异常或未查询到用户角色信息");

        List<RoleVo> roleVos;
        if (null != tenantIds) {
            roleVos = vo.getRoles().stream().filter(role -> tenantIds.contains(role.getTenantId())).collect(Collectors.toList());
        } else {
            roleVos = vo.getRoles();
        }
        user.roles(roleVos);
    }

    private void importUserPrivilegeConfigsSelective(TenantUserDetailVo user, Set<Long> tenantIds) {
        List<UserTenantPrivilegeConfig> userTenantPrivilegeConfig = backendTenantUserService.getUserTenantPrivilegeConfig(user.getId())
                .stream().filter(config -> tenantIds.contains(config.getTenantId())).collect(Collectors.toList());
        user.setUserTenantPrivilegeConfigs(userTenantPrivilegeConfig);
    }

    private void checkStructure(boolean isSuperAdmin, Long tenantId, Long hospitalId, Long hospitalAreaId, Long
            departmentId) {
        if (null == tenantId && !isSuperAdmin) {
            throw new BizException(HttpStatus.FORBIDDEN, "必须指定租户ID");
        }
        if (null != tenantId) {
            tenantService.getDetail(tenantId);
        }
        if (null != hospitalId) {
            hospitalService.getDetail(hospitalId);
        }
        if (null != hospitalAreaId) {
            hospitalAreaService.getDetail(hospitalAreaId);
        }
        if (null != departmentId) {
            departmentService.getDetail(departmentId);
        }
    }

    private void addTenantUserPasswordChangeLog(Long userId, String password) {
        tenantUserPasswordChangeLogRepository.insert(new TenantUserPasswordChangeLog(userId, password));
    }

    private void checkUserTenantPrivilegeConfigs(List<UserTenantPrivilegeConfig> tenantPrivilegeConfigs, boolean rootNotEmpty) {
        if (CollectionUtils.isEmpty(tenantPrivilegeConfigs)) {
            if (rootNotEmpty) {
                throw new BizException(HttpStatus.BAD_REQUEST, "用户权限配置不能为空");
            } else {
                return;
            }
        }

        tenantPrivilegeConfigs.forEach(tenantConfig -> {
            Tenant tenant = tenantQuery.queryTenantById(tenantConfig.getTenantId());
            if (null == tenant) {
                throw new BizException(HttpStatus.NOT_FOUND, "租户不存在");
            }
            /* 校验角色 */
            if (!CollectionUtils.isEmpty(tenantConfig.getBindRoles())) {
                tenantConfig.getBindRoles().forEach(roleId -> this.checkRole(tenantConfig.getTenantId(), roleId));
            }
            if (!CollectionUtils.isEmpty(tenantConfig.getUnbindRoles())) {
                tenantConfig.getUnbindRoles().forEach(roleId -> this.checkRole(tenantConfig.getTenantId(), roleId));
            }
            /* 非超管不能创建租户管理员 */
            boolean isTenantAdmin = Boolean.TRUE.equals(tenantConfig.isTenantAdmin());
            if (isTenantAdmin) {
                throw new BizException(HttpStatus.FORBIDDEN, "不能创建租户管理员");
            }
            if (CollectionUtils.isEmpty(tenantConfig.getHospitalPrivilegeConfigs())) {
                throw new BizException(HttpStatus.BAD_REQUEST, "非租户管理员必须指定医院归属");
            }
            tenantConfig.getHospitalPrivilegeConfigs().forEach(hospitalConfig -> {
                Hospital hospital = hospitalQuery.queryHospitalById(hospitalConfig.getHospitalId());
                if (null == hospital) {
                    throw new BizException(HttpStatus.NOT_FOUND, "医院不存在");
                }
                if (!hospital.getTenantId().equals(tenantConfig.getTenantId())) {
                    throw new BizException(HttpStatus.NOT_FOUND, "医院不属于该租户");
                }
                boolean isHospitalAdmin = Boolean.TRUE.equals(hospitalConfig.isHospitalAdmin());
                /* 租户管理员才可以创建医院管理员 */
                if (isHospitalAdmin) {
                    if (backendTenantUserService.isCurrentLayerAdmin(new BackendTenantUserService.TenantId(tenantConfig.getTenantId()))) {
                        return;
                    }
                    throw new BizException(HttpStatus.FORBIDDEN, "租户管理员才可以创建医院管理员");
                }
                if (CollectionUtils.isEmpty(hospitalConfig.getHospitalAreaPrivilegeConfigs())) {
                    throw new BizException(HttpStatus.BAD_REQUEST, "非医院管理员必须配置院区归属");
                }
                hospitalConfig.getHospitalAreaPrivilegeConfigs().forEach(hospitalAreaConfig -> {
                    Hospital hospitalArea = hospitalAreaQuery.queryHospitalAreaById(hospitalAreaConfig.getHospitalAreaId());
                    if (null == hospitalArea) {
                        throw new BizException(HttpStatus.NOT_FOUND, "院区不存在");
                    }
                    if (!hospitalArea.getParentId().equals(hospitalConfig.getHospitalId())) {
                        throw new BizException(HttpStatus.NOT_FOUND, "院区不属于该医院");
                    }
                    /* 医院管理员才可以创建院区管理员 */
                    boolean isHospitalAreaAdmin = Boolean.TRUE.equals(hospitalAreaConfig.isHospitalAreaAdmin());
                    if (isHospitalAreaAdmin) {
                        if (backendTenantUserService.isCurrentLayerAdmin(new BackendTenantUserService.HospitalId(hospitalConfig.getHospitalId()))) {
                            return;
                        }
                        throw new BizException(HttpStatus.FORBIDDEN, "医院管理员才可以创建院区管理员");
                    }
                    if (CollectionUtils.isEmpty(hospitalAreaConfig.getDepartments())) {
                        throw new BizException(HttpStatus.BAD_REQUEST, "非院区管理员必须配置科室归属");
                    }
                    hospitalAreaConfig.getDepartments().forEach(departmentConfig -> {
                        Department department = departmentQuery.queryDepartmentById(departmentConfig.getDepartmentId());
                        if (null == department) {
                            throw new BizException(HttpStatus.NOT_FOUND, "科室不存在");
                        }
                        if (!department.getHospitalAreaId().equals(hospitalAreaConfig.getHospitalAreaId())) {
                            throw new BizException(HttpStatus.NOT_FOUND, "科室不属于该院区");
                        }
                        /* 院区管理员才可以创建科室管理员 */
                        boolean isDepartmentAdmin = departmentConfig.isDepartmentAdmin();
                        if (isDepartmentAdmin) {
                            if (backendTenantUserService.isCurrentLayerAdmin(new BackendTenantUserService.HospitalAreaId(hospitalAreaConfig.getHospitalAreaId()))) {
                                return;
                            }
                            throw new BizException(HttpStatus.FORBIDDEN, "院区管理员才可以创建科室管理员");
                        }
                        if (!backendTenantUserService.isCurrentLayerAdmin(new BackendTenantUserService.DepartmentId(departmentConfig.getDepartmentId()))) {
                            throw new BizException(HttpStatus.FORBIDDEN, "科室管理员才可以创建科室成员");
                        }
                    });
                });
            });
        });
    }

    private void checkRole(Long tenantId, Long roleId) {
        ResponseEntity<RoleVo> rpcResp = privilegeClient.getRoleById(roleId);
        RoleVo roleVo = ResponseEntityUtil.assertAndGetBody(rpcResp, "远程调用异常或未查询到角色信息");
        if (!roleVo.getTenantId().equals(tenantId)) {
            throw new BizException(HttpStatus.FORBIDDEN, "租户不存在该角色");
        }
    }

    private Boolean checkPasswordIsChange(String newPassword, String oldPassword) {
        return newPassword.equals(oldPassword);
    }

    private Boolean validatePassword(String password, Long tenantUserId) {
        List<TenantUserPasswordChangeLog> passwordChangeLogs = tenantUserPasswordChangeLogQuery.queryByUserId(tenantUserId);
        if (!CollectionUtils.isEmpty(passwordChangeLogs)) {
            return passwordChangeLogs.stream().limit(tenantUserLoginConfig.getPasswordHistoryCount()).noneMatch(passwordChangeLog ->
                    PasswordUtil.isMatch(PasswordUtil.PasswordEncryptType.BCrypt, password, passwordChangeLog.getOldPassword()
                    )
            );
        }
        return true;
    }

    private void checkPassword(TenantUser tenantUser, String password) {
        if (null != password) {
            String newPassword = PasswordUtil.newPassword(PasswordUtil.PasswordEncryptType.BCrypt, password);
            if (!checkPasswordIsChange(tenantUser.getPassword(), newPassword)) {
                if (!validatePassword(password, tenantUser.getId())) {
                    throw new BizException(HttpStatus.BAD_REQUEST, String.format("密码不能与最近【%s】次相同", tenantUserLoginConfig.getPasswordHistoryCount()));
                }
                addTenantUserPasswordChangeLog(tenantUser.getId(), newPassword);
            }
            tenantUser.setPassword(newPassword);
        }
    }

    private void updateIfNotNull(TenantUser entity, TenantUserUpdateReqDto dto) {
        if (null != dto.getName()) {
            entity.setName(dto.getName());
        }
        if (null != dto.getNickname()) {
            entity.setNickname(dto.getNickname());
        }
        if (null != dto.getPhoneNumber()) {
            entity.setPhoneNumber(dto.getPhoneNumber());
        }
        if (null != dto.getGender()) {
            entity.setGender(dto.getGender());
        }
        if (null != dto.getIdCardNo()) {
            entity.setIdCardNo(dto.getIdCardNo());
        }

        if (null != dto.getStatus()) {
            entity.setStatus(dto.getStatus());
        }
        if (null != dto.getHeadImageUrl()) {
            entity.setHeadImageUrl(dto.getHeadImageUrl());
        }
        if (null != dto.getPassword()) {
            entity.setPassword(PasswordUtil.newPassword(PasswordUtil.PasswordEncryptType.BCrypt, dto.getPassword()));
        }
    }
}
