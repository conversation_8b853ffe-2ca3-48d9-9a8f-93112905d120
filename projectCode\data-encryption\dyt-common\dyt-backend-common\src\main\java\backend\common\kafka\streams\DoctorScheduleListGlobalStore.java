package backend.common.kafka.streams;

import backend.common.constants.KafkaTopicConfig;
import backend.common.entity.dto.schedule.DoctorScheduleList;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.serialization.Serde;
import org.apache.kafka.common.serialization.Serdes;
import org.apache.kafka.streams.KeyValue;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.support.serializer.JsonSerde;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

@Slf4j
@Configuration
@ConditionalOnProperty(name = "backend.kafka.global-store.info.doctor-schedule-list", havingValue = "true")
public class DoctorScheduleListGlobalStore extends GlobalStoreKafkaStreamsCallback<String, DoctorScheduleList> {

    public static final JsonSerde<DoctorScheduleList> VALUE_SERDE = new JsonSerde<>(DoctorScheduleList.class).noTypeInfo();
    private final ConcurrentMap<String, DoctorScheduleList> key2value = new ConcurrentHashMap<>();

    @Override
    public void processUpdate(String key, DoctorScheduleList update, DoctorScheduleList old) {
        log.info("inner排班 global-store 有数据需要更新 key:{}, update:{}, old:{}", key, update, old);
        if (update == null && old == null) {
            return;
        }
        if (update == null) {
            key2value.remove(key);
        } else {
            key2value.put(key, update);
        }
    }

    @Override
    public String storeName() {
        return "doctor-schedule-list";
    }

    @Override
    public String sourceTopic() {
        return KafkaTopicConfig.DOCTOR_SCHEDULE_LIST;
    }

    @Override
    public Serde<DoctorScheduleList> valueSerde() {
        return VALUE_SERDE;
    }

    @Override
    public Serde<String> keySerde() {
        return Serdes.String();
    }

    @Override
    protected void doInitialize(KeyValue<String, DoctorScheduleList> next) {
        key2value.put(next.key, next.value);
    }

    /**
     * 获取医生排班列表
     *
     * @param key 组成 hospitalCode_departmentCode_doctorCode_yyyy-MM-dd
     * @return doctorScheduleList
     */
    public @Nullable
    DoctorScheduleList getDoctorScheduleList(String key) {
//        log.info(key2value.toString());
        return key2value.get(key);
    }

    /**
     * 从global store 获取医师排班数据
     *
     * @param hospitalCode   医院院区code
     * @param departmentCode 科室code
     * @param doctorCode     医生code
     * @return 医生排班列表
     */
    public @Nullable DoctorScheduleList getDoctorScheduleList(String hospitalCode, String departmentCode, String doctorCode) {
        String key = generateKey(hospitalCode, departmentCode, doctorCode);
        log.info("inner排班 global-store 通过key:{},获取排班数据:{}", key, key2value.get(key));
        return key2value.get(key);
    }

    @NonNull
    public String generateKey(@NonNull String hospitalCode, @NonNull String departmentCode, @NonNull String doctorCode) {
        return hospitalCode + "_" + departmentCode + "_" + doctorCode + "_" + new SimpleDateFormat("yyyy-MM-dd").format(new Date());
    }

}
