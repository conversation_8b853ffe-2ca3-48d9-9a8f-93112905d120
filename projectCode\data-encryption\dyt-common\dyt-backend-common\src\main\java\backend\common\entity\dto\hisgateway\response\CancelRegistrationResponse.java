package backend.common.entity.dto.hisgateway.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

@Data
public class CancelRegistrationResponse {

    @JsonProperty("hospital_code")
    private String hospitalCode;

    @JsonProperty("order_number")
    private String orderNumber;

    private Boolean result = false;

    private String reason;

    private Map<String, Object> rawParameters;
}
