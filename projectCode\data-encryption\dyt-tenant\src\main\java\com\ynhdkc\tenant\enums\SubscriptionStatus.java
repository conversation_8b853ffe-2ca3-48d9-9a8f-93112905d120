package com.ynhdkc.tenant.enums;

import java.util.HashMap;
import java.util.Map;

public enum SubscriptionStatus {
    PENDING_NOTIFICATION(0, "待通知"),
    STARTED_NOTIFICATION(1, "开始通知"),
    NOTIFICATION_COMPLETED(2, "通知结束"),
    NOTIFICATION_FAILED(3, "通知失败"),
    CANCELLED(-1, "已取消");

    private final Integer code;
    private final String description;

    SubscriptionStatus(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取对应的枚举对象
     *
     * @param code 状态代码
     * @return 对应的枚举对象，如果找不到则返回null
     */
    public static SubscriptionStatus fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SubscriptionStatus status : SubscriptionStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    /**
     * 获取所有枚举对象及其描述信息
     *
     * @return 包含枚举对象和描述信息的映射
     */
    public static Map<Integer, String> getDescriptions() {
        Map<Integer, String> descriptions = new HashMap<>();
        for (SubscriptionStatus status : SubscriptionStatus.values()) {
            descriptions.put(status.getCode(), status.getDescription());
        }
        return descriptions;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}