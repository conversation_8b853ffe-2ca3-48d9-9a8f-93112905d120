package com.ynhdkc.tenant.service.backend;

import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.model.*;
import org.springframework.lang.Nullable;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-03 12:40
 */
public interface IDoctorService {
    DoctorDetailVo create(DoctorCreateReqDto request);

    DoctorDetailVo update(Long doctorId, DoctorUpdateReqDto request);

    BaseOperationResponse delete(Long doctorId);

    DoctorDetailVo getDetail(Long doctorId);

    DoctorDetailVo rpcGetDetail(Long doctorId);

    DoctorKafkaVo getDetailForKafka(Long doctorId);

    DoctorPageVo query(DoctorQueryReqDto request);

    @Nullable
    List<DoctorKafkaVo> queryAllForKafka();

    List<DoctorKafkaVo> queryByDepartmentIdForKafkaVo(Long departmentId);

    List<DoctorDetailVo> rpcBatchGetDetail(RpcBatchGetByIdReqDto request);

    List<DoctorDetailVo> rpcBatchGetDetailByAreaId(Long areaId, Long departmentId);

    List<Long> rpcBatchGetIdLikeName(String name);

    DoctorGroupPageRespVo getDoctorGroupList(GetDoctorGroupPageReqDto request);

    DoctorGroupRelationVo bindDoctorGroup(DoctorBindGroupReqDto request);

    BaseOperationResponse unbindDoctorGroup(DoctorUnbindGroupReqDto request);

    DoctorGroupRelationQueryRespDto queryDoctorGroup(DoctorGroupRelationQueryReqDto request);

    BaseOperationResponse batchBindDoctorGroup(DoctorBatchBindGroupReqDto request);

    BaseOperationResponse reSortDoctor(Long hospitalAreaId, Long departmentId);

    void updateGongRenDoctorDepartmentInfo();

    BaseOperationResponse batchSyncDoctorInfoFromSpecifiedDoctor(BatchSyncDoctorInfoFromSpecifiedDoctorReqDto request);
}
