package backend.common.kafka.streams;

import backend.common.entity.dto.hisgateway.response.DepartmentDoctorItem;
import backend.common.entity.dto.hisgateway.response.DepartmentDoctorListResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.serialization.Serde;
import org.apache.kafka.common.serialization.Serdes;
import org.apache.kafka.streams.KeyValue;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.support.serializer.JsonSerde;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 * @since 2023/7/5 10:13:57
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "backend.kafka.global-store.info.his-department-doctor", havingValue = "true")
public class HisDepartmentDoctorGlobalStore extends GlobalStoreKafkaStreamsCallback<String, DepartmentDoctorListResponse> {
    public static final JsonSerde<DepartmentDoctorListResponse> VALUE_SERDE = new JsonSerde<>(DepartmentDoctorListResponse.class).noTypeInfo();
    private final ConcurrentMap<String, List<DepartmentDoctorItem>> key2value = new ConcurrentHashMap<>();

    @Override
    public void processUpdate(String key, DepartmentDoctorListResponse update, DepartmentDoctorListResponse old) {
        log.info("HisDepartmentDoctorGlobalStore processUpdate key:{}, update:{}, old:{}", key, update, old);
        if (update == null && old == null) {
            return;
        }
        if (update == null) {
            key2value.remove(key);
        } else {
            key2value.put(key, update.getResult());
        }
    }

    @Override
    public String storeName() {
        return "his-department-doctor";
    }

    @Override
    public String sourceTopic() {
        return "his-department-doctor";
    }

    @Override
    public Serde<DepartmentDoctorListResponse> valueSerde() {
        return VALUE_SERDE;
    }

    @Override
    public Serde<String> keySerde() {
        return Serdes.String();
    }

    @Override
    protected void doInitialize(KeyValue<String, DepartmentDoctorListResponse> next) {
        if (next.value != null) {
            key2value.put(next.key, next.value.getResult());
        }
    }

    /**
     * 根据医院编码和科室编码获取医生列表
     *
     * @param hospitalCodeAndDepartmentCode 医院编码和科室编码
     * @return 医生列表
     */
    public List<DepartmentDoctorItem> get(String hospitalCodeAndDepartmentCode) {
        return key2value.get(hospitalCodeAndDepartmentCode);
    }
}
