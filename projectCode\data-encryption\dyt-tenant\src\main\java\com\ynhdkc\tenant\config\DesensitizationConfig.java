package com.ynhdkc.tenant.config;

import backend.security.service.impl.DesensitizationWhiteListUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * <AUTHOR>
 */
@Configuration
public class DesensitizationConfig {

    @Bean
    public DesensitizationWhiteListUtils desensitizationWhiteListUtils(
            RedisTemplate<String, Object> redisTemplate) {
        return new DesensitizationWhiteListUtils(redisTemplate);
    }
}