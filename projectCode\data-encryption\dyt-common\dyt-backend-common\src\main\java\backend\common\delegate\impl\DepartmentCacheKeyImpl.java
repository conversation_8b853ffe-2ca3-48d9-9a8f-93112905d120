package backend.common.delegate.impl;

import backend.common.delegate.CacheKeyDelegate;
import backend.common.entity.dto.hisgateway.response.MessageEnvelope;
import backend.common.util.CacheKeyUtils;
import backend.common.util.ObjectsUtils;

import java.util.Map;

public class DepartmentCacheKeyImpl implements CacheKeyDelegate {

    private final String hisOperationValue;

    public DepartmentCacheKeyImpl(String hisOperationValue) {
        this.hisOperationValue = hisOperationValue;
    }

    @Override
    public String getCacheKey(MessageEnvelope<?> messageEnvelope, Map<String, Object> requestParameters) {
        String hospitalCode = ObjectsUtils.getStringValue(requestParameters, "hospital_code");
        if (ObjectsUtils.isEmpty(hospitalCode)) {
            return null;
        }
        String parentDeptCode = ObjectsUtils.getStringValue(requestParameters, "parent_department_code");
        if (ObjectsUtils.isEmpty(parentDeptCode)) {
            return CacheKeyUtils.getCache<PERSON>ey(hisOperationValue, hospitalCode);
        }
        return CacheKeyUtils.getCacheKey(hisOperationValue, hospitalCode, parentDeptCode);
    }
}
