package com.ynhdkc.tenant.api.customer;

import com.ynhdkc.tenant.handler.MaintenanceApi;
import com.ynhdkc.tenant.service.backend.IHisRequestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Slf4j
public class MaintenanceController implements MaintenanceApi {

    @Resource
    private IHisRequestService hisRequestService;

    @Override
    public ResponseEntity<Void> syncDoctorData(String hospitalCode) {
        new Thread(() -> hisRequestService.syncDoctorSchedule(hospitalCode)).start();
        return ResponseEntity.ok().build();
    }
}
