package com.ynhdkc.tenant.api.customer;

import backend.common.exception.BizException;
import backend.security.service.BackendClientUserService;
import com.ynhdkc.tenant.handler.CustomerDepartmentApi;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.customer.CustomerDepartmentService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/7/21 9:42
 */
@Slf4j
@Api(tags = "CustomerDepartment")
@RestController
@RequiredArgsConstructor
public class CustomerDepartmentController implements CustomerDepartmentApi {
    private final CustomerDepartmentService customerDepartmentService;

    private final BackendClientUserService backendClientUserService;

    @Override
    public ResponseEntity<CustomerDepartmentDetailVo> getDetail(Long departmentId) {
        return ResponseEntity.ok(customerDepartmentService.getDetail(departmentId));
    }

    @Override
    public ResponseEntity<CustomerDepartmentDetailVo> getDetailByCode(String hospitalAreaCode, String departmentCode) {
        return ResponseEntity.ok(customerDepartmentService.getDetailByCode(hospitalAreaCode, departmentCode));
    }

    @Override
    public ResponseEntity<List<CustomerDepartmentVo>> getRecentSuccess(Long hospitalAreaId) {
        return ResponseEntity.ok(customerDepartmentService.getRecentSuccess(backendClientUserService.getCurrentUserId(), hospitalAreaId));
    }

    @Override
    public ResponseEntity<CustomerDepartmentsTreeSearchListVo> queryChildrenTree(Long departmentId) {
        return ResponseEntity.ok(customerDepartmentService.queryChildrenTree(departmentId));
    }

    @Override
    public ResponseEntity<CustomerDepartmentPageVo> queryDepartmentPage(CustomerQueryDepartmentPageReqDto request) {
        return ResponseEntity.ok(customerDepartmentService.queryDepartmentPage(request));
    }

    @Override
    public ResponseEntity<CustomerDoctorScheduleInfoVo> queryGroupedDoctorListByDepartmentId(Long departmentId, String hospitalAreaCode, String departmentCode,Integer timeType) {
        if (departmentId != null) {
            return ResponseEntity.ok(customerDepartmentService.queryGroupedDoctorList(departmentId,timeType));
        }
        if (StringUtils.hasText(hospitalAreaCode) && StringUtils.hasText(departmentCode)) {
            return ResponseEntity.ok(customerDepartmentService.queryGroupedDoctorListBy(hospitalAreaCode, departmentCode));
        }


        throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "参数错误");
    }


    @Override
    public ResponseEntity<CustomerDepartmentsTreeSearchListVo> getSubDepartments(Long departmentId) {
        return ResponseEntity.ok(customerDepartmentService.getSubDepartments(departmentId));
    }

    @Override
    public ResponseEntity<List<CustomerAllScheduleDoctorDetailVo>> querySpecialNeedsDoctorList(Long hospitalAreaId, Long departmentId, String doctorName) {
        return ResponseEntity.ok(customerDepartmentService.querySpecialNeedsDoctorList(hospitalAreaId, departmentId, doctorName));
    }

    @Override
    public ResponseEntity<CustomerDepartmentsTreeSearchListVo> queryTree(Long hospitalAreaId, Integer category, Integer level) {
        return ResponseEntity.ok(customerDepartmentService.queryTree(hospitalAreaId, category, level));
    }
}
