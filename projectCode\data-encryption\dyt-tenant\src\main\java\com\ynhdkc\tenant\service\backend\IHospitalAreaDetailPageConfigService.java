package com.ynhdkc.tenant.service.backend;

import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.entity.detailpage.HospitalAreaDetailPageConfig;
import com.ynhdkc.tenant.model.*;

/**
 * <AUTHOR>
 * @since 2023/5/24 14:31:08
 */
public interface IHospitalAreaDetailPageConfigService {
    HospitalAreaDetailPageConfigVo createHospitalAreaDetailPageConfig(CreateHospitalAreaDetailPageConfigReqDto dto);

    BaseOperationResponse deleteHospitalAreaDetailPageConfig(Long id);


    HospitalAreaDetailPageConfigVo getHospitalAreaDetailPageConfig(Long id);

    HospitalAreaDetailPageConfigPageVo searchHospitalAreaDetailPageConfig(SearchHospitalAreaDetailPageConfigReqDto searchHospitalDetailPageConfigReqDto);

    HospitalAreaDetailPageConfigVo updateHospitalAreaDetailPageConfig(Long id, UpdateHospitalAreaDetailPageConfigReqDto updateHospitalDetailPageConfigReqDto);

    void setHospitalAreaDetailPageConfig(HospitalAreaLayoutVo vo, Long hospitalAreaId);

    static HospitalAreaDetailPageConfigVo toHospitalAreaDetailPageConfigVo(HospitalAreaDetailPageConfig entity) {
        HospitalAreaDetailPageConfigVo vo = new HospitalAreaDetailPageConfigVo();
        vo.setId(entity.getId());
        vo.setTenantId(entity.getTenantId());
        vo.setHospitalId(entity.getHospitalId());
        vo.setHospitalAreaId(entity.getHospitalAreaId());
        vo.setDisplayHospitalArea(entity.getDisplayHospitalArea());
        vo.setDisplayAddress(entity.getDisplayAddress());
        vo.setDisplayLevel(entity.getDisplayLevel());
        vo.setDisplayOpenSchedulingTime(entity.getDisplayOpenSchedulingTime());
        vo.setDisplayAppointNotice(entity.getDisplayAppointNotice());
        vo.setDisplayNotice(entity.getDisplayNotice());
        vo.setDisplayFloorDistribution(entity.getDisplayFloorDistribution());
        vo.setDisplayTab(entity.getDisplayTab());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdateTime(entity.getUpdateTime());
        return vo;
    }

    default HospitalAreaDetailPageConfig toHospitalAreaDetailPageConfig(CreateHospitalAreaDetailPageConfigReqDto dto) {
        HospitalAreaDetailPageConfig entity = new HospitalAreaDetailPageConfig();
        entity.setTenantId(dto.getTenantId());
        entity.setHospitalId(dto.getHospitalId());
        entity.setHospitalAreaId(dto.getHospitalAreaId());
        entity.setDisplayHospitalArea(dto.isDisplayHospitalArea());
        entity.setDisplayAddress(dto.isDisplayAddress());
        entity.setDisplayLevel(dto.isDisplayLevel());
        entity.setDisplayOpenSchedulingTime(dto.isDisplayOpenSchedulingTime());
        entity.setDisplayAppointNotice(dto.isDisplayAppointNotice());
        entity.setDisplayNotice(dto.isDisplayNotice());
        entity.setDisplayFloorDistribution(dto.isDisplayFloorDistribution());
        entity.setDisplayTab(dto.isDisplayTab());
        return entity;
    }

    default void toHospitalAreaDetailPageConfig(UpdateHospitalAreaDetailPageConfigReqDto dto, HospitalAreaDetailPageConfig entity) {
        entity.setDisplayHospitalArea(dto.isDisplayHospitalArea());
        entity.setDisplayAddress(dto.isDisplayAddress());
        entity.setDisplayLevel(dto.isDisplayLevel());
        entity.setDisplayOpenSchedulingTime(dto.isDisplayOpenSchedulingTime());
        entity.setDisplayAppointNotice(dto.isDisplayAppointNotice());
        entity.setDisplayNotice(dto.isDisplayNotice());
        entity.setDisplayFloorDistribution(dto.isDisplayFloorDistribution());
        entity.setDisplayTab(dto.isDisplayTab());
    }
}
