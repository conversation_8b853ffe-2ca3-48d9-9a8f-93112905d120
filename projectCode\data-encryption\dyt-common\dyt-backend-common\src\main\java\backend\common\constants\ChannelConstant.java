package backend.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 渠道标识常量
 */
public interface ChannelConstant {

    @AllArgsConstructor
    @Getter
    enum WechatMpChannel {
        DYT(2, "dyt"),
        DYT_OLD(1, "dyt-old"),
        ;
        private final Integer channelCode;
        private final String channelTag;

        public static WechatMpChannel getChannelByCode(Integer code) {
            if (code == null) {
                return null;
            }
            for (WechatMpChannel channel : WechatMpChannel.values()) {
                if (Objects.equals(channel.getChannelCode(), code)) {
                    return channel;
                }
            }
            return null;
        }
    }
    @AllArgsConstructor
    @Getter
    enum WechatMaChannel {
        DYT(1, "dyt"),
        ;
        private final Integer channelCode;
        private final String channelTag;

        public static WechatMaChannel getChannelByCode(Integer code) {
            if (code == null) {
                return null;
            }
            for (WechatMaChannel channel : WechatMaChannel.values()) {
                if (Objects.equals(channel.getChannelCode(), code)) {
                    return channel;
                }
            }
            return null;
        }
    }
}
