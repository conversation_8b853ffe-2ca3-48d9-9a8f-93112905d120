create table t_hospital_list_rule
(
    id                 bigint auto_increment comment '全局唯一标识' primary key,
    name               varchar(32)                              null comment '规则名称',
    description        varchar(255)                             null comment '描述',
    type               tinyint(3)                               not null comment '规则类型：1-固定医院 2-分组距离 3-分组优先 4-广告推荐 5-分组混合距离',
    weight_order_by    tinyint(3)  default 1                    not null comment '权重排序方式：1-从低到高 2-从高到低',
    distance_order_by  tinyint(3)  default 1                    not null comment '距离排序方式：1-从近到远 2-从远到近',
    hospital_group_id  bigint                                   null comment '对应医院分组ID，当type值为1、2、3时必填',
    advertisement_id   varchar(255)                             null comment '广告推荐模式下对应广告id，当type值为4时必填',
    advertisement_type tinyint(3)                               null comment '广告推荐模式下对应广告类型，当type值为4时必填，1-推荐条件广告',
    group_scale        text                                     null comment '分组混合距离模式下对应混合规则，当type为5时必填，[{group_id:2, count: 10},{ group_id:3, count: 20},...,]',
    create_time        datetime(3) default CURRENT_TIMESTAMP(3) not null,
    update_time        datetime(3)                              null on update CURRENT_TIMESTAMP(3)
)
    comment '医院列表规则表';