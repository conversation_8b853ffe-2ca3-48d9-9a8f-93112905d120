package com.ynhdkc.tenant.service.backend;

import backend.common.util.JsonUtil;
import com.ynhdkc.tenant.entity.BulletinConfig;
import com.ynhdkc.tenant.model.*;

/**
 * <AUTHOR>
 * @description 针对表【t_bulletin_config(系统公告配置)】的数据库操作Service
 * @createDate 2024-03-04 17:34:29
 */
public interface BulletinConfigService {
    public static BulletinConfigVo toBulletinVo(BulletinConfig bulletinConfig) {
        if (bulletinConfig == null) {
            return null;
        }
        BulletinConfigVo vo = new BulletinConfigVo();
        vo.setId(bulletinConfig.getId());
        vo.setSystemStatus(bulletinConfig.getSystemStatus());
        vo.setBulletinContent(bulletinConfig.getBulletinContent());
        vo.setStatus(bulletinConfig.getStatus());
        if (bulletinConfig.getWhiteList() != null) {
            vo.setWhiteList(JsonUtil.deserializeList(bulletinConfig.getWhiteList(), Long.class));
        }
        return vo;
    }

    public static CustomBulletinConfigVo toCustomBulletinVo(BulletinConfig bulletinConfig) {
        if (bulletinConfig == null) {
            return null;
        }
        CustomBulletinConfigVo vo = new CustomBulletinConfigVo();
        vo.setId(bulletinConfig.getId());
        vo.setSystemStatus(bulletinConfig.getSystemStatus());
        vo.setBulletinContent(bulletinConfig.getBulletinContent());
        vo.setStatus(bulletinConfig.getStatus());

        return vo;
    }

    public static BulletinConfig toBulletinConfig(BulletinConfigDto dto) {
        if (dto == null) {
            return null;
        }
        BulletinConfig config = new BulletinConfig();
        config.setId(dto.getId());
        config.setSystemStatus(dto.getSystemStatus() == null ? 0 : dto.getSystemStatus());
        config.setBulletinContent(dto.getBulletinContent() == null ? "" : dto.getBulletinContent());
        config.setStatus(dto.getStatus() == null ? 1 : dto.getStatus());
        if (dto.getWhiteList() != null) {
            config.setWhiteList(JsonUtil.serializeObject(dto.getWhiteList()));
        }

        return config;

    }

    BulletinConfigVo get(Long id);

    BulletinConfigVo create(BulletinConfigDto request);

    BulletinConfigVo update(BulletinConfigDto request);

    BulletinConfigPageVo page(BulletinConfigReqDto request);

    CustomBulletinConfigVo queryCurrentBulletinConfig(Long userId);


}
