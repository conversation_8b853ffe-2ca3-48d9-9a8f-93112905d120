package backend.common.entity.dto.hisgateway.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class DoctorOutServiceItem {

    @JsonProperty("time_type")
    public Integer timeType;

    @JsonProperty("schedule_id")
    private String scheduleId;

    @JsonProperty("hospital_code")
    private String hospitalCode;

    @JsonProperty("department_code")
    private String departmentCode;

    @JsonProperty("doctor_code")
    private String doctorCode;

    @JsonProperty("visiting_date")
    private long visitingDate;
    
    private Map<String, Object> rawParameters = new HashMap<>();

}
