package backend.common.entity.dto.hisgateway.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonGetter;

public enum RealNameType {

	NOT_YET("尚未实名", 0), YET("已实名", 1);

	private final String value;

	private final Integer code;

	RealNameType(String value, Integer code) {
		this.value = value;
		this.code = code;
	}

	@JsonCreator
	public static CardType getFromCode(int code) {
		for (CardType t : CardType.values()) {
			if (t.getCode().equals(code)) {
				return t;
			}
		}
		throw new IllegalArgumentException("实名状态不存在");
	}

	public static CardType getFromValue(String value) {
		for (CardType t : CardType.values()) {
			if (t.getValue().equals(value)) {
				return t;
			}
		}
		throw new IllegalArgumentException("实名状态不存在");
	}

	public String getValue() {
		return value;
	}

	public Integer getCode() {
		return code;
	}

	@JsonGetter("code")
	public String getRequestCode() {
		return code.toString();
	}

}
