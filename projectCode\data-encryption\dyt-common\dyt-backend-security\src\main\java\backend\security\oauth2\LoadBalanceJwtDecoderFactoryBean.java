package backend.security.oauth2;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.boot.autoconfigure.security.oauth2.resource.OAuth2ResourceServerProperties;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.security.oauth2.core.DelegatingOAuth2TokenValidator;
import org.springframework.security.oauth2.core.OAuth2TokenValidator;
import org.springframework.security.oauth2.jose.jws.SignatureAlgorithm;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtTimestampValidator;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@SuppressWarnings("AlibabaClassMustHaveAuthor")
@Slf4j
@RequiredArgsConstructor
public class LoadBalanceJwtDecoderFactoryBean extends AbstractLBClientHttpRequestInterceptorProvider implements FactoryBean<JwtDecoder> {
    private static final String LB_ENABLE_KEY = "spring.security.oauth2.resourceserver.jwt.jwks-service-discovery";

    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    public JwtDecoder getObject() {
        final Environment env = getApplicationContext().getEnvironment();
        final Boolean enable = env.getProperty(LB_ENABLE_KEY, Boolean.class, false);
        OAuth2ResourceServerProperties properties = getApplicationContext().getBean(OAuth2ResourceServerProperties.class);
        final NimbusJwtDecoder decoder;
        /* 没启用服务发现，jwks则用yml里配置的属性（公钥地址和算法） */
        if (!enable) {
            decoder = NimbusJwtDecoder.withJwkSetUri(properties.getJwt().getJwkSetUri())
                    .jwsAlgorithm(SignatureAlgorithm.from(properties.getJwt().getJwsAlgorithm()))
                    .build();
        } else {
            /* 启动服务发现，则通过loadbalancer restTemplate 获取公钥 */
            ClientHttpRequestInterceptor finalLoadBalancerInterceptor = getLBClientHttpRequestInterceptor();
            RestTemplate restTemplate = new RestTemplate();
            restTemplate.setInterceptors(Collections.singletonList(finalLoadBalancerInterceptor));
            decoder = NimbusJwtDecoder.withJwkSetUri(properties.getJwt().getJwkSetUri())
                    .jwsAlgorithm(SignatureAlgorithm.from(properties.getJwt().getJwsAlgorithm()))
                    .restOperations(restTemplate)
                    .build();
        }
        List<OAuth2TokenValidator<Jwt>> chain = new ArrayList<>(2);
        chain.add(new JwtTimestampValidator());
        chain.add(new BackendJwtValidator(redisTemplate));
        DelegatingOAuth2TokenValidator<Jwt> validator = new DelegatingOAuth2TokenValidator<>(chain);
        decoder.setJwtValidator(validator);
        return decoder;
    }

    @Override
    public Class<?> getObjectType() {
        return JwtDecoder.class;
    }
}
