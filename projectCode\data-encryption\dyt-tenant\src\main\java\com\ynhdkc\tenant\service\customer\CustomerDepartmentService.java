package com.ynhdkc.tenant.service.customer;

import com.ynhdkc.tenant.dto.DoctorAdvanceAndForbiddenDay;
import com.ynhdkc.tenant.dto.ScheduleResponseDto;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.entity.DoctorGroupRelation;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import com.ynhdkc.tenant.model.*;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/7/21 9:43
 */
public interface CustomerDepartmentService {
    CustomerDepartmentDetailVo getDetail(Long departmentId);

    CustomerDepartmentDetailVo getDetailByCode(String hospitalAreaCode, String departmentCode);

    CustomerDoctorScheduleInfoVo queryGroupedDoctorList(Long departmentId, Integer timeType);

    boolean enrichCustomerAllScheduleDoctorDetailVo(List<ScheduleResponseDto.ScheduleInfo> scheduleInfos, String departmentName, List<DoctorGroupRelation> doctorGroupRelations, Integer displayDoctorUnderDepartment, CustomerAllScheduleDoctorDetailVo vo);

    List<DoctorAdvanceAndForbiddenDay> getDoctorAdvanceAndForbiddenDays(List<Doctor> doctors, Department department, AppointmentRuleSetting ruleSetting);

    CustomerDepartmentsTreeSearchListVo queryTree(Long hospitalAreaId, Integer category, Integer level);

    List<CustomerDepartmentVo> getRecentSuccess(Long userId, Long hospitalAreaId);

    CustomerDoctorScheduleInfoVo queryGroupedDoctorListByDepartmentId(Long departmentId);

    CustomerDepartmentPageVo queryDepartmentPage(CustomerQueryDepartmentPageReqDto request);

    CustomerDepartmentsTreeSearchListVo queryChildrenTree(Long departmentId);

    CustomerDoctorScheduleInfoVo queryGroupedDoctorListBy(String hospitalAreaCode, String departmentCode);

    List<CustomerAllScheduleDoctorDetailVo> querySpecialNeedsDoctorList(Long hospitalAreaId, Long departmentId, String doctorName);

    Optional<Department> queryDepartmentByCode(String hospitalAreaCode, String departmentCode);

    CustomerDepartmentsTreeSearchListVo getSubDepartments(Long departmentId);
}
