package com.ynhdkc.tenant.doctor.sort.impl;

import com.ynhdkc.tenant.doctor.sort.strategy.AbstractDoctorSortStrategy;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.tool.DoctorSortUtils;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class RealTimeSortStrategy extends AbstractDoctorSortStrategy {

    private static final Set<String> HOSPITAL_CODES = Collections.unmodifiableSet(new HashSet<>(Arrays.asList("871044", "871023")));


    public RealTimeSortStrategy() {
        super(HOSPITAL_CODES);
    }

    @Override
    public void sort(List<Doctor> doctors) {
        DoctorSortUtils.reSortDoctor(doctors);
    }
}
