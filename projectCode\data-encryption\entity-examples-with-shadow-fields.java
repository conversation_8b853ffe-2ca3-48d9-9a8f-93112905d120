// =====================================================
// 实体类示例 - 使用影子字段进行渐进式加密迁移
// 根据《数据安全法》《个人信息保护法》整改要求
// =====================================================

package com.ynhdkc.tenant.entity;

import backend.encryption.annotation.EncryptField;
import backend.encryption.converter.EncryptConverter;
import lombok.Data;

import javax.persistence.*;
import java.util.Date;

// =====================================================
// 1. t_tenant_user 表实体类
// =====================================================

@Data
@Entity
@Table(name = "t_tenant_user")
public class TenantUser {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 身份证号（核心数据）
     * 使用影子字段迁移策略
     */
    @EncryptField(
        description = "租户用户身份证号",
        shadowField = "id_card_no_encrypted",
        migrationStrategy = EncryptField.MigrationStrategy.SHADOW_PRIORITY,
        strategyKey = "tenant-user-id-card",
        algorithm = EncryptField.AlgorithmType.AES_GCM
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "id_card_no", length = 50)
    private String idCardNo;
    
    /**
     * 身份证号加密字段（影子字段）
     * 注意：这个字段不在业务代码中直接使用，由加密框架自动管理
     */
    @Column(name = "id_card_no_encrypted", length = 1000)
    private String idCardNoEncrypted;
    
    /**
     * 手机号码（重要数据）
     * 使用影子字段迁移策略
     */
    @EncryptField(
        description = "租户用户手机号码",
        shadowField = "phone_number_encrypted",
        migrationStrategy = EncryptField.MigrationStrategy.SHADOW_PRIORITY,
        strategyKey = "tenant-user-phone",
        algorithm = EncryptField.AlgorithmType.AES_GCM
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "phone_number", length = 20)
    private String phoneNumber;
    
    /**
     * 手机号码加密字段（影子字段）
     */
    @Column(name = "phone_number_encrypted", length = 1000)
    private String phoneNumberEncrypted;
    
    /**
     * 用户名（重要数据）
     * 使用影子字段迁移策略
     */
    @EncryptField(
        description = "租户用户名",
        shadowField = "name_encrypted",
        migrationStrategy = EncryptField.MigrationStrategy.SHADOW_PRIORITY,
        strategyKey = "tenant-user-name",
        algorithm = EncryptField.AlgorithmType.AES_GCM
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "name", length = 100)
    private String name;
    
    /**
     * 用户名加密字段（影子字段）
     */
    @Column(name = "name_encrypted", length = 1000)
    private String nameEncrypted;
    
    // 其他非敏感字段
    @Column(name = "tenant_id")
    private Long tenantId;
    
    @Column(name = "status")
    private Integer status;
    
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;
    
    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;
}

// =====================================================
// 2. t_recharge_record 表实体类
// =====================================================

@Data
@Entity
@Table(name = "t_recharge_record")
public class RechargeRecord {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 就诊人姓名（重要数据）
     * 使用影子字段迁移策略
     */
    @EncryptField(
        description = "充值记录就诊人姓名",
        shadowField = "patient_name_encrypted",
        migrationStrategy = EncryptField.MigrationStrategy.SHADOW_PRIORITY,
        strategyKey = "recharge-patient-name",
        algorithm = EncryptField.AlgorithmType.AES_GCM
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "patient_name", length = 100)
    private String patientName;
    
    /**
     * 就诊人姓名加密字段（影子字段）
     */
    @Column(name = "patient_name_encrypted", length = 1000)
    private String patientNameEncrypted;
    
    /**
     * 就诊卡号（核心数据）
     * 使用影子字段迁移策略
     */
    @EncryptField(
        description = "充值记录就诊卡号",
        shadowField = "jz_card_encrypted",
        migrationStrategy = EncryptField.MigrationStrategy.SHADOW_PRIORITY,
        strategyKey = "recharge-jz-card",
        algorithm = EncryptField.AlgorithmType.AES_GCM
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "jz_card", length = 50)
    private String jzCard;
    
    /**
     * 就诊卡号加密字段（影子字段）
     */
    @Column(name = "jz_card_encrypted", length = 1000)
    private String jzCardEncrypted;
    
    // 其他非敏感字段
    @Column(name = "tenant_id")
    private Long tenantId;
    
    @Column(name = "amount")
    private java.math.BigDecimal amount;
    
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;
}

// =====================================================
// 3. t_tenant 表实体类
// =====================================================

@Data
@Entity
@Table(name = "t_tenant")
public class Tenant {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 联系人手机号（重要数据）
     * 使用影子字段迁移策略
     */
    @EncryptField(
        description = "租户联系人手机号",
        shadowField = "contact_phone_number_encrypted",
        migrationStrategy = EncryptField.MigrationStrategy.SHADOW_PRIORITY,
        strategyKey = "tenant-contact-phone",
        algorithm = EncryptField.AlgorithmType.AES_GCM
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "contact_phone_number", length = 20)
    private String contactPhoneNumber;
    
    /**
     * 联系人手机号加密字段（影子字段）
     */
    @Column(name = "contact_phone_number_encrypted", length = 1000)
    private String contactPhoneNumberEncrypted;
    
    /**
     * 联系人邮箱（重要数据）
     * 使用影子字段迁移策略
     */
    @EncryptField(
        description = "租户联系人邮箱",
        shadowField = "contact_email_encrypted",
        migrationStrategy = EncryptField.MigrationStrategy.SHADOW_PRIORITY,
        strategyKey = "tenant-contact-email",
        algorithm = EncryptField.AlgorithmType.AES_GCM
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "contact_email", length = 100)
    private String contactEmail;
    
    /**
     * 联系人邮箱加密字段（影子字段）
     */
    @Column(name = "contact_email_encrypted", length = 1000)
    private String contactEmailEncrypted;
    
    // 其他非敏感字段
    @Column(name = "tenant_name")
    private String tenantName;
    
    @Column(name = "status")
    private Integer status;
    
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;
    
    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;
}

// =====================================================
// 使用示例和注意事项
// =====================================================

/*
使用说明：

1. 业务代码使用方式：
   - 正常使用原字段（如 idCardNo、phoneNumber 等）
   - 不需要直接操作影子字段（如 idCardNoEncrypted）
   - 加密框架会自动处理影子字段的读写

2. 查询示例：
   ```java
   // 正常查询，框架会自动处理加密解密
   TenantUser user = tenantUserRepository.findByPhoneNumber("13800138000");
   
   // 批量查询也支持
   List<TenantUser> users = tenantUserRepository.findByIdCardNoIn(idCardList);
   ```

3. 保存示例：
   ```java
   TenantUser user = new TenantUser();
   user.setIdCardNo("110101199001011234");  // 明文设置
   user.setPhoneNumber("13800138000");
   user.setName("张三");
   
   // 保存时框架会自动加密到影子字段
   tenantUserRepository.save(user);
   ```

4. 迁移策略切换：
   - 初期：PLAINTEXT_PRIORITY（明文优先，开始迁移）
   - 中期：SHADOW_PRIORITY（影子字段优先，逐步切换）
   - 完成：SHADOW_ONLY（仅使用影子字段）

5. 注意事项：
   - 影子字段长度设置为1000，足够存储加密数据
   - 所有影子字段允许NULL，不影响现有业务
   - 建议在业务低峰期执行DDL操作
   - 定期监控加密性能和错误日志

6. 数据分类级别：
   - 核心数据：id_card_no, jz_card（最高安全级别）
   - 重要数据：phone_number, name, patient_name, contact_phone_number, contact_email
*/
