package backend.security.oauth2;

import org.springframework.context.annotation.Bean;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-03-17 9:40
 */
@EnableWebSecurity
public class SecurityConfiguration extends WebSecurityConfigurerAdapter {
    @Override
    public void configure(WebSecurity web) {
        /* 配置忽略的路径，以及所有请求的前缀路径 */
        BackendOAuth2ResourceServerUtils.initializeOAuth2ResourceServer(web, getApplicationContext());
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        /* 配置 Jwt Converter、Decoder */
        BackendOAuth2ResourceServerUtils.initializeOAuth2ResourceServer(http);
    }

//    @Bean
//    LoadBalanceOAuth2AuthorizedClientManagerFactoryBean loadBalanceOAuth2AuthorizedClientManager() {
//        return new LoadBalanceOAuth2AuthorizedClientManagerFactoryBean();
//    }

    @Bean
    BackendOAuth2ResourceServerProperties backendOAuth2ResourceServerProperties() {
        return new BackendOAuth2ResourceServerProperties();
    }
}