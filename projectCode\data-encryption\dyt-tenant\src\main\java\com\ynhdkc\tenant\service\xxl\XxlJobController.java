package com.ynhdkc.tenant.service.xxl;

import com.ynhdkc.tenant.handler.XxlJobApi;
import com.ynhdkc.tenant.service.backend.IHisRequestService;
import com.ynhdkc.tenant.service.backend.RecommendStatisticsService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2023/7/11 17:49:06
 */
@Api(tags = "XxlJob")
@RestController
@RequiredArgsConstructor
public class XxlJobController implements XxlJobApi {
    private final RecommendStatisticsService recommendStatisticsService;
    private final IHisRequestService hisRequestService;

    @Override
    public ResponseEntity<Void> requestDepartmentsDoctorsFromHis() {
        hisRequestService.syncDoctorSchedule();
        return ResponseEntity.ok().build();
    }

    @Override
    public ResponseEntity<Void> requestDepartmentsFromHis() {
        hisRequestService.requestDepartmentsFromHis();
        return ResponseEntity.ok().build();
    }
}
