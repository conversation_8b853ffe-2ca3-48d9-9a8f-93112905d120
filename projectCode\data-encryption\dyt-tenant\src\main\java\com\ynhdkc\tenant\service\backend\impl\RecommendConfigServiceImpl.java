package com.ynhdkc.tenant.service.backend.impl;

import backend.common.dao.MySqlCommonMapper;
import backend.common.exception.BizException;
import backend.common.response.BaseOperationResponse;
import backend.common.util.JsonUtil;
import backend.common.util.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ynhdkc.oldsystem.integration.api.request.GetDytkfDoctorGroupListReqDto;
import com.ynhdkc.oldsystem.integration.api.response.DoctorGroupRespItemDto;
import com.ynhdkc.oldsystem.integration.api.response.QueryDoctorGroupRespDto;
import com.ynhdkc.oldsystem.integration.client.DytkfDoctorGroupClient;
import com.ynhdkc.tenant.dao.DepartmentQuery;
import com.ynhdkc.tenant.dao.DoctorQuery;
import com.ynhdkc.tenant.dao.HospitalAreaQuery;
import com.ynhdkc.tenant.dao.HospitalQuery;
import com.ynhdkc.tenant.dao.impl.RecommendDoctorConfigRepository;
import com.ynhdkc.tenant.dao.mapper.DictLabelMapper;
import com.ynhdkc.tenant.dao.mapper.RecommendConfigMapper;
import com.ynhdkc.tenant.entity.*;
import com.ynhdkc.tenant.entity.constant.ChannelSourceType;
import com.ynhdkc.tenant.entity.constant.RecommendConfigConstant;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.ChannelSourceService;
import com.ynhdkc.tenant.service.backend.DictLabelService;
import com.ynhdkc.tenant.service.backend.RecommendConfigService;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.ynhdkc.tenant.api.backend.SourceRecommendConfigController.dictType;

@Service
@Slf4j
@RequiredArgsConstructor
public class RecommendConfigServiceImpl implements RecommendConfigService {

    private final static ObjectMapper objectMapper = new ObjectMapper();
    private final RecommendConfigMapper recommendConfigMapper;
    private final PageVoConvert pageVoConvert;
    private final DictLabelMapper dictLabelMapper;
    private final HospitalQuery hospitalQuery;
    private final HospitalAreaQuery hospitalAreaQuery;
    private final DepartmentQuery departmentQuery;
    private final DoctorQuery doctorQuery;
    private final DytkfDoctorGroupClient dytkfDoctorGroupClient;
    private final ChannelSourceService channelSourceService;
    private final DictLabelService dictLabelService;
    private final RecommendDoctorConfigRepository recommendDoctorConfigRepository;

    @Override
    @Transactional
    public RecommendConfigVo createRecommendConfig(CreateRecommendConfigReqDto request) {
        verifyData(null, request);
        if (ObjectUtils.isEmpty(request.getChannels())) {
            log.error("渠道不能为空，request={}", JsonUtil.serializeObject(request));
            throw new BizException(HttpStatus.BAD_REQUEST, "渠道不能为空");
        }

        RecommendConfig entity = toEntity(request);
        int insertEffectCount = recommendConfigMapper.insert(entity);
        if (insertEffectCount != 1) {
            log.error("创建推荐配置失败，request={}", JsonUtil.serializeObject(request));
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "创建推荐配置失败");
        }
        channelSourceService.submitChannelSource(Collections.singletonList(entity.getId()),
                ChannelSourceType.RECOMMEND_CONFIG,
                request.getChannels());
        RecommendConfigVo vo = toVo(entity);
        vo.setChannels(request.getChannels());
        return vo;
    }

    private <T extends CreateRecommendConfigReqDto> void verifyData(Long id, T request) {
        if (ObjectUtils.isEmpty(request.getDataId())) {
            log.error("数据id不能为空，request={}", JsonUtil.serializeObject(request));
            throw new BizException(HttpStatus.BAD_REQUEST, "数据id不能为空");
        }
        RecommendConfig recommendConfig = recommendConfigMapper.selectOneByExample2(RecommendConfig.class,
                sql -> {
                    sql.andEqualTo(RecommendConfig::getBizType, request.getBizType())
                            .andEqualTo(RecommendConfig::getDataType, request.getDataType())
                            .andEqualTo(RecommendConfig::getDataId, request.getDataId());
                    if (!ObjectUtils.isEmpty(request.getDataTag())) {
                        sql.andEqualTo(RecommendConfig::getDataTag, request.getDataTag());
                    }
                }
        );
        if (recommendConfig != null && !recommendConfig.getId().equals(id)) {
            log.error("已存在相同的推荐配置，request={}", JsonUtil.serializeObject(request));
            throw new BizException(HttpStatus.BAD_REQUEST, "已存在相同的推荐配置");
        }

        if (!CollectionUtils.isEmpty(request.getKeyWordList())) {
            Set<String> keyWordSet = new HashSet<>(request.getKeyWordList());
            if (keyWordSet.size() != request.getKeyWordList().size()) {
                log.error("关键词重复，request={}", JsonUtil.serializeObject(request));
                throw new BizException(HttpStatus.BAD_REQUEST, "标签不允许重复");
            }
        }
    }

    @Override
    @Transactional
    public RecommendConfigVo updateRecommendConfig(Long id, UpdateRecommendConfigReqDto request) {
        verifyData(id, request);

        RecommendConfig entity = recommendConfigMapper.selectByPrimaryKey(id);
        if (entity == null) {
            log.error("推荐配置不存在，id={}", id);
            throw new BizException(HttpStatus.NOT_FOUND, "推荐配置不存在");
        }
        updateIfNotNull(entity, request);
        int updateEffectCount = recommendConfigMapper.updateByPrimaryKey(entity);
        if (updateEffectCount != 1) {
            log.error("更新推荐配置失败，id={}, request={}", id, JsonUtil.serializeObject(request));
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "更新推荐配置失败");
        }
        channelSourceService.submitChannelSource(Collections.singletonList(entity.getId()), ChannelSourceType.RECOMMEND_CONFIG, request.getChannels());
        RecommendConfigVo vo = toVo(entity);
        Map<Long, List<Integer>> longListMap = channelSourceService.selectChannelMap(ChannelSourceType.RECOMMEND_CONFIG, Collections.singletonList(id));
        vo.setChannels(longListMap.get(id));
        if (!CollectionUtils.isEmpty(request.getChangeKeyWordsList())) {
            request.getChangeKeyWordsList().forEach(changeKeyWords -> {
                List<RecommendDoctorConfig> recommendDoctorConfigs = recommendDoctorConfigRepository.queryListByRecommendIdAndDataTag(entity.getId(), changeKeyWords.getOldKeyWord());
                if (!CollectionUtils.isEmpty(recommendDoctorConfigs)) {
                    recommendDoctorConfigs.forEach(recommendDoctorConfig -> {
                        recommendDoctorConfig.setDataTag(changeKeyWords.getNewKeyWord());
                        recommendDoctorConfigRepository.update(recommendDoctorConfig);
                    });
                }
            });
            List<RecommendDoctorConfig> recommendDoctorConfigs = recommendDoctorConfigRepository.queryListByRecommendIdAndDataTag(entity.getId(), entity.getDataTag());
        }
        return vo;
    }

    private void updateIfNotNull(RecommendConfig entity, UpdateRecommendConfigReqDto reqDto) {
        if (reqDto.getBizType() != null) {
            entity.setBizType(reqDto.getBizType());
        }
        if (reqDto.getDataType() != null) {
            entity.setDataType(reqDto.getDataType());
        }
        if (reqDto.getDataTag() != null) {
            entity.setDataTag(reqDto.getDataTag());
        }
        if (reqDto.getDataId() != null) {
            entity.setDataId(reqDto.getDataId());
        }
        if (reqDto.getRedirectUrl() != null) {
            entity.setRedirectUrl(reqDto.getRedirectUrl());
        }
        if (reqDto.getSort() != null) {
            entity.setSort(reqDto.getSort());
        }
        if (reqDto.getEnabled() != null) {
            entity.setEnabled(reqDto.getEnabled());
        }
        if (reqDto.getDisplayTags() != null) {
            entity.setDisplayTags(StringUtils.join(reqDto.getDisplayTags(), ","));
        }
        if (reqDto.getImgUrl() != null) {
            entity.setImgUrl(reqDto.getImgUrl());
        }
        if (reqDto.getDisplayName() != null) {
            entity.setDisplayName(reqDto.getDisplayName());
        }
        if (reqDto.getKeyWords() != null) {
            entity.setKeyWords(reqDto.getKeyWords());
        }
        if (reqDto.getRecommendScore() != null) {
            entity.setRecommendScore(reqDto.getRecommendScore());
        }
        if (reqDto.getRecommendReason() != null) {
            entity.setRecommendReason(reqDto.getRecommendReason());
        }
        if (reqDto.getSpecially() != null) {
            entity.setSpecially(reqDto.getSpecially());
        }
        if (reqDto.getKeyWordList() != null) {
            entity.setKeyWords(StringUtils.join(reqDto.getKeyWordList(), "|"));
        }
    }


    @Override
    public RecommendConfigVo getRecommendConfig(Long id) {
        RecommendConfig recommendConfig = recommendConfigMapper.selectByPrimaryKey(id);
        if (recommendConfig == null) {
            log.error("推荐配置不存在，id={}", id);
            throw new BizException(HttpStatus.NOT_FOUND, "推荐配置不存在");
        }
        RecommendConfigVo vo = toVo(recommendConfig);
        return buildVo(Collections.singletonList(vo)).get(0);
    }

    @Override
    public BaseOperationResponse deleteRecommendConfig(Long id) {
        RecommendConfig recommendConfig = recommendConfigMapper.selectByPrimaryKey(id);
        if (recommendConfig == null) {
            log.error("推荐配置不存在，id={}", id);
            throw new BizException(HttpStatus.NOT_FOUND, "推荐配置不存在");
        }
        int deleteEffectCount = recommendConfigMapper.deleteByPrimaryKey(id);
        if (deleteEffectCount != 1) {
            log.error("删除推荐配置失败，id={}", id);
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "删除推荐配置失败");
        }
        channelSourceService.deleteChannelSource(Collections.singletonList(id), ChannelSourceType.RECOMMEND_CONFIG);
        return new BaseOperationResponse();
    }

    @Override
    public RecommendConfigPageVo getRecommendConfigPage(GetRecommendConfigPageReqDto request) {
        List<Long> sourceIds;
        if (!ObjectUtils.isEmpty(request.getChannels())) {
            sourceIds = channelSourceService.selectSourceIdByChannel(ChannelSourceType.RECOMMEND_CONFIG, request.getChannels());
            if (CollectionUtils.isEmpty(sourceIds)) {
                return new RecommendConfigPageVo();
            }
        } else {
            sourceIds = null;
        }
        Consumer<MySqlCommonMapper.Helper<RecommendConfig>> condition = helper -> {
            helper.defGroup(sql -> {
                if (sourceIds != null) {
                    sql.andIn(RecommendConfig::getId, sourceIds);
                }
                if (!ObjectUtils.isEmpty(request.getBizType())) {
                    sql.andEqualTo(RecommendConfig::getBizType, request.getBizType());
                }
                if (!ObjectUtils.isEmpty(request.getDataType())) {
                    sql.andEqualTo(RecommendConfig::getDataType, request.getDataType());
                }
                if (!ObjectUtils.isEmpty(request.getDataTag())) {
                    sql.andEqualTo(RecommendConfig::getDataTag, request.getDataTag());
                }
                if (!ObjectUtils.isEmpty(request.getDataId())) {
                    sql.andEqualTo(RecommendConfig::getDataId, request.getDataId());
                }
                if (!ObjectUtils.isEmpty(request.getEnabled())) {
                    sql.andEqualTo(RecommendConfig::getEnabled, request.getEnabled());
                }
                sql.andNotEqualTo(RecommendConfig::getBizType, dictType);
            });
            helper.builder(order -> order.orderByDesc(RecommendConfig::getSort));
        };

        Page<RecommendConfig> page = PageHelper.startPage(request.getCurrent(), request.getSize());
        page.doSelectPage(() -> recommendConfigMapper.selectByExample(RecommendConfig.class, condition));

        RecommendConfigPageVo pageVo = pageVoConvert.toPageVo(page, RecommendConfigPageVo.class, this::toVo);
        pageVo.setList(buildVo(pageVo.getList()));
        return pageVo;
    }

    @Override
    public List<String> getRecommendConfigKeywords(Long id) {
        RecommendConfig recommendConfig = recommendConfigMapper.selectByPrimaryKey(id);
        if (recommendConfig != null && recommendConfig.getKeyWords() != null) {
            String[] split = recommendConfig.getKeyWords().split("\\|");
            return Arrays.asList(split);
        }
        return Collections.emptyList();
    }

    @Override
    public BaseOperationResponse updateRecommendConfigChannel(UpdateRecommendConfigChannelReqDto request) {
        channelSourceService.submitChannelSource(request.getRecommendIds(), ChannelSourceType.RECOMMEND_CONFIG, request.getChannels());
        return null;
    }

    private List<RecommendConfigVo> buildVo(List<RecommendConfigVo> vos) {
        List<DictLabel> dictLabels = dictLabelMapper.selectByDictTypesAndDictLabels(RecommendConfigConstant.DictType.typeList(), null);
        Map<String, DictLabel> bizTypeMap = dictLabels.stream()
                .filter(dictLabel -> RecommendConfigConstant.DictType.BIZ_TYPE.getType().equals(dictLabel.getDictType()))
                .collect(Collectors.toMap(DictLabel::getDictValue, d -> d));
        Map<String, DictLabel> dataTypeMap = dictLabels.stream()
                .filter(dictLabel -> RecommendConfigConstant.DictType.DATA_TYPE.getType().equals(dictLabel.getDictType()))
                .collect(Collectors.toMap(DictLabel::getDictValue, d -> d));

        Set<Long> hospitalIds = vos.stream()
                .filter(vo -> RecommendConfigConstant.DataType.HOSPITAL.getType().equals(vo.getDataType()))
                .map(RecommendConfigVo::getDataId)
                .map(Long::parseLong)
                .collect(Collectors.toSet());

        List<String> hospitalAreaIds = vos.stream()
                .filter(vo -> RecommendConfigConstant.DataType.HOSPITAL_AREA.getType().equals(vo.getDataType()))
                .map(RecommendConfigVo::getDataId)
                .collect(Collectors.toList());
        List<Hospital> hospitalAreas = hospitalAreaQuery.queryBy(hospitalAreaIds);
        Map<Long, Hospital> hospitalAreaMap = CollectionUtils.isEmpty(hospitalAreas) ? new HashMap<>() : hospitalAreas.stream().collect(Collectors.toMap(Hospital::getId, h -> h));

        if (!CollectionUtils.isEmpty(hospitalAreas)) {
            hospitalAreas.forEach(hospitalArea -> hospitalIds.add(hospitalArea.getParentId()));
        }

        List<Long> departmentIds = vos.stream()
                .filter(vo -> RecommendConfigConstant.DataType.DEPARTMENT.getType().equals(vo.getDataType()))
                .map(RecommendConfigVo::getDataId)
                .map(Long::parseLong)
                .collect(Collectors.toList());
        List<Department> departments = departmentQuery.queryBy(departmentIds);
        if (!CollectionUtils.isEmpty(departments)) {
            departments.forEach(department -> hospitalIds.add(department.getHospitalId()));
        }
        Map<Long, Department> departmentMap = CollectionUtils.isEmpty(departments) ? new HashMap<>() : departments.stream().collect(Collectors.toMap(Department::getId, d -> d));

        List<Long> doctorIds = vos.stream()
                .filter(vo -> RecommendConfigConstant.DataType.DOCTOR.getType().equals(vo.getDataType()))
                .map(RecommendConfigVo::getDataId)
                .map(Long::parseLong)
                .collect(Collectors.toList());
        List<Doctor> doctors = doctorQuery.queryBy(doctorIds);
        if (!CollectionUtils.isEmpty(doctors)) {
            doctors.forEach(doctor -> hospitalIds.add(doctor.getHospitalId()));
        }
        Map<Long, Doctor> doctorMap = CollectionUtils.isEmpty(doctors) ? new HashMap<>() : doctors.stream().collect(Collectors.toMap(Doctor::getId, d -> d));

        List<Hospital> hospitals = hospitalQuery.queryBy(hospitalIds);
        Map<Long, Hospital> hospitalMap = CollectionUtils.isEmpty(hospitals) ? new HashMap<>() : hospitals.stream().collect(Collectors.toMap(Hospital::getId, h -> h));

        List<Long> onlineDoctorIds = vos.stream().filter(vo -> RecommendConfigConstant.DataType.ONLINE_DOCTOR.getType().equals(vo.getDataType()))
                .map(RecommendConfigVo::getDataId)
                .map(Long::parseLong)
                .collect(Collectors.toList());
        List<DoctorGroupRespItemDto> onlineDoctors = new ArrayList<>();
        if (!CollectionUtils.isEmpty(onlineDoctorIds)) {
            GetDytkfDoctorGroupListReqDto condition = new GetDytkfDoctorGroupListReqDto();
            condition.setIds(onlineDoctorIds);
            condition.setCurrent(1);
            condition.setSize(onlineDoctorIds.size());
            QueryDoctorGroupRespDto doctorGroupPage = dytkfDoctorGroupClient.getDoctorGroupList(condition);
            onlineDoctors = doctorGroupPage.getRecords();
        }
        Map<Long, DoctorGroupRespItemDto> doctorGroupMap = onlineDoctors.stream().collect(Collectors.toMap(DoctorGroupRespItemDto::getId, d -> d));

        List<Long> recommendIds = vos.stream().map(RecommendConfigVo::getId).collect(Collectors.toList());
        Map<Long, List<Integer>> channelMap = channelSourceService.selectChannelMap(ChannelSourceType.RECOMMEND_CONFIG, recommendIds);

        vos.forEach(vo -> {
            List<Integer> channels = channelMap.get(vo.getId());
            vo.setChannels(channels);
            DictLabel bizType = bizTypeMap.get(vo.getBizType());
            DictLabel dataType = dataTypeMap.get(vo.getDataType());
            vo.setBizTypeDesc(bizType == null ? null : bizType.getDictLabel());
            vo.setDataTypeDesc(dataType == null ? null : dataType.getDictLabel());
            handleVoWithDataType(vo, hospitalMap, hospitalAreaMap, departmentMap, doctorMap, doctorGroupMap);
        });
        return vos;
    }

    private void handleVoWithDataType(RecommendConfigVo vo,
                                      Map<Long, Hospital> hospitalMap,
                                      Map<Long, Hospital> hospitalAreaMap,
                                      Map<Long, Department> departmentMap,
                                      Map<Long, Doctor> doctorMap,
                                      Map<Long, DoctorGroupRespItemDto> doctorGroupMap) {
        RecommendConfigConstant.DataType dataType = RecommendConfigConstant.DataType.getByType(vo.getDataType());
        if (dataType == null) {
            return;
        }
        String name = null;
        Map<String, String> reservedMap = new HashMap<>();
        Hospital hospital = null;
        switch (dataType) {
            case HOSPITAL:
                hospital = hospitalMap.get(Long.parseLong(vo.getDataId()));
                if (hospital != null) {
                    name = hospital.getName();
                }
                break;
            case HOSPITAL_AREA:
                Hospital hospitalArea = hospitalAreaMap.get(Long.parseLong(vo.getDataId()));
                if (hospitalArea != null) {
                    name = hospitalArea.getName();
                    reservedMap.put(RecommendConfigConstant.ReservedField.HOSPITAL_ID.getType(), hospitalArea.getParentId().toString());
                    hospital = hospitalMap.get(hospitalArea.getParentId());
                }
                break;
            case DEPARTMENT:
                Department department = departmentMap.get(Long.parseLong(vo.getDataId()));
                if (department != null) {
                    name = department.getName();
                    reservedMap.put(RecommendConfigConstant.ReservedField.HOSPITAL_ID.getType(), department.getHospitalId().toString());
                    reservedMap.put(RecommendConfigConstant.ReservedField.HOSPITAL_AREA_ID.getType(), department.getHospitalAreaId().toString());
                    hospital = hospitalMap.get(department.getHospitalId());
                }
                break;
            case DOCTOR:
                Doctor doctor = doctorMap.get(Long.parseLong(vo.getDataId()));
                if (doctor != null) {
                    name = doctor.getName();
                    reservedMap.put(RecommendConfigConstant.ReservedField.HOSPITAL_ID.getType(), doctor.getHospitalId().toString());
                    reservedMap.put(RecommendConfigConstant.ReservedField.HOSPITAL_AREA_ID.getType(), doctor.getHospitalAreaId().toString());
                    reservedMap.put(RecommendConfigConstant.ReservedField.DEPARTMENT_ID.getType(), doctor.getDepartmentId().toString());
                    hospital = hospitalMap.get(doctor.getHospitalId());
                }
                break;
            case ONLINE_DOCTOR:
                DoctorGroupRespItemDto dto = doctorGroupMap.get(Long.valueOf(vo.getDataId()));
                if (dto != null) {
                    String docName = ObjectUtils.isEmpty(dto.getName()) ? "" : dto.getName();
                    String hospitalName = ObjectUtils.isEmpty(dto.getHospitalName()) ? "" : dto.getHospitalName();
                    String departmentName = ObjectUtils.isEmpty(dto.getDepartName()) ? "" : dto.getDepartName();
                    name = docName + "-" + hospitalName + "-" + departmentName;
                }
            default:
                break;
        }
        vo.setDataName(name);
        if (hospital != null) {
            reservedMap.put(RecommendConfigConstant.ReservedField.HOSPITAL_NAME.getType(), hospital.getName());
        }
        vo.setReservedJson(JsonUtil.serializeObject(reservedMap));
    }

    @Override
    public int selectCount(String dictLabel) {
        return recommendConfigMapper.selectCountByExample2(RecommendConfig.class, recommendConfigWeekendSqls -> {
            recommendConfigWeekendSqls.andEqualTo(RecommendConfig::getBizType, dictType);
            recommendConfigWeekendSqls.andEqualTo(RecommendConfig::getImgUrl, dictLabel);
        });
    }

    @Override
    public List<RecommendConfig> selectDoctorRecommondList(String dictLabel) {
        return recommendConfigMapper.selectDoctorRecommendList(dictType, dictLabel, null);
    }

    @Override
    @Transactional
    public List<RecommendConfigDoctorDto> createRecommendDoctor(UpdateRecommendConfigDoctorDto request, String dictType) {
        Assert.notEmpty(request, "list is empty");
        List<RecommendConfigDoctorDto> res = new ArrayList<>();
        request.forEach(doctor -> {
            List<RecommendConfig> configs = recommendConfigMapper.selectDoctorRecommendList(dictType, doctor.getDictLabel(), doctor.getDoctorId().toString());
            if (!configs.isEmpty()) {
                res.add(doctor);
            }
        });
        if (!res.isEmpty()) {
            List<Long> collect = res.stream().map(RecommendConfigDoctorDto::getDoctorId).collect(Collectors.toList());
            throw new RuntimeException(collect.toString());
        }
        List<RecommendConfig> config = doctor2Entity(request, dictType);
        recommendConfigMapper.insertList(config);
        dictLabelService.updateSourceRecommend(request.get(0).getDictLabelId());
        return Collections.emptyList();
    }

    @Override
    @Transactional
    public void updateDoctorRecommend(UpdateRecommendConfigDoctorDto list) {
        Assert.notEmpty(list, "list is empty");
        Map<Boolean, List<RecommendConfigDoctorDto>> partitionedMap = list.stream()
                .collect(Collectors.partitioningBy(myObject -> myObject.getId() != null));
        List<RecommendConfigDoctorDto> updateList = partitionedMap.get(true);
        List<RecommendConfigDoctorDto> insertList = partitionedMap.get(false);
        if (!updateList.isEmpty()) {
            for (RecommendConfigDoctorDto doctor : updateList) {
                RecommendConfig config = recommendConfigMapper.selectOneByExample2(RecommendConfig.class, sql -> {
                    sql.andEqualTo(RecommendConfig::getId, doctor.getId());
                });
                if (Objects.nonNull(config)) {
                    if (doctor.getStatus() != null) {
                        config.setEnabled(doctor.getStatus());
                    }
                    if (doctor.getSort() != null) {
                        config.setSort(doctor.getSort());
                    }
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(doctor.getDataTag())) {
                        config.setDataTag(doctor.getDataTag());
                        List<RecommendConfig> configs = recommendConfigMapper.selectByExample2(RecommendConfig.class, sql -> {
                            sql.andEqualTo(RecommendConfig::getRedirectUrl, config.getRedirectUrl());
                        });
                        // 将未被设置的记录的redirectUrl设置为""
                        for (RecommendConfig recommendConfig : configs) {
                            if (!recommendConfig.getId().equals(config.getId())) {
                                recommendConfig.setDataTag("");
                                recommendConfigMapper.updateByPrimaryKey(recommendConfig);
                            }
                        }
                    }
                }
                recommendConfigMapper.updateByPrimaryKey(config);
            }
        }
        if (!insertList.isEmpty()) {
            List<RecommendConfig> configs = doctor2Entity(insertList, dictType);
            recommendConfigMapper.insertList(configs);
        }
        dictLabelService.updateSourceRecommend(list.get(0).getDictLabelId());
    }

    @Override
    @Transactional
    public void deleteByIds(List<Long> ids, Long dictLabelId) {
        recommendConfigMapper.deleteByIds(String.join(",", ids.stream().map(Object::toString).toArray(String[]::new)));
        dictLabelService.updateSourceRecommend(dictLabelId);
    }

    @Override
    public RecommendConfig selectRecommendGroup(Long doctorId, boolean flag) {
        return recommendConfigMapper.selectOneByExample2(RecommendConfig.class, sql -> {
            sql.andEqualTo(RecommendConfig::getBizType, dictType);
            sql.andEqualTo(RecommendConfig::getRedirectUrl, doctorId);
            if (flag) {
                sql.andNotEqualTo(RecommendConfig::getDataTag, "");
                sql.andIsNotNull(RecommendConfig::getDataTag);
            }

        });
    }

    @Override
    public List<MultipleRecommendConfigDoctorDto> getMultipleDoctorList() {
        List<String> doctorIds = recommendConfigMapper.selectDoctorCount();
        if (CollectionUtils.isEmpty(doctorIds)) {
            return Collections.emptyList();
        }
        List<MultipleRecommendConfigDoctorDto> res = new ArrayList<>();
        for (String id : doctorIds) {
            List<RecommendConfig> configs = recommendConfigMapper.selectByExample2(RecommendConfig.class, sql -> {
                sql.andEqualTo(RecommendConfig::getRedirectUrl, id);
                sql.andEqualTo(RecommendConfig::getBizType, dictType);
            });
            if (CollectionUtils.isEmpty(configs)) {
                continue;
            }
            MultipleRecommendConfigDoctorDto dto = new MultipleRecommendConfigDoctorDto();
            List<RecommendConfigDoctorDto> list = new ArrayList<>();
            for (RecommendConfig config : configs) {
                objectMapper.clearProblemHandlers();
                RecommendConfigDoctorDto doctor = null;
                try {
                    doctor = objectMapper.readValue(config.getDisplayName(), RecommendConfigDoctorDto.class);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
                if (dto.getDoctorName() == null) {
                    BeanUtils.copyProperties(doctor, dto);
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(config.getDataTag())) {
                    dto.setDataTag(config.getDataTag().split("-")[0]);
                    dto.setDictLabelId(Long.parseLong(config.getDataTag().split("-")[1]));
                }
                doctor.setId(config.getId());
                list.add(doctor);
            }
            dto.setList(list);
            res.add(dto);
        }
        return res;
    }


    private int convertStatus(Integer status) {
        // 当 status 为 null 时，默认返回 1，表示初始状态
        if (status == null) {
            return 1;
        }
        return status == 0 ? 1 : (status == 1 ? 0 : 1);
    }


    private String getRandomId() {
        StringBuilder sb = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 6; i++) {
            sb.append(random.nextInt(10));
        }
        return sb.toString();
    }

    /**
     * 医生推荐 bizType-固定字典source-recommend  dataType-分组名称  dataTag-推荐分组名称 dataId-随机数 imgUrl-分组id displayName-医生信息 redirectUrl-医生id
     *
     * @param request
     * @param dictType
     * @return
     */
    private List<RecommendConfig> doctor2Entity(List<RecommendConfigDoctorDto> request, String dictType) {
        ObjectMapper objectMapper = new ObjectMapper();
        List<RecommendConfig> configs = new ArrayList<>();
        for (RecommendConfigDoctorDto doctor : request) {
            RecommendConfig config = new RecommendConfig();
            objectMapper.clearProblemHandlers();
            String doctorString = null;
            try {
                doctorString = objectMapper.writeValueAsString(doctor);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            config.setDisplayName(doctorString);
            config.setBizType(dictType);
            config.setSort(doctor.getSort());
            config.setDataType(doctor.getDictLabel());
            config.setDataTag("");
            config.setEnabled(convertStatus(doctor.getStatus()));
            config.setDataId(getRandomId());
            config.setImgUrl(doctor.getDictLabelId().toString());
            config.setRedirectUrl(doctor.getDoctorId().toString());
            config.setSort(doctor.getSort());
            configs.add(config);
        }
        return configs;
    }
}
