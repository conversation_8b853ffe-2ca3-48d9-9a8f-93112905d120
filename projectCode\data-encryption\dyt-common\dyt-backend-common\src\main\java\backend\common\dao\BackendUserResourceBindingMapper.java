package backend.common.dao;

import backend.common.domain.BackendUserResourceBinding;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * User must override newUserResourceBinding and userResourceBindingClass methods.
 *
 * @param <T>
 */
public interface BackendUserResourceBindingMapper<T extends BackendUserResourceBinding> extends MySqlCommonMapper<T> {

    default T newUserResourceBinding() {
        throw new UnsupportedOperationException("Please override this method");
    }

    default Class<T> userResourceBindingClass() {
        throw new UnsupportedOperationException("Please override this method");
    }

    default List<T> selectByTenantIdAndUserIdAndKind(Long tenantId, Long userId, Integer kind) {
        return this.selectByExample2(userResourceBindingClass(), sql -> {
            sql.andEqualTo(T::getTenantId, tenantId)
                    .andEqualTo(T::getUserId, userId)
                    .andEqualTo(T::getKind, kind);
        });
    }

    default T selectByTenantIdAndUserIdAndKindAndResourceId(Long tenantId, Long userId, Integer kind, Long resourceId) {
        return this.selectOneByExample2(userResourceBindingClass(), sql -> {
            sql.andEqualTo(T::getTenantId, tenantId)
                    .andEqualTo(T::getUserId, userId)
                    .andEqualTo(T::getKind, kind)
                    .andEqualTo(T::getResourceId, resourceId);
        });
    }

    default boolean isResourceAuthorized(Long tenantId, Long userId, Integer kind, Set<Long> needAuthorized) {
        if (needAuthorized.isEmpty()) {
            return true;
        }
        final int count = this.selectCountByExample2(userResourceBindingClass(), sql -> {
            sql.andEqualTo(T::getTenantId, tenantId)
                    .andEqualTo(T::getUserId, userId)
                    .andEqualTo(T::getKind, kind)
                    .andIn(T::getResourceId, needAuthorized);
        });
        return count == needAuthorized.size();
    }

    default int createBatch(Long tenantId, Long userId, Integer kind, Set<Long> toAdd) {
        if (CollectionUtils.isEmpty(toAdd)) {
            return 0;
        }
        final Date now = new Date();
        final List<T> dataList = toAdd.stream().map(resourceId -> {
            final T one = newUserResourceBinding();
            one.setResourceId(resourceId);
            one.setKind(kind);
            one.setTenantId(tenantId);
            one.setUserId(userId);
            one.setCreateTime(now);
            one.setUpdateTime(now);
            return one;
        }).collect(Collectors.toList());
        return this.insertList(dataList);
    }

    default int deleteBatch(Long tenantId, Long userId, Integer kind, Set<Long> toDelete) {
        if (CollectionUtils.isEmpty(toDelete)) {
            return 0;
        }
        return this.deleteByExample2(userResourceBindingClass(), sql -> {
            sql.andEqualTo(T::getTenantId, tenantId)
                    .andEqualTo(T::getUserId, userId)
                    .andEqualTo(T::getKind, kind)
                    .andIn(T::getResourceId, toDelete);
        });
    }
}
