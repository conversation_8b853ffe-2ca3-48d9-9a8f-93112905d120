package com.ynhdkc.tenant.listener.kafka;


import backend.common.component.HisGatewayResponseParser;
import backend.common.constants.KafkaTopicConfig;
import backend.common.entity.dto.hisgateway.response.*;
import backend.common.util.MessageUtil;
import backend.common.util.ObjectsUtils;
import com.ynhdkc.tenant.api.backend.SourceRecommendConfigController;
import com.ynhdkc.tenant.entity.setting.AppointmentRuleSetting;
import com.ynhdkc.tenant.service.backend.DepartmentService;
import com.ynhdkc.tenant.service.backend.HospitalKafkaService;
import com.ynhdkc.tenant.service.customer.SyncDoctorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class HisServiceResponseListener {

    private final HisGatewayResponseParser hisGatewayResponseParser;

    private final DepartmentService departmentService;

    private final HospitalKafkaService hospitalKafkaService;

    private final SyncDoctorService syncDoctorService;

    private final SourceRecommendConfigController sourceRecommendConfigController;


    @KafkaListener(groupId = "tenant", topics = KafkaTopicConfig.DepartmentList.RESPONSE_TOPIC_NAME)
    public void handleDepartmentListMessage(@Payload String message) {
        log.info("departmentListOnRecord message: {}", message);

        MessageEnvelope<?> responseEnvelope = MessageUtil.json2Obj(message, MessageEnvelope.class);
        if (responseEnvelope == null) {
            return;
        }

        Long hospitalAreaId = responseEnvelope.getHospitalAreaId();
        if (hospitalAreaId == null) {
            log.info("departmentListOnRecord hospitalAreaId is null");
            return;
        }

        DepartmentListResponse departmentListResponse = hisGatewayResponseParser.parserDepartmentList(message);
        List<DepartmentListItem> departmentListItems = departmentListResponse.getResult();

        String hospitalCode = responseEnvelope.getHospitalCode();
        if (StringUtils.hasText(hospitalCode)) {
            departmentService.saveDepartment(hospitalCode, departmentListItems);
        }

        hospitalKafkaService.syncHospitalBy(hospitalAreaId);
    }

    @KafkaListener(groupId = "tenant", topics = KafkaTopicConfig.DoctorDataSync.RESPONSE_TOPIC_NAME)
    public void doctorDataSyncMessage(@Payload String message) {
        log.info("DoctorDataSync_message: {}", message);

        MessageEnvelope<?> responseEnvelope = MessageUtil.json2Obj(message, MessageEnvelope.class);
        if (responseEnvelope == null) {
            return;
        }
        DepartmentDoctorListResponse departmentListResponse = hisGatewayResponseParser.parserDepartmentDoctorList(message);
        List<DepartmentDoctorItem> departmentListItems = departmentListResponse.getResult();

        String hospitalCode = responseEnvelope.getHospitalCode();
        AppointmentRuleSetting appointmentRuleSetting = syncDoctorService.querySetting(hospitalCode);

        Long doctorId = responseEnvelope.getDoctorId();
        if (doctorId != null && !ObjectsUtils.isEmpty(departmentListItems)) {
            DepartmentDoctorItem departmentDoctorItem = departmentListItems.get(0);
            syncDoctorService.updateDoctor4DoctorLevel(doctorId, departmentDoctorItem, hospitalCode, appointmentRuleSetting);
        }
    }

    @KafkaListener(groupId = "tenant", topics = "RecommendDoctorGroupUpdate")
    public void doctorGroupUpdateMessage(@Payload String message) {
        if (message.length() > 1) {
            message = message.substring(1, message.length() - 1);
        }
        log.info("接收到消息，开始更新推荐医生信息缓存: {}", message);
        sourceRecommendConfigController.updateCache(message);
        log.info("更新推荐医生信息缓存成功");
    }

}
