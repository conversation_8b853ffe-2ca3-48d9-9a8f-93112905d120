package com.ynhdkc.tenant.client.model;

import backend.common.util.ObjectsUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ynhdkc.tenant.entity.Doctor;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class DepartmentDoctorListEnvResponse {

    @JsonProperty("hospital_id")
    private Long hospitalId;

    @JsonProperty("hospital_code")
    private String hospitalCode;

    @JsonProperty("hospital_area_id")
    private Long hospitalAreaId;

    @JsonProperty("department_id")
    private Long departmentId;

    @JsonProperty("doctor_id")
    private Long doctorId;

    @JsonProperty("message_trace_id")
    private String messageTraceId;

    @JsonProperty("pay_load")
    private Payload payload;

    @Data
    public static class Payload {

        private List<Result> result;

        @Data
        public static class Result {

            private Object amt;

            private Object status;

            private Integer sort;

            @JsonProperty("doctor")
            private Object doctor;

            @JsonProperty("rawParameters")
            private Object rawParameters;

            @JsonProperty("hospital_code")
            private String hospitalCode;

            @JsonProperty("department_name")
            private String departmentName;

            @JsonProperty("department_code")
            private String departmentCode;

            @JsonProperty("exec_department_code")
            private String execDepartmentCode;

            @JsonProperty("doctor_name")
            private String doctorName;

            @JsonProperty("doctor_code")
            private String doctorCode;

            @JsonProperty("doctor_type")
            private Object doctorType;

            @JsonProperty("doctor_type_desc")
            private Object doctorTypeDesc;

            @JsonProperty("doctor_rank_type")
            private String doctorRankType;

            @JsonProperty("doctor_rank_value")
            private String doctorRankValue;

            @JsonProperty("doctor_rank_label")
            private String doctorRankLabel;

            @JsonProperty("registration_level")
            private Object registrationLevel;

            @JsonProperty("registration_level_desc")
            private String registrationLevelDesc;

            @JsonProperty("doctor_profile_photo")
            private String doctorProfilePhoto;

            @JsonProperty("doctor_description")
            private String doctorDescription;

            @JsonProperty("doctor_advantage")
            private String doctorAdvantage;

            @JsonProperty("src_sum")
            private Integer srcSum;

            @JsonProperty("src_number")
            private Integer srcNumber;

            @JsonProperty("is_doctor")
            private Boolean isDoctor;

            @JsonProperty("registration_date")
            private Long registrationDate;

            @JsonProperty("show_schedule_date")
            private String showScheduleDate;

            @JsonProperty("src_date_map")
            private Map<String, Integer> srcDateMap;

            @JsonProperty("schedule_list")
            private List<ScheduleInfo> scheduleList;

            @JsonProperty("registration_amt")
            private BigDecimal registrationAmt;

            @JsonProperty("treat_amt")
            private BigDecimal treatAmt;

            @JsonProperty("expert_amt")
            private BigDecimal expertAmt;

            @JsonProperty("inspection_fees")
            private BigDecimal inspectionFees;

            @JsonProperty("time_type")
            private Integer timeType;

            @JsonProperty("time_type_desc")
            private String timeTypeDesc;

            @JsonProperty("schedule_id")
            private String scheduleId;

            @JsonProperty("begin_time")
            private Object beginTime;

            @JsonProperty("end_time")
            private Object endTime;

            private String honor;

            public Doctor getDoctor() {
                Doctor doctor = new Doctor();
                doctor.setHospitalCode(this.hospitalCode);
//                doctor.setSort(this.sort);
                doctor.setDepartmentCode(ObjectsUtils.isEmpty(this.getExecDepartmentCode()) ? this.getDepartmentCode() : this.getExecDepartmentCode());
                doctor.setName(this.doctorName);
                doctor.setThrdpartDoctorCode(this.doctorCode);
                doctor.setRankDictType(this.doctorRankType);
                doctor.setRankDictValue(this.doctorRankValue);
                doctor.setRankDictLabel(this.doctorRankLabel);
                doctor.setSpeciality(this.doctorAdvantage);
                doctor.setIntroduction(this.doctorDescription);
                doctor.setHeadImgUrl(this.doctorProfilePhoto);
                doctor.setHonor(this.honor);
                doctor.setDisplayDepartmentName(this.departmentName);
                return doctor;
            }

            @Data
            public static class ScheduleInfo {

                @JsonProperty("schedule_id")
                private String scheduleId;

                @JsonProperty("registration_date")
                private Long registrationDate;

                @JsonProperty("department_code")
                private String departmentCode;

                @JsonProperty("doctor_code")
                private String doctorCode;

                @JsonProperty("time_type")
                private Integer timeType;

                @JsonProperty("time_type_desc")
                private String timeTypeDesc;

                @JsonProperty("src_number")
                private Integer srcNumber;
            }
        }
    }
}
