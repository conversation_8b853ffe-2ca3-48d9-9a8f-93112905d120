package backend.security.method;

import backend.security.oauth2.BackendOAuth2Constants;
import org.springframework.security.access.prepost.PreAuthorize;

import java.lang.annotation.*;

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
@PreAuthorize("hasAnyAuthority('" + BackendOAuth2Constants.AUTHORITY_SCOPE_GLOBAL + "','" + BackendOAuth2Constants.AUTHORITY_SCOPE_RPC + "')")
public @interface BackendSystemOrRPCRequired {
}