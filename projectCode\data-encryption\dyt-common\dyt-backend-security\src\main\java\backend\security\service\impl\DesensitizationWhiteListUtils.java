package backend.security.service.impl;/**
 * <AUTHOR>
 * @date 2024/07/24/14:55
 */


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/7/24 14:55
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class DesensitizationWhiteListUtils {
    private final static String WHITE_LIST_KEY = "dyt-tenant:desensitization:white-list";
    private static final String ALLOW_QUERY_UN_DESENSITIZATION_TENANT = "dyt-tenant:desensitization:allow-query-un-desensitization-tenant:";
    private final RedisTemplate<String, Object> redisTemplate;

    public boolean needDesensitization(Long tenantUserId) {
        return null == redisTemplate.opsForValue().get(ALLOW_QUERY_UN_DESENSITIZATION_TENANT + tenantUserId);
    }

    public boolean whiteListIsContainTenantUser(Long tenantUserId) {
        return getWhiteList().contains(tenantUserId);
    }

    public Set<Long> getWhiteList() {
        Set<Object> retrievedSet = redisTemplate.opsForSet().members(WHITE_LIST_KEY);
        if (!CollectionUtils.isEmpty(retrievedSet)) {
            return retrievedSet.stream()
                    .map(Object::toString)
                    .map(Long::parseLong)
                    .collect(Collectors.toSet());
        }
        return new HashSet<>();
    }
}
