package backend.security.method;

import backend.security.oauth2.BackendOAuth2Constants;
import backend.security.service.BackendTenantUserService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aopalliance.intercept.MethodInvocation;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.security.access.AccessDecisionManager;
import org.springframework.security.access.AccessDecisionVoter;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.access.expression.method.MethodSecurityExpressionHandler;
import org.springframework.security.access.method.AbstractMethodSecurityMetadataSource;
import org.springframework.security.access.method.MethodSecurityMetadataSource;
import org.springframework.security.access.vote.AbstractAccessDecisionManager;
import org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.util.Collection;
import java.util.Collections;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
public abstract class BackendMethodSecurityConfiguration extends GlobalMethodSecurityConfiguration {
    private SecurityManifest securityManifest;

    private final BackendTenantUserService backendTenantUserService;

    @Override
    protected AccessDecisionManager accessDecisionManager() {
        final AbstractAccessDecisionManager accessDecisionManager = (AbstractAccessDecisionManager) super.accessDecisionManager();
        accessDecisionManager.getDecisionVoters().add(new AccessDecisionVoter<MethodInvocation>() {
            @Override
            public boolean supports(ConfigAttribute attribute) {
                return attribute instanceof BackendSecurityRequiredAttribute;
            }

            @Override
            public boolean supports(Class<?> clazz) {
                return MethodInvocation.class.isAssignableFrom(clazz);
            }

            @Override
            public int vote(Authentication authentication, MethodInvocation mi, Collection<ConfigAttribute> attributes) {
                /* 校验Token是不是JWT */
                if (!(authentication instanceof JwtAuthenticationToken)) {
                    return ACCESS_ABSTAIN;
                }

                /* 获取实例为 BackendSecurityRequiredAttribute 的属性 */
                final Optional<BackendSecurityRequiredAttribute> find = attributes.stream()
                        .filter(BackendSecurityRequiredAttribute.class::isInstance)
                        .map(e -> (BackendSecurityRequiredAttribute) e)
                        .findFirst();
                if (!find.isPresent()) {
                    return ACCESS_ABSTAIN;
                }
                /* 获取JWT中的AUTH集合 */
                final JwtAuthenticationToken jwtAuthenticationToken = (JwtAuthenticationToken) authentication;
                final Set<String> authorities = jwtAuthenticationToken
                        .getAuthorities().stream()
                        .map(GrantedAuthority::getAuthority)
                        .collect(Collectors.toSet());
                final BackendSecurityRequired annotation = find.get().getAnnotation();

                /* 读取用户数据权限 */
                Long userId = jwtAuthenticationToken.getToken().getClaim(BackendOAuth2Constants.SCOPE_USER);

                /* 是否是租户管理员 */
                final boolean needTenantManager = annotation.tenantManager();
                boolean isSuperAdmin = backendTenantUserService.isSuperAdmin(userId);
                if (needTenantManager) {
                    return isSuperAdmin ? ACCESS_GRANTED : ACCESS_DENIED;
                }

                /* 管理员跳过方法权限校验 */
                if (isSuperAdmin) {
                    return ACCESS_GRANTED;
                }

                /* 判断是否拥有当前方法权限 */
                if (!backendTenantUserService.checkApiAuthorities(authorities)) {
                    return ACCESS_DENIED;
                }

                /* 判断三个表达式是否同时为空，是则跳过数据权限校验 */
                if (skipExpression(annotation)) {
                    return ACCESS_GRANTED;
                }

                /* 提取 tenant_id hospital_area_id department_id */
                Long tenantId = extractLongFromExpression(annotation.tenantIdExpr(), jwtAuthenticationToken, mi);
                Long hospitalId = extractLongFromExpression(annotation.hospitalIdExpr(), jwtAuthenticationToken, mi);
                Long hospitalAreaId = extractLongFromExpression(annotation.hospitalAreaIdExpr(), jwtAuthenticationToken, mi);
                Long departmentId = extractLongFromExpression(annotation.departmentIdExpr(), jwtAuthenticationToken, mi);

                /* 写入数据权限校验 */
                return backendTenantUserService.hasWhitePrivilege(userId, tenantId, hospitalId, hospitalAreaId, departmentId) ? ACCESS_GRANTED : ACCESS_DENIED;
            }
        });
        return accessDecisionManager;
    }

    private static boolean skipExpression(BackendSecurityRequired annotation) {
        return !StringUtils.hasText(annotation.tenantIdExpr())
                && !StringUtils.hasText(annotation.hospitalAreaIdExpr())
                && !StringUtils.hasText(annotation.departmentIdExpr());
    }

    private Long extractLongFromExpression(String expression, JwtAuthenticationToken authentication, MethodInvocation mi) {
        if (!StringUtils.hasText(expression)) {
            return null;
        }
        MethodSecurityExpressionHandler handler = getExpressionHandler();
        final Expression expr = handler.getExpressionParser().parseExpression(expression);
        EvaluationContext ctx = handler.createEvaluationContext(authentication, mi);
        return expr.getValue(ctx, Long.class);
    }

    @Override
    protected MethodSecurityMetadataSource customMethodSecurityMetadataSource() {
        return new AbstractMethodSecurityMetadataSource() {

            @Override
            public Collection<ConfigAttribute> getAllConfigAttributes() {
                return null;
            }

            @Override
            public Collection<ConfigAttribute> getAttributes(Method method, Class<?> targetClass) {
                final BackendSecurityRequired annotation = AnnotationUtils.findAnnotation(method, BackendSecurityRequired.class);
                if (annotation != null) {
                    if (shouldCheckSecureManifest()) {
                        checkSecureManifest(annotation, method, targetClass);
                    }
                    return Collections.singleton(new BackendSecurityRequiredAttribute(annotation));
                }
                return Collections.emptyList();
            }
        };
    }

    ObjectMapper JSON = new ObjectMapper();

    private void checkSecureManifest(BackendSecurityRequired annotation, Method method, Class<?> targetClass) {
        tryInitializeSecureManifest();
        if (annotation.value() != null) {
            for (String authority : annotation.value()) {
                if (!this.securityManifest.hasAuthority(authority)) {
                    throw new IllegalStateException(String.format("method[%s.%s] authority[%s] is not in security-manifest.json", targetClass.getSimpleName(), method.getName(), authority));
                }
            }
        }
    }

    private void tryInitializeSecureManifest() {
        if (this.securityManifest == null) {
            synchronized (this) {
                if (this.securityManifest == null) {
                    try {
                        final ClassPathResource resource = new ClassPathResource("/security-manifest.json");
                        try (final InputStream inputStream = resource.getInputStream()) {
                            final SecurityManifest tmp = JSON.readValue(inputStream, SecurityManifest.class);
                            tmp.initialize();
                            this.securityManifest = tmp;
                        }
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        }

    }

    protected boolean shouldCheckSecureManifest() {
        return false;
    }

    public SecurityManifest getSecurityManifest() {
        return securityManifest;
    }
}
