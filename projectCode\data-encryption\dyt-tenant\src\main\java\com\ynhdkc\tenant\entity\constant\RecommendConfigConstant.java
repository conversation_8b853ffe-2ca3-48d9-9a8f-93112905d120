package com.ynhdkc.tenant.entity.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

public class RecommendConfigConstant {
    @AllArgsConstructor
    @Getter
    public enum DataType {
        // 以字典中配置的recommend_data_type为准
        HOSPITAL("1", "医院"),
        DEPARTMENT("2", "科室"),
        DOCTOR("3", "医生"),
        ONLINE_DOCTOR("4", "在线问诊医生"),
        HOSPITAL_AREA("5", "院区"),
        ;
        final String type;
        final String desc;

        public static DataType getByType(String type) {
            for (DataType dataType : DataType.values()) {
                if (dataType.getType().equals(type)) {
                    return dataType;
                }
            }
            return null;
        }
    }

    @AllArgsConstructor
    @Getter
    public enum DictType {
        BIZ_TYPE("recommend_config_biz_type", "首页-专区推荐配置-业务类型"),
        DATA_TYPE("recommend_config_data_type", "推荐配置数据类型"),
        ;
        final String type;
        final String desc;

        public static List<String> typeList() {
            return Arrays.asList(BIZ_TYPE.type, DATA_TYPE.type);
        }
    }

    @AllArgsConstructor
    @Getter
    public enum ReservedField{
        HOSPITAL_ID("hospital_id", "医院id"),
        HOSPITAL_NAME("hospital_name", "医院名称"),
        HOSPITAL_AREA_ID("hospital_area_id", "院区id"),
        DEPARTMENT_ID("department_id", "科室id"),
        DOCTOR_ID("doctor_id", "医生id"),
        ;
        final String type;
        final String desc;
    }
}
