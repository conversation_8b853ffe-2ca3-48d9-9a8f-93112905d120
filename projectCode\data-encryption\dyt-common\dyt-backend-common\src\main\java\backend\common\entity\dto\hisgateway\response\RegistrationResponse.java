package backend.common.entity.dto.hisgateway.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

@Data
public class RegistrationResponse {

    @JsonProperty("hospital_code")
    private String hospitalCode;

    @JsonProperty("medical_record_number")
    private String medicalRecordNumber;

    @JsonProperty("registration_date")
    private String registrationDate;

    @JsonProperty("registration_level")
    private String registrationLevel;

    @JsonProperty("registration_level_desc")
    private String registrationLevelDesc;

    @JsonProperty("department_name")
    private String departmentName;

    @JsonProperty("department_code")
    private String departmentCode;

    @JsonProperty("doctor_name")
    private String doctorName;

    @JsonProperty("doctor_code")
    private String doctorCode;

    @JsonProperty("visiting_serial_number")
    private String visitingSerialNumber;

    private String amt;

    @JsonProperty("order_number")
    private String orderNumber;

    private String name;

    @JsonProperty("id_card_number")
    private String idCardNumber;

    private String birthday;

    private String phone;

    @JsonProperty("is_visiting")
    private String isVisiting;

    @JsonProperty("visiting_date")
    private String visitingDate;

    @JsonProperty("visiting_address")
    private String visitingAddress;

    @JsonProperty("visiting_desc")
    private String visitingDesc;

    @JsonProperty("invoice_number")
    private String invoiceNumber;

    @JsonProperty("bill_number")
    private String billNumber;

    @JsonProperty("bill_code")
    private String billCode;

    @JsonProperty("verification_code")
    private String verificationCode;

    private Boolean result = false;

    @JsonProperty("need_change_status")
    private boolean needChangeStatus = true;

    @JsonProperty("appointment_serial_number")
    private String appointmentSerialNumber;

    @JsonProperty("lock_registration_number")
    private String lockRegistrationNumber;

    @JsonProperty("trade_serial_number")
    private String tradeSerialNumber;

    private String reason;

    @JsonProperty("other_info") // 如果设置了该字段，会放入到 order_appointment 的 mark 字段里面
    private String otherInfo;

    @JsonProperty("visiting_start_time")
    private String visitingStartTime;

    @JsonProperty("visiting_end_time")
    private String visitingEndTime;

    private Map<String, Object> rawParameters;

    @JsonProperty("original_flag")
    private String originalFlag;

    @JsonProperty("reg_flag")
    private String regFlag;

    @JsonProperty("cancel_flag")
    private String cancelFlag;

    @JsonProperty("stop_diagnosis_flag")
    private String stopDiagnosisFlag;
}
