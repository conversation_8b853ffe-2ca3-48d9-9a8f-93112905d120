//package com.ynhdkc.tenant.api;
//
//import com.ynhdkc.tenant.DytTenantApplication;
//import com.ynhdkc.tenant.config.H2Config;
//import com.ynhdkc.tenant.model.*;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Order;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.boot.test.web.client.TestRestTemplate;
//import org.springframework.boot.web.server.LocalServerPort;
//import org.springframework.context.annotation.Import;
//import org.springframework.http.ResponseEntity;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.context.jdbc.Sql;
//import org.springframework.test.context.junit.jupiter.SpringExtension;
//
//import java.math.BigDecimal;
//import java.util.Objects;
//
//import static org.junit.jupiter.api.Assertions.assertEquals;
//import static org.junit.jupiter.api.Assertions.assertNotEquals;
//
///**
// * <AUTHOR>
// * @since 2023/2/14 16:47:12
// */
//@ExtendWith(SpringExtension.class)
//@Import(H2Config.class)
//@SpringBootTest(classes = DytTenantApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//@ActiveProfiles("dev")
//public class TenantControllerIntegrationTest {
//
//    private static final Integer PAGE_SIZE = 10;
//    private static final Integer CURRENT_PAGE = 1;
//    private String tenantPath;
//    @LocalServerPort
//    private int port;
//    @Autowired
//    private TestRestTemplate restTemplate;
//
//    @BeforeEach
//    public void setUp() {
//        String baseUrl = "http://localhost:" + port + "/apis/v1/tenant";
//        tenantPath = baseUrl + "/tenant";
//    }
//
//
//    @Test
//    @Order(1)
//    @Sql(statements = "delete from t_tenant where name = 'test'", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
//    void createTenant_ShouldPass() {
//        CreateTenantDto createTenantDto = new CreateTenantDto();
//        createTenantDto.setName("test");
//        createTenantDto.setDescription("one test tenant");
//        createTenantDto.setAddress(getAddress());
//        createTenantDto.setLogoUrl("test.png");
//        createTenantDto.setContact("test");
//        createTenantDto.setContactPhoneNumber("test");
//        createTenantDto.setContactEmail("<EMAIL>");
//
//        ResponseEntity<Long> response = restTemplate.postForEntity(tenantPath, createTenantDto, Long.class);
//        assertEquals(200, response.getStatusCodeValue());
//        assertNotEquals(0, response.getBody());
//    }
//
//    @Test
//    @Order(2)
//    @Sql(statements = "delete from t_tenant where name = 'test'", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
//    void deleteTenant_ShouldPass() {
//        CreateTenantDto createTenantDto = new CreateTenantDto();
//        createTenantDto.setName("test");
//        createTenantDto.setDescription("one test tenant");
//        createTenantDto.setAddress(getAddress());
//        createTenantDto.setLogoUrl("test.png");
//        createTenantDto.setContact("test");
//        createTenantDto.setContactPhoneNumber("test");
//        createTenantDto.setContactEmail("<EMAIL>");
//
//        ResponseEntity<Long> response = restTemplate.postForEntity(tenantPath, createTenantDto, Long.class);
//        assertEquals(200, response.getStatusCodeValue());
//
//        restTemplate.delete(tenantPath + "/" + response.getBody());
//        assertEquals(200, response.getStatusCodeValue());
//    }
//
//    @Test
//    @Order(3)
//    @Sql(statements = "delete from t_tenant where name = 'test'", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
//    void getTenantDetail_ShouldPass() {
//        CreateTenantDto createTenantDto = new CreateTenantDto();
//        createTenantDto.setName("test");
//        createTenantDto.setDescription("one test tenant");
//        createTenantDto.setAddress(getAddress());
//        createTenantDto.setLogoUrl("test.png");
//        createTenantDto.setContact("test");
//        createTenantDto.setContactPhoneNumber("test");
//        createTenantDto.setContactEmail("<EMAIL>");
//
//        ResponseEntity<Long> response = restTemplate.postForEntity(tenantPath, createTenantDto, Long.class);
//        assertEquals(200, response.getStatusCodeValue());
//
//        ResponseEntity<TenantVo> responseEntity = restTemplate.getForEntity(tenantPath + "/" + response.getBody(), TenantVo.class);
//        assertEquals(200, responseEntity.getStatusCodeValue());
//        assertEquals("test", Objects.requireNonNull(responseEntity.getBody()).getName());
//        assertEquals("test", Objects.requireNonNull(responseEntity.getBody()).getAddress().getProvince());
//    }
//
//    @Test
//    @Order(4)
//    @Sql(statements = "delete from t_tenant where name = 'test'", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
//    void getTenantList_ShouldPass() {
//        CreateTenantDto createTenantDto = new CreateTenantDto();
//        createTenantDto.setName("test");
//        createTenantDto.setDescription("one test tenant");
//        createTenantDto.setAddress(getAddress());
//        createTenantDto.setLogoUrl("test.png");
//        createTenantDto.setContact("test");
//        createTenantDto.setContactPhoneNumber("test");
//        createTenantDto.setContactEmail("<EMAIL>");
//
//        ResponseEntity<Long> response = restTemplate.postForEntity(tenantPath, createTenantDto, Long.class);
//        assertEquals(200, response.getStatusCodeValue());
//
//        ResponseEntity<TenantPageVo> responseEntity = restTemplate.getForEntity(tenantPath + "?name=test&contact=test&contact_phone_number=test&page_size=" + PAGE_SIZE + "&current_page=" + CURRENT_PAGE, TenantPageVo.class);
//        assertEquals(200, responseEntity.getStatusCodeValue());
//        assertEquals(1, Objects.requireNonNull(responseEntity.getBody()).getTotalSize());
//        assertEquals("test", Objects.requireNonNull(responseEntity.getBody()).getList().get(0).getName());
//        assertEquals("test", Objects.requireNonNull(responseEntity.getBody()).getList().get(0).getAddress().getProvince());
//    }
//
//    @Test
//    @Order(5)
//    @Sql(statements = "insert into t_tenant (id,name) values (15,'test')", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_METHOD)
//    @Sql(statements = "delete from t_tenant where name = 'test'", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
//    void updateTenant_ShouldPass() {
//        UpdateTenantDto updateTenantDto = new UpdateTenantDto();
//        updateTenantDto.setName("test1");
//        updateTenantDto.setContact("123123");
//        updateTenantDto.setContactPhoneNumber("123123");
//
//        restTemplate.put(tenantPath + "/15", updateTenantDto);
//
//        ResponseEntity<TenantVo> responseEntity = restTemplate.getForEntity(tenantPath + "/" + 15, TenantVo.class);
//        assertEquals(200, responseEntity.getStatusCodeValue());
//        assertEquals("test1", Objects.requireNonNull(responseEntity.getBody()).getName());
//    }
//
//    AddressVo getAddress() {
//        AddressVo address = new AddressVo();
//        address.setProvince("test");
//        address.setCity("test");
//        address.setCounty("test");
//        address.setDetail("test");
//        address.setLongitude(BigDecimal.valueOf(1.0));
//        address.setLatitude(BigDecimal.valueOf(1.0));
//        return address;
//    }
//}
