package com.ynhdkc.tenant.entity;

import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/11/2 10:28
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@Table(name = "t_recommend_statistics")
public class RecommendStatistics extends BaseEntity {
    private Long recommendId;

    private Integer quantity;

    private Integer total;
}
