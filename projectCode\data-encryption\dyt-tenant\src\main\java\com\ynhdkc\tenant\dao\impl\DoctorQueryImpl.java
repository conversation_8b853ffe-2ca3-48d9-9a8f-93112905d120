package com.ynhdkc.tenant.dao.impl;

import backend.common.util.MybatisUtil;
import backend.common.util.ObjectsUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.page.PageMethod;
import com.ynhdkc.tenant.dao.DoctorQuery;
import com.ynhdkc.tenant.dao.mapper.DoctorGroupRelationMapper;
import com.ynhdkc.tenant.dao.mapper.DoctorMapper;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.entity.DoctorGroupRelation;
import com.ynhdkc.tenant.entity.Hospital;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.session.RowBounds;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-03 16:25
 */
@Repository
@RequiredArgsConstructor
public class DoctorQueryImpl implements DoctorQuery {
    private final DoctorMapper doctorMapper;
    private final DoctorGroupRelationMapper doctorGroupRelationMapper;

    @Override
    public Page<Doctor> pageQueryDoctorWithCategory(DoctorQueryOption option) {
        try (final Page<Hospital> page = PageHelper.startPage(option.getCurrentPage(), option.getPageSize())) {
            return page.doSelectPage(() -> doctorMapper.selectDoctorWithCategory(option));
        }
    }

    @Override
    public Page<Doctor> pageQuery(DoctorQueryOption option) {
        try (final Page<Doctor> page = PageMethod.startPage(option.getCurrentPage(), option.getPageSize())) {
            return page.doSelectPage(() -> doctorMapper.selectByExample(Doctor.class, sql -> {
                sql.defGroup(condition -> {
                    if (null != option.getTenantId()) {
                        condition.andEqualTo(Doctor::getTenantId, option.getTenantId());
                    }
                    if (null != option.getHospitalId()) {
                        condition.andEqualTo(Doctor::getHospitalId, option.getHospitalId());
                    }
                    if (null != option.getHospitalAreaId()) {
                        condition.andEqualTo(Doctor::getHospitalAreaId, option.getHospitalAreaId());
                    }
                    if (null != option.getDepartmentId()) {
                        condition.andEqualTo(Doctor::getDepartmentId, option.getDepartmentId());
                    }
                    if (null != option.getId()) {
                        condition.andEqualTo(Doctor::getId, option.getId());
                    }
                    if (null != option.getName()) {
                        condition.andLike(Doctor::getName, MybatisUtil.likeBoth(option.getName()));
                    }
                    if (null != option.getThrdpartDoctorCode()) {
                        condition.andEqualTo(Doctor::getThrdpartDoctorCode, option.getThrdpartDoctorCode());
                    }
                    if (null != option.getStatus()) {
                        condition.andEqualTo(Doctor::getStatus, option.getStatus());
                    }
                    if (null != option.getStartCreateTime() && null != option.getEndCreateTime()) {
                        condition.andBetween(Doctor::getCreateTime, option.getStartCreateTime(), option.getEndCreateTime());
                    }
                    if (null != option.getUserId()) {
                        condition.andEqualTo(Doctor::getUserId, option.getUserId());
                    }
                    if (null != option.getDisplay()) {
                        condition.andEqualTo(Doctor::getDisplay, option.getDisplay());
                    }
                    if (!CollectionUtils.isEmpty(option.getCategory())) {
                        StringBuilder builder = new StringBuilder();
                        option.getCategory().sort(Integer::compareTo);
                        option.getCategory().forEach(category -> builder.append(category).append("%"));
                        String queryPattern = builder.toString();
                        condition.andLike(Doctor::getCategory, MybatisUtil.likeRight(queryPattern));
                    }
                    if (!CollectionUtils.isEmpty(option.getTags())) {
                        StringBuilder builder = new StringBuilder();
                        option.getTags().sort(Integer::compareTo);
                        option.getTags().forEach(tag -> builder.append(tag).append("%"));
                        String queryPattern = builder.toString();
                        condition.andLike(Doctor::getTags, MybatisUtil.likeRight(queryPattern));
                    }
                    if (!CollectionUtils.isEmpty(option.getIncludeIds())) {
                        condition.andIn(Doctor::getId, option.getIncludeIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getIncludeTenantIds())) {
                        condition.andIn(Doctor::getTenantId, option.getIncludeTenantIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getExcludeTenantIds())) {
                        condition.andNotIn(Doctor::getTenantId, option.getExcludeTenantIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getIncludeHospitalIds())) {
                        condition.andIn(Doctor::getHospitalId, option.getIncludeHospitalIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getExcludeHospitalIds())) {
                        condition.andNotIn(Doctor::getHospitalId, option.getExcludeHospitalIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getIncludeHospitalAreaIds())) {
                        condition.andIn(Doctor::getHospitalAreaId, option.getIncludeHospitalAreaIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getExcludeHospitalAreaIds())) {
                        condition.andNotIn(Doctor::getHospitalAreaId, option.getExcludeHospitalAreaIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getIncludeDepartmentIds())) {
                        condition.andIn(Doctor::getDepartmentId, option.getIncludeDepartmentIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getExcludeDepartmentIds())) {
                        condition.andNotIn(Doctor::getDepartmentId, option.getExcludeDepartmentIds());
                    }
                });
                if (null == option.getSortBy()) {
                    sql.builder(builder -> builder.orderByDesc(Doctor::getSort));
                } else {
                    sql.builder(builder -> builder.orderByDesc(option.getSortBy()));
                }
            }));
        }
    }

    @Override
    public Doctor queryDoctorById(Long doctorId) {
        return doctorMapper.selectByPrimaryKey(doctorId);
    }

    @Override
    public List<Doctor> queryAll() {
        return doctorMapper.selectAll();
    }

    @Override
    public List<Doctor> queryByIds(List<Long> doctorIds) {
        if (CollectionUtils.isEmpty(doctorIds)) {
            return Collections.emptyList();
        }
        return doctorMapper.selectByExample2(Doctor.class, sql -> {
            sql.andIn(Doctor::getId, doctorIds);
        });
    }

    @Override
    public List<Doctor> queryBy(@NonNull Long departmentId) {
        return doctorMapper.selectByExample(Doctor.class, sql -> {
            sql.defGroup(condition -> condition.andEqualTo(Doctor::getDepartmentId, departmentId));
            sql.builder(builder -> builder.orderByAsc(Doctor::getSort));
        });
    }

    @Override
    public List<Doctor> queryByDepartmentCodeAndHospitalCode(@NonNull String departmentCode, String hospitalCode) {
        return doctorMapper.selectByExample(Doctor.class, sql -> {
            sql.defGroup(condition -> condition.andEqualTo(Doctor::getDepartmentCode, departmentCode).andEqualTo(Doctor::getHospitalCode, hospitalCode));
            sql.builder(builder -> builder.orderByAsc(Doctor::getSort));
        });
    }

    @Override
    public List<Doctor> queryBy(@NonNull Long hospitalAreaId, Long departmentId) {
        return doctorMapper.selectByExample(Doctor.class,
                sql -> sql.defGroup(condition -> {
                    condition.andEqualTo(Doctor::getHospitalAreaId, hospitalAreaId);
                    if (null != departmentId) {
                        condition.andEqualTo(Doctor::getDepartmentId, departmentId);
                    }
                }));
    }

    @Override
    public List<Doctor> queryLikeName(String name) {
        return doctorMapper.selectByExample2(Doctor.class,
                sql -> sql.andLike(Doctor::getName, MybatisUtil.likeLeft(name)));
    }

    @Override
    public List<Doctor> queryList(String hospitalAreaCode, List<String> doctorCodeList) {
        Example example = new Example(Doctor.class);
        example.createCriteria().andEqualTo("hospitalCode", hospitalAreaCode).andIn("thrdpartDoctorCode", doctorCodeList);

        return doctorMapper.selectByExample(example);
    }

    @Override
    public Doctor queryByCode(String hospitalAreaCode, String departmentCode, String doctorCode) {
        List<Doctor> doctors = doctorMapper.selectByExample2(Doctor.class, sql -> {
            if (null != hospitalAreaCode) {
                sql.andEqualTo(Doctor::getHospitalCode, hospitalAreaCode);
            }
            if (null != departmentCode) {
                sql.andEqualTo(Doctor::getDepartmentCode, departmentCode);
            }
            if (null != doctorCode) {
                sql.andEqualTo(Doctor::getThrdpartDoctorCode, doctorCode);
            }
        });
        return CollectionUtils.isEmpty(doctors) ? null : doctors.get(0);
    }

    @Override
    public List<Doctor> queryDoctorsByCode(String hospitalAreaCode, String departmentCode, String doctorCode) {
        return doctorMapper.selectByExample2(Doctor.class, sql -> {
            if (null != hospitalAreaCode) {
                sql.andEqualTo(Doctor::getHospitalCode, hospitalAreaCode);
            }
            if (null != departmentCode) {
                sql.andEqualTo(Doctor::getDepartmentCode, departmentCode);
            }
            if (null != doctorCode) {
                sql.andEqualTo(Doctor::getThrdpartDoctorCode, doctorCode);
            }
        });
    }

    @Override
    public List<Doctor> queryDoctorsByHospitalAreaIdAndDepartmentCode(Long hospitalAreaId, String departmentCode) {
        return doctorMapper.selectByExample2(Doctor.class, sql -> {
            if (null != hospitalAreaId) {
                sql.andEqualTo(Doctor::getHospitalAreaId, hospitalAreaId);
            }
            if (null != departmentCode) {
                sql.andEqualTo(Doctor::getDepartmentCode, departmentCode);
            }
            sql.andEqualTo(Doctor::getStatus, 0);
        });
    }

    @Override
    public List<Doctor> queryBy(List<Long> doctorIds) {
        if (CollectionUtils.isEmpty(doctorIds)) {
            return Collections.emptyList();
        }
        return doctorMapper.selectByExample2(Doctor.class, sql -> sql.andIn(Doctor::getId, doctorIds));
    }

    @Override
    public List<Doctor> findByDepartmentId(Long departmentId) {
        return doctorMapper.selectByExample2(Doctor.class, consumer -> consumer.andEqualTo(Doctor::getDepartmentId, departmentId));
    }

    @Override
    public DoctorGroupRelation queryDoctorGroupRelation(Long doctorId, Integer doctorGroupSource, Long doctorGroupId) {
        return doctorGroupRelationMapper.selectOneByExample2(DoctorGroupRelation.class, consumer -> {
            consumer.andEqualTo(DoctorGroupRelation::getDoctorId, doctorId);
            consumer.andEqualTo(DoctorGroupRelation::getDoctorGroupSource, doctorGroupSource);
            consumer.andEqualTo(DoctorGroupRelation::getDoctorGroupId, doctorGroupId);
        });
    }

    @Override
    public DoctorGroupRelation queryDoctorGroupRelationById(Long relationId) {
        return doctorGroupRelationMapper.selectByPrimaryKey(relationId);
    }

    @Override
    public Page<DoctorGroupRelation> pageQueryDoctorGroupRelation(DoctorGroupRelationQueryOption option) {
        try (final Page<Doctor> page = PageMethod.startPage(option.getCurrentPage(), option.getPageSize())) {
            return page.doSelectPage(() -> doctorGroupRelationMapper.selectByExample2(DoctorGroupRelation.class, sql -> {
                if (null != option.getDoctorId()) {
                    sql.andEqualTo(DoctorGroupRelation::getDoctorId, option.getDoctorId());
                }
                if (null != option.getDoctorName()) {
                    sql.andLike(DoctorGroupRelation::getDoctorName, MybatisUtil.likeLeft(option.getDoctorName()));
                }
                if (null != option.getDoctorGroupSource()) {
                    sql.andEqualTo(DoctorGroupRelation::getDoctorGroupSource, option.getDoctorGroupSource());
                }
                if (null != option.getDoctorGroupId()) {
                    sql.andEqualTo(DoctorGroupRelation::getDoctorGroupId, option.getDoctorGroupId());
                }
                if (null != option.getDoctorGroupName()) {
                    sql.andLike(DoctorGroupRelation::getDoctorGroupName, MybatisUtil.likeLeft(option.getDoctorGroupName()));
                }
            }));
        }
    }

    @Override
    public Boolean hasDoctorGroup(Long id) {
        return doctorGroupRelationMapper.selectCountByExample2(DoctorGroupRelation.class, sql -> sql.andEqualTo(DoctorGroupRelation::getDoctorId, id)) > 0;
    }

    @Override
    public List<Doctor> queryList(String hospitalCode, int page, int size) {
        Example example = new Example(Doctor.class);
        example.createCriteria().andEqualTo("hospitalCode", hospitalCode).andEqualTo("status", 0);

        int offset = (page - 1) * size;
        return doctorMapper.selectByExampleAndRowBounds(example, new RowBounds(offset, size));
    }

    @Override
    public List<DoctorGroupRelation> listDoctorGroupRelationBy(List<Long> doctorIds) {
        if (CollectionUtils.isEmpty(doctorIds)) {
            return Collections.emptyList();
        }
        return doctorGroupRelationMapper.selectByExample2(DoctorGroupRelation.class, sql -> sql.andIn(DoctorGroupRelation::getDoctorId, doctorIds));
    }

    @Override
    public List<Doctor> queryByHospitalCodeAndDepartmentCodes(String hospitalCode, Collection<String> departmentCodes) {
        return doctorMapper.selectByExample2(Doctor.class, sql -> sql.andIn(Doctor::getDepartmentCode, departmentCodes)
                .andEqualTo(Doctor::getHospitalCode, hospitalCode)
                .andEqualTo(Doctor::getStatus, 0));
    }

    @Override
    public List<Doctor> queryByDepartmentIds(Collection<Long> departmentIds) {
        return doctorMapper.selectByExample2(Doctor.class, sql -> sql.andIn(Doctor::getDepartmentId, departmentIds)
                .andEqualTo(Doctor::getStatus, 0));
    }

    @Override
    public List<Long> queryEnableDoctorIds(List<Long> departmentIds) {
        if (ObjectsUtils.isEmpty(departmentIds)) {
            return Collections.emptyList();
        }
        return doctorMapper.selectByExample2(Doctor.class, sql -> sql.andIn(Doctor::getDepartmentId, departmentIds)
                .andEqualTo(Doctor::getStatus, 0)).stream().map(Doctor::getId).collect(Collectors.toList());
    }

    @Override
    public List<Doctor> queryEnableDoctorsBy(String hospitalCode) {
        return doctorMapper.selectByExample2(Doctor.class, sql -> sql.andEqualTo(Doctor::getHospitalCode, hospitalCode)
                .andEqualTo(Doctor::getStatus, 0));
    }

    @Override
    public List<Doctor> queryBy(Set<Long> doctorIds) {
        return doctorMapper.selectByExample2(Doctor.class, sql -> sql.andIn(Doctor::getId, doctorIds));
    }

    @Override
    public List<Doctor> queryBy(String hospitalCode, String thrdpartDoctorCode) {
        return doctorMapper.selectByExample2(Doctor.class, sql -> {
            sql.andEqualTo(Doctor::getHospitalCode, hospitalCode);
            sql.andEqualTo(Doctor::getThrdpartDoctorCode, thrdpartDoctorCode);
        });
    }

}
