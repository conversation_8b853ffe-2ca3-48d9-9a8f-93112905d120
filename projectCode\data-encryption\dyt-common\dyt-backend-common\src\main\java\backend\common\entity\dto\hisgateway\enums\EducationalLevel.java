package backend.common.entity.dto.hisgateway.enums;

import com.fasterxml.jackson.annotation.JsonGetter;

/**
 * GB/T 4658
 */
public enum EducationalLevel {

	POSTGRADUATE("研究生", 10), UNDERGRADUATE("大学本科", 20), SPECIALIST("大学专科及专科学校", 30), SECONDARY_EDUCATION("中专及中技", 40),
	HIGH_SCHOOL("高中", 60), MIDDLE_SCHOOL("初中", 70), PRIMARY_SCHOOL("小学", 81), ILLITERACY("文盲", 90);

	private final String value;

	private final Integer code;

	EducationalLevel(String value, Integer code) {
		this.value = value;
		this.code = code;
	}

	public static EducationalLevel getFromCode(int code) {
		for (EducationalLevel t : EducationalLevel.values()) {
			if (t.getCode().equals(code)) {
				return t;
			}
		}
		throw new IllegalArgumentException("文化程度不存在");
	}

	public static EducationalLevel getFromValue(String value) {
		for (EducationalLevel t : EducationalLevel.values()) {
			if (t.getValue().equals(value)) {
				return t;
			}
		}
		throw new IllegalArgumentException("文化程度不存在");
	}

	public String getValue() {
		return value;
	}

	public Integer getCode() {
		return code;
	}

	@JsonGetter("code")
	public String getRequestCode() {
		return code.toString();
	}

}
