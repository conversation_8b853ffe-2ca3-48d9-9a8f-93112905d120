package com.ynhdkc.tenant.api.customer;

import com.ynhdkc.tenant.handler.CustomerCodeMappingApi;
import com.ynhdkc.tenant.model.GetCodeMappingReqDto;
import com.ynhdkc.tenant.model.GetCodeMappingVo;
import com.ynhdkc.tenant.service.customer.CustomerCodeMappingService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/8/11 14:52
 */
@Api(tags = "CustomerCodeMapping")
@RestController
@RequiredArgsConstructor
public class CustomerCodeMappingController implements CustomerCodeMappingApi {
    private final CustomerCodeMappingService customerCodeMappingService;

    @Override
    public ResponseEntity<GetCodeMappingVo> getCodeMapping(GetCodeMappingReqDto request) {
        return ResponseEntity.ok(customerCodeMappingService.getCodeMapping(request));
    }
}
