CREATE TABLE t_tenant_user_structure
(
    id               BIGINT(20)  NOT NULL AUTO_INCREMENT COMMENT '全局唯一标识',
    user_id          BIGINT(20)  NOT NULL COMMENT '用户id',
    tenant_id        BIGINT(20)  NOT NULL COMMENT '租户id',
    hospital_id      BIGINT(20)  NOT NULL COMMENT '医院id',
    hospital_area_id BIGINT(20)  NOT NULL COMMENT '医院区域id',
    hospital_code    varchar(20) null comment '医院编码',
    department_id    BIGINT(20)  NOT NULL COMMENT '科室id',
    department_admin TINYINT(1)  NOT NULL DEFAULT 0 COMMENT '是否是科室管理员',
    create_time      DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    update_time      DATETIME(3) NULL ON UPDATE CURRENT_TIMESTAMP(3),
    PRIMARY KEY (id),
    UNIQUE `idx_user_structure` (user_id, tenant_id, hospital_id, hospital_area_id, department_id)
) COMMENT = '租户用户组织架构表';

insert into t_tenant_user_structure (user_id, tenant_id, hospital_id, hospital_area_id, department_id, department_admin)
values (1, 0, 0, 0, 0, 1),
       (2, 1, 0, 0, 0, 1),
       (3, 2, 0, 0, 0, 1);