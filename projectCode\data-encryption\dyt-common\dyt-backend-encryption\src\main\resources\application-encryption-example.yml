# DYT Backend Encryption 配置示例
# 将此配置复制到你的 application.yml 中

backend:
  encryption:
    # 是否启用加密功能
    enabled: true
    
    # 加密密钥（生产环境请使用环境变量或密钥管理系统）
    secret-key: "DYT-Backend-Encryption-Key-2024-Production"
    
    # 默认加密算法
    algorithm: AES_GCM
    
    # 全局迁移策略（可选，会覆盖注解中的策略）
    # global-strategy: DIRECT_ENCRYPT
    
    # 是否启用调试日志
    debug-enabled: false
    
    # 是否启用性能监控
    performance-monitor-enabled: true
    
    # 字段级别的策略配置
    strategies:
      # 用户邮箱加密策略
      user-email:
        strategy: SHADOW_PRIORITY
        shadow-field: "email_encrypted"
        algorithm: AES_GCM
        enabled: true
        description: "用户邮箱渐进式迁移"
        version: 1
      
      # 用户地址加密策略
      user-address:
        strategy: DIRECT_ENCRYPT
        algorithm: AES_GCM
        enabled: true
        description: "用户地址直接加密"
        version: 1
      
      # 用户备注加密策略（默认禁用）
      user-remark:
        strategy: DIRECT_ENCRYPT
        algorithm: AES_GCM
        enabled: false
        description: "用户备注信息加密"
        version: 1
      
      # 敏感业务数据策略
      sensitive-business-data:
        strategy: SHADOW_ONLY
        shadow-field: "data_encrypted"
        algorithm: SM2
        enabled: true
        description: "敏感业务数据使用国密算法"
        version: 1
    
    # 国密SM2算法配置
    sm2:
      enabled: true
      # 生产环境请使用真实的密钥对
      public-key: "04F6E0C3345AE42B51E06BF50B98834988DCFD30022722A88CDA3F104F8F54C83AB29E83606E4D52B70104DCBCBCE2C770F2F8A2B16971D2A8B8C6B47FA2C8A4"
      private-key: "59276E27D506861A16680F3AD9C02DCCEF3CC1FA3CDBE4CE6D54B80DEAC1BC21"
    
    # 国密SM4算法配置（待实现）
    sm4:
      enabled: false
      secret-key: "0123456789ABCDEFFEDCBA9876543210"

# 数据库配置示例
spring:
  datasource:
    url: *********************************************************************************************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true

# 日志配置
logging:
  level:
    backend.encryption: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
