package backend.common.densensitized.config;

import backend.common.densensitized.service.AbstractDesensitizedPermissionService;
import backend.common.densensitized.service.DefaultDesensitizedPermissionService;
import backend.common.densensitized.service.DelegatingDesensitizedPermissionService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class PrivacyDesensitizeConfig {

    @Bean
    @ConditionalOnMissingBean
    public AbstractDesensitizedPermissionService abstractDesensitizedPermissionService() {
        return new DefaultDesensitizedPermissionService();
    }
    @Bean
    @ConditionalOnMissingBean(AbstractDesensitizedPermissionService.class)
    public AbstractDesensitizedPermissionService defaultDesensitizedPermissionService() {
        return new DefaultDesensitizedPermissionService();
    }

    @Bean
    public DelegatingDesensitizedPermissionService delegatingDesensitizedPermissionService(
            AbstractDesensitizedPermissionService desensitizedPermissionService) {
        return new DelegatingDesensitizedPermissionService(desensitizedPermissionService);
    }
}
