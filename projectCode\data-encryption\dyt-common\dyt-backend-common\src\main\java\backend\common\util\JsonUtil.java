package backend.common.util;

import backend.common.exception.BizException;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import org.springframework.http.HttpStatus;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-28 11:10
 */
public class JsonUtil {
    private JsonUtil() {
    }

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    static {
        OBJECT_MAPPER.setDefaultPropertyInclusion(JsonInclude.Include.NON_NULL);
    }

    public static String serializeObject(Object object) {
        try {
            return OBJECT_MAPPER.writeValueAsString(object);
        } catch (Exception e) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "Json序列化错误");
        }
    }

    public static <T> T deserializeObject(String json, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (Exception e) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "Json序列化错误");
        }
    }

    public static <T> T deserializeObject(String json, TypeReference<T> typeReference) {
        try {
            return OBJECT_MAPPER.readValue(json, typeReference);
        } catch (Exception e) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "Json序列化错误");
        }
    }

    public static <T> List<T> deserializeList(String json, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(json, TypeFactory.defaultInstance().constructCollectionType(List.class, clazz));
        } catch (Exception e) {
            throw new BizException(HttpStatus.INTERNAL_SERVER_ERROR, "Json序列化错误");
        }
    }
}
