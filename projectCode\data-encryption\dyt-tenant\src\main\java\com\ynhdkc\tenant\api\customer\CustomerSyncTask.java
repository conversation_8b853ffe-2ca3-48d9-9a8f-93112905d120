package com.ynhdkc.tenant.api.customer;

import com.ynhdkc.tenant.handler.CustomerSyncTaskApi;
import com.ynhdkc.tenant.service.customer.SyncDoctorService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "CustomerSyncTask")
@RestController
@RequiredArgsConstructor
public class CustomerSyncTask implements CustomerSyncTaskApi {

    private final SyncDoctorService syncDoctorService;

    @Override
    public ResponseEntity<String> syncKunHuaData(String departmentCode) {
        syncDoctorService.syncKunHuaDoctorBy(departmentCode);
        return ResponseEntity.ok().build();
    }
}
