package com.ynhdkc.tenant.dao.mapper;

import backend.common.util.MybatisUtil;
import com.ynhdkc.tenant.entity.Building;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/2/24 09:29:25
 */
@Mapper
public interface BuildingMapper extends BaseMapper<Building> {
    default void selectByCondition(Long hospitalId, String name, Integer status) {
        selectByExample(Building.class, helper -> {
            helper.defGroup(condition -> {
                if (hospitalId != null && hospitalId > 0) {
                    condition.andEqualTo(Building::getHospitalId, hospitalId);
                }
                if (StringUtils.hasText(name)) {
                    condition.andLike(Building::getName, MybatisUtil.likeBoth(name));
                }
                if (status != null && status >= 0) {
                    condition.andEqualTo(Building::getStatus, status);
                }
            });
            helper.builder(builder -> builder.orderByDesc(Building::getCreateTime));
        });
    }
}
