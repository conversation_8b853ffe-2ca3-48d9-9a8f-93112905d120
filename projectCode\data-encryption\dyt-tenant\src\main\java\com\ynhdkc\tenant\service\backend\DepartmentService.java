package com.ynhdkc.tenant.service.backend;

import backend.common.entity.dto.hisgateway.response.DepartmentListItem;
import backend.common.response.BaseOperationResponse;
import com.ynhdkc.tenant.client.model.DepartmentList4MultiLevelResponse;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.model.*;
import org.springframework.data.util.Pair;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-04-27 9:40
 */
public interface DepartmentService {
    DepartmentDetailVo create(DepartmentCreateReqDto request);

    DepartmentDetailVo update(Long departmentId, DepartmentUpdateReqDto request);

    Pair<BaseOperationResponse, Long> delete(Long departmentId);

    DepartmentDetailVo getDetail(Long departmentId);

    DepartmentDetailVo rpcGetDetail(Long departmentId);

    DepartmentPageVo query(DepartmentQueryReqDto request);

    TableDepartmentQueryVo queryByTable(TableDepartmentQueryReqDto request);

    TreeDepartmentQueryVo queryByTree(TreeDepartmentQueryReqDto request);

    DepartmentsTreeSearchListVo queryTree(DepartmentsTreeSearchReqDto request);

    DepartmentsTreeSearchListVo queryTree2(DepartmentsTreeSearchReqDto request);

    @Nullable
    Department getById(Long id);

    List<DepartmentVo> rpcBatchGetDetail(RpcBatchGetByIdReqDto request);

    @Nullable
    void saveDepartment(String hospitalCode, List<DepartmentListItem> departmentListItems);

    DepartmentVo rpcGetDetailByCode(String hospitalAreaCode, String departmentCode);

    void createOrUpdateGongRenHospitalDepartmentInfo(List<DepartmentList4MultiLevelResponse.Result> result);

    Map<Long, Integer> queryGongRenDepartmentTreeLevel();

    DepartmentsTreeSearchPageVo queryPage(DepartmentsTreeSearchPageReqDto request);

    AppointNotifyConfigVo getAppointNotifyConfig(String hospitalCode, Long departmentId);

    SetAppointNotifyConfigResponse setAppointNotifyConfig(String hospitalCode, Long departmentId, SetAppointNotifyConfigReqDto reqDto);

}
