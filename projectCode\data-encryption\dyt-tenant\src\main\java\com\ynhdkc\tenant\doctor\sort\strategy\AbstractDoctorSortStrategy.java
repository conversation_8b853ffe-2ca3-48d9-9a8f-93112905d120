package com.ynhdkc.tenant.doctor.sort.strategy;

import com.ynhdkc.tenant.model.CustomerGroupedDoctorDetailVo;

import java.util.*;

public abstract class AbstractDoctorSortStrategy implements DoctorSortStrategy {

    protected final Set<String> supportedHospitalCodes;

    protected AbstractDoctorSortStrategy(Set<String> hospitalCodes) {
        this.supportedHospitalCodes = Collections.unmodifiableSet(new HashSet<>(hospitalCodes));
    }

    @Override
    public boolean supports(String hospitalCode) {
        return supportedHospitalCodes.contains(hospitalCode);
    }

    @Override
    public void sortGroupVo(List<CustomerGroupedDoctorDetailVo> doctorGroupVos) {
        doctorGroupVos.sort(Comparator.comparing((CustomerGroupedDoctorDetailVo vo) -> vo.getSort() == null ? 0 : vo.getSort()).reversed());
    }
}
