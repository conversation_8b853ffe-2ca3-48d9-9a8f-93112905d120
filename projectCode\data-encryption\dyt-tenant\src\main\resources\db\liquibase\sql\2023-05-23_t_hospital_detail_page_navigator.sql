CREATE TABLE t_hospital_detail_page_navigator
(
    id               BIGINT       NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    tenant_id        BIGINT       NOT NULL DEFAULT 0 COMMENT '租户id',
    hospital_id      BIGINT       NOT NULL DEFAULT 0 COMMENT '医院id',
    hospital_area_id bigint       not null comment '院区id',
    hospital_code    varchar(20)  null comment '医院编码',
    navi_module_id   BIGINT       NOT NULL DEFAULT 0 COMMENT '副标题模块ID，主标题默认为0',
    type             INT          NOT NULL DEFAULT 0 COMMENT '导航栏类型，0:主图文导航，1:副图文导航',
    title            VARCHAR(20)  NOT NULL DEFAULT '' COMMENT '标题',
    sub_title        VARCHAR(20)  NOT NULL DEFAULT '' COMMENT '副标题，副导航栏无效',
    picture          VARCHAR(300) NOT NULL DEFAULT '' COMMENT '导航图片 URL',
    url      TEXT         NOT NULL COMMENT '跳转链接',
    sort             INT          NOT NULL DEFAULT 0 COMMENT '排序',
    channels varchar(100) NOT NULL DEFAULT '' COMMENT '渠道',
    create_time      DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time      DATETIME     NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT '医院详情页导航表';

CREATE INDEX ind_type ON t_hospital_detail_page_navigator (type);
CREATE INDEX ind_hospital_id ON t_hospital_detail_page_navigator (hospital_id);
CREATE INDEX ind_navi_module_id ON t_hospital_detail_page_navigator (navi_module_id);
