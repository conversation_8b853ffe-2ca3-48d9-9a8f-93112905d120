package backend.common.domain.user;


import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "t_user_block_list")
public class UserBlockListEntity extends BaseEntity {
    private Long userId;
    private String reason;
    private String unblockReason;
    private Integer status;
}
