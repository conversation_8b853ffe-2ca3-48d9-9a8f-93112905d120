package com.ynhdkc.tenant.dao.kafka;

import backend.common.constants.HisOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

@Slf4j
@Repository
public class SyncDoctorKafkaRepository {

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;

    public void sendMessage(String message) {
        try {
            if (ObjectUtils.isEmpty(message)) {
                return;
            }
            kafkaTemplate.send(HisOperation.DOCTOR_DATA_SYNC.getRequestTopicName(), message);
        } catch (Exception e) {
            log.error("sendMessage_error:", e);
        }
    }

}
