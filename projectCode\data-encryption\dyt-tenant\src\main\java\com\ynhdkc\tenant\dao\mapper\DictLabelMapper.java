package com.ynhdkc.tenant.dao.mapper;

import backend.common.util.MybatisUtil;
import com.ynhdkc.tenant.entity.DictLabel;
import com.ynhdkc.tenant.entity.constant.SortConstants;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @since 2023/2/14 09:33:13
 */
@Mapper
public interface DictLabelMapper extends BaseMapper<DictLabel> {

    default long countByTypeAndLabel(String dictType, String dictLabel) {
        return selectCountByExample2(DictLabel.class, sql -> {
            sql.andEqualTo(DictLabel::getDictType, dictType);
            sql.andEqualTo(DictLabel::getDictLabel, dictLabel);
        });
    }

    default long create(DictLabel dictLabel) {
        insertSelective(dictLabel);
        return dictLabel.getId();
    }

    default long countByTypeAndLabelAndIdNot(Long id, String dictType, String dictLabel) {
        return selectCountByExample2(DictLabel.class, sql -> {
            sql.andEqualTo(DictLabel::getDictType, dictType);
            sql.andEqualTo(DictLabel::getDictLabel, dictLabel);
            sql.andNotEqualTo(DictLabel::getId, id);
        });
    }

    default void selectByConditions(String name, String dictTypeName, String description, String sortBy) {
        selectByExample(DictLabel.class, helper -> {
            helper.defGroup(condition -> {
                if (StringUtils.hasText(name)) {
                    condition.andLike(DictLabel::getDictLabel, MybatisUtil.likeBoth(name));
                }
                if (StringUtils.hasText(dictTypeName)) {
                    condition.andLike(DictLabel::getDictType, MybatisUtil.likeBoth(dictTypeName));
                }
                if (StringUtils.hasText(description)) {
                    condition.andLike(DictLabel::getDescription, MybatisUtil.likeBoth(description));
                }
            });

            helper.builder(builder -> {
                if (StringUtils.hasText(sortBy) && sortBy.equals(SortConstants.ASC)) {
                    builder.orderByAsc(DictLabel::getSort, DictLabel::getCreateTime);
                } else {
                    builder.orderByDesc(DictLabel::getSort, DictLabel::getCreateTime);
                }
            });
        });

    }

    default List<DictLabel> selectByDictTypesAndDictLabels(List<String> dictTypes, List<String> dictLabels) {
        Consumer<WeekendSqls<DictLabel>> consumer = dictLabel -> {
            if (!CollectionUtils.isEmpty(dictTypes)) {
                dictLabel.andIn(DictLabel::getDictType, dictTypes);
            }
            if (!CollectionUtils.isEmpty(dictLabels)) {
                dictLabel.andIn(DictLabel::getDictLabel, dictLabels);
            }
        };
        return selectByExample2(DictLabel.class, consumer);
    }

    /**
     * 获取号满推荐列表
     *
     * @param label       字典标签
     * @param dictType    字典类型
     * @param status      状态  对应 redirectPath
     * @param description 描述
     */
    default void selectSourceRecommendList(String label, String dictType, Integer status, String description) {
        selectByExample(DictLabel.class, helper -> {
            helper.defGroup(condition -> {
                if (StringUtils.hasText(label)) {
                    condition.andLike(DictLabel::getDictLabel, MybatisUtil.likeBoth(label));
                }
                if (StringUtils.hasText(dictType)) {
                    condition.andEqualTo(DictLabel::getDictType, dictType);
                }
                if (StringUtils.hasText(description)) {
                    condition.andEqualTo(DictLabel::getDescription, description);
                }
                if (status != null) {
                    condition.andEqualTo(DictLabel::getRedirectPath, status.toString());
                }
            });

            helper.builder(builder -> {
                builder.orderByDesc(DictLabel::getSort, DictLabel::getCreateTime);
            });
        });

    }


}
