package backend.common.domain.user;


import backend.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "t_user_old_wechat_profile")
public class UserOldWechatProfileEntity extends BaseEntity {
    private Long userId;
    private String openId;
    private String nickName;
    private String headImgUrl;
    private Integer sex;
    private String province;
    private String city;
    private String county;
    private String unionid;
    private Boolean subscribe;
    private Date subscribeTime;
    private String subscribeSceneCategory;
}
