package backend.common.util;

import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

public class StringUtils {

    private StringUtils() {}

    public static <T> String join(List<T> records, String symbol) {
        if(ObjectUtils.isEmpty(records)) {
            return "";
        }
        StringBuilder buffer = new StringBuilder();
        for (int i = 0; i < records.size(); i++) {
            String record = String.valueOf(records.get(i));
            if (i == 0) {
                buffer.append(record);
            } else {
                buffer.append(symbol).append(record);
            }
        }
        return buffer.toString();
    }

    public static String getUUID(int length) {
        String s = UUID.randomUUID().toString().replace("-", "");
        return s.substring(0, length);
    }

    public static String getUUID32() {
        return getUUID(32);
    }

    public static String getTimestamp() {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
        return format.format(new Date());
    }

    public static boolean isNumeric(String str) {
        return str.matches("-?\\d+(\\.\\d+)?");  //match a number with optional '-' and decimal.
    }
}
