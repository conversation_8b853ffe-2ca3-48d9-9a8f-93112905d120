package com.ynhdkc.tenant.dao.impl;

import backend.common.util.MybatisUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.ynhdkc.tenant.dao.BuildingQuery;
import com.ynhdkc.tenant.dao.mapper.BuildingMapper;
import com.ynhdkc.tenant.entity.Building;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-04 11:17
 */
@Repository
@RequiredArgsConstructor
public class BuildingQueryImpl implements BuildingQuery {
    private final BuildingMapper buildingMapper;

    @Override
    public Building queryBuildingById(Long buildingId) {
        return buildingMapper.selectByPrimaryKey(buildingId);
    }

    @Override
    public Page<Building> pageQuery(BuildingQueryOption option) {
        try (final Page<Building> page = PageMethod.startPage(option.getCurrentPage(), option.getPageSize())) {
            return page.doSelectPage(() -> buildingMapper.selectByExample(Building.class, sql -> {
                sql.defGroup(condition -> {
                    if (null != option.getTenantId()) {
                        condition.andEqualTo(Building::getTenantId, option.getTenantId());
                    }
                    if (null != option.getHospitalId()) {
                        condition.andEqualTo(Building::getHospitalId, option.getHospitalId());
                    }
                    if (null != option.getHospitalAreaId()) {
                        condition.andEqualTo(Building::getHospitalAreaId, option.getHospitalAreaId());
                    }
                    if (null != option.getName()) {
                        condition.andLike(Building::getName, MybatisUtil.likeBoth(option.getName()));
                    }
                    if (null != option.getStatus()) {
                        condition.andEqualTo(Building::getStatus, option.getStatus());
                    }

                    if (!CollectionUtils.isEmpty(option.getIncludeTenantIds())) {
                        condition.andIn(Building::getTenantId, option.getIncludeTenantIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getExcludeTenantIds())) {
                        condition.andNotIn(Building::getTenantId, option.getExcludeTenantIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getIncludeHospitalIds())) {
                        condition.andIn(Building::getHospitalId, option.getIncludeHospitalIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getExcludeHospitalIds())) {
                        condition.andNotIn(Building::getHospitalId, option.getExcludeHospitalIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getIncludeHospitalAreaIds())) {
                        condition.andIn(Building::getHospitalAreaId, option.getIncludeHospitalAreaIds());
                    }
                    if (!CollectionUtils.isEmpty(option.getExcludeHospitalAreaIds())) {
                        condition.andNotIn(Building::getHospitalAreaId, option.getExcludeHospitalAreaIds());
                    }
                });
                sql.builder(builder -> builder
                        .orderByDesc(Building::getSort)
                        .orderByDesc(Building::getId));
            }));
        }
    }

    @Override
    public List<Building> queryBuildingsByHospitalAreaId(Long hospitalAreaId) {
        return buildingMapper.selectByExample(Building.class, sql -> {
            sql.defGroup(condition -> condition.andEqualTo(Building::getHospitalAreaId, hospitalAreaId));
            sql.builder(builder -> builder
                    .orderByDesc(Building::getSort)
                    .orderByDesc(Building::getId));
        });
    }
}
