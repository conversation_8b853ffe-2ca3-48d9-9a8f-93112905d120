package com.example.encryption;

import com.example.encryption.entity.User;
import com.example.encryption.service.UserService;
import com.example.encryption.util.AESGCMUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据库加密功能测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class EncryptionTest {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private EntityManager entityManager;
    
    /**
     * 测试AES-GCM加密工具类
     */
    @Test
    public void testAESGCMUtil() {
        String originalText = "13812345678";
        
        // 测试加密
        String encrypted = AESGCMUtil.encrypt(originalText);
        assertNotNull(encrypted);
        assertNotEquals(originalText, encrypted);
        
        // 测试解密
        String decrypted = AESGCMUtil.decrypt(encrypted);
        assertEquals(originalText, decrypted);
        
        // 测试空值处理
        assertNull(AESGCMUtil.encrypt(null));
        assertEquals("", AESGCMUtil.encrypt(""));
        
        System.out.println("原文: " + originalText);
        System.out.println("密文: " + encrypted);
        System.out.println("解密: " + decrypted);
    }
    
    /**
     * 测试用户创建和查询（验证加密功能）
     */
    @Test
    public void testUserEncryption() {
        // 创建测试用户
        User user = new User();
        user.setUsername("testuser");
        user.setPhone("13800138000");
        user.setEmail("<EMAIL>");
        user.setIdCard("110101199001011111");
        user.setRealName("测试用户");
        user.setAge(25);
        
        // 保存用户
        User savedUser = userService.createUser(user);
        assertNotNull(savedUser.getId());
        
        // 验证敏感字段在应用层是明文
        assertEquals("13800138000", savedUser.getPhone());
        assertEquals("<EMAIL>", savedUser.getEmail());
        assertEquals("110101199001011111", savedUser.getIdCard());
        assertEquals("测试用户", savedUser.getRealName());
        
        // 直接查询数据库，验证敏感字段在数据库中是密文
        Query query = entityManager.createNativeQuery(
            "SELECT phone, email, id_card, real_name FROM users WHERE id = ?");
        query.setParameter(1, savedUser.getId());
        Object[] result = (Object[]) query.getSingleResult();
        
        String dbPhone = (String) result[0];
        String dbEmail = (String) result[1];
        String dbIdCard = (String) result[2];
        String dbRealName = (String) result[3];
        
        // 验证数据库中存储的是密文
        assertNotEquals("13800138000", dbPhone);
        assertNotEquals("<EMAIL>", dbEmail);
        assertNotEquals("110101199001011111", dbIdCard);
        assertNotEquals("测试用户", dbRealName);
        
        // 验证可以正确解密
        assertEquals("13800138000", AESGCMUtil.decrypt(dbPhone));
        assertEquals("<EMAIL>", AESGCMUtil.decrypt(dbEmail));
        assertEquals("110101199001011111", AESGCMUtil.decrypt(dbIdCard));
        assertEquals("测试用户", AESGCMUtil.decrypt(dbRealName));
        
        System.out.println("=== 数据库加密验证 ===");
        System.out.println("应用层手机号: " + savedUser.getPhone());
        System.out.println("数据库手机号: " + dbPhone);
        System.out.println("应用层邮箱: " + savedUser.getEmail());
        System.out.println("数据库邮箱: " + dbEmail);
    }
    
    /**
     * 测试根据加密字段查询
     */
    @Test
    public void testQueryByEncryptedField() {
        // 创建测试用户
        User user = new User();
        user.setUsername("querytest");
        user.setPhone("13900139000");
        user.setEmail("<EMAIL>");
        user.setIdCard("110101199002022222");
        user.setRealName("查询测试");
        user.setAge(30);
        
        userService.createUser(user);
        
        // 测试根据手机号查询
        Optional<User> foundByPhone = userService.getUserByPhone("13900139000");
        assertTrue(foundByPhone.isPresent());
        assertEquals("querytest", foundByPhone.get().getUsername());
        
        // 测试根据邮箱查询
        Optional<User> foundByEmail = userService.getUserByEmail("<EMAIL>");
        assertTrue(foundByEmail.isPresent());
        assertEquals("querytest", foundByEmail.get().getUsername());
        
        // 测试查询不存在的手机号
        Optional<User> notFound = userService.getUserByPhone("99999999999");
        assertFalse(notFound.isPresent());
    }
    
    /**
     * 测试用户更新（验证加密字段更新）
     */
    @Test
    public void testUserUpdate() {
        // 创建测试用户
        User user = new User();
        user.setUsername("updatetest");
        user.setPhone("13700137000");
        user.setEmail("<EMAIL>");
        user.setIdCard("110101199003033333");
        user.setRealName("更新测试");
        user.setAge(28);
        
        User savedUser = userService.createUser(user);
        
        // 更新敏感字段
        savedUser.setPhone("13800138888");
        savedUser.setEmail("<EMAIL>");
        savedUser.setRealName("已更新测试");
        
        User updatedUser = userService.updateUser(savedUser);
        
        // 验证更新后的值
        assertEquals("13800138888", updatedUser.getPhone());
        assertEquals("<EMAIL>", updatedUser.getEmail());
        assertEquals("已更新测试", updatedUser.getRealName());
        
        // 重新查询验证
        Optional<User> reloadedUser = userService.getUserById(savedUser.getId());
        assertTrue(reloadedUser.isPresent());
        assertEquals("13800138888", reloadedUser.get().getPhone());
        assertEquals("<EMAIL>", reloadedUser.get().getEmail());
        assertEquals("已更新测试", reloadedUser.get().getRealName());
    }
    
    /**
     * 测试批量查询（验证性能）
     */
    @Test
    public void testBatchQuery() {
        // 创建多个测试用户
        for (int i = 1; i <= 5; i++) {
            User user = new User();
            user.setUsername("batchtest" + i);
            user.setPhone("1380013800" + i);
            user.setEmail("batch" + i + "@example.com");
            user.setIdCard("11010119900101000" + i);
            user.setRealName("批量测试" + i);
            user.setAge(20 + i);
            
            userService.createUser(user);
        }
        
        // 查询所有用户
        List<User> allUsers = userService.getAllUsers();
        assertTrue(allUsers.size() >= 5);
        
        // 验证所有用户的敏感字段都能正确解密
        for (User user : allUsers) {
            if (user.getUsername().startsWith("batchtest")) {
                assertNotNull(user.getPhone());
                assertNotNull(user.getEmail());
                assertNotNull(user.getRealName());
                assertTrue(user.getPhone().startsWith("138"));
                assertTrue(user.getEmail().contains("@example.com"));
                assertTrue(user.getRealName().contains("批量测试"));
            }
        }
    }
}
