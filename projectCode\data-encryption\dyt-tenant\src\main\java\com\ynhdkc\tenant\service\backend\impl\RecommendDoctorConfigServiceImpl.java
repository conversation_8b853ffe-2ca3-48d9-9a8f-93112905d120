package com.ynhdkc.tenant.service.backend.impl;


import backend.common.exception.BizException;
import com.github.pagehelper.PageInfo;
import com.ynhdkc.tenant.dao.DepartmentQuery;
import com.ynhdkc.tenant.dao.DoctorQuery;
import com.ynhdkc.tenant.dao.impl.RecommendDoctorConfigRepository;
import com.ynhdkc.tenant.dao.mapper.RecommendConfigMapper;
import com.ynhdkc.tenant.entity.Department;
import com.ynhdkc.tenant.entity.Doctor;
import com.ynhdkc.tenant.entity.RecommendConfig;
import com.ynhdkc.tenant.entity.RecommendDoctorConfig;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.backend.RecommendDoctorConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class RecommendDoctorConfigServiceImpl implements RecommendDoctorConfigService {

    private final RecommendDoctorConfigRepository recommendDoctorConfigRepository;
    private final DoctorQuery doctorQuery;
    private final DepartmentQuery departmentQuery;
    private final RecommendConfigMapper recommendConfigMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RecommendDoctorConfigVo addRecommendDoctorConfig(List<RecommendDoctorConfigCreateDto> recommendDoctorConfigCreateDtoList) {

        List<RecommendDoctorConfig> recommendDoctorConfigs = recommendDoctorConfigRepository.queryListByRecommendId(recommendDoctorConfigCreateDtoList.get(0).getRecommendId());
        if (!CollectionUtils.isEmpty(recommendDoctorConfigs) && !(CollectionUtils.isEmpty(recommendDoctorConfigCreateDtoList))) {
            recommendDoctorConfigCreateDtoList.removeIf(dto1 -> recommendDoctorConfigs.stream().anyMatch(recommendDoctorConfig -> recommendDoctorConfig.getDoctorId().equals(dto1.getDoctorId()) && recommendDoctorConfig.getDataTag().equals(dto1.getDataTag())));
        }

        if (!CollectionUtils.isEmpty(recommendDoctorConfigCreateDtoList)) {
            recommendDoctorConfigCreateDtoList.forEach(recommendDoctorConfigRepository::insert);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRecommendDoctorConfig(Long id) {
        recommendDoctorConfigRepository.deleteById(id);
    }

    @Override
    public RecommendDoctorConfigVo getRecommendDoctorConfigById(Long id) {
        RecommendDoctorConfig entity = recommendDoctorConfigRepository.getById(id);
        if (entity == null) {
            throw new BizException("找不到推荐医生配置");
        }
        return convertToVo(entity);
    }

    @Override
    public RecommendDoctorConfigPageVo getRecommendDoctorConfigsPage(RecommendDoctorConfigQueryDto queryDto) {
        PageInfo<RecommendDoctorConfig> entities = recommendDoctorConfigRepository.getPage(queryDto);

        List<RecommendDoctorConfigVo> vos = entities.getList().stream()
                .map(this::convertToVo)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(vos)) {
            fillDoctorInfo(vos);
        }


        RecommendDoctorConfigPageVo pageVo = new RecommendDoctorConfigPageVo();
        pageVo.setTotalSize(entities.getTotal());
        pageVo.setCurrentPage(queryDto.getPageNum());
        pageVo.setPageSize(queryDto.getPageSize());
        pageVo.setList(vos);

        if (queryDto.getDoctorCode() != null) {
            vos = vos.stream().filter(vo -> vo.getDoctorCode().equals(queryDto.getDoctorCode())).collect(Collectors.toList());
            pageVo.setTotalSize(Long.parseLong(String.valueOf(vos.size())));
            pageVo.setList(vos);
        }


        if (queryDto.getDoctorName() != null) {
            vos = vos.stream().filter(vo -> vo.getDoctorName().equals(queryDto.getDoctorName())).collect(Collectors.toList());
            pageVo.setTotalSize(Long.parseLong(String.valueOf(vos.size())));
            pageVo.setList(vos);
        }
        return pageVo;
    }


    public void fillDoctorInfo(List<RecommendDoctorConfigVo> vos) {

        List<Long> doctorIds = vos.stream().map(RecommendDoctorConfigVo::getDoctorId).collect(Collectors.toList());
        List<Doctor> doctors = doctorQuery.queryBy(doctorIds);

        List<Long> departmentIds = vos.stream().map(RecommendDoctorConfigVo::getDepartmentId).collect(Collectors.toList());
        List<Department> departments = departmentQuery.queryBy(departmentIds);

        List<Long> recommendIds = vos.stream().map(RecommendDoctorConfigVo::getRecommendId).collect(Collectors.toList());
        List<RecommendConfig> recommendConfigs = recommendConfigMapper.selectByExample2(RecommendConfig.class, query -> query.andIn(RecommendConfig::getId, recommendIds));

        vos.forEach(vo -> {
            Doctor doctor = doctors.stream().filter(doctor1 -> doctor1.getId().equals(vo.getDoctorId())).findFirst().orElse(null);
            if (doctor != null) {
                vo.setDoctorName(doctor.getName());
                vo.setDoctorCode(doctor.getThrdpartDoctorCode());
                vo.setDataName(doctor.getName());
            }
            Department department = departments.stream().filter(department1 -> department1.getId().equals(vo.getDepartmentId())).findFirst().orElse(null);
            if (department != null) {
                vo.setDepartmentName(department.getName());
                vo.setDepartmentCode(department.getThrdpartDepCode());
            }
            RecommendConfig recommendConfig = recommendConfigs.stream().filter(recommendConfig1 -> recommendConfig1.getId().equals(vo.getRecommendId())).findFirst().orElse(null);
            if (recommendConfig != null) {
                vo.setDataId(recommendConfig.getDataId());
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRecommendDoctorConfig(Long id, RecommendDoctorConfigUpdateDto updateDto) {
        recommendDoctorConfigRepository.updateById(id, updateDto);
    }

    private RecommendDoctorConfigVo convertToVo(RecommendDoctorConfig entity) {
        RecommendDoctorConfigVo vo = new RecommendDoctorConfigVo();
        vo.setId(entity.getId());
        vo.setRecommendId(entity.getRecommendId());
        vo.setDoctorId(entity.getDoctorId());
        vo.setDepartmentId(entity.getDepartmentId());
        vo.setHospitalAreaId(entity.getHospitalAreaId());
        vo.setHospitalAreaCode(entity.getHospitalAreaCode());
        vo.setHospitalAreaName(entity.getHospitalAreaName());
        vo.setHospitalId(entity.getHospitalId());
        vo.setSort(entity.getSort());
        vo.setHospitalName(entity.getHospitalName());
        vo.setDataTag(entity.getDataTag());
        vo.setEnabled(entity.getEnabled());
        return vo;
    }
}
