package com.ynhdkc.tenant.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
public class UserWeChatProfileVo {
    @JsonProperty("id")
    private Long id = null;

    @JsonProperty("open_id")
    private String openId = null;

    @JsonProperty("nick_name")
    private String nickName = null;

    @JsonProperty("head_img_url")
    private String headImgUrl = null;

    @JsonProperty("sex")
    private Integer sex = null;

    @JsonProperty("province")
    private String province = null;

    @JsonProperty("city")
    private String city = null;

    @JsonProperty("county")
    private String county = null;

    @JsonProperty("unionid")
    private String unionid = null;

    @JsonProperty("mini_openid")
    private String miniOpenid = null;

    @JsonProperty("subscribe")
    private Boolean subscribe = null;

    @JsonProperty("subscribe_time")
    private Date subscribeTime = null;

    @JsonProperty("subscribe_scene_category")
    private String subscribeSceneCategory = null;

    @JsonProperty("scene")
    private String scene = null;

    @JsonProperty("dzj_subscribe")
    private Boolean dzjSubscribe = null;

    @JsonProperty("dzj_open_id")
    private String dzjOpenId = null;

    @JsonProperty("dzj_subscribe_time")
    private Date dzjSubscribeTime = null;
}
