package com.ynhdkc.tenant.util;

import com.ynhdkc.tenant.DytTenantApplication;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.io.File;

@SpringBootTest(classes = {DytTenantApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("dev")
class UploadFileUtilTest {

    @Autowired
    private UploadFileUtil uploadFileUtil;

    @SneakyThrows
    @Test
    void getFile() {
        File file = uploadFileUtil.getFile("https://hlwyy.ydyy.cn:10053/docHeadImg/20231218160851_预防保健科 朱燕妮.jpg");
        System.out.println(file.getName());
    }
}