package com.ynhdkc.tenant.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class DepartmentDoctorListEnvRequest {

    @JsonProperty("department_id")
    private Long departmentId;

    @JsonProperty("doctor_id")
    private Long doctorId;

    @JsonProperty("hospital_area_id")
    private Long hospitalAreaId;

    @JsonProperty("hospital_code")
    private String hospitalCode;

    @JsonProperty("hospital_id")
    private Long hospitalId;

    @JsonProperty("pay_load")
    private PayLoad payLoad;

    @Data
    public static class PayLoad {

        @JsonProperty("hospital_code")
        private String hospitalCode;

        @JsonProperty("department_code")
        private String departmentCode;

        @JsonProperty("department_id")
        private Long departmentId;

        @JsonProperty("start_date")
        private Long startDate;

        @JsonProperty("end_date")
        private Long endDate;

        @JsonProperty("time_type")
        private Integer timeType;

        @JsonProperty("doctors")
        private String doctors;

    }
}
