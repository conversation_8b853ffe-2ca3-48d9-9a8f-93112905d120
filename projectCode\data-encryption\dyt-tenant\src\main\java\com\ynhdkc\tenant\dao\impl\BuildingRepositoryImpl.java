package com.ynhdkc.tenant.dao.impl;

import com.ynhdkc.tenant.dao.BuildingRepository;
import com.ynhdkc.tenant.dao.mapper.AreaMapper;
import com.ynhdkc.tenant.dao.mapper.BuildingMapper;
import com.ynhdkc.tenant.dao.mapper.FloorMapper;
import com.ynhdkc.tenant.entity.Area;
import com.ynhdkc.tenant.entity.Building;
import com.ynhdkc.tenant.entity.Floor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-05-04 11:06
 */
@Repository
@RequiredArgsConstructor
public class BuildingRepositoryImpl implements BuildingRepository {
    private final BuildingMapper buildingMapper;
    private final FloorMapper floorMapper;
    private final AreaMapper areaMapper;

    @Override
    public void create(Building building) {
        buildingMapper.insertSelective(building);
    }

    @Override
    public void update(Building building) {
        buildingMapper.updateByPrimaryKeySelective(building);
    }

    @Override
    public void delete(Long buildingId) {
        buildingMapper.deleteByExample2(Building.class, sql -> sql.andEqualTo(Building::getId, buildingId));

        floorMapper.deleteByExample2(Floor.class, sql -> sql.andEqualTo(Floor::getBuildingId, buildingId));
        areaMapper.deleteByExample2(Area.class, sql -> sql.andEqualTo(Area::getBuildingId, buildingId));
    }
}
