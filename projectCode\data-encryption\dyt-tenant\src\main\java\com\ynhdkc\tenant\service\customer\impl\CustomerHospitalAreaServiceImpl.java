package com.ynhdkc.tenant.service.customer.impl;

import backend.common.exception.BizException;
import com.github.pagehelper.Page;
import com.ynhdkc.tenant.dao.*;
import com.ynhdkc.tenant.dao.mapper.*;
import com.ynhdkc.tenant.entity.*;
import com.ynhdkc.tenant.entity.constant.NavigatorTypeEnum;
import com.ynhdkc.tenant.entity.detailpage.*;
import com.ynhdkc.tenant.entity.setting.*;
import com.ynhdkc.tenant.model.*;
import com.ynhdkc.tenant.service.AddressService;
import com.ynhdkc.tenant.service.customer.CustomerHospitalAreaService;
import com.ynhdkc.tenant.tool.HospitalAreaPositionTreeUtil;
import com.ynhdkc.tenant.tool.LayoutModelConverter;
import com.ynhdkc.tenant.tool.SetDictInfoHelper;
import com.ynhdkc.tenant.tool.convert.AppointmentSettingConverter;
import com.ynhdkc.tenant.tool.convert.HospitalAreaConverter;
import com.ynhdkc.tenant.tool.convert.PageVoConvert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/7/19 16:37
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerHospitalAreaServiceImpl implements CustomerHospitalAreaService {
    private static final Integer IS_ENABLE = 0;
    private final HospitalQuery hospitalQuery;
    private final HospitalAreaQuery hospitalAreaQuery;
    private final HospitalAreaSettingQuery hospitalAreaSettingQuery;
    private final BuildingQuery buildingQuery;
    private final FloorQuery floorQuery;
    private final AreaQuery areaQuery;
    private final HospitalAreaPositionQuery hospitalAreaPositionQuery;
    private final IHospitalDetailPageConfigMapper hospitalDetailPageConfigMapper;
    private final IHospitalDetailPageNavigatorMapper hospitalDetailPageNavigatorMapper;
    private final IHospitalDetailPageSubNaviModuleMapper hospitalDetailPageSubNaviModuleMapper;
    private final HospitalDetailPageCubeModuleMapper hospitalDetailPageCubeModuleMapper;
    private final HospitalDetailPageCubeMapper hospitalDetailPageCubeMapper;
    private final IHospitalDetailPageTabMapper hospitalDetailPageTabMapper;
    private final AddressService addressService;
    private final PageVoConvert pageVoConvert;
    private final HospitalAreaPositionTreeUtil hospitalAreaPositionTreeUtil;
    private final SetDictInfoHelper setDictInfoHelper;

    @Override
    public CustomerHospitalAreaDetailVo getDetail(Long hospitalAreaId, String hospitalAreaCode) {
        Hospital hospitalArea;
        if (null == hospitalAreaId) {
            hospitalArea = hospitalAreaQuery.queryHospitalAreaBy(hospitalAreaCode);
        } else {
            hospitalArea = hospitalAreaQuery.queryHospitalAreaById(hospitalAreaId);
        }
        if (null == hospitalArea) {
            throw new BizException(HttpStatus.NOT_FOUND, "院区不存在");
        }
        CustomerHospitalAreaDetailVo customerHospitalAreaDetailVo = HospitalAreaConverter.toCustomerHospitalAreaDetailVo(hospitalArea);

        if (hospitalAreaId != null) {
            Optional<AppointmentRuleSetting> appointmentRuleSetting = hospitalAreaSettingQuery.queryAppointmentRuleSettingBy(hospitalAreaId);
            appointmentRuleSetting.ifPresent(setting -> customerHospitalAreaDetailVo.setCurrentSystemType(setting.getCurrentSystemType()));
        }
        return customerHospitalAreaDetailVo;
    }

    @Override
    public CustomerHospitalAreaDetailVo getDetailAndHospitalInfo(Long hospitalAreaId, String hospitalAreaCode) {
        CustomerHospitalAreaDetailVo detail = getDetail(hospitalAreaId, hospitalAreaCode);
        importHospitalInfo(detail);
        return detail;
    }

    @Override
    public CustomerHospitalAreaLayoutVo getLayout(Long hospitalAreaId, String hospitalAreaCode, Integer channel) {
        Hospital hospitalArea;
        if (null == hospitalAreaId) {
            hospitalArea = hospitalAreaQuery.queryHospitalAreaBy(hospitalAreaCode);
            hospitalAreaId = hospitalArea.getId();
        } else {
            hospitalArea = hospitalAreaQuery.queryHospitalAreaById(hospitalAreaId);
        }
        if (null == hospitalArea) {
            throw new BizException(HttpStatus.NOT_FOUND, "院区不存在");
        }

        CustomerHospitalAreaLayoutVo vo = new CustomerHospitalAreaLayoutVo();
        vo.setMapKeyword(vo.getMapKeyword());
        vo.setIntroduction(hospitalArea.getIntroduction());
        vo.setHospitalAreaId(hospitalAreaId);
        vo.setHospitalAreaCode(hospitalArea.getHospitalCode());
        setHospitalName(hospitalArea.getParentId(), vo);
        vo.setHospitalAreaName(hospitalArea.getName());
        vo.setHospitalAreaImage(hospitalArea.getPictures());
        setHospitalAreas(hospitalArea.getParentId(), vo);
        setHospitalAreaAddress(hospitalArea, vo);
        vo.setAppointmentScheduleTime(hospitalArea.getAppointmentSchedulingTime());

        Optional<AppointmentRuleSetting> appointmentRuleSettingOptional = hospitalAreaSettingQuery.queryAppointmentRuleSettingById(hospitalArea.getTenantId(), hospitalArea.getParentId(), hospitalAreaId);
        appointmentRuleSettingOptional.ifPresent(appointmentRuleSetting -> {
            vo.setAppointmentNotice(appointmentRuleSetting.getAppointmentNotice());
            vo.setAppointmentRule(appointmentRuleSetting.getAppointmentRule());
        });

        vo.setHospitalAreaPhone(hospitalArea.getContactPhoneNumber());
        vo.setHospitalAreaLevel(hospitalArea.getLevelDictValue());
        vo.setAnnouncement(hospitalArea.getAnnouncement());
        setHospitalAreaDetailPageConfig(vo, hospitalAreaId);
        setPageNavigator(vo, hospitalAreaId, channel);
        setSubNaviModule(vo, hospitalAreaId, channel);
        setCubeModule(vo, hospitalAreaId, channel);
        setTab(vo, hospitalAreaId, channel);

        Hospital hospital = hospitalQuery.queryHospitalById(hospitalArea.getParentId());
        setDictInfoHelper.setDictInfo(hospital, vo);
        return vo;
    }

    private void setTab(CustomerHospitalAreaLayoutVo vo, Long hospitalAreaId, Integer channel) {
        List<HospitalAreaDetailPageTab> hospitalAreaDetailPageTabs = hospitalDetailPageTabMapper.selectByExample2(HospitalAreaDetailPageTab.class, sql ->
                sql.andEqualTo(HospitalAreaDetailPageTab::getHospitalAreaId, hospitalAreaId)
                        .andEqualTo(HospitalAreaDetailPageTab::getStatus, 0)
        );
        if (CollectionUtils.isEmpty(hospitalAreaDetailPageTabs)) {
            return;
        }
        vo.setTabVo(hospitalAreaDetailPageTabs.stream()
                .filter(navi -> {
                    if (StringUtils.hasText(navi.getChannels())) {
                        String[] strings = navi.getChannels().split(",");
                        for (String string : strings) {
                            if (string.equals(channel.toString())) {
                                return true;
                            }
                        }
                    }
                    return false;
                })
                .map(LayoutModelConverter::toVo)
                .collect(Collectors.toList()));
    }

    @Override
    public CustomerHospitalAreaSettingDetailVo getSettingDetail(Long hospitalAreaId, String hospitalAreaCode) {
        Hospital hospitalArea;
        if (null != hospitalAreaId) {
            hospitalArea = hospitalAreaQuery.queryHospitalAreaById(hospitalAreaId);
        } else {
            hospitalArea = hospitalAreaQuery.queryHospitalAreaBy(hospitalAreaCode);
            hospitalAreaId = hospitalArea.getId();
        }

        if (null == hospitalArea) {
            throw new BizException(HttpStatus.NOT_FOUND, "院区不存在");
        }

        Optional<DiagnosisPaymentSetting> diagnosisPaymentSettingOptional = hospitalAreaSettingQuery.queryDiagnosisPaymentSettingById(null, null, hospitalAreaId);
        Optional<AppointmentRuleSetting> appointmentRuleSettingOptional = hospitalAreaSettingQuery.queryAppointmentRuleSettingById(null, null, hospitalAreaId);
        Optional<PatientReportSetting> patientReportSettingOptional = hospitalAreaSettingQuery.queryPatientReportSettingById(null, null, hospitalAreaId);
        Optional<PatientCardSetting> patientCardSettingOptional = hospitalAreaSettingQuery.queryPatientCardSettingById(null, null, hospitalAreaId);
        Optional<HospitalizationSetting> hospitalizationSettingOptional = hospitalAreaSettingQuery.queryHospitalizationSettingById(null, null, hospitalAreaId);


        CustomerHospitalAreaSettingDetailVo vo = new CustomerHospitalAreaSettingDetailVo();
        vo.setHospitalAreaId(hospitalAreaId);
        vo.setHospitalAreaCode(hospitalArea.getHospitalCode());
        diagnosisPaymentSettingOptional.ifPresent(setting -> vo.diagnosisPaymentSetting(CustomerHospitalAreaService.toDiagnosisPaymentSettingVo(setting)));
        appointmentRuleSettingOptional.ifPresent(ruleSetting -> vo.appointmentRuleSetting(AppointmentSettingConverter.toCustomerAppointmentRuleSettingVo(ruleSetting)));
        patientReportSettingOptional.ifPresent(setting -> vo.patientReportSetting(CustomerHospitalAreaService.toPatientReportSettingVo(setting)));
        patientCardSettingOptional.ifPresent(cardSetting -> vo.patientCardSetting(CustomerHospitalAreaService.toPatientCardSettingVo(cardSetting)));
        hospitalizationSettingOptional.ifPresent(setting -> vo.hospitalizationSetting(CustomerHospitalAreaService.toHospitalizationSettingVo(setting)));

        return vo;
    }

    @Override
    public CustomerHospitalAreaPageVo queryHospitalAreaPage(CustomerQueryHospitalAreaPageReqDto request) {
        Page<Hospital> hospitalPage = hospitalAreaQuery.pageQueryHospitalArea(new HospitalAreaQuery.HospitalAreaQueryOption(request.getCurrentPage(), request.getPageSize())
                .setCategory(request.getCategory())
                .setTagDictLabel(request.getTagDictLabel())
        );

        CustomerHospitalAreaPageVo pageVo = pageVoConvert.toPageVo(hospitalPage, CustomerHospitalAreaPageVo.class, hospitalArea -> {
            CustomerHospitalAreaVo vo = CustomerHospitalAreaService.toCustomerHospitalAreaVo(hospitalArea);
            importHospitalInfo(vo);
            importHospitalAreaAddress(hospitalArea.getAddressId(), vo);
            return vo;
        });

        pageVo.setList(pageVo.getList().stream()
                .sorted(Comparator.comparingInt(CustomerHospitalAreaVo::getSort).reversed())
                .collect(Collectors.toList()));
        return pageVo;

    }

    @Override
    public CustomerHospitalAreaPositionVo getHospitalAreaPosition(CustomerHospitalAreaPositionReqDto request) {
        Hospital hospitalArea;
        if (null == request.getHospitalAreaId()) {
            hospitalArea = hospitalAreaQuery.queryHospitalAreaBy(request.getHospitalAreaCode());
            request.setHospitalAreaId(hospitalArea.getId());
        } else {
            hospitalArea = hospitalAreaQuery.queryHospitalAreaById(request.getHospitalAreaId());
        }

        List<Building> buildings = getHospitalBuilding(request.getHospitalAreaId());
        List<Floor> floors = getHospitalFloor(request.getHospitalAreaId());
        List<Area> areas = getHospitalArea(request.getHospitalAreaId());

        List<HospitalAreaPosition> departmentPositions = getHospitalAreaPosition(request.getHospitalAreaId());

        List<CustomerHospitalAreaBuildingVo> buildingPositionVos = hospitalAreaPositionTreeUtil.buildCustomerTree(buildings, floors, areas, departmentPositions);

        return new CustomerHospitalAreaPositionVo()
                .hospitalAreaId(hospitalArea.getId())
                .hospitalAreaCode(hospitalArea.getHospitalCode())
                .children(buildingPositionVos);
    }

    @Override
    public CustomAppointmentRuleSettingVo getAppointmentRuleSettingDetail(String hospitalAreaCode) {
        CustomAppointmentRuleSettingVo vo = new CustomAppointmentRuleSettingVo();
        Optional<AppointmentRuleSetting> appointmentRuleSetting = hospitalAreaSettingQuery.queryAppointmentRuleSettingBy(hospitalAreaCode);
        if (appointmentRuleSetting.isPresent()) {
            vo.setCurrentSystemType(appointmentRuleSetting.get().getCurrentSystemType());
            vo.setDoctorTitleShowType(appointmentRuleSetting.get().getDoctorTitleShowType());
            return vo;
        }
        log.warn("院区预约挂号规则未配置:{}", hospitalAreaCode);
        throw new BizException(HttpStatus.NOT_FOUND, "院区预约挂号规则未配置");
    }

    private List<Building> getHospitalBuilding(Long hospitalAreaId) {
        List<Building> buildingList = buildingQuery.queryBuildingsByHospitalAreaId(hospitalAreaId);
        if (!CollectionUtils.isEmpty(buildingList)) {
            return buildingList.stream().filter(building -> building.getStatus().equals(IS_ENABLE)).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    private List<Floor> getHospitalFloor(Long hospitalAreaId) {
        List<Floor> floorList = floorQuery.queryFloorsByHospitalAreaId(hospitalAreaId);
        if (!CollectionUtils.isEmpty(floorList)) {
            return floorList.stream().filter(floor -> floor.getStatus().equals(IS_ENABLE)).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }


    private List<HospitalAreaPosition> getHospitalAreaPosition(Long hospitalAreaId) {
        List<HospitalAreaPosition> departmentPositions = hospitalAreaPositionQuery.pageQueryPositions(new HospitalAreaPositionQuery.PositionQueryOption(1, Integer.MAX_VALUE)
                .setHospitalAreaId(hospitalAreaId)
        );

        return departmentPositions.stream().filter(departmentPosition -> departmentPosition.getStatus().equals(IS_ENABLE)).collect(Collectors.toList());
    }

    private List<Area> getHospitalArea(Long hospitalAreaId) {
        List<Area> areaList = areaQuery.queryAreasByHospitalAreaId(hospitalAreaId);
        if (!CollectionUtils.isEmpty(areaList)) {
            return areaList.stream().filter(area -> area.getStatus().equals(IS_ENABLE)).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    private void importHospitalInfo(CustomerHospitalAreaVo vo) {
        Hospital hospital = hospitalQuery.queryHospitalById(vo.getHospitalId());
        vo.setHospitalName(hospital.getName());
        vo.setHospitalLogo(hospital.getLogo());
    }

    private void importHospitalAreaAddress(Long addressId, CustomerHospitalAreaVo vo) {
        vo.setAddress(addressService.getAddressVoById(addressId));
    }

    private void setPageNavigator(CustomerHospitalAreaLayoutVo vo, Long hospitalAreaId, Integer channel) {
        List<HospitalAreaDetailPageNavigator> mainNavigators = hospitalDetailPageNavigatorMapper.selectByExample2(HospitalAreaDetailPageNavigator.class, sql ->
                sql.andEqualTo(HospitalAreaDetailPageNavigator::getHospitalAreaId, hospitalAreaId)
                        .andEqualTo(HospitalAreaDetailPageNavigator::getType, NavigatorTypeEnum.MAIN_NAVIGATOR.getCode()));

        if (CollectionUtils.isEmpty(mainNavigators)) {
            return;
        }

        CustomerHospitalAreaDetailPageNavigatorVo mainNavigatorVo = LayoutModelConverter.toVo(mainNavigators.stream()
                .filter(navi -> {
                    if (StringUtils.hasText(navi.getChannels())) {
                        String[] strings = navi.getChannels().split(",");
                        for (String string : strings) {
                            if (string.equals(channel.toString())) {
                                return true;
                            }
                        }
                    }
                    return false;
                })
                .collect(Collectors.toList())
        );
        vo.setNavigationVo(mainNavigatorVo);
    }

    private void setHospitalAreaDetailPageConfig(CustomerHospitalAreaLayoutVo vo, Long hospitalAreaId) {
        CustomerHospitalAreaDetailPageConfigVo hospitalDetailPageConfigVo = getHospitalDetailPageConfigByHospitalAreaId(hospitalAreaId);
        if (hospitalDetailPageConfigVo == null) {
            return;
        }
        vo.setPageConfig(hospitalDetailPageConfigVo);
    }

    public void setSubNaviModule(CustomerHospitalAreaLayoutVo vo, Long hospitalAreaId, Integer channel) {
        List<HospitalAreaDetailPageSubNaviModule> subNaviModules = hospitalDetailPageSubNaviModuleMapper.selectByExample(HospitalAreaDetailPageSubNaviModule.class, helper -> {
            helper.defGroup(consumer -> consumer.andEqualTo(HospitalAreaDetailPageSubNaviModule::getHospitalAreaId, hospitalAreaId));
            helper.builder(builder -> builder.orderByDesc(HospitalAreaDetailPageSubNaviModule::getSort));
        });

        List<CustomerHospitalAreaDetailPageSubNaviModuleVo> subNaviModuleVos = subNaviModules
                .stream()
                .filter(navi -> {
                    if (StringUtils.hasText(navi.getChannels())) {
                        String[] strings = navi.getChannels().split(",");
                        for (String string : strings) {
                            if (string.equals(channel.toString())) {
                                return true;
                            }
                        }
                    }
                    return false;
                })
                .map(naviModule -> {
                    CustomerHospitalAreaDetailPageSubNaviModuleVo naviModuleVo = LayoutModelConverter.toVo(naviModule);
                    importNaviVos(naviModuleVo, naviModule.getId(), channel);
                    naviModuleVo.setSubNavigationList(naviModuleVo.getSubNavigationList()
                            .stream()
                            .sorted(Comparator.comparingInt(CustomerNaviVo::getSort))
                            .collect(Collectors.toList()));
                    return naviModuleVo;
                })
                .sorted(Comparator.comparingInt(CustomerHospitalAreaDetailPageSubNaviModuleVo::getSort))
                .collect(Collectors.toList());
        vo.setSubNavigationVo(subNaviModuleVos);
    }

    private void setCubeModule(CustomerHospitalAreaLayoutVo vo, Long hospitalAreaId, Integer channel) {
        List<HospitalAreaDetailPageCubeModule> cubeModules = hospitalDetailPageCubeModuleMapper.selectByExample(HospitalAreaDetailPageCubeModule.class, helper -> {
            helper.defGroup(q -> q.andEqualTo(HospitalAreaDetailPageCubeModule::getHospitalAreaId, hospitalAreaId));
            helper.builder(q -> q.orderByDesc(HospitalAreaDetailPageCubeModule::getSort));
        });
        if (CollectionUtils.isEmpty(cubeModules)) {
            return;
        }

        List<CustomerHospitalAreaDetailPageCubeModuleVo> hospitalDetailPageCubeModuleVos = setCubeVo(cubeModules.stream()
                .filter(navi -> {
                    if (StringUtils.hasText(navi.getChannels())) {
                        String[] strings = navi.getChannels().split(",");
                        for (String string : strings) {
                            if (string.equals(channel.toString())) {
                                return true;
                            }
                        }
                    }
                    return false;
                })
                .collect(Collectors.toList()));
        vo.setCubeModuleVo(hospitalDetailPageCubeModuleVos);
    }


    private @Nullable CustomerHospitalAreaDetailPageConfigVo getHospitalDetailPageConfigByHospitalAreaId(Long
                                                                                                                 hospitalAreaId) {
        HospitalAreaDetailPageConfig hospitalAreaDetailPageConfig = hospitalDetailPageConfigMapper.selectOneByExample2(HospitalAreaDetailPageConfig.class,
                sql -> sql.andEqualTo(HospitalAreaDetailPageConfig::getHospitalAreaId, hospitalAreaId));
        if (hospitalAreaDetailPageConfig != null) {
            return LayoutModelConverter.toVo(hospitalAreaDetailPageConfig);
        }
        return null;
    }

    private List<CustomerHospitalAreaDetailPageCubeModuleVo> setCubeVo
            (List<HospitalAreaDetailPageCubeModule> cubeModules) {
        List<Long> cubeModuleIds = cubeModules.stream().map(HospitalAreaDetailPageCubeModule::getId).collect(Collectors.toList());
        List<CustomerHospitalAreaDetailPageCubeVo> cubeVos = getCubeVosByIds(cubeModuleIds);

        List<CustomerHospitalAreaDetailPageCubeModuleVo> cubeModuleVos = cubeModules.stream()
                .map(LayoutModelConverter::toVo)
                .collect(Collectors.toList());
        cubeModuleVos.forEach(q -> q.setCubeVo(cubeVos.stream()
                .filter(w -> w.getCubeModuleId().equals(q.getId()))
                .collect(Collectors.toList()))
        );
        return cubeModuleVos;
    }

    private List<CustomerHospitalAreaDetailPageCubeVo> getCubeVosByIds(List<Long> cubeModuleIds) {
        return hospitalDetailPageCubeMapper.selectByExample2(HospitalAreaDetailPageCube.class,
                        sql -> sql.andIn(HospitalAreaDetailPageCube::getCubeModuleId, cubeModuleIds))
                .stream()
                .map(LayoutModelConverter::toVo).collect(Collectors.toList());
    }

    private void setHospitalAreaAddress(Hospital hospitalArea, CustomerHospitalAreaLayoutVo vo) {
        AddressVo hospitalAreaAddress = addressService.getAddressVoById(hospitalArea.getAddressId());
        if (null != hospitalAreaAddress) {
            vo.setHospitalAreaAddress(hospitalAreaAddress);
        }
    }

    private void setHospitalName(Long parentId, CustomerHospitalAreaLayoutVo vo) {
        Hospital hospital = hospitalQuery.queryHospitalById(parentId);
        if (null != hospital) {
            vo.setHospitalName(hospital.getName());
        }
    }

    private void setHospitalAreas(Long parentId, CustomerHospitalAreaLayoutVo vo) {
        List<Hospital> hospitalAreas = hospitalAreaQuery.queryHospitalAreaByParentId(parentId);
        if (!CollectionUtils.isEmpty(hospitalAreas)) {
            List<CustomerHospitalAreaLayoutVoHospitalAreas> hospitalAreaLayoutVoHospitalAreas = hospitalAreas.stream()
                    .map(hospital -> {
                        CustomerHospitalAreaLayoutVoHospitalAreas hospitalAreaLayoutVoHospitalArea = new CustomerHospitalAreaLayoutVoHospitalAreas();
                        hospitalAreaLayoutVoHospitalArea.setHospitalAreaId(hospital.getId());
                        hospitalAreaLayoutVoHospitalArea.setHospitalAreaName(hospital.getName());
                        return hospitalAreaLayoutVoHospitalArea;
                    }).collect(Collectors.toList());
            vo.setHospitalAreas(hospitalAreaLayoutVoHospitalAreas);
        }
    }

    private void importNaviVos(CustomerHospitalAreaDetailPageSubNaviModuleVo vo, Long naviModuleId, Integer channel) {
        List<CustomerNaviVo> naviVos = hospitalDetailPageNavigatorMapper.selectByExample2(HospitalAreaDetailPageNavigator.class,
                        sql -> sql.andEqualTo(HospitalAreaDetailPageNavigator::getNaviModuleId, naviModuleId)
                )
                .stream()
                .filter(navi -> {
                    if (StringUtils.hasText(navi.getChannels())) {
                        String[] strings = navi.getChannels().split(",");
                        for (String string : strings) {
                            if (string.equals(channel.toString())) {
                                return true;
                            }
                        }
                    }
                    return false;
                })
                .map(naviEntity -> {
                    CustomerNaviVo naviVo = new CustomerNaviVo();
                    naviVo.setTitle(naviEntity.getTitle());
                    naviVo.setSubTitle(naviEntity.getSubTitle());
                    naviVo.setPicture(naviEntity.getPicture());
                    naviVo.setUrl(naviEntity.getUrl());
                    naviVo.setSort(naviEntity.getSort());
                    return naviVo;
                })
                .sorted(Comparator.comparingInt(CustomerNaviVo::getSort))
                .collect(Collectors.toList());
        vo.setSubNavigationList(naviVos);
    }
}
